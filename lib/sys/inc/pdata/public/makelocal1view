// template <typename type > void makelocal( Int m, cAu3xview<type>& gvr, type **svr )
  {
      Int ncpu;
      Int id,jr,j,i;
      Int ist,ien;

      cAu3xView<type> vr;

      create( m,svr );

      //vr= new type*[m];
      vr.subv( m,n,  *svr );

      ncpu= par->getncpu();
      ien= 0;
      for( id=0;id<ncpu+1;id++ )
     {
         ist= ien;
         ien= il[id];

         for( jr=0;jr<m;jr++ )
        {
            for( j=ist;j<ien;j++ )
           {
               i= ig[j];
               vr(jr,j)= gvr(jr,i);
           }
        }

     }
      //delete[] vr;
  }
                                                                       
