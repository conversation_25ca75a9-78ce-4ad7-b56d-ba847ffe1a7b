// template <typename type> void exchange( type *sdata )
  {
      Int ia,id,nv,iv,js;
      Int iss,ise, irs,ire;
      Int irnk;
      MPI_Comm   *pcom;
      MPI_Status  status;

      type **data;

      if( master )
     {

         irnk= par->getrank();
         pcom= par->getcomm();

         ia= inlst( (void*)sdata,na,ar );
         nv= n1[ia];
         data= new type*[nv];
         subv( nv,n, sdata, data );

         if( !rwait[ia] ){ rwait[ia]= new bool[ncpu+1]; setv( (Int)0,ncpu+1,false,rwait[ia] ); };
         if( !rsize[ia] ){ rsize[ia]= new  Int[ncpu+1]; setv( (Int)0,ncpu+1,(Int)0,rsize[ia] ); };
         if(  !rreq[ia] ){ rreq[ia]= new MPI_Request[ncpu+1]; };
         if(  !rbuf[ia] ){  rbuf[ia]= new pickle_t[ncpu+1]; setv( (Int)0,ncpu+1,(pickle_t)NULL,rbuf[ia] ); };

         if( !swait[ia] ){ swait[ia]= new bool[ncpu+1]; setv( (Int)0,ncpu+1,false,swait[ia] ); };
         if( !ssize[ia] ){ ssize[ia]= new  Int[ncpu+1]; setv( (Int)0,ncpu+1,(Int)0,ssize[ia] ); };
         if(  !sreq[ia] ){ sreq[ia]= new MPI_Request[ncpu+1]; };
         if(  !sbuf[ia] ){  sbuf[ia]= new pickle_t[ncpu+1]; setv( (Int)0,ncpu+1,(pickle_t)NULL,sbuf[ia] ); };


         if( !wptr[ia] ) { wptr[ia]= new void**[ncpu+1]; setv( (Int)0,ncpu+1,(void**)NULL,wptr[ia] ); }

/*      *dbgfle << "received communication request for array "<<ia<<" address "<<sdata<<"\n";
        *dbgfle << "first dimension for this array "<<nv<<"\n";*/

/*       cout << "received communication request for array "<<ia<<" address "<<sdata<<"\n";
         cout << "dimensions for this array "<<n1[ia]<<" "<<n2[ia]<<"\n";*/

         par->newmsg( this, ia );
     
         ise=0;
         ire=0;
         rwait[ia][ncpu]= true;

         for( id=0;id<ncpu;id++ )
        {
            iss= ise; 
            irs= ire; 

            ise= isl[id]; 
            ire= il[id]; 

            if( id != irnk )
           {
               if( ise > iss )
              {
                  if( swait[ia][id] )
                 {  
                     par->lock();
                     MPI_Wait( &(sreq[ia][id]),&status );
                     par->unlock();
//                   par->plog << "hold on, this is strange (send)...\n";
                 }
                  if( !sbuf[ia][id] )
                 {
                     ssize[ia][id]= nv*(ise-iss)*sizeof(*data);
                     sbuf[ia][id]= new pickle_v[ssize[ia][id]];
//                   cout << this << " created send buffer "<<ia<<" "<<id<<" "<<(void*)sbuf[ia][id]<<" "<<ssize[ia][id]<<"\n";
                 }
                  size_t len=0;
                  for( iv=0;iv<nv;iv++ )
                 {
                     pckle( &len,(ise-iss),is+iss,data[iv],sbuf[ia][id] );
                 }
                  par->lock();
                  MPI_Isend( sbuf[ia][id],ssize[ia][id],MPI_BYTE,id,tags[ia],*pcom,&(sreq[ia][id]));
                  par->unlock();
//                cout << "the send request "<<ia<<" "<<id<<" request is "<<sreq[ia][id]<<" "<<ssize[ia][id]<<" "<<nv<<" "<<ise-iss<<"\n";
                  swait[ia][id]= true;
              }
               if( ire > irs )
              {
                  if( rwait[ia][id] )
                 {  
                     par->lock();
                     MPI_Wait( &(rreq[ia][id]),&status );
                     par->unlock();
//                   par->plog << "hold on, this is strange (receive)...\n";
                 }
                  if( !rbuf[ia][id] )
                 {
                     rsize[ia][id]= nv*(ire-irs)*sizeof(*data);
                     rbuf[ia][id]= new pickle_v[rsize[ia][id]];
                     wptr[ia][id]= new void*[nv];
//                   cout << this << " created receive buffer "<<ia<<" "<<id<<" "<<(void*)rbuf[ia][id]<<" "<<rsize[ia][id]<<"\n";
                     for( iv=0;iv<nv;iv++ )
                    {
                        wptr[ia][id][iv]= (void*)(data[iv]+irs);
                    }
                 }
                  par->lock();
                  MPI_Irecv( rbuf[ia][id],rsize[ia][id],MPI_BYTE,id,tags[ia],*pcom,&(rreq[ia][id]));
                  par->unlock();
//                cout << "the receive request "<<ia<<" "<<id<<" request is "<<rreq[ia][id]<<" "<<rsize[ia][id]<<" "<<nv<<" "<<ire-irs<<"\n";
                  rwait[ia][id]= true;
              }
           }
        }
         delete[] data;
     }
  }
