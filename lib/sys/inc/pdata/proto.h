#  ifndef _PDATA_
#  define _PDATA_

#  include <paral.h>
#  include <mem/proto.h>
#  include <fstream>
#  include <cstdint>
#ifdef _OPENACC
#  include <cuda_runtime.h>
#  include <openacc.h>
#endif
/**
   cPdata is an abstraction of a distributed data set.
   cPdata provides partitioning and communication methods
  */

   class cPdata: public cMem
  {
      protected:

         class cParal    *par;                            /**< parallel environment object */
         Int              ncpu;                           /**< number of cores in the parallel environment */

         bool             master;                         /**< master flag */
         bool             started;                        /**< started flag */

         Int              gn;                             /**< global data set size */
         Int              n;                              /**< local data set size */
         Int              ns;                             /**< halo size */
         Int             *ig;                             /**< local to global correspondence array */
         Int             *is;                             /**< local to halo correspondence array */
         Int             *il;                             /**< local to global index array */
         Int             *isl;                            /**< local to halo index array */

//      Int              *ihlp;                           /**< temporary list for partitioning */

         Int             *mrkw;                           /**< write mode reference booking array */
         Int             *mrkr;                           /**< read mode reference booking array */
         Int             *prt;                            /**< partition array */


         Int             *tags;                           /**< message tags */

         Int            **ssize;                          /**< send message sizes */
         Int            **rsize;                          /**< receive message sizes */

         pickle_t       **sbuf;                           /**< send buffer */
         pickle_t       **rbuf;                           /**< receive buffer */

         //void         ****wptr;                           /**< pointers to receive message destination array */
         uintptr_t      ***wptr;                           /**< pointers to receive message destination array */

         MPI_Request    **sreq;                           /**< send message requests */
         MPI_Request    **rreq;                           /**< receive message requests */
         bool           **swait;                          /**< send message wait status */
         bool           **rwait;                          /**< receive message wait status */

         ofstream        *dbg;                            /**< debug stream */

         Int              gn_mat;                         /**< used for jacobian matrix assembly, 
                                                               so each rank holds continous rows*/
         Int             *ig_mat;                         /**< local to new global correspondence array for jacobian matrix */
         Int             *ig_2_ig_mat;                    /**< mapping of two global numbering */

/** Initialise all pointers to NULL and all sizes to 0.
   @brief Initialisation
  */
         void             blank();


/** Unpickle a vector of length n of atomic values var from the buffer buf. The storage for the vector is 
    not created afresh.
    The current position in the buffer is updated accordingly.
   @param len                                  Current position in the buffer.
   @param n                                    Vector length.
   @param var                                  Data vector item to unpickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief Vector unpickle.
 */
         template <typename type> void   unsafeunpckle( size_t *len, Int n,       type  *var, pickle_t  buf )
#                                                                include<pdata/protected/unsafeunpckle>
;
         #ifdef _OPENACC
         template <typename type> void unsafeunpcklegpu( size_t *len, Int n, type *var, const pickle_t buf )
        {
            size_t l= (size_t)(n*sizeof(*var));
            #pragma acc host_data use_device(buf) 
           {
               cudaMemcpy( (void*)var, (const void*)(buf+(*len)),  l,cudaMemcpyDeviceToDevice );
           }
            (*len)+= l;

        }
         #endif

      public:

/** Default constructor */
         cPdata();

/** Associates the new cPdata object with a parallel entity cParal.
   @param  var           Parallel environment.
   @brief                Runtime constructor.
  */
         cPdata( cParal *var );

/** Associates the new cPdata object with a parallel entity cParal and declares the size of the global dataset.
    Additionally, the temporary list for partition is initialised.
   @param  l             Data set size.
   @param  var           Parallel environment.
   @param  iprt          Global partition table.
   @brief                Preprocessing constructor.
  */
         cPdata( Int l, cParal *var, Int *iprt );

/** Associates the new cPdata object with a parallel entity cParal and declares the size of the global dataset.
   @param  l             Data set size.
   @param  var           Parallel environment.
   @brief                Preprocessing constructor.
  */
         cPdata( Int l, cParal *var );

/** Destructor.  */
         virtual ~cPdata();

/** Sets the debug stream to fle. All debug messages will be sent to *fle.
   @param fle       Debug stream
  */

         virtual void dbgfle( ofstream *fle ){ dbg= fle; };


/** returns the local size of the cPdata object data sets, including halos
   @return local data set size.
   @brief local data set size.
  */
         virtual Int size(){ return n;};

/** returns the global size of the cPdata object data sets.
   @return global data set size.
   @brief global data set size.
  */
         virtual Int gsize(){ return gn;};

/** Prints a summary of the data set size and partition information to the stream fle.
   @param fle  Output stream.
   @brief Data set summary
  */
         virtual void summary( ofstream *fle );

/** Prints detailed information on the data set to the stream fle.
   @param fle  Output stream.
   @brief Data set debug information.
  */
         virtual void   debug( ofstream *fle );

/** Returns the range of the local entries with dependency from core id
   @param id         core
   @param ist        range start
   @param id         range end
   @brief entry range.
  */
         #pragma acc routine seq
         virtual void range( Int id, Int *ist, Int *ien );

         virtual void pickle( size_t *len, pickle_t *buf );
         virtual void unpickle( size_t *len, pickle_t buf );

/** Creates a new array for the dataset.
   @param m          number of entries in each element of var
   @param var        new array
   @brief new array
  */

         template <typename type > void create( Int m, type **var )
#  include                             <pdata/public/create>
;

         template <typename type > void regaddr( Int m, type *var )
        {
             cMem::regaddr( m, n, var );
        };


/** Partition the data set based on the partition table 
   @param        partition table
   @brief Preprocessing partition method
  */
         virtual void partition( );

/** Starts the communications between correcponding cPdata objects on the cores in the parallel environment.
   @brief Start communications.
  */
         void start();

/** Checks if a send request for array ia to core id has comleted.
   @param id           destination core
   @param ia           array
   @brief Send request completion.
  */
         bool stransit( Int id, Int ia );

/** Checks if a receive request for array ia from core id has completed.
   @param id           source core
   @param ia           array
   @brief Receive request completion.
  */
         bool rtransit( Int id, Int ia );

/** Creates a local copy svr of the array gvr. The storage for the local array svr is created afresh.
   @param m           number of columns
   @param gvr         global array
   @param srv         local array
   @brief Make local data.
  */
         template <typename type > void makelocal( Int m, type *gvr[], type **svr )
#  include                             <pdata/public/makelocal1>
;
         template <typename type > void makelocal( Int m, cAu3xView<type>& gvr, type **svr )
#  include                             <pdata/public/makelocal1view>


/** Creates a local copy svr of the topology array gvr referring to the master data set mstr. 
    The storage for the local array svr is created afresh.
   @param m           number of columns
   @param gir         global array
   @param siv         local array
   @param mstr         master partition
   @brief Make local data.
  */
         void makelocal( Int m, Int *gir[], Int **sir, cPdata *mstr );
         void makelocal( Int m, cAu3xView<Int>& gir, Int **sir, cPdata *mstr );

/** Reads the array var from the file f. The storage for var is created afresh.
   @param m           number of columns
   @param var         local array
   @param f           file
   @brief Read data
  */
         template <typename type > void read( Int m, type **var, FILE *f )
#  include                             <pdata/public/read>
;

/** Reassembles the local array vr back into the global array gvr.
   @param m            number of columns.
   @param gvr          global array.
   @param vr           local array.
   @brief Reassemble data.
  */
         template <typename type> void makeglobal( Int m, type *gvr[], type *vr[] )
#   include                           <pdata/public/makeglobal>
;

/** Reassembles the local array vr back into the global array gvr.
   @param m            number of columns.
   @param gvr          global array.
   @param vr           local array.
   @brief Reassemble data.
  */
         template <typename type> void makeglobal( Int m, type& gvr, type& vr )
#   include                           <pdata/public/makeglobalview>
;


/** Writes the array var to the file f.
   @param m           number of columns
   @param var         local array
   @param f           file
   @brief Read data
  */
         template <typename type > void write( Int m, type  *var, FILE *f )
#  include                             <pdata/public/write>
;

/** Starts a send/receive round for the array sdata
   @param sdata       array.
   @brief Communication round.
  */
//         template <typename type > void exchange( type *sdata )
//#  include                             <pdata/public/exchange>
         void exchange( Real *sdata );
;

/** Mark needed entries for the communication pattern impiled by the connection
    array iref.
   @param obj           pdata accessed during the loop.
   @param nref          number of columns in iref.
   @param iref          refrerence array.
   @brief Communication pattern pre-booking.
  */

         void rdloop( cPdata *obj, Int nref, Int *iref[] );
         void rdloop( cPdata *obj, Int nref, cAu3xView<Int>& iref );

/** Mark needed entries for the communication pattern impiled by the connection
    array iref.
   @param obj           pdata accessed during the loop.
   @param nref          number of columns in iref.
   @param iref          refrerence array.
   @brief Communication pattern pre-booking.
  */
         void wrloop( cPdata *obj, Int nref, Int *iref[] );
         void wrloop( cPdata *obj, Int nref, cAu3xView<Int>& iref );

/** Get the local indexing of an entity from the global indexing of an entity. The entity could a point or cell
   @param Int i          global indexing.
  */
         Int localindex( Int i );

         void stdout();

         void partition_mat( );
         Int* get_igmat() {return ig_mat;};

  };

#  endif
