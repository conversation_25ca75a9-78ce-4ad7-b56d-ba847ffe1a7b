#ifndef CAU3XVIEW_HPP
#define CAU3XVIEW_HPP

#include <experimental/mdspan>
#include <iostream>
#include <stdexcept>

namespace stdex = std::experimental;

  // Default layout is layout_right (row-major), but you can specify another layout.
  template <typename T, class LayoutPolicy = stdex::layout_right>
   class cAu3xView 
  {
  public:
      // Empty constructor
      cAu3xView()
          : m_data(nullptr), m_dim0(0), m_dim1(0), m_dim2(0), m_dim3(0) {}
  
      // Constructor for 1D array
      cAu3xView(T* data, size_t dim0)
          : m_data(data), m_dim0(dim0), m_dim1(0), m_dim2(0), m_dim3(0),
     #ifdef _OPENACC
            m_mdspan_1d(data, stdex::extents<size_t, stdex::dynamic_extent>(dim0)) {}
     #else
            m_mdspan_1d(data, stdex::extents<size_t, std::dynamic_extent>(dim0)) {}
     #endif
  
      // Constructor for 2D arrays
      cAu3xView(T* data, size_t dim0, size_t dim1)
          : m_data(data), m_dim0(dim0), m_dim1(dim1), m_dim2(0), m_dim3(0),
     #ifdef _OPENACC
            m_mdspan_2d(data, stdex::extents<size_t, stdex::dynamic_extent, stdex::dynamic_extent>(dim0, dim1)) {}
     #else
            m_mdspan_2d(data, stdex::extents<size_t, std::dynamic_extent, std::dynamic_extent>(dim0, dim1)) {}
     #endif
  
      // Constructor for 3D arrays
      cAu3xView(T* data, size_t dim0, size_t dim1, size_t dim2)
          : m_data(data), m_dim0(dim0), m_dim1(dim1), m_dim2(dim2), m_dim3(0),
     #ifdef _OPENACC
            m_mdspan_3d(data, stdex::extents<size_t, stdex::dynamic_extent, stdex::dynamic_extent, stdex::dynamic_extent>(dim0, dim1, dim2)) {}
     #else
            m_mdspan_3d(data, stdex::extents<size_t, std::dynamic_extent, std::dynamic_extent, std::dynamic_extent>(dim0, dim1, dim2)) {}
     #endif
  
      // Constructor for 4D arrays
      cAu3xView(T* data, size_t dim0, size_t dim1, size_t dim2, size_t dim3)
          : m_data(data), m_dim0(dim0), m_dim1(dim1), m_dim2(dim2), m_dim3(dim3),
     #ifdef _OPENACC
            m_mdspan_4d(data, stdex::extents<size_t, stdex::dynamic_extent, stdex::dynamic_extent, stdex::dynamic_extent, stdex::dynamic_extent>(dim0, dim1, dim2, dim3)) {}
     #else
            m_mdspan_4d(data, stdex::extents<size_t, std::dynamic_extent, std::dynamic_extent, std::dynamic_extent, std::dynamic_extent>(dim0, dim1, dim2, dim3)) {}
     #endif
  
      // Member function to initialize 1D mdspan
      void subv_1d(size_t dim0, T* data )
     {
          m_data = data;
          m_dim0 = dim0;
          m_dim1 = m_dim2 = m_dim3 = 0;
          m_mdspan_1d = mdspan_1d_type(data, dim0);
     }
  
      // Member function to initialize 2D mdspan
       void subv(size_t dim0,  size_t dim1, T* data) {
          m_data = data;
          m_dim0 = dim0;
          m_dim1 = dim1;
          m_dim2 = m_dim3 = 0;
          m_mdspan_2d = mdspan_2d_type(data, dim0, dim1);
      }
  
      // Member function to initialize 3D mdspan
       void subv_3d(size_t dim0, size_t dim1, size_t dim2, T* data) {
          m_data = data;
          m_dim0 = dim0;
          m_dim1 = dim1;
          m_dim2 = dim2;
          m_dim3 = 0;
          m_mdspan_3d = mdspan_3d_type(data, dim0, dim1, dim2);
      }
  
      // Member function to initialize 4D mdspan
       void subv_4d(size_t dim0, size_t dim1, size_t dim2, size_t dim3, T* data) {
          m_data = data;
          m_dim0 = dim0;
          m_dim1 = dim1;
          m_dim2 = dim2;
          m_dim3 = dim3;
          m_mdspan_4d = mdspan_4d_type(data, dim0, dim1, dim2, dim3);
      }
  
      // Accessor for 1D
      T& operator()(size_t i) {
          return m_mdspan_1d(i);
      }
  
      // Accessors for 2D
      T& operator()(size_t i, size_t j) {
          return m_mdspan_2d(i, j);
      }
  
      // Accessors for 3D
      T& operator()(size_t i, size_t j, size_t k) {
          return m_mdspan_3d(i, j, k);
      }
  
      // Accessors for 4D
      T& operator()(size_t i, size_t j, size_t k, size_t l) {
          return m_mdspan_4d(i, j, k, l);
      }
  
      // Dimension getters
      size_t get_dim0() const { return m_dim0; }
      size_t get_dim1() const { return m_dim1; }
      size_t get_dim2() const { return m_dim2; }
      size_t get_dim3() const { return m_dim3; }
  
      // Get data pointer
      T* get_data() const { return m_data; }
  
      // Get total size
      size_t get_size() const {
          size_t size = 1;
          if (m_dim0 > 0) size *= m_dim0;
          if (m_dim1 > 0) size *= m_dim1;
          if (m_dim2 > 0) size *= m_dim2;
          if (m_dim3 > 0) size *= m_dim3;
          return size;
      }
  
      // Use the mdspan's layout policy to compute the linear index
      // Must be called with correct number of indices for the rank.
      size_t get_linear_index_using_layout(size_t i) const {
          return m_mdspan_1d.mapping()(i);
      }
      size_t get_linear_index_using_layout(size_t i, size_t j) const {
          return m_mdspan_2d.mapping()(i, j);
      }
      size_t get_linear_index_using_layout(size_t i, size_t j, size_t k) const {
          return m_mdspan_3d.mapping()(i, j, k);
      }
      size_t get_linear_index_using_layout(size_t i, size_t j, size_t k, size_t l) const {
          return m_mdspan_4d.mapping()(i, j, k, l);
      }
  
  private:
     #ifdef _OPENACC
      using mdspan_1d_type = stdex::mdspan<T, stdex::extents<size_t, stdex::dynamic_extent>, LayoutPolicy>;
      using mdspan_2d_type = stdex::mdspan<T, stdex::extents<size_t, stdex::dynamic_extent, stdex::dynamic_extent>, LayoutPolicy>;
      using mdspan_3d_type = stdex::mdspan<T, stdex::extents<size_t, stdex::dynamic_extent, stdex::dynamic_extent, stdex::dynamic_extent>, LayoutPolicy>;
      using mdspan_4d_type = stdex::mdspan<T, stdex::extents<size_t, stdex::dynamic_extent, stdex::dynamic_extent, stdex::dynamic_extent, stdex::dynamic_extent>, LayoutPolicy>;
     #else
      using mdspan_1d_type = stdex::mdspan<T, stdex::extents<size_t, std::dynamic_extent>, LayoutPolicy>;
      using mdspan_2d_type = stdex::mdspan<T, stdex::extents<size_t, std::dynamic_extent, std::dynamic_extent>, LayoutPolicy>;
      using mdspan_3d_type = stdex::mdspan<T, stdex::extents<size_t, std::dynamic_extent, std::dynamic_extent, std::dynamic_extent>, LayoutPolicy>;
      using mdspan_4d_type = stdex::mdspan<T, stdex::extents<size_t, std::dynamic_extent, std::dynamic_extent, std::dynamic_extent, std::dynamic_extent>, LayoutPolicy>;
     #endif
  
      // Pointer to the contiguous data
      T* m_data;
  
      // Dimensions (0-based naming)
      size_t m_dim0, m_dim1, m_dim2, m_dim3;
  
      // mdspan objects for 1D, 2D, 3D, and 4D
      mdspan_1d_type m_mdspan_1d{};
      mdspan_2d_type m_mdspan_2d{};
      mdspan_3d_type m_mdspan_3d{};
      mdspan_4d_type m_mdspan_4d{};
  };

#endif // CAU3XVIEW_HPP

