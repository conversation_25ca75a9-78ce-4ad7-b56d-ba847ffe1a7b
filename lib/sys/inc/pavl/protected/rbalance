// void pavl_c::rbalance( int node, int ia )
  {
      int                 a,b,t,p,q;
      int                 ic,io,id,jd;
     
      a= node;
      if( a > 0  )
     {
         t= buf[a].prn;
         id= buf[a].ic;
         jd= 1;
         if( id == 0 ){ jd= -1; };
         if( buf[a].b == 0 )
        {
            buf[a].b= -ia;      /* the node was initially balanced, not any more. CF similar line in insert and note 
                               different sign: here removal is implied: if removal happens on subtree ia and the
                               node was initially balanced then its balance factor becomes -ia */
        }
         else
        {
            if( ia == -(buf[a].b) )
           {
// rebalancing is needed

               io= 0;
               ic= 1;
               if( ia == -1 ){ io= 1; ic= 0; };

               b= buf[a].ch[io];
               p= buf[b].ch[ic];

               if( buf[b].b == 0 ) 
              {

/* single rotation but b (child on opposite side wrt. removal) is balanced: the height of the tree will not change
   but both a and b will be left unbalanced. No recursion needed. */  

                  buf[a].ch[io]= p;
                  if( p > 0 )
                 {
                     buf[p].ic= io;
                     buf[p].prn= a;
                 }
                  buf[b].ch[ic]= a;
                  buf[a].ic= ic;
                  buf[a].prn= b;
     
                  buf[b].prn=    t;
                  buf[b].ic=    id;
                  buf[b].b=     ia;
                  buf[t].ch[id]= b;
              }
               else
              {
                  if( buf[b].b == buf[a].b ) 
                 {

/* single rotation, b is unbalanced and both a and b will be balanced after rotation. Recursion needed because the
   height of the tree reduces. */

                     buf[a].ch[io]= p;
                     if( p > 0 )
                    {
                        buf[p].ic= io;
                        buf[p].prn= a;
                    }
                     buf[b].ch[ic]= a;
                     buf[a].ic= ic;
                     buf[a].prn= b;
     
                     buf[a].b= 0;
                     buf[b].b= 0;
                     buf[b].prn=    t;
                     buf[b].ic=    id;
                     buf[t].ch[id]= b; 
                     
                     rbalance( t,jd );
                 }
                  else
                 {

/* double rotation */

                     q= buf[p].ch[ic];
                     buf[a].ch[io]= q;
                     if( q > 0 )
                    {
                        buf[q].prn= a;
                        buf[q].ic= io;
                    }
     
                     q= buf[p].ch[io];
                     buf[b].ch[ic]= q;
                     if( q > 0 )
                    {
                        buf[q].prn= b;
                        buf[q].ic= ic;
                    }
                     
                     buf[p].ch[ic]= a;
                     buf[a].prn= p;
                     buf[a].ic= ic; 
     
                     buf[p].ch[io]= b;
                     buf[b].prn= p;
                     buf[b].ic= io;
     
                     buf[p].prn= t;
                     buf[p].ic= id;
                     buf[t].ch[id]= p;
     
                     if( buf[p].b == 0 )
                    { 
                        buf[a].b= 0; 
                        buf[b].b= 0; 
                    }
                     else
                    {
                        if( buf[p].b == ia )
                       { 
                           buf[a].b=   0; 
                           buf[b].b= -ia; 
                           buf[p].b=   0;
                       }
                        else
                       { 
                           buf[a].b=  ia;         
                           buf[b].b=   0; 
                           buf[p].b=   0;
                       }
                    }
                     rbalance( t,jd );
                 }
              }
           }
            else
           {
// this elimination has balanced this node (but the height of the tree has changed )
               buf[a].b= 0;
               rbalance( t,jd );
           }
        }
     }
  }
