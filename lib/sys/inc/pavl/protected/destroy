//void spavl_c::destroy( int node )
 {
     int q,l,r,t;     
     int b,ic;
     int val;

     q= node; 
     if( q > 0 )
    {

        val= buf[q].val;
        indx[val]= -1;
      
        if( n > 1 )
       {
      
           n--;

           if( q != n )
          {
              val= buf[n].val;
              t=   buf[n].prn;
              l=   buf[n].ch[0];
              r=   buf[n].ch[1];
              ic=  buf[n].ic;
              b=   buf[n].b;
         
              buf[q].val= val;
              buf[q].prn=   t;
              buf[q].ch[0]= l;
              buf[q].ch[1]= r;
              buf[q].ic=   ic;
              buf[q].b=     b;
              buf[t].ch[ic]= q;
              indx[val]= q;
         
              if( l > 0 ){ buf[l].prn= q; };
              if( r > 0 ){ buf[r].prn= q; };
          }
       }

        buf[n].val=-1;
        buf[n].prn=-1;
        buf[n].ch[0]=-1;
        buf[n].ch[1]=-1;
        buf[n].ic=-1;
        buf[n].b=-1;
    }
 }
