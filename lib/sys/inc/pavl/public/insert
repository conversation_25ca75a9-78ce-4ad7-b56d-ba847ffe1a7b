// bool pavl_c::insert( int ikey, type data )
  {
      int     q,s,t,p,r,z;
      int     ia,ic=1; 
      int     io,id;
      bool    val=false;

/* at the end of the search, q will point to the location of the key (NULL if not found),
   p to its parent, t to the root of the tree and s to the first location where balancing is needed
 */

      t=  0;
      p=  0;
      q=  buf[t].ch[ic];
      s=  q;

// search the tree

      while( q > 0 )
     {
         if( data[ikey] == data[buf[q].val] ) // !not suitable for floating point calcs
        {
            val= true;
            break;
        }
         else
        {

            if( data[ikey] < data[buf[q].val] )
           {
               ic= 0;
           }
            else
           {
               ic= 1;
           }

            if( buf[q].b != 0 ) // balance factor update will start from s
           {
               t=p;
               s=q;
           }
            p= q;
            q= buf[p].ch[ic];
        }
     }

// if required insert the new entry

      if( q < 0 )
     {
         if( n == m )
        {
            realloc( &m,PAVL_DLEN,&buf );
        } 
         q= n;
         n++;

         nk= max( nk,ikey+1 );
         if( nk >= mk )
        {
            realloc( &mk,PAVL_DLEN,&indx );
        }
         indx[ikey]= q;

         buf[q].val=  ikey;
         buf[q].ch[0]= -1;
         buf[q].ch[1]= -1;
         buf[q].prn= p;
         buf[q].b=   0;

         buf[q].ic    =ic; 
         buf[p].ch[ic]= q; 

// update balance factors

         if( s > 0 )
        {

            ia= -1;
            ic= 0; 
            if( data[ikey] > data[buf[s].val] ){ ia=1; ic=1; }
            p= buf[s].ch[ic];
            r= p;

// change balance factors between the location s (first location where update is needed) and the location q.
            while( buf[p].val != buf[q].val )
           {
               if( data[ikey] < data[buf[p].val] )
              {
                  ic=    0;  // insertion on the left means left subtree has grown in height B(p)= -1
                  buf[p].b= -1;
              }
               if( data[ikey] > data[buf[p].val] )
              { 
                  ic=    1;  //  insertion on the right means right subtree has grown in height B(p)= 1
                  buf[p].b=  1; 
              }
               p= buf[p].ch[ic];
           }


            if( buf[s].b == 0 )
           { 
               buf[s].b= ia;       // the node was initially balanced, not any more
           }
            else
           {
               if( buf[s].b == -ia )
              {
                  buf[s].b= 0;      // the node was initially unbalanced, but the insertion has balanced it
              }
               else
              {
                                // the node was unbalanced and the insertion made it incompatible with AVL condition
                  if( ia == -1 )
                 { 
                     ic=0;
                     io=1; 
                 }
                  else
                 { 
                     ic=1;
                     io=0; 
                 }
                  if( buf[r].b == ia )  
                 {
                                // single rotation

                     p= r;

                     z= buf[r].ch[io];
                     buf[s].ch[ic]= z;
                     if( z > 0 )
                    { 
                        buf[z].prn= s; 
                        buf[z].ic= ic; 
                    }

                     t= buf[s].prn;
                     id= buf[s].ic;
                     buf[r].prn= t;
                     buf[r].ic=  id;
                     buf[t].ch[id]= r; 
                    
                     buf[r].ch[io]= s;
                     buf[s].prn= r;
                     buf[s].ic= io;
                     buf[s].b= 0;
                     buf[r].b= 0;

                 }
                  else
                 {
                                  // double rotation

                     t= buf[s].prn;
                     id= buf[s].ic;


                     p=         buf[r].ch[io];
                     z=         buf[p].ch[ic];

                     buf[r].ch[io]= z;
                     if( z > 0 )
                    {
                        buf[z].prn= r;
                        buf[z].ic=  io;
                    }

                     buf[p].ch[ic]= r;                
                     buf[r].prn=p; 
                     buf[r].ic= ic;

                     z=         buf[p].ch[io];
                     buf[s].ch[ic]= z;
                     if( z > 0 )
                    {
                        buf[z].prn= s;
                        buf[z].ic=  ic;
                    }

                     buf[p].ch[io]= s;                
                     buf[s].prn=p; 
                     buf[s].ic= io;
                     if( buf[p].b == ia )
                    { 
                        buf[s].b= -ia; 
                        buf[r].b= 0; 
                     }
                      else
                    {
                        if( buf[p].b == 0  )
                       { 
                           buf[s].b=   0; 
                           buf[r].b=   0; 
                       }
                        else
                       { 
                           buf[s].b=   0;  
                           buf[r].b=  ia; 
                       };
                    }

                     buf[p].prn=  t;
                     buf[p].ic=  id;
                     buf[p].b=    0;

                     buf[t].ch[id]= p;
                 }                 
              }
           }
        }
     }
      return val;
  }
