// int pavl_c::maxinf( int ikey )
  {
      int ic;
      int p,q;
      int val=-1;

      q= indx[ikey];

      if( q > 0 )
     {  

         val=buf[q].val;
         p= buf[q].ch[0];

         if( p > 0 )
        {
            while( p > 0 )
           {
               val= buf[p].val;
               p= buf[p].ch[1];
           }
        }
         else
        {
            p= buf[q].prn;
            while( p > 0 ) // don't use head node
           {
               ic= buf[q].ic;
               if( ic == 1 )
              {
                  val= buf[p].val;
                  break;
              }
               q=p;
               p= buf[q].prn;
           }
        }
     }

      return val;
  }
