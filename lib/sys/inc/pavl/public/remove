// void pavl_c::remove( int ikey )
  {
      int     p,r,l,s,t,z;
      int     r0;
      int     ic,id;

      t=  indx[ikey];

      ic= buf[t].ic;
      p=  buf[t].prn; 
      l=  buf[t].ch[0];
      r=  buf[t].ch[1];

      if( r < 0 )
     {

// no right child, just replace node with its left child. Balancing will start with the node's parent.

         t= l;
         z= p;
         id=ic;
         if( id == 0 ){ id= -1; };
     }
      else
     {
         s= buf[r].ch[0];
         if( s < 0 )
        {

// right child has no left child, just replace node with its right child. Balancing will start with the node itself.

            z=  r;
            buf[r].b= buf[t].b;
            id= 1;

            buf[r].ch[0]= l;
            if( l > 0 )
           { 
               buf[l].prn= r; 
           }
            t= r;

        }
         else
        {

// The right child has children: replace the node with its min-successor. Balancing will start with the node itself.
            
            r0=r;
            do
           {
               s= r;
               r= buf[s].ch[0];
           }while( r > 0 );
            r= buf[s].prn;

            buf[s].ch[0]= l;
            if( l > 0 ){ buf[l].prn= s; };

            z= buf[s].ch[1];
            buf[r].ch[0]= z;
            if( z > 0 )
           {
               buf[z].prn= r;
               buf[z].ic=  0;
           }

            buf[s].ch[1]= r0;
            buf[s].b=     buf[t].b;
            buf[r0].prn= s;

            t= s;

            z= r;
            id= -1;
        }
     }

      buf[p].ch[ic]= t;
      if( t > 0 )
     {
         buf[t].prn= p;
         buf[t].ic= ic;
     }

      rbalance( z,id );

      destroy( indx[ikey] );

  }
