// void pavl_c::traverse( string tab, int node, type *data )
  {
      int q,r,l;
      if( node < 0 || n < 2 ){ return; };
      q= node;
      if( q == 0 )
     { 
         q= buf[0].ch[1]; 
     }
      if( q > 0 )
     {
         
         l= buf[q].ch[0];
         r= buf[q].ch[1];

         traverse( tab+PAVL_TAB,r,data ); 
         cout<<tab<<data[buf[q].val]<<"( "<<q<<":"<<buf[q].prn<<":"<<buf[q].ic<<":"<<buf[q].b<<":"<<buf[q].val<<")\n";
         traverse( tab+PAVL_TAB,l,data );
     }
  }
