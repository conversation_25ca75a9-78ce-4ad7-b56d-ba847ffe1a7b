// int pavl_c::minsup( int ikey )
  {
      int p,q;
      int ic;
      int val=-1;

      q= indx[ikey];

      if( q > 0 )
     {

         val=buf[q].val;
         p= buf[q].ch[1];

         if( p > 0 )
        {
            while( p > 0 )
           {
               val= buf[p].val;
               p= buf[p].ch[0];
           }
        }
         else
        {
            p= buf[q].prn;
            while( p > 0 ) // don't use head node
           {
               ic= buf[q].ic;
               if( ic == 0 )
              {
                  val= buf[p].val;
                  break;
              }
               q= p;
               p= buf[q].prn;
           }
        }
     }
      return val;
      
  }
