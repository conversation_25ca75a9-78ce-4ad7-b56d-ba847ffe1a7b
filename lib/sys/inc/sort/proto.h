#  ifndef SORT_H
#  define SORT_H

/**@ingroup system
  *@{
 **/

/**@defgroup sorting Sorting algorithms
  *@{
 **/

#  include <cprec.h>
#  include <utils/proto.h>

   template < typename type > void sift_down( Int n, type *val, Int *iprm, Int l , Int r )
#  include <sort/public/sift_down>
;

   template < typename type > void sift_down( Int n, type *var, Int *iprm )
#  include <sort/public/sift_down0>
;

   template < typename type > void select( Int k, Int n, Int *iprm, type *x )
#  include <sort/public/select>
;

/** Heap sort
   @param n                 Number of elements
   @param val               Elements
   @param iprm              Permutation array
   @todo  Known bug for small n.
 **/
   template < typename type > void hsort( Int n, type *val, Int *iprm )
#  include <sort/public/hsort>
;
/** Bubble sort. Use only if n<5.
   @param n                 Number of elements
   @param val               Elements
   @param iprm              Permutation array
 **/
   template < typename type > void bsort( Int n, type *val, Int *iprm )
#  include <sort/public/bsort>
;

/** In place bubble sort. On exit iprm is 1 if the number of permutations needed to sort the array is even,
   -1 otherwise.
   @param n                 Number of elements
   @param val               Elements
   @param iprm              Permutation index.
 **/
   template < typename type > void psort( Int n, type *val, Int *iprm )
#  include <sort/public/psort>
;

/**
  *@}
 **/

/**@
  *@}
 **/

#  endif 
