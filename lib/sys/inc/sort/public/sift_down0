// template < typename type > void sift_down( Int n, type *var, Int *iprm )
  {
      Int m= n-1;

      Int j,j0;
      Int  ia;
      type a;

      a= var[0];
      ia= iprm[0];
      j0= 0;
      j= 1;
      while( j<= m )
     {
         if( j < m && var[j] < var[j+1] ) j++;
         if( a >= var[j] ) break;
         var[j0]= var[j];
         iprm[j0]= iprm[j];
         j0= j;
         j= 2*j+1;
     }
      var[j0]= a;
      iprm[j0]= ia;
  }
