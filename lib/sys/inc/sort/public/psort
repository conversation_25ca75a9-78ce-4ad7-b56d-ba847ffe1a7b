// template <typename type > void psort( Int n, type *val, Int *iprm )
  {
      Int         i,j;
      type        tmp;

     *iprm= 1;

      for( j=0;j<n-1;j++ )
     {
         for( i=j+1;i<n;i++ )
        {
            if( val[i] < val[j] )
           {
               tmp= val[i];
               val[i]= val[j];
               val[j]= tmp;
              *iprm= -( *iprm );
           }
        }
     }
  }

