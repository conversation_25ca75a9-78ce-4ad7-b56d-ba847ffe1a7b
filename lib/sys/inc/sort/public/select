// template < typename type > void select( Int k, Int n, Int *iprm, type *x )
  {
      Int i,ia,ir,j,l,im;
      Int iswp;
      type   a;

      iswp=0;
      l= 0;
      ir=n-1;
      for(;;)
     {
         iswp++;
         if( ir<= l+1 )
        {
            if( ( ir == l+1 ) && ( x[iprm[ir]] < x[iprm[l]] ) )
           {
               swap( iprm+l,iprm+ir );
           }
            return;
        }
         else
        {
            im= ( l+ir ) >> 1; 
            swap( iprm[im],iprm[l+1] );
            if( x[iprm[l]] > x[iprm[ir]] ) swap( iprm+l,iprm+ir );
            if( x[iprm[l+1]] > x[iprm[ir]] ) swap( iprm+l+1,iprm+ir );
            if( x[iprm[l]] > x[iprm[l+1]] ) swap( iprm+l,iprm+l+1 );
            i= l+1;
            j= ir;
            ia= iprm[l+1];    
            a= x[ia];
            for(;;)
           {
               do i++; while( x[iprm[i]] < a );
               do j--; while( x[iprm[j]] > a );
               if( j < i )break;
               swap( iprm+i,iprm+j );
           }
            iprm[l+1]= iprm[j];
            iprm[j]= ia;
            if( j>= k ) ir=j-1;
            if( j<= k ) l=i;
        }
     }
  }
