// void sift_down( Int n, Int *val, Int *iprm, const Int l, const Int r)
  {
      Int j,jold,ia;
      type a;
      ia= iprm[l]; 
      a=val[iprm[l]];
      jold=l;
      j=2*l+1;
      while (j <= r)
     {
         if (j < r && val[iprm[j]] < val[iprm[j+1]]) j++;
         if (a >= val[iprm[j]]) break;
         iprm[jold]= iprm[j];
         jold=j;
         j=2*j+1;
      }
      iprm[jold]= ia; 
   }
