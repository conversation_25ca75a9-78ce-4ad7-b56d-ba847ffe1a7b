#ifndef _PLUGIN_
#define _PLUGIN_   

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Jul 22 15:23:17 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         debug mode

#  include <dlfcn.h>
#  include <cstdlib>
#  include <string>

#  include <cprec.h>

#  define  MXARG 20

   class cPlugin
  {
      private:

         string       path,name;
         void        *lib_handle;
         void        *sub;
         Int          argc;
         string       argv[MXARG];

      public:
	//! Default constructor
         cPlugin( );

	//! Construct from.....
         cPlugin( string, string );
         cPlugin( string, string, Int, string * );

        ~cPlugin( );

	/*!
	 * \return return a pointer to void
	 */
         void *Sub(){ return(sub); }; 

	/*!
	 * \return no return type
	 */
         void  unload();
  };

#endif
