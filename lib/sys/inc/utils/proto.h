#  ifndef _UTILS_
#  define _UTILS_

#  include <string>
#  include <sstream>
#  include <iostream>
#  include <cstdlib>
#  include <cassert>
#  include <cmath>

#  include <cprec.h>
#  include <au3xview.h>

/**@{ @ingroup utils */


/** Concatenation of integer pairs**/

   struct concat_t
  {
      Int                 n;
      Int             *ilst[2];
      Int                 m;
      Int             *lprm;
  };

/** Install path 
   @brief Install path
   */
   string installpath();

   #define ADDR_( i, j, n ) ( i*n+j )
   inline Int ADDR( Int i, Int j, Int n ) {return i*n+j;}
   //assume dimension is 3
   inline Int ADDR( Int i, Int j, Int k, Int n ) {return (i*3+j)*n+k;}
   #define ADDRG( i, j, k, n ) ((i*3+j)*n+k)
   

/** Debug
   @brief debug
  */
   void stophere();
  
/** Wait for m milliseconds
   @param m                                    Number of milliseconds to wait.
   @brief Wait.
  */
   void milliseconds( double m );

/** Integer to string conversion
   @param var                                   The int to convert to string.
   @return                                      A string version of var.
   @brief Int to string
  */
   template <typename type> string           strc( type data )
                                            {
                                                string t;
                                                stringstream s;
                                                s.clear();
                                                s << data;
                                                s >> t;
                                                return t;
                                            }

/** Integer to string conversion
   @param var                                   The int to convert to string.
   @return                                      A string version of var.
   @brief Int to string
  */
   string                                    str( int var );
  
/** Pointer to string conversion
   @param var                                   The pointer to convert to string.
   @return                                      A string version of var.
   @brief Pointer to string
  */
   string                                    str( void *var );

/** String to int conversion
   @param var                                   The string to convert to int.
   @return                                      An int  version of var.
   @brief Pointer to string
  */
   int                                   str2int( string var );

/** Safe string to <type> conversion. If an error occurs the variable is no assignment takes place.
   @param data                                  The string to convert into <type>.
   @param var                                   The <type> variable to be assigned.
   @brief Safe string to <type> conversion.
  */
   template <typename type> bool conv( string data, type *var )
#  include                                      <utils/public/conv>
;

/** String parsing
   @param data                                  The string to parse.
   @param n                                     The number of words
   @param var                                   The list words.
   @param syn                                   The syntax string.
   @brief String parsing
  */
   void parse( string data, Int *n, string **var, string syn );
   bool sep( char c, string synt );

/** Returns the length of the trimmed version of data.
   @param  data                                 The character buffer to trim.
   @return                                      The length of the trimmed data.
   @see   trim( char *data )
   @brief  Character buffer trim.
  */
   Int                                   trimlen( char *data );

/** Trims the buffer data at the first blank or new line character 
   @param  data                                 The character buffer to trim.
   @return                                      A string containing the trimmed buffer.
   @brief  Character buffer trim.
  */
   string                                   trim( char *data );

/** set entries ist to ien in a data buffer to a specified value .
   @param ist                                   starting location.
   @param ien                                   ending location.
   @param val                                   value.
   @param data                                   buffer.
  */
   template < typename type > void setv( Int ist, Int ien, type val, type *data )
  {
      Int i;
      for( i=ist;i<ien;i++ )
     {
         data[i]= val;
     }
  };

/** set entries ist to ien in a data buffer to a specified value .
   @param ist                                   starting location.
   @param ien                                   ending location.
   @param n                                     number of columns
   @param val                                   value.
   @param data                                   buffer.
  */
   template < typename type > void setv( Int ist, Int ien, Int n, type val, type *data[] )
  {
      Int i;
      for( i=0;i<n;i++ )
     {
         setv( ist,ien, val,data[i] );
     }
  };

   template < typename type > void setv( Int ist, Int ien, Int n, type val, type *sdata, Int nq )
  {
      Int i,iq;
      for(iq=ist; iq<ien; iq++)
     {
         for( i=0;i<n;i++ )
        {
            sdata[ADDR(i,iq,nq)] = val;
        }
     }
  };

   template < typename type > void setvgpu( Int ist, Int ien, Int n, type val, type *sdata, Int nq )
  {
      Int i,iq;
      #pragma acc parallel loop gang vector\
       present(sdata[0:n*nq]) \
       default(none)
      for(iq=ist; iq<ien; iq++)
     {
         for( i=0;i<n;i++ )
        {
            sdata[ADDR(i,iq,nq)] = val;
        }
     }
  };

/** set entries ist to ien in a data buffer to a specified value .
   @param ist                                   starting location.
   @param ien                                   ending location.
   @param val                                   value.
   @param data                                   buffer.
  */
   template < typename type > void setv( Int n, type *val, type *data )
  {
      Int i;
      for( i=0;i<n;i++ )
     {
         data[i]= val[i];
     }
  };

/** set entries specified in the reference array iref from ist to ien in a data buffer to a specified value.
   @param ist                                   starting location.
   @param ien                                   ending location.
   @param n                                     second dimension of data.
   @param iref                                  refernce array.
   @param val                                   value.
   @param data                                  buffer.
  */
   template < typename type > void setv( Int ist, Int ien, Int n, Int *iref, type val, type *data[] )
#  include                                      <utils/public/setvn>
;

   template < typename type > void setv( Int ist, Int ien, Int n, Int *iref, type val, cAu3xView<type>& data )
  {
      Int i,j,k;
      for( j=0;j<n;j++ )
     {
         for( k=ist;k<ien;k++ )
        {
            i= iref[k];
            data(j,i)= val;
        }
     }
  }

/** Reallocate a pointer to pointers to accomodate increased size.
   @param  len                                  The current size of the pointer.
   @param  dlen                                 The size increase.
   @param  data                                 The pointer.
   @brief  Reallocate.
  */
   template < class object > void realloc( Int *len, Int dlen, object ***data )
#  include                                      <utils/public/realloc>
;

/** Reallocate a pointer to elementary types to accomodate increased size.
   @param  len                                  The current size of the pointer.
   @param  dlen                                 The size increase.
   @param  data                                 The pointer.
   @brief  Reallocate.
  */
   template < typename type > void realloc( Int *len, Int dlen, type  **data )
#  include                                      <utils/public/realloct>
;

/** Reallocate a pointer to elementary types to accomodate increased size and appends the entries in data.
   @param  len                                  The current size of the pointer.
   @param  dlen                                 The size increase.
   @param  data                                 The pointer.
   @brief  Reallocate.
  */
   template < typename type > void append( Int *len, type **var, Int dlen, type *data )
#  include                                      <utils/public/append>
;

/** Swap two values 
   @param v0                                    First value.
   @param v1                                    Second value.
   @brief Swap.
  */
   template <typename type > void swap( type *v0, type *v1 ){ type dum; dum= *v0; *v0= *v1; *v1= dum; };

/** Pre-compute section pointer to access a linear storage array in row-major mode.
   @param n1                                    Numer of columns.
   @param n1                                    Number of rows.
   @param a                                     Pointer to the storage.
   @param as                                    Pointers to the columns.
   @brief Access pointers.
  */
   template < typename type > void subv( Int n1, Int n2, type *a, type *as[] )
  {
      Int i;
      as[0]= a;
      for( i=1;i<n1;i++ )
     {
         as[i]= as[i-1]+n2;
     }
  }

   template < typename type > void subvgpu( Int n1, Int n2, type *a, type *as[] )
  {
      Int i;
     #pragma acc kernels present(a[0:n1*n2],as[0:n1])
     {
         as[0]= a;
        #pragma acc loop seq
         for( i=1;i<n1;i++ )
        {
            as[i]= as[i-1]+n2;
        }
     }
  }


   template <typename type > void identv( Int n, type *ival )
  {
      Int i;
      for( i=0;i<n;i++ )
     {
         ival[i]=(type)i;
     }
  }

/** Performs binary search on a sorted array. The array is sorted in ascending order 
    and the function returns the position of the smallest entry larger than var.
   @param                    var              Search key.
   @param                    n                Number of entries.
   @param                    data             Array.
   @return                                    The position of the smallest entry in data larger than var.
 **/
   template < typename type > Int bsearch( type var, Int n, type *data )
#  include                               <utils/public/bsearch>
;

/** Performs binary search on an array sorted by the permutation iprm. The array is sorted in ascending order 
    and the function returns the position of the smallest entry larger than var.
   @param                    var              Search key.
   @param                    n                Number of entries.
   @param                    data             Array.
   @param                    iprm             Permutation array for data. data[iprm[k]] is the k-th smallest entry in data.
   @return                                    The position of the smallest entry in data larger than var.
 **/
   template < typename type > Int bsearch( type var, Int n, type *data, Int *iprm )
#  include                               <utils/public/bsearchi>
;


/** Reverse the order of the entries in data.
   @param                     n                Size of the array.
   @param                     data             Array to be reversed.
 **/
   template < typename type > void reverse( Int n, type *data )
#  include                               <utils/public/invert>
;

   template < typename type > Int inlst( type var, Int n, type *data )
#  include                               <utils/public/inlst>
;
   void accml( Int n, Int *ia );

   template < typename type > void shftl( Int n, type *data )
#  include                               <utils/public/shftl>
;

   template < typename type > void shftr( Int n, type *data )
#  include                               <utils/public/shftr>
;

   template < typename type > void shftc( Int n, type *data )
#  include                               <utils/public/shftc1>
;

   template < typename type > void shftc( Int n, type *data, Int m )
#  include                               <utils/public/shftm>
;

   template < typename type > void shftc( Int ist, Int ien, type *data )
#  include                               <utils/public/shftc>
;

   template < typename type > void line( Int i, Int n, type *data[], type *var )
#  include                               <utils/public/linen>
;

   template < typename type > void line( Int i, Int n, cAu3xView<type>& data, type *var )
#  include                               <utils/public/linenview>


   template < typename type > void line2( Int i, type *data[], type *var )
#  include                               <utils/public/line2>
;

   template < typename type > void line3( Int i, type *data[], type *var )
#  include                               <utils/public/line3>
;


/** Permute the array data according to the permutation table iprm. E.g. data[i] becomes data[iprm[i]];
   @param                                   n                array size;
   @param                                   data             array to be permuted;
   @param                                   iprm             permutation array;
**/
   template < typename type > void permute( Int n, type *data, Int *iprm )
#  include                               <utils/public/permute>
;


/** Determine a permutation and a set of equivalent classess for a concatenated sequence of integer numbers. 
    Version for periodic sequences.
   @param       n           Length of the sequence.
   @param       ilst        Sequence.
   @param       iprm        Permutation array.
   @param       m           Number of equivalence classes.
   @param       lprm        Ending index of each equivalence class.
 **/
   void   concatp( Int n, Int *ilst[], Int *iprm, Int *m, Int *lprm );


/** Determine a permutation and a set of equivalent classess for a concatenated sequence of integer numbers. 
    General sequences.
   @param       n           Length of the sequence.
   @param       ilst        Sequence.
   @param       iprm        Permutation array.
   @param       m           Number of equivalence classes.
   @param       lprm        Ending index of each equivalence class.
 **/
   void    concatg( Int n, Int *ilst[], Int *iprm, Int *m, Int *lprm );

/** Determine if at least one of the equivalence classes for a concatenated sequence is reducbile and if so, reports 
    its position in lprm. If no reducble classes are found return -1. 
   @param       n           Length of the sequence.
   @param       ilst        Sequence.
   @param       iprm        Permutation array.
   @param       m           Number of equivalence classes.
   @param       lprm        Ending index of each equivalence class.
   @return                  The position of one of the reducible classes in lprm.
 **/
   Int  reducible( Int n, Int *ilst[], Int *iprm, Int  m, Int *lprm );

/** Match two sequences and return a permutation array for the second
   @param       n           Length of the sequences.
   @param       ilst0       First sequence.
   @param       ilst1       Second sequence.
   @param       iprm        Permuation index for the second sequence.
 **/

    void match( Int n, Int *ilst0[], Int *ilst1[], Int *iprm );

/**@}*/

   Real lower_bound_by_mag(Real var, Real var_bound);


   template < typename type > void setv( Int ist, Int ien, Int n, type val, cAu3xView<type>& data, string arch )
  {
      Int i,iq;

      if(arch=="d")
     {
         Int nq;
         type *sdata;

         sdata = data.get_data();
         nq = data.get_dim1();

         #pragma acc parallel loop gang vector\
          present(sdata[0:n*nq]) \
          default(none)
         for(iq=ist; iq<ien; iq++)
        {
            for( i=0;i<n;i++ )
           {
               sdata[ADDR(i,iq,nq)] = val;
           }
        }
     }
      else if(arch=="h")
     {
         for(iq=ist; iq<ien; iq++)
        {
            for( i=0;i<n;i++ )
           {
               data(i,iq) = val;
           }
        }
     }
      else
     {
         cout << "unknown ARCH to initialize the array\n";
         assert(0);
     }
  };


   template < typename type > void setv_3d( Int ist, Int ien, Int n1, Int n2, type val, cAu3xView<type>& data, string arch )
  {
      Int iq,i1,i2;

      if(arch=="d")
     {
         Int nq;
         type *sdata;

         sdata = data.get_data();
         nq = data.get_dim2();

         #pragma acc parallel loop gang vector\
          present(sdata[0:n1*n2*nq]) \
          default(none)
         for(iq=ist; iq<ien; iq++)
        {
            for( i1=0;i1<n1;i1++ )
           {
               for( i2=0;i2<n2;i2++ )
              {
                  sdata[ADDR(i1,i2,iq,nq)] = val;
              }
           }
        }
     }
      else if(arch=="h")
     {
         for(iq=ist; iq<ien; iq++)
        {
            for( i1=0;i1<n1;i1++ )
           {
               for( i2=0;i2<n2;i2++ )
              {
                  data(i1,i2,iq) = val;
              }
           }
        }
     }
      else
     {
         cout << "unknown ARCH to initialize the array\n";
         assert(0);
     }
  };

#  endif
