// template < typename type > Int bsearch( type var, Int n, type *data )
  {
      Int val=-1;
      Int i0,i1;
      if( n > 0 )
     {
        if( var <= data[0] )
       {
           val=0; 
       }
        else
       {
           if( var >= data[n-1] )
          {
              val= n-1;
          }
           else
          {
              i0=0;
              i1=n-1;
              val= i0+i1;
              val/= 2;
              while( true )
             {
                 if( var < data[val] )
                {
                    i1= val;
                }
                 else
                {
                    i0= val;
                }
                 if( i1 <= i0+1 )
                {
                    val= i1;
                    break;
                }
                 else
                {
                    val= i0+i1;
                    val/= 2;
                }
              }
           }
        }
     }
      return val;
  }
