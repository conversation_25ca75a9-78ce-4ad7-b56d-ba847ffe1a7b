// template < class object > void realloc( Int len, Int dlen, object ***data )
  {
      Int                                      i;
      Int                              len0,len1;
      object                               **buf;
      buf= NULL;
      if( dlen != 0 )
     {
         len0= *len;
         if( len0 > 0 )
        {
            buf= new object*[len0]; 
            for( i=0;i<len0;i++ )
           {
               buf[i]= (*data)[i];
           } 
            delete[] *data;
           *data= NULL;
        }
         len1= len0+ dlen;
        *data= new object*[len1];
         if( dlen > 0 )
        {
            for( i=0;i<len0;i++ )
           {
             (*data)[i]= buf[i];
           }
            for( i=len0;i<len1;i++ )
           {
             (*data)[i]= NULL;
           }
        }
         else
        {
            for( i=0;i<len1;i++ )
           {
             (*data)[i]= buf[i];
           }
        }
         delete[] buf; 
       (*len)= len1;
     }
  };
