#  ifndef _THREAD_
#  define _THREAD_

#  include <pthread.h>
// include <iostream>
// include <fstream>
// include <string>

/*3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
         1         2         3         4         5         6         7         8         9         0         1         2

   <PERSON>          <PERSON> <<EMAIL>>
   Created         Wed Jul 14 15:46:59 BST 2010
   Changes History -
   Next Change(s)  ( work in progress )
*/

/**@ingroup thread  */
/**@{*/

   typedef pthread_mutex_t               mutex_t; //!< Default mutex variable type.
   typedef int                             pid_t; //!< Default thread id type. 

/** threadf() invokes the thread() method of the object passed as data. When thread() returns, 
    the object is detached from the list of active threads and destroyed.
   @param data                                  The thread object.
   @brief POSIX thread function.
  */
   void                                 *threadf( void *data );

/**@}*/


/**@ingroup thread
    The cThread class implements a POSIX-based joinable thread management facility.
    New threads are created using the default constructors and attached to a master thread
    through the boot() method. The actions performed by the threads are specified by the thread() method implementation.
    Thread joining is performed via the join() method of the master thread.
    join() will wait until the last thread booted by the master expires. cThread also implements 
    a basic MUTEX locking-unlocking service through the lock()/unlock() methods. lock()/unlock() always operates
    on the MUTEX of the master thread. 
    Classes inheriting cThread must reimplement the thread() method.
   @brief Basic thread management class.
  */

   class cThread
  {

      protected:

/**@{@name Thread bookeeping symbols */
         cThread                           *more; //!< Pointer to next thread in active thread list.
         cThread                           *less; //!< Pointer to previous thread in active thread list.
         cThread                           *root; //!< Pointer to master thread
/**@}*/


/**@{@name POSIX symbols */
         mutex_t                            mutx; //!<  Thread MUTEX variable
         pthread_t                           tid; //!<  Thread POSIX id
         pthread_attr_t                     attr; //!<  Thread POSIX attributes
/**@}*/

                                         cThread();

/**@{@name Thread management methods. */

/** Attach the thread var to the master thread.
   @param var                                   The thread to attach.
   @brief Attach new thread.
  */
         virtual void                      weave( cThread *var );

/** Detach the thread from the master thread.
   @brief Detach thread.
  */
         virtual void                    isolate();
/**@}*/

      public:

         virtual                        ~cThread();
/** Attach the thread object var to the list of active threads, create a new POSIX thread and run the thread method of 
    var. boot() invokes threadf() as POSIX thread funxtion.
   @param var                                   The thread to boot.
   @see   threadf
   @brief Thread boot.
  */
         virtual void                       boot( cThread *var ); 
         virtual void                       boot();

/** Empty virtual method implementing the task to be performed by the thread. 
   @brief Thread task.
  */
         virtual void                     thread(){};

/** Wait for all threads to exit. 
    @brief Thread join.
  */
                 void                       join();

/** Wait for thread var to exit. 
    @param var    the thread to join.
    @brief Thread join.
  */
                 void                       join( cThread *var );

/** lock the master thread MUTEX variable 
   @brief Master thread MUTEX lock 
  */
         virtual void                       lock();

/** unlock the master thread MUTEX variable 
   @brief Master thread MUTEX unlock
  */
         virtual void                     unlock();
 
         friend void *threadf( void * );
  };

#  endif
