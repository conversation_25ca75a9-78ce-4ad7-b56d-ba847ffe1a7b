#  ifndef _PARAL_
#  define _PARAL_

#  include <mpi.h>
#  include <pthread.h>
#  include <utils/proto.h>

//!MPI binding to default types

#    define          MPI_Int MPI_INT
#ifdef FP32
   #    define          MPI_Real MPI_FLOAT
#else
   #    define          MPI_Real MPI_DOUBLE
#endif

#    define          IDLET 1.

   class cParal
  {
      protected:

         pthread_mutex_t   mutx;

         Int               itagb;

         MPI_Comm          pcom;

         Int               nque;
         Int              *ique;

         Int               nmsg;
         class cPdata    **msgd;
         Int              *msgv;
         bool            **msgw;

         int          rank,size;
         int    irnk,lrnk,lsize;
         Int               ncpu; 
         Int              *icpu;
         Int              *acpu;
         Int             *icore;
         Int             *inode;
//       Real             *cpul;

         bool resident;

	//! Default constructor
         cParal();

	/*!
	 * \param Irnk an Int
	 * \return no return type
	 */
         void impersonate( Int Irnk ){ irnk = Irnk; };

	/*!
	 * \return no return type
	 */
         void identify();
         bool  queue();

      public:
	//! Default constructor
         virtual ~cParal();

	/*!
	 * \return return a bool value
	 */
         bool resides(){ return resident; };

	/*!
	 * \return return a pointer to MPI_Comm
	 */
         MPI_Comm*   getcomm(){ return &pcom;};   

	/*!
	 * \return the mutex variable
	 */
         pthread_mutex_t*   mutex(){ return &mutx;};   

         void newmsg( class cPdata *, Int );

	/*!
	 * \return return an Int
	 */	
         Int   getrank(){ return irnk;};   

	/*!
	 * \return return an Int
	 */
         Int getncpu(){ return ncpu; };

	/*!
	 * \param id an Int
	 * \return return an Int
	 */
         Int geticpu(Int id){ return icpu[id]; };

         bool transit();
         Int  avail();

         void lock();
         void unlock();


         void newtag( Int *val );
         Int tag( ){ return itagb; };

         void gsum( Int, Real * );
         void gmax( Int, Real * );
         void gmin( Int, Real * );

         void gsumg( Int, Real * );
         void gmaxg( Int, Real * );
         void gming( Int, Real * );

         void gsumg( Int, Int * );
         void gmaxg( Int, Int * );
         void gming( Int, Int * );

         void  gopen( ofstream *, string );
         void gclose( ofstream * );
  };

#  include <pdata/proto.h>

#  endif
