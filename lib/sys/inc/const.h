#  ifndef _CONST_
#  define _CONST_

#  include <string>
#  include <cprec.h>

/**@{ @ingroup constants */

#ifdef FP32
   const Real   big=                            1.e+8;  //!< Default large value. 
   const Real   small=                          1.e-8;  //!< Default small value.
   const Int    bad=                         -999999;  //!< Bad initialisation integer.
#else
   const Real   big=                            1.e+16;  //!< Default large value. 
   const Real   small=                          1.e-16;  //!< Default small value.
   const Int    bad=                         -99999999;  //!< Bad initialisation integer.
#endif
   const string unassigned=              "<unassigned>"; //!< Unassigned string.
   const string home=                             Home ; //!< Shell environment variable HOME value.
   const string host=                             Host ; //!< Shell environment host name variable. 
   const string share=       home+string("/xcode/lib/share/"); //!< Default shared object/data path. 

   const Real   pi=               3.1415926535897323844; //!< \f$\pi\f$. 
   const Real   pi2=             6.28318530717958647688; //!< \f$2\pi\f$. 
   const Real   rad=                            360/pi2; //!< Radiants to degrees conversion. 
   const Real   runi=                           8.31451; //!< Universal gas constant. 
   const Real   gvctr=                             9.81; //!< Earth's sea level gravitational acceleration (medium latitudes). 
   const Real   rpm=                            pi2/60.; //!< rpm to rad/s conversion
   const Real   ZERO=                           (Real)0.; //!< zero

/**@}*/

#endif

