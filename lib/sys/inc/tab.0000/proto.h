#  ifndef _REF_
#  define _REF_

#  include <iostream>
#  include <string>
#  include <sstream>
#  include <cprec.h>
#  include <const.h>
#  include <utils/proto.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Sat Nov 27 17:31:14 GMT 2010
// Changes History -
// Next Change(s)  -

/**@ingroup tab */
/**@{*/

#  define TABDSIZE 5
#  define MXNTBS 100

/** Basic data types available for keyword- or list-based access */
    enum tbd_t { tbdbad=-1, tbdint, tbdreal, tbdbool, tbdstr};

/**@}*/

/**@ingroup tab */
/** cTabItem represents an abstract set of data. Each cTabItem contains a vector of homogeneous entries.
    The entries are all interpreted as being of the type specified by rtyp.
    The internal storage is always allocated first for data. The size of the storage can be modified to hold vector
    values by assigning n. The actual resizing is performed automatically.
   @brief Abstract data item.
  */
   class cTabItem
  {
      protected:
         void                                   nullify();            /**< Nullify the state of a cTabItem prior to use**/

         tbd_t                                  rtyp;                 /**< Type of the data entries.*/
         Int                                    m;                    /**< Physical size of internal storage.*/


/**@name Type compatibility enquiries. */
/**@{*/
         bool                                   compat( Real   );     /**< Real data.*/
         bool                                   compat( Int    );     /**< Int data.*/
         bool                                   compat( bool   );     /**< bool data.*/
         bool                                   compat( string );     /**< string data.*/
         string                                *val;                  /**< Internal storage.*/
/**@}*/
      public:
         Int                                    n;                    /**< Logical size of internal storage.*/
         Int                                    n_fix;                /**< Number of fixed entries. */
         Int                                    n_flx;                /**< Number of flexabel added entries. */
         Int                                    n_gmember;            /**< Number of logical groups (?) */
         string                                 hlp;                  /**< Help string.*/
         string                                 hlp1;                 /**< 2nd piece of Help string.*/
         bool                                   vrange;               /**< A range of items of variable length starts 
                                                                           here **/
         bool                                   vlength;              /**< This item has variable length **/
         string                                 lbc[10*MXNTBS];          /**< column string message */

         cTabItem();                                                  /**< Basic constructor. No data type assigned.*/
         cTabItem( Real   );                                          /**< Real entry constructor. */
         cTabItem( Int    );                                          /**< Int entry constructor. */
         cTabItem( bool   );                                          /**< bool entry constructor. */
         cTabItem( string );                                          /**< string entry constructor. */

         cTabItem( cTabItem * );                                      /**< Copy constructor. */

        ~cTabItem();                                                  /**< Destructor */


/** Return a string with a brief description of the entry at position i.
   @param            i              Position
   @return                          A help message
 **/  
                                    string help( Int i );


/** Resize updates the physical size of the internal storage to match (at least) its logical size.
   @brief Internal storage update. 
  */
                                    void resize()
#                                               include <tab/public/resize>
;

/** Replicate entries from ist to ien and inject them at position i.
   @brief Internal storage update. 
  */
                                    void replicate( Int ist, Int ien, bool left );

/** Finds which block a particular entry is in.
   @brief Internal storage update. 
  */
                                    void block( Int i, Int *, Int *, Int *, Int * );

/**@name Internal store set methods*/
/**@{*/
/** Set the internal store value. Scalar entries.
   @param                 v       Input value.
   @brief                         Set internal store.
  */
         template < typename type > void set( type v )
#                                               include <tab/public/set>
;

/** Set the internal store value. Vector entries. The size of the input vector is assumed to match
    the logical store size as defined by n.
   @param                 v       Input value.
   @brief                         Set internal store.
  */
         template < typename type > void set( type *v )
#                                               include <tab/public/setv>
;

/** Set the internal store value. Vector entries. The size of the input vector is assumed to match
    the logical store size as defined by n.
   @param                 i       Entry position.
   @param                 v       Input value.
   @brief                         Set internal store.
  */
         template < typename type > void set( Int i, type v )
#                                               include <tab/public/seti>
;

/** Forces a string buffer into the internal store at position i.
   @param                 i       Entry index.
   @return                v       String buffer
   @brief                         Set internal store.
  */
         void                force( Int i, string v );

/** Resize the buffer and force a string buffer into the internal store at position i.
   @param                 i       Entry index.
   @return                v       String buffer
   @brief                         Set internal store.
  */
         void               inject( Int i, string v );

/** Removes the value at position i in the internal store.
   @param                 i       Entry index.
   @brief                         Remove value
  */
         void               remove( Int i );

/** Removes the value between ist and ien from the internal store.
   @param                 ist     Start index
   @param                 ien     Start index
   @brief                         Remove value
  */
         void               remove( Int ist, Int ien );

/**@}*/


/**@name Internal store get methods*/
/**@{*/

/** Get the internal store i-th strings. 
   @param                 i       Entry index.
   @return                v       Output value.
   @brief                         Get internal store.
  */
         string              fetch( Int i );

/** Get the internal store value. Scalar entries.
   @param                 v       Output value.
   @brief                         Get internal store.
  */
         template < typename type > bool get( type *v )
#                                               include <tab/public/get>
;

/** Get the internal store value. Vector entries. The size of the input vector is assumed to match
    the logical store size as defined by n.
   @param                 v       Output value.
   @brief                         Get internal store.
  */
         template < typename type > bool getv( type *v )
#                                               include <tab/public/getv>
;

// earmarked for decant
/** Get the internal store value. Vector entries. The size of the input vector is assumed to match
    the logical store size as defined by n.
   @param                 i       Entry position.
   @param                 v       Output value.
   @brief                         Get internal store.
  */
         template < typename type > bool get( Int i, type *v )
#                                               include <tab/public/geti>
;
/**@}*/

/** Prints to std::cout the content of the internal store for checking purposes. 
   @param                 tab     Alingement tab.
   @brief                         Check.
  */
         virtual                    void check( string tab );

/** Returns true if the item has been initialised without a type and should be treated as a page break
   @brief                         Check.
  */
         virtual                    bool isbreak(){ return rtyp==tbdbad; };

  };

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          Luca di Mare <<EMAIL>>
// Created         Sat Nov 27 17:31:14 GMT 2010
// Changes History -
// Next Change(s)  -


/**@ingroup tab */
/** cTabData is an abstract set of possibly heterogeneous data items. Each data item is treated as a vector entry.
    The items can be accessed either sequentially or through keywords. Keywords are assigned to the data items
    when they are added to the store and are immutable.
   @brief Abstract heterogeneous data set.
  */
   class cTabData
  {
      protected:
         Int                                    n;                   /**< Internal store logical size. */
         Int                                    m;                   /**< Internal store physical size. */
         string                              *lbl;                   /**< Data items labels */
         cTabItem                           **tbd;                   /**< Data items */
      public:

                                              cTabData();
                                             ~cTabData();

         void                                     copy( Int ist, Int ien, cTabData *var );

/** getn returns the current logical size of the internal store.
   @return                   Logical store size.
   @brief                    Internal store size enquiry.
  */
         Int                                  getn(){ return n; };   

/**@name Data item identification. */
/**@{*/
/** returns the label of the i-th data item in the internal store.
   @param             i    data item position.
   @return                 data item label. Empty string if i > n.
   @brief i-th data item label.
  */
         string                               label( Int i );

/** returns all the labels of the iterms in the internal store. The string vector needs to be deallocated.
   @param             n     data item count.
   @return            l     data item labels. NULL if n=0;
   @brief i-th data item label.
  */
         void                               labels( Int *n, string **data );

/** which returns the i-th data item in the internal store.
   @param             i    data item position.
   @return                 pointer to the i-th data item. NULL if i > n.
   @brief i-th data item.
  */
         cTabItem                            *which( Int i );

/** which returns the i-th data item in the internal store.
   @param             l    data item keyword.
   @return                 pointer to the i-th data item. NULL if i > n.
   @brief i-th data item.
  */
         cTabItem                            *which( string l );

/** where returns the current position of a given data item in the internal store.
   @param             l    data item label.
   @return                 data item index. -1 if data item not in current store.
   @brief data item position.
  */
         Int                                  where( string l );

/** where returns the current position of a given data item in the internal store.
   @param             var    data item address.
   @return                 data item index. -1 if data item not in current store.
   @brief data item position.
  */
         Int                                  where( cTabItem *var );
/**@}*/

/**@name Internal store management*/
/**@{*/

/** Clear the internal store.
   @brief Store clear.
  */
         void                              clear();
/** Add a new data item to the internal store.
   @param     s                Data item keyword.
   @param     data             Data item.
   @brief Add data item.
  */
         void                              append( string s,        cTabItem *data );

/** Inset a new data item into the internal store at postion i.
   @param     s                Data item keyword.
   @param     i                Data item position.
   @param     data             Data item.
   @brief Add data item.
  */
         void                              insert( string s, Int i, cTabItem *data );

/** Removes  the data item at position i.
   @param     i                Data item position.
   @brief Remove data item.
  */
         void                              remove( Int    i );

/** Replaces the data item at position i with new data. The existing data item is destroyed.
   @param     s                Data item keyword.
   @param     i                Data item position.
   @param     data             Data item.
   @brief Remove data item.
  */
         void                             replace( string s, Int i, cTabItem *data );

/** Resize the data item corresponding to the keyword s.
   @param     s                Data item keyword.
   @param     len              Data item size.
   @brief Resize data item.
  */
         void                                 resize( string s, Int len );

/** Resize interval between entries ist and ien. New entries are set to null, redundant entries are removed.
   @param     ist              Starting position.
   @param     ien              Ending position.
   @param     len              Requested number of entries.
   @brief Resize data item.
  */
         void                                 resize( Int ist, Int ien, Int len );

/**@}*/

/**@name Internal store set methods*/
/**@{*/

/** Set value for the data item associated to the keyword l using string conversion. Scalar entries.
   @param     l                Data item keyword.
   @param     v                Value.
   @brief Set data item.
  */
         void     force( string l, string s );

/** Set value for the data item associated to the keyword l. Scalar entries.
   @param     l                Data item keyword.
   @param     v                Value.
   @brief Set data item.
  */
         template < typename type > void      set( string l, type v )
#                                               include <tab/public/lset>
;
/** Set value for the data item associated to the keyword l. Vector entries. The size of the data is assumed to 
    match the size of the internal store for the data item. Only the i-th entry in the data item is set.
   @param     l                Data item keyword.
   @param     i                Position in data item.
   @param     v                Value.
   @brief Set data item.
  */
         template < typename type > void      set( string l, Int i, type v )
#                                               include <tab/public/lseti>
;

/** Set value for the data item associated to the keyword l. Vector entries. The size of the data is assumed to 
    match the size of the internal store for the data item. The whole vector data item  is set.
   @param     l                Data item keyword.
   @param     v                Value.
   @brief Set data item.
  */
         template < typename type > void      set( string l, type *v )
#                                               include <tab/public/lsetv>
;
/**@}*/

/**@name Internal store get methods*/
/**@{*/

/** Print to standard output the entire content of the tab data object.
   @brief Standard output.
  */
         void      check();
;

/** Get value for the data item associated to the keyword l. Scalar entries.
   @param     l                Data item keyword.
   @param     v                Value.
   @brief Get data item.
  */
         template < typename type > bool      get( string l, type *v )
#                                               include <tab/public/lget>
;

/** Get value for the data item associated to the keyword l. Vector entries. The size of the data is assumed to 
    match the size of the internal store for the data item. Only the i-th entry in the data item is retrieved.
   @param     l                Data item keyword.
   @param     i                Position in data item.
   @param     v                Value.
   @brief Get data item.
  */
         template < typename type > bool      get( string l, Int i, type *v )
#                                               include <tab/public/lgeti>
;

/** Get values for the data item associated to the keyword l. Vector entries. The size of the data is assumed to 
    match the size of the internal store for the data item. The whole vector data item is retrieved.
   @param     l                Data item keyword.
   @param     v                Value.
   @brief Get data item.
  */
         template < typename type > bool      getv( string l, type *v )
#                                               include <tab/public/lgetv>
;
/**@}*/

  };

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          Luca di Mare <<EMAIL>>
// Created         Sat Nov 27 17:31:14 GMT 2010
// Changes History -
// Next Change(s)  -

/**@ingroup tab */

   class cTabSrc
  {
      protected:
                                               cTabSrc(){};
      public:

         virtual                              ~cTabSrc(){};
         virtual void                           get( cTabData *var ){};
         virtual void                           set( cTabData *var ){};

  };

#  endif
