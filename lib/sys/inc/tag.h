#  ifndef _TRACED_
#  define _TRACED_

#  include <string>
#  include <pickle/proto.h>
#  include <uid.h>

/**@ingroup tags
    The tagging system associates arbitrary entities with an cUid entity for tracking and identification purposes.
   @see   cUid
   @brief Tagging system
  */

/**@ingroup tags
    cTag implements a basic tagging system. A tagged object is univocally associated to a cUid object.
    Multiple cTag objects can be associated to each cUid object, but they are not referenced by it.
   @brief Basic tagging system.
  */


   class cTag: public cPickle
  {
      protected:
         ident_t                             sid; /**< source object uid. */
         cUid                               *src; /**< source object address. */
         string                              tag; /**< identification tag. */
      public:
                                            cTag();
         virtual                           ~cTag();

         virtual                            void copy( cTag *var ){ var->sid= sid; var->src= src; var->tag= tag; };

         virtual bool                     tagged();
/*!@name tag management methods
 */
/*@{*/


/**
    settrace sets the source of the traced object to obj  and its tag to data.
   @param    obj source address.
   @param    data tag.
   @brief    Trace assignement
 */
         virtual void                   settrace( cUid  *obj, string  data );
/**
    settrace sets  obj  to the source of the traced object and data to its tag.
   @param    obj source address.
   @param    data tag.
   @brief    Trace retrieval
 */
         virtual void                   gettrace( cUid **obj, string *data );

/**
    gettag sets the source the traced object to obj.
   @param    obj source address.
   @brief    Trace retrieval
 */
         virtual void                     setsrc( cUid *obj );
/**
    gettag sets the tag the traced object to data.
   @param    data source address.
   @brief    Trace retrieval
 */
         virtual void                     settag( string data );

/**
    gettag returns the tag of the traced object.
   @return    traced object tag.
   @brief    Trace retrieval
 */
         virtual string                   gettag( ){ return tag; };

/**
    getsrc returns the source of the traced object.
   @return    traced object source.
   @brief    Trace retrieval
 */
         virtual cUid                    *getsrc( ){ return src; };
/**
    getsid returns the uid of the source of the traced object.
   @return    traced object source uid.
   @brief    Trace retrieval
 */
         virtual ident_t                  getsid( ){ return sid; };

/**
    copytrace copies the whole trace information from the current object to obj
   @return    obj    object receiving trace information
   @brief    Trace copy
 */
         virtual void                  copytrace( cTag *obj );
/**
    comparetrace compares the whole trace information from the current object with the one held by obj
   @return    obj    object receiving trace information
   @brief    Trace comparison
 */
         virtual bool               comparetrace( cTag *obj );
/*@}*/

/*!@name Pickle personality
 */
/*@{*/
         virtual void                     pickle( size_t *, pickle_t *);
         virtual void                   unpickle( size_t *, pickle_t  );
/*@}*/

         virtual void                      check( string );
  };

#  endif
