#  ifndef _TCPIP_
#  define _TCPIP_

#  include <sys/socket.h>
#  include <netinet/in.h>
#  include <arpa/inet.h>
#  include <netdb.h>
#  include <string.h>
#  include <stdlib.h>
#  include <unistd.h>

#  include <sig.h>
#  include <pickle/proto.h>

/**@ingroup tcpip */
/**@{*/

//! Default UNIX socket address 
#  define   DPORT          2000

//! Default IP address (loopback device)
#  define   DIPADDR  "127.0.0.1"

/** Default TCP/IP signals */
   enum
  {
      sig_tcpip_stop, //!< TCP/IP server stop signal.
      sig_tcpip       //!< TCP/IP server null signal.
  };

/** Default TCP/IP server stop signal handler.
   @param data                                  User data. 
   @param sig                                   Signal handler object.
   @see   sigf_t
   @brief Stop signal handler.
  */
   void tcpip_stop( sigdata_t data, cSig *sig );

   typedef  int                         socket_t; //!< Default socket type.
   typedef  int                           port_t; //!< Default port type.
   typedef  int                            err_t; //!< Default error type.

/**@}*/

/**@ingroup tcpip 
    cTcpip server provides a basic TCP/IP client/server pair. The cTcpip server is started by invoking the
    start() method. The start() method initialise the UNIX socket connection and enters an infinite loop
    listening for requests from clients. The infinite loop is interrupted when the signl_tcpip_stop signal is
    received. The server address is initialised on the client side by the method open().
   @brief Basic TCPIP communication facility.
  */

   class cTcpip: public cSig, public cPickle
  {
      protected:

         string                              cip; //!< Client IP address.
         string                              sip; //!< Server IP address.
         pid_t                               cid; //!< Client POSIX thread id.
         pid_t                               sid; //!< Server POSIX thread id.
         port_t                             port; //!< Connection UNIX port.
         err_t                               err; //!< Error state.
         bool                                run; //!< Up/down state.

         size_t                              len; //!< Data buffer length.
         pickle_t                            buf; //!< Data buffer.

         socket_t                           scom; //!< Server permanent UNIX socket.
         socket_t                            com; //!< Client and server ephemeral UNIX socket.

         signl_t                             sig; //!< Signal.
         int                                 lev; //!< Service level


/** Clear deletes the data buffer and sets its length to 0.
   @brief Clear data buffer.
  */
         virtual void                      clear();

/**@name cPickle personality */
/**@{*/
         virtual void                     pickle( size_t*, pickle_t * );
         virtual void                   unpickle( size_t*, pickle_t );
/**@}*/

/**@name Communications */
/**@{*/
  
/** Sends the current status and the contents of the data buffer to the active socket (com).
    The buffer is cleared once the communication is over.
   @brief Data buffer send.
   @see   com
   */
         virtual void                    outtray();
  
/** Receives a message from the active socket, updates the current status and places it in the data buffer(com).
   @brief Data buffer receive.
   @see   com
   */
         virtual void                     intray();

/** Hung up connection. hungup() also clears the data buffer.
   @brief Hungup.
   */

         virtual void                     hungup();

/**@}*/

      public:

                                          cTcpip();
         virtual                         ~cTcpip();

/** Returns the status of the server.
   @brief Status enquiry.
  */
                 bool                         up(){ return  run; };

/** Startup the server. start() opens the first available port, starting from DPORT and sets it into listen mode.
   @brief Server startup.
  */
         virtual void                      start();

/** Set the port number and IP address for connection. Ignored on the server side.
   @brief Server TCP/IP address.
  */
         virtual void                       open( string, port_t );

/** Sends the stop signal to the server.
   @brief Client stop.
  */
         virtual void                       stop();

/** Server stop signal handler.
   @brief Server stop.
  */

         virtual void                      rstop();

/** Initialises connection to the server from the client side.
   @brief Connection startup.
  */
         virtual void                       dial();

/** Print to std::cout the current status of the server 
   @brief Checkup
  */
         virtual void                      check();

  };

#  endif
