#  ifndef _RANDOM_
#  define _RANDOM_

#include <cmath>
#include <cprec.h>

/**@ingroup system
  *@{
 **/

/**@defgroup rand Random numbers
  *@{
 **/

/** Uniform deviates between 0 and 1.
   @param              idum            seed.
 **/
   Real ran1(long *idum);

/** Gaussian distribution with 0 avreage and unit standard deviation.
   @param              idum            seed.
 **/
   Real gasdev(long *idum);

/**
  *@}
 **/

/**
  *@}
 **/

#  endif
