c23456789 123456789 123456789 123456789 123456789 123456789 123456789 12
c        1         2         3         4         5         6         7

c ... Author          <PERSON> <<EMAIL>>
c ... Created         Mon May 12 11:55:54 BST 2008
c ... Changes History -
c ... Next Change(s)  -
c ... Purpose         ( work in progress )

c*@{ @ingroup precision

#ifdef _32

#  define          Int  integer*4
#  define          Real real*8
#  define          Pntr integer*4
#  define          INT(i) integer(i,kind=4)  

#else

#  define          Int  integer*4
#  define          Real real*8
#  define          Pntr integer*8
#  define          INT(i) integer(i,kind=4)  

c*@}
#endif
