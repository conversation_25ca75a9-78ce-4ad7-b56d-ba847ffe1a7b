#  ifndef _AUDIT_
#  define _AUDIT_

#  include <sstream>
#  include <iostream>
#  include <uid.h>
#  include <utils/proto.h>


/*3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
         1         2         3         4         5         6         7         8         9         0         1         2

   <PERSON>          <PERSON> <<EMAIL>>
   Created         Wed Jul 14 18:11:59 BST 2010
   Changes History -
   Next Change(s)  ( work in progress )
 */

/**@ingroup audit
    audit_t enumerates the possible results of comparison operations between audit traces.
   @brief Audit comparison return type 
  */

   enum audit_t{ audit_later,                     /**< Audit trace postdates reference */
                 audit_earlier,                   /**< Audit trace predates  reference */
                 audit_match,                     /**< Audit trace matches reference */
                 audit_mismatch,                  /**< Audit trace inconsistent with reference */
                 audit_invalid                    /**< Audit trace invalid */
               };                 

/**@ingroup audit 
    cAudit implements a basic auditing system. The audit objects are represented simply as a linked list.
    New entries are always added at the tail and record date, username, host and a brief description of the
    transaction. Audit records are also tagged with a random value. Audit traces can be compared with the compare()
    method. The return values for compare() are defined by the enum audit_t.
   @brief Basic audit record object.
  */

   class cAudit: public cUid
  {
      protected:

         time_t                              tme; /**< Audit record date (GMT). */
         string                              usr; /**< User name. */
         string                              hst; /**< Host. */
         string                             note; /**< Brief description of the transaction. */

         cAudit                             *nxt; /**< Pointer. */

      public:

                                          cAudit();
/** Create a new audit record using data as transaction description
   @param data                                  Transaction description.
   @brief                                       Transaction description constructor.
  */
                                          cAudit( string data );
         virtual                         ~cAudit();

/**@name cPickle personality */
/**@{*/
         virtual void                     pickle( size_t*, pickle_t * );
         virtual void                   unpickle( size_t*, pickle_t );
/**@}*/
         
/** Print a line to std::iostream detailing the audit record, preceeded by the string tab.
   @param tab                                   Left justification string
   @brief Standard output check
  */
         virtual void                      check( string );

/** Update the audit trace adding a new record with brief description data.
   @param data                                  Brief description of the transaction.
   @brief Audit trace update
  */
         virtual void                      touch( string );

/** Check whether the audit trace data is the same as the current audit trace.
   @param data                                  Audit trace.
   @brief Audit trace identification.
  */
         virtual bool                       same( cAudit *data );

/** Compare the current audit trace with the trace data.
   @param data                                  Audit trace.
   @brief Audit trace comparison.
  */
         virtual audit_t                 compare( cAudit *data );

/** Copy the current audit trace into the trace data. The trace data must be empty.
   @param data                                  Destination trace.
   @brief Copy constructor.
  */
  
         virtual void                       copy( cAudit *data );

  };

   class cAudited: public cUid
  {
      protected:
         cAudit                           *audit;
                                        cAudited(){ audit= NULL; };
      public:
         virtual                       ~cAudited(){ delete audit; audit= NULL; };
         virtual audit_t                 compare( cAudited * );

         virtual void                     pickle( size_t *, pickle_t * );
         virtual void                   unpickle( size_t *, pickle_t   );        
         virtual void                      touch( string );
         virtual void                      check( string );
  };

#  endif
