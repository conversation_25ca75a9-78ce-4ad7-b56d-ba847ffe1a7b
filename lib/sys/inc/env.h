#  ifndef _ENVC_
#  define _ENVC_

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Mon May  5 23:03:58 BST 2008
// Changes History -
// Next Change(s)  -
// Purpose         ( work in progress )

#  include <mpi.h>
#  include <string>
#  include <cstdlib>

#  include <cprec.h>
#  include <const.h>
#  include <utils/proto.h>

/**@ ingroup sys
  *@{
 **/

/**@ defgroup env Environment  
     Environment management
  *@{
 **/

/** The class cEnv enrolls the current process in a MPI_WORLD and initializes parallel computations.
    It also provides facilities to handle command line options
 **/

   class cEnv
  {
      protected:

        int        rank;           /**< Local core index **/
        int        size;           /**< Number of cores in the MPI world **/
        Int        nopt;           /**< Number of command line options **/
        string    *opt;            /**< Option labels **/
        string    *val;            /**< Option values **/

        Real    rtime0;            /**< Clock value at beginning of the run **/
        Real     rtick;            /**< Clock tick. **/
      public:

/** Default constructor **/
        cEnv();

/** Command line  constructor.  If the main program has header int main( int argc, char **argv )
    then cEnv is constructed as cEnv(&argc,&argv).
   @param         argc             Number of command line arguments. 
   @param         argv             Command line arguments.
 **/
        cEnv( int *argc, char ***argv );

/** Destructor **/
        virtual ~cEnv();

/** Come out of MPI **/
        void exit( );

/** Return the value of argument
       @param        arg           command line argument label
       @return                     a string with the value of the argument labelled arg
 **/
        string getarg( string arg );

/** Return the size of the MPI world
 **/
        int    getncpu(){ return size; };
/** Return the position of the local cpu in the MPI world 
 **/
        int    getrank(){ return rank; };

/** Return the current runtime **/
        Real     runtime();
  };

/**
  *@}
 **/

/**
  *@}
 **/

#endif
