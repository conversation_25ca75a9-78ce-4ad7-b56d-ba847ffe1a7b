#ifndef _TXTFLE_
#define _TXTFLE_

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Sun May 11 22:37:08 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         text file

#include <cprec.h>
#include <utils/proto.h>

   class cTxtfle
  {
      private:
         Int              nl;
         Int             *nw;
         string        **txt;

      public:

	//! Default constructor
         cTxtfle(){ nl= 0; nw= NULL; txt= NULL; };

	//! Construct from......
         cTxtfle( string , string , string );
        ~cTxtfle();

	/*!
	 * \param name a string
	 * \param comments a string
	 * \param seps a string
	 * \return no return type
	 */
         void            read( string , string , string );

	/*!
	 * \param argc an Int
	 * \param argv a pointer to char-type pointer
	 * \return return an Int
	 */
         int         fromcmdl( int, char ** );

	/*!
	 * \return return an Int
	 */
         Int           nlines(){ return nl; };

	/*!
	 * \param i an Int
	 * \return return an Int
	 */
         Int           nwords( Int i ){ return(nw[i]); };

	/*!
	 * \param il an Int
	 * \return return an pointer to string
	 */
         string         *text( Int il ){ return(txt[il]); };

	/*!
	 * \param il an Int
	 * \param iw an Int
	 * \return return a string
	 */
         string          text( Int il, Int iw ){ return(txt[il][iw]); };

	/*!
	 * \param il an Int
	 * \param iw an Int
	 * \return return a pointer to stringstream
	 */
//       stringstream &stream( Int , Int );

	/*!
	 * \param text a string
	 * \param ic an Int
	 * \return return an Int
	 */
         Int          whereis( string , Int  );

  };
//!Parse strings based on a given syntax
/*!
 * \param line a pointer to string
 * \param n1 a pointer to Int
 * \param n2 a pointer to Int
 * \param csyn a pointer to const string
 * \return no return typr
 */
   void parse( string *line, Int *n1, Int *n2, const string *csyn );

#endif
