#  ifndef _CPREC_
#  define _CPREC_

/*3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
         1         2         3         4         5         6         7         8         9         0         1         2

   <PERSON>          <PERSON> <<EMAIL>>
   Created         Wed Jul 14 18:11:25 BST 2010
   Changes History -
   Next Change(s)  ( work in progress )
 */
            
#    define Int                           int
#ifdef FP32
#    define Real                          float
#else
#    define Real                          double
#endif
#    define Real64                        double


/**@ingroup precision */

/**@{*/

/** Print to std::iostream the size of C/C++ default integer, real and pointer types, as declared by 
    Int, Real and void *. 
   @brief C/C++ precision enquiry.
  */
     void                               cprecenq( );

/** Print to standard output the size of Fortran default integer, real and pointer types, as declared by 
    Int, Real and Ptr.
   @brief Fortran precision enquiry.
  */
     extern "C" void                    fprecenq( );

/**@}*/

#endif
