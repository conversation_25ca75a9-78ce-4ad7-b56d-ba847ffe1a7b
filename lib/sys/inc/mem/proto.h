#ifndef _MEM_
#define _MEM_

#include <cprec.h>
#include <const.h>
#include <utils/proto.h>
#include <pickle/proto.h>

   class cMem: public cPickle
  {
      protected:

         Int              na;
         void           **ar;
         string         *lbl;
         type_t         *typ;
         Int             *n1;
         Int             *n2;
         size_t          *nb;

      public:

	//! Default constructor
         cMem();
         virtual ~cMem();

         template <typename type > void   create( Int m, Int n, type  **var )
#                                         include<mem/public/create>
;
         template <typename type > void  destroy( type  **var )
#                                         include<mem/public/destroy>
;

         template < typename type > void regaddr( Int m, Int n, type *var )
        {
            Int    tmp;
            type_t val=-1;
            size_t len;
            if( (var) )
           {
               len= n*m*sizeof(*var);

               tmp= na;
               append( &na,&ar, (Int)1,(void**)&var ); na=tmp;
               append( &na,&typ,(Int)1,        &val ); na=tmp;
               append( &na,&n1, (Int)1,        &m   ); na=tmp;
               append( &na,&n2, (Int)1,        &n   ); na=tmp;
               append( &na,&nb, (Int)1,        &len );

              }
        }

  };

//   #define ADDR_( i, j, n ) ( i*n+j )
//   inline Int ADDR( Int i, Int j, Int n ) {return i*n+j;}
//   //assume dimension is 3
//   inline Int ADDR( Int i, Int j, Int k, Int n ) {return (i*3+j)*n+k;}

#endif
