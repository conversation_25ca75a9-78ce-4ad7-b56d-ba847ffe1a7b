// void cMemmng::destroy( type **var )
  {

      Int ia,tmp;

      ia= inlst( (void*)(*var), na,ar );
      if( ia > -1 )
     {
         delete[] (*var); (*var)= NULL;

         swap( ar+ia,ar+na-1);
         swap( n1+ia,n1+na-1);
         swap( n2+ia,n2+na-1);
         swap( nb+ia,nb+na-1);

         tmp= na;
         realloc( &na,(Int)-1,&ar ); na=tmp;
         realloc( &na,(Int)-1,&n1 ); na=tmp;
         realloc( &na,(Int)-1,&n2 ); na=tmp;
         realloc( &na,(Int)-1,&nb ); 
     }
      else
     {
         cout << "quasi-sigsegv - deallocating unitialised pointer\n";
         exit(11);

     }
  }
