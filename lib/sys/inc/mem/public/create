// template <typename > void cMem::create( Int m, Int n, type **var  )
  {
      Int    tmp;
      type_t val=-1;
      size_t len;
      if( !(*var) )
     {
        *var= new type[n*m];
         len= n*m*sizeof(**var);

         tmp= na;
         append( &na,&ar, (Int)1,(void**)var ); na=tmp;
         append( &na,&typ,(Int)1,       &val ); na=tmp;
         append( &na,&n1, (Int)1,       &m   ); na=tmp;
         append( &na,&n2, (Int)1,       &n   ); na=tmp;
         append( &na,&nb, (Int)1,       &len ); 
   
     }
      else
     {
         cout << "quasi-sigsegv - allocating initialised Int pointer\n";
         exit(1);
     }
  }
