#  ifndef _SIG_
#  define _SIG_

#  include <cstdlib>


/*3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
         1         2         3         4         5         6         7         8         9         0         1         2

   <PERSON>          <PERSON> <<EMAIL>>
   Created         Wed Jul 14 18:29:07 BST 2010
   Changes History -
   Next Change(s)  ( work in progress )
 */

/**@ingroup signal */
/**@{*/

#  define   SIGS 100

   typedef int                           signl_t;        //!< Default signal type. 
   typedef void                       *sigdata_t;        //!< Default signal handler data type. 

/** sigf_t() is called by the signal handler to perform the actions corresponding to the signal.
   @param data                                  User data for the signal handler.
   @param sig                                   Signal handler object.
   @brief Signal handler prototype.
  */

   typedef void                           sigf_t( sigdata_t data, class cSig *sig );

/**@}*/

/**@ingroup signal 
    cSig provides a basic signal handling capability. The signals are represented by signl_t values and are
    used to reference pointers to signal handling functions. The signals are connected to the signal handling
    functions by the method sigh(). The signal handlers are invoked by the method handle().
    The signal handling functions have prototype specified by sigf_t and accept a pointer to user data and 
    a pointer to the signal handler object. Typically the signal handling functions should be inline and simply
    contain a call to the appropriate method of the signal handling object.
   @brief Basic signal handler.
  */

   class cSig
  {
      protected:

         sigf_t                            *sigf[SIGS];      //!< Array of signal handling functions. 

      public:

                                           cSig(); 
         virtual                          ~cSig(); 

/** sigh() connects the signal sig to the signal handler func.
   @param sig                                   Signal.
   @param func                                  Signal handler.
   @see   sigf_t
   @see   signl_t
  */
         virtual  void                      sigh( signl_t sig, sigf_t *func );

/** handle() invokes the signal handler associate to the signal sig with sigh().
    If no handler is associated to sig, no action is taken.
   @param sig                                   Signal.
   @param data                                  User data
   @see   sigf_t
   @see   sigdata_t
  */
         virtual  void                    handle( signl_t sig, sigdata_t data );

  };

#  endif
