#  ifndef _TUPLE_
#  define _TUPLE_

#  include <tag.h>
#  include <audit.h>
#  include <fstream>

/** ingroup system
  *@{
 **/

/** defgroup tuples
  *@{
 **/

#  define TDLEN  1024

/** A class to reprent arbitrary collections of values. Values are identified by keyword.
    Keywords can be added to the tuple at any time and can be retrieved or given new values. **/

   class cTuple:public cTag, public cAudited
  {
      protected:
         Int                                   n;  /** number of keys stored **/
         Int                                   m;  /** maximum number of keys **/
         string                             *key;  /** keys **/
         string                             *val;  /** values **/
      public:
                                          cTuple();
         virtual                         ~cTuple();
         virtual void                     update( cTuple * );

         virtual void                     pickle( size_t *, pickle_t * );
         virtual void                   unpickle( size_t *, pickle_t   );

/** dump to string the value of the key str **/
         virtual void                      check( string str );

/** add the key str to the tuple. If the tuple already contains a key named str no action takes place. 
   @param        str                              new key
**/
         virtual void                        add( string str );

/** return the value of the key str as a string.
   @param        str                              key
**/
         virtual string                    value( string str );

/** set the value of the key str to the string line.
   @param        str                              key
   @param        line                             value
**/
         virtual void                      value( string str, string line );

         virtual void write( ofstream *fle );
         virtual void  read( ifstream *fle );
  };

   Int which( Int n, cTuple *data, cUid *uid, string tag );

/** 
  *@}
 **/

/** 
  *@}
 **/

#  endif
