#  ifndef _UID_
#  define _UID_

#  include <const.h>
#  include <pickle/proto.h>
#  include <tab/proto.h>
#  include <cstdlib>

   typedef int                         ident_t; /**< Default identiy tag type.  */

   class cUid: public cPickle, public cTabSrc
  {
      protected:
         string                             name; /**< Object name. */
         ident_t                             uid; /**< Object record random id. */
                                            cUid(){ uid=rand(); name= unassigned; };
      public:
         virtual                           ~cUid(){ uid=bad; name= unassigned; };
         ident_t                          getuid(){ return uid; };
         string                          getname(){ return name; };
         void                            setname( string s ){ name= s; };
         virtual void                     pickle( size_t *len, pickle_t *buf ){ 
                                                                                pckle( len,name,buf ); 
                                                                                pckle( len,uid,buf ); };
         virtual void                   unpickle( size_t *len, pickle_t  buf ){ 
                                                                                unpckle( len, &name, buf ); 
                                                                                unpckle( len, &uid,  buf ); 
                                                                                srand(uid);};
         virtual void                      check( string tab ){ cout << name << " (uid) "<< uid <<" @ "<<this<<" "; };

         virtual void                       copy( cUid *var ){ var->name= name; var->uid= uid; };
  };

#  endif
