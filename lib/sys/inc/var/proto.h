#  ifndef _VAR_
#  define _VAR_

#  include <cstdlib>
#  include <utils/proto.h>

   class cVar
  {
      protected:
      public:
         cVar(){};
         virtual ~cVar(){};
/*       template<typename type> void create( Int n, Int m, type **v ){};
         template<typename type> bool is( type *var ){ return false; };*/
  };

   template< typename type > class cTVar: public cVar
  {
      protected:
         Int                 n1,n2;
         type              **var;
      public:
         cTVar()
#  include           <var/public/constructor>
;
         virtual ~cTVar()
#  include           <var/public/destructor>
;
         void create( Int n, Int m, type **v )
#  include           <var/public/create>
;
         bool is( type *v ){ return (*var)==v; };
  };

#  endif
