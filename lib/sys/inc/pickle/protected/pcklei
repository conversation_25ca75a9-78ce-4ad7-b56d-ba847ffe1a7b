// template <typename type> void cPickle::pckle( size_t *len, const Int n, Int *iref, const type *var, pickle_t buf )
  {
      type*                                  tmp=NULL;
      Int                                    i,j;
      size_t                                   l= (size_t)(n*sizeof(*var));
      tmp= new type[n];
      for( j=0;j<n;j++ )
     {
         i= iref[j];
         tmp[j]= var[i];
     }
      memcpy( (void*)(buf+(*len)),  (const void*)(tmp),        l  ); (*len)+= l;
      delete[] tmp;
  }
