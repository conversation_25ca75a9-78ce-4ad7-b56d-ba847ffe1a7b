// template <typename type> void cPickle::pckle( size_t *len, const type var, pickle_t *buf )
  {
      pickle_t                               tmp= NULL;
      size_t                                   l= sizeof(var);
      tmp= new pickle_v[l+(*len)];
      memcpy( (void*)(tmp),         (const void*)(*buf), (*len) );
      memcpy( (void*)(tmp+(*len)),  (const void*)(&var),   l  ); (*len)+= l; 
      delete[] *buf;
     *buf= tmp;
  }
