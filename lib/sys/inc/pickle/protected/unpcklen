// template <typename type> void cPickle::unpckle( size_t *len, Int *n, type **var, const pickle_t buf )
  {
      memcpy( (void*) n  , (const void*)(buf+(*len)),  sizeof(*n)  ); (*len)+= sizeof(*n);  
      if( *var ){ cout << "unpickle quasi sig-segv: overwriting buffer!\n"; exit(1); };
      size_t l= (size_t)((*n)*sizeof(**var));
     *var= new type[*n];
      memcpy( (void*)(*var), (const void*)(buf+(*len)),  l  ); (*len)+= l; 
  }
