#  ifndef _PICKLE_
#  define _PICKLE_

#  include <cstring>
#  include <cstdlib>
#  include <iostream>

#  include <utils/proto.h>
#ifdef _OPENACC
#  include <cuda_runtime.h>
#  include <openacc.h>
#endif

/*3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
         1         2         3         4         5         6         7         8         9         0         1         2

   Author          <PERSON> <<EMAIL>>
   Created         Wed Jul 14 18:30:01 BST 2010
   Changes History -
   Next Change(s)  ( work in progress )
 */

/**@ingroup pickle*/
/**@{*/

   typedef char*   pickle_t; //!< Default pickle data type pointer.
   typedef char    pickle_v; //!< Default pickle data type value.
   typedef Int       type_t; //!< Atomic data type identifiers.

/**@}*/

/**@ingroup pickle 
    cPickle provides a base data pickling facility. Data pickling is a concept derived from Python and consists
    in packing the whole content of an object in a binary stream. This is useful when transferring complex 
    data structures across networks or in preparation for I/O. The same facility is used to support drag'n'drop
    support, do/undo support and interprocess communication. 
    cPickle provides a set of protected methods for pickling/unpickling 
    basic types, e.g. integers, reals, strings and arrays thereof. The public interface is realised by two methods 
    only, pickle() and unpickle().
  */

   class cPickle
  {

      protected:

/** Pickle a scalar atomic value var and add it to the buffer buf. The length len of the buffer is updated accordingly.
   @param len                                  Pickle buffer size.
   @param var                                  Data item to pickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief Atomic type pickle.
 */
         template <typename type> void     pckle( size_t *len,            const type    var, pickle_t *buf )
#                                                                include<pickle/protected/pckle>
;

/** Pickle a string var and add it to the buffer buf. The length len of the buffer is updated accordingly.
   @param len                                  Pickle buffer size.
   @param var                                  Data item to pickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief String pickle.
 */
                                  void     pckle( size_t *len,            const string  var, pickle_t *buf )
#                                                                include<pickle/protected/pckles>
;

/** Pickle a vector of length n of atomic values var and add it to the buffer buf. The buffer is reallocated.
    The length len of the buffer is updated accordingly.
   @param len                                  Pickle buffer size.
   @param n                                    Vector length.
   @param var                                  Data vector item to pickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief Vector pickle.
 */
         template <typename type> void     pckle( size_t *len, Int  n, const type   *var, pickle_t *buf )
#                                                                include<pickle/protected/pcklen>
;

/** Pickle a vector of length n of atomic values var and add it to the buffer buf. The buffer is reallocated.
    The length len of the buffer is updated accordingly.
   @param len                                  Pickle buffer size.
   @param n                                    Vector length.
   @param var                                  Data vector item to pickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief Vector pickle.
 */
         void     pckle( size_t *len, Int  n, const string   *var, pickle_t *buf )
#                                                                include<pickle/protected/pcklesn>
;

/** Pickle a vector of length n of atomic values var and add it to the buffer buf. The buffer is not reallocated. 
    The length len of the buffer is updated accordingly.
   @param len                                  Pickle buffer size.
   @param n                                    Vector length.
   @param var                                  Data vector item to pickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief Vector pickle.
 */
         template <typename type> void     pckle( size_t *len, Int  n, const type   *var, pickle_t buf )
#                                                                include<pickle/protected/pcklens>
;

/** Pickle n selected entries iref from a vector of atomic values var and add it to the buffer buf. The buffer is not reallocated. 
    The length len of the buffer is updated accordingly.
   @param len                                  Pickle buffer size.
   @param n                                    Vector length.
   @param var                                  Data vector item to pickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief Vector pickle.
 */
         template <typename type> void     pckle( size_t *len, Int  n, Int *iref, const type   *var, pickle_t buf )
#                                                                include<pickle/protected/pcklei>
;

/** Unpickle a scalar atomic value var from the buffer buf. 
    The current position in the buffer is update accordingly. 
   @param len                                  Current position in the buffer.
   @param var                                  Data vector item to unpickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief Atomic type unpickle.
 */

         template <typename type> void   unpckle( size_t *len,                  type   *var, pickle_t  buf )
#                                                                include<pickle/protected/unpckle>
;

/** Unpickle a string var from the buffer buf. 
    The current position in the buffer is update accordingly. 
   @param len                                  Current position in the buffer.
   @param var                                  String to unpickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief String unpickle.
 */
                                  void   unpckle( size_t *len,                string   *var, pickle_t  buf )
#                                                                include<pickle/protected/unpckles>
;

/** Unpickle a vector of length n of atomic values var from the buffer buf. The storage for the vector is 
    created afresh.
    The current position in the buffer is updated accordingly.
   @param len                                  Current position in the buffer.
   @param n                                    Vector length.
   @param var                                  Data vector item to unpickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief Vector unpickle.
 */
         template <typename type> void   unpckle( size_t *len, Int *n,       type  **var, pickle_t  buf )
#                                                                include<pickle/protected/unpcklen>
;

/** Unpickle a vector of length n of atomic values var from the buffer buf. The storage for the vector is 
    created afresh.
    The current position in the buffer is updated accordingly.
   @param len                                  Current position in the buffer.
   @param n                                    Vector length.
   @param var                                  Data vector item to unpickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief Vector unpickle.
 */
         void   unpckle( size_t *len, Int *n,       string  **var, pickle_t  buf )
#                                                                include<pickle/protected/unpcklesn>
;

/** Unpickle a vector of length n of atomic values var from the buffer buf. The storage for the vector is 
    not created afresh.
    The current position in the buffer is updated accordingly.
   @param len                                  Current position in the buffer.
   @param n                                    Vector length.
   @param var                                  Data vector item to unpickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief Vector unpickle.
 */
         template <typename type> void   unpckle( size_t *len, Int n,       type  *var, pickle_t  buf )
#                                                                include<pickle/protected/unpcklens>
;

/** Unpickle a vector of length n of atomic values var from the buffer buf. The storage for the vector is 
    not created afresh.
    The current position in the buffer is updated accordingly.
   @param len                                  Current position in the buffer.
   @param n                                    Vector length.
   @param var                                  Data vector item to unpickle.
   @param buf                                  Pickle buffer.
   @see   pickle_t
   @brief Vector unpickle.
 */
                                  void   unpckle( size_t *len, Int n,   string    *var, pickle_t  buf )
#                                                                include<pickle/protected/unpcklenss>
;

#ifdef _OPENACC
     template <typename type> void pcklegpu( size_t *len, const Int n, Int *iref, const type *var, pickle_t buf )
    {
        type*                                  tmp=NULL;
        Int                                    i,j;
        size_t                                   l= (size_t)(n*sizeof(*var));
        tmp= new type[n];
       #pragma acc enter data create(tmp[0:n])
       #pragma acc parallel loop \
        present(iref[0:n],tmp[0:n],var[0:n]) \
        default(none)
        for( j=0;j<n;j++ )
       {
           i= iref[j];
           tmp[j]= var[i];
       }
        #pragma acc host_data use_device(buf,tmp) 
       {
           cudaMemcpy( (void*)(buf+(*len)),  (const void*)(tmp),        l,cudaMemcpyDeviceToDevice);
       }
       (*len)+= l;
       #pragma acc exit data delete(tmp[0:n])
        delete[] tmp;
    }
#endif

      public:

                                         cPickle(){};
         virtual                        ~cPickle(){};

         virtual Int                 pcklefamily(){ return -1; };


/** Pickles the current object and add it to the buffer buf. The length len of the buffer is updated accordingly.
   @param len                                  Pickle buffer size.
   @param buf                                  Pickle buffer.
   @brief Object pickle.
  */
         virtual void                     pickle( size_t *len, pickle_t *buf ){};

/** Unpickles the current object from the buffer buf. The current position in the buffer is updated accordingly.
   @param len                                  Pickle buffer size.
   @param buf                                  Pickle buffer.
   @brief Object unpickle.
  */
         virtual void                   unpickle( size_t *len, pickle_t buf ){};
  };
         
#  endif
