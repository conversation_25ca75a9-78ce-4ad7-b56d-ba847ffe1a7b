#  ifndef _EVENT_
#  define _EVENT_

#  include <utils/proto.h>
#  include <mpi.h>
#  include <fstream>

#  define MXEVENTS 10000

   class cEvent
  {
      protected:
         Int      n;
         ofstream fle;
         Int     *ibuf;
         Real    *rbuf;
         string  *sbuf;
         bool     flag; 
         void flush();
      public:
         cEvent();
         ~cEvent();

         void open( string name );
         void log( Int iev, string label );
          
  };

#  endif
