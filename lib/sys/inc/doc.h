#  ifndef _DOC_
#  define _DOC_

/**@defgroup system 
    Core system library. 
  */

/**@defgroup gui
    Basic gui utilities
  */

/**@defgroup dnd
    Drag'n'drop support
   @ingroup  gui  */

/**@defgroup tabview
    Tab-based data display
   @ingroup  gui  */

/**@defgroup base
   @ingroup system
    Abstract database representation.
  */

/**@defgroup constants 
    System wide constants. 
   @ingroup  system  */

/**@defgroup precision 
    System wide precsion.
   @ingroup  system  
  */

/**@defgroup utils
    General purpose utilities.
   @ingroup  system  
  */

/**@defgroup thread 
    POSIX-based joinable thread management.
   @ingroup  system  
  */

/**@defgroup audit
    Auditing system.
   @ingroup system */  

/**@defgroup tags
    Tagging system.
   @ingroup system */  

/**@defgroup signal
    Signal handling.
   @ingroup system 
  */  

/**@defgroup pickle
    Data pickling/unpickling.
   @ingroup system
  */

/**@defgroup tab
    Keyword- and list-based access.
   @ingroup system
  */

/**@defgroup tcpip
    TCP/IP based services.
   @ingroup system
  */

/**@defgroup gtk
    database gtk based server.
   @ingroup system
  */

/**@defgroup gtkserver
    gtk based server template.
   @ingroup base
  */

/**@defgroup tcpipserver
    tcpip based server template.
   @ingroup base
  */

/**@defgroup binioserver
    binary i/o based server template.
   @ingroup base
  */

/**@defgroup streamserver
    ASCII stream based server template.
   @ingroup base
  */

/**@mainpage
    This is the main source code documentation for the AU3X system.
   @section History
   @section Architecture
   @subsection Directories
   @section Guidelines
   @subsection Formatting
   @subsection Documentation
   @subsection Acceptance
  */

#endif
