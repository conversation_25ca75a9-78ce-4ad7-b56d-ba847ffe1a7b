# include         <fprec.h>

c23456789 123456789 123456789 123456789 123456789 123456789 123456789 12
c        1         2         3         4         5         6         7

c ... Author          <PERSON> <<EMAIL>>
c ... Created         Mon May 12 11:55:54 BST 2008
c ... Changes History -
c ... Next Change(s)  ( work in progress )

      subroutine fprecenq()

      implicit none

c     Int          i0;
c     Int          i1;
c     Real         r0;
c     Pntr         p0;
c     integer      sizeof 
c     external     sizeof 
c     write( *,* ) "Fortran integer   size ",sizeof(i0)
c     write( *,* ) "Fortran Int  size ",     sizeof(i1)
c     write( *,* ) "Fortran Real size ",     sizeof(r0)
c     write( *,* ) "Fortran Pntr size ",     sizeof(p0)

      return

      end
