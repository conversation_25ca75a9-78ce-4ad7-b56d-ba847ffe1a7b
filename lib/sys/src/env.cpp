   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Mon May  5 23:03:58 BST 2008
// Changes History -
// Next Change(s)  -
// Purpose         ( work in progress )

#  include <env.h>

   cEnv::cEnv()
  {
       rank=-1;
       size=-1;
       nopt=0;
       opt=NULL;
       val=NULL;
       rtick=-1;
       rtime0=-1;
  }

   cEnv::cEnv( int *argc, char ***argv )
  {
      Int i,n;
      string host;

      MPI_Init( argc, argv );
      MPI_Comm_rank(  MPI_COMM_WORLD, &rank );
      MPI_Comm_size(  MPI_COMM_WORLD, &size );
      host= getenv( Host );
      rtime0= MPI_Wtime();
      rtick= MPI_Wtick();
      cout << "rank "<<rank<<" of "<<size<<" enrolled on "<<host<<" at "<<rtime0<<" ( "<<rtick<<" )\n";
      MPI_Barrier( MPI_COMM_WORLD );

      i=1;
      n=(*argc)-1;
      nopt=0;
      opt= new string[n];
      val= new string[n];
      while( n >= 2 )
     {
         opt[nopt]= string( (*argv)[i] ); i++;
         val[nopt]= string( (*argv)[i] ); i++;
         cout << "option "<<opt[nopt]<<" "<<val[nopt]<<"\n";
         nopt++;
         n-= 2;
     }

  };

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Mon May  5 23:03:58 BST 2008
// Changes History -
// Next Change(s)  -
// Purpose         ( work in progress )


   cEnv::~cEnv( )
  {
      cout << "rank "<<rank<<" of "<<size<<" signing off after "<<runtime()<<" seconds\n";
      MPI_Barrier( MPI_COMM_WORLD );
      MPI_Finalize();
      delete[] opt; opt=NULL;
      delete[] val; val=NULL;
  }

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Mon May  5 23:03:58 BST 2008
// Changes History -
// Next Change(s)  -
// Purpose         ( work in progress )

   void cEnv::exit()
  {

  }

   string cEnv::getarg( string s )
  {
      Int i;
      string arg="";
      i= inlst( s,nopt,opt );
      if( i != -1 )
     {
         arg= val[i]; 
     } 
      return arg;
  }

   Real cEnv::runtime()
  {
      Real val;
      MPI_Barrier(MPI_COMM_WORLD);
      val= MPI_Wtime();
      val-= rtime0;
      return val;
  }
