using namespace std;

#  include <tag.h>

   cTag::cTag()
  {
      src=NULL;
      sid= bad;
      tag=unassigned;
  }

   cTag::~cTag()
  { 
      src= NULL;
      sid= bad;
      tag="";
  };


   bool cTag::tagged()
  { 
      return ( (tag != unassigned ) && ( src ) ); 
  };

   void cTag::setsrc( cUid *s )
  {
      src=    s;
      sid=    s->getuid();
  }

   void cTag::settag( string t )
  {
      tag= t;
  }

   void cTag::settrace( cUid *u, string t )
  {
      setsrc(u);
      settag(t);
  }

   void cTag::gettrace( cUid **nmd, string *tg )
  {
      *nmd= src;
      *tg=  tag;
  }

   bool cTag::comparetrace( cTag *t )
  {
      bool val;
      val= ( src== t->src && tag == t->tag );
      return val;
  }

   void cTag::copytrace( cTag *t )
  {
      src=    t->src;
      tag=    t->tag;
  }

   void cTag::pickle( size_t *len, pickle_t *buf )
  {
      pckle( len,src->getuid(),buf );
      pckle( len,tag,          buf );
  }

   void cTag::unpickle( size_t *len, pickle_t buf )
  {
      unpckle( len,&sid,buf );
      unpckle( len,&tag,buf );
  }

   void cTag::check( string tab )
  {
      if( tagged() )
     {
         cout << tab << " " << tag << " attached to "<< src << " ( "<<src->getuid() << " ) ";
     }
      else
     {
         cout << "cannot check an untagged object\n";
         exit(0);
     }
  }
