   using namespace std;

#  include <tab/proto.h>
#  include <cassert>

   void cTabItem::nullify()
  { 
      n=0;
      ncl=0; 
      m=0; 
//    val= new string[m];
      val= NULL;
      rtyp= tbdbad; 
      hlp= "no help"; 
      hlp1= ""; 
      n_fix=0;
      n_flx=0;
      n_gmember=1;
      vrange=  false;
      vlength= false;
      for(Int i=0; i<MXNTBS; i++) {lbc[i]="";}
  }

   cTabItem::cTabItem()
  { 
      nullify();
  }

   cTabItem::cTabItem( cTabItem *var )
  { 
      Int i;
      nullify();
      n=var->n; 
      ncl=n; 
      m=var->m; 
//    assert( n < m );
      val= new string[m]; 
      for( i=0;i<n;i++ )
     {
         val[i]= var->val[i];
         lbc[i]= var->lbc[i];
     }
      hlp= var->hlp;
      hlp1= var->hlp1;
      n_fix= var->n_fix;
      n_flx= var->n_flx;
      n_gmember= var->n_gmember;
      vrange= var->vrange;
      vlength= var->vlength;

      rtyp= var->rtyp; 

  }

   cTabItem::cTabItem( Real   )
  { 
      nullify();
      rtyp= tbdreal;
     
  }

   cTabItem::cTabItem( Int    )
  { 
      nullify();
      rtyp= tbdint;

  }

   cTabItem::cTabItem( bool   )
  { 
      nullify();
      rtyp= tbdbool; 

  }

   cTabItem::cTabItem( string )
  { 
      nullify();
      rtyp= tbdstr; 
  }

   cTabItem::~cTabItem()
  { 
      n=0;
      ncl=0; 
      m=0; 
      delete[] val; val= NULL;
      rtyp= tbdbad;
      hlp= ""; 
      hlp1= ""; 
      n_fix=0;
      n_flx=0;
      n_gmember=1;
      vrange= false;
      vlength= false;
      for(Int i=0; i<MXNTBS; i++) {lbc[i]="";}
  }
   
   bool cTabItem::compat( Real   )
  { 
      return rtyp == tbdreal; 
  };

   string cTabItem::help( Int i )
  {
      Int    j,k,ist,ien;
      string val= hlp;
      string val1= hlp1;
      if( i >= 0 )
     {
         if( i<n_fix )
        {
            val= val+ " ("+strc(i)+")";// + val1;
        }
       /*  else if(i<(n_fix+n_flx))
        {
            val= val+ " {"+strc(i-n_fix)+"}" + val1;
        }*/
         else
        {
            block( i, &j,&k, &ist,&ien );
            if( n_gmember > 1 )
           {
              // val= val1+" ["+strc(j)+":"+strc(k)+"]" + val1;
               val= strc(k+1)+val1+" : ["+strc(j)+"]";
           }
            else
           {
               //val= val+" ("+strc(k)+")" + val1;
               val= val1+" ("+strc(k)+")";
           }
        }
     }
      return val;
  }

   bool cTabItem::compat( Int    )
  { 
      return rtyp == tbdint; 
  };

   bool cTabItem::compat( bool   )
  { 
      return rtyp == tbdbool; 
  };

   bool cTabItem::compat( string )
  { 
      return rtyp == tbdstr; 
  };

   void cTabItem::block( Int i, Int *j, Int *k, Int *ist, Int *ien )
  {
    (*k)=i;
    //(*k)-= (n_fix+n_flx); 
    (*k)-= n_fix;
    (*j)=(*k);
    (*j)= (*j)%n_gmember;
    (*k)/= n_gmember;
    //(*ist)= n_fix+ n_flx+ (*k)*n_gmember;
    (*ist)= n_fix+ (*k)*n_gmember;
    (*ien)= (*ist)+ n_gmember;
  }

   void cTabItem::replicate( Int ist, Int ien, bool left )
  {
      Int i,j,k,kst,ken;
      block( ist, &j,&k, &kst,&ken );
      assert( kst == ist );
      assert( ken == ien );
      if( left )
     {
         for( i=ist,j=0;i<ien;i++,j++ )
        {
            inject( i,val[i+j] );
        }

     }
      else
     {
         for( i=ist,j=ien;i<ien;i++,j++ )
        {
            inject( j,val[i] );
        }
     }
  }

   void cTabItem::check( string tab )
  {
      Int i;
      cout << tab << this <<": ";
      for( i=0;i<n;i++ )
     {
         cout << val[i]<<" | ";
     }
      cout << "\n";
  }

   string cTabItem::fetch( Int i )
  {
      string var=""; 
      if( i < n && rtyp != tbdbad )
     { 
         var= val[i]; 
     }
         return var; 
  }

   void cTabItem::force( Int i, string buf )
  {
      string var=""; 
      if( i < n && rtyp != tbdbad )
     { 
         val[i]= buf; 
     }
  }

   void cTabItem::inject( Int i, string buf )
  {
      Int j;
      if( rtyp != tbdbad && i <= n )
     {
         n++;
         ncl++;
         resize();
         for( j=n-1;j>i;j-- )
        {
            val[j]= val[j-1]; 
        }
         val[i]= buf; 
 
     }
  }

   void cTabItem::remove( Int i )
  {
      Int j;
      for( j=i+1;j<n;j++ )
     {
         val[j-1]= val[j];
     }
      n--;
      ncl--;
     
  }

   void cTabItem::remove( Int ist, Int ien )
  {
      Int j,k,kst,ken;
      block( ist, &j,&k, &kst,&ken );
      if( n != n_fix+n_gmember )
     // if( n != n_fix+n_flx+n_gmember )
     {
         assert( kst == ist );
         assert( ken == ien );
         for( j=ien-1;j>=ist;j-- )
        {
            remove( j );
        }
     }
  }

   cTabData::cTabData()
  {
      m= 0;
      n= 0;
      lbl= NULL;
      tbd= NULL;
  }

   cTabData::~cTabData()
  {
      clear();
  }

   void cTabData::clear()
  {
      Int i;
      for( i=0;i<n;i++ )
     {
         delete tbd[i]; tbd[i]= NULL;
     }
      delete[] lbl; lbl= NULL;
      delete[] tbd; tbd= NULL;
      n= 0;
      m= 0;
  }

   void cTabData::append( string l, cTabItem *v )
  {
      insert( l,n,v );
  }

   void cTabData::insert( string l, Int i, cTabItem *v )
  {
      Int j;
      Int len,dlen;
      if( !which( l ) || l == "" )
     {
         dlen= TABDSIZE;
         if( n >= m )
        {
            len=         m;
            realloc( &len, dlen, &lbl );
            len=         m;
            realloc( &len, dlen, &tbd );
            m= len; 
        } 
         v->resize();
         for( j=n-1;j>=i;j-- )
        {
            lbl[j+1]= lbl[j];
            tbd[j+1]= tbd[j]; 
        }
         lbl[i]= l;
         tbd[i]= v;
         n++;
         v->ncl=v->n;
     }
  }

   void cTabData::replace( string s, Int i, cTabItem *data )
  {
      if( i < n )
     {
         delete tbd[i];
         lbl[i]= s;
         tbd[i]= data;
     }
  }

   void cTabData::remove( Int il )
  {
      Int i;
      if( il < n )
     {
         delete tbd[il];
         for( i=il+1;i<n;i++ )
        {
            tbd[i-1]= tbd[i];
            lbl[i-1]= lbl[i];
        }
         tbd[n-1]= NULL;
         lbl[n-1]= "";
         n--;
     }
  }

   cTabItem *cTabData::which( string l )
  {
      Int i;
      cTabItem *val=NULL;
      if( l != "" )
     {
         for( i=0;i<n;i++ )
        {
            if( l == lbl[i] )
           {
               val=tbd[i];
               break;
           }
        }
     }
/*    if( !val )
     {
         cout << "warning: unknown key "<<l<<"\n";
     }*/
      return val;
  }

   cTabItem *cTabData::which( Int l )
  {
      cTabItem *val=NULL;
      if( l < n )
     {
         val= tbd[l];
     }
      return val;
  }

   Int cTabData::where( string l )
  {
      Int i;
      Int  val=-1;
      if( l != "" )
     {
         for( i=0;i<n;i++ )
        {
            if( l == lbl[i] )
           {
               val=i;
               break;
           }
        }
     }
      return val;
  }

   Int cTabData::where( cTabItem *var )
  {
      Int i;
      Int  val=-1;
      for( i=0;i<n;i++ )
     {
         if( var == tbd[i] )
        {
            val= i;
            break;
        }
     }
      return val;
  }

   void cTabData::resize( string l, Int len )
  {
      cTabItem *val=NULL;
      val= which(l);
      if( val )
     {
         val->n= len;
         val->ncl= len;
     }
  }

   void cTabData::resize( Int ist, Int ien, Int len )
  {
      Int       il;
      cTabItem *tmp;
      Int    dlen; 
      if( ist < n && ien < n && ist < ien )
     {
         dlen= ien- ( ist+1 );
         dlen= len- dlen;
         if( dlen > 0 )
        {
            for( il=0;il<dlen;il++ )
           {
               tmp= new cTabItem();
               insert( "",ist+1,tmp );
           }
        }
         else
        {
            for( il=ien-1;il>ist+len;il-- )
           {
                remove( il );
           } 
        }

     }
  }

  
   string cTabData::label( Int i )
  { 
      string val=""; 
      if( i < n )
     { 
         val= lbl[i]; 
     } 
      return val; 
  }

   void cTabData::labels( Int *nl, string **data )
  {
      Int i;
      assert( !(*data) );
     *nl=0;
      if( n > 0 ) 
     {
        *data= new string[n];
         for( i=0;i<n;i++ )
        {
          (*data)[i]= lbl[i];
        }
        *nl= n;
     }
  }

   void cTabData::force( string l, string v )
  {
      cTabItem *var;
      var= which( l );
      if( var )
     {
         var->force( 0,v );
     }
  }

   void cTabData::check()
  {
      Int i;
      for( i=0;i<n;i++ )
     {
         cout << lbl[i]<<": ";
         tbd[i]->check("+   ");
         cout << "\n";
     }
  }

   void cTabData::copy( Int ist, Int ien, cTabData *var )
  {
      Int i,j;
      var->clear();
      cTabItem *tmp;
      for( j=0,i= ist;i<ien;i++,j++ )
     {
         tmp= new cTabItem( tbd[i] );
         var->append( lbl[i],tmp ); 
         var->lbl[j]= lbl[i];
     }
  }
