   using namespace std;

#  include <event.h>

   cEvent::cEvent()
  {
      n=0;
      ibuf=NULL;
      rbuf=NULL;
      sbuf=NULL;
      flag=false;
  }

   cEvent::~cEvent()
  {
      if( flag )
     {
         flush();
         fle.close();
     } 
      delete[] rbuf; rbuf=NULL;
      delete[] sbuf; sbuf=NULL;
      delete[] ibuf; ibuf=NULL;
      n=0; 
  }

   void cEvent::flush()
  {
      Int i;
    
      if( n == MXEVENTS )
     {
         for(i=0;i<n;i++ )
        {
            fle << rbuf[i]<<" "<<ibuf[i]<<" "<<sbuf[i]<<"\n";
        }
         fle.flush();
         n=0;
     }
  }

   void cEvent::log( Int i, string label )
  {
      if( flag )
     {
         ibuf[n]= i;
         sbuf[n]= label;
         rbuf[n]= MPI_Wtime();
         n++;
         flush();
     }
  }

   void cEvent::open( string name )
  {
      fle.open( name.c_str() );
      flag= true;
  }
