   using namespace std;

#  include <pdata/proto.h>

   void cPdata::range( Int irnk, Int *ist, Int *ien )
  {

     *ist=0;
      if( irnk > 0 ){ *ist= il[irnk-1]; };
     *ien= il[irnk];

  }

   void cPdata::start()
  {
      MPI_Comm   *pcom;
      MPI_Request srq,rrq;
      MPI_Status  stat;
      Int         irnk;
      Int         id,ia;
      Int         ios,ioe,iss,ise,irs,ire,no;
      Int         jso,jo,iso;
     
      if( !started )
     {
         started= true;
         if( master )
        {

            isl= new Int[ncpu+1];
            setv( (Int)0,ncpu+1, (Int)0,isl );

            pcom= par->getcomm();
            irnk= par->getrank();
            ioe=0;

//            cout << par->geticpu(irnk) << " ======================= " << irnk << "\n";

            #ifdef _OPENACC
            int ngpus=acc_get_num_devices(acc_device_nvidia);
            int devicenum=par->geticpu(irnk);
            //int devicenum=irnk;
            acc_set_device_num(devicenum,acc_device_nvidia);
            acc_init(acc_device_nvidia);
            #endif

            for( id=0;id<ncpu;id++ )
           {
               ios= ioe;
               ioe= il[id];
               if( id != irnk )
              {
                  no= ioe-ios;

                  par->lock();
                  MPI_Isend( &no,1,MPI_Int,id,0,*pcom,&srq );
                  MPI_Irecv( &(isl[id]),1,MPI_Int,id,0,*pcom,&rrq );
                  MPI_Wait( &srq,&stat );
                  MPI_Wait( &rrq,&stat );
                  par->unlock();
//                cout << "sent request for "<<no<<" data items to "<<id<<"\n";
//                cout << "received request for "<<isl[id]<<" data items from "<<id<<"\n";
              }
           }

            accml( ncpu+1,isl );
            ns= isl[ncpu];

            is= new Int[ns]; 
            setv( (Int)0,ns, (Int)-1,is );

            range( irnk,&irs,&ire );

            ioe=0;
            ise=0;
            for( id=0;id<ncpu;id++ )
           {
               ios= ioe;
               ioe= il[id];
               iss= ise;
               ise= isl[id];
               if( id != irnk )
              {
                  par->lock();
                  if( ioe > ios )
                 {
                     MPI_Isend( &(ig[ios]),ioe-ios,MPI_Int,id,0,*pcom,&srq );
//                   cout << "sent identities of requested data items to "<<id<<"\n";
                 }
                  if( ise > iss )
                 {
                     MPI_Irecv( &(is[iss]),ise-iss,MPI_Int,id,0,*pcom,&rrq );
//                   cout << "received identities of requested data items from "<<id<<"\n";
                 }

                  MPI_Wait( &srq,&stat );
                  MPI_Wait( &rrq,&stat );
                  par->unlock();

                  for( jo=ios;jo<ioe;jo++ )
                 {
//                   cout << "receive item "<<jo<<" is "<<ig[jo]<<"\n";
                 }

                  for( jso=iss;jso<ise;jso++ )
                 {
                     iso= is[jso];
                     is[jso]= -1;
                     for( jo=irs;jo<ire;jo++ )     
                    {
                        if( ig[jo] == iso )
                       {
                           is[jso]= jo;
                           break;
                       }
                    }
                     if( is[jso] == -1 )
                    {
                        cout << "could not find "<<iso<<"("<<jso<<") in resident data\n";
                        exit(1);
                    }
                     else
                    {
//                      cout << "send item "<<jso<<" is "<<is[jso]<<" ("<<ig[is[jso]]<<")\n";
                    }
                 }
              }
           }

            tags= new Int[na];
            for( ia=0;ia<na;ia++ )
           {
               par->newtag( tags+ia );
           }

            rwait= new bool*[na];
            rreq=  new MPI_Request*[na];
            rbuf=  new pickle_t*[na];
            rsize= new Int*[na];

            wptr= new uintptr_t**[na];

            swait= new bool*[na];
            sreq=  new MPI_Request*[na];
            sbuf=  new pickle_t*[na];
            ssize= new Int*[na];

            setv( (Int)0,na,(bool*)NULL,rwait );
            setv( (Int)0,na,(MPI_Request*)NULL,rreq );
            setv( (Int)0,na,(pickle_t*)NULL,rbuf );
            setv( (Int)0,na,(Int*)NULL,rsize );

            setv( (Int)0,na,(bool*)NULL,swait );
            setv( (Int)0,na,(MPI_Request*)NULL,sreq );
            setv( (Int)0,na,(pickle_t*)NULL,sbuf );
            setv( (Int)0,na,(Int*)NULL,ssize );

            setv( (Int)0,na,(uintptr_t**)NULL,wptr );

           #pragma acc enter data copyin(this)
           #pragma acc enter data copyin(rbuf[0:na])
           #pragma acc enter data copyin(sbuf[0:na])
           #pragma acc enter data copyin(is[0:ns])
           #pragma acc enter data copyin(wptr[0:na])
        }
         else
        {
//          cout << "starting derived cPdata (no action)\n";
        }
     }
  }

   bool cPdata::stransit( Int id, Int ia )
  {
      MPI_Status status;
      int flag;
      Int  jd;

      if( master )
     {

         if( id == ncpu )
        {
            swait[ia][id]=false;
            for( jd=0;jd<ncpu;jd++ )
           {
               swait[ia][id]= ( swait[ia][id] || swait[ia][jd] );
           }
        }
         else
        { 
            if( id < 0 || id > ncpu )
           {
               cout << "this cpu is not in this device\n";
               exit(1);
           }

            if( swait[ia][id] )
           {  
               par->lock();
               MPI_Test( &(sreq[ia][id]),&flag,&status );
               if( flag )
              {
                  MPI_Wait( &(sreq[ia][id]),&status );
                  swait[ia][id]= false;
              }
               par->unlock();
           }
        }
     }
      else
     {
         cout << "you cannot invoke transit with a non-master\n"; 
     }
      return swait[ia][id];
  }


   bool cPdata::rtransit( Int id, Int ia )
  {

      Int                 nv;
      Int                 iv,jd;
      MPI_Status          status;
      int                 flag;
      size_t              len;

      if( master )
     {

         if( id == ncpu )
        {
            rwait[ia][id]=false;
            for( jd=0;jd<ncpu;jd++ )
           {
               rwait[ia][id]= ( rwait[ia][id] || rwait[ia][jd] );
           }
        }
         else
        {
            nv= n1[ia];

            if( id < 0 || id > ncpu-1 )
           {
               cout << "this cpu is not in this device\n";
               exit(1);
           }

            if( rwait[ia][id] )
           {  
               par->lock();
               MPI_Test( &(rreq[ia][id]),&flag,&status );
               if( flag )
              {
                  MPI_Wait( &(rreq[ia][id]),&status );
                  rwait[ia][id]= false;
                  if( !rbuf[ia][id] )
                 {
                     cout << "oh shit!\n";
                     assert( false );
                 }
                 #pragma acc update self(wptr[ia:1][id:1][0:nv])
                  len=0;
                  for( iv=0;iv<nv;iv++ )
                 {
                     #ifdef _OPENACC
                        unsafeunpcklegpu( &len,rsize[ia][id]/nv,(pickle_t)wptr[ia][id][iv],rbuf[ia][id] );
                     #else
                        unsafeunpckle( &len,rsize[ia][id]/nv,(pickle_t)wptr[ia][id][iv],rbuf[ia][id] );//cpu should use this one
                     #endif
                 }
              }
               par->unlock();
           }
        }
     }
      else
     {
         cout << "you cannot invoke transit with a non-master\n"; 
         exit(1);
     }
      return rwait[ia][id];
  }

   void cPdata::exchange( Real *sdata )
  {
      Int ia,id,nv,iv,js;
      Int iss,ise, irs,ire;
      Int irnk;
      MPI_Comm   *pcom;
      MPI_Status  status;

      Real **data;

      if( master )
     {

         irnk= par->getrank();
         pcom= par->getcomm();

         ia= inlst( (void*)sdata,na,ar );
         nv= n1[ia];
         data= new Real*[nv];
         subv( nv,n, sdata, data );
         #ifdef _OPENACC
            #pragma acc enter data create(data[0:nv])
            subvgpu( nv,n, sdata, data );
         #endif

         if( !rwait[ia] ){ rwait[ia]= new bool[ncpu+1]; setv( (Int)0,ncpu+1,false,rwait[ia] ); };
         if( !rsize[ia] ){ rsize[ia]= new  Int[ncpu+1]; setv( (Int)0,ncpu+1,(Int)0,rsize[ia] ); };
         if(  !rreq[ia] ){ rreq[ia]= new MPI_Request[ncpu+1]; };
         if(  !rbuf[ia] )
        {  
            rbuf[ia]= new pickle_t[ncpu+1]; 
            setv( (Int)0,ncpu+1,(pickle_t)NULL,rbuf[ia] ); 
           #pragma acc enter data copyin(rbuf[ia][0:ncpu+1])
        }

         if( !swait[ia] ){ swait[ia]= new bool[ncpu+1]; setv( (Int)0,ncpu+1,false,swait[ia] ); };
         if( !ssize[ia] ){ ssize[ia]= new  Int[ncpu+1]; setv( (Int)0,ncpu+1,(Int)0,ssize[ia] ); };
         if(  !sreq[ia] ){ sreq[ia]= new MPI_Request[ncpu+1]; };
         if(  !sbuf[ia] )
        {  
            sbuf[ia]= new pickle_t[ncpu+1]; 
            setv( (Int)0,ncpu+1,(pickle_t)NULL,sbuf[ia] ); 
           #pragma acc enter data copyin(sbuf[ia][0:ncpu+1])
        }


         if( !wptr[ia] ) 
        { 
            wptr[ia]= new uintptr_t*[ncpu+1]; 
            setv( (Int)0,ncpu+1,(uintptr_t*)NULL,wptr[ia] ); 
           #pragma acc enter data copyin(wptr[ia][0:ncpu+1])
        }

/*      *dbgfle << "received communication request for array "<<ia<<" address "<<sdata<<"\n";
        *dbgfle << "first dimension for this array "<<nv<<"\n";*/

/*       cout << "received communication request for array "<<ia<<" address "<<sdata<<"\n";
         cout << "dimensions for this array "<<n1[ia]<<" "<<n2[ia]<<"\n";*/

         par->newmsg( this, ia );
     
         ise=0;
         ire=0;
         rwait[ia][ncpu]= true;

         for( id=0;id<ncpu;id++ )
        {
            iss= ise; 
            irs= ire; 

            ise= isl[id]; 
            ire= il[id]; 

            if( id != irnk )
           {
               if( ise > iss )
              {
                  if( swait[ia][id] )
                 {  
                     par->lock();
                     MPI_Wait( &(sreq[ia][id]),&status );
                     par->unlock();
//                   par->plog << "hold on, this is strange (send)...\n";
                 }
                  if( !sbuf[ia][id] )
                 {
                     ssize[ia][id]= nv*(ise-iss)*sizeof(*sdata);
                     sbuf[ia][id]= new pickle_v[ssize[ia][id]];
                    #pragma acc enter data copyin(sbuf[ia][id][0:ssize[ia][id]])
//                   cout << this << " created send buffer "<<ia<<" "<<id<<" "<<(void*)sbuf[ia][id]<<" "<<ssize[ia][id]<<"\n";
                 }
                  size_t len=0;
                  for( iv=0;iv<nv;iv++ )
                 {
                     #ifdef _OPENACC
                        pcklegpu( &len,(ise-iss),is+iss,data[iv],sbuf[ia][id] );
                     #else
                        pckle( &len,(ise-iss),is+iss,data[iv],sbuf[ia][id] );
                     #endif
                 }
                  par->lock();
                  pickle_t sptr = sbuf[ia][id];
                 #pragma acc host_data use_device (sptr)
                 {
                     //MPI_Isend( sbuf[ia][id],ssize[ia][id],MPI_BYTE,id,tags[ia],*pcom,&(sreq[ia][id]));
                     MPI_Isend( sptr,ssize[ia][id],MPI_BYTE,id,tags[ia],*pcom,&(sreq[ia][id]));
                 }
                  par->unlock();
//                cout << "the send request "<<ia<<" "<<id<<" request is "<<sreq[ia][id]<<" "<<ssize[ia][id]<<" "<<nv<<" "<<ise-iss<<"\n";
                  swait[ia][id]= true;
              }
               if( ire > irs )
              {
                  if( rwait[ia][id] )
                 {  
                     par->lock();
                     MPI_Wait( &(rreq[ia][id]),&status );
                     par->unlock();
//                   par->plog << "hold on, this is strange (receive)...\n";
                 }
                  if( !rbuf[ia][id] )
                 {
                     rsize[ia][id]= nv*(ire-irs)*sizeof(*sdata);
                     rbuf[ia][id]= new pickle_v[rsize[ia][id]];
                    #pragma acc enter data copyin(rbuf[ia][id][0:rsize[ia][id]])
                     wptr[ia][id]= new uintptr_t[nv];
                    #pragma acc enter data copyin(wptr[ia][id][0:nv])
//                   cout << this << " created receive buffer "<<ia<<" "<<id<<" "<<(void*)rbuf[ia][id]<<" "<<rsize[ia][id]<<"\n";
                    #pragma acc kernels present(data[0:nv],wptr,this)
                    {
                       #pragma acc loop seq
                        for( iv=0;iv<nv;iv++ )
                       {
                           wptr[ia][id][iv]= (uintptr_t)(data[iv]+irs);
                       }
                    }
                 }
                  par->lock();
                  pickle_t rptr = rbuf[ia][id];
                 #pragma acc host_data use_device (rptr)
                 {
                     //MPI_Irecv( rbuf[ia][id],rsize[ia][id],MPI_BYTE,id,tags[ia],*pcom,&(rreq[ia][id]));
                     MPI_Irecv( rptr,rsize[ia][id],MPI_BYTE,id,tags[ia],*pcom,&(rreq[ia][id]));
                 }
                  par->unlock();
//                cout << "the receive request "<<ia<<" "<<id<<" request is "<<rreq[ia][id]<<" "<<rsize[ia][id]<<" "<<nv<<" "<<ire-irs<<"\n";
                  rwait[ia][id]= true;
              }
           }
        }
        #pragma acc exit data delete(data[0:nv])
         delete[] data;
     }
  }
