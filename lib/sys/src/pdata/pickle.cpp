   using namespace std;

#  include <pdata/proto.h>

   void cPdata::pickle( size_t *len, pickle_t *buf )
  {
      pckle( len,    gn,   buf );
      pckle( len,ncpu+1,il,buf );
      pckle( len,     n,ig,buf );
      pckle( len,master,buf );

      pckle( len,    gn_mat,   buf );
      if(gn_mat>0)
     {
         pckle( len,     n,ig_mat,buf );
     }
  }

   void cPdata::unpickle( size_t *len, pickle_t buf )
  {
      Int       mcpu;
      Int       tmpn;
      delete[] il; il=NULL;
      delete[] ig; ig=NULL;
      unpckle( len,           &gn,    buf );
      unpckle( len,  &mcpu,&il,buf ); 
      unpckle( len,     &n,&ig,buf );
      unpckle( len,&master,    buf );

      unpckle( len,           &gn_mat,    buf );
      if(gn_mat>0)
     {
         unpckle( len,     &tmpn,&ig_mat,buf );
         assert(tmpn==n);
         assert(gn_mat==gn);
     }

      if( mcpu-1 != ncpu )
     {
         cout << "common size does not match pickled common size\n";
         exit(11);
     }
  }
