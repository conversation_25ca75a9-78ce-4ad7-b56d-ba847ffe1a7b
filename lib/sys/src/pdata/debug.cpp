
   using namespace std;

#  include <pdata/proto.h>

   void cPdata::stdout()
  {
      Int i;
      cout << "--------------------------------------------------------------------------------\n";
      if( gn > 0 )
     {
         cout << "ppdata object "<<this<<"\n";
         cout << "cpu "<<par->getrank()<<"/"<<par->getncpu()<<"\n";
         cout << "global size "<<gn<<"\n";
         cout << "master "<<master<<"\n";
         cout << "read/write access bookings and data dependencies \n";
         for( i=0;i<gn;i++ )
        {
           cout << i <<": "<<mrkr[i]<<" "<<mrkw[i]<<" "<<prt[i]<<"\n";
        }
     }
      cout << "--------------------------------------------------------------------------------\n";
      cout << "\n";
      cout << "\n";
      cout << "\n";
  }
   void cPdata::summary( ofstream *fle )
  {
      Int id;
      if( n > 0 )
     {
         for( id=0;id<ncpu;id++ )
        {
           *fle << il[id] << " ("<<id<<")  ";
        }
         id= ncpu;
        *fle << il[id] << " (misc.)  ";
        *fle << gn << " (global)  ";
         if( master )
        {
           *fle << " - master partition  ";
        }
         else
        {
           *fle << " - derived partition ";
        }
     }
      else
     {
        *fle << "(none) ";
     }
     *fle << "\n";
  }

   void cPdata::debug( ofstream *fle )
  {
      Int id;
      Int is,ie;
      Int j,i;
      if( n > 0 )
     {
         ie= 0;
         for( id=0;id<ncpu;id++ )
        {
            is= ie;
            ie= il[id];
           *fle << "domain "<<id<<"\n";
            for( j=is;j<ie;j++ )
           {
               i= ig[j];
              *fle << j << " " << i << "\n";
           }
        }
         id= ncpu;
         is= ie;
         ie= il[id];
        *fle << "domain "<<id<<"\n";
         for( j=is;j<ie;j++ )
        {
            i= ig[j];
           *fle << j << " " << i << "\n";
        }
     }
  }
