   using namespace std;

#  include <pdata/proto.h>

   void cPdata::blank()
  {

      par= NULL;
      master= false;
      started= false;

      tags= NULL;

      ncpu= 0;
      gn=0;
      n=0;
      ns=0;
      ig=NULL;
      is=NULL;
      il=NULL;
      isl=NULL;
      mrkw= NULL;
      mrkr= NULL;
      prt=  NULL;

      rbuf=NULL;
      rreq=NULL;
      rwait=NULL;
      rsize=NULL;

      wptr=NULL;

      sbuf=NULL;
      sreq=NULL;
      swait=NULL;
      ssize=NULL;

      gn_mat = 0;
      ig_mat = NULL;
      ig_2_ig_mat = NULL;
  }

   cPdata::cPdata()
  {
      blank();
  }


   cPdata::cPdata( cParal *var )
  {

      blank();
      par= var;
      ncpu=par->getncpu();
      il= new Int[ncpu+1];

  }

   cPdata::cPdata( Int n, cParal *var )
  {

      blank();

      par= var;
      gn=  n;
      ncpu=par->getncpu();
      il= new Int[ncpu+1];

      gn=n;
      mrkr= new Int[gn];
      mrkw= new Int[gn];
      prt= new Int[gn];

      setv( 0,gn, (Int) 0, mrkw );
      setv( 0,gn, (Int) 0, mrkr );
      setv( 0,gn, (Int)-1, prt );


  }

   cPdata::cPdata( Int n, cParal *var, Int *iprt )
  {
      Int  i;
      Int  irnk;
      blank();

      par= var;
      gn=  n;
      ncpu=par->getncpu();
      irnk=par->getrank();
      il= new Int[ncpu+1];

      master= true;
      gn=n;
      mrkw= new Int[gn];
      mrkr= new Int[gn];
      prt= new Int[gn];

      setv( 0,gn, (Int) 0, mrkr );
      setv( 0,gn, (Int) 0, mrkw );
      setv( 0,gn, (Int)-1, prt );

      for( i=0;i<gn;i++ )
     {
         prt[i]= iprt[i];
         if( iprt[i] == irnk )
        {
            mrkr[i]= 1;
            mrkw[i]= 1;
        }
     }

  }

   cPdata::~cPdata()
  {
      Int ia,id;

      if( master )
     {
         if( started )
        {
            for( ia=0;ia<na;ia++ )
           {

               if( sbuf[ia] )
              {
                  for( id=0;id<ncpu+1;id++ )
                 {
                     if( sbuf[ia][id] )
                    { 
                       #pragma acc exit data delete(sbuf[ia][id]])
                        delete[] sbuf[ia][id]; 
                    }
                 }
              }
               if( rbuf[ia] )
              {
                  for( id=0;id<ncpu+1;id++ )
                 {
                     if( rbuf[ia][id] )
                    { 
                       #pragma acc exit data delete(rbuf[ia][id]) 
                       #pragma acc exit data delete(wptr[ia][id])
                        delete[] rbuf[ia][id]; 
                        delete[] wptr[ia][id];
                    }
                 }
              }
              #pragma acc exit data delete(rbuf[ia])
              #pragma acc exit data delete(wptr[ia])
              #pragma acc exit data delete(sbuf[ia])

               delete[] rbuf[ia];
               delete[] rwait[ia];
               delete[] rreq[ia];
               delete[] rsize[ia];

               delete[] wptr[ia];

               delete[] sbuf[ia];
               delete[] swait[ia];
               delete[] sreq[ia];
               delete[] ssize[ia];
           }
           #pragma acc exit data delete(rbuf)
           #pragma acc exit data delete(wptr)
           #pragma acc exit data delete(sbuf)
           #pragma acc exit data delete(is)

            delete[] rbuf; rbuf=NULL;
            delete[] rwait; rwait=NULL;
            delete[] rreq; rreq=NULL;
            delete[] rsize; rsize=NULL;
      
            delete[] wptr; wptr=NULL;
      
            delete[] sbuf; sbuf=NULL;
            delete[] swait; swait=NULL;
            delete[] sreq; sreq=NULL;
            delete[] ssize; ssize=NULL;
            delete[]  is;  is=NULL;
            delete[] isl; isl=NULL;
            delete[] tags; tags=NULL;
        }
     }



      master= true;
      started= false;

      ncpu=0;
      gn=0;
      n=0;
      ns=0;
      delete[]  ig;  ig=NULL;
      delete[]  il;  il=NULL;
      delete[] mrkw; mrkw=NULL;
      delete[] mrkr; mrkr=NULL;
      delete[]  prt;  prt=NULL;

      gn_mat = 0;
      delete[]  ig_mat;  ig_mat=NULL;
      delete[]  ig_2_ig_mat;  ig_2_ig_mat=NULL;
   
  }

