   using namespace std;

#  include <pdata/proto.h>

   void cPdata::wrloop( cPdata *obj, Int m, Int *ir[] )
  {
      Int        i,j,k;
      bool       flag;
      Int        ival=1;
      if( master ){ ival=-1; };
      for( i=0;i<gn;i++ )
     {
//       flag= ( mrkw[i] > 0 );
         flag= ( mrkr[i] > 0 ) || ( mrkw[i] > 0 );
         for( j=0;j<m;j++ )
        {
            k=ir[j][i];   
            flag= flag || ( obj->mrkr[k] > 0 );
        }
         if( flag )
        {
            if( mrkr[i] == 0 )
           {
               mrkr[i]= ival;
           }
            for( j=0;j<m;j++ )
           {
               k= ir[j][i];
               if( obj->mrkw[k] == 0 )
              {
                  obj->mrkw[k]= -1;
              }
           }
        }
     }
  }

   void cPdata::wrloop( cPdata *obj, Int m, cAu3xView<Int>& ir )
  {
      Int        i,j,k;
      bool       flag;
      Int        ival=1;
      if( master ){ ival=-1; };
      for( i=0;i<gn;i++ )
     {
//       flag= ( mrkw[i] > 0 );
         flag= ( mrkr[i] > 0 ) || ( mrkw[i] > 0 );
         for( j=0;j<m;j++ )
        {
            k=ir(j,i);
            flag= flag || ( obj->mrkr[k] > 0 );
        }
         if( flag )
        {
            if( mrkr[i] == 0 )
           {
               mrkr[i]= ival;
           }
            for( j=0;j<m;j++ )
           {
               k= ir(j,i);
               if( obj->mrkw[k] == 0 )
              {
                  obj->mrkw[k]= -1;
              }
           }
        }
     }
  }

   void cPdata::rdloop( cPdata *obj, Int m, Int *ir[] )
  {
      Int        i,j,k;
      Int        id,jd,kd;
      Int        nd,md;

      bool       flag;

      Int       *iwrk=NULL;
      Int        icpu;

      Int        ival=1;

      icpu= par->getrank();

      if( master ){ ival=-1; };
      iwrk= new Int[ncpu+1];
      for( i=0;i<gn;i++ )
     {
         flag= ( mrkr[i] > 0 );
         for( j=0;j<m;j++ )
        {
            k=ir[j][i];   
//          flag= flag || ( obj->mrkw[k] > 0 );
            flag= ( mrkr[i] > 0 ) || ( mrkw[i] > 0 );
        }
         if( flag )
        {
            if( mrkw[i] == 0 )
           {
               mrkw[i]= ival;
           }
            for( j=0;j<m;j++ )
           {
               k= ir[j][i];
               if( obj->mrkr[k] == 0 )
              {
                  obj->mrkr[k]= -1;
              }
           }
// resolve data dependency
            nd=0;
            md=0;
            setv( 0,ncpu+1, (Int)0, iwrk );
            if( obj->master )
           {
               for( j=0;j<m;j++ )
              {
                  k= ir[j][i];
                  id=obj->prt[k];
                  if( iwrk[id] == 0 )
                 {
                     nd++;
                     if( id != icpu )
                    {
                        md++;
                        kd=id;
                    }
 
                 }
                  iwrk[id]++;
                  jd= id;
              }


               if( nd > 1 )
              {
                  if( md == 1 )
                 {
                     jd= kd;
                 } 
                  else
                 {
                     jd= ncpu;
                 }
              }

               if( prt[i] == -1 )
              {
                  prt[i]= jd;
              }
               else
              {
                  if( jd != prt[i] )
                 {
                     if( prt[i] == icpu )
                    {
                        prt[i]= jd;
                    }
                     else
                    {
                        if( jd != icpu )
                       {
                           prt[i]= ncpu;
                       }
                    }
                 }
              }
           }
        }
     }
      delete[] iwrk; iwrk=NULL;
  }

   void cPdata::rdloop( cPdata *obj, Int m, cAu3xView<Int>& ir )
  {
      Int        i,j,k;
      Int        id,jd,kd;
      Int        nd,md;

      bool       flag;

      Int       *iwrk=NULL;
      Int        icpu;

      Int        ival=1;

      icpu= par->getrank();

      if( master ){ ival=-1; };
      iwrk= new Int[ncpu+1];
      for( i=0;i<gn;i++ )
     {
         flag= ( mrkr[i] > 0 );
         for( j=0;j<m;j++ )
        {
            k=ir(j,i);   
//          flag= flag || ( obj->mrkw[k] > 0 );
            flag= ( mrkr[i] > 0 ) || ( mrkw[i] > 0 );
        }
         if( flag )
        {
            if( mrkw[i] == 0 )
           {
               mrkw[i]= ival;
           }
            for( j=0;j<m;j++ )
           {
               k= ir(j,i);
               if( obj->mrkr[k] == 0 )
              {
                  obj->mrkr[k]= -1;
              }
           }
// resolve data dependency
            nd=0;
            md=0;
            setv( 0,ncpu+1, (Int)0, iwrk );
            if( obj->master )
           {
               for( j=0;j<m;j++ )
              {
                  k= ir(j,i);
                  id=obj->prt[k];
                  if( iwrk[id] == 0 )
                 {
                     nd++;
                     if( id != icpu )
                    {
                        md++;
                        kd=id;
                    }
 
                 }
                  iwrk[id]++;
                  jd= id;
              }


               if( nd > 1 )
              {
                  if( md == 1 )
                 {
                     jd= kd;
                 } 
                  else
                 {
                     jd= ncpu;
                 }
              }

               if( prt[i] == -1 )
              {
                  prt[i]= jd;
              }
               else
              {
                  if( jd != prt[i] )
                 {
                     if( prt[i] == icpu )
                    {
                        prt[i]= jd;
                    }
                     else
                    {
                        if( jd != icpu )
                       {
                           prt[i]= ncpu;
                       }
                    }
                 }
              }
           }
        }
     }
      delete[] iwrk; iwrk=NULL;
  }
