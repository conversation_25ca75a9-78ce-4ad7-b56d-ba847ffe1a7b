   using namespace std;

#  include <pdata/proto.h>
#  include <assert.h>

   void cPdata::partition( )
  {

      Int i,j,ic;


// count marked entities in each partition
      setv( (Int)0,ncpu+1,(Int)0,il );
      for( i=0;i<gn;i++ )
     {
         if( mrkr[i] != 0 )
        {
            ic= prt[i];
            if( ic > -1 )
           {
               il[ic]++;
           }
            else
           {
               cout << "shite happens at entry "<<i<<" of pdata "<<this<<"\n";
               assert(false);
           }
        }
     }
   

      accml( ncpu+1,il );

      n= il[ncpu];
      ig= new Int[n];

      shftl( ncpu+1,il );

// build global to local table

      for( i=0;i<gn;i++ )
     {
         if( mrkr[i] != 0 )
        {
            ic= prt[i];
            if( ic > -1 )
           {
               j= il[ic];
               ig[j]= i;
   
               il[ic]++;
           }
        }
     }
  }


   void cPdata::makelocal( Int nr, Int *gir[], Int **sir, cPdata *mstr )
  {
      Int id,jr,j,i;
      Int ist,ien;
      Int nm;
      Int im;
      Int *iwrk;

      Int **ir;
      ir= new Int*[nr];

      create( nr, sir );
      subv( nr, n, *sir,ir );

      nm= mstr->gn;
      iwrk= new Int[nm];
      setv( (Int)0,nm,(Int)-1,iwrk );

      ien= 0;
      for( id=0;id<ncpu+1;id++ )
     {
         ist=ien;
         ien= mstr->il[id];
         for( j=ist;j<ien;j++ )
        {
            im= mstr->ig[j];
            iwrk[im]= j;
        }
     }

      ien= 0;
      for( id=0;id<ncpu+1;id++ )
     {
         ist= ien;
         ien= il[id];
         for( jr=0;jr<nr;jr++ )
        {
            for( j=ist;j<ien;j++ )
           {
               i= ig[j];
               im= gir[jr][i];
               if( iwrk[im] != -1 )
              {
                  ir[jr][j]= iwrk[im];
              }
               else
              {
                  cout << "local entry for this dataset "<<j<<"\n";
                  cout << "global entry for this dataset "<<i<<"\n";
                  cout << "global entry for master dataset "<<im<<"\n";
                  cout << "spotted incompatible partition\n";
                  assert(false);
              }
           }
        }
     }

      delete[] iwrk;
      delete[] ir;
  }

   void cPdata::makelocal( Int nr, cAu3xView<Int>& gir, Int **sir, cPdata *mstr )
  {
      Int id,jr,j,i;
      Int ist,ien;
      Int nm;
      Int im;
      Int *iwrk;

      //Int **ir;
      //ir= new Int*[nr];
      cAu3xView<Int> ir;
      //ir= new Int*[nr];

      create( nr, sir );
      ir.subv( nr, n, *sir );

      nm= mstr->gn;
      iwrk= new Int[nm];
      setv( (Int)0,nm,(Int)-1,iwrk );

      ien= 0;
      for( id=0;id<ncpu+1;id++ )
     {
         ist=ien;
         ien= mstr->il[id];
         for( j=ist;j<ien;j++ )
        {
            im= mstr->ig[j];
            iwrk[im]= j;
        }
     }

      ien= 0;
      for( id=0;id<ncpu+1;id++ )
     {
         ist= ien;
         ien= il[id];
         for( jr=0;jr<nr;jr++ )
        {
            for( j=ist;j<ien;j++ )
           {
               i= ig[j];
               im= gir(jr,i);
               if( iwrk[im] != -1 )
              {
                  ir(jr,j)= iwrk[im];
              }
               else
              {
                  cout << "local entry for this dataset "<<j<<"\n";
                  cout << "global entry for this dataset "<<i<<"\n";
                  cout << "global entry for master dataset "<<im<<"\n";
                  cout << "spotted incompatible partition\n";
                  assert(false);
              }
           }
        }
     }

      delete[] iwrk;
//      delete[] ir;
  }

   Int cPdata::localindex( Int i )
  {
     Int ncpu, ist, ien, id, j;

     ncpu= par->getncpu();
     ien= 0;
     for( id=0;id<ncpu+1;id++ )
    {
       ist= ien;
       ien= il[id];
       //cout << "cpu " << id << ": " << ist << " " << ien << " ============== \n";
       for( j=ist;j<ien;j++ )
      {
         if(ig[j] == i)
        {
           //cout << "get here " << j << "\n";
           return j;
        }
      }
    }
     return -1;
  }

   void cPdata::partition_mat( )
  {
      Int ist, ien;
      Int i,id,j,k,ic, icpu;

//new global ordering for assembling jacobian matrix
//so global numbering is continous through each partition

      id = par->getrank();

//mapping of these two global numbering
      ig_mat= new Int[n];
      ig_2_ig_mat= new Int[gn];
      for(icpu=0; icpu<ncpu; icpu++)
     {
         for( i=0;i<gn;i++ )
        {
            ic= prt[i];
            if( ic > -1 && ic==icpu)
           {
               ig_2_ig_mat[i] = gn_mat;
               gn_mat++;
           }
            else if(ic<0)
           {
               cout << "partiion to a negative rank?\n";
               exit(0);
           }
        }
     }
      assert(gn_mat==gn);

//mark non-halo elements

      for(i=0; i<ncpu+1; i++)
     {
         range(i, &ist, &ien);
         for(j=ist; j<ien; j++)
        {
            k = ig[j];
            ig_mat[j] = ig_2_ig_mat[k];
        }
     }

      for(i=0; i<ncpu+1; i++)
     {
         range(i, &ist, &ien);
         for(j=ist; j<ien; j++)
        {
            k = ig[j];
//            cout << "old numbering " << ig[j] << " ---> new numbering " <<  ig_mat[j] << "\n";
        }
     }
  }
