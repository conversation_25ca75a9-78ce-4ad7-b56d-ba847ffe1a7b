#   include <iostream>
#   include <string>

using namespace std;

#   include <plugin.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Jul 22 15:23:17 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         debug mode

   cPlugin::cPlugin( )
  { sub= NULL;
    lib_handle= NULL; }

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Jul 22 15:23:17 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         debug mode

   cPlugin::cPlugin( string ipath, string iname ) 
  {

      char* error;
      path= ipath;
      name= iname;
 
// open plugins library
      lib_handle= dlopen( path.c_str(), RTLD_LAZY );
      if( !lib_handle )
      {
         cout << dlerror() <<"\n" ;
         exit(1);
      }

// choose one
       sub= dlsym( lib_handle, name.c_str() );
       if( ( error=dlerror() ) != NULL )
      {
          cout << error << "\n" ;
          exit(1);
      }

  }

   cPlugin::cPlugin( string ipath, string iname, Int Argc, string *Argv ) 
  {

      char* error;
      path= ipath;
      name= iname;

      Int i;
      argc=Argc;
      for( i=0;i<argc;i++ ) 
     {
         argv[i]= Argv[i]; 
     }
 
// open plugins library
      lib_handle= dlopen( &(path[0]), RTLD_LAZY );
      if( !lib_handle )
      {
         cout << dlerror() <<"\n" ;
         exit(1);
      }

// choose one
       sub= dlsym( lib_handle, &(name[0]) );
       if( ( error=dlerror() ) != NULL )
      {
          cout << error << "\n" ;
          exit(1);
      }

  }

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Tue Jul 22 15:23:17 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         debug mode

   cPlugin::~cPlugin( )
  {
      unload();
/*    cout << "going out of scope\n";
      dlclose(lib_handle);*/
  }

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Tue Jul 22 15:23:17 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         debug mode

   void cPlugin::unload( )
  {
      if( lib_handle ){ dlclose(lib_handle); }
  }
