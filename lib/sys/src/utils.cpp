using namespace std;

#  include <utils/proto.h>


/*3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
         1         2         3         4         5         6         7         8         9         0         1         2

   <PERSON>          <PERSON> <<EMAIL>>
   Created         Thu Jul 15 13:27:09 BST 2010
   Changes History -
   Next Change(s)  -
 */

   string str( int i )
  {
      string t;
      stringstream s;
      s.clear();
      s << i; 
      s >> t;
      return t;
  }

   string str( void *i )
  {
      string t;
      stringstream s;
      s.clear();
      s << i;
      s >> t;
      return t;
  }

   int str2int( string t )
  {
      int i;
      stringstream s;
      s.clear();
      s << t;
      s >> i;
      return i;
  }

   Int trimlen( char *buf )
  {
      Int len=0;
      while( buf[len] != 0 && buf[len] != 10 ){ len++; }; 
      while( --len, buf[len] == ' ' || buf[len] == 0 ){}
      return ++len; 
  }

   string trim( char *buf )
  {
      return string( buf,trimlen(buf) );
  }

// generate cumulative counts

   void accml( Int n, Int *ia )
  {
      Int i;
      for( i=1;i<n;i++ )
     {
         ia[i]+= ia[i-1];
     }
  }

   bool sep( char c, string synt )
  {
      bool val= false;
      Int  n;
      n= synt.length();
      val= ( inlst( c,n,(char*)(synt.c_str()) ) != -1 ) || c == ' '; 
      return val;
  }

   void parse( string data, Int *n, string **var, string syn )
  {
      Int m,l,n1,n2;
      string w;
      if( *var )
     {
         cout << "quasi-sigsegv: cannot parse onto existing string **\n";
         exit(1);
     }
      else
     {
        *n=0;
         l= data.length();
         n1=0;
         n2=0;
         do
        {
            m= n2;
            while( sep( data.at(m),syn ) )
           {
               m++;
               if( m == (l) )break;
           }
            n1=m;
            while( !(sep( data.at(m),syn) ) )
           {
               m++;
               if( m == (l) ){ break;}
           }
            n2=m;
            if( n2 > n1 )
           {
               w= string(data,n1,n2-n1);
               append( n,var,1,&w );
           }

        }while( n2 < l-1 );
     }
  }

   void stophere()
  {
      bool val=true;
      while( val )
     {

     }
  }


   void milliseconds( double m )
  {
      long int    i,n;
      double dt;
 
/*    if( m <= 0 )
     {
         n=1000000000;
         t0= MPI_Wtime();
         for( i=0;i<n;i++ ){}; 
         t1= MPI_Wtime();
         cout << 1000*(t1-t0)/(double)n<<"\n";
     }
      else*/
     /* Real msec=1.e-5;
     {
         dt=m/msec;
         n= 1+(long int)dt;
     }
      for( i=0;i<n;i++ ){};*/
  }

   bool isnan( Real var )
  {
      bool val;
      val= (!(var>=0)) && (!(var<=0));
      return val;
  }

   void concatp( Int n, Int *ilst[], Int *iprm, Int *m, Int *lprm )
  {
      Int  i,j;
      Int  i0;

     (*m)= 0;
      if( n > 0 )
     {
         for( i=0;i<n-1;i++ )
        {
            for( j=i+1;j<n;j++ )
           {
               if( ilst[0][iprm[j]] == ilst[1][iprm[i]] )
              {
                  if( j != i+1 )
                 {
                     swap( iprm+j,iprm+(i+1) );
                 }
                  break;
              }
           }
        }
         i0= ilst[1][iprm[0]];
         for( i=1;i<n;i++ )
        {
            lprm[(*m)]= i;
            if( ilst[0][iprm[i]] != i0 ){ (*m)++; }
            i0= ilst[1][iprm[i]];
        }
         lprm[(*m)]= i;
       (*m)++;

     }
  }

   Int reducible( Int n, Int *ilst[], Int *iprm, Int m, Int *lprm )
  {
      Int i0,i1;
      Int i,j;
      Int val;

      val= -1;
      for( i=m-1;i>=1;i-- )
     {
         for( j=0;j<i;j++ )
        {
            if( j != i )
           {
               i0= 0; if( j > 0 ){ i0= lprm[j-1]; };
               i1= lprm[i]-1;

               if( ilst[0][iprm[i0]] == ilst[1][iprm[i1]] )
              {
                  val= i;
                  break;
              } 
           }
        }
         if( val != -1 ){ break; };
     }

      return val;
  }

   void concatg( Int n, Int *ilst[], Int *iprm, Int *m, Int *lprm )
  {
      Int  i,j;
    (*m)= 0;
      if( n > 0 )
     {
         identv( n,iprm );
         do
        {
            concatp( n,ilst,iprm, m,lprm );
            j= reducible( n,ilst,iprm, *m,lprm );
            if( j != -1 )
           {
               assert( j > 0 );
               assert( j < n );
               i= lprm[j-1];
               swap( iprm+i,iprm+0 );
           }
        }while( j != -1 );
     }
  }


   void match( Int n, Int *ilst0[], Int *ilst1[], Int *iprm )
  {
      Int i,j;
      Int i0,i1;
      Int j0,j1;

      identv( n,iprm );
      for( i=0;i<n;i++ )
     {
         i0=ilst0[0][i]; 
         i1=ilst0[1][i]; 
         for( j=i;j<n;j++ )
        {
            j0=ilst1[1][j]; 
            j1=ilst1[0][j]; 
            if( i0 == j0 && i1 == j1 )
           {
               if( i != j )
              {
                  swap( iprm+i,iprm+j );
              }
               break;
           }
        }
     }
  } 

   string installpath()
  {
      string val;
      char *buf;
      buf= getenv( "AU3XPATH" );
      assert( buf );
      val= string(buf);
      return val;
  }

   Real lower_bound_by_mag(Real var, Real var_bound)
  {
      if(var>0.0 && fabs(var)<var_bound)
     {
         if(var>0.0)      var = var_bound;
         else if(var<0.0) var =-var_bound;
     }
      return var;
  }
