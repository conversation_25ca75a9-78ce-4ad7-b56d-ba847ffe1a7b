   using namespace std;

#  include <iostream>
#  include <fstream>

#  include <paral.h>

   cParal::cParal()
  {

      pthread_mutex_init( &mutx,NULL );

      itagb=0;
      ncpu=0;

      icpu= NULL;
      acpu= NULL;
      icore=NULL;
      inode=NULL;

      nque=0;
      ique=NULL;

      nmsg=0;
      msgd=NULL;
      msgv=NULL;
      msgw=NULL;

      lock(); 
      MPI_Comm_rank(  MPI_COMM_WORLD, &rank );
      MPI_Comm_size(  MPI_COMM_WORLD, &size );
      unlock(); 
 
      irnk= rank;
      resident= false;

  }

   cParal::~cParal()
  {
      delete[]  icpu;  icpu=NULL;
      delete[]  acpu;  acpu=NULL;
      delete[] icore; icore=NULL;
      delete[] inode; inode=NULL;
//    delete[]  cpul;  cpul=NULL;
      delete[]  ique;  ique=NULL;

      irnk= -1;
      resident= false;

      nque=0;
      ncpu=0;

      if( nmsg != 0 )
     {
         cout << "should not be destroying parallel object while messages are still in transit\n";
         exit(11);
     }

      pthread_mutex_destroy( &mutx );

  }

   void cParal::identify()
  {
      MPI_Group         world,local;
      Int               id;
      int              *ranks;

      resident= false;
      for( id=0;id<ncpu;id++ )
     {
         if( irnk == acpu[id] )
        {
            resident=true;
            impersonate(id);
            break;
        }
     }

      ranks= new int[ncpu+1];
      for( id=0;id<ncpu;id++ )
     {
         ranks[id]= acpu[id];
     }

      lock();
      MPI_Comm_group( MPI_COMM_WORLD,&world );
      MPI_Group_incl( world, ncpu,ranks, &local );
      MPI_Comm_create( MPI_COMM_WORLD, local, &pcom );
      MPI_Group_free( &world );
      MPI_Group_free( &local );
      unlock();

      if( resident )    
     {
       
         lock();
         MPI_Comm_rank(  pcom, &lrnk );
         MPI_Comm_size(  pcom, &lsize );
         unlock();
         id=0;

         delete[] ique;
         ique=new Int[ncpu+1];
         identv( ncpu+1,ique );
         nque=0;

     }
      else
     {
         lrnk=-1;
         lsize=-1;
     }
      delete[] ranks;
  }

   bool cParal::queue()
  {
      Int im,id,jd;
      bool que;
      bool val;

      val= false;
      for( jd=nque;jd<ncpu+1;jd++ )
     {
         id=ique[jd];
         que=false;
         for( im=0;im<nmsg;im++ )
        {
            que= que || msgw[im][id];
        }
         if( !que )
        {
            swap( ique+jd,ique+nque );
            nque++;
            val= true;
            break;
        }
     }
      return val;
  }

   bool cParal::transit()
  {
      cPdata *var;
      Int     id,iv,im;
      bool    val=true;
      bool    bsnd,brcv;

      if( nque == ncpu+1 )
     {
         val= false;
         nque=0;
         identv( ncpu+1,ique );
         
         for( im=0;im<nmsg;im++ )
        {
            delete[] msgw[im];
            msgw[im]= NULL;
        }
         delete[] msgd; msgd= NULL;
         delete[] msgv; msgv= NULL;
         delete[] msgw; msgw= NULL;
         nmsg= 0;
     }
      else
     {
         val= true;
         while( !queue() )
        {
            for( im=0;im<nmsg;im++ )
           {
               var= msgd[im];
               iv=  msgv[im];
               for( id=0;id<ncpu+1;id++ )
              {
                  bsnd= var->stransit( id,iv );
                  brcv= var->rtransit( id,iv );// brcv= false;
                  msgw[im][id]= ( bsnd || brcv );
              }
           }
            milliseconds(IDLET);
        }
     }
      return val;
  }

   Int cParal::avail()
  {
      Int val;
      val= ique[nque-1];
      return val;
  }

   void cParal::newmsg( cPdata *var, Int i )
  {
      bool *val=NULL;
      Int tmp;
      tmp= nmsg;
      append( &nmsg,&msgd,(Int)1, &var ); nmsg=tmp;
      append( &nmsg,&msgv,(Int)1,   &i ); nmsg=tmp;
      append( &nmsg,&msgw,(Int)1, &val ); 

      msgw[nmsg-1]= new bool[ncpu+1];
      setv( 0,ncpu+1,true,msgw[nmsg-1] );
      msgw[nmsg-1][irnk]=false;

//    cout << "paral logs message "<<nmsg-1<<" from pdata "<<var<<" variable "<<i<<" ("<<msgd[nmsg-1]<<" "<<msgv[nmsg-1]<<")\n";

  }

   void cParal::newtag( Int *val )
  {
     *val= itagb++;
  }

   void cParal::gsum( Int n, Real *r )
  {
      Int i;
      Real *dum;
      dum= new Real[n];
      setv( 0,n, ZERO, dum );
      lock();
      MPI_Allreduce( r,dum, n,MPI_Real, MPI_SUM, pcom );
      unlock();
      for( i=0;i<n;i++ )
     {
         r[i]= dum[i];
     }
      delete[] dum;
  }

   void cParal::gmax( Int n, Real *r )
  {
      Int i;
      Real *dum;
      dum= new Real[n];
      setv( 0,n, -big, dum );
      lock();
      MPI_Allreduce( r,dum, n,MPI_Real, MPI_MAX, pcom );
      unlock();
      for( i=0;i<n;i++ )
     {
         r[i]= dum[i];
     }
      delete[] dum;
  }

   void cParal::gmin( Int n, Real *r )
  {
      Int i;
      Real *dum;
      dum= new Real[n];
      setv( 0,n, big, dum );
      lock();
      MPI_Allreduce( r,dum, n,MPI_Real, MPI_MIN, pcom );
      unlock();
      for( i=0;i<n;i++ )
     {
         r[i]= dum[i];
     }
      delete[] dum;
  }

   void cParal::gsumg( Int n, Real *r )
  {
      Int i;
      Real *dum;
      dum= new Real[n];
      setv( 0,n, ZERO, dum );
      lock();
      MPI_Allreduce( r,dum, n,MPI_Real, MPI_SUM, MPI_COMM_WORLD );
      unlock();
      for( i=0;i<n;i++ )
     {
         r[i]= dum[i];
     }
      delete[] dum;
  }

   void cParal::gmaxg( Int n, Real *r )
  {
      Int i;
      Real *dum;
      dum= new Real[n];
      setv( 0,n, -big, dum );
      lock();
      MPI_Allreduce( r,dum, n,MPI_Real, MPI_MAX, MPI_COMM_WORLD );
      unlock();
      for( i=0;i<n;i++ )
     {
         r[i]= dum[i];
     }
      delete[] dum;
  }

   void cParal::gming( Int n, Real *r )
  {
      Int i;
      Real *dum;
      dum= new Real[n];
      setv( 0,n, big, dum );
      lock();
      MPI_Allreduce( r,dum, n,MPI_Real, MPI_MIN, MPI_COMM_WORLD );
      unlock();
      for( i=0;i<n;i++ )
     {
         r[i]= dum[i];
     }
      delete[] dum;
  }

   void cParal::gsumg( Int n, Int *r )
  {
      Int i;
      Int *dum;
      dum= new Int[n];
      setv( 0,n, (Int)0, dum );
      lock();
      MPI_Allreduce( r,dum, n,MPI_Int, MPI_SUM, MPI_COMM_WORLD );
      unlock();
      for( i=0;i<n;i++ )
     {
         r[i]= dum[i];
     }
      delete[] dum;
  }

   void cParal::gmaxg( Int n, Int *r )
  {
      Int i;
      Int *dum;
      dum= new Int[n];
      setv( 0,n, (Int)-big, dum );
      lock();
      MPI_Allreduce( r,dum, n,MPI_Int, MPI_MAX, MPI_COMM_WORLD );
      unlock();
      for( i=0;i<n;i++ )
     {
         r[i]= dum[i];
     }
      delete[] dum;
  }

   void cParal::gming( Int n, Int *r )
  {
      Int i;
      Int *dum;
      dum= new Int[n];
      setv( 0,n, (Int)big, dum );
      lock();
      MPI_Allreduce( r,dum, n,MPI_Int, MPI_MIN, MPI_COMM_WORLD );
      unlock();
      for( i=0;i<n;i++ )
     {
         r[i]= dum[i];
     }
      delete[] dum;
  }

   void cParal::lock()
  {
      pthread_mutex_lock(&mutx);
  }

   void cParal::unlock()
  {
      pthread_mutex_unlock(&mutx);
  }

   void cParal::gopen( ofstream *var, string fnme )
  {
      if( getrank() == 0 )
     {
         var->open( fnme.c_str() );
     }
      else
     {
         var->open( "/dev/null" );
     }
  }

   void cParal::gclose( ofstream *var )
  {
      var->close();
  }
