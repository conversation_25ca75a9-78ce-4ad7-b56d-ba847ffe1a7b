include ../../Makefile.in

CSRC=   audit.cpp \
	audited.cpp \
	cprec.cpp \
	env.cpp \
	pdata/debug.cpp \
	pdata/loops.cpp \
	pdata/mesg.cpp \
	pdata/part.cpp \
	pdata/pdata.cpp \
	pdata/pickle.cpp \
	paral.cpp \
	plugin.cpp \
	sig.cpp \
	tag.cpp \
	thread.cpp \
	tuple.cpp \
	utils.cpp \
	tab.cpp \
	txtfle.cpp \
	event.cpp \
	mem.cpp \
	random.cpp

#FSRC=   fprec.F
FSRC= 

COBJ= $(CSRC:.cpp=.o)
FOBJ= $(FSRC:.F=.o) 
OBJS= $(COBJ) $(FOBJ)

BINS= ../libsys.so

$(BINS): $(OBJS)
	$(PCCMP) -shared $(OBJS) -o $@


.cpp.o:
	$(PCCMP) $(COPT) $(SYSI) -o $@ -c $<

.F.o:
	$(FCMP) $(FOPT) $(SYSI) -o $@ -c $<

clean:
	rm -f $(OBJS) $(BINS)
