//   bool stransit( Int id, Int *sdata )
  {
      Int ia;
      MPI_Status status;
      int flag;

      ia= inlst( (void*)sdata,na,ar );

      if( master )
     {

         if( id < 0 || id > ncpu )
        {
            cout << "this cpu is not in this device\n";
            exit(1);
        }

         if( swait[ia][id] )
        {  
            MPI_Test( &(sreq[ia][id]),&flag,&status );
            if( flag )
           {
               MPI_Wait( &(sreq[ia][id]),&status );
               swait[ia][id]= false;
           }
        }
     }
      else
     {
         cout << "you cannot invoke transit with a non-master\n"; 
     }
      return swait[ia][id];
  }
