using namespace std;

# include         <iostream>
# include         <string>

# include         <cprec.h>

/*3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
         1         2         3         4         5         6         7         8         9         0         1         2

   <PERSON>          <PERSON> <<EMAIL>>
   Created         Mon May 12 11:55:54 BST 2008
   Changes History -
   Next Change(s)  ( work in progress )
 */

   void cprecenq( )
  {
      Int          I;
      int          i;
      Real         R;
      void        *p;
      cout << " C/C++ int   size "<<sizeof(i)<<"\n";
      cout << " C/C++ Int  size "<<sizeof(I)<<"\n";
      cout << " C/C++ Real size "<<sizeof(R)<<"\n";
      cout << " C/C++ void* size "<<sizeof(p)<<"\n";
  }
