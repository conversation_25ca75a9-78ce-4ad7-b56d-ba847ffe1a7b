   using namespace std;

#  include <audit.h>


/*3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
         1         2         3         4         5         6         7         8         9         0         1         2

   <PERSON>          <PERSON> <<EMAIL>>
   Created         Wed Jul 14 18:12:55 BST 2010
   Changes History -
   Next Change(s)  ( work in progress )
 */

   cAudit::cAudit()
  {

      uid= rand();

      usr=getenv( "USER" );
      tme=  time(  NULL  );
      hst=getenv(  host.c_str()  );
      nxt=         NULL;
      note= "";

  }

   cAudit::cAudit( string data )
  {

      uid= rand();

      usr=getenv( "USER" );
      tme=  time(  NULL  );
      hst=getenv(  host.c_str()  );
      nxt=         NULL;

      note= data;
  }

   cAudit::~cAudit()
  {
      delete nxt; nxt= NULL;
  }

   void cAudit::pickle( size_t *len, pickle_t *buf )
  {
      bool val=(nxt);
      cUid::pickle( len,buf );
      pckle( len, usr,  buf );
      pckle( len, tme,  buf );
      pckle( len, hst,  buf );
      pckle( len, note, buf );
      pckle( len, val,  buf );
      if( val )
     {
         nxt->pickle( len,buf );
     }
      
  }

   void cAudit::unpickle( size_t *len, pickle_t buf )
  {
      bool val;
      cUid::unpickle( len,buf );
      unpckle( len, &usr,  buf ); 
      unpckle( len, &tme,  buf );
      unpckle( len, &hst,  buf );
      unpckle( len, &note, buf );
      unpckle( len, &val,  buf );
      if( val )
     {
         if( nxt )
        {
            cout << "fatal: trying to overwrite audit trace\n";
            exit(1);
        }
         else
        {
            nxt= new cAudit();
            nxt->unpickle( len,buf );
        }
     }
  }

   void cAudit::check( string tab )
  {
      cout << tab << "+ "<< note << " on "<< trim(asctime(gmtime(&tme)))<<" by "<<usr<<"@"<<hst<<" ";
      cUid::check( tab );
      cout << "\n";
      if( nxt )
     {
         nxt->check( tab );
     }
  }

   void cAudit::touch( string data )
  {
      cAudit *tmp;
      tmp=this; 
      while( tmp->nxt )
     {
         tmp= tmp->nxt;
     }
      tmp->nxt= new cAudit( data );
  }

   audit_t cAudit::compare( cAudit *data )
  {
      audit_t val=audit_invalid;
      if( data )
     { 
         if( same( data ) )
        {
            if( nxt )
           {
               val= nxt->compare( data->nxt );
           }
            else
           {
               if( data->nxt )
              {
                  val= audit_earlier;
              }
               else
              {
                  val= audit_match;
              }
           }
        } 
         else
        {
            val= audit_mismatch;
        }
     }
      else
     {
         val= audit_later; 
     }
      return val;
  }
   
   bool cAudit::same( cAudit *data )
  {
      bool val= false;
      if( data )
     {
         if( uid == data->uid && 
             tme == data->tme && 
             usr == data->usr && 
             hst == data->hst &&
             note== data->note )
        {
            val= true;
        }
     }
      return val;
  }

   void cAudit::copy( cAudit *data )
  {
      uid=  data->uid; 
      tme=  data->tme; 
      usr=  data->usr; 
      hst=  data->hst; 
      note= data->note; 
      if( data->nxt )
     {
         if( !nxt )
        {
            nxt= new cAudit();
        }
         nxt->copy( data->nxt );
     }
  }
