   using namespace std;

#  include <tuple.h>

   cTuple::cTuple()
  {
      n=0;
      m=0;
      val=NULL;
      key=NULL;
  }

   cTuple::~cTuple()
  {
      delete[] val; val=NULL;
      delete[] key; key=NULL;
      n=0;
      m=0;
  }

   void cTuple::add( string str )
  {
      Int i;
      Int l;
      bool flag=false;
      for( i=0;i<n;i++ ) 
     {
         if( str == key[i] ){ flag=true; break; };
     }
      if( !flag )
     {
         if( n == m )
        {
            l= m; realloc( &l,TDLEN,&key );  
            l= m; realloc( &l,TDLEN,&val );  
            m= l;
        }   
         key[n]= str; 
         n++;
     }
  }

   string cTuple::value( string str )
  {
      Int i;
      bool flag=false;
      string line="";
      for( i=0;i<n;i++ ) 
     {
         if( str == key[i] ){ flag=true; break; };
     }
      if( flag )
     {
         line= val[i];
     }
      return line;
  }

   void cTuple::value( string str, string line )
  {
      Int i;
      bool flag=false;
      for( i=0;i<n;i++ ) 
     {
         if( str == key[i] ){ flag=true; break; };
     }
      if( flag )
     {
         val[i]= line;
     }
  }

   void cTuple::update( cTuple *data )
  {
       
  }

   void cTuple::check( string tab )
  {
      Int i;
      cTag::check( tab );
      cAudited::check( tab );
      for( i=0;i<n;i++ )
     {
         cout << key[i]<<" "<<val[i]<<"\n";
     }
  }

   void cTuple::pickle( size_t *len, pickle_t *buf )
  {
      Int i;
      Int l;
      cTag::pickle( len,buf );
      cAudited::pickle( len,buf );
//    cPickle::pckle( len,n,buf );
/*    for( i=0;i<n;i++ ) 
     {
         pckle( len,key[i],buf );
         pckle( len,val[i],buf );
     }*/
  }

   void cTuple::unpickle( size_t *len, pickle_t buf )
  {
      cTag::unpickle( len,buf );
      cAudited::unpickle( len,buf );

/*    unpckle( len, &n, buf );
      delete[] key; key= new string[n];
      delete[] val; val= new string[n];
      m= n;
       
      for( i=0;i<n;i++ ) 
     {
         unpckle( len,key+i,buf );
         unpckle( len,val+i,buf );
     }*/
  }

   void cTuple::write( ofstream *fle )
  {
     *fle<<sid<<"\n"; 
     *fle<<tag<<"\n"; 
     *fle<<n; 
      for( int i=0;i<n;i++ )
     {
        *fle<<key[i]<<"\n";
        *fle<<val[i]<<"\n";
     }
  }
   void cTuple::read( ifstream *fle )
  {
     *fle>>sid;
     *fle>>tag;
     *fle>>n; 
      delete[] key; key= new string[n];
      delete[] val; val= new string[n];
      m= n;
      for( int i=0;i<n;i++ )
     {
        *fle>>key[i];
        *fle>>val[i];
     }
  }

   Int which( int n, cTuple *data, cUid *var, string tag )
  {
      Int i;
      Int val;
      string tg;
      ident_t uid;
      for( i=0;i<n;i++ )
     {
         uid= data[i].getsid();
         tg= data[i].gettag();
         if(   uid == var->getuid() && tag == tg )
        {
            val= i;
        } 
     }
      return val;
  }
