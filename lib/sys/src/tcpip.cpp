using namespace std;

#  include <iostream>

#  include <tcpip.h>


/*3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
         1         2         3         4         5         6         7         8         9         0         1         2

   <PERSON>          <PERSON> <<EMAIL>>
   Created         Thu Jul 15 13:26:22 BST 2010
   Changes History -
   Next Change(s)  -
 */

   void tcpip_stop( sigdata_t data, cSig *var ){ ((cTcpip*)var)->rstop(); };

   cTcpip::cTcpip()
  {
      cip=          "";
      sip=     DIPADDR;
      cid=           0;
      sid=           0;
      port=      DPORT;
      run=       false;
      len=           0;
      err=           0;
      buf=        NULL;
      sig=          -1;
      lev=          -1;
  }

   void cTcpip::open( string s, port_t p )
  {
      port=          p;
      sip=           s; 
  }

   cTcpip::~cTcpip()
  {
      cip=          "";
      sip=          "";
      cid=           0;
      sid=           0;
      port=         -1;
      run=       false;
      len=           0;
      err=           0;
      clear();
      sig=          -1;
      lev=          -1;
  }

   void cTcpip::pickle( size_t *len, pickle_t *buf )
  {
      pckle( len,          cip,   buf );
      pckle( len,          sip,   buf );
      pckle( len,          cid,   buf );
      pckle( len,          sid,   buf );
      pckle( len,         port,   buf );
      pckle( len,          run,   buf );
      pckle( len,     (int)err,   buf );
  }

   void cTcpip::unpickle( size_t *len, pickle_t buf )
  {
      unpckle( len,        &cip, buf );
      unpckle( len,        &sip, buf );
      unpckle( len,        &cid, buf );
      unpckle( len,        &sid, buf );
      unpckle( len,       &port, buf );
      unpckle( len,        &run, buf );
      unpckle( len,  (int*)&err, buf );
  }

   void cTcpip::check()
  {
      cout << "====================================\n";
      cout << "(tcp/ip   server)client ipaddress   "<<       cip   << "\n";
      cout << "(tcp/ip   server)server ipaddress   "<<       sip   << "\n";
      cout << "(tcp/ip   server)client process id  "<<       cid   << "\n";
      cout << "(tcp/ip   server)server process id  "<<       sid   << "\n";
      cout << "(tcp/ip   server)server port        "<<      port   << "\n";
      cout << "(tcp/ip   server)status             "<<       run   << "\n";
      cout << "(tcp/ip   server)error              "<<       err   << "\n";
      cout << "(tcp/ip   server)buffer address     "<<(void*)buf   << "\n";
      cout << "(tcp/ip   server)buffer size        "<<(void*)len   << "\n";
      cout << "====================================\n";
  }

   void cTcpip::start()
  {
      int                   ier;
      struct sockaddr_in    sdr;

      scom= socket( PF_INET,SOCK_STREAM,IPPROTO_TCP ); 
      if( scom == -1 )
     { 
         cout << "(server): cannot make socket \n"; 
         exit(1); 
     }

// bind to address (INET address family, port <port>, any address );
      port= DPORT-1;
      ier= -1;
      while( ier == -1 )
     {
         memset( &sdr,0,sizeof(struct sockaddr_in) );
         sdr.sin_family=      AF_INET;
         sdr.sin_port=        htons(++port);
         sdr.sin_addr.s_addr= INADDR_ANY;
         ier= bind( scom,(const struct sockaddr *)&sdr, sizeof(struct sockaddr_in) );
     }
      run= true;
      check();

      sigh( sig_tcpip_stop,tcpip_stop );

      ier= listen(scom,100);
      if( ier == -1 ){ cout << "(server): cannot set listen status\n"; close(com); exit(1); }
// accept connections
      while( run )
     {
         com= accept(scom,NULL,NULL );
         intray();
         err=-1;
         handle(sig,(sigdata_t)this);
         outtray();
         hungup();
     }
      close( scom );

  }
   
   void cTcpip::dial()
  {

      int                   ier;
      struct sockaddr_in    sdr;

// create the socket ( INET protocl family, stream socket, TCP/IP protocol );

      com= socket( PF_INET,SOCK_STREAM,IPPROTO_TCP ); 
      if( com == -1 )
     {
         cout << "(client): cannot make socket \n"; 
         exit(1); 
     }

// bind to address INET address family, port <port>, address <ipaddr.c_str()>

      memset( &sdr,0,sizeof(struct sockaddr_in) );
      sdr.sin_family=      AF_INET;
      sdr.sin_port=        htons( port );
      ier= inet_pton( AF_INET, ( sip ).c_str(), &sdr.sin_addr );
      if( ier == -1 ){ cout << "(client): not a valid address family\n";      close(com); exit(1); }
      if( ier ==  0 ){ cout << "(client): not a valid address\n";             close(com); exit(1); }
      if( ier !=  1 ){ cout << "(client): some other error from inet_pton\n"; close(com); exit(1); }

// start connection

      ier= -1;
      while( ier== -1 )
     {
         ier= connect( com, (const struct sockaddr *)&sdr, sizeof( struct sockaddr_in) );
         if( ier == -1 ){ sleep(1); };
     }
  }

   void cTcpip::outtray()
  {
      size_t                                slen;
      pickle_t                              sbuf;

      slen= 0;
      sbuf= NULL;

      pckle( &slen, sig,     &sbuf ); 
      pckle( &slen, err,     &sbuf ); 
      pckle( &slen, len,buf ,&sbuf );

      send( com,(const void*) &slen,  sizeof(slen),0 );  
      send( com,(const void*)  sbuf,         slen, 0 ); 

      delete[] sbuf; sbuf=NULL;
      slen= 0;

  }

   void cTcpip::intray()
  {
      size_t                                rlen,l;
      Int                                   m;
      pickle_t                              rbuf;

      if( buf )
     {
         cout << "(tcp/ip   server)intray error: overwriting buffer"<<(void*)buf<<"\n";
         exit(1);
     }

      rlen= 0;
      rbuf= NULL;

      l=  recv( com,(void*)&rlen, sizeof(rlen),0 ); rbuf= new pickle_v[ rlen ];
      l=  recv( com,(void*) rbuf,        rlen, 0 );

      l=0;
      unpckle( &l,     &sig, rbuf ); 
      unpckle( &l,     &err, rbuf ); 
      unpckle( &l,  &m,&buf, rbuf ); 
      len= m;

      delete[] rbuf; rbuf= NULL;
      rlen=0;
     
  }

   void cTcpip::hungup()
  {
      shutdown( com, SHUT_RDWR );
      close( com );
      clear();
  }

   void cTcpip::clear()
  {
//    free( buf ); buf= NULL; len=0;
      delete[] buf; buf=NULL; len=0;
  }

   void cTcpip::stop()
  {
      sig= sig_tcpip_stop;
      dial();
      outtray();
      clear();
      err= 0;
      intray();
      hungup();
      port=-1;
      sip="";
  }

   void cTcpip::rstop()
  {
      cout << "\n";
      cout << "\n";
      cout << this << " received close signal\n";
      run= false;
  }
