   using namespace std;

#  include <audit.h>

   audit_t cAudited::compare( cAudited *data )
  {
      return audit->compare( data->audit );
  }

   void cAudited::pickle( size_t *len, pickle_t *buf )
  {
      bool var;
//    cUid::pckle( len,uid,buf );
      cUid::pickle( len,buf );
      var= (audit);
      pckle( len,var,buf );
      if( var )
     { 
         audit->pickle(len,buf); 
     };
  }

   void cAudited::unpickle( size_t *len, pickle_t  buf )
  { 
      bool var;
      cUid::unpickle( len,buf );
      unpckle( len,&var,buf );
      if( var )
     { 
         audit= new cAudit(); 
         audit->unpickle(len,buf); 
     }; 
  }

   void cAudited::touch( string data )
  { 
      if( audit )
     { 
         audit->touch( data ); 
     }
      else
     { 
         audit= new cAudit( data ); 
     }
  }
  
   void cAudited::check( string tab )
  {
      cUid::check( tab ); cout << "\n";
      if( audit )
     { 
         audit->check( tab ); 
     }
  }
