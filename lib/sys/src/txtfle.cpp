   using namespace std;

#  include <string>
#  include <fstream>
#  include <iostream>
#  include <sstream>

#  include <txtfle.h>

//3456789 ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Sun May 11 22:37:08 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         check empty or commented lines

   bool isok( string line, string comments, string seps )
  {
      bool retv;
      string blank=" ";
      Int    iblank,icomm;

      retv= false;
      if( !line.empty() )                                          // line not empty
     {
        iblank= line.find_first_not_of( blank );
        icomm=  line.find_first_of( comments );
        if( iblank >= 0 )                                          // line not blank
           if( icomm > iblank || icomm < 0 ){ retv= true; }        // line contains valid text before comments
     }
      return( retv );
  }

//3456789 ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Wed May  7 19:21:53 BST 2008
// Changes History -
// Next Change(s)  -
// Purpose         determine whether a character is present in a syntax string

   bool sep( char c, const string *csyn )
  {
      bool        ret= 0;
      Int         i;
      Int         n=csyn->length();
      for( i=0;i<n;i++ )
         ret= ( ret || ( c == csyn->at(i) ) );
      return(ret);
  }

//3456789 ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Wed May  7 19:21:53 BST 2008
// Changes History -
// Next Change(s)  -
// Purpose         parse strings based on a given syntax

   void parse( string *line, Int *n1, Int *n2, const string *csyn )
  {
      Int n,m;
/*    Int n10,n20;
      n10=*n1;
      n20=*n2;*/
      m= line->size();
      if( *n2 < m-1 )
     {
        n= *n2+1;
        while( sep( line->at(n),csyn ) )
       {
           if( n == (m-1) )break;
           n++;
       }
       *n1=n;
        while( !(sep( line->at(n),csyn) ) )
       {
           if( n == (m-1) ){ n++; break;}
           n++;
       }
       *n2=n-1;
     }
  }

//3456789 ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Sun May 11 22:37:08 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         text file constructor

   cTxtfle::cTxtfle( string name, string comments, string seps )
  {
      read( name,comments,seps );
  }

//3456789 ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Sun May 11 22:37:08 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         text file constructor

   int cTxtfle::fromcmdl( int argc, char **argv )
  {

      nl= argc;
      nw= new Int[nl];
      txt= new string *[nl];
      for( Int il=0;il<nl;il++ )
     {
         nw[il]= 1;
         txt[il]= new string[1];
         txt[il][0]= string( argv[il] );
     }
      return(0);
  }

//3456789 ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Sun May 11 22:37:08 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         text file constructor

   void cTxtfle::read( string name, string comments, string seps )
  {

      Int n1,n2,m;
      Int i,j;

      string line;
      string blank=" ";

      nl= 0;
      nw=NULL;
      txt=NULL;

      ifstream fle;
      fle.open( name.data() );

      if( fle ) 
     {
//    file exists: scan to count valid lines
         getline( fle,line );
         while( fle.good() )
        {
            if( isok( line, comments, seps ) ){ nl++; }
            getline( fle,line );
        }
         fle.close();

         if( nl > 0 )
        {

//       there are valid lines, rewind and read them
            txt= new string*[nl];
            nw= new Int[nl];

            fle.clear();
            fle.open( name.data() );
            getline( fle,line );
            i= 0;
            while( fle.good() )
           {
               if( isok( line, comments, seps ) )
              { 

//             count words in this line
                  n1= -1;
                  n2= -1;
                  m= line.length();
                  j=0;
                  parse( &line, &n1,&n2, &seps );
                  do 
                 {
                     j++;
                     if( n2 == m-1 ){ break; }
                     parse( &line, &n1,&n2, &seps );
                 }while( n2>=n1 );

//             store in a string **
                  nw[i]= j;
                  txt[i]= new string[nw[i]];

                  n1= -1;
                  n2= -1;
                  j=0;
                  parse( &line, &n1,&n2, &seps );
                  do 
                 {
                     txt[i][j]= line.substr(  n1,n2-n1+1 );
                     j++;
                     if( n2 == m-1 ){ break; }
                     parse( &line, &n1,&n2, &seps );
                 }while( n2>=n1 );

                  i++;
              }
               getline( fle,line );
           }
            fle.close();
        }
     }

  }

//3456789 ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Sun May 11 22:37:08 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         text file

   cTxtfle::~cTxtfle( )
  {
      Int i;
      for( i=0;i<nl;i++ )
     {
         delete[] txt[i];
     }
      delete[] txt;
      delete[] nw;
  }

//3456789 ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Sun May 11 22:37:08 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         text file

/* stringstream  *cTxtfle::stream( Int il, Int iw )
  {
      stringstream *tmp;
      tmp= new stringstream( txt[il][iw] );
      return( tmp );
  }*/

/* stringstream  &cTxtfle::stream( Int il, Int iw )
  {
      return &stringstream( txt[il][iw] );
  }*/

//3456789 ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* ********* 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          Feng Wang <<EMAIL>>
// Created         Sun May 11 22:37:08 BST 2008
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         text file

   Int  cTxtfle::whereis( string text, Int ic )
  {
      Int il=-1;
      for( Int jl=0;jl<nl;jl++ )
         if( nw[jl]>= ic )
            if( txt[jl][ic] == text ){ il= jl; break; }
      return(il); 
  }
