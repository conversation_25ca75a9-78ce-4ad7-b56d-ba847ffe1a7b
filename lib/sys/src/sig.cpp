using namespace std;

#  include     <sig.h>
#  include     <iostream>


/*3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
         1         2         3         4         5         6         7         8         9         0         1         2

   <PERSON>          <PERSON> <<EMAIL>>
   Created         Wed Jul 14 18:15:29 BST 2010
   Changes History -
   Next Change(s)  ( work in progress )
 */

   cSig::cSig()
  {
      int i;
      for( i=0;i<SIGS;i++ )
     {
         sigf[i]= NULL;
     }
  }

   cSig::~cSig()
  {
      int i;
      for( i=0;i<SIGS;i++ )
     {
         sigf[i]= NULL;
     }
  }

   void cSig::sigh( signl_t sig, sigf_t *f )
  {
      sigf[sig]= f; 
  }

   void cSig::handle( signl_t sig, sigdata_t data )
  {
      if( sigf[sig] )
     {
        (sigf[sig])(data,this);
     }
      else
     {
         cout << "(signal handler)signal not connected\n";
         exit(1);
     }
  }
