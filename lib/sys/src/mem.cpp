using namespace std;

# include <mem/proto.h>

   cMem::cMem()
  {
      na= 0;
      n1= NULL;
      n2= NULL;
      nb= NULL;
      ar= NULL;
      typ=NULL;
  }

   cMem::~cMem()
  {
/*    if( na != 0 )
     {
         cout << "quasi-sigsegv: cannot destroy non-empty mem\n";
         exit(1);
     }*/
      delete[] n1; n1= NULL;
      delete[] n2; n2= NULL;
      delete[] nb; nb= NULL;
      delete[] ar; ar= NULL;
      delete[]typ;typ= NULL;
  }

