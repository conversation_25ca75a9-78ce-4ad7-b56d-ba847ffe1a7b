   using namespace std;

#  include <iostream>
#  include <thread.h>


//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Thu Jun 17 17:35:02 BST 2010
// Changes History -
// Next Change(s)  ( work in progress )

   void *threadf( void *data )
  {
      cThread  *var=  (cThread*)data;
      if( var )
     {
         var->thread();
         var->isolate();
         delete var;
     }
      return NULL;
  }

   cThread::cThread()
  {
      more=NULL;
      less=NULL;
      tid=    0;
      root= this;
      pthread_mutex_init( &mutx,NULL );
      pthread_attr_init(&attr);
      pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_JOINABLE);
  };

   cThread::~cThread()
  { 
      tid=            0;
      pthread_attr_destroy(&attr);
      pthread_mutex_destroy( &mutx );
  };

   void cThread::join()
  {
      void *stat;
//    pthread_join(tid,&stat );
//    while( more ){};
      pthread_join(tid,&stat );
  }

   void cThread::join( cThread *var )
  {
      void *stat;
      pthread_join(var->tid,&stat );
  }

   void cThread::lock()
  {   
      if( root != this )
     {
         root->lock();
     }
      else
     {
         pthread_mutex_lock( &mutx );
     }
  }

   void cThread:: unlock()
  { 
      if( root != this )
     {
         root->unlock();
     }
      else
     {
         pthread_mutex_unlock( &mutx );
     }
  }

   void cThread::boot( cThread *var )
  {
      lock();
      weave( var );
      var->root= this;
      unlock();
      pthread_create( &(var->tid),&attr,threadf,(void*)var );
      pthread_attr_destroy(&attr);

  }

   void cThread::isolate()
  {
      lock();
      if( less ){ less->more= more; };
      if( more ){ more->less= less; };
      unlock();
      root= NULL;
      less= NULL;
      more= NULL;
  }

   void cThread::weave( cThread *var )
  {
      if( more )
     {
         more->weave( var );
     }
      else
     {
         more= var;
         var->less= this;
     }
  }

   void cThread::boot()
  {

/*    lock();
      weave( var );
      var->root= this;
      unlock();*/
      pthread_create( &tid,&attr,threadf,(void*)this );

  }
