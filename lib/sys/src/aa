Reading specs from /usr/local/bin/../lib/gcc-lib/i686-pc-cygwin/4.0.3//specs
Reading specs from /usr/local/lib/gcc-lib/i686-pc-cygwin/4.0.3/specs
Target: 
Configured with: /src/G95/gcc-4.0.3/configure --enable-languages=c --disable-nls
Thread model: single
gcc version 4.0.3 (g95 0.92!) Dec  7 2008

 /usr/local/bin/../lib/gcc-lib/i686-pc-cygwin/4.0.3//cc1 -quiet -v -iprefix /usr/local/bin/../lib/gcc-lib/i686-pc-cygwin/4.0.3// -D__CYGWIN32__ -D__CYGWIN__ -Dunix -D__unix__ -D__unix -idirafter /usr/lib///../include/w32api -idirafter /usr/lib/gcc///../../include/w32api help-dummy -quiet -dumpbase help-dummy -mtune=pentiumpro -auxbase help-dummy -version --help -o /cygdrive/c/Users/<USER>/AppData/Local/Temp/cchvFWl1.s
Usage: g95 [options] file...
Options:
  -pass-exit-codes         Exit with highest error code from a phase
  --help                   Display this information
  --target-help            Display target specific command line options
  -dumpspecs               Display all of the built in spec strings
  -dumpversion             Display the version of the compiler
  -dumpmachine             Display the compiler's target processor
  -print-search-dirs       Display the directories in the compiler's search path
  -print-libgcc-file-name  Display the name of the compiler's companion library
  -print-file-name=<lib>   Display the full path to library <lib>
  -print-prog-name=<prog>  Display the full path to compiler component <prog>
  -print-multi-directory   Display the root directory for versions of libgcc
  -print-multi-lib         Display the mapping between command line options and
                           multiple library search directories
  -print-multi-os-directory Display the relative path to OS libraries
  -Wa,<options>            Pass comma-separated <options> on to the assembler
  -Wp,<options>            Pass comma-separated <options> on to the preprocessor
  -Wl,<options>            Pass comma-separated <options> on to the linker
  -Xassembler <arg>        Pass <arg> on to the assembler
  -Xpreprocessor <arg>     Pass <arg> on to the preprocessor
  -Xlinker <arg>           Pass <arg> on to the linker
  -combine                 Pass multiple source files to compiler at once
  -save-temps              Do not delete intermediate files
  -pipe                    Use pipes rather than intermediate files
  -time                    Time the execution of each subprocess
  -specs=<file>            Override built-in specs with the contents of <file>
  -std=<standard>          Assume that the input sources are for <standard>
  -B <directory>           Add <directory> to the compiler's search paths
  -b <machine>             Run gcc for target <machine>, if installed
  -V <version>             Run gcc version number <version>, if installed
  -v                       Display the programs invoked by the compiler
  -###                     Like -v but options quoted and commands not executed
  -E                       Preprocess only; do not compile, assemble or link
  -S                       Compile only; do not assemble or link
  -c                       Compile and assemble, but do not link
  -o <file>                Place the output into <file>
  -x <language>            Specify the language of the following input files
                           Permissible languages include: c c++ assembler none
                           'none' means revert to the default behavior of
                           guessing the language based on the file's extension

Options starting with -g, -f, -m, -O, -W, or --param are automatically
 passed on to the various sub-processes invoked by g95.  In order to pass
 other options on to these processes the -W<letter> options must be used.
cc1: error: unrecognized command line option "-v"
cc1: error: unrecognized command line option "-iprefix"
cc1: error: unrecognized command line option "-idirafter"
cc1: error: unrecognized command line option "-idirafter"
The following options are language-independent:

  --help                      Display this information
  --param <param>=<value>     Set parameter <param> to value.  See below for a
                              complete list of parameters
  --target-help               This switch lacks documentation
  --version                   This switch lacks documentation
  -G<number>                  Put global and static data smaller than <number>
                              bytes into a special section (on some targets)
  -O<number>                  Set optimization level to <number>
  -Os                         Optimize for space rather than speed
  -W                          This switch is deprecated; use -Wextra instead
  -Waggregate-return          Warn about returning structures, unions or arrays
  -Wcast-align                Warn about pointer casts which increase alignment
  -Wdeprecated-declarations   Warn about uses of __attribute__((deprecated))
                              declarations
  -Wdisabled-optimization     Warn when an optimization pass is disabled
  -Werror                     Treat warnings as errors
  -Wextra                     Print extra (possibly unwanted) warnings
  -Wfatal-errors              Exit on the first error occurred
  -Winline                    Warn when an inlined function cannot be inlined
  -Wlarger-than-<number>      Warn if an object is larger than <number> bytes
  -Wmissing-noreturn          Warn about functions which might be candidates
                              for __attribute__((noreturn))
  -Wpacked                    Warn when the packed attribute has no effect on
                              struct layout
  -Wpadded                    Warn when padding is required to align structure
                              members
  -Wshadow                    Warn when one local variable shadows another
  -Wstrict-aliasing           Warn about code which might break strict aliasing
                              rules
  -Wstrict-aliasing=          Warn about code which might break strict aliasing
                              rules
  -Wswitch                    Warn about enumerated switches, with no default,
                              missing a case
  -Wswitch-default            Warn about enumerated switches missing a
                              "default:" statement
  -Wswitch-enum               Warn about all enumerated switches missing a
                              specific case
  -Wsystem-headers            Do not suppress warnings from system headers
  -Wuninitialized             Warn about uninitialized variables (DISABLED)
  -Wunreachable-code          Warn about code that will never be executed
  -Wunused                    Enable all -Wunused- warnings
  -Wunused-function           Warn when a function is unused
  -Wunused-label              Warn when a label is unused
  -Wunused-parameter          Warn about unused parameters.  Not implied by
                              -Wall
  -Wunused-value              Warn when an expression value is unused
  -Wunused-variable           Warn when a variable is unused
  -aux-info <file>            Emit declaration information into <file>
  -aux-info=                  This switch lacks documentation
  -auxbase                    This switch lacks documentation
  -auxbase-strip              This switch lacks documentation
  -d<letters>                 Enable dumps from specific passes of the compiler
  -dumpbase <file>            Set the file basename to be used for dumps
  -fPIC                       Generate position-independent code if possible
                              (large mode)
  -fPIE                       Generate position-independent code for
                              executables if possible (large mode)
  -fabi-version=              This switch lacks documentation
  -falign-functions           Align the start of functions
  -falign-functions=          This switch lacks documentation
  -falign-jumps               Align labels which are only reached by jumping
  -falign-jumps=              This switch lacks documentation
  -falign-labels              Align all labels
  -falign-labels=             This switch lacks documentation
  -falign-loops               Align the start of loops
  -falign-loops=              This switch lacks documentation
  -fargument-alias            Specify that arguments may alias each other and
                              globals
  -fargument-noalias          Assume arguments may alias globals but not each
                              other
  -fargument-noalias-global   Assume arguments alias neither each other nor
                              globals
  -fasynchronous-unwind-tables Generate unwind tables that are exact at each
                              instruction boundary
  -fbounds-check              Check array bounds at runtime
  -fbranch-count-reg          Replace add, compare, branch with branch on count
                              register
  -fbranch-probabilities      Use profiling information for branch probabilities
  -fbranch-target-load-optimize Perform branch target load optimization before
                              prologue / epilogue threading
  -fbranch-target-load-optimize2 Perform branch target load optimization after
                              prologue / epilogue threading
  -fbtr-bb-exclusive          Restrict target load migration not to re-use
                              registers in any basic block
  -fcall-saved-<register>     Mark <register> as being preserved across
                              functions
  -fcall-used-<register>      Mark <register> as being corrupted by function
                              calls
  -fcaller-saves              Save registers around function calls
  -fcommon                    Do not put uninitialized globals in the common
                              section
  -fcprop-registers           Perform a register copy-propagation optimization
                              pass
  -fcrossjumping              Perform cross-jumping optimization
  -fcse-follow-jumps          When running CSE, follow jumps to their targets
  -fcse-skip-blocks           When running CSE, follow conditional jumps
  -fcx-limited-range          Omit range reduction step when performing complex
                              division
  -fdata-sections             Place data items into their own section
  -fdefer-pop                 Defer popping functions args from stack until
                              later
  -fdelayed-branch            Attempt to fill delay slots of branch instructions
  -fdelete-null-pointer-checks Delete useless null pointer checks
  -fdiagnostics-show-location=[once|every-line] How often to emit source
                              location at the beginning of line-wrapped
                              diagnostics
  -fdump-<type>               Dump various compiler internals to a file
  -fdump-unnumbered           Suppress output of instruction numbers and line
                              number notes in debugging dumps
  -feliminate-dwarf2-dups     Perform DWARF2 duplicate elimination
  -feliminate-unused-debug-symbols Perform unused type elimination in debug info
  -feliminate-unused-debug-types Perform unused type elimination in debug info
  -fexceptions                Enable exception handling
  -fexpensive-optimizations   Perform a number of minor, expensive optimizations
  -ffast-math                 This switch lacks documentation
  -ffinite-math-only          Assume no NaNs or infinities are generated
  -ffixed-<register>          Mark <register> as being unavailable to the
                              compiler
  -ffloat-store               Don't allocate floats and doubles in extended-
                              precision registers
  -fforce-addr                Copy memory address constants into registers
                              before use
  -fforce-mem                 Copy memory operands into registers before use
  -ffunction-cse              Allow function addresses to be held in registers
  -ffunction-sections         Place each function into its own section
  -fgcse                      Perform global common subexpression elimination
  -fgcse-after-reload         Perform global common subexpression elimination
                              after register allocation
  -fgcse-las                  Perform redundant load after store elimination in
                              global common subexpression
  -fgcse-lm                   Perform enhanced load motion during global common
                              subexpression elimination
  -fgcse-sm                   Perform store motion after global common
                              subexpression elimination
  -fguess-branch-probability  Enable guessing of branch probabilities
  -fident                     Process #ident directives
  -fif-conversion             Perform conversion of conditional jumps to
                              branchless equivalents
  -fif-conversion2            Perform conversion of conditional jumps to
                              conditional execution
  -finhibit-size-directive    Do not generate .size directives
  -finline                    Pay attention to the "inline" keyword
  -finline-functions          Integrate simple functions into their callers
  -finline-functions-called-once Integrate functions called once into their
                              callers
  -finline-limit-             This switch lacks documentation
  -finline-limit=<number>     Limit the size of inlined functions to <number>
  -finstrument-functions      Instrument function entry and exit with profiling
                              calls
  -fivopts                    Optimize induction variables on trees
  -fkeep-inline-functions     Generate code for functions even if they are
                              fully inlined
  -fkeep-static-consts        Emit static const variables even if they are not
                              used
  -fleading-underscore        Add a leading underscore to public names
  -floop-optimize             Perform loop optimizations
  -floop-optimize2            Perform loop optimizations using the new loop
                              optimizer
  -fmath-errno                Set errno after built-in math functions
  -fmem-report                Report on permanent memory allocation
  -fmerge-all-constants       Attempt to merge identical constants and constant
                              variables
  -fmerge-constants           Attempt to merge identical constants across
                              compilation units
  -fmessage-length=<number>   Limit diagnostics to <number> characters per
                              line.  0 suppresses line-wrapping
  -fmodulo-sched              Perform SMS based modulo scheduling before the
                              first scheduling pass
  -fmove-loop-invariants      Move loop invariant computations out of loops
  -fmudflap                   Add mudflap bounds-checking instrumentation for
                              single-threaded program.
  -fmudflapir                 Ignore read operations when inserting mudflap
                              instrumentation.
  -fmudflapth                 Add mudflap bounds-checking instrumentation for
                              multi-threaded program.
  -fnon-call-exceptions       Support synchronous non-call exceptions
  -fomit-frame-pointer        When possible do not generate stack frames
  -foptimize-register-move    Do the full register move optimization pass
  -foptimize-sibling-calls    Optimize sibling and tail recursive calls
  -fpack-struct               Pack structure members together without holes
  -fpack-struct=<number>      Set initial maximum structure member alignment
  -fpcc-struct-return         Return small aggregates in memory, not registers
  -fpeel-loops                Perform loop peeling
  -fpeephole                  Enable machine specific peephole optimizations
  -fpeephole2                 Enable an RTL peephole pass before sched2
  -fpic                       Generate position-independent code if possible
                              (small mode)
  -fpie                       Generate position-independent code for
                              executables if possible (small mode)
  -fprefetch-loop-arrays      Generate prefetch instructions, if available, for
                              arrays in loops
  -fprofile                   Enable basic program profiling code
  -fprofile-arcs              Insert arc-based program profiling code
  -fprofile-generate          Enable common options for generating profile info
                              for profile feedback directed optimizations
  -fprofile-use               Enable common options for performing profile
                              feedback directed optimizations
  -fprofile-values            Insert code to profile values of expressions
  -frandom-seed               This switch lacks documentation
  -frandom-seed=<string>      Make compile reproducible using <string>
  -freg-struct-return         Return small aggregates in registers
  -fregmove                   Enables a register move optimization
  -frename-registers          Perform a register renaming optimization pass
  -freorder-blocks            Reorder basic blocks to improve code placement
  -freorder-blocks-and-partition Reorder basic blocks and partition into hot
                              and cold sections
  -freorder-functions         Reorder functions to improve code placement
  -frerun-cse-after-loop      Add a common subexpression elimination pass after
                              loop optimizations
  -frerun-loop-opt            Run the loop optimizer twice
  -freschedule-modulo-scheduled-loops Enable/Disable the traditional scheduling
                              in loops that already passed modulo scheduling
  -frounding-math             Disable optimizations that assume default FP
                              rounding behavior
  -fsched-interblock          Enable scheduling across basic blocks
  -fsched-spec                Allow speculative motion of non-loads
  -fsched-spec-load           Allow speculative motion of some loads
  -fsched-spec-load-dangerous Allow speculative motion of more loads
  -fsched-stalled-insns       Allow premature scheduling of queued insns
  -fsched-stalled-insns-dep   Set dependence distance checking in premature
                              scheduling of queued insns
  -fsched-stalled-insns-dep=<number> Set dependence distance checking in
                              premature scheduling of queued insns
  -fsched-stalled-insns=<number> Set number of queued insns that can be
                              prematurely scheduled
  -fsched-verbose=<number>    Set the verbosity level of the scheduler
  -fsched2-use-superblocks    If scheduling post reload, do superblock
                              scheduling
  -fsched2-use-traces         If scheduling post reload, do trace scheduling
  -fschedule-insns            Reschedule instructions before register allocation
  -fschedule-insns2           Reschedule instructions after register allocation
  -fshared-data               Mark data as shared rather than private
  -fsignaling-nans            Disable optimizations observable by IEEE
                              signaling NaNs
  -fsingle-precision-constant Convert floating point constants to single
                              precision constants
  -fspeculative-prefetching   Use value profiling for speculative prefetching
  -fsplit-ivs-in-unroller     Split lifetimes of induction variables when loops
                              are unrolled.
  -fstack-check               Insert stack checking code into the program
  -fstack-limit               This switch lacks documentation
  -fstack-limit-register=<register> Trap if the stack goes past <register>
  -fstack-limit-symbol=<name> Trap if the stack goes past symbol <name>
  -fstrength-reduce           Perform strength reduction optimizations
  -fstrict-aliasing           Assume strict aliasing rules apply
  -fsyntax-only               Check for syntax errors, then stop
  -ftest-coverage             Create data files needed by "gcov"
  -fthread-jumps              Perform jump threading optimizations
  -ftime-report               Report the time taken by each compiler pass
  -ftls-model=[global-dynamic|local-dynamic|initial-exec|local-exec] Set the
                              default thread-local storage code generation model
  -ftracer                    Perform superblock formation via tail duplication
  -ftrapping-math             Assume floating-point operations can trap
  -ftrapv                     Trap for signed overflow in addition, subtraction
                              and multiplication
  -ftree-based-profiling      Use tree-ssa based implementation of profiling
  -ftree-ccp                  Enable SSA-CCP optimization on trees
  -ftree-ch                   Enable loop header copying on trees
  -ftree-combine-temps        Coalesce memory temporaries in the SSA->normal
                              pass
  -ftree-copyrename           Replace SSA temporaries with better names in
                              copies.
  -ftree-dce                  Enable SSA dead code elimination optimization on
                              trees
  -ftree-dominator-opts       Enable dominator optimizations
  -ftree-dse                  Enable dead store elimination
  -ftree-fre                  Enable Full Redundancy Elimination (FRE) on trees
  -ftree-loop-im              Enable loop invariant motion on trees
  -ftree-loop-ivcanon         Create canonical induction variables in loops
  -ftree-loop-linear          Enable linear loop transforms on trees
  -ftree-loop-optimize        Enable loop optimizations on tree level
  -ftree-lrs                  Perform live range splitting during the SSA-
                              >normal pass.
  -ftree-pre                  Enable SSA-PRE optimization on trees
  -ftree-sra                  Perform scalar replacement of aggregates
  -ftree-ter                  Replace temporary expressions in the SSA->normal
                              pass
  -ftree-vectorize            Enable loop vectorization on trees
  -ftree-vectorizer-verbose=<number> Set the verbosity level of the vectorizer
  -funit-at-a-time            Compile whole compilation unit at a time
  -funroll-all-loops          Perform loop unrolling for all loops
  -funroll-loops              Perform loop unrolling when iteration count is
                              known
  -funsafe-math-optimizations Allow math optimizations that may violate IEEE or
                              ISO standards
  -funswitch-loops            Perform loop unswitching
  -funwind-tables             Just generate unwind tables for exception handling
  -fvar-tracking              Perform variable tracking
  -fvariable-expansion-in-unroller Apply variable expansion when loops are
                              unrolled.
  -fverbose-asm               Add extra commentary to assembler output
  -fvisibility=[default|internal|hidden|protected] Set the default symbol
                              visibility
  -fvpt                       Use expression value profiles in optimizations
  -fweb                       Construct webs and split unrelated uses of single
                              variable
  -fwrapv                     Assume signed arithmetic overflow wraps around
  -fzero-initialized-in-bss   Put zero initialized data in the bss section
  -g                          Generate debug information in default format
  -gcoff                      Generate debug information in COFF format
  -gdwarf-2                   Generate debug information in DWARF v2 format
  -ggdb                       Generate debug information in default extended
                              format
  -gstabs                     Generate debug information in STABS format
  -gstabs+                    Generate debug information in extended STABS
                              format
  -gvms                       Generate debug information in VMS format
  -gxcoff                     Generate debug information in XCOFF format
  -gxcoff+                    Generate debug information in extended XCOFF
                              format
  -m                          This switch lacks documentation
  -o <file>                   Place output into <file>
  -p                          Enable function profiling
  -pedantic                   Issue warnings needed for strict compliance to
                              the standard
  -pedantic-errors            Like -pedantic but issue them as errors
  -quiet                      Do not display functions compiled or elapsed time
  -version                    Display the compiler's version
  -w                          Suppress warnings

The --param option recognizes the following as parameters:

  sra-max-structure-size      The maximum structure size (in bytes) for which
                              GCC will use by-element copies
  sra-max-structure-count     The maximum number of structure fields for which
                              GCC will use by-element copies
  sra-field-structure-ratio   The threshold ratio between instantiated fields
                              and the total structure size
  max-inline-insns-single     The maximum number of instructions in a single
                              function eligible for inlining
  max-inline-insns-auto       The maximum number of instructions when
                              automatically inlining
  max-inline-insns-recursive  The maximum number of instructions inline
                              function can grow to via recursive inlining
  max-inline-insns-recursive-auto The maximum number of instructions non-inline
                              function can grow to via recursive inlining
  max-inline-recursive-depth  The maximum depth of recursive inlining for
                              inline functions
  max-inline-recursive-depth-auto The maximum depth of recursive inlining for
                              non-inline functions
  max-variable-expansions-in-unroller If -fvariable-expansion-in-unroller is
                              used, the maximum number of            times that
                              an individual variable will be expanded          
                              during loop unrolling
  max-delay-slot-insn-search  The maximum number of instructions to consider to
                              fill a delay slot
  max-delay-slot-live-search  The maximum number of instructions to consider to
                              find accurate live register information
  max-pending-list-length     The maximum length of scheduling's pending
                              operations list
  large-function-insns        The size of function body to be considered large
  large-function-growth       Maximal growth due to inlining of large function
                              (in percent)
  inline-unit-growth          how much can given compilation unit grow because
                              of the inlining (in percent)
  inline-call-cost            expense of call operation relative to ordinary
                              aritmetic operations
  max-gcse-memory             The maximum amount of memory to be allocated by
                              GCSE
  max-gcse-passes             The maximum number of passes to make when doing
                              GCSE
  gcse-after-reload-partial-fraction The threshold ratio for performing partial
                              redundancy elimination after reload.
  gcse-after-reload-critical-fraction The threshold ratio of critical edges
                              execution count that permit performing redundancy
                              elimination after reload.
  max-unrolled-insns          The maximum number of instructions to consider to
                              unroll in a loop
  max-average-unrolled-insns  The maximum number of instructions to consider to
                              unroll in a loop on average
  max-unroll-times            The maximum number of unrollings of a single loop
  max-peeled-insns            The maximum number of insns of a peeled loop
  max-peel-times              The maximum number of peelings of a single loop
  max-completely-peeled-insns The maximum number of insns of a completely
                              peeled loop
  max-completely-peel-times   The maximum number of peelings of a single loop
                              that is peeled completely
  max-once-peeled-insns       The maximum number of insns of a peeled loop that
                              rolls only once
  max-unswitch-insns          The maximum number of insns of an unswitched loop
  max-unswitch-level          The maximum number of unswitchings in a single
                              loop
  max-iterations-to-track     Bound on the number of iterations the brute force
                              # of iterations analysis algorithm evaluates
  max-sms-loop-number         Maximum number of loops to perform swing modulo
                              scheduling on (mainly for debugging)
  sms-max-ii-factor           A factor for tuning the upper bound that swing
                              modulo scheduler uses for scheduling a loop
  sms-dfa-history             The number of cycles the swing modulo scheduler
                              considers when 	  checking conflicts using DFA
  sms-loop-average-count-threshold A threshold on the average loop count
                              considered by the swing modulo scheduler
  hot-bb-count-fraction       Select fraction of the maximal count of
                              repetitions of basic block in program given basic
                              block needs to have to be considered hot
  hot-bb-frequency-fraction   Select fraction of the maximal frequency of
                              executions of basic block in function given basic
                              block needs to have to be considered hot
  tracer-dynamic-coverage-feedback The percentage of function, weighted by
                              execution frequency, that must be covered by
                              trace formation. Used when profile feedback is
                              available
  tracer-dynamic-coverage     The percentage of function, weighted by execution
                              frequency, that must be covered by trace
                              formation. Used when profile feedback is not
                              available
  tracer-max-code-growth      Maximal code growth caused by tail duplication
                              (in percent)
  tracer-min-branch-ratio     Stop reverse growth if the reverse probability of
                              best edge is less than this threshold (in percent)
  tracer-min-branch-probability-feedback Stop forward growth if the probability
                              of best edge is less than this threshold (in
                              percent). Used when profile feedback is available
  tracer-min-branch-probability Stop forward growth if the probability of best
                              edge is less than this threshold (in percent).
                              Used when profile feedback is not available
  max-crossjump-edges         The maximum number of incoming edges to consider
                              for crossjumping
  min-crossjump-insns         The minimum number of matching instructions to
                              consider for crossjumping
  max-goto-duplication-insns  The maximum number of insns to duplicate when
                              unfactoring computed gotos
  max-cse-path-length         The maximum length of path considered in cse
  lim-expensive               The minimum cost of an expensive expression in
                              the loop invariant motion
  iv-consider-all-candidates-bound Bound on number of candidates below that all
                              candidates are considered in iv optimizations
  iv-max-considered-uses      Bound on number of iv uses in loop optimized in
                              iv optimizations
  iv-always-prune-cand-set-bound If number of candidates in the set is smaller,
                              we always try to remove unused ivs during its
                              optimization
  scev-max-expr-size          Bound on size of expressions used in the scalar
                              evolutions analyzer
  global-var-threshold        Given N calls and V call-clobbered vars in a
                              function.  Use .GLOBAL_VAR if NxV is larger than
                              this limit
  max-cselib-memory-locations The maximum memory locations recorded by cselib
  ggc-min-expand              Minimum heap expansion to trigger garbage
                              collection, as a percentage of the total size of
                              the heap
  ggc-min-heapsize            Minimum heap size before we start collecting
                              garbage, in kilobytes
  max-reload-search-insns     The maximum number of instructions to search
                              backward when looking for equivalent reload
  max-aliased-vops            The maximum number of virtual operands allowed to
                              represent aliases before triggering alias
                              grouping.
  max-sched-region-blocks     The maximum number of blocks in a region to be
                              considered for interblock scheduling
  max-sched-region-insns      The maximum number of insns in a region to be
                              considered for interblock scheduling
  max-last-value-rtl          The maximum number of RTL nodes that can be
                              recorded as combiner's last value
  integer-share-limit         The upper bound for sharing integer constants

The F95 front end recognizes the following options:

  -D                          Define a preprocessor macro
  -E                          Show preprocessed source only
  -I<directory>               Append 'directory' to the include and module
                              files search path
  -M                          Write dependencies in Makefile form
  -U                          Undefine a preprocessor macro
  -Wall                       Enable most warning messages
  -Werror=                    Comma separated list of warnings to treat as
                              errors.
  -Wglobals                   Cross-check procedure use and definition within
                              the same source file.  On by default.
  -Wimplicit-interface        Warn about using an implicit interface.
  -Wimplicit-none             Same as -fimplicit-none
  -Wline-truncation           Warn about truncated source lines
  -Wmissing-intent            Warn about missing intents on formal arguments
  -Wno=                       Disable warnings (comma separated list of warning
                              numbers).
  -Wobsolescent               Warn about obsolescent constructs
  -Wprecision-loss            Warn about precision loss in implicit type
                              conversions
  -Wunset-vars                Warn about unset variable
  -Wunused-internal-procs     Warn if an internal procedure is never used.
  -Wunused-module-procs       Warn about unused module procedures.  Used to
                              build ONLY clauses.
  -Wunused-module-vars        Warn about unused module variables.  Used to
                              build ONLY clauses.
  -Wunused-target             Warn about variables that have the TARGET
                              attribute, but are never pointed to.
  -Wunused-types              Warn about unused module types.  Not implies by
                              -Wall.
  -Wunused-vars               Warn about unused variables
  -arch                       Specify architexture (ignored)
  -cpp                        Force the input files to be run through the C
                              preprocessor
  -d8                         Set the default real and integer kinds to double
                              precision
  -fbackslash                 Interpret backslashes in character constants as
                              escape code (default).  Use -fno-backslash to
                              treat backslashes literally.
  -fc-binding                 Display C bindings for procedures to standard
                              output
  -fcase-upper                Make all public symbols uppercase
  -fd-comment                 Make D lines executable statements (fixed form).
  -fdollar-ok                 Allow dollar signs in entity names
  -fendian=                   Specify endian for unformatted I/O.  Legal values
                              are 'big' and 'little'
  -ffixed-form                Assume that the source file is fixed form
  -ffixed-line-length-132     132 character line width in fixed mode
  -ffixed-line-length-80      80 character line width in fixed mode
  -ffree-form                 Assume that the source file is free form
  -ffree-line-length-huge     Large free-form line length, currently 10000.
  -fimplicit-none             Specify that no implicit typing is allowed,
                              unless overridden by explicit IMPLICIT statements
  -finteger=<n>               Initialize uninitialized scalar integer variables
                              to <n>
  -fintrinsic-extensions      Allow all intrinsic extensions even in a strict
                              language mode (-std=f95, -std=F, -std=f2003)
  -fintrinsic-extensions=     Allow specified intrinsic extensions even in a
                              strict language mode (-std=f95, -std=F,
                              -std=f2003).  Comma-separated list of names, case
                              insensitive.
  -flogical=none/true/false   Initialize uninitialized scalar logical variables
                              to true or false
  -fmod=<directory>           Put module files in 'directory'
  -fmodule-private            Set default accessibility of module entities to
                              PRIVATE
  -fmultiple-save             Allow the SAVE attribute to be specified multiple
                              times
  -fone-error                 Force compilation to stop after the first error.
  -fonetrip                   Force DO-loops to execute at least once (buggy
                              fortran 66)
  -fpack-derived              Try to layout derived types as compact as possible
  -fpointer=none/null/invalid Initialize a scalar pointer to NULL(), or to a
                              non-null location.
  -fqkind=<n>                 Set the kind for a real with the 'q' exponent to
                              'n'
  -freal-loops                Allow DO-loops with real typed loop variable and
                              parameters
  -freal=none/zero/nan/inf/+inf/-inf Initialize uninitialized scalar real
                              variables to the given value
  -fround=nearest/plus/minus/zero Controls compile-time rounding.  Default is
                              round to nearest, plus is round to plus infinity,
                              minus is minus infinity, zero is towards zero.
  -fsecond-underscore         Append a second trailing underscore in names
                              having an underscore (default).  Use -fno-second-
                              underscore to suppress.
  -fshort-circuit             Cause the .AND. and .OR. operators to not compute
                              the second operand if the value of the expression
                              is known from the first operand.  On by default.
  -fsloppy-char               Prevent type checks when printing formatted
                              characters variables.
  -fstatic                    Put local variables in static memory where
                              possible.
  -fsyntax                    Show IR (Don't depend on this one staying like it
                              is)
  -ftr15581                   Enable TR 15581 allocatable array extensions even
                              in a -std= mode
  -ftrace=none/frame/full     '-ftrace=frame' will insert code to allow stack
                              tracebacks on abnormal end of program.  This will
                              slow down your program. '-trace=full'
                              additionally allows finding the line number of
                              arithmetic exceptions (slower).  Default is
                              'none'.
  -funderscoring              Append a trailing underscore in global names
                              (default).  Use -fno-underscoring to suppress.
  -fzero                      Implies -finteger=0, -flogical=false and
                              -freal=zero
  -i4                         Set kinds of integers without specification to
                              kind=4 (32 bits)
  -i8                         Set kinds of integers without specification to
                              kind=8 (64 bits)
  -include                    Auto-include a file.  Does nothing.
  -max-frame-size=<n>         How large a single stack frame will get before
                              arrays are allocated dynamically
  -no-cpp                     Prevent the input files from being C preprocessed
  -nontraditional             Cause non-traditional C preprocessing.
  -r10                        Set kinds of reals without kind specifications to
                              kind=10 reals
  -r16                        Set kinds of reals without kind specifications to
                              kind=16 reals
  -r4                         Set kinds of reals without kind specifications to
                              kind=4 reals
  -r8                         Set kinds of reals without kind specifications to
                              double default precision
  -std=F                      Warn about non-F features
  -std=f2003                  Strict fortran 2003 checking
  -std=f95                    Strict fortran 95 checking
  -traditional                Cause traditional C preprocessing (default).


Target specific options:
  -mthreads                 Use Mingw-specific thread support
  -mnop-fun-dllimport       Ignore dllimport for functions
  -mdll                     Generate code for a DLL
  -mconsole                 Create console application
  -mwin32                   Set Windows defines
  -mno-win32                Don't set Windows defines
  -mwindows                 Create GUI application
  -mno-cygwin               Use the Mingw32 interface
  -mcygwin                  Use the Cygwin interface
  -mno-tls-direct-seg-refs  Do not use direct references against %gs when accessing tls data
  -mtls-direct-seg-refs     Use direct references against %gs when accessing tls data
  -mno-red-zone             Do not use red-zone in the x86-64 code
  -mred-zone                Use red-zone in the x86-64 code
  -mno-ms-bitfields         Use gcc default bitfield layout
  -mms-bitfields            Use native (MS) bitfield layout
  -m32                      Generate 32bit i386 code
  -m64                      Generate 64bit x86-64 code
  -m96bit-long-double       sizeof(long double) is 12
  -m128bit-long-double      sizeof(long double) is 16
  -mno-sse3                 Do not support MMX, SSE, SSE2 and SSE3 built-in functions and code generation
  -msse3                    Support MMX, SSE, SSE2 and SSE3 built-in functions and code generation
  -mno-sse2                 Do not support MMX, SSE and SSE2 built-in functions and code generation
  -msse2                    Support MMX, SSE and SSE2 built-in functions and code generation
  -mno-sse                  Do not support MMX and SSE built-in functions and code generation
  -msse                     Support MMX and SSE built-in functions and code generation
  -mno-3dnow                Do not support 3DNow! built-in functions
  -m3dnow                   Support 3DNow! built-in functions
  -mno-mmx                  Do not support MMX built-in functions
  -mmmx                     Support MMX built-in functions
  -mno-accumulate-outgoing-args Do not use push instructions to save outgoing arguments
  -maccumulate-outgoing-args Use push instructions to save outgoing arguments
  -mno-push-args            Do not use push instructions to save outgoing arguments
  -mpush-args               Use push instructions to save outgoing arguments
  -mno-inline-all-stringops Do not inline all known string operations
  -minline-all-stringops    Inline all known string operations
  -mno-align-stringops      Do not align destination of the string operations
  -malign-stringops         Align destination of the string operations
  -mstack-arg-probe         Enable stack probing
  -momit-leaf-frame-pointer Omit the frame pointer in leaf functions
  -mfancy-math-387          Generate sin, cos, sqrt for FPU
  -mno-fancy-math-387       Do not generate sin, cos, sqrt for FPU
  -mno-fp-ret-in-387        Do not return values of functions in FPU registers
  -mfp-ret-in-387           Return values of functions in FPU registers
  -mno-ieee-fp              Do not use IEEE math for fp comparisons
  -mieee-fp                 Use IEEE math for fp comparisons
  -mno-svr3-shlib           Uninitialized locals in .data
  -msvr3-shlib              Uninitialized locals in .bss
  -mno-align-double         Align doubles on word boundary
  -malign-double            Align some doubles on dword boundary
  -mno-rtd                  Use normal calling convention
  -mrtd                     Alternate calling convention
  -mno-soft-float           Use hardware fp
  -msoft-float              Do not use hardware fp
  -mhard-float              Use hardware fp
  -mno-80387                Do not use hardware fp
  -m80387                   Use hardware fp
  -mtls-dialect=            Use given thread-local storage dialect
  -masm=                    Use given assembler dialect
  -mcmodel=                 Use given x86-64 code model
  -mbranch-cost=            Branches are this expensive (1-5, arbitrary units)
  -mpreferred-stack-boundary= Attempt to keep stack aligned to this power of 2
  -malign-functions=        Function starts are aligned to this power of 2
  -malign-jumps=            Jump targets are aligned to this power of 2
  -malign-loops=            Loop code aligned to this power of 2
  -mregparm=                Number of registers used to pass integer arguments
  -march=                   Generate code for given CPU
  -mfpmath=                 Generate floating point mathematics using given instruction set
  -mtune=                   Schedule code for given CPU

There are undocumented target specific options as well.

For bug reporting instructions, please see:
http://www.g95.org <NAME_EMAIL>
