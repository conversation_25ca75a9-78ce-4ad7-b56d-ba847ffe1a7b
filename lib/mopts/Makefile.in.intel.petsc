
DEFS=-DHost=\"HOST\" -DHome=\"$(HOME)\" -DPETSC
CCMP=icpc
PCCMP=mpicxx
COPT=-O2 -ansi $(DEFS) -fPIC 
#-Wall
COPT0=-O0 -ansi $(DEFS) -fPIC -Wall

PETSCI = -I$(HOME)/cfd/petsc.intel/petsc.complex/arch-opt/include -I$(HOME)/cfd/petsc.intel/petsc.complex/include
PETSCL = -L$(HOME)/cfd/petsc.intel/petsc.complex/arch-opt/lib -lpetsc -lm -ldl
#PETSCI = -I$(HOME)/cfd/petsc.intel/petsc.complex/arch-dbg/include -I$(HOME)/cfd/petsc.intel/petsc.complex/include
#PETSCL = -L$(HOME)/cfd/petsc.intel/petsc.complex/arch-dbg/lib -lpetsc -lm -ldl

MKL_HOME=/opt/intel/oneapi/mkl/2022.0.2/
LAPACK=-L$(MKL_HOME)/lib/intel64/ -lmkl_intel_lp64 -lmkl_sequential -lmkl_core -lpthread -lm -ldl -lmkl_lapack95_lp64  -lmkl_blas95_lp64

