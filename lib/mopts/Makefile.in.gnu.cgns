
# GNU xeo64 Ubuntu (tcsh) Transtec XT1200 - enkidu.me.ic.ac.uk
DEFS=-DHost=\"HOST\" -DHome=\"$(HOME)\" -DCGNS
CCMP=g++
PCCMP=mpicxx
COPT=-O2 $(DEFS) -fPIC -Wall -I/home/<USER>/cfd/mdspan/include
#COPT=-g $(DEFS) -fPIC -Wall -I/home/<USER>/cfd/mdspan/include
COPT0=-O0 $(DEFS) -fPIC -Wall
#LAPACK=-llapack -lblas

CGNSI = -I$(HOME)/cfd/high_order/cgns_build/include
CGNSL = -L$(HOME)/cfd/high_order/cgns_build/lib -lcgns

HDF5I = -I$(HOME)/cfd/high_order/hdf5_build/include
HDF5L = -L$(HOME)/cfd/high_order/hdf5_build/lib -lhdf5 -lm -ldl
