
# nvidia hpc sdk
DEFS=-DHost=\"HOST\" -DHome=\"$(HOME)\" -DCGNS
CCMP=nvc++
PCCMP=mpicxx
COPT=-fast -acc -Minfo=accel -gpu=cc86,nordc $(DEFS) -fPIC -Wall
#COPT=-g -acc -Minfo=accel -gpu=cc86,nordc $(DEFS) -fPIC -Wall

CGNSI = -I$(HOME)/cfd/high_order/cgns_build/include
CGNSL = -L$(HOME)/cfd/high_order/cgns_build/lib -lcgns

HDF5I = -I$(HOME)/cfd/high_order/hdf5_build/include
HDF5L = -L$(HOME)/cfd/high_order/hdf5_build/lib -lhdf5 -lm -ldl

