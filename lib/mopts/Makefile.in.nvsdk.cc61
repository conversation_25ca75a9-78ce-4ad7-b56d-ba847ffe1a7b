
# nvidia hpc sdk
DEFS=-DHost=\"HOST\" -DHome=\"$(HOME)\"
CCMP=nvc++
PCCMP=mpicxx
#COPT=-fast -acc -Minfo=accel -gpu=cc61,nordc,managed -Mcuda $(DEFS) -fPIC -Wall
COPT=-fast -acc -Minfo=accel -gpu=cc61,nordc -Mcuda $(DEFS) -fPIC -Wall
#COPT=-g -acc -Minfo=accel -gpu=cc61,nordc -Mcuda $(DEFS) -fPIC -Wall
#COPT=-fast -acc -Minfo=accel -gpu=cc70,nordc,managed $(DEFS) -fPIC -Wall
#LAPACK=-llapack -lblas


