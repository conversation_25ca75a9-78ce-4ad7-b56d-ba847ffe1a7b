   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdfdneut.h>

   void cCfdFdneut::setup( string fnme, cFdDomain **dmn )
  {
      cout << "fdneut normal setup=====================================\n";      
      Int     nw=0;
      Int     kb;
      Int     jb;
      string *line=NULL;
      string syn=" .,#%";

      Int            im;

     *dmn= new cFdDomain();

// default element kinds from FDNEUT

// assign element kinds
      cout << "will open file "<<fnme<<"\n";
      
      image( fnme );//, &nx, &np, &rwrk, &ng, &neg, &npg, igt, &iwrk, &gnms );

      subv( nx,np, rwrk,xp );
      mnms= new string[ng];
      blbl= new string[ng];
      bnms= new string[ng];
      blbs= new string[ng];
      bbjs= new string[ng];
      bbj=  new cFbndry*[ng];

      if( nx == 2 )
     {
         elems2(*dmn );
     }
      else
     {
         elems3(*dmn );
     }


      nms( &nm,mnms );

// boundary groups
      string btyp[8]={"NEUTRAL","PERIODIC","FREE","INV","VWALL","PROF", "WAVE", "SLIDE"}; 
      string bpl[8]={ "libbcs.so",
                      "none",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so" };
      string bpo[8]={ "unibcs",
                      "none",
                      "unibcs",
                      "adiabcs",
                      "adiabcs",
                      "profilebcs",
                      "unibcs",
                      "unibcs"};
      Int kbk[8]={ neut_fbndry,neut_fbndry,free_fbndry,inv_fbndry,visc_fbndry,free_fbndry,wave_fbndry,slide_fbndry };

      nmsb( &mb,bnms );

      for( im=0;im<mb;im++ )
     {
         nw= 0;
         line=NULL;
         parse( bnms[im],&nw,&line,syn );
         if( nw > 1 )
        {
            kb=inlst( line[0], 8,btyp );
            jb=inlst( line[1],mb,bnms );
            blbs[im]= bpl[kb];
            bbjs[im]= bpo[kb];
            blbl[im]= line[1];
            bbj[im]= newfbndry(kbk[kb]);
        }
         else
        {
            cout << "cannot parse boundary label " << bnms[im] << "\n";
            exit(0);
        }
         delete[] line; line=NULL; nw=0;
     }
    (*dmn)->boundaries( mb,blbl,blbs,bbjs );
    (*dmn)->bndries( bbj );

  }

  /* void cCfdFdneut::setuprestart( string fnme, cFdDomain **dmn )  
  {      
      Int     nw=0;
      Int     kb;
      Int     jb;
      string *line=NULL;
      string syn=" .,#%";

      cout << "fdneut restart setup=====================================\n";      

      Int            im;

     *dmn= new cFdDomain();

// default element kinds from FDNEUT

// assign element kinds
      cout << "will open file "<<fnme<<"########################################\n";
      
      image( fnme );//, &nx, &np, &rwrk, &ng, &neg, &npg, igt, &iwrk, &gnms );

      subv( nx,np, rwrk,xp );
      mnms= new string[ng];
      blbl= new string[ng];
      bnms= new string[ng];
      blbs= new string[ng];
      bbjs= new string[ng];
      bbj=  new cFbndry*[ng];

      if( nx == 2 )
     {
         elems2(*dmn );
     }
      else
     {
         elems3(*dmn );
     }


      nms( &nm,mnms );

// boundary groups
      string btyp[8]={"NEUTRAL","PERIODIC","FREE","INV","VWALL","PROF", "WAVE", "SLIDE"}; 
      string bpl[8]={ "libbcs.so",
                      "none",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so" };
      string bpo[8]={ "binrestartbcs",
                      "none",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs"};
      Int kbk[8]={ neut_fbndry,neut_fbndry,free_fbndry,inv_fbndry,visc_fbndry,free_fbndry,wave_fbndry,slide_fbndry };

      nmsb( &mb,bnms );

      for( im=0;im<mb;im++ )
     {
         nw= 0;
         line=NULL;
         parse( bnms[im],&nw,&line,syn );
         if( nw > 1 )
        {
            kb=inlst( line[0], 8,btyp );
            jb=inlst( line[1],mb,bnms );
            blbs[im]= bpl[kb];
            bbjs[im]= bpo[kb];
            blbl[im]= line[1];
            bbj[im]= newfbndry(kbk[kb]);
        }
         else
        {
            cout << "cannot parse boundary label " << bnms[im] << "\n";
            exit(0);
        }
         delete[] line; line=NULL; nw=0;
     }
    (*dmn)->boundaries( mb,blbl,blbs,bbjs );
    (*dmn)->bndries( bbj );

  }*/


   void cCfdFdneut::setuprestart( string fnme, cFdDomain **dmn )
  {
      cout << "fdneut normal setup=====================================\n";      
      Int     nw=0;
      Int     kb;
      Int     jb;
      string *line=NULL;
      string syn=" .,#%";

      Int            im;

     *dmn= new cFdDomain();

// default element kinds from FDNEUT

// assign element kinds
      cout << "will open file "<<fnme<<"\n";
      
      image( fnme );//, &nx, &np, &rwrk, &ng, &neg, &npg, igt, &iwrk, &gnms );

      subv( nx,np, rwrk,xp );
      mnms= new string[ng];
      blbl= new string[ng];
      bnms= new string[ng];
      blbs= new string[ng];
      bbjs= new string[ng];
      bbj=  new cFbndry*[ng];

      if( nx == 2 )
     {
         elems2(*dmn );
     }
      else
     {
         elems3(*dmn );
     }


      nms( &nm,mnms );

// boundary groups
      string btyp[8]={"NEUTRAL","PERIODIC","FREE","INV","VWALL","PROF", "WAVE", "SLIDE"}; 
      string bpl[8]={ "libbcs.so",
                      "none",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so" };
      string bpo[8]={ "binrestartbcs",
                      "none",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs"};
      Int kbk[8]={ neut_fbndry,neut_fbndry,free_fbndry,inv_fbndry,visc_fbndry,free_fbndry,wave_fbndry,slide_fbndry };

      nmsb( &mb,bnms );

      for( im=0;im<mb;im++ )
     {
         nw= 0;
         line=NULL;
         parse( bnms[im],&nw,&line,syn );
         if( nw > 1 )
        {
            kb=inlst( line[0], 8,btyp );
            jb=inlst( line[1],mb,bnms );
            blbs[im]= bpl[kb];
            bbjs[im]= bpo[kb];
            blbl[im]= line[1];
            bbj[im]= newfbndry(kbk[kb]);
        }
         else
        {
            cout << "cannot parse boundary label " << bnms[im] << "\n";
            exit(0);
        }
         delete[] line; line=NULL; nw=0;
     }
    (*dmn)->boundaries( mb,blbl,blbs,bbjs );
    (*dmn)->bndries( bbj );

  }

   void cCfdFdneut::setup( cDom *dev, string fnme, cFdDomain **dmn )
  {
      cout << "fdneut normal setup=====================================\n";      
      Int     nw=0;
      Int     kb;
      Int     jb;
      string *line=NULL;
      string syn=" .,#%";

      Int            im;
      string devnm ,cpath;

     *dmn= new cFdDomain();

// default element kinds from FDNEUT

// assign element kinds
      cout << "will open file "<<fnme<<"\n";
      
      image( fnme );//, &nx, &np, &rwrk, &ng, &neg, &npg, igt, &iwrk, &gnms );

      subv( nx,np, rwrk,xp );
      mnms= new string[ng];
      blbl= new string[ng];
      bnms= new string[ng];
      blbs= new string[ng];
      bbjs= new string[ng];
      bbj=  new cFbndry*[ng];

      if( nx == 2 )
     {
         elems2(*dmn );
     }
      else
     {
         elems3(*dmn );
     }


      nms( &nm,mnms );

// boundary groups
      string btyp[8]={"NEUTRAL","PERIODIC","FREE","INV","VWALL","PROF", "WAVE", "SLIDE"}; 
      string bpl[8]={ "libbcs.so",
                      "none",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so" };
      string bpo[8]={ "unibcs",
                      "none",
                      "unibcs",
                      "adiabcs",
                      "adiabcs",
                      "profilebcs",
                      "unibcs",
                      "unibcs"};
      Int kbk[8]={ neut_fbndry,neut_fbndry,free_fbndry,inv_fbndry,visc_fbndry,free_fbndry,wave_fbndry,slide_fbndry };

      nmsb( &mb,bnms );

//      for( im=0;im<mb;im++ )
//     {
//         nw= 0;
//         line=NULL;
//         parse( bnms[im],&nw,&line,syn );
//         if( nw > 1 )
//        {
//            kb=inlst( line[0], 8,btyp );
//            jb=inlst( line[1],mb,bnms );
//            blbs[im]= bpl[kb];
//            bbjs[im]= bpo[kb];
//            blbl[im]= line[1];
//            bbj[im]= newfbndry(kbk[kb]);
//        }
//         else
//        {
//            cout << "cannot parse boundary label " << bnms[im] << "\n";
//            exit(0);
//        }
//         delete[] line; line=NULL; nw=0;
//     }

      for( im=0;im<mb;im++ )
     {
         blbs[im]= bpl[im];
         bbjs[im]= bpo[im];
         blbl[im]= bnms[im];
     }
      ng = mb;

      cpath = dev->getcpath();
      devnm = dev->getname();
      json_parse_bnd(cpath, devnm);

    (*dmn)->boundaries( mb,blbl,blbs,bbjs );
    (*dmn)->bndries( bbj );

  }
