   using namespace std;

#  include <cfdfdneut.h>

   void cCfdFdneut::nms( Int *nm, string *mnms )
  {
      Int    im,ig;
    (*nm)= 0;
      for( ig=0;ig<ng;ig++ )
     {
         if( igt[0][ig] == igt[1][ig] )
        {
            im= inlst( gnms[ig], *nm,mnms );
            if( im == -1 )
           {
               im=(*nm);
               mnms[im]= gnms[ig];
             (*nm)++;
           } 
        }
     }
  }

   void cCfdFdneut::nmsb( Int *nm, string *mnms )
  {
      Int    im,ig;
    (*nm)= 0;
      for( ig=0;ig<ng;ig++ )
     {
         if( igt[0][ig] != igt[1][ig] )
        {
            im= inlst( gnms[ig], *nm,mnms );
            if( im == -1 )
           {
               im=(*nm);
               mnms[im]= gnms[ig];
             (*nm)++;
           } 
        }
     }
  }
