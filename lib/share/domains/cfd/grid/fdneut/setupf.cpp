   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -
#  include <iniParser.h>
#  include <cfdfdneut.h>

//   void cCfdFdneut::setupf( string misc, cFdDomain *dmn )
//  {      
//      cTabData      *ctab,*dtab;
//
//      cCosystem     *coo;
//      cGas          *fld;
//      cVisc         *vsc;
//
//      string         syn=";()";
//      string        *argv=NULL;
//      Int            argc=0;
//      Int            irg,ico,igs,ivs;
//      Int            i;
//
//      parse( misc,&argc,&argv, syn );
//      for( i=0;i<argc;i++ )
//      {
//         cout << "argument "<<i<<" "<<argv[i]<<"\n";
//      }
//
//      irg= inlst( (string)"cosystem",argc,argv );
//      ico= inlst( argv[irg+1], (Int)cosystem_num,(string*)cosystem_s );
//
//      irg= inlst( (string)"turb",argc,argv );
//      ivs= inlst( argv[irg+1], (Int)num_visc,(string*)visc_s );
//
//      irg= inlst( (string)"gas",argc,argv );
//      igs= inlst( argv[irg+1], (Int)gas_num,(string*)gas_s );
//
////    cout << "the number of coordinates appears to be "<<nx<<"\n";
//
//      ctab= new cTabData();
//      coo= newcosystem( ico );
//      coo->validate( nx );
//
//      coo->get( ctab );
//      for( i=0;i<argc;i+=2 )
//     {
//         ctab->force( argv[i],argv[i+1] ); 
//     }
////    ctab->check();
//      coo->set( ctab );
//
//      vsc= newvisc( ivs );
//      fld= newgas( igs, coo,vsc );
//    (dmn)->assgncoo( coo );
//    (dmn)->assgngas( fld );
//
//      delete ctab; ctab= NULL;
//
//      dtab= new cTabData();
//      dmn->get( dtab );
//      for( i=0;i<argc;i+=2 )
//     {
//         dtab->force( argv[i],argv[i+1] ); 
//     }
////    dtab->check();
//      dmn->set( dtab );
//
//      delete dtab;
//      delete[] argv;
//  }
      
   void cCfdFdneut::setupf( string devnm, cFdDomain *dmn )
  {      
      cTabData      *ctab,*dtab;

      cCosystem     *coo;
      cGas          *fld;
      cVisc         *vsc;

      string         syn=";()";
      string        *argv=NULL;
      Int            argc=0;
      Int            irg,ico,igs,ivs;
      Int            i;

      IniParser parser;
      string item, val, str_sec;

      parser.parseFromFile("input.au3x");

      str_sec = devnm;

      //coordinate system
      item = "cosystem";   val = parser.getValue(str_sec, item);
      ico= inlst( val, (Int)cosystem_num,(string*)cosystem_s );
      cout << "the number of coordinates appears to be "<<nx<<"\n";
      cout << "the argument for the cosystem is "<<ico<<"\n";
      coo= newcosystem( ico );
      coo->validate( nx );
      cout << "here?????????????? setupf " << ico << "\n";
      if(ico==0)
     {
         cout << "here ++++++++++++++++ setupf\n";
         //annular coordinate system
         ctab= new cTabData();
         coo->get( ctab );
         item = "grid-sectors";   val = parser.getValue(str_sec, item);
         ctab->force( item, val ); cout << item << " " << val << "\n"; 

         item = "assembly-sectors";   val = parser.getValue(str_sec, item);
         ctab->force( item, val );  cout << item << " " << val << "\n";

         coo->set( ctab );
         delete ctab; ctab= NULL;
     }

      //turbulence model
      item = "turb";   val = parser.getValue(str_sec, item);
      ivs= inlst( val, (Int)num_visc,(string*)visc_s );

      //gas model
      item = "gas";   val = parser.getValue(str_sec, item);
      igs= inlst( val, (Int)gas_num,(string*)gas_s );


//      parse( misc,&argc,&argv, syn );
//      for( i=0;i<argc;i++ )
//     {
//     //  cout << "argument "<<i<<" "<<argv[i]<<"\n";
//     }
//
//      irg= inlst( (string)"cosystem",argc,argv );
//      ico= inlst( argv[irg+1], (Int)cosystem_num,(string*)cosystem_s );
//
//      irg= inlst( (string)"turb",argc,argv );
//      ivs= inlst( argv[irg+1], (Int)num_visc,(string*)visc_s );
//
//      irg= inlst( (string)"gas",argc,argv );
//      igs= inlst( argv[irg+1], (Int)gas_num,(string*)gas_s );


//      coo->get( ctab );
//      for( i=0;i<argc;i+=2 )
//     {
//         ctab->force( argv[i],argv[i+1] ); 
//     }
////    ctab->check();
//      coo->set( ctab );

      vsc= newvisc( ivs );
      fld= newgas( igs, coo,vsc );
    (dmn)->assgncoo( coo );
    (dmn)->assgngas( fld );


      //solver properties
      dtab= new cTabData();
      dmn->get( dtab );
//      for( i=0;i<argc;i+=2 )
//     {
//         dtab->force( argv[i],argv[i+1] ); 
//     }
//    dtab->check();
//      item = "solution-initialiser-library";   val = parser.getValue(str_sec, item);
//      dtab->force( item, val );  
//      item = "solution-initialiser-object";    val = parser.getValue(str_sec, item);
//      dtab->force( item, val );  
//      item = "solution-writer-library";        val = parser.getValue(str_sec, item);
//      dtab->force( item, val );  
//      item = "solution-writer-object";         val = parser.getValue(str_sec, item);
//      dtab->force( item, val );  
//      item = "frame-initialiser-library";      val = parser.getValue(str_sec, item);
//      dtab->force( item, val );  
//      item = "frame-initialiser-object";       val = parser.getValue(str_sec, item);
//      dtab->force( item, val );  
      item = "frame-speed";                    val = parser.getValue(str_sec, item);
      dtab->force( item, val );  
      item = "smoothing-iterations";           val = parser.getValue(str_sec, item);
      dtab->force( item, val );  
      item = "pre-smoothing-iterations";       val = parser.getValue(str_sec, item);
      dtab->force( item, val );  
      item = "outer-iterations";               val = parser.getValue(str_sec, item);
      dtab->force( item, val );  
      item = "output-frequency";               val = parser.getValue(str_sec, item);
      dtab->force( item, val );  
      item = "cfl-initial";                    val = parser.getValue(str_sec, item);
      dtab->force( item, val );  
      item = "cfl-final";                      val = parser.getValue(str_sec, item);
      dtab->force( item, val );  
      item = "cfl-increment";                  val = parser.getValue(str_sec, item);
      dtab->force( item, val );  

      dmn->set( dtab );

      delete dtab;
      delete[] argv;
  }
      
