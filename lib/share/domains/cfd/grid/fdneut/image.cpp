   using namespace std;

#  include <cfdfdneut.h>

   void cCfdFdneut::image( string nme )
//, Int nx, Int np, Real64 *rwrk, Int ng, Int **neg, Int *npg, Int **igt, Int ***iwrk, string **gnms )
  {

      Int      ne,ip,jp,ie,ix,il,nl;
      Int      idum;
      string   sdum,sdum1,sdum2;
      stringstream s;
      ifstream fle;
      string   fnme;
      string   line,buf;

      Real    *x[3]; 
      Int    **iep=NULL;
 
      fnme= nme+".FDNEUT";
      fle.open( fnme.c_str() );

// skip FDNEUT file header

      getline( fle, line ); 
      getline( fle, line );
      getline( fle, line );
      getline( fle, line );
      getline( fle, line );

// number of grid points, coordinates, elements, groups

      getline( fle, line );
      s.clear();
      s.str( line );

      s >> (np);
      s >>   ne;
      s >> (ng);
      s >> (nx);

      getline( fle, line );
      getline( fle, line );
      getline( fle, line );
      getline( fle, line );
      getline( fle, line );
      getline( fle, line );
      getline( fle, line );

// coordinates
     rwrk= new Real[(nx)*(np)];
      subv( (nx),(np), rwrk,x );
      for( ip=0;ip<(np);ip++ )
     {
         getline( fle, line );
         s.clear();
         s.str( line );
         Int idum;
         s >> idum;
         for( ix=0;ix<(nx);ix++ )
        {
            s >> x[ix][ip];
        }
     }

      getline( fle, line ); 
      getline( fle, line ); 
      getline( fle, line ); 

      iwrk= new Int*[(ng)];
      neg=  new  Int[(ng)];
    (npg)=  new  Int[(ng)];
      igt[0]=  new  Int[(ng)];
      igt[1]=  new  Int[(ng)];
      gnms= new string[(ng)];
      
      for( Int ig=0;ig<(ng);ig++ )
     {
         getline( fle, line ); 
         s.clear();
         s.str( line );
         s >> sdum;
         s >> idum; 
         s >> sdum;
         s >> neg[ig]; 
         s >> sdum;
         s >> (npg)[ig]; 
         s >> sdum;
         s >> igt[0][ig]; 
         s >> sdum;
         s >> igt[1][ig]; 

         iwrk[ig]= new Int[(npg)[ig]*neg[ig]];
         iep= new Int*[(npg)[ig]];
         subv( (npg)[ig],neg[ig], iwrk[ig],iep );
 
         getline( fle, line ); 
         s.clear();
         s.str( line );
         s >> sdum1;
         s >> sdum1;
         s >> gnms[ig];
         s >> sdum2; 
  
         for( ie=0;ie<neg[ig];ie++ )
        {
//          getline( fle, line ); 
            nl= 1+(npg)[ig]/9;
            line= "";
            for( il=0;il<nl;il++ ) // FDNEUT files have no more than 9 entries per line in the topology section
           {
               getline( fle, buf );
               line= line+buf;
           }

            s.clear();
            s.str( line );
            s >> idum;
            for( jp=0;jp<(npg)[ig];jp++ )
           {
               s >> idum;
               iep[jp][ie]= idum-1;
           }
        }
         delete[] iep; iep=NULL;
     }
      fle.close();
  }
