
   using namespace std;

#  include <cfdfdneut.h>

   void cCfdFdneut::elems2( cFdDomain *fdm )
//, Int  nbk, Int *jbk, Int *nbp, Int *nbq, Int *nbd, Int bpmsk[][MxNPSs],
//                            Int  nek, Int *jek, Int *nep, Int *neq,           Int ipmsk[][MxNPSs],
//                            Int *nce[], Int ***icep[], Int ***iceq[] )
  {

      Int            iek,ick;
      cFElement     *elm[MxNSk];
      cFElement     *blm[MxNSk];

      setv( (Int)0,(Int)MxNSk, (Int)-1, jek );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nep );
      setv( (Int)0,(Int)MxNSk, (Int) 0, neq );

      setv( (Int)0,(Int)MxNSk, (Int)-1, jbk );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nbp );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nbq );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nbd );

// boundary elements

      nbk= 0;
      blm[ nbk]= new cfe22();
      nbp[ nbk]= blm[ nbk]->getnp();
      nbq[ nbk]= blm[ nbk]->getnq();
      nbd[ nbk]= blm[ nbk]->getnd();
      jbk[0]=  nbk; 
      bpmsk[ nbk][0]=0;
      bpmsk[ nbk][1]=1;
    ( nbk)++;

// internal elements

      nek=0;
      elm[ nek]= new cfq42();
      nep[ nek]= elm[ nek]->getnp();
      neq[ nek]= elm[ nek]->getnq();
      jek[1]=  nek; 
      ipmsk[ nek][0]=0;
      ipmsk[ nek][1]=1;
      ipmsk[ nek][2]=2;
      ipmsk[ nek][3]=3;
    ( nek)++;

      elm[ nek]= new cft32();
      nep[ nek]= elm[ nek]->getnp();
      neq[ nek]= elm[ nek]->getnq();
      jek[2]=  nek; 
      ipmsk[ nek][0]=0;
      ipmsk[ nek][1]=1;
      ipmsk[ nek][2]=2;
    ( nek)++;

// boundary masks

      for( ick=0;ick<MxNSk;ick++ )
     {
         nce[ick]= new Int[ nek];
         icep[ick]= new Int**[ nek];
         iceq[ick]= new Int**[ nek];
         for( iek=0;iek< nek;iek++ )
        {
            elm[iek]->cmsk( ick, nce[ick]+iek, icep[ick]+iek,iceq[ick]+iek );           
        }
     }

      fdm->elements( ( nbk),blm, ( nek),elm );

  }
