   using namespace std;

#  include <cfdfdneut.h>

   void cCfdFdneut::gather( string mnms, Int  *nb, Int *sibp[] )
  {
      Int           *jwrk[MxNPSs];
      Int           *iep[MxNPSs];
      Int            ig,kp,je,jp,ie;
      Int            iek;
      Int            me[MxNSk];


      setv( (Int)0,(Int)MxNSk, (Int)0,nb );
      setv( (Int)0,(Int)MxNSk, (Int)0,me );
      setv( (Int)0,(Int)MxNSk, (Int*)0,sibp );
      for( ig=0;ig<ng;ig++ )
     {
         if( gnms[ig] == mnms )
        {
            iek= igt[0][ig];
            iek= jbk[iek];
            nb[iek]+= neg[ig];
        }
     }

      for( iek=0;iek<nbk;iek++ )
     {
         sibp[iek]= new Int[nb[iek]*nbp[iek]];
     }

      for( ig=0;ig<ng;ig++ )
     {
         if( gnms[ig] == mnms )
        {
            iek= igt[0][ig];
            iek= jbk[iek];
            subv( npg[ig],neg[ig], iwrk[ig], jwrk );
            subv( nbp[iek],nb[iek], sibp[iek], iep );
            ie= me[iek];
            for( je=0;je<neg[ig];je++ )
           {
               for( kp=0;kp<nbp[iek];kp++ )
              {
                  jp= bpmsk[iek][kp];
                  iep[jp][ie]= jwrk[kp][je];
              }
               ie++;
           }
            me[iek]= ie;
        }
     }
  }
