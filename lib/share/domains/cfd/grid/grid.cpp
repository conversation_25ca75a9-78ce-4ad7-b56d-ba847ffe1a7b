   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdgrid.h>

   void cCfdGrid::blank()
  {      
      Int           ig;
      ug=  NULL;
      ihlp=NULL;
      bhlp=NULL;
      neg=NULL;
      npg=NULL;
      gnms=NULL;
      blbl=NULL;
      blbs=NULL;
      bbjs=NULL;

      bnms=NULL;
      mnms=NULL;

      sxp= NULL;
      sxq= NULL;
      setv( (Int)0,(Int)MxNSk,(Int)0,   ne );
      setv( (Int)0,(Int)MxNSk,(Int*)NULL,siep );
      setv( (Int)0,(Int)MxNSk,(Int*)NULL,sieq );
      setv( (Int)0,(Int)MxNSk,(Int*)NULL,siem );

      setv( (Int)0,(Int)MxNBG,(Real*)NULL,sxb  );
      setv( (Int)0,(Int)MxNBG,(Real*)NULL,sxqb  );
      setv( (Int)0,(Int)MxNBG,(Int)0,nbb  );
      for( ig=0;ig<MxNBG;ig++ )
     {
         setv( (Int)0,(Int)MxNSk,(Int)0,   nb[ig]   );
         setv( (Int)0,(Int)MxNSk,(Int*)NULL,sibp[ig] );
         setv( (Int)0,(Int)MxNSk,(Int*)NULL,sibq[ig] );
         setv( (Int)0,(Int)MxNSk,(Int*)NULL,sibb[ig] );
     }

      setv( (Int)0,(Int)MxNSk, (Int)0, ne );
   
      setv( (Int)0,(Int)MxNSk,(Int*)NULL, nce );
      setv( (Int)0,(Int)MxNSk,(Int***)NULL, icep );
      setv( (Int)0,(Int)MxNSk,(Int***)NULL, iceq );

      setv( (Int)0,(Int)MxNSk,(Int*)NULL,medium_marker );

      bcht = false;
  }

   cCfdGrid::cCfdGrid()
  {      
      blank();
  }

   cCfdGrid::~cCfdGrid()
  {
      Int iek,ick;;
      delete[] neg;
      delete[] npg;

      delete[] ihlp;
      delete[] bnms;
      delete[] blbs;
      delete[] blbl;
      delete[] bbjs;
      delete[] gnms;
      delete[] mnms;
      for( ick=0;ick<MxNSk;ick++ )
     {
         delete[] nce[ick];
         delete[] icep[ick];
         delete[] iceq[ick];
     }

      for( iek=0;iek<nek;iek++ )
     {
         delete[] iep[iek];
         delete[] ieq[iek];
     //    delete[] medium_marker[iek];
     }
      delete[] iep;
      delete[] ieq;
      delete[] bbj;
   
      delete ug; 
      //delete[] sxq; sxq= NULL;

      bcht = false;
  }


void cCfdGrid::json_parse_bnd(string cpath, string devnm)
{
    Int iv,ig;
    string fnm;
    Real tmpq[100];
    cFbndry *val;
    Int ihlp[100];

    fnm= cpath+ "/"+devnm+"_init.json";

    // 1. Load file into a string
    ifstream ifs( fnm.c_str() );
    if ( ! ifs )
    {
        cerr << "Error: cannot open '" << fnm << "'\n";
        return;
    }

    ostringstream oss;
    oss << ifs.rdbuf();
    string text = oss.str();

    try
    {
        // 2. Parse root
        cJsonParser parser( text );
        cJsonValue  root   = parser.Parse();
        const map<string,cJsonValue> & rootObj = root.AsObject();

        // ----- Boundaries -----
        if ( ! rootObj.count( "boundaries" ) )
        {
            cerr << "Error: no \"boundaries\" section\n";
            return;
        }
    
        const vector<cJsonValue> & bnds = rootObj.at( "boundaries" ).AsArray();
    
        cout << "--- Boundaries (" << bnds.size() << ") ---\n";
        if(bnds.size()!=ng)
        {
            cout << "Error: not all boundaries are listed in the json file\n";
            exit(0);
        }
    
        //if the order of the boundaries are different from the .flag file
        //need a permutation array
        for ( size_t i = 0; i < bnds.size(); ++i )
        {
            ihlp[i] = -1;
            const map<string,cJsonValue> & bndObj = bnds[i].AsObject();
            string name = bndObj.at( "name" ).AsString();
            for(ig=0; ig<ng; ig++)
            {
                if(name==bnms[ig])
                {
                    ihlp[i] = ig;
                    break;
                }
            }
             assert(ihlp[i]>=0);
        }
    
        for ( size_t i = 0; i < bnds.size(); ++i )
        {
            ig = ihlp[i];
    
            const map<string,cJsonValue> & bndObj = bnds[i].AsObject();
    
            string name = bndObj.at( "name" ).AsString();
            string type = bndObj.at( "type" ).AsString();
    
            cout << "\nBoundary [" << i << "]\n" << "  Name: " << name << ",  Type: " << type << "\n";
    
            // All types have an "init" object
            const map<string,cJsonValue> & initObj = bndObj.at( "init" ).AsObject();
    
            cout << std::fixed << setprecision( 6 );
   
            val = NULL; 
            // --- freestream ---
            if ( type == "freestream" )
            {
                 val= new cFreeFbndry();
                 bbj[ig] = val;
            }
            // --- sub_in (uniform or profile_data) ---
            else if ( type == "sub_in" )
            {
                //cannot not do it now
                 val= new cFreeFbndrySubin();
                 bbj[ig] = val;
            }
            // --- sub_out (uniform or profile_data) ---
            else if ( type == "sub_out" )
            {
                 val= new cFreeFbndrySubout();
                 bbj[ig] = val;
            }
            // --- inviscid_wall & viscous_wall ---
            else if ( type == "inviscid_wall" )
            {
                val= new cInvFbndry();
                bbj[ig] = val;
            }
            else if ( type == "viscous_wall" )
            {
                val= new cViscFbndry();
                bbj[ig] = val;
            }
            // --- sliding_plane ---
            else if ( type == "sliding_plane" )
            {
                val= new cSlideFbndry();
                bbj[ig] = val;
            }
            else if ( type == "thermal_wall" )
            {
                 val= new cThermalWallFbndry();
                 cout << "create thermwal wall boundary\n";
                 bbj[ig] = val;
            }
            // --- periodic ---
            else if ( type == "periodic" )
            {
                val= new cFbndry();
                bbj[ig] = val;
            }
            else if ( type == "massflow" )
            {
                //cannot not do it now
                 val= new cFreeFbndryMass();
                 bbj[ig] = val;
            }
            else
            {
                cout << "Error: cannot handle this boundary type yet\n";
                exit(0);
            }
        }

    }
    catch ( const exception & e )
    {
        cerr << "JSON error: " << e.what() << "\n";
    }
}
