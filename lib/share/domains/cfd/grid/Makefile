include ../../../../Makefile.in

CSRC=   grid.cpp \
	dof.cpp \
	bdof.cpp \
	jm28/cfdjm28.cpp \
	jm28/elems2.cpp \
	jm28/elems3.cpp \
	jm28/setup.cpp \
	jm28/setupf.cpp \
	jm28/jm28.cpp \
	jm28/nms.cpp \
	jm28/image.cpp \
	jm28/load.cpp
#dntprd.cpp

FSRC= 


COBJ=$(CSRC:.cpp=.o)
FOBJ=$(FSRC:.F=.o)
OBJS=$(COBJ) $(FOBJ)

BINS=libfdneut.so

$(BINS): $(OBJS)
	$(PCCMP) $(COPT) $(OBJS) $(ORGL) $(ENGL) $(SYSL) $(LIBF) -shared -o $@

clean:
	rm -f $(OBJS) $(BINS)

.cpp.o:
	$(PCCMP) $(COPT) -Wunused -I./ $(ORGI) $(ENGI) $(SYSI) $(PETSCI) -fPIC -o $@ -c $<

.F.o:
	$(PFCMP) $(FOPT) -I./ $(ORGI) $(ENGI) $(SYSI) $(GUII)  -o $@ -c $<

