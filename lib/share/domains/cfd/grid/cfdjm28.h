#  ifndef _CFDAU3X_
#  define _CFDAU3X_

#  include <cfdgrid.h>

   class cCfdJm28: public cCfdGrid
  {
      protected:

         virtual void elems2( cFdDomain *fdm );
         virtual void elems3( cFdDomain *fdm );

         virtual void image( string mnms );
         virtual void imagebin( string mnms, string devnm );

         virtual void nms( Int *nm, string *mnms );
         virtual void nmsb( Int *nm, string *mnms );

     public:
         cCfdJm28();
         virtual ~cCfdJm28();
         virtual void setup( cDom* dev, string, cFdDomain ** );
         virtual void setupf( string,cFdDomain * );
         virtual void load( cFdDomain * );
  };

#  endif
