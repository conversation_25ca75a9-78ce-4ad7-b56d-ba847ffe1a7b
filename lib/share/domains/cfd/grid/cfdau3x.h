#  ifndef _CFDAU3X_
#  define _CFDAU3X_

#  include <cfdgrid.h>

   class cCfdAu3x: public cCfdGrid
  {
      protected:

         virtual void elems2( cFdDomain *fdm );
         virtual void elems3( cFdDomain *fdm );

         virtual void image( string mnms );

         virtual void nms( Int *nm, string *mnms );
         virtual void nmsb( Int *nm, string *mnms );

     public:
         cCfdAu3x();
         virtual ~cCfdAu3x();
         virtual void setup( string, cFdDomain ** );
         virtual void setupf( string,cFdDomain * );
         virtual void load( cFdDomain * );
  };

#  endif
