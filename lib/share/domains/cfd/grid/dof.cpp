   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdgrid.h>

   void cCfdGrid::dof( Int nek, Int *ne, Int *nep, Int *neq, Int *siep[], Int *sieq[], Int nx, Real *xp[], Int *nq, Real **sxq )
  {      
      Int ix,iq,ie,iek,jq,ip,jp,kq;
      Int *iep[MxNPSs],*ieq[MxNPSs];
      Real *xq[3];
      Real x[3];
      Real dx[3];

// count the degrees of freedom
      iq=0;
      for( iek=0;iek<nek;iek++ )
     {
         iq+= neq[iek]*ne[iek];
         sieq[iek]= new Int[neq[iek]*ne[iek]];
     }
    (*nq)= iq;
    (*sxq)=new Real[nx*(*nq)]; 
      subv( nx,(*nq), (*sxq),xq );

      iq=0;
      for( iek=0;iek<nek;iek++ )
     {
         subv( neq[iek],ne[iek], sieq[iek],ieq );
         for( ie=0;ie<ne[iek];ie++ )
        {
            for( jq=0;jq<neq[iek];jq++ )
           {
               ieq[jq][ie]= iq;
               iq++;
           }
        }
     }

      for( iek=0;iek<nek;iek++ )
     {
         subv( neq[iek],ne[iek], sieq[iek],ieq );
         subv( nep[iek],ne[iek], siep[iek],iep );
         for( ie=0;ie<ne[iek];ie++ )
        {
            setv( (Int)0,(Int)3, (Real)0.,x );
            for( jp=0;jp<nep[iek];jp++ )
           {
               ip= iep[jp][ie];
               line( ip,nx,xp, dx );
               vsum( nx,dx,x, x );
// replace with appropriate vsum
           }
            for( ix=0;ix<nx;ix++ ) 
           {
               x[ix]/= (Real)nep[iek];
           }
            for( jq=0;jq<neq[iek];jq++ )
           {
               kq= ieq[jq][ie];
               for( ix=0;ix<nx;ix++ )
              {
                  xq[ix][kq]= x[ix];
              }
           }
        }
     }
  }
