   using namespace std;

#  include <cfdgrid.h>
  
   void cCfdGrid::bdof( Int *nep, Int *neq, Int **iep[], Int **ieq[], Int *nce[], Int ***icep[], Int ***iceq[],
                  cUgraph *ug,
                  Int  *nb, Int nbk, Int *nbp, Int *nbq, Int *sibp[], Int *sibq[] )
  {
      Int ibk,iek,ib,ie,jq,jb,iq;
      Int **ibp;
      Int **ibq;
      Int *ibe[3];
      for( ibk=0;ibk<nbk;ibk++ )
     {
         sibq[ibk]= new Int[nb[ibk]*nbq[ibk]];
            
         ibp= new Int*[nbp[ibk]];
         ibq= new Int*[nbq[ibk]];
         subv( nbp[ibk],nb[ibk], sibp[ibk],ibp );
         subv( nbq[ibk],nb[ibk], sibq[ibk],ibq );
         ibe[0]= new Int[nb[ibk]];
         ibe[1]= new Int[nb[ibk]];
         ibe[2]= new Int[nb[ibk]];
         ug->match( nb[ibk],nbp[ibk],ibp, ibe, nep,iep,nce[ibk],icep[ibk] );
        for( ib=0;ib<nb[ibk];ib++ )
       {
           iek= ibe[2][ib];
           if( neq[iek] == nbq[ibk] )
          {
               for( jq=0;jq<nbq[ibk];jq++ ) 
              {
                  ie=  ibe[0][ib];
                  jb=  ibe[1][ib];
                  iq= iceq[ibk][iek][jq][jb];
                  ibq[jq][ib]= ieq[iek][iq][ie];
              }
           }               
        }
         delete[] ibe[0];
         delete[] ibe[1];
         delete[] ibe[2];
         delete[] ibp;
         delete[] ibq;
     }
  }

   void cCfdGrid::bdof( Int *nep, Int *neq, Int **iep[], Int **ieq[], Int *nce[], Int ***icep[], Int ***iceq[],
                  cUgraph *ug,
                  Int  *nb, Int nbk, Int *nbp, Int *nbq, Int *sibp[], Int *sibq[], Int *medium_marker[] )
  {
      Int ibk,iek,ib,ie,jq,jb,iq;
      Int **ibp;
      Int **ibq;
      Int *ibe[3];
      for( ibk=0;ibk<nbk;ibk++ )
     {
         sibq[ibk]= new Int[nb[ibk]*nbq[ibk]];
            
         ibp= new Int*[nbp[ibk]];
         ibq= new Int*[nbq[ibk]];
         subv( nbp[ibk],nb[ibk], sibp[ibk],ibp );
         subv( nbq[ibk],nb[ibk], sibq[ibk],ibq );
         ibe[0]= new Int[nb[ibk]];
         ibe[1]= new Int[nb[ibk]];
         ibe[2]= new Int[nb[ibk]];
         ug->match( nb[ibk],nbp[ibk],ibp, ibe, nep,iep,nce[ibk],icep[ibk], medium_marker );
        for( ib=0;ib<nb[ibk];ib++ )
       {
           iek= ibe[2][ib];
           if( neq[iek] == nbq[ibk] )
          {
               for( jq=0;jq<nbq[ibk];jq++ ) 
              {
                  ie=  ibe[0][ib];
                  jb=  ibe[1][ib];
                  iq= iceq[ibk][iek][jq][jb];
                  ibq[jq][ib]= ieq[iek][iq][ie];
              }
           }               
        }
         delete[] ibe[0];
         delete[] ibe[1];
         delete[] ibe[2];
         delete[] ibp;
         delete[] ibq;
     }
  }
