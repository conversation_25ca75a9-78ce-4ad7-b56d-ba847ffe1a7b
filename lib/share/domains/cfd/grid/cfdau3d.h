#  ifndef _CFDAU3D_
#  define _CFDAU3D_

#  include <cfdgrid.h>

   class cCfdAu3d: public cCfdGrid
  {
      protected:

         virtual void elems2( cFdDomain *fdm );
         virtual void elems3( cFdDomain *fdm );

         virtual void image( string mnms );

         virtual void nms( Int *nm, string *mnms );
         virtual void nmsb( Int *nm, string *mnms );

     public:
         cCfdAu3d();
         virtual ~cCfdAu3d();
         virtual void setup( string, cFdDomain ** );
         virtual void setupf( string,cFdDomain * );
         virtual void load( cFdDomain * );
  };

   extern "C" void pltscan( Int *, const char *, Int *,  Int *, Int *, Int *, 
                            Int *, Int *,char ** );
   extern "C" void pltload( Int *, const char *, Int *, Int *, Real *, Int *, 
                            Int **, Int *, Int **, Int *, Int * );

#  endif
