   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdau3d.h>
#  include <cassert>

   void cCfdAu3d::setup( string fnme, cFdDomain **dmn )
  {      
      Int     nw=0;
      Int     kb;
      Int     jb;
      string  wrk;

      Int            im;

     *dmn= new cFdDomain();

// default element kinds from FDNEUT

// assign element kinds
      image( fnme );

//    subv( nx,np, rwrk,xp );
      mnms= new string[ng];
      blbl= new string[ng];
      bnms= new string[ng];
      blbs= new string[ng];
      bbjs= new string[ng];
      bbj=  new cFbndry*[ng];

      nek=0;
      nbk=0;
      elems2(*dmn );
      elems3(*dmn );

      nms( &nm,mnms );


// boundary groups
      string btyp[13]={"NEUT","RIGH","LEFT","FREE","INTERF","INLE","EXIT","HUB","CASI","BLAD","PLTFM","INJ", "INVI"};
      string bpl[13]={"none",
                      "none",
                      "none",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so" };
      string bpo[13]={"none",
                      "none",
                      "none",
                      "unibcs",
                      "unibcs",
                      "q263bc",
                      "q263bc",
                      "adiabcs",
                      "adiabcs",
                      "adiabcs",
                      "adiabcs",
                      "unibcs",
                      "adiabcs"};
      Int kbk[13]={ neut_fbndry,neut_fbndry,neut_fbndry,free_fbndry,free_fbndry,free_fbndry,free_fbndry,
                   visc_fbndry,visc_fbndry,visc_fbndry,visc_fbndry,inj_fbndry, inv_fbndry };

      nmsb( &mb,bnms );

      for( im=0;im<mb;im++ )
     {
         nw= 0;
         wrk= bnms[im].substr(0,4);
         
         kb=inlst( wrk, 13,btyp );
         if( kb == -1 ){ if( bnms[im].find( "INTERF",0 ) != string::npos ){ kb=4; }; }
         if( kb == -1 ){ if( bnms[im].find( "PLTFM",0 ) != string::npos ){ kb=10; }; }

         blbs[im]= bpl[kb];
         bbjs[im]= bpo[kb];
         blbl[im]= bnms[im];
         bbj[im]= newfbndry(kbk[kb]);
         cout << bnms[im]<<" WILL BE "<<btyp[kb]<<" ("<<kbk[kb]<<") created boundary object "<<bbj[im]<<" "<<bpo[kb]<<"\n";
     }
    (*dmn)->boundaries( mb,blbl,blbs,bbjs );
    (*dmn)->bndries( bbj );

  }
      

