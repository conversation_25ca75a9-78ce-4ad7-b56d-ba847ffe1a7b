# include            <fprec.h>

c23456789 123456789 123456789 123456789 123456789 123456789 123456789 12
c        1         2         3         4         5         6         7

c ... Author          <PERSON> <<EMAIL>>
c ... Created         Sun May 11 20:14:59 BST 2008
c ... Changes History -
c ... Next Change(s)  ( work in progress )
c ... Purpose         cBlock basic constructor

      subroutine  icpyf( nv, ist1,ist2,nc, ld1,ia1, ld2,ia2 )
 
      implicit none

c ... arguments

      Int                 nv
      Int                 ist1,ist2,nc
      Int                 ld1,ld2
      Int                 ia1,ia2
      dimension           ia1(ld1,nv),ia2(ld2,nv)

c ... local symbols

      Int                 i,i1,i2,j

c ... executable statements

      do 10 j=1,nv
        i1= ist1
        i2= ist2
        do 10 i=1,nc
          ia2(i2,j)= ia1(i1,j)
          i2= i2+1
          i1= i1+1
   10 continue

      return

      end

c23456789 123456789 123456789 123456789 123456789 123456789 123456789 12
c        1         2         3         4         5         6         7

c ... Author          <PERSON> di <PERSON> <<EMAIL>>
c ... Created         Sun May 11 20:14:59 BST 2008
c ... Changes History -
c ... Next Change(s)  ( work in progress )
c ... Purpose         cBlock basic constructor

      subroutine  rcpyf( nv, ist1,ist2,nc, ld1,ra1, ld2,ra2 )
 
      implicit none

c ... arguments

      Int                 nv
      Int                 ist1,ist2,nc
      Int                 ld1,ld2
      Real                ra1,ra2
      dimension           ra1(ld1,nv),ra2(ld2,nv)

c ... local symbols

      Int                 i,i1,i2,j

c ... executable statements

      do 10 j=1,nv
        i1= ist1
        i2= ist2
        do 10 i=1,nc
          ra2(i2,j)= ra1(i1,j)
          i2= i2+1
          i1= i1+1
   10 continue

      return

      end
