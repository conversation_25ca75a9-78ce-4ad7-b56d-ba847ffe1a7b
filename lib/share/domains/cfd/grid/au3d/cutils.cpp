using namespace std;

#include <iostream>
#include <cprec.h>


   extern "C" void cassign( Int *len, char *src, char **dst )
  {
      Int i;
      for( i=0;i<*len;i++ )
     {
       (*dst)[i]= src[i];
     }
  }

   extern "C" void cretrieve( Int *len, char **src, char *dst )
  {
      Int i;
      for( i=0;i<*len;i++ )
     {
       dst[i]= (*src)[i];
     }
  }

   extern "C" void icpyf( Int *, Int *, Int*, Int *, Int *, Int *, Int *, Int * );

   extern "C" void iassign( Int *nv, Int *ist1, Int *ist2, Int *nc, Int *ld1, Int *ia1, Int *ld2, Int **ia2 )
  {
      icpyf( nv, ist1,ist2,nc, ld1,ia1, ld2,*ia2 );
  }
