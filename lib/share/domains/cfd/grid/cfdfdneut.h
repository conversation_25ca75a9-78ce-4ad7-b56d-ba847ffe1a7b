
#  ifndef _CFDFDNEUT_
#  define _CFDFDNEUT_

#  include <cfdgrid.h>

   class cCfdFdneut: public cCfdGrid
  {
      protected:

         Real          *rwrk;
         Real          *xwrk;
         Real         **xbwrk;
         Int          **iwrk;
         Int           *jwrk[MxNPSs];
         Int           *igt[2];


         virtual void elems2( cFdDomain *fdm );
         virtual void elems3( cFdDomain *fdm );

         virtual void image( string mnms );

         virtual void gather( Int nm, string *mnms, Int  *ne,Int *siem[], Int *siep[] );
         virtual void gather( string mnms, Int  *ne, Int *siep[] );

         virtual void nmsb( Int *nm, string *mnms );
         virtual void nms( Int *nm, string *mnms );
         virtual void blank();

     public:
         cCfdFdneut();
         virtual ~cCfdFdneut();
         virtual void setup( string, cFdDomain ** );
         virtual void setup( cDom *, string, cFdDomain ** );
         virtual void setuprestart( string, cFdDomain ** );
         virtual void setupbinrestart( string, cFdDomain ** ){ cout << "Error: setupbinrestart for cCfdFdneut is not done yet";};
         virtual void setupf( string,cFdDomain * );
         virtual void load( cFdDomain * );
  };

#  endif
