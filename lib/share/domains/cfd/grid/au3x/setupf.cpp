   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdau3x.h>

   void cCfdAu3x::setupf( string misc, cFdDomain *dmn )
  {      
      cTabData      *ctab,*dtab;

      cCosystem     *coo;
      cGas          *fld;
      cVisc         *vsc;

      string         syn=";()";
      string        *argv=NULL;
      Int            argc=0;
      Int            irg,ico,igs,ivs;
      Int            i;

      parse( misc,&argc,&argv, syn );
      for( i=0;i<argc;i++ )
     {
//       cout << "argument "<<i<<" "<<argv[i]<<"\n";
     }

      irg= inlst( (string)"cosystem",argc,argv );
      ico= inlst( argv[irg+1], (Int)cosystem_num,(string*)cosystem_s );

      irg= inlst( (string)"turb",argc,argv );
      ivs= inlst( argv[irg+1], (Int)num_visc,(string*)visc_s );

      irg= inlst( (string)"gas",argc,argv );
      igs= inlst( argv[irg+1], (Int)gas_num,(string*)gas_s );

      cout << "the number of coordinates appears to be "<<nx<<"\n";
      ctab= new cTabData();
      coo= newcosystem( ico );
      coo->validate( nx );

      coo->get( ctab );
      for( i=0;i<argc;i+=2 )
     {
         ctab->force( argv[i],argv[i+1] ); 
     }
//    ctab->check();
      coo->set( ctab );

      vsc= newvisc( ivs );
      fld= newgas( igs, coo,vsc );
    (dmn)->assgncoo( coo );
    (dmn)->assgngas( fld );

      delete ctab; ctab= NULL;

      dtab= new cTabData();
      dmn->get( dtab );
      for( i=0;i<argc;i+=2 )
     {
         dtab->force( argv[i],argv[i+1] ); 
     }
//    dtab->check();
      dmn->set( dtab );

      delete dtab;
      delete[] argv;
  }
      
