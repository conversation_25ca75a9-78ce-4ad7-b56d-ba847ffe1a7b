   using namespace std;

#  include <cfdau3x.h>

   void cCfdAu3x::image( string nme )
  {

     Int          i,j,iek,ig,ibk;
     ifstream     fle;
     Int          nep;

     string       fnme;
     fnme= nme+".grid";
     fle.open( fnme.c_str() );


      fle >> nx;
      fle >> np;
      sxp= new Real[nx*np]; subv( nx,np,sxp,xp );
      for( i=0;i<np*nx;i++ )
     {
         fle >> sxp[i];
     }

      for(iek=0;iek<6;iek++)
     {
//       cout << iek<<"\n";
         fle >> ne[iek];
         fle >> nep;
         if( ne[iek] > 0 )
        {
//          cout << "RETRIEVED "<<ne[iek]<<" "<<nep<<"-NODED ELEMENTS FROM THE JM28 GRID\n";
            siep[iek]= new Int[ nep*ne[iek] ];
            siem[iek]= new Int[ ne[iek] ];
            setv( 0,ne[iek], (Int)0,siem[iek] );
            for( i=0;i<nep*ne[iek];i++ )
           {
                fle >> siep[iek][i];
           }
        }
     }

     Int nbp[3]= {2,3,4};

     fle >> ng;
     mb= ng;
     gnms= new string[ng];
     for(ig=0; ig<ng; ig++)
    {
        fle >> gnms[ig];
        for(ibk=0; ibk<3; ibk++)
       {
           fle >> nb[ig][ibk];
           if( nb[ig][ibk] > 0 )
          {
//           cout << "RETRIEVED "<<nb[ig][ibk]<<" "<<nbp[ibk]<<"-NODED ELEMENTS FROM THE JM28 GRID FOR GROUP "<<ig<<" "
//                <<  gnms[ig]<<"\n";
             sibp[ig][ibk]= new Int[ nbp[ibk]*nb[ig][ibk] ];
             for( i=0;i<nbp[ibk]*nb[ig][ibk];i++ )
            {
                  fle >> sibp[ig][ibk][i];
            }
         }
       }
    }
  }
