   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdau3x.h>

   void cCfdAu3x::load( cFdDomain *dmn )
  {      
      Int            im;
      Int            iek,ibk;

    ( dmn)->assgnxp( np,sxp );
      dof( nek,ne, nep,neq, siep,sieq, nx,xp, &nq,&sxq );
      dmn->assgnxq( nq,sxq );

      ug= new cUgraph(np);
      iep= new Int**[nek];
      ieq= new Int**[nek];
      for( iek=0;iek<nek;iek++ )
     {
         iep[iek]= new Int*[nep[iek]];
         ieq[iek]= new Int*[neq[iek]];
         dmn->assgniep( iek, ne[iek], siep[iek],sieq[iek],siem[iek] );
         subv( nep[iek],ne[iek],siep[iek],iep[iek] );
         subv( neq[iek],ne[iek],sieq[iek],ieq[iek] );
         ug->build( ne[iek],nep[iek],iep[iek],iek ); 
     }

      for( im=0;im<mb;im++ )
     {

         dof( nbk,nb[im], nbp,nbd, sibp[im],sibb[im], nx,xp, &nbb[im],&sxqb[im] );
         dmn->assgnxb( im,nbb[im],sxqb[im] );

         bdof( nep,neq,iep,ieq, nce,icep,iceq, ug, nb[im], nbk,nbp,nbq, sibp[im],sibq[im] );
         for( ibk=0;ibk<nbk;ibk++  )
        {
          dmn->assgnibp( im,ibk, nb[im][ibk], sibp[im][ibk],sibq[im][ibk],sibb[im][ibk] );
        }
     }


      Int iprd0= inlst( string("RIGHT"), mb,bnms );
      Int iprd1= inlst( string("LEFT"), mb,bnms );

      if( iprd0 != -1 && iprd1 != -1 )
     {
         cout << "periodic surfaces are on groups "<<iprd0<<" "<<iprd1<<"\n";
         Real d;
         dmn->assgnprd( iprd0,iprd1, &d );
         cout << "periodic surfaces are on groups "<<iprd0<<" "<<iprd1<<" max periodic error "<<d<<"\n";
     }

  }
      

