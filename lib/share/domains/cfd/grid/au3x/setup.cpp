   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdau3x.h>

   void cCfdAu3x::setup( string fnme, cFdDomain **dmn )
  {      
      Int     nw=0;
      Int     kb;
      Int     jb;
      string  wrk;

      Int            im;

     *dmn= new cFdDomain();

// default element kinds from FDNEUT

      image( fnme );

      mnms= new string[ng];
      blbl= new string[ng];
      bnms= new string[ng];
      blbs= new string[ng];
      bbjs= new string[ng];
      bbj=  new cFbndry*[ng];

      nek=0;
      nbk=0;
      elems2(*dmn );
      elems3(*dmn );

      nms( &nm,mnms );

// boundary groups
      string btyp[10]={"NEUT","RIGH","LEFT","FREE","INLE","EXIT","HUB","CASI","BLAD","INJ"};
      string bpl[10]={ "none",
                      "none",
                      "none",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so"};
      string bpo[10]={ "none",
                      "none",
                      "none",
                      "unibcs",
                      "unibcs",
                      "unibcs",
                      "adiabcs",
                      "adiabcs",
                      "adiabcs",
                      "unibcs"};
      Int kbk[10]={ neut_fbndry,neut_fbndry,neut_fbndry,free_fbndry,free_fbndry,free_fbndry,
                   inv_fbndry,inv_fbndry,inv_fbndry,inj_fbndry };

      nmsb( &mb,bnms );


      for( im=0;im<mb;im++ )
     {
         nw= 0;
         wrk= bnms[im].substr(0,4);
         
         kb=inlst( wrk, 10,btyp );
         blbs[im]= bpl[kb];
         bbjs[im]= bpo[kb];
         blbl[im]= bnms[im];
         bbj[im]= newfbndry(kbk[kb]);
     }
    (*dmn)->boundaries( mb,blbl,blbs,bbjs );
    (*dmn)->bndries( bbj );

  }
      

