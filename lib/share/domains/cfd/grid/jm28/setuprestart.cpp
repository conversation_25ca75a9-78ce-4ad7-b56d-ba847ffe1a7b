   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdjm28.h>
#  include <cassert>

// boundary groups
      const Int cnb = 21;
      string btypr[cnb]={"NEUT","RIGH","LEFT","FREE","FREX","INLE","EXIT","HUB","CASI","BLAD","PLTFM","INJ", "INVI",
                        "SPIN", "SPEX", "WIN", "WEX", "MIN", "MEX", "MINC", "MEXC"};
      string bplr[cnb]={"none",
                      "none",
                      "none",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so" };
      string bpor[cnb]={"none",
                      "none",
                      "none",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs" ,
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs" };
      Int kbkr[cnb]={ neut_fbndry,neut_fbndry,neut_fbndry,free_fbndry,free_fbndry,
                    free_fbndry,free_fbndry, visc_fbndry,visc_fbndry,
                    visc_fbndry,visc_fbndry,inj_fbndry, inv_fbndry,
                    slide_fbndry,slide_fbndry, free_fbndry_wake, free_fbndry_wake, free_fbndry, free_fbndry,
                    free_fbndry, free_fbndry };


   void cCfdJm28::setuprestart( string fnme, cFdDomain **dmn )
  {      
      Int     nw=0;
      Int     kb;
      Int     jb;
      string  wrk;

      Int            im;

     *dmn= new cFdDomain();

// default element kinds from FDNEUT

// assign element kinds
      cout << "will open file "<<fnme<<"\n";
      
      image( fnme );

//    subv( nx,np, rwrk,xp );
      mnms= new string[ng];
      blbl= new string[ng];
      bnms= new string[ng];
      blbs= new string[ng];
      bbjs= new string[ng];
      bbj=  new cFbndry*[ng];

      nek=0;
      nbk=0;
      if(nx==2)
     {
        elems2(*dmn );
     }
      else if(nx==3)
     {
        elems3(*dmn );
     }

      nms( &nm,mnms );


      nmsb( &mb,bnms );

      for( im=0;im<mb;im++ )
     {
         nw= 0;
         wrk= bnms[im].substr(0,4);
         
         kb=inlst( wrk, 12,btypr );
         if( kb == -1 ){ if( bnms[im].find( "INTERF",0 ) != string::npos ){ kb=4; }; }
         if( kb == -1 ){ if( bnms[im].find( "PLTFM",0 ) != string::npos ){ kb=10; }; }

         blbs[im]= bplr[kb];
         bbjs[im]= bpor[kb];
         blbl[im]= bnms[im];
         bbj[im]= newfbndry(kbkr[kb]);
         cout << bnms[im]<<" WILL BE "<<btypr[kb]<<" ("<<kbkr[kb]<<") created boundary object "<<bbj[im]<<" "<<bpor[kb]<<"\n";
     }
    (*dmn)->boundaries( mb,blbl,blbs,bbjs );
    (*dmn)->bndries( bbj );

  }
      

   void cCfdJm28::setupbinrestart( string fnme, cFdDomain **dmn )
  {      
      Int     nw=0;
      Int     kb;
      Int     jb;
      string  wrk;

      Int            im;

     *dmn= new cFdDomain();

// default element kinds from FDNEUT

// assign element kinds
      cout << "will open file "<<fnme<<"\n";
     
      imagebin(fnme);
     

//    subv( nx,np, rwrk,xp );
      mnms= new string[ng];
      blbl= new string[ng];
      bnms= new string[ng];
      blbs= new string[ng];
      bbjs= new string[ng];
      bbj=  new cFbndry*[ng];

      nek=0;
      nbk=0;
      if(nx==2)
     {
        elems2(*dmn );
     }
      else if(nx==3)
     {
        elems3(*dmn );
     }

      nms( &nm,mnms );


      nmsb( &mb,bnms );

      for( im=0;im<mb;im++ )
     {
         nw= 0;
         wrk= bnms[im].substr(0,4);
         
         kb=inlst( wrk, 19,btypr );
         //if( kb == -1 ){ if( bnms[im].find( "INTERF",0 ) != string::npos ){ kb=4; }; }
         if( kb == -1 ){ if( bnms[im].find( "PLTFM",0 ) != string::npos ){ kb=10; }; }

         blbs[im]= bplr[kb];
         bbjs[im]= bpor[kb];
         blbl[im]= bnms[im];
         bbj[im]= newfbndry(kbkr[kb]);
         cout << bnms[im]<<" WILL BE "<<btypr[kb]<<" ("<<kbkr[kb]<<") created boundary object "<<bbj[im]<<" "<<bpor[kb]<<"\n";
     }
    (*dmn)->boundaries( mb,blbl,blbs,bbjs );
    (*dmn)->bndries( bbj );

  }
