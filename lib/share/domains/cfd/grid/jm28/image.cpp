   using namespace std;

#  include <iniParser.h>
#  include <cfdjm28.h>

   void cCfdJm28::image( string nme )
  {

     Int          i,j,iek,ig,ibk;
     ifstream     fle;
     Int          nep;

     string       fnme;
     fnme= nme+".grid";
     cout << "read grid file "<<fnme << "\n";
     fle.open( fnme.c_str() );


      fle >> nx;
      fle >> np;
      sxp= new Real[nx*np]; subv( nx,np,sxp,xp );
      for( i=0;i<np*nx;i++ )
     {
         fle >> sxp[i];
     }

      for(iek=0;iek<6;iek++)
     {
//       cout << iek<<"\n";
         fle >> ne[iek];
         fle >> nep;
         if( ne[iek] > 0 )
        {
            cout << "RETRIEVED " << ne[iek] << " " << nep 
                 << "-NODED ELEMENTS FROM THE JM28 GRID\n";
            siep[iek]= new Int[ nep*ne[iek] ];
            siem[iek]= new Int[ ne[iek] ];
            setv( 0,ne[iek], (Int)0,siem[iek] );
            for( i=0;i<nep*ne[iek];i++ )
           {
                fle >> siep[iek][i];
           }
        }
     }

     Int nbp[3]= {2,3,4};

     fle >> ng;
     mb= ng;
     gnms= new string[ng];
     for(ig=0; ig<ng; ig++)
    {
        fle >> gnms[ig];
        for(ibk=0; ibk<3; ibk++)
       {
           fle >> nb[ig][ibk];
           if( nb[ig][ibk] > 0 )
          {
             cout << "RETRIEVED " << nb[ig][ibk] << " " << nbp[ibk] 
                  << "-NODED ELEMENTS FROM THE JM28 GRID FOR GROUP "<<ig<<" "
                  <<  gnms[ig]<<"\n";
             sibp[ig][ibk]= new Int[ nbp[ibk]*nb[ig][ibk] ];
             for( i=0;i<nbp[ibk]*nb[ig][ibk];i++ )
            {
                  fle >> sibp[ig][ibk][i];
            }
         }
       }
    }
     fle.close();
  }

   void cCfdJm28::imagebin( string nme, string devnm )
  {

     Int          ix, iek,ig,ibk, ip;
     Int          nep, idum, *tmpiep[MxNSk];
     Real64         rdum;
     size_t       i;
     Real64      *tmpsxp,*tmpxp[3];

     FILE *f;

     string       fnme;
     fnme= nme+".gridbin";
     cout << "read grid file "<<fnme << "\n";
     f = fopen( fnme.c_str(), "r" );

     if(!f)
    {
        cout << "Error: can not read grid file " << fnme << "\n";
    } 

      //fle >> nx;
      //fle >> np;
      i=fread( &nx,1,  sizeof(idum),f );
      i=fread( &np,1,  sizeof(idum),f );

      sxp= new Real[nx*np]; subv( nx,np,sxp,xp );
      tmpsxp= new Real64[nx*np]; subv( nx,np,tmpsxp,tmpxp );
     // for( i=0;i<np*nx;i++ )
     //{
     //    fle >> sxp[i];
     //}
      for(ix=0; ix<nx; ix++)
     {
         //i=fread( xp[ix],np,  sizeof(rdum),f );
         i=fread( tmpxp[ix],np,  sizeof(rdum),f );
     }
      for(ip=0; ip<nx*np;ip++) {sxp[ip]=tmpsxp[ip];}

      for(iek=0;iek<6;iek++)
     {
//       cout << iek<<"\n";
         //fle >> ne[iek];
         //fle >> nep;
         
         i=fread( &ne[iek],1,  sizeof(idum),f );
         i=fread( &nep,    1,  sizeof(idum),f );
         if( ne[iek] > 0 )
        {
            cout << "RETRIEVED " << ne[iek] << " " << nep 
                 << "-NODED ELEMENTS FROM THE JM28 GRID\n";
            siep[iek]= new Int[ nep*ne[iek] ]; subv( nep, ne[iek], siep[iek], tmpiep );
            siem[iek]= new Int[ ne[iek] ];
            setv( 0,ne[iek], (Int)0,siem[iek] );
            for(ip=0; ip<nep; ip++)
           {
               i=fread( tmpiep[ip], ne[iek], sizeof(idum),f );
           }
           // for( i=0;i<nep*ne[iek];i++ )
           //{
           //     i=fread( ,    1,  sizeof(idum),f );
           //     fle >> siep[iek][i];
           //}
        }
     }

     Int nbp[3]= {2,3,4};

     //fle >> ng;
     i=fread( &ng, 1, sizeof(idum),f );
     mb= ng;
     for(ig=0; ig<ng; ig++)
    {
        //fle >> gnms[ig];
        for(ibk=0; ibk<3; ibk++)
       {
           //fle >> nb[ig][ibk];
           i=fread( &nb[ig][ibk], 1, sizeof(idum),f );
           if( nb[ig][ibk] > 0 )
          {
             cout << "RETRIEVED " << nb[ig][ibk] << " " << nbp[ibk] 
                  << "-NODED ELEMENTS FROM THE JM28 GRID FOR GROUP "<<ig<<"\n";
             sibp[ig][ibk]= new Int[ nbp[ibk]*nb[ig][ibk] ]; subv( nbp[ibk], nb[ig][ibk], sibp[ig][ibk], tmpiep );
             for( ip=0;ip<nbp[ibk];ip++ )
            {
               i=fread( tmpiep[ip], nb[ig][ibk], sizeof(idum),f );
            }
          }
       }
    }

      IniParser parser; 
      string item, val, str_sec;
    
      parser.parseFromFile("input.au3x");
  
      str_sec = devnm;
      //check if this is a CHT case
      item = "gas";   val = parser.getValue(str_sec, item);
      if(val=="mfroe_cht")
     {
         //read the medium flag for each element
         for(iek=0;iek<6;iek++)
        {
            if( ne[iek] > 0 )
           {
               cout << "READ MEDIUM TAG FOR THE VOLUME ELEMENTS: " << ne[iek] << " " << nep 
                    << "-NODED ELEMENTS FROM THE JM28 GRID\n";
               medium_marker[iek]= new Int[ ne[iek] ];
               setv( 0,ne[iek], (Int)-1,medium_marker[iek] );
               i=fread( medium_marker[iek], ne[iek], sizeof(idum),f );
           }
        }
         bcht=true;
     }
      else
     {
         //read the medium flag for each element
         for(iek=0;iek<6;iek++)
        {
            if( ne[iek] > 0 )
           {
               //cout << "READ MEDIUM TAG FOR THE VOLUME ELEMENTS: " << ne[iek] << " " << nep 
               //     << "-NODED ELEMENTS FROM THE JM28 GRID \n";
               medium_marker[iek]= new Int[ ne[iek] ];
               setv( 0,ne[iek], (Int)1,medium_marker[iek] ); //set the medium to be fluid by default
           }
        }
     }


     fclose(f);

     gnms= new string[ng];
     fnme= nme+".flag";
     ifstream fle;
     fle.open(fnme.c_str());
     cout << "open boundary lable file " << fnme << "\n";
     fle >> idum;
     for(ig=0; ig<ng; ig++)
    {
       fle >> gnms[ig];
       cout << "BOUNDARY GROUP " << ig << " " << gnms[ig] << "\n";
    }
     fle.close();
     delete[] tmpsxp;
  }

