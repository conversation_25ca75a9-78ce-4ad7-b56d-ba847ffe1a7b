void readingrid( ifstream *fle, Int *nx, Int *gnp, Int *gne, Int *ng, string *bgnm, Int gnb[MxNGrp][MxNCk], Real **gxp, Int *giep[], Int *gibp[MxNGrp][MxNCk])
  {
     Int          i, iek, ig, ick;
     Real        *rbuf;
     Int         *ibuf;
     Int          npe[MxNEk];


     *fle >> *nx;
     *fle >> *gnp;
     *gxp= new Real[(*nx)*(*gnp)];
      for( i=0;i<(*gnp)*(*nx);i++ )
     {
       *fle >> (*gxp)[i];
     }

      for(iek=0; iek<MxNEk; iek++)
     {
         *fle >> gne[iek];
         *fle >> npe[iek];
         if( gne[iek] > 0 )
        {
            cout << "RETRIEVED "<<gne[iek]<<" "<<npe[iek]<<"-NODED ELEMENTS FROM THE JM28 GRID\n";
            giep[iek]= new Int[ npe[iek]*gne[iek] ];
            for( i=0;i<npe[iek]*gne[iek];i++ )
           {
               *fle >> giep[iek][i];
           }
        }
     }

     *fle >> *ng;
     for(ig=0; ig<*ng; ig++)
    {
        *fle >> bgnm[ig];
        for(ick=0; ick<MxNCk; ick++)
       {
          *fle >> gnb[ig][ick];
           if( gnb[ig][ick] > 0 )
          {
             cout << "RETRIEVED "<<gnb[ig][ick]<<" "<<npc[ick]<<"-NODED ELEMENTS FROM THE JM28 GRID FOR GROUP "<<ig<<"\n";
             gibp[ig][ick]= new Int[ npc[ick]*gnb[ig][ick] ];
             for( i=0;i<npc[ick]*gnb[ig][ick];i++ )
            {
                 *fle >> gibp[ig][ick][i];
            }
         }
       }
    }
  }
