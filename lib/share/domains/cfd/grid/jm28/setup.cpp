   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdjm28.h>
#  include <cassert>

// boundary groups
      const Int cnb = 27;
      string btyp[cnb]={"NEUT","RIGH","LEFT","FREE","FREX","INLE","EXIT","HUB","CASI","BLAD","PLTFM","INJ", "INVI", 
                        "SPIN", "SPEX", "WIN", "WEX", "MIN", "MEX", "MINC", "MEXC", "SP2D", "WAVE",
                        "NRIN", "NREX", "NRMI", "NRME"};
      string bpl[cnb]={"none",
                      "none",
                      "none",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so" };
      string bpo[cnb]={"none",
                      "none",
                      "none",
                      "unibcs",
                      "unibcs",
                      "q263bc",
                      "q263bc",
                      "adiabcs",
                      "adiabcs",
                      "adiabcs",
                      "adiabcs",
                      "unibcs" ,
                      "adiabcs",
                      "q263bc",
                      "q263bc",
                      "q263bc",
                      "q263bc",
                      "mixbc",
                      "mixbc",
                      "mixbc",
                      "mixbc",
                      "unibcs",
                      "mixbc",
                      "q263bc",
                      "q263bc",
                      "mixbc",
                      "mixbc" };
      Int kbk[cnb]={ neut_fbndry,neut_fbndry,neut_fbndry,free_fbndry,free_fbndry_x,
                    free_fbndry,free_fbndry, visc_fbndry,visc_fbndry,
                    visc_fbndry,visc_fbndry,inj_fbndry, inv_fbndry,
                    slide_fbndry,slide_fbndry, free_fbndry_wake, free_fbndry_wake, free_fbndry, free_fbndry,
                    free_fbndry, free_fbndry, slide_fbndry, wave_fbndry, free_fbndry, free_fbndry, free_fbndry, free_fbndry };

   void cCfdJm28::setup( cDom* dev, string fnme, cFdDomain **dmn )
  {      
      Int     nw=0;
      Int     kb;
      Int     jb;
      string  wrk;

      Int            im;
      string devnm ,cpath;

     *dmn= new cFdDomain();

// default element kinds from FDNEUT

// assign element kinds
      cout << "will open file "<<fnme<<"\n";
     
      cpath = dev->getcpath();
      devnm = dev->getname();

      imagebin(fnme,devnm);
     

//    subv( nx,np, rwrk,xp );
      mnms= new string[ng];
      blbl= new string[ng];
      bnms= new string[ng];
      blbs= new string[ng];
      bbjs= new string[ng];
      bbj=  new cFbndry*[ng];

      nek=0;
      nbk=0;
      if(nx==2)
     {
        elems2(*dmn );
     }
      else if(nx==3)
     {
        elems3(*dmn );
     }

      nms( &nm,mnms );


      nmsb( &mb,bnms );

      for( im=0;im<mb;im++ )
     {
         blbs[im]= bpl[im];
         bbjs[im]= bpo[im];
         blbl[im]= bnms[im];
     }

      json_parse_bnd(cpath, devnm);

    (*dmn)->boundaries( mb,blbl,blbs,bbjs );
    (*dmn)->bndries( bbj );

  }
