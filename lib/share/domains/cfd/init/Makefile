include ../../../../Makefile.in

CSRC=   uni.cpp \
	binrestart.cpp \
	q263.cpp \
	fullannlinit.cpp \
	profile.cpp

FSRC=
COBJ=$(CSRC:.cpp=.o)
FOBJ=$(FSRC:.F=.o)
OBJS=$(COBJ) $(FOBJ)

BINS=libinit.so

$(BINS): $(OBJS)
	$(PCCMP) $(COPT) $(OBJS) $(ORGL) $(ENGL) $(SYSL) $(LIBF) -shared -o $@

clean:
	rm -f $(OBJS) $(BINS)

.cpp.o:
	$(PCCMP) $(COPT) -I./ $(ORGI) $(ENGI) $(SYSI) $(PETSCI) -fPIC -o $@ -c $<

.F.o:
	$(PFCMP) $(FOPT) -I./ $(ORGI) $(ENGI) $(SYSI) $(GUII)  -o $@ -c $<

