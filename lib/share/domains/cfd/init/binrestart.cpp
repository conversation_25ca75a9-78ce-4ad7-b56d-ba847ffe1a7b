   using namespace std;

#  include <domain/cfd/domain.h>

   extern "C" void binrestartinit( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, cAu3xView<Real>& xq, cAu3xView<Real>& q, Real *tm,
                                   Real *su[NTLEV], bool *bsu, Int naux, Real *cfl, Int *idone, Int nfre, 
                                   Real *sz_re[], Real *sz_im[] )
  {
      cDevice     *dev;
      cPdata      *dof,*pts;
      pickle_t     buf;
      size_t       len,l;
      string       fnme;
      Int          icpu,ilev;
      Int          i,j, il, ih, iv, iq;
      FILE        *f;
      Real        *sxpl=NULL,*sxql=NULL,*sql=NULL,*sauxl=NULL, *sul[NTLEV];
      cAu3xView<Real> xpl,xql,ql,auxl;
      cField      *fld;
      Real rdum;
      Int          tmpntlv;
      cAu3xView<Real> ul, u; 
      Real        tmpcfl;
      Real        *szl_re=NULL, *szl_im=NULL;
      cAu3xView<Real> zl_re, zl_im, z_re, z_im;


      for(il=0; il<NTLEV; il++)
     {
         sul[il]=NULL;
     }

      dev= dom->device();
      ilev= dom->level();
      fld= dom->field();

      fnme= dev->getcpath();
      icpu= dev->getrank();
      fnme= fnme+"/"+dev->getname()+".restart.q."+strc(ilev)+"."+strc(icpu);
      //fnme= "./restart/"+dev->getname()+".restart.q."+strc(ilev)+"."+strc(icpu);
      f= fopen(fnme.c_str(),"r");
      cout << "open restart file " << fnme << "\n";

      l= fread( tm,  1,sizeof(rdum),f );
      l= fread( &len,1,sizeof(len),f );
      buf= new pickle_v[len];
      cout << "len " << len << "\n";
      l= fread(  buf,1,        len,f );

      len=0;
      dof= new cPdata( dev );
      pts= new cPdata( dev );

      dof->unpickle( &len,buf );
      pts->unpickle( &len,buf );
      delete[] buf;buf= NULL; len=0;


      pts->read( nx,   &sxpl,f );
      dof->read( nx,   &sxql,f );
      dof->read( nv,    &sql,f );
      dof->read( naux,&sauxl,f );
      l= fread( &tmpntlv,  1,sizeof(tmpntlv),f );
      for(il=0; il<tmpntlv; il++)
     {
         dof->read( nv, &(sul[il]),f );
     }
      l= fread( &tmpcfl,  1,sizeof(rdum),f );
      fclose(f);

      cout << "restart from physical time " << *tm << " with cfl " << *cfl << "\n";

      //ql= new Real*[nv];
      
      ql.subv( nv,nq, sql );
      fld->redim( 0,nq, ql );

      for( j=0;j<nv;j++ )
     {
         for( i=0;i<nq;i++ )
        {
            q(j,i)= ql(j,i);
        }
     }

      //DO NOT NORMALIZE U HERE!
      for(il=0; il<tmpntlv; il++)
     {
         ul.subv( nv,nq, sul[il] );
         u.subv( nv,nq, su[il] );
         for( j=0;j<nv;j++ )
        {
            for( i=0;i<nq;i++ )
           {
               u(j,i)= ul(j,i);
           }
        }
     }
      *bsu = true;

     /* for(il=0; il<tmpntlv; il++)
     {
        for( i=0;i<nq;i++ )
       {
          for( j=0;j<nv;j++ )
         {
           cout << u[j][i] << " ";
         }
          cout << "\n";
       }
     }*/

      pts->destroy(   &sxpl );
      dof->destroy(   &sxql );
      dof->destroy(    &sql );
      dof->destroy(  &sauxl );
      for(il=0; il<tmpntlv; il++)
     {
         dof->destroy(  &(sul[il]) );
     }
      delete pts; pts=NULL;
      delete dof; dof=NULL;
      //delete[] ql;

      if(nfre>0)
     {
         //restart fourier solutions
         fnme= dev->getcpath();
         icpu= dev->getrank();
         fnme= fnme+"/"+dev->getname()+".restart.z."+strc(ilev)+"."+strc(icpu);
         f= fopen(fnme.c_str(),"r");

         if(f)
        {
            cout << "open restart file " << fnme << "\n";
   
            l= fread( &len,1,sizeof(len),f );
            buf= new pickle_v[len];
            l= fread(  buf,1,        len,f );
  
            len=0;
            dof= new cPdata( dev );
            pts= new cPdata( dev );
  
            dof->unpickle( &len,buf );
            pts->unpickle( &len,buf );
            delete[] buf;buf= NULL; len=0;
     
            for(ih=0; ih<nfre; ih++)
           {
               dof->read( nv,    &szl_re,f );
               dof->read( nv,    &szl_im,f );
   
               zl_re.subv( nv,nq, szl_re );
               zl_im.subv( nv,nq, szl_im );
               fld->redim( 0,nq, zl_re );
               fld->redim( 0,nq, zl_im );
     
               z_re.subv( nv,nq, sz_re[ih] );
               z_im.subv( nv,nq, sz_im[ih] );
     
               for( iv=0;iv<nv;iv++ )
              {
                  for( iq=0;iq<nq;iq++ )
                 {
                     z_re(iv,iq)= zl_re(iv,iq);
                     z_im(iv,iq)= zl_im(iv,iq);
                 }
              }
               dof->destroy(    &szl_re );
               dof->destroy(    &szl_im );
           }
            fclose(f);
            delete pts; pts=NULL;
            delete dof; dof=NULL;
        }
     }
  }

   extern "C" void binrestartinit2( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, cAu3xView<Real>& xq, cAu3xView<Real>& q, Real *tm,
                                   Real *su[NTLEV], bool *bsu, Int naux, Real *cfl, Int *idone, Int nfre, 
                                   Real *sz_re[], Real *sz_im[] )
  {
      cDevice     *dev;
      cPdata      *dof,*pts;
      pickle_t     buf;
      size_t       len,l;
      string       fnme;
      Int          icpu,ilev;
      Int          i,j, il;
      FILE        *f;
      Real        *sxpl=NULL,*sxql=NULL,*sql=NULL,*sauxl=NULL, *sul[NTLEV];
      cAu3xView<Real> xpl,xql,ql,auxl;
      cField      *fld;
      Real rdum;
      Int          tmpntlv;
      cAu3xView<Real> ul, u;

      for(il=0; il<NTLEV; il++)
     {
         sul[il]=NULL;
     }

      dev= dom->device();
      ilev= dom->level();
      fld= dom->field();

      fnme= dev->getcpath();
      icpu= dev->getrank();
      fnme= fnme+"/"+dev->getname()+".restart.q."+strc(ilev)+"."+strc(icpu);
      //fnme= "./restart/"+dev->getname()+".restart.q."+strc(ilev)+"."+strc(icpu);
      f= fopen(fnme.c_str(),"r");
      cout << "open restart file " << fnme << "\n";

      l= fread( tm,  1,sizeof(rdum),f );
      l= fread( &len,1,sizeof(len),f );
      buf= new pickle_v[len];
      cout << "len " << len << "\n";
      l= fread(  buf,1,        len,f );

      len=0;
      dof= new cPdata( dev );
      pts= new cPdata( dev );

      dof->unpickle( &len,buf );
      pts->unpickle( &len,buf );
      delete[] buf;buf= NULL; len=0;


      pts->read( nx,   &sxpl,f );
      dof->read( nx,   &sxql,f );
      dof->read( nv,    &sql,f );
      dof->read( naux,&sauxl,f );
      l= fread( &tmpntlv,  1,sizeof(tmpntlv),f );
      for(il=0; il<tmpntlv; il++)
     {
         dof->read( nv, &(sul[il]),f );
     }
      l= fread( cfl,  1,sizeof(rdum),f );
      fclose(f);

      cout << "restart from physical time " << *tm << " with cfl " << *cfl << "\n";

      //ql= new Real*[nv];
      
      ql.subv( nv,nq, sql );
      fld->redim( 0,nq, ql );

      for( j=0;j<nv;j++ )
     {
         for( i=0;i<nq;i++ )
        {
            q(j,i)= ql(j,i);
        }
     }

      //DO NOT NORMALIZE U HERE!
      for(il=0; il<tmpntlv; il++)
     {
         ul.subv( nv,nq, sul[il] );
         u.subv( nv,nq, su[il] );
         for( j=0;j<nv;j++ )
        {
            for( i=0;i<nq;i++ )
           {
               u(j,i)= ul(j,i);
           }
        }
     }
      *bsu = true;

     /* for(il=0; il<tmpntlv; il++)
     {
        for( i=0;i<nq;i++ )
       {
          for( j=0;j<nv;j++ )
         {
           cout << u[j][i] << " ";
         }
          cout << "\n";
       }
     }*/

      pts->destroy(   &sxpl );
      dof->destroy(   &sxql );
      dof->destroy(    &sql );
      dof->destroy(  &sauxl );
      for(il=0; il<tmpntlv; il++)
     {
         dof->destroy(  &(sul[il]) );
     }
      delete pts; pts=NULL;
      delete dof; dof=NULL;
//      delete[] ql;

  }

   extern "C" void binrestartinit3( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, cAu3xView<Real>& xq, cAu3xView<Real>& q, Real *tm,
                                   Real *su[NTLEV], bool *bsu, Int naux, Real *cfl, Int *idone, Int nfre, 
                                   Real *sz_re[], Real *sz_im[] )
  {
      cDevice     *dev;
      cPdata      *dof,*pts;
      pickle_t     buf;
      size_t       len,l;
      string       fnme;
      Int          icpu,ilev;
      Int          i,j, il;
      FILE        *f;
      Real        *sxpl=NULL,*sxql=NULL,*sql=NULL,*sauxl=NULL, *sul[NTLEV];
      cAu3xView<Real> xpl,xql,ql,auxl;
      cField      *fld;
      Real rdum;
      Int          tmpntlv;
      cAu3xView<Real> ul, u; 
      Real tmpcfl;

      for(il=0; il<NTLEV; il++)
     {
         sul[il]=NULL;
     }

      dev= dom->device();
      ilev= dom->level();
      fld= dom->field();

      fnme= dev->getcpath();
      icpu= dev->getrank();
      fnme= fnme+"/"+dev->getname()+".restart.q."+strc(ilev)+"."+strc(icpu);
      //fnme= "./restart/"+dev->getname()+".restart.q."+strc(ilev)+"."+strc(icpu);
      f= fopen(fnme.c_str(),"r");
      cout << "open restart file " << fnme << "\n";

      l= fread( tm,  1,sizeof(rdum),f );
      l= fread( &len,1,sizeof(len),f );
      buf= new pickle_v[len];
      cout << "len " << len << "\n";
      l= fread(  buf,1,        len,f );

      len=0;
      dof= new cPdata( dev );
      pts= new cPdata( dev );

     dof->unpickle( &len,buf );
      pts->unpickle( &len,buf );
      delete[] buf;buf= NULL; len=0;


      pts->read( nx,   &sxpl,f );
      dof->read( nx,   &sxql,f );
      dof->read( nv,    &sql,f );
      fclose(f);

      //ql= new Real*[nv];

      ql.subv( nv,nq, sql );
      fld->redim( 0,nq, ql );

      for( j=0;j<nv;j++ )
     {
         for( i=0;i<nq;i++ )
        {
            q(j,i)= ql(j,i);
        }
     }

      pts->destroy(   &sxpl );
      dof->destroy(   &sxql );
      dof->destroy(    &sql );
      delete pts; pts=NULL;
      delete dof; dof=NULL;
//      delete[] ql;

  }
                                         
