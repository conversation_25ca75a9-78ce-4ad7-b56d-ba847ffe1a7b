   using namespace std;

#  include <domain/cfd/domain.h>

   bool readsolutionbin( string fnm, Int *nx, Int *nv, Int *nq, Int *naux,
                      Real *xq[3], Real *q[20], Real *aux[20], Int *ng, 
                      string *bgnm, Int *nbb, Real *xb[20][3], Real *qb[20][20],
                      Real *auxb[20][20]);

   extern "C" void spsginit( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, cAu3xView<Real>& xq, cAu3xView<Real>& q, Real *tm,
                             Real *su[], bool *bsu, Int naux, Real *cfl, Int *idone )
  {
      Int iv,iq, ix, ib, iq0, jq;
      cCosystem *coo;
      cDevice *dev;
      string cpath, devnm, fnm;
      bool btmp;
      Int tmpnx, tmpnv, tmpnq, tmpng, tmpnaux, tmpnbb[20], npsg, *infected[1], asct, gsct;
      Real *tmpxq[3], *tmpq[20], *tmpaux[20],*tmpxb[20][3], *tmpqb[20][20], *tmpauxb[20][20];
      string tmpbgnm[20];
      Real *tmpxrt[3], ptch;
      ifstream fle;
      Real y[3], y0[3], y1[3], dl[3], d;
      cField      *fld;
   
      cout << "initialize multi-passage with a single passage solution\n";

      *tm = 0;
 
      dev= dom->device();
      devnm = dev->getname();
      cpath= dev->getcpath();
      fnm= cpath+ "/" + devnm + ".0" + ".single.solution.bin";

      coo= dom->cosystem();

      //read single passage solutions
      btmp = readsolutionbin( fnm, &tmpnx, &tmpnv, &tmpnq, &tmpnaux, tmpxq, tmpq, tmpaux, &tmpng, 
                              tmpbgnm, tmpnbb, tmpxb, tmpqb, tmpauxb );
      if(!btmp) 
     {
         cout << "Error: can not read the single passage solution\n";
         return;      
     }

      cTabData *tab;
      string syn=";()";
      Int    iarg=0,argc=0;
      string *argv=NULL;

      tab= new cTabData();
      dev->get( tab );
      string misc;
      tab->get( "misc",&misc );
      parse( misc,&argc,&argv,syn );
      iarg=inlst( string("assembly-sectors"),argc,argv );
      if( iarg != -1 )
     {
         conv( argv[iarg+1],&asct );
     }
      iarg=inlst( string("grid-sectors"),argc,argv );
      if( iarg != -1 )
     {
         conv( argv[iarg+1],&gsct );
     }
      delete tab; tab=NULL;



      fld= dom->field();

      fnm= "./nb/" + devnm + ".dat";
      fle.open(fnm.c_str());
      fle >> npsg;
      fle.close();
      cout << "number of passage is " << npsg << "\n";
      ptch = (Real)gsct*pi2/((Real)asct*npsg);

      cout << "get here0\n";
      //convert coordinates into cylindrical coordinates
      tmpxrt[0] = new Real [tmpnq]; 
      tmpxrt[1] = new Real [tmpnq]; 
      tmpxrt[2] = new Real [tmpnq]; 
     
      infected[0] = new Int [tmpnq];
      for(iq=0; iq<tmpnq; iq++) infected[0][iq] = iq;
      coo->bcoor( 0,tmpnq, infected, tmpxq, tmpxrt );
      coo->bvel(  0,tmpnq, infected, tmpxq, tmpq, tmpq );
      delete[] infected[0]; infected[0]=NULL;
      for(iq=0; iq<tmpnq; iq++)
     {
        if(tmpxrt[2][iq]<0.) tmpxrt[2][iq]+=pi2;
     }

     /* ofstream ofle;
      ofle.open("single_qx.dat");
      for(iq=0; iq<tmpnq; iq++)
     {
        //ofle << tmpxrt[0][iq] << " " << tmpxrt[1][iq] << " " << tmpxrt[2][iq] << "\n";
        ofle << tmpxq[0][iq] << " " << tmpxq[1][iq] << " " << tmpxq[2][iq] << "\n";
     }
      ofle.close();*/

      cout << "get here1 " << tmpnq << " " << nq << "\n";

      Int ntmp=0;
      for(iq=0; iq<nq; iq++)
     {
         Real dt, t;

         y[0] = xq(0,iq);
         y[1] = xq(1,iq);
         y[2] = xq(2,iq);
         y0[0] = y[0];                         //x
         y0[1] = sqrt(y[1]*y[1] + y[2]*y[2]);  //r
         y0[2] = atan2(y[1], y[2]);            //theta
         if(y0[2]<0.) y0[2]+=pi2;

         for(jq=0; jq<tmpnq; jq++)
        {
            y1[0] = tmpxrt[0][jq];    
            y1[1] = tmpxrt[1][jq];    
            y1[2] = tmpxrt[2][jq];    
      
            //if two points match, they must have the same x-r coordinates the difference of the theta coordinates 
            //must be a multiple of pitch
  
            bool btmp;
            btmp = false; 
            if(fabs(y0[0]-y1[0])<1e-9) 
           {
               //same x
               if(fabs(y0[1]-y1[1])<1e-9) 
              {
                 //same r
                 for(ib=0; ib<npsg; ib++)
                {
                    t = y1[2] + ib*ptch;
                    if(t>pi2) t = t - pi2;
                    dt = fabs(y0[2] - t);
                    //cout << dt << "\n";
                    if(dt<1e-9)
                   {
                       //the difference in theta is a multiple of pitch then this two points match
                       btmp = true;
                       break;
                   }
                }
              }
           }

            if(btmp)
           {
               for(iv=0; iv<nv; iv++)
              {
                  q(iv,iq) = tmpq[iv][jq];
              }
               //cout << q[0][iq] << " " << q[1][iq] << " " << q[2][iq] << " " << q[3][iq]  << " "
               //     << q[4][iq] << " " << q[5][iq] << "\n";
               break;  
           }
        }
         if(jq==tmpnq)
        {
            ntmp++;
        }
     }
      cout << "get here2 \n";
      fld->redim( 0,nq, q );
      coo->zvel( 0,nq, NULL, xq, q,q );
      cout << ntmp << " elements are left to initialize in the single passage initializer for device "<<devnm << "\n";

      //use all variables of the single passage solution
      for(iv=0; iv<nv; iv++) idone[iv] = 1;

      *bsu = false;

      for(Int ig=0; ig<tmpng; ig++)
     {
         for(ix=0; ix<tmpnx; ix++)
        {
            delete[] tmpxb[ig][ix]; tmpxb[ig][ix]=NULL;
        }
         for(iv=0; iv<tmpnv; iv++)
        {
            delete[] tmpqb[ig][iv]; tmpqb[ig][iv]=NULL;
        }
         for(iv=0; iv<2; iv++)
        {
            delete[] tmpauxb[ig][iv]; tmpauxb[ig][iv]=NULL;
        }
     }
      for(ix=0; ix<tmpnx; ix++)   delete[] tmpxq[ix];  tmpxq[ix]=NULL;
      for(iv=0; iv<tmpnv; iv++)   delete[] tmpq[iv];   tmpq[iv]=NULL;
      for(iv=0; iv<tmpnaux; iv++) delete[] tmpaux[iv]; tmpaux[iv]=NULL;

      delete[] tmpxrt[0]; tmpxrt[0]=NULL;
      delete[] tmpxrt[1]; tmpxrt[1]=NULL;
      delete[] tmpxrt[2]; tmpxrt[2]=NULL;
  }

   bool readsolutionbin( string fnm, Int *nx, Int *nv, Int *nq, Int *naux,
                         Real *xq[3], Real *q[20], Real *aux[20], Int *ng, 
                         string *bgnm,Int *nbb,Real *xb[20][3],Real *qb[20][20],
                         Real *auxb[20][20])
  {
      FILE *f;
      Int idum, ib, ix, iv, ig, iq;
      Real rdum;
      string sdum;
      Real *sxq,*sq,*saux; 
      Real *tmpxq[3], *tmpq[20], *tmpaux[20];
      Real *sxb[20], *sqb[20], *sauxb[20];
      Real *tmpxb[20][3], *tmpqb[20][20], *tmpauxb[20][20];
      size_t i;

      f= fopen(fnm.c_str(),"r");
      i = fread( nx,1,  sizeof(idum),f );
      i = fread( nv,1,  sizeof(idum),f );
      i = fread( nq,1,  sizeof(idum),f );
      i = fread( naux,1,sizeof(idum),f );

      sxq = new Real [(*nx)*(*nq)];
      i = fread( sxq, (*nx)*(*nq),   sizeof(rdum),f );

      sq = new Real [(*nv)*(*nq)];
      i = fread( sq,  (*nv)*(*nq),   sizeof(rdum),f );

      saux = new Real [(*naux)*(*nq)];
      i = fread( saux, (*naux)*(*nq), sizeof(rdum),f );

      i = fread( ng  ,   1, sizeof(idum),f );
     //i = fread( bgnm, *ng, sizeof(sdum), f);
      i = fread( nbb,  *ng, sizeof(idum), f);
      for(ig=0; ig<*ng; ig++)
     {
         sxb[ig] = new Real [(*nx)*nbb[ig]];        
         i = fread( sxb[ig],   (*nx)*nbb[ig], sizeof(rdum), f);

         sqb[ig] = new Real [(*nv)*nbb[ig]];        
         i = fread( sqb[ig],   (*nv)*nbb[ig], sizeof(rdum), f);

         sauxb[ig] = new Real [2*nbb[ig]];        
         i = fread( sauxb[ig],  2*nbb[ig], sizeof(rdum), f);
     }
      fclose(f);

      subv(   *nx,*nq,   sxq, tmpxq );
      subv(   *nv,*nq,   sq,  tmpq );
      subv( *naux,*nq, saux,  tmpaux );
      for(ig=0; ig<*ng; ig++)
     {
        subv( *nx,  nbb[ig], sxb[ig],    tmpxb[ig] );
        subv( *nv,  nbb[ig], sqb[ig],    tmpqb[ig] );
        subv( 2,    nbb[ig], sauxb[ig],  tmpauxb[ig] );
     }

    

      cout << "nx " << *nx << "\n";
      cout << "nv " << *nv << "\n";
      cout << "nq " << *nq << "\n";
      cout << "naux " << *naux << "\n";
      for(ig=0; ig<*ng; ig++)
     {
         cout << ig << " " << bgnm[ig] << " " << nbb[ig] << "\n";
     }

      for(ix=0; ix<*nx; ix++)  
     {
        xq[ix] = new Real [*nq];
        for(iq=0; iq<*nq; iq++)
       {
          xq[ix][iq] = tmpxq[ix][iq];
       }
     }
      for(iv=0; iv<*nv; iv++)  
     {
        q[iv] = new Real [*nq];
        for(iq=0; iq<*nq; iq++)
       {
          q[iv][iq] = tmpq[iv][iq];
       }
     }
      for(iv=0; iv<*naux; iv++) 
     {
        aux[iv]  = new Real [*nq];
        for(iq=0; iq<*nq; iq++)
       {
          aux[iv][iq] = tmpaux[iv][iq];
       }
     }


      for(ig=0; ig<*ng; ig++)
     {
        for(ix=0; ix<*nx; ix++)
       {
          xb[ig][ix] = new Real [nbb[ig]];
          for(ib=0; ib<nbb[ig]; ib++)
         {
            xb[ig][ix][ib] = tmpxb[ig][ix][ib];
         }
       }
        for(iv=0; iv<*nv; iv++)
       {
          qb[ig][iv] = new Real [nbb[ig]];
          for(ib=0; ib<nbb[ig]; ib++)
         {
            qb[ig][iv][ib] = tmpqb[ig][iv][ib];
         }
       }
        for(iv=0; iv<2; iv++)
       {
          auxb[ig][iv] = new Real [nbb[ig]];
          for(ib=0; ib<nbb[ig]; ib++)
         {
            auxb[ig][iv][ib] = tmpauxb[ig][iv][ib];
         }
       }
     }

      delete[] sxq; sxq=NULL;
      delete[] sq; sq=NULL;
      delete[] saux; saux=NULL;
      for(ig=0; ig<*ng; ig++)
     {
         delete[] sxb[ig]; sxb[ig]=NULL;
         delete[] sqb[ig]; sqb[ig]=NULL;
         delete[] sauxb[ig]; sauxb[ig]=NULL;
     }

      return true;
  }
