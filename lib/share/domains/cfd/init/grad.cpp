
   extern "C" void gradinit( cDevice *dev, cFeature *rftr, cBookEntry *rvrs, cCase *rcse, string bname,
                             Int *nx, Int *nv, Int *nq, Real *xq[], Real *q[], Int *idone )                                              
  {
      Int iv,iq;
      Real x,x2;
      for( iq=0;iq<*nq;iq++ )
     {
         x= xq[0][iq];
         x2= x*x;
         for( iv=0;iv<*nx;iv++ )
        {
            q[iv][iq]= x;
        }
         iv= *nx;
         q[iv++][iq]= 1000+ x;
         q[iv++][iq]= 100000+ 100*x;
         for( iv=(*nx)+2;iv<*nv;iv++ )
        {
            q[iv][iq]= 0;
        }
     }

      for( iv=0;iv<*nv;iv++ )
     {
         idone[iv]= 1;
     }
   }
