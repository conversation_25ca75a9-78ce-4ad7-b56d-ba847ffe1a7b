   bool readsolutionbin( string fnm, Int *nx, Int *nv, Int *nq, Int *naux,
                         Real *xq[3], Real *q[20], Real *aux[20], Int *ng, 
                         string *bgnm,Int *nbb,Real *xb[20][3],Real *qb[20][20],
                         Real *auxb[20][20])
  {
      FILE *f;
      Int idum, ib, ix, iv, ig, iq;
      Real rdum;
      string sdum;
      Real *sxq,*sq,*saux; 
      Real *tmpxq[3], *tmpq[20], *tmpaux[20];
      Real *sxb[20], *sqb[20], *sauxb[20];
      Real *tmpxb[20][3], *tmpqb[20][20], *tmpauxb[20][20];
      size_t i;

      f= fopen(fnm.c_str(),"r");
      i = fread( nx,1,  sizeof(idum),f );
      i = fread( nv,1,  sizeof(idum),f );
      i = fread( nq,1,  sizeof(idum),f );
      i = fread( naux,1,sizeof(idum),f );

      sxq = new Real [(*nx)*(*nq)];
      i = fread( sxq, (*nx)*(*nq),   sizeof(rdum),f );

      sq = new Real [(*nv)*(*nq)];
      i = fread( sq,  (*nv)*(*nq),   sizeof(rdum),f );

      saux = new Real [(*naux)*(*nq)];
      i = fread( saux, (*naux)*(*nq), sizeof(rdum),f );

      i = fread( ng  ,   1, sizeof(idum),f );
     //i = fread( bgnm, *ng, sizeof(sdum), f);
      i = fread( nbb,  *ng, sizeof(idum), f);
      for(ig=0; ig<*ng; ig++)
     {
         sxb[ig] = new Real [(*nx)*nbb[ig]];        
         i = fread( sxb[ig],   (*nx)*nbb[ig], sizeof(rdum), f);

         sqb[ig] = new Real [(*nv)*nbb[ig]];        
         i = fread( sqb[ig],   (*nv)*nbb[ig], sizeof(rdum), f);

         sauxb[ig] = new Real [2*nbb[ig]];        
         i = fread( sauxb[ig],  2*nbb[ig], sizeof(rdum), f);
     }
      fclose(f);

      subv(   *nx,*nq,   sxq, tmpxq );
      subv(   *nv,*nq,   sq,  tmpq );
      subv( *naux,*nq, saux,  tmpaux );
      for(ig=0; ig<*ng; ig++)
     {
        subv( *nx,  nbb[ig], sxb[ig],    tmpxb[ig] );
        subv( *nv,  nbb[ig], sqb[ig],    tmpqb[ig] );
        subv( 2,    nbb[ig], sauxb[ig],  tmpauxb[ig] );
     }

    

      cout << "nx " << *nx << "\n";
      cout << "nv " << *nv << "\n";
      cout << "nq " << *nq << "\n";
      cout << "naux " << *naux << "\n";
      for(ig=0; ig<*ng; ig++)
     {
         cout << ig << " " << bgnm[ig] << " " << nbb[ig] << "\n";
     }

      for(ix=0; ix<*nx; ix++)  
     {
        xq[ix] = new Real [*nq];
        for(iq=0; iq<*nq; iq++)
       {
          xq[ix][iq] = tmpxq[ix][iq];
       }
     }
      for(iv=0; iv<*nv; iv++)  
     {
        q[iv] = new Real [*nq];
        for(iq=0; iq<*nq; iq++)
       {
          q[iv][iq] = tmpq[iv][iq];
       }
     }
      for(iv=0; iv<*naux; iv++) 
     {
        aux[iv]  = new Real [*nq];
        for(iq=0; iq<*nq; iq++)
       {
          aux[iv][iq] = tmpaux[iv][iq];
       }
     }


      for(ig=0; ig<*ng; ig++)
     {
        for(ix=0; ix<*nx; ix++)
       {
          xb[ig][ix] = new Real [nbb[ig]];
          for(ib=0; ib<nbb[ig]; ib++)
         {
            xb[ig][ix][ib] = tmpxb[ig][ix][ib];
         }
       }
        for(iv=0; iv<*nv; iv++)
       {
          qb[ig][iv] = new Real [nbb[ig]];
          for(ib=0; ib<nbb[ig]; ib++)
         {
            qb[ig][iv][ib] = tmpqb[ig][iv][ib];
         }
       }
        for(iv=0; iv<2; iv++)
       {
          auxb[ig][iv] = new Real [nbb[ig]];
          for(ib=0; ib<nbb[ig]; ib++)
         {
            auxb[ig][iv][ib] = tmpauxb[ig][iv][ib];
         }
       }
     }

      delete[] sxq; sxq=NULL;
      delete[] sq; sq=NULL;
      delete[] saux; saux=NULL;
      for(ig=0; ig<*ng; ig++)
     {
         delete[] sxb[ig]; sxb[ig]=NULL;
         delete[] sqb[ig]; sqb[ig]=NULL;
         delete[] sauxb[ig]; sauxb[ig]=NULL;
     }

      return true;
  }
