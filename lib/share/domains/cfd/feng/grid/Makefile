include ../../../../Makefile.in

CSRC=   grid.cpp \
	dof.cpp \
	bdof.cpp \
	fdneut/cfdfdneut.cpp \
	fdneut/elems2.cpp \
	fdneut/elems3.cpp \
	fdneut/setup.cpp \
	fdneut/setupf.cpp \
	fdneut/fdneut.cpp \
	fdneut/image.cpp \
	fdneut/load.cpp \
	fdneut/gather.cpp \
	fdneut/bgather.cpp \
	fdneut/nms.cpp \
	au3x/cfdau3x.cpp \
	au3x/elems2.cpp \
	au3x/elems3.cpp \
	au3x/setup.cpp \
	au3x/setupf.cpp \
	au3x/au3x.cpp \
	au3x/nms.cpp \
	au3x/image.cpp \
	au3x/load.cpp \
	jm28/cfdjm28.cpp \
	jm28/elems2.cpp \
	jm28/elems3.cpp \
	jm28/setup.cpp \
	jm28/setupf.cpp \
	jm28/jm28.cpp \
	jm28/nms.cpp \
	jm28/image.cpp \
	jm28/load.cpp \
	au3d/cfdau3d.cpp \
	au3d/elems2.cpp \
	au3d/elems3.cpp \
	au3d/setup.cpp \
	au3d/setupf.cpp \
	au3d/au3d.cpp \
	au3d/nms.cpp \
	au3d/image.cpp \
	au3d/cutils.cpp \
	au3d/load.cpp
#dntprd.cpp

FSRC= au3d/gridutils.F \
      au3d/futils.F


COBJ=$(CSRC:.cpp=.o)
FOBJ=$(FSRC:.F=.o)
OBJS=$(COBJ) $(FOBJ)

BINS=libfdneut.so

$(BINS): $(OBJS)
	$(PCCMP) $(COPT) $(OBJS) $(ORGL) $(ENGL) $(SYSL) $(LIBF) -shared -o $@

clean:
	rm -f $(OBJS) $(BINS)

.cpp.o:
	$(PCCMP) $(COPT) -Wunused -I./ $(ORGI) $(ENGI) $(SYSI) $(HYPREI) -fPIC -o $@ -c $<

.F.o:
	$(PFCMP) $(FOPT) -I./ $(ORGI) $(ENGI) $(SYSI) $(GUII)  -o $@ -c $<

