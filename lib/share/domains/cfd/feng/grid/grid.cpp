   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdgrid.h>

   void cCfdGrid::blank()
  {      
      Int           ig;
      ug=  NULL;
      ihlp=NULL;
      bhlp=NULL;
      neg=NULL;
      npg=NULL;
      gnms=NULL;
      blbl=NULL;
      blbs=NULL;
      bbjs=NULL;

      bnms=NULL;
      mnms=NULL;

      sxp= NULL;
      sxq= NULL;
      setv( (Int)0,(Int)MxNSk,(Int)0,   ne );
      setv( (Int)0,(Int)MxNSk,(Int*)NULL,siep );
      setv( (Int)0,(Int)MxNSk,(Int*)NULL,sieq );
      setv( (Int)0,(Int)MxNSk,(Int*)NULL,siem );

      setv( (Int)0,(Int)MxNBG,(Real*)NULL,sxb  );
      setv( (Int)0,(Int)MxNBG,(Real*)NULL,sxqb  );
      setv( (Int)0,(Int)MxNBG,(Int)0,nbb  );
      for( ig=0;ig<MxNBG;ig++ )
     {
         setv( (Int)0,(Int)MxNSk,(Int)0,   nb[ig]   );
         setv( (Int)0,(Int)MxNSk,(Int*)NULL,sibp[ig] );
         setv( (Int)0,(Int)MxNSk,(Int*)NULL,sibq[ig] );
         setv( (Int)0,(Int)MxNSk,(Int*)NULL,sibb[ig] );
     }

      setv( (Int)0,(Int)MxNSk, (Int)0, ne );
   
      setv( (Int)0,(Int)MxNSk,(Int*)NULL, nce );
      setv( (Int)0,(Int)MxNSk,(Int***)NULL, icep );
      setv( (Int)0,(Int)MxNSk,(Int***)NULL, iceq );
  }

   cCfdGrid::cCfdGrid()
  {      
      blank();
  }

   cCfdGrid::~cCfdGrid()
  {
      Int iek,ick;;
      delete[] neg;
      delete[] npg;

      delete[] ihlp;
      delete[] bnms;
      delete[] blbs;
      delete[] blbl;
      delete[] bbjs;
      delete[] gnms;
      delete[] mnms;
      for( ick=0;ick<MxNSk;ick++ )
     {
         delete[] nce[ick];
         delete[] icep[ick];
         delete[] iceq[ick];
     }

      for( iek=0;iek<nek;iek++ )
     {
         delete[] iep[iek];
         delete[] ieq[iek];
     }
      delete[] iep;
      delete[] ieq;
      delete[] bbj;
   
      delete ug; 
  }

