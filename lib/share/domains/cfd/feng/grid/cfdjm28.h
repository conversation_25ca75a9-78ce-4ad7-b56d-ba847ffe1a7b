
//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  ifndef _CFDAU3X_
#  define _CFDAU3X_

#  include <cfdgrid.h>

   class cCfdJm28: public cCfdGrid
  {
      protected:

         virtual void elems2( cFdDomain *fdm );
         virtual void elems3( cFdDomain *fdm );

         virtual void image( string mnms );

         virtual void nms( Int *nm, string *mnms );
         virtual void nmsb( Int *nm, string *mnms );

     public:
         cCfdJm28();
         virtual ~cCfdJm28();
         virtual void setup( string, cFdDomain ** );
         virtual void setupf( string,cFdDomain * );
         virtual void load( cFdDomain * );
  };

#  endif
