   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdfdneut.h>

   void cCfdFdneut::setup( string fnme, cFdDomain **dmn )
  {
      cout << "fdneut normal setup=====================================\n";      
      Int     nw=0;
      Int     kb;
      Int     jb;
      string *line=NULL;
      string syn=" .,#%";

      Int            im;

     *dmn= new cFdDomain();

// default element kinds from FDNEUT

// assign element kinds
      cout << "will open file "<<fnme<<"\n";
      
      image( fnme );//, &nx, &np, &rwrk, &ng, &neg, &npg, igt, &iwrk, &gnms );

      subv( nx,np, rwrk,xp );
      mnms= new string[ng];
      blbl= new string[ng];
      bnms= new string[ng];
      blbs= new string[ng];
      bbjs= new string[ng];
      bbj=  new cFbndry*[ng];

      if( nx == 2 )
     {
         elems2(*dmn );
     }
      else
     {
         elems3(*dmn );
     }


      nms( &nm,mnms );

// boundary groups
      string btyp[6]={"NEUTRAL","PERIODIC","FREE","INV","VWALL","PROF"}; // MAURO : Added for Profiling Inlet
      string bpl[6]={ "libbcs.so",
                      "none",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so" };
      string bpo[6]={ "unibcs",
                      "none",
                      "unibcs",
                      "adiabcs",
                      "adiabcs",
                      "profilebcs"};
      Int kbk[6]={ neut_fbndry,neut_fbndry,free_fbndry,inv_fbndry,visc_fbndry,free_fbndry };

      nmsb( &mb,bnms );

      for( im=0;im<mb;im++ )
     {
         nw= 0;
         line=NULL;
         parse( bnms[im],&nw,&line,syn );
         if( nw > 1 )
        {
            kb=inlst( line[0], 6,btyp );
            jb=inlst( line[1],mb,bnms );
            blbs[im]= bpl[kb];
            bbjs[im]= bpo[kb];
            blbl[im]= line[1];
//          cout << "THE TYPE FOR THIS BOUNDARY WILL BE "<<kbk[kb]<<"\n";
            bbj[im]= newfbndry(kbk[kb]);
//          cout <<"created boundary object "<<bbj[im]<<"\n";
        }
         else
        {
            cout << "cannot parse boundary label\n";
            exit(0);
        }
         delete[] line; line=NULL; nw=0;
     }
//    cout << "mb "<<mb<<"\n";
    (*dmn)->boundaries( mb,blbl,blbs,bbjs );
    (*dmn)->bndries( bbj );

  }

   void cCfdFdneut::setuprestart( string fnme, cFdDomain **dmn )  // MAURO : Created in order to restart also the Boundaries@
  {      
      Int     nw=0;
      Int     kb;
      Int     jb;
      string *line=NULL;
      string syn=" .,#%";

      cout << "fdneut restart setup=====================================\n";      

      Int            im;

     *dmn= new cFdDomain();

// default element kinds from FDNEUT

// assign element kinds
      cout << "will open file "<<fnme<<"########################################\n";
      
      image( fnme );//, &nx, &np, &rwrk, &ng, &neg, &npg, igt, &iwrk, &gnms );

      subv( nx,np, rwrk,xp );
      mnms= new string[ng];
      blbl= new string[ng];
      bnms= new string[ng];
      blbs= new string[ng];
      bbjs= new string[ng];
      bbj=  new cFbndry*[ng];

      if( nx == 2 )
     {
         elems2(*dmn );
     }
      else
     {
         elems3(*dmn );
     }


      nms( &nm,mnms );

// boundary groups
      string btyp[6]={"NEUTRAL","PERIODIC","FREE","INV","VWALL","PROF"}; // MAURO : Added for Profiling Inlet
      string bpl[6]={ "libbcs.so",
                      "none",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so",
                      "libbcs.so" };
      string bpo[6]={ "binrestartbcs",
                      "none",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs",
                      "binrestartbcs"};
      Int kbk[6]={ neut_fbndry,neut_fbndry,free_fbndry,inv_fbndry,visc_fbndry,free_fbndry };

      nmsb( &mb,bnms );

      for( im=0;im<mb;im++ )
     {
         nw= 0;
         line=NULL;
         parse( bnms[im],&nw,&line,syn );
         if( nw > 1 )
        {
            kb=inlst( line[0], 6,btyp );
            jb=inlst( line[1],mb,bnms );
            blbs[im]= bpl[kb];
            bbjs[im]= bpo[kb];
            blbl[im]= line[1];
//          cout << "THE TYPE FOR THIS BOUNDARY WILL BE "<<kbk[kb]<<"\n";
            bbj[im]= newfbndry(kbk[kb]);
//          cout <<"created boundary object "<<bbj[im]<<"\n";
            cout << "The plugin to initialize this boundary is " << bbjs[im] << "\n";
        }
         else
        {
            cout << "cannot parse boundary label\n";
            exit(0);
        }
         delete[] line; line=NULL; nw=0;
     }
//    cout << "mb "<<mb<<"\n";
    (*dmn)->boundaries( mb,blbl,blbs,bbjs );
    (*dmn)->bndries( bbj );

  }
