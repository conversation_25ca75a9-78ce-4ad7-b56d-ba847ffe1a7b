   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdfdneut.h>

   void cCfdFdneut::blank()
  {
      cCfdGrid::blank();
      rwrk=NULL;
      xwrk=NULL;
      xbwrk=NULL;
      iwrk=NULL;
      setv( (Int)0,MxNPSs,(Int*)NULL,jwrk);
      igt[0]=NULL;
      igt[1]=NULL;
  }

   cCfdFdneut::cCfdFdneut()
  {      
      blank();
  }

   cCfdFdneut::~cCfdFdneut()
  {
      Int ig;
      for( ig=0;ig<ng;ig++ )
     {
         delete[] iwrk[ig];
     }
      delete[] iwrk;
      delete[] xbwrk;
      delete[] igt[0];
      delete[] igt[1];
  }

