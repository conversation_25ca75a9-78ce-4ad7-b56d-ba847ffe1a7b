   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdfdneut.h>

   extern "C" void fdntruntime( cDom *dev, void *, void *, cCase *cse, cDomain **dmn )
  {
//zz s
	  cout<< "!!!!!! fdntruntime !!!!!!!!!!!\n";
      cTabData      *dtab;
      string         tmpo, dspl,dspo;
      dtab= new cTabData();
      dev->get( dtab );
	  cout<< "!!!!!! domain-solution-library  !!!!!!!!!!!\n";
      dtab->get( "domain-solution-library",&dspl);
      cout<< "!!!!!! domain-solution-library ok  !!!!!!!!!!!\n";
      dtab->get( "domain-solution-object", &dspo);
      cout<< "!!!!!! domain-solution-object!!!!!!!!!!!\n";
//zz e
     *dmn= new cFdDomain(); // cDom should take care of deleting dmn!

/*   cout<<"!!!!! dsp !!!!!\n";
     cout<<"dspl "<<dspl<<" dspo"<<dspo<<endl;
     cPlugin *dsp = new cPlugin( dspl,dspo );
     cout<<"dspl "<<dspl<<" dspo"<<dspo<<endl;
     (*dmn)->splug(dsp); // assigns dsp plugin
     cout<< " !!!!!! dsp  ok!!!!!!!!!!!\n";

     tmpo=dspo+"alloc";
     cPlugin *dmp = new cPlugin( dspl,tmpo );
     (*dmn)->mplug(dmp); // assigns dmp plugin
     cout<< " !!!!!! dmp  ok!!!!!!!!!!!\n";
     tmpo=dspo+"free";
     cPlugin *dfp = new cPlugin( dspl,tmpo );
     (*dmn)->fplug(dfp); // assigns dmp plugin
     cout<< " !!!!!! dfp  ok!!!!!!!!!!!\n";
     //
     cout<< "!!!!!! Exiting fdntruntime !!!!!!!!!!!\n";*/
     delete dtab; dtab = NULL;
  }

   extern "C" void fdntdomain( cDom *dev, void *, void *, cCase *, cDomain **dmn )
  {
      cout << "Inside fdntdomain=====================================\n";
      cTabData      *dtab;
      cCfdGrid      *obj;
      cFdDomain     *fdm[10];

      Int            ilv,nlv;
      string         fnme;
      string         misc;

      dtab= new cTabData();
      dev->get( dtab );
      dtab->get( "multigrid-levels",&nlv );
      dtab->get( "misc",&misc );
  
      for( ilv=0;ilv<nlv;ilv++ )
     {
   // assign element kinds
         fnme= dev->getcpath();
   
         fnme= fnme+ "/"+dev->getname();
         fnme= fnme+"."+strc(ilv);

         obj= new cCfdFdneut();
         obj->setup( fnme,fdm+ilv ); //0 , 1 , 2
         obj->setupf( misc,fdm[ilv] );
         obj->load( fdm[ilv] );
         delete obj; obj= NULL;

         fdm[ilv]->assgndev( dev );
//zz debugging
         cout<<"fdntDmn "<< ilv <<" "<< fdm[ilv]<<" "<< fdm+ilv<<endl;
     }

      delete dtab; dtab= NULL;
      for( ilv=0;ilv<nlv-1;ilv++ )
     {
    	 cout<<"Attaching lvl "<<ilv+1<<" ----> "<<ilv<<endl;
         fdm[ilv]->attach( fdm[ilv+1] );
     }

     *dmn= fdm[0];
  }
// MAURO : For Restart file also from BC (Set this plug in device script)

  extern "C" void fdntdomainrestart( cDom *dev, void *, void *, cCase *, cDomain **dmn )
  {
      cout << "Inside fdntdomainrestart=====================================\n";
      cTabData      *dtab;
      cCfdGrid      *obj;
      cFdDomain     *fdm[10];

      Int            ilv,nlv;
      string         fnme;
      string         misc;

      dtab= new cTabData();
      dev->get( dtab );
      dtab->get( "multigrid-levels",&nlv );
      dtab->get( "misc",&misc );

      for( ilv=0;ilv<nlv;ilv++ )
     {
   // assign element kinds
         fnme= dev->getcpath();

         fnme= fnme+ "/"+dev->getname();
         fnme= fnme+"."+strc(ilv);

         obj= new cCfdFdneut();
         obj->setuprestart( fnme,fdm+ilv ); //0 , 1 , 2
         obj->setupf( misc,fdm[ilv] );
         obj->load( fdm[ilv] );
         delete obj; obj= NULL;

         fdm[ilv]->assgndev( dev );
//zz debugging
         cout<<"fdntDmn "<< ilv <<" "<< fdm[ilv]<<" "<< fdm+ilv<<endl;
     }

      delete dtab; dtab= NULL;
      for( ilv=0;ilv<nlv-1;ilv++ )
     {
         cout<<"Attaching lvl "<<ilv+1<<" ----> "<<ilv<<endl;
         fdm[ilv]->attach( fdm[ilv+1] );
     }

     *dmn= fdm[0];

   }


