
//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  ifndef _CFDFDNEUT_
#  define _CFDFDNEUT_

#  include <cfdgrid.h>

   class cCfdFdneut: public cCfdGrid
  {
      protected:

         Real          *rwrk;
         Real          *xwrk;
         Real         **xbwrk;
         Int          **iwrk;
         Int           *jwrk[MxNPSs];
         Int           *igt[2];


         virtual void elems2( cFdDomain *fdm );
         virtual void elems3( cFdDomain *fdm );

         virtual void image( string mnms );

         virtual void gather( Int nm, string *mnms, Int  *ne,Int *siem[], Int *siep[] );
         virtual void gather( string mnms, Int  *ne, Int *siep[] );

         virtual void nmsb( Int *nm, string *mnms );
         virtual void nms( Int *nm, string *mnms );
         virtual void blank();

     public:
         cCfdFdneut();
         virtual ~cCfdFdneut();
         virtual void setup( string, cFdDomain ** );
         virtual void setuprestart( string, cFdDomain ** );
         virtual void setupf( string,cFdDomain * );
         virtual void load( cFdDomain * );
  };

#  endif
