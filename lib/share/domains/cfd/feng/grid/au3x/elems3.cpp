
   using namespace std;

#  include <cfdau3x.h>

   void cCfdAu3x::elems3( cFdDomain *fdm )
  {

      Int            iek,ick;
      cFElement     *elm[MxNSk];
      cFElement     *blm[MxNSk];

      setv( (Int)0,(Int)MxNSk, (Int)-1, jek );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nep );
      setv( (Int)0,(Int)MxNSk, (Int) 0, neq );

      setv( (Int)0,(Int)MxNSk, (Int)-1, jbk );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nbp );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nbq );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nbd );

// boundary elements

      blm[ nbk]= new cfe22();
      nbp[ nbk]= blm[ nbk]->getnp();
      nbq[ nbk]= blm[ nbk]->getnq();
      nbd[ nbk]= blm[ nbk]->getnd();
      jbk[0]=  nbk; 
      bpmsk[ nbk][0]=0;
      bpmsk[ nbk][1]=1;
    ( nbk)++;

      blm[nbk]= new cft33();
      nbp[nbk]= blm[nbk]->getnp();
      nbq[nbk]= blm[nbk]->getnq();
      nbd[nbk]= blm[nbk]->getnd();
      jbk[1]= nbk; 
      bpmsk[nbk][0]=0;
      bpmsk[nbk][1]=1;
      bpmsk[nbk][2]=2;
     (nbk)++;

      blm[nbk]= new cfq43();
      nbp[nbk]= blm[nbk]->getnp();
      nbq[nbk]= blm[nbk]->getnq();
      nbd[nbk]= blm[nbk]->getnd();
      jbk[2]= nbk; 
      bpmsk[nbk][0]=0;
      bpmsk[nbk][1]=1;
      bpmsk[nbk][2]=2;
      bpmsk[nbk][3]=3;
     (nbk)++;
        
// internal elements

      elm[ nek]= new cft32();
      nep[ nek]= elm[ nek]->getnp();
      neq[ nek]= elm[ nek]->getnq();
      jek[0]=  nek; 
      ipmsk[ nek][0]=0;
      ipmsk[ nek][1]=1;
      ipmsk[ nek][2]=2;
    ( nek)++;

      elm[ nek]= new cfq42();
      nep[ nek]= elm[ nek]->getnp();
      neq[ nek]= elm[ nek]->getnq();
      jek[1]=  nek; 
      ipmsk[ nek][0]=0;
      ipmsk[ nek][1]=1;
      ipmsk[ nek][2]=2;
      ipmsk[ nek][3]=3;
    ( nek)++;

      elm[nek]= new cft43();
      nep[nek]= elm[nek]->getnp();
      neq[nek]= elm[nek]->getnq();
      jek[2]= nek; 
      ipmsk[nek][0]=0;
      ipmsk[nek][1]=1;
      ipmsk[nek][2]=2;
      ipmsk[nek][3]=3;
     (nek)++;

      elm[nek]= new cfp53();
      nep[nek]= elm[nek]->getnp();
      neq[nek]= elm[nek]->getnq();
      jek[3]= nek; 
      ipmsk[nek][0]=0;
      ipmsk[nek][1]=1;
      ipmsk[nek][2]=2;
      ipmsk[nek][3]=3;
      ipmsk[nek][4]=4;
     (nek)++;

      elm[nek]= new cfp63();
      nep[nek]= elm[nek]->getnp();
      neq[nek]= elm[nek]->getnq();
      jek[4]= nek; 
      ipmsk[nek][0]=0;
      ipmsk[nek][1]=1;
      ipmsk[nek][2]=2;
      ipmsk[nek][3]=3;
      ipmsk[nek][4]=4;
      ipmsk[nek][5]=5;
     (nek)++;

      elm[nek]= new cfq83();
      nep[nek]= elm[nek]->getnp();
      neq[nek]= elm[nek]->getnq();
      jek[5]= nek; 
      ipmsk[nek][0]=0;
      ipmsk[nek][1]=1;
      ipmsk[nek][2]=2;
      ipmsk[nek][3]=3;
      ipmsk[nek][4]=4;
      ipmsk[nek][5]=5;
      ipmsk[nek][6]=6;
      ipmsk[nek][7]=7;
     (nek)++;

// boundary masks

      for( ick=0;ick<MxNSk;ick++ )
     {
         nce[ick]= new Int[nek];
         icep[ick]= new Int**[nek];
         iceq[ick]= new Int**[nek];
         for( iek=0;iek<nek;iek++ )
        {
            elm[iek]->cmsk( ick, nce[ick]+iek, icep[ick]+iek,iceq[ick]+iek );           
        }
     }

      fdm->elements( (nbk),blm, (nek),elm );

  }
