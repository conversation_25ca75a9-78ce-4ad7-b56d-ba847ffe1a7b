
//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  ifndef _CFDGRID_
#  define _CFDGRID_

#  include <string>
#  include <fstream>
#  include <sstream>
#  include <utils/proto.h>
#  include <topo/topo.h>
#  include <topo/ugraph.h>
#  include <device/client/dom.h>
#  include <domain/symplex.h>
#  include <domain/cfd/domain.h>
#  include <cosystem/cosystems.h>
#  include <lin/vect.h>

   class cCfdGrid
  {
      protected:
         Int           *neg,*npg;
         string        *gnms;
         string        *bnms;
         string        *blbl;
         string        *blbs;
         string        *bbjs;
         string        *mnms;
         string         fnme;
         string         bnm;

         Real          *sxp; 
         Real          *sxq; 
         Real          *sxqb[MxNBG]; 
         Real          *xp[3]; 

         Int            nbk,nek;

         Int            iqmsk[MxNSk][MxNPSs];
         Int            ipmsk[MxNSk][MxNPSs];
         Int            bqmsk[MxNSk][MxNPSs];
         Int            bpmsk[MxNSk][MxNPSs];

         Int            nep[MxNSk];
         Int            neq[MxNSk];
         Int            jek[MxNSk];

         Int           *siem[MxNSk];
         Int           *siep[MxNSk],***iep;
         Int           *sieq[MxNSk],***ieq;
         Real          *sxb[MxNBG];

         Int            nb[MxNBG][MxNSk];
         Int            ne[MxNSk];
         Int            nq,np,ng,nx,mb,nm;

         Int            nbb[MxNBG];
         Int            nbp[MxNSk];
         Int            nbq[MxNSk];
         Int            nbd[MxNSk];
         Int            jbk[MxNSk];

         Int           *sibp[MxNBG][MxNSk],**ibp;
         Int           *sibq[MxNBG][MxNSk],**ibq;
         Int           *sibb[MxNBG][MxNSk];

         Int           *nce[MxNSk];
         Int         ***icep[MxNSk];
         Int         ***iceq[MxNSk];
    
         Int           *ihlp;
         Int           *bhlp;
         cFbndry      **bbj;

         cUgraph       *ug;

         virtual void elems2( cFdDomain *fdm ){};
         virtual void elems3( cFdDomain *fdm ){};

         virtual void image( string mnms ){};
         virtual void dof( Int nek, Int *ne, Int *nep, Int *neq, Int *siep[], Int *sieq[], Int nx, Real *xp[], Int *nq, Real **sxq );
         virtual void bdof( Int *nep, Int *neq, Int **iep[], Int **ieq[], Int *nce[], Int ***icep[],  Int ***iceq[],
                        cUgraph *ug, Int  *nb, Int nbk, Int *nbp, Int *nbq, Int *sibp[], Int *sibq[] );

         virtual void nmsb( Int *nm, string *mnms ){};
         virtual void nms( Int *nm, string *mnms ){};

         virtual void blank();
     public:
         cCfdGrid();
         virtual ~cCfdGrid();
         virtual void setup( string, cFdDomain ** ){};
         virtual void setuprestart( string, cFdDomain ** ){};
         virtual void setupf( string,cFdDomain * ){};
         virtual void load( cFdDomain * ){};
  };

#  endif
