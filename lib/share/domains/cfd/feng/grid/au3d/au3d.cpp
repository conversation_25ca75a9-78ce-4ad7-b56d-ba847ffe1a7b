   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <cfdau3d.h>

   extern "C" void au3druntime( cDom *dev, void *, void *, cCase *cse, 
                                cDomain **dmn )
  {
     *dmn= new cFdDomain();
  }

   extern "C" void au3ddomain( cDom *dev, void *, void *, cCase *, 
                               cDomain **dmn )
  {      
      cTabData      *dtab;
      cCfdGrid      *obj;
      cFdDomain     *fdm[10];

      Int            ilv,nlv;
      string         fnme;
      string         misc;

      dtab= new cTabData();
      dev->get( dtab );
      dtab->get( "multigrid-levels",&nlv );
      dtab->get( "misc",&misc );
  
      for( ilv=0;ilv<nlv;ilv++ )
     { 

   // assign element kinds
         fnme= dev->getcpath();
   
         fnme= fnme+ "/"+dev->getname();
         fnme= fnme+"."+strc(ilv);

         obj= new cCfdAu3d();
         obj->setup( fnme,fdm+ilv );
         obj->setupf( misc,fdm[ilv] );
         obj->load( fdm[ilv] );
         delete obj; obj= NULL;

         fdm[ilv]->assgndev( dev );
     }
      
      delete dtab; dtab= NULL;
      for( ilv=0;ilv<nlv-1;ilv++ )
     {
         fdm[ilv]->attach( fdm[ilv+1] );
     }

     *dmn= fdm[0];

  }
