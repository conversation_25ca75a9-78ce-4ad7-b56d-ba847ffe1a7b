   using namespace std;

#  include <cfdau3d.h>

   void cCfdAu3d::image( string nme )
  {

      Int          i,j,iek,ig,ibk;
      ifstream     fle;
      const Int    NMAX = 100;
      Int          lcbnm[NMAX];
      char        *cbnm[NMAX];
      Int          nbld, mbld;
      const char  *cname;
      Int          lname;
      Int          npc[3] = {2,3,4};
      Int          tmpnb[100][3];
      Int         *tmpsibp[100][3];
 
      string       fnme;
      fnme= nme;
      cout << "read plt file "<<fnme << ".plt\n";
      fle.open( fnme.c_str() );

      cname= fnme.c_str();
      lname= fnme.length();

      for( ig=0;ig<NMAX;ig++ )
     {
         lcbnm[ig]= 0;
         cbnm[ig]= new char[NMAX];
     }

      //pltscan( &lname,cname, &nx, &np, ne, &ng,&(nb[0][0]), lcbnm,cbnm );
      pltscan( &lname,cname, &nx, &np, ne, &ng,&(tmpnb[0][0]), lcbnm,cbnm );

      //cout << "nx " << nx << "\n";
      //cout << "np " << np << "\n";
      //for(iek=0; iek<6; iek++)
    // {
    //    cout << "ne " << iek << " " << ne[iek] << "\n";
    // }
    //  cout << "nb " << ng << "\n";
      for(ig=0; ig<ng; ig++)
     {
         for(ibk=0; ibk<3; ibk++)
        {
           //cout << "nb " << ig << " " << ibk << " " << tmpnb[ig][ibk] << "\n";
            nb[ig][ibk] = tmpnb[ig][ibk];
        } 
     } 

      gnms= new string[ng];
      for( ig=0;ig<ng;ig++ )
     { 
         //gnms[ig]= string( cbnm[ig],0,lcbnm[ig] ); 
         gnms[ig].assign( cbnm[ig],0,lcbnm[ig] ); 
         //cout << "gnms " << ig << " " << gnms[ig] << "\n";
     }

      for(iek=0; iek<6; iek++)
     {
          siep[iek] = new Int [8*ne[iek]];
          siem[iek]= new Int[ ne[iek] ];
     }

      for(ig=0; ig<ng; ig++)
     {
         for(ibk=0; ibk<3; ibk++)
        {
             //cout << "npc " << ibk << " " << npc[ibk] << "\n";
             tmpsibp[ig][ibk] = new Int [npc[ibk]*nb[ig][ibk]];
             sibp[ig][ibk] = new Int [npc[ibk]*nb[ig][ibk]];
        } 
     } 

      sxp= new Real[nx*np]; subv( nx,np,sxp,xp );
      pltload( &lname,cname,&nx,&np,sxp,ne,siep,&(nb[0][0]), &(tmpsibp[0][0]),
               &nbld,&mbld );
      //note: nbld or mbld is not used. The value is read from the setup file
      for(ig=0; ig<ng; ig++)
     {
         for(ibk=0; ibk<3; ibk++)
        {
            if(nb[ig][ibk]>0)
           {
               for(Int ip=0; ip<npc[ibk]*nb[ig][ibk]; ip++)
              { 
                   sibp[ig][ibk][ip] = tmpsibp[ig][ibk][ip];
              }
           }
        } 
     } 

      //cout << "nbld " << nbld << " " << mbld << "\n";

      for( ig=0;ig<MxNBG;ig++ )
     {
         delete[] cbnm[ig]; cbnm[ig]=NULL;
         lcbnm[ig]= 0;
     }

      for(ig=0; ig<ng; ig++)
     {
         for(ibk=0; ibk<3; ibk++)
        {
            delete[] tmpsibp[ig][ibk]; tmpsibp[ig][ibk]=NULL;
        } 
     } 
  }

