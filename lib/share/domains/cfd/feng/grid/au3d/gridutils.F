c# include            <cfdau3d.h>
# include            <fprec.h>

c23456789 123456789 123456789 123456789 123456789 123456789 123456789 12
c        1         2         3         4         5         6         7

c ... Author          <PERSON> <<EMAIL>>
c ... Created         Sun May 11 20:14:59 BST 2008
c ... Changes History -
c ... Next Change(s)  ( work in progress )
c ... Purpose         cBlock basic constructor

      subroutine  pltscan( lfle,cfle, nx,np,ne, ng,nb, lbnm,pbnm )
 
      implicit none

c ... arguments

      Int                 lfle
      Int                 np,nx
      character(len=lfle) cfle
      Int                 ne(*)
      Int                 nb(3,100)

      Int                 ng

      Int                 lbnm(*)
      Pntr                pbnm(*)

c ... local symbols

      Int                 ldum

      integer             nel4,nel5,nel6,nel8
      integer             npoin
      integer             nbou3,nbou4
 
      character*500       fname

      character(len=80)   gname
      character(len=80)   bnm

      integer             ie,ib,jp,ix,ig,ip
      integer             idum
      real                rdum

      integer             idmb
      dimension           idmb(:,:)
      pointer             idmb

c ... executable statements

      ldum=80
      fname= cfle//'.plt'

      ng=0
      do 5 ig=1,100
        nb(1,ig)= 0
        nb(2,ig)= 0
        nb(3,ig)= 0
        write( bnm,9995 ) ig
        call cassign( ldum,bnm,pbnm(ig) )
    5 continue
 9995 format('BOUNDARY',i3.3 )

      open( unit=12,file=trim(fname),form='unformatted',status='old')
      read( 12 ) nel4,nel5,nel6,nel8, npoin, nbou3,nbou4
      if( nel4 .gt. 0 ) read( 12 )(( idum,ie=1,nel4),jp=1,4 )
      if( nel5 .gt. 0 ) read( 12 )(( idum,ie=1,nel5),jp=1,5 )
      if( nel6 .gt. 0 ) read( 12 )(( idum,ie=1,nel6),jp=1,6 )
      if( nel8 .gt. 0 ) read( 12 )(( idum,ie=1,nel8),jp=1,8 )
      read( 12 )(( rdum,ip=1,np ),ix=1,3 )
      if( nbou3 .gt. 0 )then
        allocate( idmb(nbou3,6 ) )
        read( 12 )(( idmb(ib,jp),ib=1,nbou3),jp=1,6 )
        do 13 ib=1,nbou3
          ig= idmb(ib,5)
          ng= max( ng,ig )
          nb(2,ig)= nb(2,ig)+1
   13   continue
        deallocate( idmb )
      endif
      if( nbou4 .gt. 0 )then
        allocate( idmb(nbou4,7 ) )
        read( 12 )(( idmb(ib,jp),ib=1,nbou4),jp=1,7 )
        do 14 ib=1,nbou4
          ig= idmb(ib,6) 
          ng= max( ng,ig )
          ng= max( ng,ig )
          nb(3,ig)= nb(3,ig)+1
   14   continue
        deallocate( idmb )
      endif
      close( 12 )

      nx= 3
      np= npoin

      ne(1)= 0
      ne(2)= 0
      ne(3)= nel4
      ne(4)= nel5
      ne(5)= nel6
      ne(6)= nel8

      if( ng .eq. 7 .or. ng .eq. 6 )then
c ... jm28 defaults
        bnm= 'INLET'
        call cassign( ldum,bnm,pbnm(1))
        bnm= 'EXIT'
        call cassign( ldum,bnm,pbnm(2))
        bnm= 'RIGHT'
        call cassign( ldum,bnm,pbnm(3))
        bnm= 'LEFT'
        call cassign( ldum,bnm,pbnm(4))
        bnm= 'HUB'
        call cassign( ldum,bnm,pbnm(5))
        bnm= 'CASING'
        call cassign( ldum,bnm,pbnm(6))
        bnm= 'BLADE'
        call cassign( ldum,bnm,pbnm(7))
      endif

      fname= cfle//'.boundaries'
      open( unit=12,file=trim(fname),form='formatted',status='old',
     1      err=0100 )
        do 10 ig=1,ng
          read( 12,9999 ) gname
          bnm= adjustl(gname)
          call cassign( ldum,bnm,pbnm(ig))
   10   continue
        close( 12 )
 0100 continue
 9999 format( a )

      do 40 ig=1,ng
        call cretrieve( ldum,pbnm(ig),bnm)
        lbnm(ig)=len_trim(bnm)
   40 continue

      return

      end

c23456789 123456789 123456789 123456789 123456789 123456789 123456789 12
c        1         2         3         4         5         6         7

c ... Author          Luca di Mare <<EMAIL>>
c ... Created         Sun May 11 20:14:59 BST 2008
c ... Changes History -
c ... Next Change(s)  ( work in progress )
c ... Purpose         cBlock basic constructor

      subroutine pltload( lfle,cfle, nx,np,x, ne, piep, nb, pibp, 
     1                    nbld,mbld )
 
      implicit none

c ... arguments

      Int                 lfle
      character(len=lfle) cfle

      Pntr                piep(*), pibp(3,100)
      
      Int                 ne
      dimension           ne(*)

      Int                 nx,np
      Real                x(*)
      Int                 nb(3,100)

      Int                 nbld,mbld
c ... local symbols

      integer             nel4,nel5,nel6,nel8
      integer             npoin
      integer             nbou3,nbou4

      Int                 lnel4,lnel5,lnel6,lnel8
      Int                 lnpoin
      Int                 lnbou3,lnbou4
      Int                 ldum0,ldum1,ldum2
  
      character*500       fname
      integer             ip,iv,ig

      Real                rwrk
      dimension           rwrk(:,:)
      pointer             rwrk

      real                rbuf
      dimension           rbuf(:,:)
      pointer             rbuf

      integer             iofb3,iofb4
      dimension           iofb3(100),iofb4(100)

      Pntr                pibp3(100)
      Pntr                pibp4(100)

      integer             nstag
      integer             istag,nblde,mblde
      dimension           istag(:),nblde(:),mblde(:)
      pointer             istag,nblde,mblde

c ... executable statements

      fname= trim(cfle)//'.plt'

      open( unit=12,file=trim(fname),form='unformatted',status='old')
      read( 12 ) nel4,nel5,nel6,nel8, npoin, nbou3,nbou4
      lnel4=  nel4
      lnel5=  nel5
      lnel6=  nel6
      lnel8=  nel8
      lnpoin= npoin
      lnbou3= nbou3
      lnbou4= nbou4

      call pltrdelem( 4,nel4, piep(3) )
      call pltrdelem( 5,nel5, piep(4) )
      call pltrdelem( 6,nel6, piep(5) )
      call pltrdelem( 8,nel8, piep(6) )

      allocate( rbuf(npoin,3) )
      allocate( rwrk(npoin,3) )
      read( 12 )(( rbuf(ip,iv),ip=1,npoin),iv=1,3 )
      do 19 iv=1,3
        do 19 ip=1,npoin
          rwrk(ip,iv)= rbuf(ip,iv)
   19 continue
      
      ldum0=3
      ldum1=1
      ldum2=1
      call rcpyf( ldum0,ldum1,ldum2, lnpoin, lnpoin, rwrk, lnpoin ,x )
      deallocate( rbuf )
      deallocate( rwrk )

      do 50 ig=1,100
        iofb3(ig)=0
        iofb4(ig)=0
        pibp3(ig)= pibp(2,ig)
        pibp4(ig)= pibp(3,ig)
   50 continue

      call pltrdbou( 3,nbou3, pibp3,iofb3 )
      call pltrdbou( 4,nbou4, pibp4,iofb4 )
      read( 12 ) nstag
      allocate( istag(nstag) )
      allocate( nblde(nstag) )
      allocate( mblde(nstag) )
      read( 12 ) (istag(ig),ig=1,nstag)
      read( 12 ) (nblde(ig),ig=1,nstag)
      read( 12 ) (mblde(ig),ig=1,nstag)
      nbld= nblde(1)
      mbld= mblde(1)
      deallocate( istag )
      deallocate( nblde )
      deallocate( mblde )

      close( 12 )

      return

      end

c ...

      subroutine pltrdelem( nep,nel, piep )
      implicit none

c ... arguments

      integer               nep,nel
      Pntr                  piep

c ... local symbols

      integer               ibuf
      dimension             ibuf(:,:)
      pointer               ibuf

      Int                   iwrk
      dimension             iwrk(:,:)
      pointer               iwrk
      integer               ie,jp
      Int                   one,inep,inel

c ... executable statements

      one=1
      inep= nep
      inel= nel
      if( nel .gt. 0 )then
        allocate( iwrk(nel,nep) )
        allocate( ibuf(nel,nep) )
        read( 12 )(( ibuf(ie,jp),ie=1,nel),jp=1,nep )
        do 10 ie=1,nel
          do 10 jp=1,nep
            iwrk(ie,jp)= ibuf(ie,jp)-1
  10    continue
        call iassign( inep,one,one, inel, inel,iwrk, inel,piep )
        deallocate( ibuf )
        deallocate( iwrk )
      endif
      return
      end

      subroutine pltrdbou( nbp,nbou, pibp,iofb )

      implicit none

c ... arguments

      integer               nbp,nbou
      Pntr                  pibp(100)

      Integer               iofb
      dimension             iofb(100)

c ... local symbols

      integer               ibuf
      dimension             ibuf(:,:)
      pointer               ibuf

      Int                   iwrk
      dimension             iwrk(:,:)
      pointer               iwrk
      integer               jb,jp,ig
      Int                   one,inbp,inbou,ib,idum

      one= 1
      inbou= nbou
      inbp=  nbp

      if( nbou .gt. 0 )then
        allocate( ibuf(nbou,nbp+3) )
        allocate( iwrk(nbou,nbp) )
        read( 12 )(( ibuf(ib,jp),ib=1,nbou),jp=1,nbp+3 )
        do 30 ig=1,100
          ib= 0
          do 31 jb=1,nbou 
            if( ibuf(jb,nbp+2) .eq. ig )then
              ib= ib+1
              do 32 jp=1,nbp
                iwrk(ib,jp)= ibuf(jb,jp)-1 
   32         continue
            endif
   31     continue
          idum= iofb(ig)+1
          call iassign( inbp,one,idum, ib, inbou,iwrk, ib,pibp(ig) )
          iofb(ig)= iofb(ig)+ib
   30   continue
        deallocate( ibuf )
        deallocate( iwrk )
      endif

      return

      end

c23456789 123456789 123456789 123456789 123456789 123456789 123456789 12
c        1         2         3         4         5         6         7

c ... Author          Luca di Mare <<EMAIL>>
c ... Created         Sun May 11 20:14:59 BST 2008
c ... Changes History -
c ... Next Change(s)  ( work in progress )
c ... Purpose         cBlock basic constructor

      subroutine fdntscan( lfle,cfle, nx,np,ne, ng,nb, lbnm,pbnm )

      implicit none

c ... arguments

      Int                 lfle
      Int                 np,nx
      character(len=lfle) cfle
      Int                 ne(*)
      Int                 nb(3,100)

      Int                 ng

      Int                 lbnm(*)
      Pntr                pbnm(*)

c ... local symbols

      integer             nelms,ngs
      integer             jg,ig, neg,npg,igg,igt,ik,ikd
 
      character(len=500)  fname

      character(len=80)   ename,enameo

      integer             iek
      dimension           iek(2,8)
      
      data                iek(1,1) / -1 /, iek(2,1) / -1 /,
     1                    iek(1,2) / -1 /, iek(2,2) / -1 /,
     1                    iek(1,3) /  1 /, iek(2,3) / -1 /,
     1                    iek(1,4) /  2 /, iek(2,4) /  3 /,
     1                    iek(1,5) / -1 /, iek(2,5) /  4 /,
     1                    iek(1,6) / -1 /, iek(2,6) /  5 /,
     1                    iek(1,7) / -1 /, iek(2,7) / -1 /,
     1                    iek(1,8) / -1 /, iek(2,8) /  6 /

      integer             ibk
      dimension           ibk(4)
      
      data                ibk(1) / -1 /,
     1                    ibk(2) /  1 /,
     1                    ibk(3) /  2 /,
     1                    ibk(4) /  3 /

      Int                 ldum
      integer             iu


c ... executable statements

      ldum=80
      iu= 12

      do 3 ig=1,100
        do 3 ik=1,3
          nb(ik,ig)= 0
    3 continue

      do 5 ik=1,6
    5   ne(ik)= 0

      fname= trim(cfle)//'.FDNEUT'

      enameo=''
      ng= 0
      
      open( unit=iu,file=trim(fname),form='formatted',status='old')
      call skipl( iu,5 )
      read( iu,* ) np,nelms,ngs,nx
      if( nx .eq. 3 )then
        ikd= 2
      else
        ikd= 1
      endif
      call skipl( iu,7 )
      call skipl( iu,np )
      call skipl( iu,3 )
      do 10 jg=1,ngs
        read( iu,0101 ) ig,neg,npg,igg,igt
 0101   format( 6x,i9,10x,i11,8x,i11,10x,i5,6x,i4 )
        read( iu,0102 ) ename
c ... volume elements
        if( igt .eq. igg )then
          ik= iek(ikd,npg)
          ne(ik)= ne(ik)+ neg
        else
c ... boundary faces
          ik= ibk(npg)
          ename= trim(adjustl(ename))
          if( ename .ne. enameo )then
            ng= ng+1
            enameo= ename
            call cassign( ldum,ename,pbnm(ng) )
            lbnm(ng)=len_trim(ename)
          endif
          nb(ik,ng)= nb(ik,ng)+neg
        endif
 0102   format( 12x,a )
        call skipl( iu,neg )
   10 continue
      close( iu )

c     fname= cpath//'/'//cname//'.boundaries'
c     open( unit=12,file=trim(fname),form='formatted',status='old')
c     read( 12,* ) ng
c     close( 12 )

      return

      end

