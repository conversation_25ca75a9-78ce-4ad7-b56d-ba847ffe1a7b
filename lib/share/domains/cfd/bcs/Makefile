include ../../../../Makefile.in

CSRC=   bcs.cpp \
	profile.cpp \
	q263.cpp

FSRC=
COBJ=$(CSRC:.cpp=.o)
FOBJ=$(FSRC:.F=.o)
OBJS=$(COBJ) $(FOBJ)

BINS=libbcs.so

$(BINS): $(OBJS)
	$(PCCMP) $(COPT) $(OBJS) $(ORGL) $(ENGL) $(SYSL) $(LIBF) -shared -o $@

clean:
	rm -f $(OBJS) $(BINS)

.cpp.o:
	$(PCCMP) $(COPT) -Wunused -I./ $(ORGI) $(ENGI) $(SYSI) $(PETSCI) -fPIC -o $@ -c $<

.F.o:
	$(PFCMP) $(FOPT) -I./ $(ORGI) $(ENGI) $(SYSI) $(GUII)  -o $@ -c $<

