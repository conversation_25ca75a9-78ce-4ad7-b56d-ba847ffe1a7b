   using namespace std;

#  include <domain/cfd/domain.h>
#  include <geo/vspline.h>
#  include <q263.h>
#  include <assert.h>

//   extern "C" void profilebcs( cDomain *dom, void *rftr, string bname, Real omega, Int nx, Int nv, Int nb, Real *xb[], 
//                               Real *qb[], Int nvauxb, Real *auxb[], Int *idone )
//  {
//      cDevice      *dev;
//      string       fnme;
//      ifstream      fle;
//      Int           i,j,n,n1;
//      Int           iprf;
//      cField       *fld;
//      cCosystem    *coo;
//      cVSpline     *lut;
//      double        dummy; //MAURO
//
//  //    assert( nv == 5 );     // MAURO
//      cout << "MAURO.... nx= " << nx <<"\n";
//      assert( nx == 2 );       // Control for 2D
//      Real *sy,*y[3];
//      Real  ***x,***v;
//      Real vp[5],dvp[5];
//
//     cout << bname<<"\n";
//
//      fld= dom->field();
//      coo= dom->cosystem();
//      dev= dom->device();
//
//      fnme= dev->getcpath();
//      fnme= fnme+"/profiles.dat";
//
//      cout << "should open "<<fnme<<"\n";
//
//      fle.open( fnme.c_str() );
//      fle >> n ;
//
//      x= new Real **[2];
//      v= new Real **[2];
//      x[0]= new Real*[2];
//      x[1]= new Real*[2];
//      v[0]= new Real*[nv];
//      v[1]= new Real*[nv];
//      for( i=0;i<2;i++ )
//     {
//         x[0][i]= new Real[n];
//         x[1][i]= new Real[n];
//     }
//      for( i=0;i<nv;i++ )
//     {
//         v[0][i]= new Real[n];
//         v[1][i]= new Real[n];
//     }
//
//      for( i=0;i<n;i++ )
//     {
//      //   fle >> x[0][1][i]>> x[0][0][i]>>  v[0][0][i]>> v[0][1][i]>> v[0][2][i]>> v[0][3][i]>> v[0][4][i] ;
//           fle >> x[0][1][i]>>  v[0][0][i]>> v[0][1][i]>> v[0][2][i]>> v[0][3][i]>> v[0][4][i] >>  v[0][5][i] ;   //MAURO
////             fle >> x[0][1][i]>>  v[0][0][i]>> v[0][1][i]>> v[0][2][i]>> v[0][3][i] >> v[0][4][i] ;   //MAURO for spalart
////           fle >> x[0][1][i]>>  v[0][0][i]>> v[0][1][i]>> v[0][2][i]>> v[0][3][i] >> dummy;   //MAURO for cebeci
//
//     //    x[0][0][i] = -0.06; //   ?????
////         v[0][0][i] = 3* v[0][0][i];
////         v[0][1][i] = 3* v[0][1][i];
//         // MAURO : ATTENZIONE poi da CALCELLARE:
//         cout << x[0][1][i] << " " << v[0][0][i] <<" " << v[0][1][i] <<"\n" ;
//
//
//         // ATTENZIONE Qui legge y , X e le valriabili del u,v,T p
//    //     cout << x[0][1][i] << " " << x[0][0][i]<<" " <<  v[0][0][i] <<" " << v[0][1][i] <<" " << v[0][2][i] <<" " << v[0][3][i] <<" " << v[0][4][i];
//     }
//      cout << "MAURO .... before assert profile.ccp line 59 \n";
//      //n1 = n;
//      fle >> n1; cout << " ++++++++++++++++++++" << nv << " " << n <<"\n" ; assert( n1 == n );
//      cout << "MAURO .... after assert profile.ccp line 61 \n";
//      for( i=0;i<n;i++ )
//     {
////        fle >> x[1][1][i]>> x[1][0][i]>>  v[1][0][i]>> v[1][1][i]>> v[1][2][i]>> v[1][3][i]>> v[1][4][i];
//          fle >> x[1][1][i]>>  v[1][0][i]>> v[1][1][i]>> v[1][2][i]>> v[1][3][i]>> v[1][4][i] >> v[1][5][i];  //MAURO
////        fle >> x[1][1][i]>>  v[1][0][i]>> v[1][1][i]>> v[1][2][i]>> v[1][3][i] >> v[1][4][i] ;  //MAURO   for spalart
////        fle >> x[1][1][i]>>  v[1][0][i]>> v[1][1][i]>> v[1][2][i]>> v[1][3][i] >> dummy;  //MAURO   for Cebeci
//
//
//
////           x[1][0][i] =  0.94;  // Dubbioso ????
//    //     cout << x[1][1][i] << " " << x[1][0][i]<<" " <<  v[1][0][i] <<" " << v[1][1][i] <<" " << v[1][2][i] <<" " << v[1][3][i] <<" " << v[1][4][i];
//     }
//      fle.close();
//
//      sy= new Real[nx*nb];
//      cout << " MAURO : nx, nb, xb  = " << nx <<" "<< nb << " " << **xb << "\n";
//      cout << "reading " << (bname.c_str())[0] << "\n";              //MAURO
//      subv( nx,nb, sy,y );
//      coo->bcoor( 0,nb, xb,y );
//
//      if( (bname.c_str())[0] == 'I' ){ iprf= 0; cout << "Inlet... \n"; };    //MAURO
//      if( (bname.c_str())[0] == 'E' ){ iprf= 1;cout << "Outlet... \n"; };     //MAURO
//
//      lut= new cVSpline();
//
//      lut->build( 0, n,nv, x[iprf][1],v[iprf],NULL,NULL );
//
//      for( i=0;i<nb;i++ )
//     {
//         lut->interp( y[1][i], vp,dvp );
//         for( j=0;j<nv;j++ )
//        {
//            qb[j][i]= vp[j];
//        }
//     }
//// Distruction of pointer
//      delete lut; lut=NULL;
//      for( i=0;i<2;i++ )
//     {
//         delete[] x[0][i]; x[0][i]= NULL;
//         delete[] x[1][i]; x[1][i]= NULL;
//     }
//      for( i=0;i<nv;i++ )
//     {
//         delete[] v[0][i]; v[0][i]= NULL;
//         delete[] v[1][i]; v[1][i]= NULL;
//     }
//      delete[] x[0]; x[0]= NULL;
//      delete[] x[1]; x[1]= NULL;
//      delete[] v[0]; v[0]= NULL;
//      delete[] v[1]; v[1]= NULL;
//      delete[] x; x=NULL;
//      delete[] v; v=NULL;
//      delete[] sy; sy=NULL;
//  }


   extern "C" void q263bc( cDomain *dom, void *rftr, string bname, Real omega, Int nx,
                           Int nv, Int nb, cAu3xView<Real>& xb, cAu3xView<Real>& qb, Int nvauxb, cAu3xView<Real>& auxb, 
                           Int *idone )
  {
     cQ263 *qinit;
     string fnm, cpath;
     cDevice *dev;
     int tmpidone[100];

     dev= dom->device();
     fnm= dev->getcpath();
     fnm= fnm+"/profiles.dat";

     //only velocity, T and P are used

     cout <<"The profile file to initilize the boundary " << bname << " is " << fnm << "\n";
     qinit = new cQ263();
     qinit->setnv(nv);
     qinit->read(fnm);
     qinit->buildgrid();
     qinit->interp(nx, nv, nb, xb, qb, tmpidone);

     delete qinit; qinit=NULL;
  }

   extern "C" void mixbc( cDomain *dom, void *rftr, string bname, Real omega, Int nx,
                          Int nv, Int nb, cAu3xView<Real>& xb, cAu3xView<Real>& qb, Int nvauxb, cAu3xView<Real>& auxb,
                          Int *idone )
  {
     string fnm, cpath, devnm, tmpstr;
     cDevice *dev;
     Int nlev, ib, jl, iv, il;
     Real *tmpq[10], y[3], xr[3], *levx[2], rmax, rmin, w;
     istringstream ss;
     string sdum;
     cField      *fld;


     fld= dom->field();


     dev= dom->device();
     fnm= dev->getcpath();
     devnm = dev->getname();

          if(bname=="MIN" || bname=="SPIN" || bname=="WIN" || bname=="NRMI") tmpstr = "INLET";
     else if(bname=="MEX" || bname=="SPEX" || bname=="WEX" || bname=="NRME") tmpstr = "EXIT";
     else                                                   tmpstr = bname; 

     fnm= fnm+ "/" + devnm + "." + tmpstr + ".mixbc";

     assert(nx==3);
     cout <<"The mixplane output to initialize boundary " << bname << " is " << fnm << "\n";
     ifstream fle;
     fle.open(fnm.c_str());
     if(!fle.good())
    {
        cout << "Error: can not open file " << fnm << "\n";
        exit(0);
    }
     getline(fle, sdum);
     ss.clear();
     ss.str(sdum);
     ss >> sdum >> nlev;
     cout << "number of levels in mixbc " << nlev << "\n";
     for(iv=0; iv<nv; iv++)
    {
        tmpq[iv] = new Real [nlev];    
    }
     levx[0] = new Real [nlev];
     levx[1] = new Real [nlev];
     rmin = big;
     rmax =-big;
     for(il=0; il<nlev; il++)
    {
       getline(fle, sdum);
       ss.clear();
       ss.str(sdum);
       ss >> levx[0][il] >> levx[1][il];
       for(iv=0; iv<nv; iv++)
      {
         ss >> tmpq[iv][il];
      }
    }
     fle.close();

     rmax = levx[1][nlev-1]; 
     rmin = levx[1][0]; 
     for(ib=0; ib<nb; ib++)
    {
        y[0] = xb(0,ib);
        y[1] = xb(1,ib);
        y[2] = xb(2,ib);
        xr[0] = y[0];
        xr[1] = sqrt(y[1]*y[1] + y[2]*y[2]);
        for(il=0; il<nlev-1; il++)
       {
           if(xr[1]<rmin)
          {
              jl=0;
              for(iv=0; iv<nv; iv++)
             {
                qb(iv,ib) = tmpq[iv][jl];   
             }
              break;
          }
           else if(xr[1]>rmax)
          {
              jl=nlev-1;
              for(iv=0; iv<nv; iv++)
             {
                qb(iv,ib) = tmpq[iv][jl];   
             }
              break;
          }
           else if(xr[1]>=levx[1][il] && xr[1]<=levx[1][il+1])
          {
              w = xr[1] - levx[1][il];
              w/= levx[1][il+1] - levx[1][il];
              for(iv=0; iv<nv; iv++)
             {
                qb(iv,ib) = tmpq[iv][il]*(1-w) + tmpq[iv][il+1]*w;
             }
              break;
          }
       }
    }

//the velocities are in ux, ur, ut, turn them into ux, uy, uz
     cCosystem    *coo;
     coo= dom->cosystem();
     coo->zvel( 0,nb, NULL, xb, qb,qb );
     fld->redim( 0,nb, qb );

     for(iv=0; iv<nv; iv++) idone[iv] = 1;

     for(iv=0; iv<nv; iv++)
    {
        delete[] tmpq[iv]; tmpq[iv]=NULL;
    }
     delete[] levx[0]; levx[0]=NULL;
     delete[] levx[1]; levx[1]=NULL;
  }

