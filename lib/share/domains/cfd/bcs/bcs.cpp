
   using namespace std;

#  include <domain/domain.h>

// extern "C" void databcs( cDomain *dom, cPdata *, cPdata *, string bgnm, Int nx, Real *x[], Int nv, Real *q[] )

   extern "C" void unibcs( cDomain *dom, void *rftr, string bname, Real omega, Int nx, Int nv, Int nb, cAu3xView<Real>& xb, 
                           cAu3xView<Real>& qb, Int nvauxb, cAu3xView<Real>& auxb, Int *idone )
  {
      Real    *q0;
      Real     x,y,z;
      Int      iv,iq;
      string cpath;
      cCosystem *coo;
      cDevice *dev= dom->device();
      cpath= dev->getcpath();
      cpath= cpath+ "/"+bname+".dat";

      cout << "READ "<<cpath<<"\n";
      q0= new Real[nv];
      ifstream fle;
      fle.open( cpath.c_str() );
      cout <<"Uniform boundary conditon for " << bname << " ";
      for( iv=0;iv<nv;iv++ )
     {
         fle >> q0[iv];
         cout << q0[iv] << " ";
     } 
      fle.close();
      cout << "\n";   
 
      for( iq=0;iq<nb;iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            qb(iv,iq)= q0[iv];
        }
     }
      delete[] q0;

      coo= dom->cosystem();
      coo->zvel( 0,nb, NULL, xb, qb,qb );

  }

   extern "C" void adiabcs( cDomain *dom, void *rftr, string bname, Real omega, Int nx, Int nv, Int nb, cAu3xView<Real>& xb,
                            cAu3xView<Real>& qb, Int nvauxb, cAu3xView<Real>& auxb, Int *idone )
  {
      cCosystem *coo= dom->cosystem();
      setv( 0,nb, nv,ZERO, qb, "h" );
      coo->frame( 0,nb, omega,xb,qb );
  }

//needs to restart auxb in the future
   extern "C" void binrestartbcs( cDomain *dom, void *rftr, string bname, Real omega, Int nx, Int nv, Int nb,
                                  cAu3xView<Real>& xb, cAu3xView<Real>& qb, Int nvauxb, cAu3xView<Real>& auxb, Int *idone )
  {
      cDevice *dev;
      string fnme;
      Int ilev, icpu;
      size_t       len,l;
      FILE        *f;
      cPdata      *dof;
      pickle_t     buf;
      Real *sxqb=NULL, *sqb=NULL, *sauxb=NULL;
      cAu3xView<Real> ql, auxbl;
      cField      *fld;
      Real tmptm;

      dev= dom->device();
      ilev= dom->level();
      fnme= dev->getcpath();
      icpu= dev->getrank();

      fnme= fnme+"/"+dev->getname()+".restart.b."+bname+"."+strc(ilev)+"."+strc(icpu);
      //fnme= "./restart/"+dev->getname()+".restart.b."+bname+"."+strc(ilev)+"."+strc(icpu);
      f= fopen(fnme.c_str(),"r");
      cout << "The restart file to restart boundary " << bname << " is " << fnme << "\n";

      l= ::fread( &tmptm,1,sizeof(tmptm),f );
      l= ::fread( &len,1,sizeof(len),f );
      buf= new pickle_v[len];
      l= ::fread(  buf,1,        len,f );

      len=0; 
      dof= new cPdata( dev );

      dof->unpickle( &len,buf );
      delete[] buf;buf= NULL; len=0;

      dof->read( nx,&sxqb,f );
      dof->read( nv,&sqb,f );
      dof->read( nvauxb,&sauxb,f );    //MAURO (There was a comment here 20130617)**********

      fclose(f);

      //ql= new Real*[nv];
      ql.subv( nv,nb, sqb );
      fld= dom->field();
      fld->redim( 0,nb, ql );    //MAURO ... REDIM


      for( int iq=0;iq<nb;iq++ )
     {
         //cout << "iq :\n";
         for( int iv=0;iv<nv;iv++ )
        {
            qb(iv,iq)= ql(iv,iq);
            //cout << qb[iv][iq] << " ";
        }
     }

      //auxbl= new Real*[nvauxb];
      auxbl.subv( nvauxb,nb, sauxb );
      for( int iq=0;iq<nb;iq++ )
     {
         //0: utau, 1: yplus, the rest is not needed
         for( int iv=0;iv<2;iv++ )
        {
           auxb(iv,iq)= auxbl(iv,iq);
        }
     }

      for(int iv=0; iv<nv; iv++)
     {
        idone[iv] = 1;
     }

//      delete[] ql;    ql=NULL;
//      delete[] auxbl; auxbl=NULL;
  }

