include ../../../Makefile.in

libs:
	echo; cd bcs;    make; cd ../../
	echo; cd grid;   make; cd ../../
	echo; cd frame;  make; cd ../../
	echo; cd init;   make; cd ../../
	echo; cd save;   make; cd ../../
#echo; cd fdneut; make; cd ../../

all: libs

clean:
	echo; cd bcs;    make clean; cd ../../
	echo; cd grid;   make clean; cd ../../
	echo; cd frame;  make clean; cd ../../
	echo; cd init;   make clean; cd ../../
	echo; cd save;   make clean; cd ../../
#echo; cd fdneut; make clean; cd ../../
