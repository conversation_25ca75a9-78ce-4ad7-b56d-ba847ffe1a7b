#ifndef _Q263_H_
#define _Q263_H_

#  include <lin/vect.h>


   class cQ263
  {
      protected:
         static const Int MAXSLN=100;
         static const Int MAXVAR=10;

         Int nlev, nsta, nv;
         Real *x[MAXSLN], *r[MAXSLN], *var[MAXSLN][MAXVAR];

         // *ut[MAXSLN], *t[MAXSLN], *p[MAXSLN], *phi[MAXSLN], *ux[MAXSLN], 
         //*ur[MAXSLN];

         Int np;
         Int **ijn, **iqn[4];
         Real *pv[MAXVAR], *px[2];

      public:
         cQ263();
         virtual ~cQ263();
         virtual void read (string );
         void setnv( Int n ) {nv = n;};

         void buildgrid();
         void interp( Int , Int , Int , Real *[], Real *[],  Int * );
         bool findclose( Int , Real *[2], Real *[2], Int , Int , Int **[4], 
                         Int *, Int * );
         void newtoniter( Real *l1, Real *l2, Real bx0[2][4], Real x, Real t);
         void parametric( Real *l1, Real *l2, Int ii, Int ij, Int **iqn[4],  
                          Real *initfx[2], Int ip, Real *tx[2] );
         bool inside( Int ip, Real *tx[2], Real *initfx[2], Int ni, Int nj, 
                      Int **iqn[4], Int *ii, Int *ij );

  };

#endif
