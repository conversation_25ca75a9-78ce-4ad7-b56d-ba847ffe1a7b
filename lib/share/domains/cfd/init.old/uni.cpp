
   using namespace std;

#  include <domain/cfd/domain.h>

   extern "C" void gradinit( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, Real *xq[], Real *q[] )
  {
      Int iv,iq;
      Real x,x2;
      for( iq=0;iq<nq;iq++ )
     {
         x= xq[0][iq];
         x2= x*x;
         for( iv=0;iv<nx;iv++ )
        {
            q[iv][iq]= x;
        }
         iv= nx;
         q[iv++][iq]= 1000+ x;
         q[iv++][iq]= 100000+ 100*x;
         for( iv=nx+2;iv<nv;iv++ )
        {
            q[iv][iq]= 0;
        }
     }
   }

   extern "C" void gradinitcyl( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, Real *xq[], Real *q[] )
  {
      Int iv,iq;
      Real x,y,z;
      for( iq=0;iq<nq;iq++ )
     {
         for( iv=0;iv<nx;iv++ )
        {
            q[iv][iq]= xq[iv][iq];
        }
         iv= nx;
         q[iv++][iq]= 1000;
         q[iv++][iq]= 100000;
         for( iv=(nx)+2;iv<nv;iv++ )
        {
            q[iv][iq]= 0;
        }
     }
   }

   extern "C" void vortexinit( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, Real *xq[], Real *q[] )
  {
      Int iv,iq;
      Real x,y,z;
      for( iq=0;iq<nq;iq++ )
     {
         q[0][iq]= xq[0][iq];
         q[1][iq]=-xq[2][iq];
         q[2][iq]= xq[1][iq];
         iv= nx;
         q[iv++][iq]= 1000;
         q[iv++][iq]= 100000;
         for( iv=(nx)+2;iv<nv;iv++ )
        {
            q[iv][iq]= 0;
        }
     }
  }

   extern "C" void sourceinit( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, Real *xq[], Real *q[] )
  {
      Int iv,iq;
      Real x,y,z;
      for( iq=0;iq<nq;iq++ )
     {
         q[0][iq]= xq[0][iq];
         q[1][iq]= xq[1][iq];
         q[2][iq]= xq[2][iq];
         iv= nx;
         q[iv++][iq]= 1000;
         q[iv++][iq]= 100000;
         for( iv=(nx)+2;iv<nv;iv++ )
        {
            q[iv][iq]= 0;
        }
     }
  }

   extern "C" void shearinit( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, Real *xq[], Real *q[] )
  {
      Int iv,iq;
      Real x,y,z;
      for( iq=0;iq<nq;iq++ )
     {
         q[0][iq]= xq[1][iq];
         q[1][iq]= 0;
         q[2][iq]= 0;
         iv= nx;
         q[iv++][iq]= 300;
         q[iv++][iq]= 100000;
         for( iv=(nx)+2;iv<nv;iv++ )
        {
            q[iv][iq]= 0;
        }
     }
  }

   extern "C" void uniinit( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, Real *xq[], Real *q[] )
  {
      Int iv,iq;
      Real x,y,z;
      cCosystem *coo;
      cDevice *dev;
      Real *q0;
      string cpath;
      dev= dom->device();
      cpath= dev->getcpath();
      cpath= cpath+ "/init.dat";
 
      coo= dom->cosystem();
      q0= new Real[nv];
      ifstream fle;
      fle.open( cpath.c_str() );
      cout <<"Uniform initial solution " << "\n";
      for( iv=0;iv<nv;iv++ )
     {
         fle >> q0[iv];
         cout << q0[iv] << " ";
     }
      fle.close();
      cout << "\n";
 
      for( iv=0;iv<nv;iv++ )
     {
         for( iq=0;iq<nq;iq++ )
        {
            q[iv][iq]= q0[iv];
        }
     }
      coo->zvel( 0,nq, NULL, xq, q,q );
      delete[] q0;

/*    for( iq=0;iq<nq;iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            cout << q[iv][iq]<<" ";
        }
         cout << "\n";
     }*/
     
  }

   extern "C" void asciinit( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, Real *xq[], Real *q[] )
  {
      Int iv,iq;
      Real x,y,z;

      string cpath;
      cDevice *dev;
      dev= dom->device();
      cpath= dev->getcpath();
      cpath= cpath+ "/asci.init.dat";
 
      ifstream fle;
      fle.open( cpath.c_str() );
      for( iq=0;iq<nq;iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            fle >> q[iv][iq];
        }
     }
  }
