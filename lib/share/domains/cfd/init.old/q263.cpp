using namespace std;

# include <q263.h>
# include <fstream>
# include <iostream>
# include <sstream>

//this is not ideal to put this function
   Real area( Real x0[2], Real x1[2], Real x2[2] );

   cQ263::cQ263()
  {
     Int il, iv;

     nlev=0;
     nsta=0;
     nv=5;

     for(il=0; il<MAXSLN; il++)
    {
       x[il] = NULL;
       r[il] = NULL;
       for(iv=0; iv<MAXVAR; iv++)
      {
         var[il][iv] = NULL;
      }
    }

     ijn=NULL;
     iqn[0]=NULL;
     iqn[1]=NULL;
     iqn[2]=NULL;
     iqn[3]=NULL;

     px[0] = NULL;
     px[1] = NULL;
     for(iv=0; iv<MAXVAR; iv++)
    {
       pv[iv]=NULL;
    }

  }

   cQ263::~cQ263()
  {
     Int il, iv, j;

     nlev=0;
     nsta=0;
     for(il=0; il<MAXSLN; il++)
    {
       delete[] x[il]; x[il]=NULL;
       delete[] r[il]; r[il]=NULL;
       for(iv=0; iv<MAXVAR; iv++)
      {
         delete[] var[il][iv]; var[il][iv]=NULL;
      }
    }

     for(il=0; il<nlev; il++)
    {
       delete ijn[il]; ijn[il]=NULL;
    }

     for(iv=0; iv<MAXVAR; iv++)
    {
       delete pv[iv]; pv[iv]=NULL;
    }

     delete px[0]; px[0]=NULL;
     delete px[1]; px[1]=NULL;


     for(j=0; j<nlev; j++)
    {
       delete[] iqn[0][j]; iqn[0][j]=NULL;
       delete[] iqn[1][j]; iqn[1][j]=NULL;
       delete[] iqn[2][j]; iqn[2][j]=NULL;
       delete[] iqn[3][j]; iqn[3][j]=NULL;
    }
     delete[] iqn[0]; iqn[0]=NULL;
     delete[] iqn[1]; iqn[1]=NULL;
     delete[] iqn[2]; iqn[2]=NULL;
     delete[] iqn[3]; iqn[3]=NULL;

  }

   void cQ263::read(string fnm)
  {
      Int il, is, iv;
      istringstream ss;
      string dum;

      ifstream fle(fnm.c_str());

      if(!(fle.good())) { cout << "ERROR: FAIL IN READING PROFILE DATA...\n"; 
                          return;};

      ss.clear();
      getline(fle, dum);
      ss.str(dum);
      ss >> nlev;

      ss.clear();
      getline(fle, dum);
      ss.str(dum);
      ss >> nsta;

      //fle >> nlev;
      //fle >> nsta;

      for(il=0; il<nlev; il++)
     {
        x[il] = new Real [nsta];
        r[il] = new Real [nsta];
      
        for(iv=0; iv<nv; iv++)
       {
          var[il][iv] = new Real [nsta]; 
       } 
     }

     for(is=0; is<nsta; is++)
    {
        for(il=0; il<nlev; il++)
       {
          ss.clear();
          getline(fle, dum);
          ss.str(dum);
          ss >> r[il][is] >> x[il][is];
          for(iv=0; iv<nv; iv++)
         {
            ss >> var[il][iv][is];
         }

          //fle >>  r[il][is] >> x[il][is] >> ux[il][is] >> ur[il][is] 
          //    >> ut[il][is] >> t[il][is] >> p[il][is];
       }
     }
      fle.close();
  }

   void cQ263::buildgrid()
  {
     Int j, i, ip0, iv;
     Int ntmp;

     np = nlev*nsta;

     ijn = new Int* [nlev];
     for(j=0; j<nlev; j++)
    {
       ijn[j] = new Int [nsta];
    }

     iqn[0] = new Int* [nlev];
     iqn[1] = new Int* [nlev];
     iqn[2] = new Int* [nlev];
     iqn[3] = new Int* [nlev];
     for(j=0; j<nlev; j++)
    {
       iqn[0][j] = new Int [nsta];
       iqn[1][j] = new Int [nsta];
       iqn[2][j] = new Int [nsta];
       iqn[3][j] = new Int [nsta];
    }


     for(iv=0; iv<nv; iv++)
    { 
       pv[iv] = new Real [np]; 
    }
     px[0] = new Real [np];
     px[1] = new Real [np];

     ntmp=0;
     for(j=0; j<nlev; j++)
    {
       for(i=0; i<nsta; i++)
      {
          ijn[j][i] = ntmp;
          ntmp++;
      }
    }

     for(j=0; j<nlev-1; j++)
    {
       for(i=0; i<nsta-1; i++)
      {
         iqn[0][j][i] = ijn[j][i];
         iqn[1][j][i] = ijn[j][i+1];
         iqn[2][j][i] = ijn[j+1][i+1];
         iqn[3][j][i] = ijn[j+1][i];
      }
    }

     for(j=0; j<nlev; j++)
    {
       for(i=0; i<nsta; i++)
      { 
         ip0 = ijn[j][i];
         px[0][ip0] = x[j][i];
         px[1][ip0] = r[j][i];
         for(iv=0; iv<nv; iv++)
        {
           pv[iv][ip0] = var[j][iv][i];
        }
         //pv[1][ip0] = ur[j][i];
         //pv[2][ip0] = ut[j][i];
         //pv[3][ip0] = t[j][i];
         //pv[4][ip0] = p[j][i];
      }
    }  

/*     ofstream fle("initslo.grid");
     for(j=0; j<nlev; j++)
    {
       fle << "\n";
       for(i=0; i<nsta; i++)
      {
         ip0 = ijn[j][i];
         fle << px[0][ip0] << " " << px[1][ip0] << "\n";
      }
       fle << "\n";
    } 
 
     for(i=0; i<nsta; i++)
    {
       fle << "\n";
       for(j=0; j<nlev; j++)
      {
         ip0 = ijn[j][i];
         fle << px[0][ip0] << " " << px[1][ip0] << "\n";
      }
       fle << "\n";
    } 

     fle.close();*/
 
  }

   void cQ263::interp( Int nx, Int nv, Int nb, Real *xb[], Real *qb[],  
                       Int *idone )
  {
      Int ib, ip1, ip2, ip3, ip4, iv;
      Int icell, jcell;
      Real *xr[2], tmpy, tmpz, tmpr;
      Real *cth, *sth, ur, ut;
      bool binside, close;
      Real s0, s1;
      Real g1, g2, g3, g4;

      xr[0] = new Real [nb];
      xr[1] = new Real [nb];
      cth = new Real [nb];
      sth = new Real [nb];

      for(ib=0; ib<nb; ib++)
     {
         tmpy = xb[1][ib];
         tmpz = xb[2][ib];
         tmpr = tmpy*tmpy + tmpz*tmpz;
         tmpr = sqrt(tmpr);
         cth[ib] = tmpz/tmpr;
         sth[ib] = tmpy/tmpr;

         xr[0][ib] = xb[0][ib];
         xr[1][ib] = tmpr;
     }

      //find the owernship of each boundary points in the grids and 
      //interpolate the vaules
      for(ib=0; ib<nb; ib++)
     { 
         binside=false;
         close=false;
         binside=inside( ib, xr, px, nsta-1, nlev-1, iqn, &icell, &jcell );
         if(binside)
        {
            parametric( &s0, &s1, icell, jcell,  iqn, px, ib, xr );
        }
         else
        {
           close=findclose(ib, xr, px, nsta-1, nlev-1, iqn, &icell, &jcell);
           if(close)
          {
             parametric( &s0, &s1, icell, jcell,  iqn, px, ib, xr );
          }
           else
          {
             cout <<"WARNING: CAN NOT FIND VOLUME CONTAINS THE BOUNDARY POINTS\n";
             s0=0.5;
             s1=0.5;
             icell = 0;
             jcell = 0;
             //exit(0);
          }
        }

        // fle << s0 << "    " << s1 << "\n";

         g1= (1-s0)*(1-s1);
         g2=    s0 *(1-s1);
         g3=    s0 *   s1;
         g4= (1-s0)*   s1;

         ip1 = iqn[0][jcell][icell];
         ip2 = iqn[1][jcell][icell];
         ip3 = iqn[2][jcell][icell];
         ip4 = iqn[3][jcell][icell];

         for(iv=0; iv<nv; iv++)
        {
           qb[iv][ib] =  g1*pv[iv][ip1] + g2*pv[iv][ip2] + 
                         g3*pv[iv][ip3] + g4*pv[iv][ip4];
        }

         //qb[0][ib] =  g1*pv[0][ip1] + g2*pv[0][ip2] + 
         //             g3*pv[0][ip3] + g4*pv[0][ip4];
         //qb[1][ib] =  g1*pv[1][ip1] + g2*pv[1][ip2] + 
         //             g3*pv[1][ip3] + g4*pv[1][ip4];
         //qb[2][ib] =  g1*pv[2][ip1] + g2*pv[2][ip2] + 
         //             g3*pv[2][ip3] + g4*pv[2][ip4];
         //qb[3][ib] =  g1*pv[3][ip1] + g2*pv[3][ip2] + 
         //             g3*pv[3][ip3] + g4*pv[3][ip4];
         //qb[4][ib] =  g1*pv[4][ip1] + g2*pv[4][ip2] + 
         //             g3*pv[4][ip3] + g4*pv[4][ip4];

         ur=qb[1][ib];
         ut=qb[2][ib];

         qb[1][ib]= cth[ib]* ut+ sth[ib]* ur;
         qb[2][ib]=-sth[ib]* ut+ cth[ib]* ur;

//         for(iv=0; iv<5; iv++) idone[iv]=1;

        /*if(nv>5)
        {
           for(iv=5; iv<nv; iv++)
          {
              qb[iv][ib] = 0;
//            idone[iv]=0;
          }
        }*/

     }
      //fle.close();

      for(iv=0; iv<nv; iv++) idone[iv] = 1;

      delete[] xr[0]; xr[0]=NULL;
      delete[] xr[1]; xr[1]=NULL;
      delete[] cth; cth=NULL;
      delete[] sth; sth=NULL;
  }

   bool cQ263::findclose( Int ip, Real *tx[2], Real *initfx[2], Int ni, Int nj,
                          Int **iqn[4], Int *ii, Int *ij )
  {
    
     Int i, j;
     Int ip1, ip2, ip3, ip4;
     Real x0[2], x1[2], x2[2], x3[2], x4[2];
     Real a0, a1;
     bool close;

     Real da, damin;

     x0[0] = tx[0][ip];
     x0[1] = tx[1][ip];

     *ii=-1;
     *ij=-1;
     close=false;
     da=999;
     damin=999;
     for(j=0; j<nj; j++)
    {
       for(i=0; i<ni; i++)
      {
         ip1 = iqn[0][j][i];
         ip2 = iqn[1][j][i];
         ip3 = iqn[2][j][i];
         ip4 = iqn[3][j][i];

         x1[0] = initfx[0][ip1]; x1[1] = initfx[1][ip1];
         x2[0] = initfx[0][ip2]; x2[1] = initfx[1][ip2];
         x3[0] = initfx[0][ip3]; x3[1] = initfx[1][ip3];
         x4[0] = initfx[0][ip4]; x4[1] = initfx[1][ip4];

         a0 = area( x4, x1, x2 );
         a0+= area( x2, x3, x4 );

         a1 = area( x0, x1, x2 );
         a1+= area( x0, x2, x3 );
         a1+= area( x0, x3, x4 );
         a1+= area( x0, x4, x1 );

         da = fabs(a1-a0);
         da = da/(a0+small);

         if(da<damin)
        {
           *ii=i;
           *ij=j;
            damin=da;
            close=true;
        }
      }
    }
      return close;
  }

   void cQ263::newtoniter( Real *l1, Real *l2, Real bx0[2][4], Real tmpx, 
                           Real tmpt )
  {
      Int it;
      Real x0, t0, b1, b2;
      Real x1, x2, x3, x4;
      Real t1, t2, t3, t4;
      Real g1, g2, g3, g4;
      Real g11, g21, g31, g41;
      Real g12, g22, g32, g42;
      Real a11, a12, a21, a22;
      Real det, dl1, dl2;

      x1=bx0[0][0]; x2=bx0[0][1]; x3=bx0[0][2]; x4=bx0[0][3];
      t1=bx0[1][0]; t2=bx0[1][1]; t3=bx0[1][2]; t4=bx0[1][3];

      *l1= 0.5;
      *l2= 0.5;

      for(it=0; it<100; it++)
     {
        g1= (1-(*l1))*(1-(*l2));
        g2=    (*l1) *(1-(*l2));
        g3=    (*l1) *   (*l2);
        g4= (1-(*l1))*   (*l2);
        g11=       -(1-(*l2));
        g21=        (1-(*l2));
        g31=           (*l2);
        g41=          -(*l2);
        g12=-(1-(*l1));
        g22=   -(*l1) ;
        g32=    (*l1);
        g42= (1-(*l1));
        x0= g1*x1+ g2*x2+ g3*x3+ g4*x4;
        t0= g1*t1+ g2*t2+ g3*t3+ g4*t4;
        b1= tmpx-x0;
        b2= tmpt-t0;
        a11= g11*x1+ g21*x2+ g31*x3+ g41*x4;
        a12= g12*x1+ g22*x2+ g32*x3+ g42*x4;
        a21= g11*t1+ g21*t2+ g31*t3+ g41*t4;
        a22= g12*t1+ g22*t2+ g32*t3+ g42*t4;
        det= a11*a22- a12*a21;
        dl1= ( a22*b1- a12*b2 )/det;
        dl2= (-a21*b1+ a11*b2 )/det;
        *l1= *l1+ 0.8*dl1;
        *l2= *l2+ 0.8*dl2;

//        cout << "it " << it << " *l1: " << *l1 << " " << *l2 << "\n";
     }
  }

   void cQ263::parametric( Real *l1, Real *l2, Int ii, Int ij, Int **iqn[4], 
                           Real *initfx[2], Int ip, Real *tx[2] )
  {
      Int ip1, ip2, ip3, ip4;
      Real bx[2][4];

      Real x[2];

      ip1 = iqn[0][ij][ii];
      ip2 = iqn[1][ij][ii];
      ip3 = iqn[2][ij][ii];
      ip4 = iqn[3][ij][ii];

      bx[0][0] = initfx[0][ip1]; bx[1][0] = initfx[1][ip1];
      bx[0][1] = initfx[0][ip2]; bx[1][1] = initfx[1][ip2];
      bx[0][2] = initfx[0][ip3]; bx[1][2] = initfx[1][ip3];
      bx[0][3] = initfx[0][ip4]; bx[1][3] = initfx[1][ip4];

      x[0] = tx[0][ip];
      x[1] = tx[1][ip];

      newtoniter( l1, l2, bx, x[0], x[1]);

      if(fabs(*l1)>5 || fabs(*l2)>5)
     {
        /*cout << "Error: Can not find a parametric coordinates for this point 
                 in this quad\n";
        cout << *l1 << " " << *l2 << "\n";
        cout << x[0] << " " << x[1] << "\n";
        cout << bx[0][0] << " " << bx[1][0] << "\n"
             << bx[0][1] << " " << bx[1][1] << "\n"
             << bx[0][2] << " " << bx[1][2] << "\n"
             << bx[0][3] << " " << bx[1][3] << "\n";
        exit(0);*/
        *l1 = 0.5;
        *l2 = 0.5;
     }
  }

   bool cQ263::inside( Int ip, Real *tx[2], Real *initfx[2], Int ni, Int nj, 
                       Int **iqn[4], Int *ii, Int *ij )
  {
     Int i, j;
     Int ip1, ip2, ip3, ip4;
     Real x0[2], x1[2], x2[2], x3[2], x4[2];
     Real a0, a1;
     bool breakout;
     bool in;

     x0[0] = tx[0][ip];
     x0[1] = tx[1][ip];

     *ii=-1;
     *ij=-1;
     breakout=false;
     in=false;
     for(j=0; j<nj; j++)
    {
       for(i=0; i<ni; i++)
      {
         ip1 = iqn[0][j][i];
         ip2 = iqn[1][j][i];
         ip3 = iqn[2][j][i];
         ip4 = iqn[3][j][i];

         x1[0] = initfx[0][ip1]; x1[1] = initfx[1][ip1];
         x2[0] = initfx[0][ip2]; x2[1] = initfx[1][ip2];
         x3[0] = initfx[0][ip3]; x3[1] = initfx[1][ip3];
         x4[0] = initfx[0][ip4]; x4[1] = initfx[1][ip4];

         a0 = area( x4, x1, x2 );
         a0+= area( x2, x3, x4 );

         a1 = area( x0, x1, x2 );
         a1+= area( x0, x2, x3 );
         a1+= area( x0, x3, x4 );
         a1+= area( x0, x4, x1 );

         if(fabs(a1-a0)<1e-16)
        {
           *ii=i;
           *ij=j;
            in=true;
            breakout=true;
            break;
        }
      }
        if(breakout) break;
    }
      return in;

  }

   Real area( Real x0[2], Real x1[2], Real x2[2] )
  {
     Real a;
     Real dx0[2], dx1[2];

     dx0[0] = x1[0] - x0[0];
     dx0[1] = x1[1] - x0[1];

     dx1[0] = x2[0] - x0[0];
     dx1[1] = x2[1] - x0[1];

     a = (dx0[0]*dx1[1] - dx0[1]*dx1[0]);
     a*= 0.5;
     a = fabs(a);

     return a;
  }

