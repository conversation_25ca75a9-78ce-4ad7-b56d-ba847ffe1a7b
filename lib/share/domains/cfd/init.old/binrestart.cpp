   using namespace std;

#  include <domain/cfd/domain.h>

   extern "C" void binrestartinit( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, Real *xq[], Real *q[] )
  {
      cDevice     *dev;
      cPdata      *dof,*pts;
      pickle_t     buf;
      size_t       len,l;
      string       fnme;
      Int          icpu,ilev;
      Int          i,j;
      FILE        *f;
      Real        *sxpl=NULL,*sxql=NULL,*sql=NULL,*sauxl=NULL;
      Real       **xpl=NULL,**xql=NULL,**ql=NULL,**auxl=NULL;
      cField      *fld;

      dev= dom->device();
      ilev= dom->level();

      fnme= dev->getcpath();
      icpu= dev->getrank();
      fnme= fnme+"/"+dev->getname()+".restart.q."+strc(ilev)+"."+strc(icpu);
      f= fopen(fnme.c_str(),"r");

      l= fread( &len,1,sizeof(len),f );
      buf= new pickle_v[len];
      l= fread(  buf,1,        len,f );

      len=0;
      dof= new cPdata( dev );
      pts= new cPdata( dev );

      dof->unpickle( &len,buf );
      pts->unpickle( &len,buf );
      delete[] buf;buf= NULL; len=0;

      pts->read( nx,   &sxpl,f );
      dof->read( nx,   &sxql,f );
      dof->read( nv,    &sql,f );
//    dof->read( naux,&lauxl,f );
      fclose(f);

      ql= new Real*[nv];
      fld= dom->field();
      
      subv( nv,nq, sql, ql );
      fld->redim( 0,nq, ql );

      for( j=0;j<nv;j++ )
     {
         for( i=0;i<nq;i++ )
        {
            q[j][i]= ql[j][i];
        }
     }

      pts->destroy(   &sxpl );
      dof->destroy(   &sxql );
      dof->destroy(    &sql );
//    dof->destroy(  &sauxl );
      delete pts; pts=NULL;
      delete dof; dof=NULL;
      delete[] ql;

  }
