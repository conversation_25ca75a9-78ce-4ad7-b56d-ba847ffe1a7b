   using namespace std;

#  include <domain/cfd/domain.h>
#  include <q263.h>
#  include <lookup/lookup.h>
#  include <assert.h>

   extern "C" void profileinit( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, Real *xq[], Real *q[] )
  {
      cDevice      *dev;
      string       fnme;
      ifstream      fle;
      Int           i,j,n;
      cField       *fld;
      cCosystem    *coo;
      cLookup      *lut;
      Real *sy,*y[3];
      Real ***x,***v;
      Real yp[2],vp[5],dvp[2][5];


      fld= dom->field();
      coo= dom->cosystem();
      dev= dom->device();

      fnme= dev->getcpath();
      fnme= fnme+"/profiles.dat";

      cout << "should open "<<fnme<<"\n";

      fle.open( fnme.c_str() );
      assert( fle.good() );
      fle >> n ;

      allc3( 2,2,n, &x );
      allc3( 5,2,n, &v );

      for( i=0;i<n;i++ )
     {
         for( int j=0;j<nv;j++ ){ v[i][0][j]= 0.; };
         fle >> x[i][0][1]>> x[i][0][0]>>  v[i][0][0]>> v[i][0][1]>> v[i][0][2]>> v[i][0][3]>> v[i][0][4];
     }
      Int n1;
      fle >> n1; assert( n1 == n );
      for( i=0;i<n;i++ )
     {
         for( int j=0;j<nv;j++ ){ v[i][0][j]= 0.; };
         fle >> x[i][1][1]>> x[i][1][0]>>  v[i][1][0]>> v[i][1][1]>> v[i][1][2]>> v[i][1][3]>> v[i][1][4];
     }
      fle.close();

      lut= new cLookup();
      lut->build( nv,2,n, x,v );

      sy= new Real[nx*nq]; 
      subv( nx,nq, sy,y );
      coo->bcoor( 0,nq, xq,y );

      //cout << "values\n";
      for( i=0;i<nq;i++ )
     {
         line2( i,y,yp );
         lut->interp( yp, vp, dvp[0],dvp[1] );
         for( j=0;j<nv;j++ )
        {
            q[j][i]= vp[j];
        }
     }

      coo= dom->cosystem();
      coo->zvel( 0,nq, NULL, xq, q,q );


      cout << "TODO: BUGFIX IN DEL3 (lut.h)\n";
/*    del3(  2,2,n, &x );
      del3( nv,2,n, &v );*/

      delete lut; lut=NULL;
      delete[] sy; sy=NULL;
  }

   extern "C" void q263init( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, Real *xq[],
                             Real *q[] )
  {
     cQ263 *qinit;
     string fnm, cpath;
     cDevice *dev;
     int idone[100];

     dev= dom->device();
     fnm= dev->getcpath();
     fnm= fnm+"/profiles.dat";

     cout <<"The profile file to initilize the solution is " << fnm << "\n";
     qinit = new cQ263();
     qinit->setnv(nv);
     qinit->read(fnm);
     qinit->buildgrid();
     qinit->interp(nx, nv, nq, xq, q, idone);

     delete qinit; qinit=NULL;
  }

