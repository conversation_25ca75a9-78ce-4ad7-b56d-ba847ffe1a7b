
   using namespace std;
#  include <iniParser.h>
#  include <domain/domain.h>
// include <device/device.h>

//   extern "C" void jm28frame( cDomain *dom, void *rftr, Real *omega, Int ng, string *bgnm, Real *omegb )
//  {
//      Int ig;
//      string fname;
//      ifstream fle;
//      string syn=";()";
//      Int    iarg=0,argc=0;
//      string *argv=NULL;
//
//      cout << "JM28 FRAME PLUGIN\n";
//      cTabData *tab; 
//      tab= new cTabData();
//      cDevice *dev= dom->device();
//      dev->get( tab );
//      string misc;
//      tab->get( "misc",&misc );
//      cout << misc << "\n";
//      parse( misc,&argc,&argv,syn );
//      iarg=inlst( string("frame-speed"),argc,argv );
//     *omega=0;
//      if( iarg != -1 )
//     {
//         conv( argv[iarg+1],omega );
//     }
//      cout << omega<<"\n";
//      for( ig=0;ig<ng;ig++ )
//     {
//         omegb[ig]=*omega;
//         if( bgnm[ig] == "CASING" ){ omegb[ig]=0; };
//         cout << bgnm[ig]<<" "<<omegb[ig]<<"\n";
//     }
//
//      delete[] argv; argv= NULL;
//      delete tab; tab=NULL;
//  }

   extern "C" void jm28frame( cDomain *dom, void *rftr, Real *omega, Int ng, string *bgnm, Real *omegb )
  {
      cout << "JM28 FRAME PLUGIN\n";
      Int ig;
      cDevice *dev= dom->device();
      IniParser parser;
      string item, val, str_sec;
      parser.parseFromFile("input.au3x");

      str_sec = dev->getname();
      item = "frame-speed";
      val = parser.getValue(str_sec, item);
      *omega = stod(val);
      for( ig=0;ig<ng;ig++ )
     {
         omegb[ig]=*omega;
         if( bgnm[ig] == "CASING" ){ omegb[ig]=0; };
         cout << bgnm[ig]<<" "<<omegb[ig]<<"\n";
     }
  }

