
   using namespace std;

#  include <domain/domain.h>

   extern "C" void trivia( cDomain *dom, void *rftr, Real *omega, Int ng, string *bgnm, Real *omegb )
  {

       Int ig;
      *omega= 0.;
       for( ig=0;ig<ng;ig++ )
      {
          omegb[ig]= 0.;
      }
  }

   extern "C" void fileframe( cDomain *dom, void *rftr, Real *omega, Int ng, string *bgnm, Real *omegb )
  {
      Int ig;
      string fname;
      ifstream fle;

//    stophere();
      cDevice *dev= dom->device();
      fname= dev->getcpath()+"/frames.dat";

      fle.open( fname.c_str() );
      fle >> *omega;
      for( ig=0;ig<ng;ig++ )
     {
         fle >> omegb[ig];
     }
  }

