include ../../../../Makefile.in

CSRC=   frame.cpp \
	jm28.cpp

FSRC=
COBJ=$(CSRC:.cpp=.o)
FOBJ=$(FSRC:.F=.o)
OBJS=$(COBJ) $(FOBJ)

BINS=libframe.so

$(BINS): $(OBJS)
	$(PCCMP) $(COPT) $(OBJS) $(ORGL) $(ENGL) $(SYSL) $(LIBF) -shared -o $@

clean:
	rm -f $(OBJS) $(BINS)

.cpp.o:
	$(PCCMP) $(COPT) -I../inc $(ORGI) $(ENGI) $(SYSI) $(PETSCI) -fPIC -o $@ -c $<

.F.o:
	$(PFCMP) $(FOPT) -I../inc $(ORGI) $(ENGI) $(SYSI) $(GUII)  -o $@ -c $<

