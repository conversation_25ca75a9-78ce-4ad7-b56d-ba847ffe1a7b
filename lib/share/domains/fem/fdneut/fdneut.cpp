   using namespace std;



//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <misc/fdneut.h>
#  include <device/worker/dom.h>
#  include <domain/fem/domain.h>
#  include <cosystem/cosystems.h>

   void fdntelems( cFeDomain *fdm, Int *nek, Int *jek, Int *nep, Int *neq, Int *neaux, Int ipmsk[][MxNPSs], Int iqmsk[][MxNPSs] );
   void fdntbnds( cFeDomain *fdm, Int *nek, Int *jek, Int *nep, Int *neq, Int *neaux, Int ipmsk[][MxNPSs], Int iqmsk[][MxNPSs] );

   extern "C" void fdntruntime( cDom *dev, void *, void *, cCase *cse, cDomain **dmn )
  {
     *dmn= new cFeDomain();
  }

   extern "C" void fdntdomain( cDom *dev, void *, void *, cCase *, cDomain **dmn )
  {      
      cElement    *tmp;
      Int            nlv,ilv;
      Int            n;
      Real           dum0,dum1,dum2;
      ifstream       fle;

      Int           *neg=NULL,*npg=NULL,*igt[2]={NULL,NULL};
      string        *gnms=NULL;
      string        *bnms=NULL;
      string         fnme;
      string         bnm;
      Real          *rwrk=NULL;
      Real          *xwrk=NULL;
      Int          **iwrk=NULL;
      Int           *jwrk[MxNPSs];
      Int           *pwrk0[2];
      Int           *pwrk1[9];

      Int            nbk,nek;
      Int            nq,np,ng,nx,mb;
      Int            jg,kg,iq,ip,ig,ib,ie,ix,jp,je,is,il,kp;
      Int            iek,ick;
      Int            npe; // number of patch element
      Int            iqmsk[MxNSk][MxNPSs];
      Int            ipmsk[MxNSk][MxNPSs];

      Int            ne[MxNSk];
      Int            nep[MxNSk];
      Int            neq[MxNSk];
      Int            neaux[MxNSk];
      Int            jek[MxNSk];

      Int           *siem[MxNSk];
      Int           *siep[MxNSk],*iep[MxNSk][MxNPSs];
      Int           *sieq[MxNSk],*ieq[MxNSk][MxNPSs];
      Real          *sauxe[MxNSk],*auxe[MxNSk][MxNPSs];
      Real          *sxb[MxNBG],*xb[MxNBG][3];

      Int            nb[MxNBG][MxNSk];
      Int            nbp[MxNSk];
      Int            nbq[MxNSk];
      Int            nbaux[MxNSk];
      Int            jbk[MxNSk];

      Int           *sibp[MxNBG][MxNSk],*ibp[MxNBG][MxNSk][MxNPSs];
      Int           *sibq[MxNBG][MxNSk],*ibq[MxNBG][MxNSk][MxNPSs];
      Int           *sibb[MxNBG][MxNSk],*ibb[MxNBG][MxNSk][MxNPSs];
      Real          *sauxb[MxNBG][MxNSk],*auxb[MxNBG][MxNSk][MxNPSs];
 
      Int           *ihlp=NULL;
      Int           *bhlp=NULL;
      cFeDomain     *fdm[10];
      cCosystem     *coo;
      cSolid        *sl;
    
      nlv=1;
      for( ilv=0;ilv<nlv;ilv++ )
     { 
         coo= new cCartCosystem();
   
         fdm[ilv]= new cFeDomain();
         fdm[ilv]->assgndev( dev );
   
   
   // default element kinds from FDNEUT
   
         fdntelems( fdm[ilv], &nek, jek, nep,neq, neaux, ipmsk,iqmsk );
         fdntbnds( fdm[ilv], &nbk, jbk, nbp,nbq, nbaux, ipmsk,iqmsk );
         setv( (Int)0,(Int)MxNSk, (Int)0, ne );
   
   // assign element kinds
         fnme= dev->getcpath();
   
         fnme= fnme+ "/"+dev->getname();
//       fnme= fnme+"."+strc(ilv);
         fdneutimg( fnme, &nx, &np, &rwrk, &ng, &neg, &npg, igt, &iwrk, &gnms );
   
         cout << fnme << "\n";
         coo->validate( nx );

         sl= new cElastic(); 
         sl->sizes( coo );

         Int nm=0;
         Int im=0;
         string *mnms= new string[ng];
         setv( (Int)0,ng, (string)"",mnms );
         for( ig=0;ig<ng;ig++ )
        {
            if( igt[0][ig] == igt[1][ig] )
           {
               im= inlst( gnms[ig], nm,mnms );
               if( im == -1 )
              {
                  mnms[nm]= gnms[ig];
                  nm++;
              }
           }
        }
         for( im=0;im<nm;im++ )
        {
            cout << "material "<<im<<" is "<<mnms[im]<<"\n";
        }

         sl->props( nm,mnms );
         fdm[ilv]->assgnv( nx );
         fdm[ilv]->assgnsld( sl );
         fdm[ilv]->assgncoo( coo );
         fdm[ilv]->assgnxp( np,rwrk );
         nq= np;
         xwrk=new Real[nx*nq]; 
         Real *xq[3]; subv( nx,nq, xwrk,xq );
         Real *xp[3]; subv( nx,np, rwrk,xp );
         for( ix=0;ix<nx;ix++ )
        {
            for( iq=0;iq<nq;iq++ )
           {
               xq[ix][iq]= xp[ix][iq];
           }
        }
         fdm[ilv]->assgnxq( np,xwrk );
   
         for( ig=0;ig<ng;ig++ )
        {
            if( igt[0][ig] == igt[1][ig] )
           {
               iek= igt[0][ig];
               iek= jek[iek];
               ne[iek]+= neg[ig];
           }      
        }
   
         for( iek=0;iek<nek;iek++ ) 
        {
            siep[iek]= NULL;
            sieq[iek]= NULL;
            siep[iek]= new Int[ne[iek]*nep[iek]]; 
            sieq[iek]= new Int[ne[iek]*neq[iek]]; 
            siem[iek]= new Int[ne[iek]]; 
            setv( (Int)0,ne[iek], (Int)-1, siem[iek] );
            sauxe[iek]= new Real[ne[iek]*neaux[iek]]; 
            subv( nep[iek],ne[iek], siep[iek],iep[iek] );
            subv( neq[iek],ne[iek], sieq[iek],ieq[iek] );
            subv( neaux[iek],ne[iek], sauxe[iek], auxe[iek] );
            setv( 0,ne[iek]*neaux[iek], 0.001, sauxe[iek] ); 
            ne[iek]=0;
        }
   
         for( ig=0;ig<ng;ig++ )
        {
            if( igt[0][ig] == igt[1][ig] )
           {
               subv( npg[ig],neg[ig], iwrk[ig], jwrk );
               iek= igt[0][ig];
               iek= jek[iek];
               im= inlst( gnms[ig], nm,mnms );
               for( je=0;je<neg[ig];je++ )
              {
                  siem[iek][ne[iek]]= im;
                  for( kp=0;kp<nep[iek];kp++ )
                 {
                     jp= ipmsk[iek][kp];
                     iep[iek][jp][ne[iek]]= jwrk[kp][je];
                 }
                  for( kp=0;kp<neq[iek];kp++ )
                 {
                     jp= iqmsk[iek][kp];
                     ieq[iek][jp][ne[iek]]= jwrk[kp][je];
                 }
                  ne[iek]++;
              }
           }
        }
   
         for( iek=0;iek<nek;iek++ )
        {
            fdm[ilv]->assgniep( iek, ne[iek], siep[iek],sieq[iek],siem[iek] );
            fdm[ilv]->assgnauxe( iek, sauxe[iek] );
        }
   
   // patches
/*       pwrk0[0]= new Int[np];
         pwrk0[1]= new Int[np];
         pwrk1[0]= new Int[ne[0]]; setv( (Int)0,ne[0], (Int)-1, pwrk1[0] );
         pwrk1[1]= new Int[ne[0]]; setv( (Int)0,ne[0], (Int)-1, pwrk1[1] );
         pwrk1[2]= new Int[ne[0]]; setv( (Int)0,ne[0], (Int)-1, pwrk1[2] );
         pwrk1[3]= new Int[ne[0]]; setv( (Int)0,ne[0], (Int)-1, pwrk1[3] );
         pwrk1[4]= new Int[ne[0]]; setv( (Int)0,ne[0], (Int)-1, pwrk1[4] );
         pwrk1[5]= new Int[ne[0]]; setv( (Int)0,ne[0], (Int)-1, pwrk1[5] );
         pwrk1[6]= new Int[ne[0]]; setv( (Int)0,ne[0], (Int)-1, pwrk1[6] );
         pwrk1[7]= new Int[ne[0]]; setv( (Int)0,ne[0], (Int)-1, pwrk1[7] );
   
         npe=0;
         fdm[ilv]->patchesc( 0,2, pwrk0, &npe, pwrk1 );
   
         tmp= new cb6();
         iek= fdm[ilv]->addelem(tmp);
         el[iek]= tmp;
         nek++;
   
         ne[iek]=  npe;
         nep[iek]= el[iek]->getnp();
   
         siep[iek]= new Int[ne[iek]*nep[iek]]; 
         subv( nep[iek],ne[iek], siep[iek],iep[iek] );
         sauxe[iek]= new Real[ne[iek]*el[iek]->getnaux()]; 
         setv( 0,ne[iek]*el[iek]->getnaux(), 1.e-3, sauxe[iek] ); 
         subv( el[iek]->getnaux(), ne[iek], sauxe[iek], auxe[iek] );
   
         if( ne[iek] > 0 )
        {
            fdm[ilv]->assgniep( iek, ne[iek], siep[iek] ); 
            fdm[ilv]->assgnauxe( iek, sauxe[iek] ); 
        }
               
         npe=0;
         fdm[ilv]->patchesl( 0, iek, &npe, pwrk1 );
         delete[] pwrk0[0];
         delete[] pwrk0[1];
         delete[] pwrk1[0];
         delete[] pwrk1[1];
         delete[] pwrk1[2];
         delete[] pwrk1[3];
         delete[] pwrk1[4];
         delete[] pwrk1[5];
         delete[] pwrk1[6];
         delete[] pwrk1[7];
*/
// boundary groups

         ihlp= new Int[np];

         Int **igb;
         Int  *igk;
         Int  *jgb;
         Int  *lgb;
         lgb= new Int[ng];
         igb= new Int*[ng];
         igk= new Int[ng];
         jgb= new Int[ng];
         setv( (Int)0,ng, (Int)0, lgb );
         for( ig=0;ig<ng;ig++ )
        {
            igb[ig]= new Int[ng];
            setv( (Int)0,ng, (Int)0, igb[ig] );
        } 
         bnms= new string[ng];
         string btyp[2]={"CONSTRAINED","LOADED"};
         string bpl[2]={ "libbcs.so",
                         "libbcs.so"};
         string bpo[2]={"databcs","databcs"};
         setv( (Int)0,(Int)ng, string(""),bnms );
         Int mb=0;
         for( ig=0;ig<ng;ig++ )
        {
            cout << "FDNEUT group "<<ig<<" "<<gnms[ig]<<"\n";
            string *line=NULL;
            Int     nw=0;
            string syn=" .,#%";
            parse( gnms[ig],&nw,&line,syn );
            if( nw > 1 )
           {
               cout << "boundary type "<<line[0]<<" boundary name "<<line[1]<<"\n";
               Int kb=inlst( line[0], 2,btyp );
               Int jb=inlst( line[1],mb,bnms );
               if( jb == -1 )
              {
                  jb= lgb[mb];
                  igb[mb][jb]= ig;
                  lgb[mb]++;
                  igk[mb]= kb;
                  jgb[mb]=fdm[ilv]->addbgrp( line[1],bpl[kb],bpo[kb] ); 
                  mb++;
              }
           }
            delete[] line; line=NULL; nw=0;
        }

         for( jg=0;jg<mb;jg++ )
        {
            setv( (Int)0,np, (Int)0, ihlp );
            for( kg=0;kg<lgb[jg];kg++ )
           {
               ig=igb[jg][kg];
               bhlp= new Int[neg[ig]];
               subv( npg[ig],neg[ig], iwrk[ig],jwrk );
               setv( (Int)0,neg[ig], (Int)1, bhlp );
               marktp( 0,neg[ig], npg[ig],jwrk,bhlp, 1, ihlp );
               delete[] bhlp;
           }
            Int n=0;
            for( ip=0;ip<np;ip++ )
           {
               if( ihlp[ip] != 0 )
              {
                 n++;
              } 
           }
            kg= igk[jg];
            nb[jg][kg]= n;
            ig= jgb[jg];
            sibp[ig][kg]= new Int[n];
            sibq[ig][kg]= new Int[n];
            sibb[ig][kg]= new Int[n];
            sxb[ig]= new Real[nx*n]; subv( nx,n, sxb[ig],xb[ig] );
            n=0;
            for( ip=0;ip<np;ip++ )
           {
               if( ihlp[ip] != 0 )
              {
                  sibp[ig][kg][n]=ip;
                  sibq[ig][kg][n]=ip;
                  sibb[ig][kg][n]=n; // this only holds for point boundary groups - modify for
                                     // face boundaries
                  for( ix=0;ix<nx;ix++ )
                 {
                     xb[ig][ix][n]= xp[ix][ip];
                 }
                  n++;
              }
           }
            fdm[ilv]->assgnxb( ig,n,sxb[ig] );
            fdm[ilv]->assgnibp( ig,kg,n,sibp[ig][kg],sibq[ig][kg],sibb[ig][kg] );
            cout << "this group should be associated to "<<n<<" points\n";
        }

         for( ig=0;ig<ng;ig++ )
        {
            delete[] igb[ig]; igb[ig]= NULL;
        }
         delete[] igb;
         delete[] jgb;
         delete[] lgb;
         delete[] igk;
         delete[] neg;
         delete[] npg;
         delete[] igt[0];
         delete[] igt[1];
         for( ig=0;ig<ng;ig++ )
        {
            delete[] iwrk[ig];
        }
         delete[] iwrk;
         delete[] ihlp;
         delete[] bnms;
         delete[] gnms;
         delete[] mnms;
   
     }
      
/*    for( ilv=0;ilv<nlv-1;ilv++ )
     {
         fdm[ilv]->attach( fdm[ilv+1] );
     }*/

     *dmn= fdm[0];

  }

