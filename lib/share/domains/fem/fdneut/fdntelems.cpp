
   using namespace std;

#  include <domain/fem/domain.h>

   void fdntelems( cFeDomain *fdm, Int *nek, Int *jek, Int *nep, Int *neq, Int *neaux, Int ipmsk[][MxNPSs], Int iqmsk[][MxNPSs] )
  {

      Int            iek;
      cElement    *tmp;
     *nek= -1;

      setv( (Int)0,(Int)MxNSk, (Int)-1, jek );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nep );
      setv( (Int)0,(Int)MxNSk, (Int) 0, neq );
      setv( (Int)0,(Int)MxNSk, (Int) 0, neaux );

      tmp= new cq43();
      iek= fdm->addelem(tmp);
     *nek= max(*nek,iek );
      nep[iek]= tmp->getnp();
      neq[iek]= tmp->getnq();
      neaux[iek]= tmp->getnaux();
      jek[1]= iek; 
      ipmsk[iek][0]=0;
      ipmsk[iek][1]=1;
      ipmsk[iek][2]=2;
      ipmsk[iek][3]=3;

      iqmsk[iek][0]=0;
      iqmsk[iek][1]=1;
      iqmsk[iek][2]=2;
      iqmsk[iek][3]=3;

      tmp= new ct33();
      iek= fdm->addelem(tmp);
     *nek= max(*nek,iek );
      nep[iek]= tmp->getnp();
      neaux[iek]= tmp->getnaux();
      jek[2]= iek; 
      ipmsk[iek][0]=0;
      ipmsk[iek][1]=1;
      ipmsk[iek][2]=2;

      iqmsk[iek][0]=0;
      iqmsk[iek][1]=1;
      iqmsk[iek][2]=2;
        
      tmp= new cq83();
      iek= fdm->addelem(tmp);
     *nek= max(*nek,iek );
      nep[iek]= tmp->getnp();
      neq[iek]= tmp->getnq();
      neaux[iek]= tmp->getnaux();
      jek[3]= iek; 

      ipmsk[iek][0]=0;
      ipmsk[iek][1]=1;
      ipmsk[iek][2]=3;
      ipmsk[iek][3]=2;
      ipmsk[iek][4]=4;
      ipmsk[iek][5]=5;
      ipmsk[iek][6]=7;
      ipmsk[iek][7]=6;

      iqmsk[iek][0]=0;
      iqmsk[iek][1]=1;
      iqmsk[iek][2]=3;
      iqmsk[iek][3]=2;
      iqmsk[iek][4]=4;
      iqmsk[iek][5]=5;
      iqmsk[iek][6]=7;
      iqmsk[iek][7]=6;

      tmp= new cp63();
      iek= fdm->addelem(tmp);
     *nek= max(*nek,iek );
      nep[iek]= tmp->getnp();
      neq[iek]= tmp->getnq();
      neaux[iek]= tmp->getnaux();
      jek[4]= iek; 

      ipmsk[iek][0]=0;
      ipmsk[iek][1]=1;
      ipmsk[iek][2]=2;
      ipmsk[iek][3]=3;
      ipmsk[iek][4]=4;
      ipmsk[iek][5]=5;

      iqmsk[iek][0]=0;
      iqmsk[iek][1]=1;
      iqmsk[iek][2]=2;
      iqmsk[iek][3]=3;
      iqmsk[iek][4]=4;
      iqmsk[iek][5]=5;

      tmp= new ct43();
      iek= fdm->addelem(tmp);
     *nek= max(*nek,iek );
      nep[iek]= tmp->getnp();
      neq[iek]= tmp->getnq();
      neaux[iek]= tmp->getnaux();
      jek[5]= iek; 

      ipmsk[iek][0]=0;
      ipmsk[iek][1]=1;
      ipmsk[iek][2]=2;
      ipmsk[iek][3]=3;

      iqmsk[iek][0]=0;
      iqmsk[iek][1]=1;
      iqmsk[iek][2]=2;
      iqmsk[iek][3]=3;

    (*nek)++;
  }

   void fdntbnds( cFeDomain *fdm, Int *nek, Int *jek, Int *nep, Int *neq, Int *neaux, Int ipmsk[][MxNPSs], Int iqmsk[][MxNPSs] )
  {

      Int            iek;
      cElement    *tmp;
     *nek= -1;

      setv( (Int)0,(Int)MxNSk, (Int)-1, jek );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nep );
      setv( (Int)0,(Int)MxNSk, (Int) 0, neq );
      setv( (Int)0,(Int)MxNSk, (Int) 0, neaux );

      tmp= new cc13();
      iek= fdm->addblem(tmp);
     *nek= max(*nek,iek );
      nep[iek]= tmp->getnp();
      neq[iek]= tmp->getnq();
      neaux[iek]= tmp->getnaux();
      jek[0]= iek; 
      ipmsk[iek][0]=0;
      iqmsk[iek][0]=0;

      tmp= new cl13();
      iek= fdm->addblem(tmp);
     *nek= max(*nek,iek );
      nep[iek]= tmp->getnp();
      neaux[iek]= tmp->getnaux();
      jek[1]= iek; 
      ipmsk[iek][0]=0;
      iqmsk[iek][0]=0;

    (*nek)++;
  }
