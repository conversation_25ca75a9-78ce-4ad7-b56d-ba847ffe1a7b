   using namespace std;

#  include <device/client/dom.h>
#  include <domain/domain.h>
#  include <sort/proto.h>
#  include <fstream>
   extern "C" void runtimeloader( cDom *dev, void *, void *, cCase *cse, cDomain **dmn )
  {
     *dmn= new cDomain();
  }

   extern "C" void loader( cDom *dev, void *, void *, cCase *cse, cDomain **dmn )
  {
      ifstream  fle;
      string    fnme;
      string    path;

      cElement *elm;
      Int       iek,ibk;
      Int       n;
      Int       i,j,idum;
      Int      *iwrk0,*iep[MxNPSs];
      Int      *iwrk1,*ieq[MxNPSs];
      Real     *rwrk0,*xp[2];
      Real     *rwrk1,*xq[2];

      cout << "ok, got inside the plugin\n";
      path=dev->getcpath();
      cout << "look for data.dat inside "<<path<<"\n";

     *dmn= new cDomain();
    (*dmn)->assgnn( 2,2 );

      fnme= path+"/data.dat";
      fle.open( fnme.c_str() );

      fle >> n;
      rwrk0=new Real[2*n];
      rwrk1=new Real[2*n];
      subv( 2,n, rwrk0,xp );
      subv( 2,n, rwrk1,xq );
      for( i=0;i<n;i++ )
     {
         fle >> xp[0][i]>> xp[1][i];
         xq[0][i]= xp[0][i];
         xq[1][i]= xp[1][i];
 
     }
    (*dmn)->assgnxp( n,rwrk0 );
    (*dmn)->assgnxq( n,rwrk1 );

      elm= new cL2(); 
      iek= (*dmn)->addelem( elm );
      fle >> n;
      iwrk0= new Int[ 2*n ];
      iwrk1= new Int[ 2*n ];
      subv( 2,n, iwrk0,iep );
      subv( 2,n, iwrk1,ieq );
      for( i=0;i<n;i++ )
     {
         for( j=0;j<2;j++ )
        {
            fle >> idum;
            iep[j][i]=idum-1; 
            ieq[j][i]=idum-1; 
        }
     }
   (*dmn)->assgniep( iek,n,iwrk0,iwrk1 );

      elm= new cL3(); 
      iek= (*dmn)->addelem( elm );
      fle >> n;
      iwrk0= new Int[ 3*n ];
      iwrk1= new Int[ 3*n ];
      subv( 3,n, iwrk0,iep );
      subv( 3,n, iwrk1,ieq );
      for( i=0;i<n;i++ )
     {
         for( j=0;j<3;j++ )
        {
  
            fle >> idum;
            iep[j][i]=idum-1; 
            ieq[j][i]=idum-1; 
        }
     }
   (*dmn)->assgniep( iek,n,iwrk0,iwrk1 );

      elm= new cQ4(); 
      iek= (*dmn)->addelem( elm );
      fle >> n;
      iwrk0= new Int[ 4*n ];
      iwrk1= new Int[ 4*n ];
      subv( 4,n, iwrk0,iep );
      subv( 4,n, iwrk1,ieq );
      for( i=0;i<n;i++ )
     {
         for( j=0;j<4;j++ )
        {
            fle >> idum;
            iep[j][i]=idum-1; 
            ieq[j][i]=idum-1; 
        }
     }
   (*dmn)->assgniep( iek,n,iwrk0,iwrk1 );

      elm= new cP1(); 
      ibk= (*dmn)->addbndy( elm );
      fle >> n;
      iwrk0= new Int[ n ];
      iwrk1= new Int[ n ];
      subv( 1,n, iwrk0,iep );
      subv( 1,n, iwrk1,ieq );
      for( i=0;i<n;i++ )
     {
         fle >> idum;
         iep[0][i]=idum-1; 
         ieq[0][i]=idum-1; 
     }
   (*dmn)->assgnibp( ibk,n,iwrk0,iwrk1 );

     fle.close();

  }

   extern "C" void xpart(  cDom *dev, cDomain *dmn, Real tld, Real *cld, Int *ipcpu, Int *ilcpu, Int *ncpu, Int *icpu, 
                           Real *cpul )
  {
      FILE     *f;
      string    path,fnme;

      Int       nq,nx;
      Real     *sxq,*xq[3];
      Real     *qcst;
      Int      *qcpu;

      Int      *iprm;
      Int       iq,jq,ix,ic,jc,ic0,jc0;

      dmn->getxq( &nq,&nx,&sxq );
      subv( nx,nq, sxq,xq );

      dmn->getqcost( &qcst );
      dmn->getqcpu( &qcpu );

      iprm= new  Int[nq];

      hsort( nq,xq[0], iprm ); 

      jc=0;
      ic=*ipcpu;
      icpu[jc]= ic;
      cpul[jc]= 0;
      for( jq=0;jq<nq;jq++ )
     {
         if( cld[ic] > tld )
        {
            jc++;
            ic++;
            icpu[jc]= ic;
        }
         iq=        iprm[jq];
         cld[ic]+=  qcst[iq];
         cpul[jc]+= qcst[iq];
         qcpu[iq]=  jc;
     }

      path= dev->getcpath();
      fnme= path+ "/part.dat";
      f= fopen( fnme.c_str(),"w" );
      fwrite( (void*)qcpu,sizeof(*qcpu),(size_t)nq,f );
      fclose( f );

     *ipcpu= ic;
     *ncpu= ++jc;
// next device will need a new logical cpu
    (*ilcpu)+= (*ncpu);

      delete[] iprm;

  }

   extern "C" void ypart(  cDom *dev, cDomain *dmn, Real tld, Real *cld, Int *ipcpu, Int *ilcpu, Int *ncpu, Int *icpu, 
                           Real *cpul )
  {
      FILE     *f;
      string    path,fnme;

      Int       nq,nx;
      Real     *sxq,*xq[3];
      Real     *qcst;
      Int      *qcpu;

      Int      *iprm;
      Int       iq,jq,ix,ic,jc,ic0,jc0;

      dmn->getxq( &nq,&nx,&sxq );
      subv( nx,nq, sxq,xq );

      dmn->getqcost( &qcst );
      dmn->getqcpu( &qcpu );

      iprm= new  Int[nq];

      hsort( nq,xq[1], iprm ); 

      jc=0;
      ic=*ipcpu;
      icpu[jc]= ic;
      cpul[jc]= 0;
      for( jq=0;jq<nq;jq++ )
     {
         if( cld[ic] > tld )
        {
            jc++;
            ic++;
            icpu[jc]= ic;
        }
         iq=        iprm[jq];
         cld[ic]+=  qcst[iq];
         cpul[jc]+= qcst[iq];
         qcpu[iq]=  jc;
     }

      path= dev->getcpath();
      fnme= path+ "/part.dat";
      f= fopen( fnme.c_str(),"w" );
      fwrite( (void*)qcpu,sizeof(*qcpu),(size_t)nq,f );
      fclose( f );

     *ipcpu= ic;
     *ncpu= ++jc;
// next device will need a new logical cpu
    (*ilcpu)+= (*ncpu);

      delete[] iprm;

  }
