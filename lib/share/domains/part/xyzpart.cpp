   using namespace std;

#  include <device/worker/dom.h>
#  include <fstream>

   extern "C" void xpart(  cDomain *dmn, cCase *cse, Int *n, Int **inode, Int **icore, Int **icpu )
  {
      FILE     *f;
      string    path,fnme;
      cDevice  *dev;

      Int       nq,nx;
      Real     *sxq,*xq[3];
      Real     *qcst;
      Int      *qcpu;

      Real      token,dtoken;
      Real      qc;
      Int      *iprm;
      Int       i,j;

      dev=dmn->device();

      dmn->getxq( &nq,&nx,&sxq );
      subv( nx,nq, sxq,xq );
      iprm= new  Int[nq];
      hsort( nq,xq[0], iprm ); 

      dmn->getqcost( &qcst );
      qc=0;
      for( i=0;i<nq;i++ )
     {
         qc+= qcst[i];
     }
      dmn->getqcpu( &qcpu );

      token=qc/(Real)nq;
      dtoken= token/(Real)nq;
      dtoken/=2;

      cse->costs( qc,token,dtoken );

      for( i=0;i<nq;i++ )
     {
         j= iprm[i];
         qcpu[j]= cse->local();
//       cout << j << " "<<qcpu[j]<<"\n";
         cse->load( n,inode,icore,icpu, qcst[j] );

     }
      cse->finalize( n,inode,icore,icpu );
     *n= cse->local();
      cout << "XYZ PART SAYS "<<*n<<"\n";

      path= dev->getcpath();
      fnme= path+ "/part.dat";
      f= fopen( fnme.c_str(),"w" );
      fwrite( (void*)qcpu,sizeof(*qcpu),(size_t)nq,f );
      fclose( f );

      delete[] iprm;

  }
