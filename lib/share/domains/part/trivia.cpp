   using namespace std;

#  include <device/worker/dom.h>
#  include <fstream>

   extern "C" void trivia(  cDom *dev, cDomain *dmn, Real tld, Real *cld, Int *ipcpu, Int *ilcpu, Int *ncpu, Int *icpu, 
                            Real *cpul )
  {
      FILE     *f;
      string    path,fnme;

      Int       nq,nx;
      Real     *sxq,*xq[3];
      Real     *qcst;
      Int      *qcpu;

      Int      *iprm;
      Int       iq,jq,ix,ic,jc,ic0,jc0;

      dmn->getxq( &nq,&nx,&sxq );

      cout << "at entry to trivia "<<nq<<"\n";
      cout << "target load "<<tld<<"\n";

      dmn->getqcost( &qcst );
      dmn->getqcpu( &qcpu );

      jc=0;
      ic=*ipcpu;
      icpu[jc]= ic;
      cpul[jc]= 0;
      for( iq=0;iq<nq;iq++ )
     {
         cld[ic]+=  qcst[iq];
         cpul[jc]+= qcst[iq];
         qcpu[iq]=  jc;
     }

/*    path= dev->getcpath();
      fnme= path+ "/part.dat";
      f= fopen( fnme.c_str(),"w" );
      fwrite( (void*)qcpu,sizeof(*qcpu),(size_t)nq,f );
      fclose( f );*/

     *ipcpu= ic;
     *ncpu= ++jc;
// next device will need a new logical cpu
    (*ilcpu)+= (*ncpu);

      cout << "trivia finished\n";

  }

