include ../../../Makefile.in

CSRC=   xyzpart.cpp \
        metis_part.cpp \
	trivia.cpp

FSRC=
COBJ=$(CSRC:.cpp=.o)
FOBJ=$(FSRC:.F=.o)
OBJS=$(COBJ) $(FOBJ)

BINS=libpart.so

$(BINS): $(OBJS)
	$(PCCMP) $(COPT) $(OBJS) $(ORGL) $(ENGL) $(SYSL) $(LIBF) -shared -o $@

clean:
	rm -f $(OBJS) $(BINS)

.cpp.o:
	$(PCCMP) $(COPT) -I../inc $(ORGI) $(ENGI) $(SYSI) $(HYPREI) $(PETSCI) -fPIC -o $@ -c $<

.F.o:
	$(PFCMP) $(FOPT) -I../inc $(ORGI) $(ENGI) $(SYSI) $(GUII)  -o $@ -c $<

