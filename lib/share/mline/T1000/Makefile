include ../../../Makefile.in

CSRC=   utils.cpp \
	compressor/in.cpp \
	compressor/out.cpp \
	compressor/visl.cpp \
	compressor/rlt.cpp \
	dcompressor/in.cpp \
	dcompressor/out.cpp \
	dcompressor/visl.cpp \
	dcompressor/rlt.cpp \
	dpcompressor/in.cpp

FSRC=

COBJ=$(CSRC:.cpp=.o)
FOBJ=$(FSRC:.F=.o)

OBJS=$(COBJ) $(FOBJ)

BINS=../libT1000.so

$(BINS): $(OBJS)
	$(CCMP) $(COPT) $(OBJS) $(ORGL) $(ENGL) $(SYSL) $(LIBF) -shared -o $@

clean:
	rm -f $(OBJS) $(BINS)

.cpp.o:
	$(CCMP) $(COPT) $(ORGI) $(ENGI) $(SYSI) -o $@ -c $<
.F.o:
	$(FCMP) $(FOPT) $(ORGI) $(ENGI) $(SYSI) -o $@ -c $<
                                                                     
