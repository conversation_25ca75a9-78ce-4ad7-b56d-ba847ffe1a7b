   using namespace std;

#  include <sstream>
#  include <mline/mline.h>
#  include <mline/block/arotor.h>
#  include <mline/block/astator.h>
#  include <mline/block/agap.h>
#  include <gas/jgas.h>
#  include <geo/2d/spline.h>
#  include <aero/compressor/loss/liebl.h>

   extern "C" void T1000dcompressorin( cMlineDev *dev, void *, void *, void *, 
#define _DECL_
#include                  <mline/args.h>
#include                  <mline/dargs.h>
#undef  _DECL_
                          );

   extern "C" void T1000dpcompressorin( cMlineDev *dev, void *, void *, void *, 
#define _DECL_
#include                  <mline/args.h>
#include                  <mline/dargs.h>
#include                  <mline/dpargs.h>
#undef  _DECL_
                          )
  {
		
      T1000dcompressorin( dev,NULL,NULL,NULL, wdot, pr, minl, mext, gas, ml, bld, nv, naux, nwrk, 
                          nrbvrs, nsbvrs, ngbvrs, rbvnms, sbvnms, gbvnms, bvrs, nbr, nrr, nsr, ngr, 
                          irbld, isbld, igbld, xq, q, aux, stgs, nstgs ,dbvrs, dxq, dq, daux, drhs );

      for( Int i=0;i<(*nwrk);i++ )
     {
         dbvrs1[i]= new Real[1000];
         setv( (Int)0,(Int)1000, 0.,dbvrs1[i] );
     }
      for( Int i=0;i<NXQ;i++) 
     {         
         dxq1[i]= new Real[(*nbr)+1];     
         setv( 0,(*nbr)+1, 0.,dxq1[i] );
     }
      for( Int i=0;i<3*(*nv);i++) 
     {         
         dq1[i]= new Real[(*nbr)+1]; 
         setv( 0,(*nbr)+1, 0.,dq1[i] );
     }
      for( Int i=0;i<3*(*naux);i++) 
     {
         daux1[i]= new Real[(*nbr)+1];
         setv( 0,(*nbr)+1, 0.,daux1[i] );
     }
      for( Int i=0;i<20;i++) 
     {
         drhs1[i]= new Real[(*nbr)+1];
         setv( 0,(*nbr), 0.,drhs1[i] );
     }

  }

