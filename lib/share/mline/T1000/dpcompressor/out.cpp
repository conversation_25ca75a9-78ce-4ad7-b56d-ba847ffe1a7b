   using namespace std;

#  include <mline/mline.h>
#  include <geo/2d/spline.h>
#     include <iomanip>

	void results( cIdGas *gas, ofstream *fle, Real *xq[], Real *q[], Real *aux[], Real *bvrs[],
					  Int *stgs[], Int nbr, Int nrr, Int nsr, Int *irbld, Int *isbld);
	void percpert(cIdGas *gas, Real *xq[], Real *q[], Real *aux[], Real *bvrs[],
					  Real *dxq[], Real *dq[], Real *daux[], Real *dbvrs[],
					  Int *stgs[], Int nbr, Int nrr, Int nsr, Int *irbld, Int *isbld);

   extern "C" void T1000dcompressorout( cMlineDev *dev, void *, void *, void *,
#define _DECL_
#include                  <mline/args.h>
#include                  <mline/dargs.h>
#undef  _DECL_
                          )
  {
      Int ib;
      ofstream fle;
      ofstream cfle, hfle, mfle;
      Real     y[2],dy[2],m,d;

		Int wno;
		wno= 10*(*wdot);
		cout << "wno= " << wno << "\n";

		string rname;
		stringstream out;
		out << wno;
		rname = out.str();


  // output results (just in case)
		rname= "dresults"+rname+".dat"; 
      fle.open( &(rname[0]) );
      fle.setf( ios_base::scientific );
      fle.width( 14 );
      fle.precision( 9 );

		results( (*gas), &fle, xq, q, aux, bvrs, stgs, (*nbr), (*nrr), (*nsr), (*irbld), (*isbld) );
		cout << rname << " saved successfully\n";
      fle.close();
		
  // output perturbations
		fle.open("distortions/results_perturbations.dat");
		fle.setf( ios_base::scientific );
      fle.width( 14 );
      fle.precision( 9 );

		results( (*gas), &fle, dxq, dq, daux, dbvrs, stgs, (*nbr), (*nrr), (*nsr), (*irbld), (*isbld) );
		cout << "distortions/results_perturbations.dat" << " saved successfully\n";
      fle.close();

  // perturbations in percent
		percpert( (*gas), xq, q, aux, bvrs, dxq, dq, daux, dbvrs, stgs, (*nbr), (*nrr), (*nsr), (*irbld), (*isbld) );

 
  }

