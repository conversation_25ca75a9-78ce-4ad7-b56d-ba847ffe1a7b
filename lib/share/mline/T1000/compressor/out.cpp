   using namespace std;

#  include <mline/mline.h>
#  include <geo/2d/spline.h>
#     include <iomanip>

	void dumpsol( cIdGas *gas, Real *x[], Real *q[], Real *aux[], Real *data[], Int nbr,
					  Int nv, Int naux, string prefix );
	void results( cIdGas *gas, ofstream *fle, Real *xq[], Real *q[], Real *aux[], Real *bvrs[],
					  Int *stgs[], Int nbr, Int nrr, Int nsr, Int *irbld, Int *isbld);

   extern "C" void T1000compressorout( cMlineDev *dev, void *, void *, void *,
#define _DECL_
#include                  <mline/args.h>
#undef  _DECL_
                          )
  {
      Int ib;
      ofstream fle;
      ofstream cfle, hfle, mfle;
      Real     y[2],dy[2],m,d;

		Int wno;
		wno= 10*(*wdot);
		cout << "wno= " << wno << "\n";

		string rname;
		stringstream out;
		out << wno;
		rname = out.str();


		rname= "results"+rname+".dat";

      fle.open( &(rname[0]) );
      fle.setf( ios_base::scientific );
      fle.width( 14 );
      fle.precision( 9 );

		results( (*gas), &fle, xq, q, aux, bvrs, stgs, (*nbr), (*nrr), (*nsr), (*irbld), (*isbld) );
		cout << rname << " saved successfully\n";

		string prefix;
		prefix= "steady-state/steady-";
		dumpsol( *gas, xq, q, aux, bvrs, *nbr, *nv, *naux, prefix );
 
  }
