   using namespace std;

#  include <sstream>
#  include <mline/mline.h>
#  include <mline/block/arotor.h>
#  include <mline/block/astator.h>
#  include <mline/block/agap.h>
#  include <gas/jgas.h>
#  include <geo/2d/spline.h>
#  include <aero/compressor/loss/liebl.h>

	void checkflip( Real *wrk, Int *flip);
	void bflip( Real *qi, Real *qo, Real *wrk );


   extern "C" void T1000compressorin( cMlineDev *dev, void *, void *, void *, 
#define _DECL_
#include                  <mline/args.h>
#include                  <mline/dargs.h>
#undef  _DECL_
                          )
  {
		
      Int                                         n;
      Int                                        jb,i1,i2;
      Real                                      *x[2];
      cMlineBlock                               *tmp;
      ifstream mlfle, fle, bfle;
		ifstream vufle;
      string                                     line, bname;
      stringstream                               strm;
      Real                                       omega,etap,ar,dh,sigma,dalpha,um,bf,wf,wloss,adev,thr;
		
  // Artyom's thingies
		Int                                        nbp, ibp; //number of bladed passages (gap-blade-gap)
		Int                                        i, j, k;
		Real                                       *tqi, *tqo, *tauxi, *tauxo, tploss, twrk[50];
		Real                                       xle, xte, rle, rte, V1, V2, A1, A2, dum;
		Real   t0in, p0in;
      Real imp;
      imp= 6894.75729/0.45359237;

		Int nbleed;    // no of stage AFTER which it bleeds
		Real bleed;    // bleed of massflow in %
		string rcond, scond;


      cout << "ascii fixed plugin invoked by "<<dev<<" adapted for T1000\n";
  // the gas
		
  	 (*gas)= new cIdGas();
//((cJGas*)*gas)->props("");
	 (*nv)= (*gas)->getnv();
	 (*naux)= (*gas)->getnaux();
	 cout << "nv= " << (*nv) << "\n";

	 tqi= new Real[(*nv)];
	 tqo= new Real[(*nv)];
	 tauxi= new Real[(*naux)];
	 tauxo= new Real[(*naux)];		

  // read the block definitions and data
		
		(*bld)= new cMlineBlock*[100];
      setv( 0,100, (cMlineBlock*)NULL, (*bld) );
		
		(*nwrk)=  50;
		(*nrbvrs)=(*nwrk); 
		(*nsbvrs)=(*nwrk); 
		(*ngbvrs)=(*nwrk); 
      for( Int i=0;i<(*nwrk);i++ )
     {
         bvrs[i]= new Real[1000];
         setv( (Int)0,(Int)1000, 0.,bvrs[i] );
     }



		fle.open("bladelist.dat");
      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> (*nrr ) >> (*nsr );      
		nbp= (*nrr) + (*nsr);
		(*ngr )= nbp * 2;
		(*nbr)= (*nrr)+ (*ngr)+ (*nsr);


     *irbld= new Int[(*nrr)]; 
     *isbld= new Int[(*nsr)]; 
     *igbld= new Int[(*ngr)]; 

		cout << "there are " << nbp << " physical blades to be analyzed.\n";
		cout << "of them are " << *nrr << " rotors, " << *nsr << " stators and " << *ngr << " gaps.\n";

		(*nstgs)= (*nrr);
      cout << (*nstgs) <<" stages\n";
		for( Int i=0;i<(*nrr);i++ )
     {
			stgs[i]= new Int[4];
	  }

  // creating blocks:
  // ROTORS
		vufle.open("vu59_angles_rot");
		getline( vufle,line ); strm.clear(); strm.str( line );
		strm >> rcond;
		for(i=0; i<(*nrr); i++)
	  {
			Real leth, teth, chord;
         tmp= new cMlineARotor();      

			fle >> bname;
			bname= "Blade_wrk_"+bname;
			bfle.open(&(bname[0]));
			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> jb >> i1 >> i2;
			
			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> twrk[ 5] >> twrk[ 6] >> twrk[ 7] >> twrk[ 8] >> twrk[ 9] >> twrk[10] 
				  >> twrk[11] >> twrk[12] >> twrk[13] >> twrk[14] >> twrk[15] >> twrk[16]
				  >> leth >> teth >> chord;
	  
	  // getting vu59-info
			if(rcond=="yes")
		  {
				getline( vufle,line ); strm.clear(); strm.str( line );
				strm >> twrk[7] >> twrk[8] >> twrk[14] >> twrk[12] >> twrk[11] >> chord;
				
				twrk[7]=  twrk[7]/rad;
				twrk[8]=  twrk[8]/rad;
		  //twrk[14]= twrk[14]/rad;
				twrk[14]= 0.5*(twrk[7]+twrk[8]);
				twrk[11]= 1/twrk[11];
				
				Real a1= fabs(twrk[7]-twrk[8])/2;   
				twrk[13]=  (0.5*chord*tan(a1) + 0.5*(leth+teth)) / chord  ;
		  }
	  // vu-59 done
	  
			twrk[15]= twrk[15]/chord;
			twrk[16]= twrk[16]/chord;

			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> tqi[2] >> tqo[2] >> tqi[3] >> tqo[3] >> tqi[0] >> tqo[0] >> tqi[1] >> tqo[1];
			tqi[4]= 0.;
			tqo[4]= 0.;

			(*gas)->auxv( tqi, tauxi );
			(*gas)->auxv( tqo, tauxo );

			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> xle >> xte >> rle >> rte;

			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> twrk[17];

			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> twrk[18];
         tmp->index( jb,i,i1,i2 );

			
			Int flip;
			checkflip( twrk , &flip);
			if (flip<0)
		  {			bflip( tqi, tqo, twrk );	  }
 
			liebl( (*gas), tqi, tauxi, tqo, tauxo, &tploss, twrk );
			if (flip<0)
		  {			bflip( tqi, tqo, twrk );	  }


			for(j=0; j<20; j++)
		  {
				bvrs[j][jb]= twrk[j];
		  }
			
       (*irbld)[i]= jb;
       (*bld)[jb]= tmp;
	  	   bfle.close();

		 stgs[i][0]= i1;         // inlet station
		 stgs[i][1]= i1+4;       // outlet station
		 stgs[i][2]= jb;         // inlet block
		 stgs[i][3]= jb+3;       // outlet station

	  }
		vufle.close();

  // STATORS
		vufle.open("vu59_angles_stat");
		getline( vufle,line ); strm.clear(); strm.str( line );
		strm >> scond;
		for(i=0; i<(*nsr); i++)
	  {
         tmp= new cMlineAStator();      
			Real leth, teth, chord;
			fle >> bname;
			bname= "Blade_wrk_"+bname;
			bfle.open(&(bname[0]));
			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> jb >> i1 >> i2;
			
			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> twrk[ 5] >> twrk[ 6] >> twrk[ 7] >> twrk[ 8] >> twrk[ 9] >> twrk[10] 
				  >> twrk[11] >> twrk[12] >> twrk[13] >> twrk[14] >> twrk[15] >> twrk[16]
				  >> leth >> teth >> chord;
	  
	  // getting vu59-info
	  if(scond=="yes")
		  {
				getline( vufle,line ); strm.clear(); strm.str( line );
				strm >> twrk[7] >> twrk[8] >> twrk[14] >> twrk[12] >> twrk[11] >> chord;
				
				twrk[7]=  twrk[7]/rad;
				twrk[8]=  twrk[8]/rad;
		  //twrk[14]= twrk[14]/rad;
				twrk[14]= 0.5*(twrk[7]+twrk[8]);
				twrk[11]= 1/twrk[11];
				
				Real a1= fabs(twrk[7]-twrk[8])/2;   
				twrk[13]=  (0.5*chord*tan(a1) + 0.5*(leth+teth)) / chord  ;
		  }
	  // vu-59 done
	  
			twrk[15]= twrk[15]/chord;
			twrk[16]= twrk[16]/chord;

			twrk[16]= 0.;   // no tipgap for stators


			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> tqi[2] >> tqo[2] >> tqi[3] >> tqo[3] >> tqi[0] >> tqo[0] >> tqi[1] >> tqo[1];
			(*gas)->auxv( tqi, tauxi );
			(*gas)->auxv( tqo, tauxo );

			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> xle >> xte >>  rle >> rte;

			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> dum;

			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> twrk[18];

         tmp->index( jb,i,i1,i2 );


			Int flip;
			checkflip( twrk , &flip);
			if (flip<0)
		  {			bflip( tqi, tqo, twrk );	  }

			liebl( (*gas), tqi, tauxi, tqo, tauxo, &tploss, twrk );
			if (flip<0)
		  {			bflip( tqi, tqo, twrk );	  }

			for(j=0; j<20; j++)
		  {
				bvrs[j][jb]= twrk[j];
		  }
			
       (*isbld)[i]= jb;
       (*bld)[jb]= tmp;
		   bfle.close();
	  }
		vufle.close();

		fle.close();

  // GAPS
		fle.open("gaplist.dat");
		getline( fle,line ); strm.clear(); strm.str( line );
		strm >> (*ngr);		
      for( Int i=0;i<(*ngr);i++ )
     {
	  //cout << "reading gap block "<<i<<"\n";
         tmp= new cMlineAGap();      
         getline( fle,line ); strm.clear(); strm.str( line );
         strm >> jb >> i1 >> i2;
         tmp->index( jb,i,i1,i2 );

         getline( fle,line ); strm.clear(); strm.str( line );
         strm >> V1 >> V2 >> A1 >> A2;
			
         bvrs[ 5][jb]= V1;
         bvrs[ 6][jb]= V2;
         bvrs[ 9][jb]= A1;
         bvrs[10][jb]= A2;
       (*igbld)[i]= jb;
       (*bld)[jb]= tmp;
     } 
		fle.close();

	
  // creating Q, XQ and AUX arrays

      for( Int i=0;i<NXQ;i++) 
     {
         xq[i]= new Real[(*nbr)+1];
     }
      for( Int i=0;i<3*(*nv);i++) 
     {
         q[i]= new Real[(*nbr)+1];
     }
      for( Int i=0;i<3*(*naux);i++) 
     {
         aux[i]= new Real[(*nbr)+1];
         setv( 0,(*nbr)+1, 0.,aux[i] );
     }


  // read the meanline definition
 	   mlfle.open("mline_definition");
		getline( mlfle,line ); strm.clear(); strm.str( line );
		strm >> (*wdot) >> (*minl) >> bf >> t0in >> p0in >> nbleed >> bleed;
		getline( mlfle,line ); strm.clear(); strm.str( line );
		strm >> n;
		cout << n << " points on the meanline definition\n";
		cout << "NBR+1=   " << *nbr+1 << "\n";
		x[0]= new Real[n]; 
		x[1]= new Real[n]; 
		for( Int i=0;i<n;i++ )
	  {
			getline( mlfle,line ); strm.clear(); strm.str( line );
			strm >> x[0][i] >> x[1][i] >> xq[3][i] >> xq[4][i]
				  >> q[0][i] >> q[1][i] >> q[2][i] >> q[3][i];
			q[4][i]= 0.;
	  }
		(*ml)= new cSpline();
		((cSpline*)*ml)->build( 1,n, 2,x, NULL,NULL );
		mlfle.close();

		for(i=0; i<(*nbr)+1; i++)
	  {
			xq[1][i]= x[0][i];
			xq[2][i]= x[1][i];
			xq[7][i]= (*wdot);
			xq[8][i]= bf;   
			xq[9][i]= 0.;
			q[(*nv)+2][i]= t0in;
			q[(*nv)+3][i]= p0in;
	  }
		xq[9][nbleed*6]= bleed;
  //cout << "Blockage factor is not YET inserted into ""XQ"" \n";

  delete[] x[0];
  delete[] x[1];

  // initial values at the flow stations

		
		cout << "\n####################################################\n";
		cout << "Input plugin operational, running at:\n";
		cout << "NRT=   " << fabs(bvrs[5][ stgs[0][2] ])/xq[2][ stgs[0][2] ] * 60/pi2 / sqrt(t0in) << "\n";
		cout << "MRTP=  " << *wdot * imp * sqrt(t0in) / p0in << "\n";
		cout << "####################################################\n\n";
  //std::exit(0);

			
 
  }

	void checkflip( Real *wrk, Int *flip)
  {
  // if camber_up returns 1, otherwise 0
		if(wrk[7]-wrk[8] > 0)
	  {			*flip= 1;	     }
		else
	  {	
			*flip= -1;
			cout << "The blade is originally camber-down! Flip it!\n";
	  }
		
  }
	
	void bflip( Real *qi, Real *qo, Real *wrk )
  {
  // flips metal angles, frame speed, tangential velocities
		cout << "Flipping the blade... (g1, g2, v1, v2, ut1, ut2)\n";
		wrk[0]*= -1;

		wrk[5]*= -1;
		wrk[6]*= -1;
		wrk[7]*= -1;
		wrk[8]*= -1;

		qi[1]*= -1;
		qo[1]*= -1;
		
  }
