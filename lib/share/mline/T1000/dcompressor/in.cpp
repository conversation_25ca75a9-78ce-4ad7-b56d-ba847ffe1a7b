   using namespace std;

#  include <sstream>
#  include <mline/mline.h>
#  include <mline/block/arotor.h>
#  include <mline/block/astator.h>
#  include <mline/block/agap.h>
#  include <gas/jgas.h>
#  include <geo/2d/spline.h>
#  include <aero/compressor/loss/liebl.h>


   extern "C" void T1000dcompressorin( cMlineDev *dev, void *, void *, void *, 
#define _DECL_
#include                  <mline/args.h>
#include                  <mline/dargs.h>
#undef  _DECL_
                          )
  {
		
      Int                                         n;
      Int                                        jb,i1,i2;
      Real                                      *x[2];
      cMlineBlock                               *tmp;
      ifstream mlfle, fle, bfle;
		ifstream vufle;
      string                                     line, bname;
      stringstream                               strm;
      Real                                       omega,etap,ar,dh,sigma,dalpha,um,bf,wf,wloss,adev,thr;
		
  // Artyom's thingies
		Int                                        nbp, ibp; //number of bladed passages (gap-blade-gap)
		Int                                        i, j, k;
		Real                                       xle, xte, rle, rte, V1, V2, A1, A2;
		Real   t0in, p0in;
      Real imp;
      imp= 6894.75729/0.45359237;

		Int nbleed;    // no of stage AFTER which it bleeds
		Real bleed;    // bleed of massflow in %

      cout << "ascii fixed plugin invoked by "<<dev<<" adapted for T1000\n";
  // the gas
		
  	 (*gas)= new cIdGas();
//((cJGas*)*gas)->props("");
	 (*nv)= (*gas)->getnv();
	 (*naux)= (*gas)->getnaux();
	 cout << "nv= " << (*nv) << "\n";


  // read the block definitions and data
		
		(*bld)= new cMlineBlock*[100];
      setv( 0,100, (cMlineBlock*)NULL, (*bld) );
		
		(*nwrk)=  40;
		(*nrbvrs)=(*nwrk); 
		(*nsbvrs)=(*nwrk); 
		(*ngbvrs)=(*nwrk); 
      for( Int i=0;i<(*nwrk);i++ )
     {
         bvrs[i]= new Real[1000];
         dbvrs[i]= new Real[1000];
         setv( (Int)0,(Int)1000, 0.,bvrs[i] );
         setv( (Int)0,(Int)1000, 0.,dbvrs[i] );
     }



		fle.open("bladelist.dat");
      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> (*nrr ) >> (*nsr );      
		nbp= (*nrr) + (*nsr);
		(*ngr )= nbp * 2;
		(*nbr)= (*nrr)+ (*ngr)+ (*nsr);


     *irbld= new Int[(*nrr)]; 
     *isbld= new Int[(*nsr)]; 
     *igbld= new Int[(*ngr)]; 

		cout << "there are " << nbp << " physical blades to be analyzed.\n";
		cout << "of them are " << *nrr << " rotors, " << *nsr << " stators and " << *ngr << " gaps.\n";

		(*nstgs)= (*nrr);
      cout << (*nstgs) <<" stages\n";
		for( Int i=0;i<(*nrr);i++ )
     {
			stgs[i]= new Int[4];
	  }

  // creating blocks:
  // ROTORS
		for(i=0; i<(*nrr); i++)
	  {
			Real leth, teth, chord;
         tmp= new cMlineARotor();      
			fle >> bname;
			bname= "Blade_wrk_"+bname;
			bfle.open(&(bname[0]));
			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> jb >> i1 >> i2;

			tmp->index( jb,i,i1,i2 );

       (*irbld)[i]= jb;
       (*bld)[jb]= tmp;
	  	   bfle.close();

		 stgs[i][0]= i1;         // inlet station
		 stgs[i][1]= i1+4;       // outlet station
		 stgs[i][2]= jb;         // inlet block
		 stgs[i][3]= jb+3;       // outlet station
	  }

  // STATORS
		for(i=0; i<(*nsr); i++)
	  {
         tmp= new cMlineAStator();      
			Real leth, teth, chord;
			fle >> bname;
			bname= "Blade_wrk_"+bname;
			bfle.open(&(bname[0]));
			getline( bfle,line ); strm.clear(); strm.str( line );
			strm >> jb >> i1 >> i2;
		
			tmp->index( jb,i,i1,i2 );

       (*isbld)[i]= jb;
       (*bld)[jb]= tmp;
		   bfle.close();
	  }

		fle.close();

  // GAPS
		fle.open("gaplist.dat");
		getline( fle,line ); strm.clear(); strm.str( line );
		strm >> (*ngr);		
      for( Int i=0;i<(*ngr);i++ )
     {
         tmp= new cMlineAGap();      
         getline( fle,line ); strm.clear(); strm.str( line );
         strm >> jb >> i1 >> i2;
         tmp->index( jb,i,i1,i2 );

         getline( fle,line ); strm.clear(); strm.str( line );

       (*igbld)[i]= jb;
       (*bld)[jb]= tmp;
     } 
		fle.close();
 
	
  // creating Q, XQ and AUX arrays

      for( Int i=0;i<NXQ;i++) 
     {
         xq[i]= new Real[(*nbr)+1];
     }
      for( Int i=0;i<3*(*nv);i++) 
     {
         q[i]= new Real[(*nbr)+1];
     }
      for( Int i=0;i<3*(*naux);i++) 
     {
         aux[i]= new Real[(*nbr)+1];
         setv( 0,(*nbr)+1, 0.,aux[i] );
     }


  // creating dQ, dXQ and dAUX arrays
		cout << "creating dxq\n";
		for( Int i=0;i<NXQ;i++) 
	  {         
			dxq[i]= new Real[(*nbr)+1];     
			setv( 0,(*nbr)+1, 0.,dxq[i] );
	  }
		
		cout << "creating dq\n";
		for( Int i=0;i<3*(*nv);i++) 
	  {         
			dq[i]= new Real[(*nbr)+1]; 
			setv( 0,(*nbr)+1, 0.,dq[i] );
	  }
		
		cout << "creating daux\n";
		for( Int i=0;i<3*(*naux);i++) 
	  {
			daux[i]= new Real[(*nbr)+1];
			setv( 0,(*nbr)+1, 0.,daux[i] );
	  }

		cout << "creating drhs (assuming max number of drhs'es is 20)\n";
		for( Int i=0;i<20;i++) 
	  {
			drhs[i]= new Real[(*nbr)+1];
			setv( 0,(*nbr), 0.,drhs[i] );
	  }

  // //////////////////////////////
  // read the steady-state solution
		cout << "reading the steady-state solution\n";
		Int nn;
  // xq
		fle.open("steady-state/steady-xq.dat");
		getline( fle,line ); strm.clear(); strm.str( line );
		strm >> nn;		
		cout << "nn= " << nn << "\n";
		for(i=0; i<nn; i++)
	  {
			getline( fle,line ); strm.clear(); strm.str( line );
			for(j=0; j<NXQ; j++)
		  {
				strm >> xq[j][i];
		  }
	  }
		fle.close();
		cout << "xq read\n";

  // q
		fle.open("steady-state/steady-q.dat");
		getline( fle,line ); strm.clear(); strm.str( line );
		strm >> nn;		

		for(i=0; i<nn; i++)
	  {
			getline( fle,line ); strm.clear(); strm.str( line );
			for(j=0; j<(*nv)*3; j++)
		  {
				strm >> q[j][i];
		  }
	  }
		fle.close();
		cout << "q read\n";		

  // aux
		fle.open("steady-state/steady-aux.dat");
		getline( fle,line ); strm.clear(); strm.str( line );
		strm >> nn;		

		for(i=0; i<nn; i++)
	  {
			getline( fle,line ); strm.clear(); strm.str( line );
			for(j=0; j<(*naux)*3; j++)
		  {
				strm >> aux[j][i];
		  }
	  }
		fle.close();
		cout << "aux read\n";

 // bvrs
		fle.open("steady-state/steady-bvrs.dat");
		getline( fle,line ); strm.clear(); strm.str( line );
		strm >> nn;		

		for(i=0; i<nn; i++)
	  {
			getline( fle,line ); strm.clear(); strm.str( line );
			for(j=0; j<40; j++)
		  {
				strm >> bvrs[j][i];
		  }
	  }
		fle.close();
		cout << "bvrs read\n";


		cout << "\n####################################################\n";
		cout << "Input plugin operational:\n";

  //std::exit(0);

			
  
  }

