   using namespace std;

#  include <mline/mline.h>
#  include <geo/2d/spline.h>

   extern "C" void compressorout( cMlineDev *dev, void *, void *, void *,
#define _DECL_
#include                  <mline/args.h>
#undef  _DECL_
                          )
  {
      Int ib;
      ofstream fle;
      ofstream cfle, hfle, mfle;
      Real     y[2],dy[2],m,d;

      fle.open( "results.dat" );
      fle.setf( ios_base::scientific );
      fle.width( 8 );
      fle.precision( 3 );

      fle << " overall variables\n";
      fle << *wdot << " "<<*pr<<" "<< *minl << " "<<*mext<<"\n";
      fle << " number of rotor blocks "<<(*nrr)<<"\n";
      for( Int i=0;i<(*nrr);i++ )
     {
         ib= (*irbld)[i];
         for( Int j=0;j<(*nrbvrs);j++ )
        {
            fle << bvrs[j][ib]<<" ";
        }
         fle << "\n";
     }
      fle << " number of stator blocks "<<(*nsr)<<"\n";
      for( Int i=0;i<(*nsr);i++ )
     {
         ib= (*isbld)[i];
         for( Int j=0;j<(*nsbvrs);j++ )
        {
            fle << bvrs[j][ib]<<" ";
        }
         fle << "\n";
     }
      fle << " number of gap blocks "<<(*ngr)<<"\n";
      for( Int i=0;i<(*ngr);i++ )
     {
         ib= (*igbld)[i];
         for( Int j=0;j<(*ngbvrs);j++ )
        {
            fle << bvrs[j][ib]<<" ";
        }
         fle << "\n";
     }
      fle << "there should be in total "<<(*nbr)<<" stations \n";

      for( Int i=0;i<(*nbr);i++ )
     {
         fle <<(*bld)[i]->whatis()<<"\n";
     }

      fle << "station data - xq\n";
      for( Int i=0;i<(*nbr)+1;i++ )
     {
         for( Int j=0;j<NXQ;j++ )
        {
            fle << xq[j][i]<<" ";
        }
         fle << "\n";
     }
      fle << "station data - q/q0\n";
      for( Int i=0;i<(*nbr)+1;i++ )
     {
         for( Int j=0;j<(*nv);j++ )
        {
            fle << q[j][i]<<" ";
        }
         for( Int j=0;j<(*nv);j++ )
        {
            fle << q[(*nv)+j][i]<<" ";
        }
         fle << "\n";
     }
      fle << "station data - aux/aux0\n";
      for( Int i=0;i<(*nbr)+1;i++ )
     {
         for( Int j=0;j<(*naux);j++ )
        {
            fle << aux[j][i]<<" ";
        }
         for( Int j=0;j<(*naux);j++ )
        {
            fle << aux[(*naux)+j][i]<<" ";
        }
         fle << "\n";
     }
      fle.close();

      Real *tmp[4];
      Real *xcs[2];
      Real *xhb[2];

      tmp[0]= new Real[(*nbr+2)];
      tmp[1]= new Real[(*nbr+2)];
      tmp[2]= new Real[(*nbr+2)];
      tmp[3]= new Real[(*nbr+2)];

      xcs[0]= new Real[(*nbr+2)];
      xcs[1]= new Real[(*nbr+2)];
      xhb[0]= new Real[(*nbr+2)];
      xhb[1]= new Real[(*nbr+2)];

      cfle.open( "designed/blades/casingline.dat" );
      hfle.open( "designed/blades/hubline.dat" );
      mfle.open( "designed/blades/meanline.dat" );
      for( Int i=0;i<(*nbr)+1;i++ )
     {
         m= xq[0][i];
         d= xq[4][i];
         m/= (*ml)->length();
       (*ml)->interp( m,y,dy );
         rot90( dy );
         d/= 2*norm22( dy ); 
         sclv2( d,dy );
         tmp[0][i]= y[0]+dy[0];
         tmp[1][i]= y[1]+dy[1];
         tmp[2][i]= y[0]-dy[0];
         tmp[3][i]= y[1]-dy[1];
         cfle << tmp[0][i]<<" "<<tmp[1][i]<<"\n";
         hfle << tmp[2][i]<<" "<<tmp[3][i]<<"\n";
         mfle << y[0]<<" "<<y[1]<<"\n";       
     }
      cfle.close();
      hfle.close();
      mfle.close();

      xcs[0][0]= tmp[0][0];
      xcs[1][0]= tmp[1][0];
      xhb[0][0]= tmp[2][0];
      xhb[1][0]= tmp[3][0];
      Int idf=1;
      for( Int i=1;i<(*nbr)+1;i++ )
     {
         xcs[0][idf]= 0.5* (tmp[0][i]+ tmp[0][i-1] );
         xcs[1][idf]= 0.5* (tmp[1][i]+ tmp[1][i-1] );
         xhb[0][idf]= 0.5* (tmp[2][i]+ tmp[2][i-1] );
         xhb[1][idf]= 0.5* (tmp[3][i]+ tmp[3][i-1] );
         idf++;
     }
      xcs[0][idf]= tmp[0][(*nbr)];
      xcs[1][idf]= tmp[1][(*nbr)];
      xhb[0][idf]= tmp[2][(*nbr)];
      xhb[1][idf]= tmp[3][(*nbr)];
      idf++;
      cfle.close();
      hfle.close();
      mfle.close();

      cSpline *cbz= new cSpline();
      cSpline *hbz= new cSpline();
      cbz->build( 1,idf,2,xcs,NULL,NULL );
      hbz->build( 1,idf,2,xhb,NULL,NULL );

      cfle.open( "designed/blades/casinglinedef.dat" );
      hfle.open( "designed/blades/hublinedef.dat" );
      mfle.open( "designed/blades/meanlinedef.dat" );
      for( Int i=0;i<1000;i++ )
     {
         m= (Real)i/(Real)999;

       (*ml)->interp( m,y,dy );
         mfle << y[0]<<" "<<y[1]<<"\n";       
         cbz->interp( m,y,dy );
         cfle << y[0]<<" "<<y[1]<<"\n";       
         hbz->interp( m,y,dy );
         hfle << y[0]<<" "<<y[1]<<"\n";       
     }
      cfle.close();
      hfle.close();
      mfle.close();

      for( Int ir=0;ir<(*nrr);ir++ )
     {
         cout << "make rotor blade from block "<<(*irbld)[ir]<<"\n";
         Int ib= (*irbld)[ir];
       (*bld)[ib]->blade( *gas, *ml, cbz, hbz, xq,q, aux, bvrs );
		 (*bld)[ib]->section( *gas,  *ml, xq, q, aux, bvrs );

     }

      for( Int ir=0;ir<(*nsr);ir++ )
     {
         cout << "make stator blade from block "<<(*isbld)[ir]<<"\n";
         Int ib= (*isbld)[ir];
       (*bld)[ib]->blade( *gas, *ml, cbz, hbz, xq,q, aux, bvrs );
		 (*bld)[ib]->section( *gas,  *ml, xq, q, aux, bvrs );

     }

  }  

 
