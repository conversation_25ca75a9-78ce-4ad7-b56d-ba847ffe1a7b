   using namespace std;

#  include <mline/mline.h>
#  include <geo/2d/spline.h>
#     include <iomanip>

	void eta_is(  cIdGas *gas, Real *q0, Real *aux0, Real *q1, Real *aux1, Real *eta_is );

	void results( cIdGas *gas, ofstream *fle, Real *xq[], Real *q[], Real *aux[], Real *bvrs[],
					  Int *stgs[], Int nbr, Int nrr, Int nsr, Int *irbld, Int *isbld)
  {
      Int ib;

		Real q0[100], aux0[100], q1[100], aux1[100], w[2], rhs[4];
		Real eta[2], nrt, mrtp, pr0, wdot;

		Real imp;
      imp= 6894.75729/0.45359237;

		Int nv, naux;
		nv= gas->getnv();
		naux= gas->getnaux();

		line( 0, nv*3 , q, q0 );
      line( 0, naux*3 , aux, aux0 );

		line( nbr, nv*3 , q, q1 );
      line( nbr, naux*3 , aux, aux1 );

		eta_is( gas, q0, aux0, q1, aux1, eta );

		nrt=  bvrs[5][1]/xq[2][1] * 60/pi2 / sqrt( q[nv+2][0] );
		wdot= q[0][0] * aux[0][0] * xq[3][0]*(1-xq[8][0]);
		mrtp= wdot * imp * sqrt( q[nv+2][0] ) / q[nv+3][0];
		pr0= q[nv+3][nbr]/ q[nv+3][0];

      cout << " overall variables\n";
		cout << " wdot          pr0            eta_is           eta_p          NRT          MRTP\n";
      cout << wdot << "   "<< pr0 <<"   "<< eta[0] << "   " << eta[1] << "   " << nrt << "   " << mrtp<< "\n";

      *fle << " overall variables\n";
      *fle << wdot << "   "<< pr0 <<"   "<< eta[0] << "   " << eta[1] << "   " << nrt << "   " << mrtp<< "\n";
		*fle << "\n";
      *fle << " number of rotor blocks "<<nrr<<"\n";
		*fle << setw(18) << left << "kngloss"
			  << setw(18) << left << "strsmp"
			  << setw(18) << left << "wrtmil"
			  << setw(18) << left << "TOTloss"
			  << setw(18) << left << "inc(deg)"
			  << setw(18) << left << "dev(deg)"
			  << setw(18) << left << "b1(deg)"
			  << setw(18) << left << "b2(deg)"
			  << setw(18) << left << "PR_0"
			  << setw(18) << left << "eta_is" 
			  << setw(18) << left << "mrtp_in"
			  << setw(18) << left << "stagePR0"
			  << setw(18) << left << "eta_p"
			  << "\n";
		Int stin, stout;
      for( Int i=0;i<nrr;i++ )
     {
         ib= irbld[i];
			stin= stgs[i][0];
			stout= stgs[i][1];

			line( stin, nv*3 , q, q0 );
			line( stin, naux*3 , aux, aux0 );
			line( stout, nv*3 , q, q1 );
			line( stout, naux*3 , aux, aux1 );
			eta_is( gas, q0, aux0, q1, aux1, eta );

 			*fle << bvrs[20][ib]<<"   "; 
			*fle << bvrs[21][ib]<<"   ";
			*fle << bvrs[22][ib]<<"   ";
			*fle << bvrs[20][ib]+bvrs[21][ib]+bvrs[22][ib]<<"   ";
			*fle << bvrs[23][ib]<<"   ";
			*fle << bvrs[24][ib]<<"   ";
			*fle << bvrs[25][ib]<<"   ";
			*fle << bvrs[26][ib]<<"   ";
			*fle << bvrs[27][ib]<<"   ";
			*fle << eta[0] <<"   ";
			*fle << bvrs[30][ib]<<"   ";
			*fle << q[nv+3][stout] / q[nv+3][stin]<<"   ";
			*fle << eta[1] <<"   ";
         *fle << "\n";
     }
		*fle << "\n";
      *fle << " number of stator blocks "<<nsr<<"\n";
		*fle << setw(18) << left << "kngloss"
			 << setw(18) << left << "strsmp"
			 << setw(18) << left << "wrtmil"
			 << setw(18) << left << "TOTloss"
			 << setw(18) << left << "inc(deg)"
			 << setw(18) << left << "dev(deg)"
			 << setw(18) << left << "b1(deg)"
			 << setw(18) << left << "b2(deg)"
			 << setw(18) << left << "PR"
			 << setw(18) << left << "mrtp_in"
			 << "\n";
      for( Int i=0;i<nsr;i++ )
     {
         ib= isbld[i];
  
			*fle << bvrs[20][ib]<<"   ";
			*fle << bvrs[21][ib]<<"   ";
			*fle << bvrs[22][ib]<<"   ";
			*fle << bvrs[20][ib]+bvrs[21][ib]+bvrs[22][ib]<<"   ";
			*fle << bvrs[23][ib]<<"   ";
			*fle << bvrs[24][ib]<<"   ";
			*fle << bvrs[25][ib]<<"   ";
			*fle << bvrs[26][ib]<<"   ";
			*fle << bvrs[27][ib]<<"   ";
			*fle << bvrs[30][ib]<<"   ";
			*fle << "\n";
     }
		*fle << "\n";
      *fle << "there should be in total "<<nbr<<" stations \n";

		*fle << "\n";
      *fle << "station data - xq\n";

		*fle << setw(18) << left << "x"
			 << setw(18) << left << "r"
			 << setw(18) << left << "A"
			 << setw(18) << left << "d"
			 << setw(18) << left << "mflow"
			 << setw(18) << left << "bf"
			 << "\n";
      for( Int i=0;i<nbr+1;i++ )			
     {
			*fle << xq[1][i]<<"   ";
			*fle << xq[2][i]<<"   ";
			*fle << xq[3][i]<<"   ";
			*fle << xq[4][i]<<"   ";
			*fle << xq[7][i]<<"   ";
			*fle << xq[8][i]<<"   ";
         *fle << "\n";
     }

		*fle << "\n";
      *fle << "station data - q, q0\n";
		*fle << setw(18) << left << "um"
			 << setw(18) << left << "ut"
			 << setw(18) << left << "T"
			 << setw(18) << left << "P"
			 << setw(18) << left << "T0"
			 << setw(18) << left << "P0"
			 << "\n";
      for( Int i=0;i<nbr+1;i++ )
     {
			*fle << q[0][i]<<"   ";
			*fle << q[1][i]<<"   ";
			*fle << q[2][i]<<"   ";
			*fle << q[3][i]<<"   ";
			*fle << q[nv+2][i]<<"   ";
			*fle << q[nv+3][i]<<"   ";
			*fle << "\n";
     }

		*fle << "\n";
      *fle << "station data - aux\n";
      for( Int i=0;i<nbr+1;i++ )
     {
         for( Int j=0;j<naux;j++ )
        {
            *fle << aux[j][i]<<"   ";
        }
         *fle << "\n";
     }

		*fle << "\n";
      *fle << "station data - aux0\n";
      for( Int i=0;i<nbr+1;i++ )
     {
         for( Int j=0;j<naux;j++ )
        {
            *fle << aux[naux+j][i]<<"   ";
        }
         *fle << "\n";
     }


  }
	
	
	void percpert(cIdGas *gas, Real *xq[], Real *q[], Real *aux[], Real *bvrs[],
					  Real *dxq[], Real *dq[], Real *daux[], Real *dbvrs[],
					  Int *stgs[], Int nbr, Int nrr, Int nsr, Int *irbld, Int *isbld)
  {
		ofstream fle;
		Int ib, stin, stout;
		Real q0[100], aux0[100], q1[100], aux1[100], w[2], rhs[4];
		Real eta[2], nrt, mrtp, pr0, wdot, prs, dprs;

		Int nv, naux;
		nv= gas->getnv();
		naux= gas->getnaux();

		fle.setf( ios_base::scientific );
		fle.width( 14 );
      fle.precision( 9 );

		fle.open("distortions/results_perturbations_perc_drbvrs.dat");

		fle << " number of rotor blocks "<<nrr<<"\n";
		fle << setw(18) << left << "nobl"
			 << setw(18) << left << "dkngloss"
			 << setw(18) << left << "dstrsmp"
			 << setw(18) << left << "dwrtmil"
			 << setw(18) << left << "dTOTloss"
			 << setw(18) << left << "dinc(deg)"
			 << setw(18) << left << "ddev(deg)"
			 << setw(18) << left << "db1(deg)"
			 << setw(18) << left << "db2(deg)"
			 << setw(18) << left << "dPR_0"
			 << setw(18) << left << "deta_is"
			 << setw(18) << left << "dmrtp_in"
			 << setw(18) << left << "dPR0/dMRTPin"
			 << setw(18) << left << "dPR0s"
			 << setw(18) << left << "dPR0s/dMRTPin"
			 << setw(18) << left << "deta_p"
			 << "\n";

		Int ibs;
      for( Int i=0;i<nrr;i++ )
     {
			ib= irbld[i];
			stin= stgs[i][0];
			stout= stgs[i][1];

			line( stin, nv*3 , q, q0 );
			line( stin, naux*3 , aux, aux0 );
			line( stout, nv*3 , q, q1 );
			line( stout, naux*3 , aux, aux1 );
			eta_is( gas, q0, aux0, q1, aux1, eta );

			prs= q[nv+3][stout] / q[nv+3][stin];
			dprs= dq[nv+3][stout]/q[nv+3][stout] - dq[nv+3][stin]/q[nv+3][stin];
			dprs*= prs;
			
			fle << i+1 << "   ";
 			fle << 100*dbvrs[20][ib] / bvrs[20][ib] <<"   ";
			fle << 100*dbvrs[21][ib] / (bvrs[21][ib]+small) <<"   ";
			fle << 100*dbvrs[22][ib] / bvrs[22][ib] <<"   ";
			fle << 100*( dbvrs[20][ib] + dbvrs[21][ib] + dbvrs[22][ib])/( bvrs[20][ib] + bvrs[21][ib] + bvrs[22][ib]) <<"   ";
			fle << 100*dbvrs[23][ib] / bvrs[23][ib] <<"   ";
			fle << 100*dbvrs[24][ib] / bvrs[24][ib] <<"   ";
			fle << 100*dbvrs[25][ib] / bvrs[25][ib] <<"   ";
			fle << 100*dbvrs[26][ib] / bvrs[26][ib] <<"   ";
			fle << 100*dbvrs[27][ib] / bvrs[27][ib] <<"   ";
			fle << 100*dbvrs[28][ib] / eta[0] <<"   ";
			fle << 100*dbvrs[30][ib] / bvrs[30][ib] <<"   ";
			fle << dbvrs[27][ib] / dbvrs[30][ib] <<"   ";
			fle << 100*dprs/prs <<"   ";
			fle << dprs / dbvrs[30][ib] <<"   ";
			fle << 100*dbvrs[29][ib] / eta[1] <<"   ";
         fle << "\n";
     }
		fle << "\n";
		fle.close();

		fle.open("distortions/results_perturbations_perc_dsbvrs.dat");
      fle << " number of stator blocks "<<nsr<<"\n";
		fle << "nobl"
			 << setw(18) << left << "dkngloss"
			 << setw(18) << left << "dstrsmp"
			 << setw(18) << left << "dwrtmil"
			 << setw(18) << left << "dTOTloss"
			 << setw(18) << left << "dinc(deg)"
			 << setw(18) << left << "ddev(deg)"
			 << setw(18) << left << "db1(deg)"
			 << setw(18) << left << "db2(deg)"
			 << setw(18) << left << "dPR"
			 << setw(18) << left << "dMRTPin"
			 << "\n";
      for( Int i=0;i<nsr;i++ )
     {
         ib= isbld[i];
  			fle << i+1 << "   ";
			fle << 100*dbvrs[20][ib] / bvrs[20][ib] <<"   ";
			fle << 100*dbvrs[21][ib] / (bvrs[21][ib]+small) <<"   ";
			fle << 100*dbvrs[22][ib] / bvrs[22][ib] <<"   ";
			fle << 100*( dbvrs[20][ib] + dbvrs[21][ib] + dbvrs[22][ib])/( bvrs[20][ib] + bvrs[21][ib] + bvrs[22][ib]) <<"   ";
			fle << 100*dbvrs[23][ib] / bvrs[23][ib] <<"   ";
			fle << 100*dbvrs[24][ib] / bvrs[24][ib] <<"   ";
			fle << 100*dbvrs[25][ib] / bvrs[25][ib] <<"   ";
			fle << 100*dbvrs[26][ib] / bvrs[26][ib] <<"   ";
			fle << 100*dbvrs[27][ib] / bvrs[27][ib] <<"   ";
			fle << 100*dbvrs[30][ib] / bvrs[30][ib] <<"   ";
			fle << "\n";
     }
		fle << "\n";
		fle.close();

		fle.open("distortions/results_perturbations_perc_dq.dat");
		fle << "station data - dq\n";
		fle << setw(18) << left << "stn"
			 << setw(18) << left << "dum"
			 << setw(18) << left << "dut"
			 << setw(18) << left << "dT"
			 << setw(18) << left << "dP" 
			 << setw(18) << left << "dT0"
			 << setw(18) << left << "dP0"
			 << "\n";
      for( Int i=0;i<nbr+1;i++ )
     {
			fle << i << "  ";
			fle << 100*dq[0][i] / (q[0][i]+small) <<"   ";
			fle << 100*dq[1][i] / (q[1][i]+small) <<"   ";
			fle << 100*dq[2][i] / q[2][i] <<"   ";
			fle << 100*dq[3][i] / q[3][i] <<"   ";
			fle << 100*dq[nv+2][i] / q[nv+2][i] <<"   ";
			fle << 100*dq[nv+3][i] / q[nv+3][i] <<"   ";/*
			fle << 100*dq[(*nv)*2+0][i] / (q[(*nv)*2+0][i]+small) <<"   ";
			fle << 100*dq[(*nv)*2+1][i] / (q[(*nv)*2+1][i]+small) <<"   ";
			fle << 100*dq[(*nv)*2+2][i] / q[(*nv)*2+2][i] <<"   ";
			fle << 100*dq[(*nv)*2+3][i] / q[(*nv)*2+3][i] <<"   ";*/
			fle << "\n";
     }
		fle << "\n";
		fle.close();

		fle.open("distortions/results_perturbations_perc_daux.dat");
      fle << "station data - daux\n";
      for( Int i=0;i<nbr+1;i++ )
     {
			fle << i << "  ";
         for( Int j=0;j<naux;j++ )
        {
            fle << 100*daux[j][i] / aux[j][i] <<"   ";
        }
         fle << "\n";
     }

		fle << "\n";
		fle.close();

		fle.open("distortions/results_perturbations_perc_daux0.dat");
      fle << "station data - daux0\n";
      for( Int i=0;i<nbr+1;i++ )
     {
			fle << i << "  ";
         for( Int j=0;j<naux;j++ )
        {
            fle << 100*daux[naux+j][i] / aux[naux+j][i] <<"   ";
        }
         fle << "\n";
     }

 		fle.close();
  }



	void eta_is(  cIdGas *gas, Real *q0, Real *aux0, Real *q1, Real *aux1, Real *eta )
  {
  // calculating isentropic efficiency
		Real w[2], dqo[4], dauxo[4], qo_id[4], auxo_id[4], rhs[4] ;
		Int nv, naux, iv, it;
		Real *qi, *auxi, *qi0, *auxi0; 
      Real *qo, *auxo, *qo0, *auxo0;
      Real  *qo1,  *auxo1;
		Real eta_is, eta_p;

		nv= gas->getnv();
		naux= gas->getnaux();

      qi=  q0;
      qi0= q0+ nv;
      qo0= q1+ nv;
      qo1= qo0+ nv;

      auxi=  aux0;
      auxi0= aux0+ naux;
      auxo=  aux1;
      auxo0= aux1+ naux;

      setv( 0,nv, 0., rhs );

		qo_id[0]= qo0[0];
		qo_id[1]= qo0[1];
		qo_id[2]= qo0[2];
		qo_id[3]= qo0[3];
		gas->auxv( qo_id, auxo_id );

  // iterate
		w[0]= 1.;  // set eta to 100%;
		for( Int it=0;it<10;it++ )
     {
			setv( 0,nv, 0., rhs );
			gas->auxv( qo_id, auxo_id );
			
			w[1]= qo0[3]/qi0[3];
			gas->prrhs( qi0, auxi0, w, -1., rhs );
			
			w[1]= 1.;
			gas->prrhs( qo_id, auxo_id, w, 1., rhs );
			
			gas->dpr( qo_id, auxo_id, w, rhs, dqo, dauxo );
			
			for( iv=0;iv<nv;iv++ ){ qo_id[iv]+= 0.99*dqo[iv]; };
	  }
		
		eta_is= (auxo_id[1] - auxi0[1]) / (auxo0[1] - auxi0[1]);
		eta_p= log(qo_id[2]/qi0[2]) / log(qo0[2]/qi0[2]);
		
		eta[0]= eta_is;
		eta[1]= eta_p;

  }



	void deta_is(  cIdGas *gas, Real *q0, Real *aux0, Real *q1, Real *aux1, Real *eta_is )
  {
  // STILL TO BE WRITTEN!!!!!!!!!!!
  // calculating isentropic efficiency

  }

 




	void dumpsol( cIdGas *gas, Real *x[], Real *q[], Real *aux[], Real *data[], 
					  Int nbr, Int nv, Int naux, string prefix)
  {

		Int i, j, k;
		ofstream ofle;
		string fnme;
      ofle.setf( ios_base::scientific );
      ofle.width( 20 );
      ofle.precision( 15 );

  // xq
		fnme= prefix+"xq.dat";
		ofle.open(&(fnme[0]));
		ofle << nbr+1 << "\n";

		for(i=0; i<(nbr+1); i++)
	  {
			for(j=0; j<NXQ; j++)
		  {
				ofle << x[j][i] << "  ";
		  }
			ofle << "\n";
	  }
		ofle.close();
		

  // q
		fnme= prefix+"q.dat";
		ofle.open(&(fnme[0]));
		ofle << nbr+1 << "\n";

		for(i=0; i<(nbr+1); i++)
	  {
			for(j=0; j<nv*3; j++)
		  {
				ofle << q[j][i] << "  ";
		  }
			ofle << "\n";
	  }
		ofle.close();
		

  // aux
		fnme= prefix+"aux.dat";
		ofle.open(&(fnme[0]));
		ofle << nbr+1 << "\n";

		for(i=0; i<(nbr+1); i++)
	  { 
			for(j=0; j<naux*3; j++)
		  {
				ofle << aux[j][i] << "  ";
		  }
			ofle << "\n";
	  }
		ofle.close();


 // data
		fnme= prefix+"bvrs.dat";
		ofle.open(&(fnme[0]));
		ofle << nbr << "\n";

		for(i=0; i<(nbr); i++)
	  {
			for(j=0; j<40; j++)
		  {
				ofle << data[j][i] << "  ";
		  }
			ofle << "\n";
	  }
		ofle.close();


  }
