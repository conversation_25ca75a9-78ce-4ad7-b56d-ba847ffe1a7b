   using namespace std;

#  include <sstream>
#  include <mline/mline.h>
#  include <mline/block/arotor.h>
#  include <mline/block/astator.h>
#  include <mline/block/agap.h>
#  include <gas/jgas.h>
#  include <geo/2d/spline.h>
#  include <aero/compressor/loss/liebl.h>

	void checkflip( Real *wrk, Int *flip);
	void bflip( Real *qi, Real *qo, Real *wrk );


   extern "C" void acompressorin( cMlineDev *dev, void *, void *, void *, 
#define _DECL_
#include                  <mline/args.h>
#undef  _DECL_
                          )
  {

      Int                                         n; 
      Int                                        jb,i1,i2;
      Real                                      *x[2];
      cMlineBlock                               *tmp;
      ifstream fle;
      string                                     line;
      stringstream                               strm;
      Real                                       omega,etap,ar,dh,sigma,dalpha,um,bf,wf,wloss,adev,thr,g1,g2,weff,gr;
		Real                                       t0in, p0in, ain;

      cout << "ascii fixed plugin invoked by "<<dev<<"\n";
// the gas

    (*gas)= new cIdGas();
//((cJGas*)*gas)->props("");
    (*nv)= (*gas)->getnv();
    (*naux)= (*gas)->getnaux(); 

      fle.open( "data_ad.dat" );

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );

      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> (*wdot) >> (*pr) >> (*minl) >> (*mext) >> t0in >> p0in >> ain;

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );

// read the meanline definition
      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> n;
      cout << n << " points on the meanline definition\n";
      x[0]= new Real[n]; 
      x[1]= new Real[n]; 
      for( Int i=0;i<n;i++ )
     {
         getline( fle,line ); strm.clear(); strm.str( line );
         strm >> x[0][i]>> x[1][i];
     }
    (*ml)= new cSpline();
   ((cSpline*)*ml)->build( 1,n, 2,x, NULL,NULL );
      delete[] x[0];
      delete[] x[1];

// read the block definitions and data

    (*bld)= new cMlineBlock*[100];
      setv( 0,100, (cMlineBlock*)NULL, (*bld) );

    (*nwrk)=  40;
    (*nrbvrs)=(*nwrk); 
    (*nsbvrs)=(*nwrk); 
    (*ngbvrs)=(*nwrk); 
      for( Int i=0;i<(*nwrk);i++ )
     {
         bvrs[i]= new Real[1000]; 
         setv( (Int)0,(Int)1000, 0.,bvrs[i] );
     }

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );
 
      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> (*nrr );      
      cout << (*nrr) <<" rotor blocks\n";

		(*nstgs)= (*nrr);
      cout << (*nstgs) <<" stages\n";
		for( Int i=0;i<(*nrr);i++ )
     {
			stgs[i]= new Int[4];
	  }
  
     *irbld= new Int[(*nrr)]; 
  
      for( Int i=0;i<(*nrr);i++ )
     {
         cout << "reading rotor block "<<i<<"\n";
         tmp= new cMlineARotor();      
         getline( fle,line ); strm.clear(); strm.str( line );
         strm >> jb >> i1 >> i2 >> omega >> ar >> etap >> dh >> sigma >> um >> weff >> wf >> g1 >> g2 >> thr >> gr;
         tmp->index( jb,i,i1,i2 );
         bvrs[32][jb]= omega*rpm;
         bvrs[15][jb]= ar;
	  //  bvrs[2][jb]= etap;
         bvrs[31][jb]= dh;
         bvrs[11][jb]= sigma; 
         bvrs[34][jb]= um;
	      bvrs[13][jb]= weff;
	      bvrs[17][jb]= wf;
			bvrs[7][jb]= g1;
			bvrs[8][jb]= g2;
         bvrs[12][jb]= thr;
			bvrs[14][jb]= 0.5*( bvrs[7][jb]+bvrs[8][jb] );
			bvrs[16][jb]= gr;
       (*irbld)[i]= jb;
       (*bld)[jb]= tmp;

		 stgs[i][0]= i1;         // inlet station
		 stgs[i][1]= i1+4;       // outlet station
		 stgs[i][2]= jb;         // inlet block
		 stgs[i][3]= jb+3;       // outlet station
	  } 

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );
 
      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> (*nsr );      
      cout << (*nsr) <<" stator blocks\n";

     *isbld= new Int[(*nsr)]; 
      for( Int i=0;i<(*nsr);i++ )
     {
         cout << "reading stator block "<<i<<"\n";
         tmp= new cMlineAStator();      
         getline( fle,line ); strm.clear(); strm.str( line );
         strm >> jb >> i1 >> i2 >> ar >> dalpha >> sigma >> um >> weff >> g1 >> g2 >> thr >> gr;
         tmp->index( jb,i,i1,i2 );
         bvrs[15][jb]= ar;
         bvrs[36][jb]= dalpha;
         bvrs[11][jb]= sigma;
         bvrs[34][jb]= um;
			bvrs[13][jb]= weff;
			bvrs[7][jb]= g1;
			bvrs[8][jb]= g2;
         bvrs[12][jb]= thr;
			bvrs[14][jb]= 0.5*( bvrs[7][jb]+bvrs[8][jb] );
			bvrs[16][jb]= gr;
       (*isbld)[i]= jb;
       (*bld)[jb]= tmp;
     } 

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );
 
      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> (*ngr );      
      cout << (*ngr) <<" gap blocks\n";

     *igbld= new Int[(*ngr)]; 
      for( Int i=0;i<(*ngr);i++ )
     {
         cout << "reading gap block "<<i<<"\n";
         tmp= new cMlineAGap();      
         getline( fle,line ); strm.clear(); strm.str( line );
         strm >> jb >> i1 >> i2 >> ar >> um >> bf >> wloss;
         tmp->index( jb,i,i1,i2 );
         bvrs[15][jb]= ar;
         bvrs[34][jb]= um;
	  //  bvrs[2][jb]= bf;
	  //  bvrs[3][jb]= wloss;
       (*igbld)[i]= jb;
       (*bld)[jb]= tmp;
     } 
      
    (*nbr)= (*nrr)+ (*ngr)+ (*nsr);
      cout << "there are "<<(*nbr)<<" blocks in all\n";


// flow stations - initial estimates

       
      for( Int i=0;i<NXQ;i++) 
     {
         xq[i]= new Real[(*nbr)+1];
     }
      for( Int i=0;i<3*(*nv);i++) 
     {
         q[i]= new Real[(*nbr)+1];
     }
      for( Int i=0;i<3*(*naux);i++) 
     {
         aux[i]= new Real[(*nbr)+1];
         setv( 0,(*nbr)+1, 0.,aux[i] );
     }

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );

      for( Int i=0;i<(*nbr)+1;i++)
     {
         getline( fle,line ); strm.clear(); strm.str( line );
         for( Int j=0;j<NXQ;j++ )
        {
            strm >> xq[j][i];  
        }
         for( Int j=0;j<(*nv);j++ ) 
        {
            strm >> q[j][i];
        }
     }
      fle.close();

  // /////////////////////////////////////////////////////////////////
  // reading the designed angles, areas, x's, r's, and other thingies
  // including q's and aux's
		Int i, j, k;
		fle.open("designed/q.dat");
		getline( fle,line ); strm.clear(); strm.str( line );
		for(i=0; i<(*nbr)+1; i++)
	  {
			getline( fle,line ); strm.clear(); strm.str( line );
			for(j=0; j<(*nv)*2; j++)
		  {
				strm >> q[j][i];
		  }
	  } 
		fle.close();

		fle.open("designed/aux.dat");
		getline( fle,line ); strm.clear(); strm.str( line );
		for(i=0; i<(*nbr)+1; i++)
	  {
			getline( fle,line ); strm.clear(); strm.str( line );
			for(j=0; j<(*naux)*2; j++)
		  {
				strm >> aux[j][i];
		  }
	  } 
		fle.close();

		fle.open("designed/xq.dat");
		getline( fle,line ); strm.clear(); strm.str( line );
		for(i=0; i<(*nbr)+1; i++)
	  {
			getline( fle,line ); strm.clear(); strm.str( line );
			for(j=0; j<NXQ; j++)
		  {
				strm >> xq[j][i];
		  }
	  }
		fle.close();

		fle.open("designed/bvrs.dat");
		getline( fle,line ); strm.clear(); strm.str( line );
		for(i=0; i<(*nbr)+1; i++)
	  {
			getline( fle,line ); strm.clear(); strm.str( line );
			for(j=0; j<20; j++)
		  {
				if(j!=11)
			  {
					strm >> bvrs[j][i];
			  }
				else
			  { Real dum; strm >> dum; } 
		  }
	  }
		fle.close();

		q[(*nv)+2][0]= t0in;
		q[(*nv)+3][0]= p0in;
		xq[3][0]= ain;
      return;
  }

