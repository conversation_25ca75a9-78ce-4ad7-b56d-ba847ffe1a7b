   using namespace std;

#  include <sstream>
#  include <mline/mline.h>
#  include <mline/block/lrotor.h>
#  include <mline/block/lstator.h>
#  include <mline/block/lgap.h>
#  include <gas/jgas.h>
#  include <geo/2d/spline.h>

   extern "C" void fixedin( cMlineDev *dev, void *, void *, void *, 
#define _DECL_
#include                  <mline/args.h>
#undef  _DECL_
                          )
  {

      Int                                         n;
      Int                                        jb,i1,i2;
      Real                                      *x[2];
      cMlineBlock                               *tmp;
      ifstream fle;
      string                                     line;
      stringstream                               strm;
      Real                                       omega,etap,ar,dh,sigma,dalpha,um,bf,wf,wloss;

      cout << "ascii fixed plugin invoked by "<<dev<<"\n";
// the gas

    (*gas)= new cJGas();
   ((cJGas*)*gas)->props("");
    (*nv)= (*gas)->getnv();
    (*naux)= (*gas)->getnaux();

      fle.open( "data.dat" );

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );

      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> (*wdot) >> (*pr) >> (*minl) >> (*mext);

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );

// read the meanline definition
      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> n;
      cout << n << " points on the meanline definition\n";
      x[0]= new Real[n]; 
      x[1]= new Real[n]; 
      for( Int i=0;i<n;i++ )
     {
         getline( fle,line ); strm.clear(); strm.str( line );
         strm >> x[0][i]>> x[1][i];
     }
    (*ml)= new cSpline();
   ((cSpline*)*ml)->build( 1,n, 2,x, NULL,NULL );
      delete[] x[0];
      delete[] x[1];

// read the block definitions and data

    (*bld)= new cMlineBlock*[100];
      setv( 0,100, (cMlineBlock*)NULL, (*bld) );

    (*nwrk)=  20;
    (*nrbvrs)=(*nwrk); 
    (*nsbvrs)=(*nwrk); 
    (*ngbvrs)=(*nwrk); 
      for( Int i=0;i<(*nwrk);i++ )
     {
         bvrs[i]= new Real[1000];
         setv( (Int)0,(Int)1000, 0.,bvrs[i] );
     }

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );
 
      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> (*nrr );      
      cout << (*nrr) <<" rotor blocks\n";
  
     *irbld= new Int[(*nrr)]; 
  
      for( Int i=0;i<(*nrr);i++ )
     {
         cout << "reading rotor block "<<i<<"\n";
         tmp= new cMlineLRotor();      
         getline( fle,line ); strm.clear(); strm.str( line );
         strm >> jb >> i1 >> i2 >> omega >> ar >> etap >> dh >> sigma >> um >> bf >> wf >> wloss;
         tmp->index( jb,i,i1,i2 );
         bvrs[0][jb]= omega*rpm;
         bvrs[1][jb]= ar;
         bvrs[2][jb]= etap;
         bvrs[3][jb]= dh;
         bvrs[4][jb]= sigma;
         bvrs[5][jb]= um;
         bvrs[6][jb]= bf;
         bvrs[7][jb]= wf;
         bvrs[8][jb]= wloss;
       (*irbld)[i]= jb;
       (*bld)[jb]= tmp;
     } 

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );
 
      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> (*nsr );      
      cout << (*nsr) <<" stator blocks\n";

     *isbld= new Int[(*nsr)]; 
      for( Int i=0;i<(*nsr);i++ )
     {
         cout << "reading stator block "<<i<<"\n";
         tmp= new cMlineLStator();      
         getline( fle,line ); strm.clear(); strm.str( line );
         strm >> jb >> i1 >> i2 >> ar >> dalpha >> sigma >> um >> bf >> wloss;
         tmp->index( jb,i,i1,i2 );
         bvrs[0][jb]= ar;
         bvrs[1][jb]= dalpha;
         bvrs[2][jb]= sigma;
         bvrs[3][jb]= um;
         bvrs[4][jb]= bf;
         bvrs[5][jb]= bf;
       (*isbld)[i]= jb;
       (*bld)[jb]= tmp;
     } 

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );
 
      getline( fle,line ); strm.clear(); strm.str( line );
      strm >> (*ngr );      
      cout << (*ngr) <<" gap blocks\n";

     *igbld= new Int[(*ngr)]; 
      for( Int i=0;i<(*ngr);i++ )
     {
         cout << "reading gap block "<<i<<"\n";
         tmp= new cMlineLGap();      
         getline( fle,line ); strm.clear(); strm.str( line );
         strm >> jb >> i1 >> i2 >> ar >> um >> bf >> wloss;
         tmp->index( jb,i,i1,i2 );
         bvrs[0][jb]= ar;
         bvrs[1][jb]= um;
         bvrs[2][jb]= bf;
         bvrs[3][jb]= wloss;
       (*igbld)[i]= jb;
       (*bld)[jb]= tmp;
     } 
      
    (*nbr)= (*nrr)+ (*ngr)+ (*nsr);
      cout << "there are "<<(*nbr)<<" blocks in all\n";


// flow stations - initial estimates

       
      for( Int i=0;i<NXQ;i++) 
     {
         xq[i]= new Real[(*nbr)+1];
     }
      for( Int i=0;i<2*(*nv);i++) 
     {
         q[i]= new Real[(*nbr)+1];
     }
      for( Int i=0;i<2*(*naux);i++) 
     {
         aux[i]= new Real[(*nbr)+1];
         setv( 0,(*nbr)+1, 0.,aux[i] );
     }

      getline( fle,line );
      getline( fle,line );
      getline( fle,line );

      for( Int i=0;i<(*nbr)+1;i++)
     {
         getline( fle,line ); strm.clear(); strm.str( line );
         for( Int j=0;j<NXQ;j++ )
        {
            strm >> xq[j][i];  
        }
         for( Int j=0;j<(*nv);j++ ) 
        {
            strm >> q[j][i];
        }
     }
      fle.close();
      return;
  }

