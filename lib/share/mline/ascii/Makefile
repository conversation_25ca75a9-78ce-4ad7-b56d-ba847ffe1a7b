include ../../../Makefile.in

CSRC=   simple/in.cpp \
	simple/out.cpp \
	simple/visl.cpp \
	simple/rlt.cpp \
	fixed/in.cpp \
	fixed/out.cpp \
	fixed/visl.cpp \
	fixed/rlt.cpp

FSRC=

COBJ=$(CSRC:.cpp=.o)
FOBJ=$(FSRC:.F=.o)

OBJS=$(COBJ) $(FOBJ)

BINS=../libascii.so

$(BINS): $(OBJS)
	$(CCMP) $(COPT) $(OBJS) $(ORGL) $(ENGL) $(SYSL) $(LIBF) -shared -o $@

clean:
	rm -f $(OBJS) $(BINS)

.cpp.o:
	$(CCMP) $(COPT) $(ORGI) $(ENGI) $(SYSI) -o $@ -c $<
.F.o:
	$(FCMP) $(FOPT) $(ORGI) $(ENGI) $(SYSI) -o $@ -c $<
                                                                     
