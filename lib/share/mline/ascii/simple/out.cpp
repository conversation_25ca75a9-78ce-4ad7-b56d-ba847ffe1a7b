   using namespace std;

#  include <mline/mline.h>

   extern "C" void simpleout( cMlineDev *dev, void *, void *, void *,
#define _DECL_
#include                  <mline/args.h>
#undef  _DECL_
                          )
  {
      Int ib;
      ofstream fle;

      fle.open( "results.dat" );
      fle.setf( ios_base::scientific );
      fle.width( 8 );
      fle.precision( 3 );

      fle << " overall variables\n";
      fle << *wdot << " "<<*pr<<" "<< *minl << " "<<*mext<<"\n";
      fle << " number of rotor blocks "<<(*nrr)<<"\n";
      for( Int i=0;i<(*nrr);i++ )
     {
         ib= (*irbld)[i];
         for( Int j=0;j<(*nrbvrs);j++ )
        {
            fle << bvrs[j][ib]<<" ";
        }
         fle << "\n";
     }
      fle << " number of stator blocks "<<(*nsr)<<"\n";
      for( Int i=0;i<(*nsr);i++ )
     {
         ib= (*isbld)[i];
         for( Int j=0;j<(*nsbvrs);j++ )
        {
            fle << bvrs[j][ib]<<" ";
        }
         fle << "\n";
     }
      fle << " number of gap blocks "<<(*ngr)<<"\n";
      for( Int i=0;i<(*ngr);i++ )
     {
         ib= (*igbld)[i];
         for( Int j=0;j<(*ngbvrs);j++ )
        {
            fle << bvrs[j][ib]<<" ";
        }
         fle << "\n";
     }
      fle << "there should be in total "<<(*nbr)<<" stations \n";

      for( Int i=0;i<(*nbr);i++ )
     {
         fle <<(*bld)[i]->whatis()<<"\n";
     }

      fle << "station data - xq\n";
      for( Int i=0;i<(*nbr)+1;i++ )
     {
         for( Int j=0;j<NXQ;j++ )
        {
            fle << xq[j][i]<<" ";
        }
         fle << "\n";
     }
      fle << "station data - q\n";
      for( Int i=0;i<(*nbr)+1;i++ )
     {
         for( Int j=0;j<(*nv);j++ )
        {
            fle << q[j][i]<<" ";
        }
         fle << "\n";
     }
      fle << "station data - aux\n";
      for( Int i=0;i<(*nbr)+1;i++ )
     {
         for( Int j=0;j<(*naux);j++ )
        {
            fle << aux[j][i]<<" ";
        }
         fle << "\n";
     }
  }
