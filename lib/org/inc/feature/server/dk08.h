#  ifndef _DK08_FEATURE_
#  define _DK08_FEATURE_

#  include <cstdio>
#  include <fstream>
#  include <feature/feature.h>
#  include <feature/client.h>

/**@ingroup servers 
  *DK08 cFeature/ASCII converter
  */

  enum dk08feature_e { dk08feature_bad=-1, dk08feature_empty,      dk08feature_rb211,     dk08feature_spool,          // 1
                                           dk08feature_bladed,     dk08feature_endwall,   dk08feature_bladeroot,      // 2
                                           dk08feature_clip,       dk08feature_hook,      dk08feature_generic,
                                           dk08feature_bearing,    dk08feature_seal,      dk08feature_knife,
                                           dk08feature_nonaxi,     dk08feature_match,     dk08feature_pipe,
                                           dk08feature_junction,   dk08feature_clade,     dk08feature_coupling,
                                           dk08feature_combustor,  dk08feature_num };

   class cDk08Feature: public cFeature
  {

      protected:
         virtual void                          fread( string, cFeature *, cFeature ** );
         virtual void                          fread0( ifstream *, cTabData * );
         virtual void                          fread1( ifstream *, cTabData * );

         virtual void                          rgeneric( ifstream *, cTabData *, Int *, cInterp *[] );
         virtual void                          rrb211( ifstream *, cTabData *, Int *, cInterp *[] );
         virtual void                          rspool( ifstream *, cTabData *, Int *, cInterp *[]  );
         virtual void                          rbladed( ifstream *, cTabData *, Int *, cInterp *[]  );
         virtual void                          rendwall( ifstream *, cTabData *, Int *, cInterp *[]  );
         virtual void                          rbladeroot( ifstream *, cTabData * , Int *, cInterp *[] );
         virtual void                          rclip( ifstream *, cTabData * , Int *, cInterp *[] );
         virtual void                          rhook( ifstream *, cTabData * , Int *, cInterp *[] );
         virtual void                          rbearing( ifstream *, cTabData *, Int *, cInterp *[]  );
         virtual void                          rseal( ifstream *, cTabData * , Int *, cInterp *[] );
         virtual void                          rknife( ifstream *, cTabData * , Int *, cInterp *[] );
         virtual void                          rnonaxi( ifstream *, cTabData * , Int *, cInterp *[] );
         virtual void                          rmatch( ifstream *, cTabData * , Int *, cInterp *[] );
         virtual void                          rpipe( ifstream *, cTabData * , Int *, cInterp *[] );
         virtual void                          rcombustor( ifstream *, cTabData * , Int *, cInterp *[] );
         virtual void                          rcoupling( ifstream *, cTabData * , Int *, cInterp *[] );
         virtual void                          rclade( ifstream *, cTabData * , Int *, cInterp *[] );
         virtual void                          rjunction( ifstream *, cTabData * , Int *, cInterp *[] );


         virtual cFeature                      *newfeature( Int );

      public:

                                                cDk08Feature();
                                                cDk08Feature( cFeature *var );
         virtual                               ~cDk08Feature();
//       virtual cFeature                      *checkout( string name );
         virtual cFeature               *checkouts( ident_t node, string data );
  };

#  endif
