#  ifndef _GTK_FEATURE_
#  define _GTK_FEATURE_

#  include <cstdio>
#  include <feature/feature.h>
#  include <feature/client.h>
#  include <base/server/gtk/proto.h>

/**@ingroup servers 
  *Feature GTK front end server 
  */

   class cGtkFeature: public gtk_server<cFeature>
  {
      protected:
         cGtkFeature() {};
      public:
         cGtkFeature( cFeature *var )
        {
            context( var );
            uid=-1;
        }
         virtual void newsubitems();
         virtual void actionsubitems();
//                 void gtksetshapes(Int nshp, cInterp **shp);
                 void gtksetshapes(string );
                 void savebinio( );
         virtual void SaveNxAction();
         virtual void savenx(cFeature *obj);
         virtual void ftrsimport( );
         virtual void binioimport( );

  };

#  endif

