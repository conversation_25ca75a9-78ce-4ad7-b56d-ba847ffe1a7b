#  ifndef _FEATURE_CLIENT_
#  define _FEATURE_CLIENT_

#  include <feature/feature.h>
#  include <feature/client/empty.h>
#  include <feature/client/generic.h>
#  include <feature/client/rb211.h>
#  include <feature/client/spool.h>
#  include <feature/client/bladed.h>
#  include <feature/client/endwall.h>
#  include <feature/client/bladeroot.h>
#  include <feature/client/clip.h>
#  include <feature/client/hook.h>
#  include <feature/client/bearing.h>
#  include <feature/client/seal.h>
#  include <feature/client/knife.h>
#  include <feature/client/nonaxi.h>
#  include <feature/client/match.h>
#  include <feature/client/pipe.h>
#  include <feature/client/combustor.h>
#  include <feature/client/coupling.h>
#  include <feature/client/clade.h>
#  include <feature/client/pipe.h>
#  include <feature/client/junction.h>

#  endif
