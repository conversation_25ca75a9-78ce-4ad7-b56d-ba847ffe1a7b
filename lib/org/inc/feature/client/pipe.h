#  ifndef _PIPE_FEATURE_
#  define _PIPE_FEATURE_

#  include <feature/client/relative.h>
#  include <geo/2d/utils.h>
#  include <feature/client/match.h>
   class cPipe: public cMatch
  {
      protected:
         Real        dbox;             /**<  */ 
         Real        dx[2];            /**<  */  
         Real        d1;               /**<  */   
         string      tag;              /**< Pipe tag. */   
         Int         noff;             /**< Number of pipes along circumference. */  
         Int         npts;             /**<  */ 
         Int         nclpts;           /**<  */      
         Real        dia;              /**<  */ 
         Real        pipethk;          /**< Pipe thickness. */
         Real       *axispts[2];       /**< Pipe thickness. */     
         Real       *brad;             /**< Bent radii at turning location. */  
         Real        pipedir[2];       /**<  */     
         Real        theta0;           /**<  */

         Int        *nsgs;
         Real      **spts;
         Real      **dpts[2];
       cPolyline    *cl, *cl1;
       cInterp     **scl;
       
         fillet_t  **fl, *fl0;
         cap_t      *cp1, *cp2;

      public:
         virtual Int gettype(){  return feature_pipe; };

         cPipe() ;
         virtual ~cPipe() ;

         virtual void set( cTabData * );
         virtual void get( cTabData * );

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );
         
         void clearpts();

         virtual void checkc( string );
         virtual void getpolyc( cPolyset *data );
         virtual void setftrs();

         virtual cPolyline *getcl() { return cl1;};

  };

# endif
