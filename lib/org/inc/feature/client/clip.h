#  ifndef _CLIP_FEATURE_
#  define _CLIP_FEATURE_

#  include <feature/client/relative.h>
#  include <geo/2d/utils.h>

   class cClip: public cRelative
  {
      protected:
         Int                  npts, npts1;
         Real                 l1;                  /**< Insertion depth of male part. */   
         Real                *xpts[2];             /**< Data point to build male part. */
         Real                *xpts1[2];            /**< Data point to build fmale part. */    
         Real                 dpts;                /**< Thickness of geometry for male part. */ 
         Real                 dpts1;               /**< Thickenss of geometry fro femal part. */  
         cPolyline           *cl, *cl1;

         fillet_t            *fl0, **fl1, *fl2;
         Int                 *ns;                 
         cap_t               *cp1, *cp2;
 
   
      public:
         virtual Int gettype(){ return feature_clip; };

         cClip() ;
         virtual ~cClip() ;

         virtual void set( cTabData * );
         virtual void get( cTabData * );

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

         virtual void checkc( string );
         virtual void getpolyc( cPolyset *data );
         virtual void setftrs();

         virtual void makecl();
         void clearpts();

         virtual cPolyline *getcl() { return cl1;}; 
   
         virtual void  scale();


  };

# endif
