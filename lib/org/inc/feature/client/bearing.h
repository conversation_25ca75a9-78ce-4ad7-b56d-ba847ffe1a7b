#  ifndef _BEARING_FEATURE_
#  define _BEARING_FEATURE_

#  include <feature/client/relative.h>
#  include <geo/2d/utils.h>

   class cBearing: public cRelative
  {
      protected:
         Int                  brgtype;                  /**< Type of bearing. */
         Int                  nroll;                    /**< Number of rolling elements. */
         Int                  nrows;                    /**< Number of rows of rolling elements. */
         Int                  splitflag ;               /**< Flag for spliting-casing construction. */
         Real                 dshaft;                   /**< Diameter of shaft. */ 
         Real                 drace;                    /**< Diameter of inner race. */ 
         Real                 Dhousing;                 /**< Diameter of bearing housing. */ 
         Real                 Drace;                    /**< Diameter of outter race. */ 
         Real                 bwidth;                   /**< Track width. */ 
         Real                 rfillet ;                 /**< Fillet radius. */  
         Real                 dtrack;                   /**< Diameter of inner track. */ 
         Real                 Dtrack;                   /**< Diameter of  outter track. */ 
         Real                 dimclr ;                  /**< Diameter of  clearance. */ 
         Real                 droll;                    /**< Standard depth of groove. */
         Real                 lroll;                    /**< Length of roll. */
         Real                 offset[2] ;               /**< Bearing location offset. */

         Real                *xpts[2];                  /**< Points to consturct the bottom bearing. */              
         Real                *xpts1[2];                 /**< Points to construct the upper bearing. */ 
         Real                *clpt[2];                  /**< Bearing central-line points. */
         fillet_t            *fl00, *fl01;
         Int                  npts,   npts1;
         cPolyline           *cl, *cl1;
         cap_t               *cp1,*cp2;       
         


      public:
         virtual Int gettype(){return feature_bearing; };

         cBearing() ;
         virtual ~cBearing() ;

         virtual void set( cTabData * );
         virtual void get( cTabData * );

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

         virtual void checkc( string );
         virtual void getpolyc( cPolyset *data );
         virtual void setftrs();
         
         void clearpts();
     
         void makecl();
 
         virtual void scale();
      
  };

# endif
