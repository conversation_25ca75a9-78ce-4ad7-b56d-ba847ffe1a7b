#  ifndef _SEAL_FEATURE_
#  define _SEAL_FEATURE_

#  include <feature/client/relative.h>
#  include <geo/2d/utils.h>
#  include <feature/client/knife.h>

#define   MXSETS 5

   class cSeal: public cRelative
  {
      protected:

      Int                  nknives[MXSETS];            /**< Number of knives. */ 
      Int                  baseflag;                   /**< Seal base-flag.*/ 
      Int                  abdrpts;                    /**< Number of points to construct abradale geometry. */ 
      Int                  nsets;                      /**< Number of knife sets.*/  
      Int                  nsteps;                     /**< Number of knife steps.*/
      Int                  snpts;                      /**< Seal data points. */   
      Int                 *snsgs;                     
      Real                 baseht;                     /**< Seal base height. */
      Real                 baselen;                    /**< Seal base length. */ 
      Real                 seallength;                 /**< Seal length. */   
      Real                 sealslope;                  /**< Seal slope. */   
      Real                 stepht;                     /**< Height of each knife step.*/ 
      Real                 statthk;                    
      Real                 statlen;                   
      Real                 fknfloc[MXSETS];            /**< Curvilinear coordinate for each knife location. */  
      Real                 kpitch[MXSETS];             /**< Knife pitch. */ 
      Real                 kheight[MXSETS];            /**< Knife height.*/  
      Real                 kthk[MXSETS];               /**< Knife tip thickness. */
      Real                 dtc[MXSETS];                /**< Distance to contact. */
      Real                 knfang[MXSETS];             /**< Knife angle.  */ 
      Real                 sealclr[MXSETS];            /**< Seal clearance. */  
      Real                 kbeta[2][MXSETS];           /**< Knife slange angle. */  
      Real                 abdthk[MXSETS];             /**< Abradable thickness. */  
      Real                 kr[2][MXSETS];              /**< Knife radius. */ 
      Real                 kdist[MXSETS];              /**< Knife distance. */  
      Real                 endlen[2];                  /**< Seal based length. */ 
      Real                 slantwidth[2];              
      Real                 halfbaselen[2];            
      Real                 xorg[2];
      Real                 offset[2] ;
      Real                *bpts[2];
      Real                *spts[2];
      Real                *abdpts[2];
      Real                 knfloc[2][MXSETS][10];
      Real                *shr[2];

      Real               **sspts, **sdpts[2] ;
      class cKnife        *knf ;
      cPolyline           *cl;

      cap_t               *cp1;                      /**< Start cap specification. */
      cap_t               *cp2;                      /**< End cap specifiacation. */
      fillet_t           **fl1;                      /**< Upper side thickness change fillets */
      fillet_t           **fl2;                      /**< Lower side thickness change fillets */
      fillet_t            *fl0;                      /**< Centre-line fillets */


      public:
         virtual Int gettype(){ return feature_seal; };

         cSeal() ;
         virtual ~cSeal() ;
         virtual void clearpts();

         virtual void set( cTabData * );
         virtual void get( cTabData * );

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );



         virtual void checkc( string );
         virtual void getpolyc( cPolyset *data );
         virtual void setftrs();
         virtual void makecl();

         virtual void length(Int nknf);

         virtual cPolyline *getcl() {return cl;};

         virtual void scale();

  };

# endif
