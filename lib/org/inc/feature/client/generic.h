#  ifndef _GENERIC_FEATURE_
#  define _GENERIC_FEATURE_

#  include <feature/client/relative.h>
#  include <geo/2d/utils.h>

/**@ingroup clients */ 
  class cGeneric: public cRelative
  {
      protected:
         bool                 sym;                      /**< Symmetry flag specifies the means of building the polygon. */
         Int                  npts ;                    /**< Number of points on generating line definition.*/
         Real                *xpts[2] ;                 /**< Coordintes of generating line definition points.*/

         cPolyline           *cl;    

         Int                 *nsgs;                     /**< Number of thickness distribution segments .*/
         Real               **spts;                     /**< Curvilinear coordinate of thickness distribution segments.*/
         Real               **dpts[2];                  /**< Thickness distribution.*/

         cap_t               *cp1;                      /**< End cap specification. */
         cap_t               *cp2;                      /**< Start cap specification. */
         fillet_t           **fl1;                      /**< Upper side thickness change fillets. */
         fillet_t           **fl2;                      /**< Lower side thickness change fillets. */
         fillet_t            *fl0;                      /**< Centre-line fillets. */
     
         virtual void         clearpts();
         virtual void         makecl();
  
 
      public:
                               cGeneric();
         virtual              ~cGeneric();
         virtual Int gettype(){  return feature_generic; };

         virtual void set( cTabData * );
         virtual void get( cTabData * );

         virtual void checkc( string );
         virtual void getpolyc( cPolyset *data );

         virtual void setftrs();
 
         virtual void setshapes(Int, cInterp*[]) { nshp=0; };

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

   
         virtual bool getsymmetry() { return sym; } ;
         virtual cPolyline *getcl() { return cl;};

         virtual void scale( );

         virtual void project( Real *y, cInterp **itp, Real *s );
         virtual bool negotiate( cFeature *req );
         virtual void requestposition( cFeature *req, Real *y );

  };

#  endif
