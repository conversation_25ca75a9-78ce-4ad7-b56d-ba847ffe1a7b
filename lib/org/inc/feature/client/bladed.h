#  ifndef _BLADED_FEATURE_
#  define _BLADED_FEATURE_

#  include <feature/feature.h>



/**@ingroup clients */ 
   class cBladed: public cFeature
  {
      protected:
         Int                                     nptsl;             /**< Number of points to build the blade leading edge. */ 
         Int                                     nptst;             /**< Number of points to build the blade trailing edge. */
         Real                                   *xptsl[2];          /**< Points to build the blade leading edge. */     
         Real                                   *xptst[2];          /**< Points to build the blade trailing edge. */

         void                               autoshapes();

      public:
                                               cBladed();
                                              ~cBladed();
         virtual Int                           gettype(){ return feature_bladed; };

         virtual void                              set( cTabData * );
         virtual void                              get( cTabData * );
                                                  
         virtual void                          picklec( size_t *len, pickle_t *buf );
         virtual void                        unpicklec( size_t *len, pickle_t buf );
                                                  
         virtual void                           checkc( string);
         virtual void                         getpolyc( cPolyset *data);
         virtual void                          setftrs();
                                                  
                 void                         clearpts();

         virtual void                   loadshape(string fname );
  };


#  endif
