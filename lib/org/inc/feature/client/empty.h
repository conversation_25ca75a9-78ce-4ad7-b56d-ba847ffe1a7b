#  ifndef _EMPTYFTR_
#  define _EMPTYFTR_

#  include <feature/feature.h>

   class cEmptyFeature: public cFeature
  {
      protected:
      public:
                                              cEmptyFeature(){};
         virtual                              ~cEmptyFeature(){};

         virtual Int                          gettype(){ return feature_empty; };

         virtual void                         set( cTabData *);
         virtual void                         get( cTabData *);


         virtual void checkc( string);
         virtual void getpolyc( cPolyset *data);
         virtual void setftrs();         

  };
#  endif

