#  ifndef _KNIFE_FEATURE_
#  define _KNIFE_FEATURE_

#  include <feature/client/relative.h>
#  include <geo/2d/utils.h>

   class cKnife: public cRelative
  {
      protected:
         Real                 knfang;         /**< Knife angle. */  
         Real                 kheight;        /**< Knife height. */   
         Real                 kthk;           /**< Knife thickness. */ 
         Real                 kbeta[2];       /**< Knife slant angle. */   
         Real                 sealslope;      /**< Seal slope. */    
         Int                  readmode;       /**< Read mode. */  

         fillet_t            *fl0;
      public:


         virtual Int gettype(){ return feature_knife; };

         cKnife() ;
         virtual ~cKnife() ;

         virtual void set( cTabData * );
         virtual void get( cTabData * );

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

         virtual void checkc( string );
         virtual void getpolyc( cPolyset *tmp1 );
         virtual void setftrs();
         void setdat( Real kf, Real kh, Real kth, Real kb0, Real kb1, Real slop, string nam, cFeature* featur);
//         void setorder(Int i);
   
         virtual void scale();


  };

# endif
