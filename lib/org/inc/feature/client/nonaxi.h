#  ifndef _NONAXI_FEATURE_
#  define _NONAXI_FEATURE_

#  include <feature/client/relative.h>
#  include <geo/2d/utils.h>

   class cNonaxi: public cRelative
  {
      protected:
         Int                  npts;       
         Int                  noff;           /**< Number of non-axi-symmetric feature components. */ 
         Real                 dx0[2];         /**< User-defined orientation vector. */   
         Real                 theta0;         /**< Orientation angle in circumferential direction. */ 
         Real                 a0;             /**< Total area of feature. */     
         Real                 ar;             /**< Aspect ratio. */     
         Real                 l0;             /**< One side length of polygons in transverse direction. */     
         Real                 l1;             /**< The other side length of polygons in transverse direction. */     
         Real                 trulen1;        /**< One side thickness change along the length of non-axi-symmetric feature. */  
         Real                 trulen2 ;       /**< The other side thickness change along the length of non-axi-symmetric feature. */   
         Int                 *nsgs;     
         Real               **spts;    
         Real               **dpts[2];    
         Real                *xpts[2];    

 
         Real                 trulentmp;    /**< store the value of trulen1 before scaling oerpation */
         bool                 bscaled;      /**< flag indicate the happening of scaling operation */
 
         cPolyline           *cl;       
         cInterp            **pcl;     
    
         cap_t              *cp1, *cp2;
         fillet_t           *fl;

      public:
         virtual Int gettype(){ return feature_nonaxi; };

         cNonaxi() ;
         virtual ~cNonaxi() ;

         virtual void set( cTabData * );
         virtual void get( cTabData * );

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

         virtual void checkc( string );
         virtual void getpolyc( cPolyset *data );
         virtual void setftrs();

         virtual cPolyline *getcl() {return cl;};
         virtual void clearpts();
 
         virtual void autoshapes();

         virtual void defineshapes( cInterp **var ){};

         virtual void scale();


  };

# endif
