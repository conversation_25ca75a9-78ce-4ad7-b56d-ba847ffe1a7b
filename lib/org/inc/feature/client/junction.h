#  ifndef _JUNCTION_FEATURE_
#  define _JUNCTION_FEATURE_

#  include <feature/client/relative.h>
#  include <geo/2d/utils.h>
#  include <feature/client/nonaxi.h>

   class cJunction: public cNonaxi
  {
      protected:
         Int         npts;          
         Int         nopen;         /**< Number of openings for hydraulic connections. */ 
         Int         jtype;         /**< Type of the junction. */  
         Real        loc[2];        /**< Global location coordinates. */  
         Real        l0[2];         /**< Slope of parent feature at the location of attachment. */  
         Real        coord[5];      
         Real        dia[5];        /**< Pipe connection Diameter. */ 
         Real        halfang[2];    /**< Half-anlge for three-legged junctions. */  
         Real        rad[3];        /**< Internal radii at joints. */
         Real        joffset[2];    /**< Junction offset. */   
         Real       *xpts[2] ;      
         Int         noff;          /**< NUmber of off along the circumference. */ 
         Real        theta0;        /**< Orientation angle in circumferential direction. */
         string      tag[3];        /**< Pipe connection tag. */
         Real        dum0;          /**< A dummy value. */
         Real        dum1;          /**< A dummy value. */
         Real        dum2[5];       /**< Dymmy values. */

        cPolyline   *cl;

      public:
         virtual Int gettype(){  return feature_junction; };

                  cJunction() ;
         virtual ~cJunction() ;

         virtual void set( cTabData * );
         virtual void get( cTabData * );

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );
         

         virtual void checkc( string );
         virtual void getpolyc( cPolyset *data );
         virtual void setftrs();

         Int getJtyp() { return jtype; };
               
                 void setpts();                  
 
         virtual void scale();

 
  }; 

# endif
