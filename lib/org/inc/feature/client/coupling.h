#  ifndef _COUPLING_
#  define _COUPLING__

#  include <feature/feature.h>
#  include <geo/2d/utils.h>

   class cCoupling: public cFeature
  {
      protected:
         Int       npts, npts1;
         Real     *xpts[2], *xpts1[2];
         Real      dpts;                 /**< Thickness of the flange 1. */  
         Real      dpts1;                /**< Thickness of the flange 2.*/  
         Real      l1;                   /**< Length of overlap is constrained to conver the full length of base part of flange 1. */
         Real      l2;                   /**< Length of overlap is constrained to conver the full length of base part of flange 2. */
         Real      mclr;                 /**< Values of meridonal clearance. */ 
         Real      tclr;                 /**< Values of tangential clearance. */ 

    cPolyline     *cl, *cl1;

     fillet_t     *fl, *fl1;
         cap_t    *cp1, *cp2;
      public:
                                              cCoupling();
         virtual                              ~cCoupling();

         virtual Int                          gettype(){ return feature_coupling; };

         virtual void set( cTabData * );
         virtual void get( cTabData * );
                 void clearpts();

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

         virtual void checkc( string);
         virtual void getpolyc( cPolyset *data);
         virtual void setftrs();
    
                 void makecl();
         virtual void autoshapes();

         virtual void scale(); 

  };


#  endif


