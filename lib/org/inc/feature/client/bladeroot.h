#  ifndef _BLADEROOT_FEATURE_
#  define _BLADEROOT_FEATURE_

#  include <feature/feature.h>
#  include <feature/client/endwall.h>


/**@ingroup clients */ 
   class cBladeroot: public cEndwall
  {

      protected:

/*inherited from the cEndwall class*/
         Int                 *nsgs, *nnsgs, nprts, npts, nnpts ;
         Real               **spts, **dpts[2];
         Real               **sspts, **ddpts[2] ;

         Int                  Roottype;                          /**< Type of blade-root. */ 
         Real                 halfwid[2];                        /**< Width of the blade-root at the root axial direction, halfwod[1] is the width of insertion blade for circumferential insertion blade */
         Real                 chamfer[2];                        /**< Disc chamfer at the outer radius at the direction of its central-line pointing. */
         Real                 brootht;                           /**< Root height. */ 
         Real                 slotht;                            /**< Slot height. */
         Real                *xpts[2], *clpt[2] ;

         cPolyline           *cl ; 

//         virtual void         autoshapes();
      public:
                                              cBladeroot();
                                             ~cBladeroot();
         virtual Int                          gettype(){ return feature_bladeroot; };

         virtual void set( cTabData *);
         virtual void get( cTabData *);

         virtual void picklec( size_t *len, pickle_t *buf);
         virtual void unpicklec( size_t *len, pickle_t buf);

         virtual void checkc( string );
         virtual void getpolyc( cPolyset *data );
         virtual void setftrs();
   
         virtual void setshapes(Int , cInterp *[]);
         virtual bool getsymmetry() { return true; } ;
         virtual cPolyline *getcl() { return cl;};

                 void setdummyshapes();
                 void makecl();
                 void getpts();

         virtual void scale();


/** Transforms to absolute coordinates a pair of relative coordinates, reimplementation. As the position of the bladedroot is determined by its parent which is the bladed edges. 
   @param                xr              Relative coordinates.
   @param                xa              Absolute coordinates.
 **/
         virtual void abscoord( Real *xr, Real *xa );
         virtual void relcoord( Real *xr, Real *xa );
 
  };


#  endif
