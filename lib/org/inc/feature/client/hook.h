#  ifndef _HOOK_FEATURE_
#  define _HOOK_FEATURE_

#  include <feature/client/relative.h>
#  include <geo/2d/utils.h>

   class cHook: public cRelative
  {
      protected:
         Int                  npts;        
         Real                 l1;           /**< Depth of insertion to be considered as the length of overlap.  */ 
         Real                *xpts[2];      /**< Data points to build one side of the hook. */  
         Real                *xpts1[2];     /**< Data points to build the other side of the hook. */  
         Real                 dpts;         /**< Thickness of one side of the hook. */ 
         Real                 dpts1;        /**< Thickness of the other side of the hook. */   
         cPolyline           *cl, *cl1;

         fillet_t            *fl0, **fl1, **fl2;
         cap_t               *cp1, *cp2;
      public:
         virtual Int gettype(){  return feature_hook; };

         cHook() ;
         virtual ~cHook() ;

         virtual void set( cTabData * );
         virtual void get( cTabData * );

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

         virtual void checkc( string );
         virtual void getpolyc( cPolyset *data );
         virtual void setftrs();

         virtual void makecl();
         
         void clearpts();
 
         virtual void scale();


  };

# endif
