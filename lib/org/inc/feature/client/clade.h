#  ifndef _CLADE_FEATURE_
#  define _CLADE_FEATURE_

#  include <feature/feature.h>
#  include <geo/2d/utils.h>
#  include <feature/client/bladed.h>


/**@ingroup clients */ 
   class cClade: public cBladed
  {
      protected:
         
         Int                  npassage;                  /**< Number of passages through a blade. */  
         Int                 *iopen[2];                  /**< Description of pasage location. */ 
         Int                  npts;                      
         Real                *mcord[2];                  /**< Location of passage. */ 
         Real                 h0,h1,c0,c1;               
         Real                *addht[2];                  /**< Blade pasaage size offset. */
         string              *tag;                       /**< Tag of the passage. */
        cPolyline            *hcl[5], *ccl[5];
         Real                *hcpts[2], *ccpts[2];
         Int                 *nsgs, nclpts;
         Real               **spts;
         Real               **hdpts[2], **cdpts[2];


         cap_t               *cp1;                      /**< Start cap specification. */
         cap_t               *cp2;                      /**< End cap specifiacation. */
         fillet_t            *fl;                     

         void                               autoshapes();


      public:
                                              cClade();
                                             ~cClade();
         virtual void                         clearpts();
         virtual Int                          gettype(){ return feature_clade; };

         virtual void set( cTabData * );
         virtual void get( cTabData * );

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

         virtual void checkc( string );
         virtual void getpolyc( cPolyset *data );
         virtual void setftrs();
   
         virtual bool getsymmetry() { return false ; } ;

         void makecl();

  };


#  endif
