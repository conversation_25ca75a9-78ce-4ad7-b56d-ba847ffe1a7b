#  ifndef _RB211_FEATURE_
#  define _RB211_FEATURE_

#  include <feature/feature.h>
#  include <feature/client/spool.h>
#  include <feature/client/combustor.h>


/**@ingroup clients */ 
   class cRb211: public cFeature
  {
      protected: 

         virtual void                     autoshapes();

      public:
                                              cRb211();
                                             ~cRb211();
         virtual Int                         gettype(){ return feature_rb211; };

         virtual void                            set( cTabData *);
         virtual void                            get( cTabData *);

         virtual void                        picklec( size_t *len, pickle_t *buf);
         virtual void                      unpicklec( size_t *len, pickle_t buf);

         virtual void                         checkc( string);
         virtual void                       getpolyc( cPolyset *data);
         virtual void                        setftrs();
        
         virtual void                      setshapes(Int, cInterp*[] );   

         virtual void                   loadshape(string fname); 
  };


#  endif
