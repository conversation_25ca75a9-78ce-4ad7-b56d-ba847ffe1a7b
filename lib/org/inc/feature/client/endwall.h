#  ifndef _ENDWALL_FEATURE_
#  define _ENDWALL_FEATURE_

#  include <feature/feature.h>
//#  include <feature/client/relative.h>
#  include <geo/2d/utils.h>



/**@ingroup clients */ 

   class cEndwall: public cFeature
  {
      protected:

         Int                                     *nsgs;   
         Int                                      npts;    
         Real                                      dle;          /**< Extension length of endwall beyons blade's leaing edge. */       
         Real                                      dte;          /**< Extension length of endwall beyons blade's trailing edge. */     
         Real                                    *xpts[2];  
         Real                                   **spts;    
         Real                                   **dpts[2];   
         Int                                     nprts;    
         Int                                     nnpts;   
         Real                                     sign;          /**< Sign indicating casing/hub of annulus line. */
                                          
         Int                                    *nnsgs; 
         Real                                  **sspts;
         Real                                  **ddpts[2];
                                          
         Real                                      ble[2];     
         Real                                      bte[2];
         Real                                       c0;          /**< Normalised intersection position between annulus line and blade leading edge. */         
         Real                                       c1;          /**< Normalised intersection intersection between annulus line and blade trailing edge. */
                                          
         Real                                      thk;          /**< Thickness of the endwall casing. */
         cPolyline                                *cl, *cl1;
         cap_t                                    *cp1;          /**< Start cap specification. */
         cap_t                                    *cp2;          /**< End cap specifiacation. */
         fillet_t                                **fl;           /**< Upper side thickness change fillets */
         fillet_t                                 *fl0;          /**< Centre-line fillets */

         virtual void                       autoshapes();

      public:
                                              cEndwall();
                                             ~cEndwall();
         virtual void                         clearpts();
         virtual Int                           gettype(){ return feature_endwall; };

         virtual void                              set( cTabData * );
         virtual void                              get( cTabData * );

         virtual void                          picklec( size_t *len, pickle_t *buf );
         virtual void                        unpicklec( size_t *len, pickle_t buf );

         virtual void                           checkc( string );
         virtual void                         getpolyc( cPolyset *data );
         virtual void                          setftrs();
   
         virtual void                        setshapes(Int,  cInterp*[]);
         virtual bool                      getsymmetry() { return false ; } ;
         virtual cPolyline                      *getcl() { return cl;};
 
         virtual void                           makecl();

         virtual void                           scale();


         virtual bool                           negotiate( cFeature *req );
         virtual void                           requestposition( cFeature *req, Real *y );
 };


#  endif
