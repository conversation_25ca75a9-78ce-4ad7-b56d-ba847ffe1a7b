#  ifndef _SPOOL_FEATURE_
#  define _SPOOL_FEATURE_

#  include <feature/feature.h>


/**@ingroup clients */ 
   class cSpool: public cFeature
  {
      protected: 
         void                       autoshapes();

      public:
                                        cSpool();
                                       ~cSpool();
         virtual Int                   gettype(){ return feature_spool; };


         virtual void                     set( cTabData *);
         virtual void                     get( cTabData *);

         virtual void                 picklec( size_t *len, pickle_t *buf);
         virtual void               unpicklec( size_t *len, pickle_t buf);

         virtual void                  checkc( string);
         virtual void                getpolyc( cPolyset *data);
         virtual void                 setftrs();

         virtual void               setshapes(Int, cInterp*[] );         

  };



#  endif
