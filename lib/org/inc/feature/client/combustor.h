#  ifndef _COMBUSTOR_
#  define _COMBUSTOR_

#  include <feature/feature.h>
#  include <geo/2d/utils.h>

   class cCombustor: public cFeature
  {
      protected:
         Real                                        thk;     /**< Thickness of the combustor casing. */
         cPolyline                                   *cl;   
         cap_t                                 *cp1,*cp2;
         cInterp                                   *cshp[4];
         void                                 autoshapes();

      public:
                                              cCombustor();
         virtual                             ~cCombustor();

         virtual Int                             gettype(){ return feature_combustor; };

         virtual void                                set( cTabData * );
         virtual void                                get( cTabData * );
                 void                           clearpts();
         virtual void                            picklec( size_t *len, pickle_t *buf );
         virtual void                          unpicklec( size_t *len, pickle_t buf );
         virtual void                             checkc( string);
         virtual void                           getpolyc( cPolyset *data);
         virtual void                            setftrs();         
                                                    
         virtual void                          setshapes( Int, cInterp*[] );
                                                    
         virtual void                             makecl();

                 void                          setdisplacedshapes(cInterp **cl1, cInterp **cl2 );

         virtual void scale(){return;};

  };


#  endif
