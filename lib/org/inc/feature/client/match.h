#  ifndef _MATCH_FEATURE_
#  define _MATCH_FEATURE_

#  include <feature/client/relative.h>
#  include <geo/2d/utils.h>

   class cMatch: public cRelative
  {
      protected:

         bool                 mtchd;      /**< Matched condition. */  
         Real                 dbox;       /**< Dimension of side of square area. */ 
         Real                 dx[2];      /**< Offset from desinged location. */  
         Real                 d1;    
         string               tag;        /**< Tag for the match feature. */
         cFeature            *mctf;      


      public:
         virtual Int gettype(){ return feature_match; };

         cMatch() ;
         virtual ~cMatch() ;

         virtual void set( cTabData * );
         virtual void get( cTabData * );

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

         virtual void checkc( string );
         virtual void getpolyc( cPolyset *data );
         virtual void setftrs();

         virtual void setshapes(Int , cInterp*[]) { nshp= 0; };

         virtual  void getmatches( Int *nmt, cFeature *mtc[] );
         virtual  void getref( Real *x, Real *d );
         virtual  void requestposition( cFeature *req, Real *y );
         virtual  bool compatible( cFeature *ftr );
         virtual  void releasematches();
         virtual  void match( cFeature *ftr );
  };

# endif
