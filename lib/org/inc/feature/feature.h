#  ifndef _FEATURE_
#  define _FEATURE_

#  include <base/proto.h>
#  include <geo/2d/polyset.h>
#  include <geo/2d/spline.h>

/**@ingroup features 
  *Abstract geometry representation
  */

#  define MXSHP 300

   enum feature_e{ feature_bad=-1, feature_empty  ,  feature_rb211    ,  feature_spool,      feature_bladed,
                                   feature_endwall,  feature_bladeroot,  feature_clip ,      feature_hook,
                                   feature_generic,  feature_bearing  ,  feature_seal ,      feature_knife,
                                   feature_nonaxi ,  feature_match    ,  feature_pipe ,      feature_junction,
                                   feature_clade,    feature_coupling,   feature_combustor,  feature_num };
   static const string feature_s[feature_num]= { "empty",    "rb211"    ,    "spool",     "bladed",
                                                  "endwall",  "bladeroot",    "clip" ,     "hook"  ,
                                                  "generic",  "bearing"  ,    "seal" ,     "kinfe" ,
                                                  "nonaxi",   "match"    ,    "pipe" ,     "junction",
                                                  "clade",    "coupling",     "combustor" };



   class cFeature: public default_server<cFeature>
  {
      protected:
         Real                                 sxftm[2];                     /**< self-mirroring axis */
         Real                                 xref[2];                      /**< local frame origin/insertion point */
         Real                                 dxref[2];                     /**< local frame: x-axis (?) actually tangent to parent*/
         Real                                 theta;                        /**< rigid-body rotation angle */
         Real                                 sectthk;                      /**< parent's section thickness */
         Real                                 xftm[2];                      /**< parent's local frame */
         Real                                 sft;                          /**< position on parent */
         bool                                 ftm;                          /**< mirroring flag */
         bool                                 sftm;                         /**< self-mirroring flag */
         bool                                 negl;                         /**< negotiation flag */
         bool                                 frozen;                       /**< frozen feature flag */
         string                               material[3];                  /**< material description strings */
         string                               manufact ;                    /**< manufacture description string */
         Int                                  flag[10];                     /**< dummy flags */
         Real                                 dimn[10] ;                    /**< dummy parameters */
         Real                                 phi;                          /**< angular offset */
         Real                                 N1;                           /**< sectors */
         Real                                 N2;                           /**< repetitions per sector */
         bool                                 rscal;                        /**< Apply scaling recursively for this feature */
         Real                                 fscal[2];                     /**< scale factor for parametric data, first is acculmative value for display, the second is the effective scale factor */
         Real                                 ftrans[2][2];                    /**< translation factor for parametric data, x/y */
         bool                                 rshptrs;                      /**< Apply geo transform recursively for the shapes only */ 

         Int                                  nshp;                         /**< Number of shapes visible to this feature*/
         Int                                  nshp0;                        /**< Number of shapes owned by this feature */
         cInterp                             *shp[MXSHP];                   /**< Shapes visible to this feature */
         Int                                  npth;                         /**< Number of paths for this feature */
         cInterp                             *pth[MXSHP];                   /**< Paths for this feature */

         Int                                  nshpd;                        /**<number of dummy shapes*/
         cInterp                             *dshp[MXSHP];                  /**<dummy shapes container*/

// These set of data are for the engine displacement and scaling, tmporary used.
//offset factor the engine shps in the server side, effective when reading shp data. >            Real                                 offset[2];      
 
/** Create a set of dummy shapes for this feature. These are used to initialise the shapes
    used to build the body of the feature, whether they are owned or not.
    Successive calls to defineshapes and setshapes will override the objects left by
    autoshapes() in shp.  Will also set the values of nshp0 and nshp. 
   @see                   setshapes.
   @see                   defineshapes.
 **/
         virtual void                       autoshapes();

/** Clear the auto shapes for this feature. 
   @see                   autoshapes.
 **/
         virtual void                  clearautoshapes();

      public:
         virtual string                       gettypes(){ return feature_s[gettype()];};
                                              cFeature();
         virtual                             ~cFeature();
                                              cFeature( cFeature * );

         virtual Int                          pcklefamily(){ return 1; };
         virtual cFeature*                    newobject( Int );

         virtual void                         reset();

         virtual void                         get( cTabData * );
         virtual void                         set( cTabData * );

         virtual void   picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

         virtual void getpoly( cPolyset *data );
         virtual void setpoly( cPolyset *data );

         virtual void getpolys( ident_t node, cPolyset *data );
         virtual void getpolyc( cPolyset *data );

         virtual Real getposition(){ return sft; };
         virtual void setposition( Real var ){ sft=var; };
//         virtual Real getfscale(){ return fscal[0]; };
         virtual void      setref( Real *y, Real *dy, Real d );
/** Retrieve the position of the insertion point of a feature and the local thickness of the parent
    at that point 
   @param                x                 Position of the insertion point.
   @param                d                 Local thickness of the parent.
   @todo                                   Make consistent with setref, or deprecate both
 **/
         virtual void      getref( Real *x, Real *d );

         virtual void     setftrs();

/** Sets the shapes for this feature. These include the shapes onwned by the feature
    and those only visible to the feature but owned by one of its ancestors 
   @param                n                 Number of shapes.
   @param                data              Shapes.
 **/
         virtual void setshapes( Int  n, cInterp *data[]);

/** Get the and replace the default shapes 
   @param                n                 Number of shapes.
   @param                data              Shapes.
 **/
         virtual void defineshapes( cInterp **data );

/** Clear all shape pointers for this feature. The owned shapes are destroyed.
 **/
         virtual void clearshapes();
         virtual void clearownshapes();


/** Transforms to absolute coordinates a pair of relative coordinates.
   @param                xr              Relative coordinates.
   @param                xa              Absolute coordinates.
 **/
         virtual void abscoord( Real *xr, Real *xa );

/** Transforms to relative coordinates a pair of absolute coordinates.
   @param                ax              Absolute coordinates.
   @param                rx              Relative coordinates.
 **/
         void virtual relcoord( Real *xa, Real *xr );

         virtual bool getsymmetry() { return false ; } ;
         virtual cPolyline *getcl(){ return NULL; };
         virtual void makecl() {};
/** Produces a list of interpolatable objects used as shapes by the invoking feature and its children.
    Recursive. The shapes reported by getcshapes are added to the list. If the list is initially empty,
    n must be set to 0 before calling.
   @param                n               Number of shapes. Output.
   @param                data            Shapes. Output.
 **/
         virtual void getcshapes(Int *n, cInterp *data[]);


         virtual  void                  scale( );
         virtual  void                  translate( );

         virtual  void                  shapescale();

/** save the current feature list into nx primitives.
   @param                cPolyset*         cPolyset object of the feature.
   @param                string            the name of the feature object and the output file.
 **/
         virtual void                    savenx(cPolyset *, string){};

/** load shapes from external resourse, i.e., txt files. 
**/
         virtual void                   loadshape( string fname ) {};

/** Produce a list of match features contained in the subtree headed by this feature.
   @param                   nmt          Number of match features.
   @param                   mtc          List of match features.
 **/
         virtual void getmatches( Int *nmt, cFeature *mtc[] );

/** Perform negotiation rounds to resolve dimensional conflicts and satisfy constraints.
 **/
         virtual void makematches();

/** Negotiate dimensional changes on behalf of a requesting feature 
   @param                   req          Feature issuing the request.
   @return                               True if the parent is willing to negotiate.
 **/
         virtual bool negotiate( cFeature *req );
   
/** Issue a negotiation request to move reference point for feature insertion to a new position.
   @param                   req          Feature issuing the request.
   @param                   y            Requested position of the insertion point.
 **/
         virtual void requestposition( cFeature *req, Real *y );

/** Release match points for future negotiation rounds
 **/
         virtual void releasematches();

/** Declares whether a feature is negotiation-wise compatible with another feature.
   @param                   var          The other feature.
   @return                               True if the two features are compatible.
 **/
         virtual bool compatible( cFeature *var ){ return false; };

/** Acknwoledges existence of a matching feature for negotiation purposes and
    compute needed displacement to resolve conflicts/constraints
   @param                   var          The other feature.
 **/
         virtual void      match( cFeature *var ){};


  };

#  endif
