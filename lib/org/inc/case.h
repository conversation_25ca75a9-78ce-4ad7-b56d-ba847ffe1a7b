
#  ifndef _CASE_
#  define _CASE_

#  include <cprec.h>
#  include <audit.h>
#  include <cmath>

   class cCase:public cAudited
  {
      private:
         string              path;
         Int                mnode;
         Int                dnode;
         Int                rnode;
         Int                nnode;
         Int                 nrnk;
         Int                ncore;
         Int          ir,ic,in,il;
//       Real        token,dtoken;
         Real              dtoken;
         bool                 ovc;
         bool           finalised;
         Real               gload;
         Real               rload;
         Real               lload;
         Real               tload;
         Real                *cld;
         Real                 *ld;
         Real                 ocr;
         Real               rtime;
         bool              bmetis;
/*       Real              rtime0;
         Real               rtick;*/

         virtual void     newnode();
         virtual void     newlocal( Int *mcpu, Int **inode, Int **icore, Int **irnk );

      public:
	//! Default constructor
         cCase();
        ~cCase();

         string getpath(){ return path; };

         void       reset();
         void       costs( Real dcost, Real token, Real dtoken );

         Int        local(){ return il; };

         void        load( Int *m, Int **inode, Int **icore, Int **irnk, Real tkn );
         void    finalize( Int *m, Int **inode, Int **icore, Int **irnk );
         void    finalize();


// uid personality
         void check( string tab );

// pickle personality
/** Pickles the current object and add it to the buffer buf. The length len of the buffer is updated accordingly.
   @param len                                  Pickle buffer size.
   @param buf                                  Pickle buffer.
   @brief Object pickle.
  */
         virtual void                     pickle( size_t *len, pickle_t *buf );

/** Unpickles the current object from the buffer buf. The current position in the buffer is updated accordingly.
   @param len                                  Pickle buffer size.
   @param buf                                  Pickle buffer.
   @brief Object unpickle.
  */
         virtual void                   unpickle( size_t *len, pickle_t buf );

// reflist personality
         void get( cTabData *rl ); 
         void set( cTabData *rl ); 

//metis flag
         void setmetis( bool var ) {bmetis = var;};
  };
#endif
