#  ifndef _FEMELEMENTS_
#  define _FEMELEMENTS_

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 10:58:48 BST 2010
// Changes History -
// Next Change(s)  -

#  include <field/solid/solid.h>
#  include <domain/symplex.h>

   enum element_t { element_bad=-1, element_q43, element_t33, element_q83, element_p63, element_t43,
                                    element_c13, element_l13 };

   class cElement: public cSymplex
  {

       protected:
         Int                         nlhs;
         Int                         nwrk;
         Int                         naux;
         Int                         nbdv;
         Int                         nst;
         Int                ng;
         Real              *yg[3];
         Real              *wg;
       public:
                      cElement();
         virtual     ~cElement();
         virtual Int  gettype(){ return element_bad; };
         virtual void  sizes( cSolid * );
         virtual void getcost( Int,Int, Int *[], Real * );

         virtual void shpx( Real *, Real *, Real *[] ){};
         virtual void shpf( Real *, Real *, Real *[] ){};

         virtual Int getnlhs( );
         virtual Int getnd( );
         virtual Int getnwrk( );
         virtual Int getnaux( );
         virtual void gtlhs( Int ies, Int iee, Int *iem, Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld ){};

/*       virtual void  gtrhs( Int , Int , Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem *, cSolid * ){};
         virtual void gtdrhs( Int , Int , Real *[], Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem *, cSolid * ){};*/
          virtual void apply( Int ist, Int ien, Int *ibq[],     Int *ibb[],      Real *xq[], Real *q[], Real *xb[], Real *qb[], Real *rhs[], cCosystem * ){};
  };

   class cq43: public cElement
  {
      public:
                                          cq43();
         virtual                         ~cq43();
         virtual Int  gettype(){ return element_q43; };
         virtual void shpx( Real *, Real *, Real *[] );
         virtual void shpf( Real *, Real *, Real *[] );
         virtual void gtlhs( Int ies, Int iee, Int *iem, Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld );
/*       virtual void  gtrhs( Int , Int , Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );
         virtual void gtdrhs( Int , Int , Real *[], Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );*/
  };

   class ct33: public cElement
  {
      public:
                                          ct33();
         virtual                         ~ct33();
         virtual Int  gettype(){ return element_t33; };
         virtual void shpx( Real *, Real *, Real *[] );
         virtual void shpf( Real *, Real *, Real *[] );
         virtual void gtlhs( Int ies, Int iee, Int *iem, Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld );

/*       virtual void  gtrhs( Int , Int , Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );
         virtual void gtdrhs( Int , Int , Real *[], Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );*/
  };


   class ct43: public cElement
  {
      public:
                                          ct43();
         virtual                         ~ct43();
         virtual Int  gettype(){ return element_t43; };
         virtual void shpx( Real *, Real *, Real *[] );
         virtual void shpf( Real *, Real *, Real *[] );
         virtual void gtlhs( Int ies, Int iee, Int *iem, Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld );
/*       virtual void  gtrhs( Int , Int , Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );
         virtual void gtdrhs( Int , Int , Real *[], Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );*/
  };
   
   class cp63: public cElement
  {
      public:
                                          cp63();
         virtual                         ~cp63();
         virtual Int  gettype(){ return element_p63; };
         virtual void shpx( Real *, Real *, Real *[] );
         virtual void shpf( Real *, Real *, Real *[] );
         virtual void gtlhs( Int ies, Int iee, Int *iem,Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld );

/*       virtual void  gtrhs( Int , Int , Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );
         virtual void gtdrhs( Int , Int , Real *[], Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );*/
  }; 

   class cq83: public cElement
  {
      public:
                                          cq83();
         virtual                         ~cq83();
         virtual Int  gettype(){ return element_q83; };
         virtual void shpx( Real *, Real *, Real *[] );
         virtual void shpf( Real *, Real *, Real *[] );
         virtual void gtlhs( Int ies, Int iee, Int *iem,Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld );

/*       virtual void  gtrhs( Int , Int , Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );
         virtual void gtdrhs( Int , Int , Real *[], Real *[], Real *[], Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );*/
  };
   


   class cl13: public cElement
  {
      protected:
      public:
                      cl13();
         virtual     ~cl13();
         virtual Int  gettype(){ return element_l13; }; 

/*       virtual void gtrhs( Int , Int , Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );*/
  };

   class cc13: public cElement
  {
      protected:
      public:
                      cc13();
         virtual     ~cc13();
         virtual Int  gettype(){ return element_c13; }; 
         virtual void gtlhs( Int ies, Int iee, Int *iem,Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld );


/*       virtual void gtrhs( Int , Int , Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );
         virtual void gtdrhs( Int , Int , Int *[], Int *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], cCosystem * );*/

         virtual void apply( Int ist, Int ien, Int *ibq[],     Int *ibb[],      Real *xq[], Real *q[], Real *xb[], Real *qb[], Real *rhs[], cCosystem * );


  };


#  endif
