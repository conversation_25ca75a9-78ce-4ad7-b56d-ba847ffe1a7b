#  ifndef _FEDOMAIN_
#  define _FEDOMAIN_

#  include <domain/domain.h>
#  include <domain/fem/element/element.h>
#  include <field/solid/solid.h>
#  include <lin/matrix.h>

   class cFeDomain: public cDomain
  {
      protected:

         cSolid         *sld;

         cElement       *elm[MxNSk];
         cElement       *blm[MxNSk];

         Real          *sq,**q;
         Real          *sr,**r;
         Real          *sp,**p;
         Real          *sx,**x;
         Real          *sax,**ax;
         Real          *slhsd,**lhsd;

         cPdata        *dof;
         cPdata        *pts;
         cPdata        *eld[MxNSk];
         cPdata        *bld[MxNBG][MxNSk];
         cPdata        *bdf[MxNBG];

         Real          *slhse[MxNSk];
         Real          *swrke[MxNSk];
         Real          *sauxe[MxNSk];

         Real          **lhse[MxNSk];
         Real          **wrke[MxNSk];
         Real          **auxe[MxNSk];

         Real           *sqb[MxNBG],**qb[MxNBG];
         Real           *sdqb[MxNBG],**dqb[MxNBG];

         Real          *slhsb[MxNBG][MxNSk];
         Real          *swrkb[MxNBG][MxNSk];
         Real          *sauxb[MxNBG][MxNSk];
         Real          **lhsb[MxNBG][MxNSk];
         Real          **wrkb[MxNBG][MxNSk];
         Real          **auxb[MxNBG][MxNSk];


         virtual void loadpart();
         virtual void   getcost0( Real * );

      public:
         cFeDomain();
        ~cFeDomain();

         virtual void pickle( size_t *len, pickle_t *buf );
         virtual void unpickle( size_t *len, pickle_t  buf );

         virtual cElement *newelement( Int );
         virtual void assgnsld( cSolid * );
         virtual Int   addelem( cElement *el );
         virtual Int   addblem( cElement *bn );
         virtual void assgnauxe( Int iek, Real *aux );


         virtual void comp();
         virtual void prep( Int );
         virtual void loadpre( );

         virtual void gtlhs( );
  };

   typedef void (*bpl_t)( cDomain *, string , Int , Int , Real *[], Int , Real *[] );

#  endif
