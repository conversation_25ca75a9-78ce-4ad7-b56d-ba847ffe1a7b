#  ifndef _DOMAIN_
#  define _DOMAIN_

#  include <cstdlib>
#  include <tab/proto.h>
#  include <device/device.h>
#  include <domain/symplex.h>
#  include <field/field.h>
#  include <geo/kdtree.h>
#  include <geo/2d/boxt.h>
#  include <lin/vect.h>
#  include <cosystem/cosystems.h>
#  include <au3xview.h>

#  define MxNBG 100

   class cDomain: public cPickle, public cTabSrc
  {
      protected:

         cDevice        *dev;
         cCosystem      *coo;

         Real            omega;
         Real            omegb[MxNBG];

         Int             ilev;
         bool            bcrs;
         cDomain        *crs,*fne;
         Int            *iqcrs;

         Int             nek;
         Int             nbk;

         Int             ng,nq,np,nx,nv,nprp,nprq;

         Int             ne[MxNSk];
         Int             nep[MxNSk];
         Int             neq[MxNSk];
         Int             nbp[MxNSk];
         Int             nbq[MxNSk];
         Int             nbd[MxNSk];

         Int             nbb[MxNBG];
         Int             nb[MxNBG][MxNSk];
         Int             npb[MxNBG];


         Int             ipr0,ipr1;
         Int             nprb[MxNSk];
         Int            *siprp,*iprp[2];
         Int            *siprq,*iprq[2],*siprq0,*siprq1;
         cAu3xView<Int>  iprq0, iprq1;
         Int            *siprb[MxNSk],*iprb[MxNSk][2];
        
// move to FeDomain
         Int             nlhse[MxNSk],nwrke[MxNSk],nauxe[MxNSk];
         Int             nlhsb[MxNSk],nwrkb[MxNSk],nauxb[MxNSk];

         Real           *sxb[MxNBG];cAu3xView<Real> xb[MxNBG];

/*       Real           *sqb[MxNBG],**qb[MxNBG];
         Real           *sdqb[MxNBG],**dqb[MxNBG];*/

         Int            *sibp[MxNBG][MxNSk], **ibp[MxNBG][MxNSk];
         Int            *sibq[MxNBG][MxNSk], **ibq[MxNBG][MxNSk];
         Int            *sibb[MxNBG][MxNSk], **ibb[MxNBG][MxNSk]; cAu3xView<Int> ibb_view[MxNBG][MxNSk];
         Int            *sipbp[MxNBG][MxNSk]; cAu3xView<Int> ipbp[MxNBG][MxNSk];


         Int            *siem[MxNSk], **iem[MxNSk];
         Int            *siep[MxNSk], **iep[MxNSk];
         Int            *sieq[MxNSk], **ieq[MxNSk];
         cAu3xView<Int>  iep_view[MxNSk], ieq_view[MxNSk];
         Int            *smedium_marker[MxNSk], **medium_marker[MxNSk];

//       Int            *sieg[MxNSk], *ieg[MxNSk][MxNPSs*MxNPSs];
//       Int            *sibg[MxNBG][MxNSk], *ibg[MxNBG][MxNSk][MxNPSs*MxNPSs];

         Real           *sxp; cAu3xView<Real> xp;
         Real           *sxq; cAu3xView<Real> xq;

         Real           *qcst;

         Int            *qcpu;
         Int            *pcpu;
         Int            *bcpu[MxNBG];
         Int            *rcpu;

         string         bgnm[MxNBG];
         string         bpl[MxNBG],bpo[MxNBG];

         string         sidl,sido;
         string         swdl,swdo;
         string         fidl,fido;

         virtual void   getcost0( Real * ){};

         ofstream      *flg;
      public:

         cDomain();
         virtual ~cDomain();

         virtual void     pickle( size_t *len, pickle_t *buf );
         virtual void   unpickle( size_t *len, pickle_t  buf );

         virtual void boundaries( Int n, string *data, string *lib, string *obj );

         virtual void   assgncoo( cCosystem * );
         virtual void   assgniep( Int iek, Int n, Int *ip, Int *iq, Int *im, Int *marker );
         virtual void   assgnibp( Int ig, Int iek, Int n, Int *ip, Int *iq, Int *ib );
         virtual void   assgnipb();

         virtual void     assstp( string sil, string sio, string fil, string fio, string swl, string swo );

         virtual void     assgnv( Int m );
         virtual void    assgnxp( Int n, Real *x );
         virtual void    assgnxq( Int n, Real *x );
         virtual void    assgnxb( Int ig, Int n, Real *x );
         virtual void   assgndev( cDevice *dev );
         virtual void   assgnprd( Int, Int, Real * );
         virtual void   assgnprq();

         virtual cDevice *device(){ return dev; };
         virtual cCosystem *cosystem(){ return coo; };
         virtual cField *field(){ return NULL; };

//       virtual void      getxp( Int *, Int *, Real ** );
         virtual void      getxq( Int *, Int *, Real ** );
         virtual void      getselfxq( Int *, Real *[3]);
         virtual void   getqcost( Real ** );
         virtual void    getqcpu( Int ** );
         virtual void   loadpart(){};

         virtual void    getcost( Real * );
         virtual void       comp(){};
         virtual void       comp2(){};
         virtual void       comp3(){};
         virtual void       prep( Int ){};
         virtual void postprocess(){};
         virtual void postprocess( Int it ){};
         virtual void ppostprocess(){};
         virtual void outputjl09(){};
         virtual void    rebuild( Int ){};
         virtual void    rebuildunst( Int id, Int it ){};
         virtual void    loadpre( ){};

         virtual void     attach( cDomain * );
         virtual void     readmgpart( cDomain * );
         virtual void      level( Int i ){ ilev=i; };
         virtual Int       level( ){ return ilev; };

         virtual void mg_restrict(){};
         virtual void smooth2(){};
         virtual void smooth3(){};
         virtual void prolong(){};
         virtual void save();

         virtual void makebox( box_t * );
         virtual void bpts( Int, Int *, Int ** );
         virtual void makeboxes( Int *n, Int *m, box_t **b, Int **i, string **str );
         virtual void request( Int, Int *, Int *, Int *, Real ** );
         virtual void service( Int, Int, Int, Int, Real *[], Real ** , bool );
         virtual void service( Int ig, Int nxr, Int nvr, Int nqr, Real *sxr, Real **sqr );
         virtual void  accept( Int, Int, Int, Real *, Real *w );

         virtual void get( cTabData * );
         virtual void set( cTabData * );

         virtual void starthist( ofstream *tmp ) { flg = tmp; };
         virtual string get_resd_info() {};

         virtual void init_vol(){};
         virtual void init_bcs(){};
  };

   typedef void (*bil_t)( cDomain *dom, void *rftr, string bname, Real omega, Int nx, Int nv, Int nb, cAu3xView<Real>& xb, 
                          cAu3xView<Real>& qb, Int nvauxb, cAu3xView<Real>& auxb, Int *idone );
   typedef void (*sil_t)( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, cAu3xView<Real>& xq, cAu3xView<Real>& q,
                          Real *tm, Real *su[], bool *bsu, Int naux, Real *cfl, Int *idone, Int nfre, 
                          Real *sz_re[], Real *sz_im[]);
   typedef void (*fil_t)( cDomain *dom, void *rftr, Real *omega, Int ng, string *bgnm, Real *omegb );
   typedef void (*swl_t)( cDomain *, Int , Int , Real *[], Int , Real *[] );

#  endif
