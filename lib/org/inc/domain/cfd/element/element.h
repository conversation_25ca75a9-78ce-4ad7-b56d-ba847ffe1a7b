#  ifndef _FEMELEMENTS_
#  define _FEMELEMENTS_

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 10:58:48 BST 2010
// Changes History -
// Next Change(s)  -

#  include <domain/symplex.h>
#  include <cosystem/cosystem.h>

   enum felement_t { felement_bad=-1, felement_q43, felement_t33, felement_q83, felement_p63, felement_t43, 
                     felement_e23,  felement_q42, felement_t32, felement_e22, felement_p53 };

   class cFElement: public cSymplex
  {

       protected:
         Int               ncp[MxNSk];
         Int               ncq[MxNSk];
         Int               nce[MxNSk];
         Int            **icep[MxNSk];
         Int            **iceq[MxNSk];
         Int                ng;
         Real              *yg[3];
         Real              *wg;
         //virtual void volume2( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x, Int *ieq[], cAu3xView<Real>& wq, cAu3xView<Real>& xq, Real *wrk[], cCosystem *coo );
         virtual void volume2( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x, cAu3xView<Int>& ieq, cAu3xView<Real>& wq, cAu3xView<Real>& xq, 
                               cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo );
         //virtual void volume3( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x, Int *ieq[], cAu3xView<Real>& wq, cAu3xView<Real>& xq, Real *wrk[], cCosystem *coo );
         virtual void volume3( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x, cAu3xView<Int>& ieq, cAu3xView<Real>& wq, cAu3xView<Real>& xq, 
                               cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo );
         //virtual void  area2( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, Int *ieq[], cAu3xView<Real>& wns, cAu3xView<Real>& wxds, 
         //                     cAu3xView<Real>& xs, Real *wrk[], cCosystem *coo );
         virtual void  area2( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, cAu3xView<Int>& ieq, cAu3xView<Real>& wns, cAu3xView<Real>& wxds, 
                              cAu3xView<Real>& xs, cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo );
         //virtual void  area3( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, Int *ieq[], cAu3xView<Real>& wns, cAu3xView<Real>& wxds, 
         //                     cAu3xView<Real>& xs, Real *wrk[], cCosystem *coo );
         virtual void  area3( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, cAu3xView<Int>& ieq, cAu3xView<Real>& wns, cAu3xView<Real>& wxds, 
                              cAu3xView<Real>& xs, cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo );
       public:
                      cFElement();
         virtual     ~cFElement();
         virtual Int      getnd();

         virtual Int  gettype(){ return felement_bad; };
         virtual void getcost( Int,Int, Int *[], Real * );

         virtual void    shpx( Real *, cAu3xView<Real>&, cAu3xView<Real>& ){};
         virtual void    shpf( Real *, cAu3xView<Real>&, cAu3xView<Real>& ){};

         virtual void    cmsk( Int, Int *, Int ***, Int *** );

         //virtual void  volume( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x, Int *ieq[], cAu3xView<Real>& wq, cAu3xView<Real>& xq, Real *wrk[], cCosystem *coo ){};
         virtual void  volume( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x, cAu3xView<Int>& ieq, cAu3xView<Real>& wq, cAu3xView<Real>& xq, 
                               cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo ){};
         //virtual void    area( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, Int *ieq[], cAu3xView<Real>& wns, cAu3xView<Real>& wxds, cAu3xView<Real>& xs, Real *wrk[], cCosystem *coo ){};
         virtual void    area( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, cAu3xView<Int>& ieq, cAu3xView<Real>& wns, cAu3xView<Real>& wxds, cAu3xView<Real>& xs, 
                               cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo ){};

         virtual bool  layers(){ return false; };
         
  };

   class cFElement3V: public cFElement
  {
      public:
         cFElement3V(){};
         virtual ~cFElement3V(){};
         //virtual void  volume( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x, Int *ieq[], cAu3xView<Real>& wq, cAu3xView<Real>& xq, Real *wrk[], cCosystem *coo ){ volume3( ies, iee, iep, nx, x, ieq, wq, xq, wrk, coo ); };
         virtual void  volume( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x, cAu3xView<Int>& ieq, cAu3xView<Real>& wq, cAu3xView<Real>& xq, 
                               cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx,  cCosystem *coo ){ volume3( ies, iee, iep, nx, x, ieq, wq, xq, wrk0, wrk1, wrk2, wrkx, coo ); };
         virtual void  tet_vol(Real *y0, Real *y1, Real *y2, Real *y3, Real *vol, Real *cent);
  };

   class cFElement3S: public cFElement
  {
      public:
         cFElement3S(){};
         virtual ~cFElement3S(){};
         //virtual void  area( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, Int *ieq[], cAu3xView<Real>& wns, cAu3xView<Real>& wxds, cAu3xView<Real>& xs, cAu3xView<Real> *wrk[], cCosystem *coo ){
         //              area3( ies, iee, iep, nx, x0, xd0, ieq, wns, wxds, xs, wrk, coo );};
         virtual void  area( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, cAu3xView<Int>& ieq, cAu3xView<Real>& wns, cAu3xView<Real>& wxds, cAu3xView<Real>& xs,
                             cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo ){
                             area3( ies, iee, iep, nx, x0, xd0, ieq, wns, wxds, xs, wrk0, wrk1, wrk2, wrkx, coo );};
  };

   class cFElement2V: public cFElement
  {
      public:
         cFElement2V(){};
         virtual ~cFElement2V(){};
         //virtual void  volume( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x, Int *ieq[], cAu3xView<Real>& wq, cAu3xView<Real>& xq, Real *wrk[], cCosystem *coo ){ volume2( ies, iee, iep, nx, x, ieq, wq, xq, wrk, coo ); };
         virtual void  volume( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x, cAu3xView<Int>& ieq, cAu3xView<Real>& wq, cAu3xView<Real>& xq,
                               cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo ){ volume2( ies, iee, iep, nx, x, ieq, wq, xq, wrk0, wrk1, wrk2, wrkx, coo ); };
  };

   class cFElement2S: public cFElement
  {
      public:
         cFElement2S(){};
         virtual ~cFElement2S(){};
         //virtual void  area( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, Int *ieq[], cAu3xView<Real>& wns, cAu3xView<Real>& wxds, cAu3xView<Real>& xs,
         //                    Real *wrk[], cCosystem *coo ){ area2( ies, iee, iep, nx, x0, xd0, ieq, wns, wxds, xs, wrk, coo ); };
         virtual void  area( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, cAu3xView<Int>& ieq, cAu3xView<Real>& wns, cAu3xView<Real>& wxds, cAu3xView<Real>& xs,
                             cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo ){ area2( ies, iee, iep, nx, x0, xd0, ieq, wns, wxds, xs, wrk0, wrk1, wrk2, wrkx, coo ); };
  };

   class cfq43: public cFElement3S
  {
      public:
                                          cfq43();
         virtual                         ~cfq43();
         virtual Int  gettype(){ return felement_q43; };
         virtual void shpx( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual void shpf( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual bool  layers(){ return true; };
  };

   class cft33: public cFElement3S
  {
      public:
                                          cft33();
         virtual                         ~cft33();
         virtual Int  gettype(){ return felement_t33; };
         virtual void shpx( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual void shpf( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
  };


   class cft43: public cFElement3V
  {
      public:
                                          cft43();
         virtual                         ~cft43();
         virtual Int  gettype(){ return felement_t43; };
         virtual void shpx( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual void shpf( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
  };
   
   class cfp63: public cFElement3V
  {
      public:
                                          cfp63();
         virtual                         ~cfp63();
         virtual Int  gettype(){ return felement_p63; };
         virtual void shpx( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual void shpf( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
  }; 

   class cfp53: public cFElement3V
  {
      public:
                                          cfp53();
         virtual                         ~cfp53();
         virtual Int  gettype(){ return felement_p53; };
         virtual void shpx( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual void shpf( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         //virtual void volume( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x, Int *ieq[], cAu3xView<Real>& wq, cAu3xView<Real>& xq, Real *wrk[], cCosystem *coo );
         virtual void volume( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x, cAu3xView<Int>& ieq, cAu3xView<Real>& wq, cAu3xView<Real>& xq, 
                              cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo );
         void pyramid_vol( Real *y0, Real *y1, Real *y2, Real *y3, Real *y4, Real *vol, Real *cent);

  }; 

   class cfq83: public cFElement3V
  {
      public:
                                          cfq83();
         virtual                         ~cfq83();
         virtual Int  gettype(){ return felement_q83; };
         virtual void shpx( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual void shpf( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
  };

   class cfe23: public cFElement
  {
      public:
                                          cfe23();
         virtual                         ~cfe23();
         virtual Int  gettype(){ return felement_e23; };
         virtual void shpx( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual void shpf( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
  };

   class cfe22: public cFElement2S
  {
      public:
                                          cfe22();
         virtual                         ~cfe22();
         virtual Int  gettype(){ return felement_e22; };
         virtual void shpx( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual void shpf( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual bool  layers(){ return true; };

  };
   
   class cfq42: public cFElement2V
  {
      public:
                                          cfq42();
         virtual                         ~cfq42();
         virtual Int  gettype(){ return felement_q42; };
         virtual void shpx( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual void shpf( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
  };

   class cft32: public cFElement2V
  {
      public:
                                          cft32();
         virtual                         ~cft32();
         virtual Int  gettype(){ return felement_t32; };
         virtual void shpx( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
         virtual void shpf( Real *, cAu3xView<Real>&, cAu3xView<Real>& );
  };
   
#  endif
   
