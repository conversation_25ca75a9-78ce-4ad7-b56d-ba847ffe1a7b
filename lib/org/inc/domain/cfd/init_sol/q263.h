#ifndef _Q263_H_
#define _Q263_H_

#  include <cprec.h>
#  include <au3xview.h>

   class cQ263
  {
      protected:
         static const Int MAXSLN=100;
         static const Int MAXVAR=10;

         Int nlev, nsta, nv;
         Real *x[MAXSLN], *r[MAXSLN], *var[MAXSLN][MAXVAR];

         // *ut[MAXSLN], *t[MAXSLN], *p[MAXSLN], *phi[MAXSLN], *ux[MAXSLN], 
         //*ur[MAXSLN];

         Int np;
         Int *sijn, *siqn;
         cAu3xView<Int> ijn, iqn;
         Real *spv, *spx;
         cAu3xView<Real> pv, px;

      public:
         cQ263();
         virtual ~cQ263();
         virtual void read (string );
         void setnv( Int n ) {nv = n;};

         void buildgrid();
         void interp( Int , Int , Int , cAu3xView<Real>& , cAu3xView<Real>& ,  Int * );
         void newtoniter( Real *l1, Real *l2, Real bx0[2][4], Real x, Real t);

  };

#endif
