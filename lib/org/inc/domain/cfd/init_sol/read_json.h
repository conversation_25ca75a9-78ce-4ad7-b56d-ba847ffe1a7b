#ifndef _READ_JSON_
#define _READ_JSON_


#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
#include <map>
#include <vector>
#include <stdexcept>
#include <cctype>
#include <cstdlib>   // for std::atof
#include <iomanip>  // for std::setw, std::fixed, std::setprecision
#include <cprec.h>  // for std::setw, std::fixed, std::setprecision

//-----------------------------------------------------------------------------
// cJsonValue
//-----------------------------------------------------------------------------

class cJsonValue
{
public:

    enum Type
    {
        Null,
        Bool,
        Number,
        String,
        Array,
        Object
    };

    cJsonValue()
        : m_type( Null )
        , m_bool( false )
        , m_number( 0.0 )
    {
    }

    cJsonValue( bool b )
        : m_type( Bool )
        , m_bool( b )
        , m_number( 0.0 )
    {
    }

    cJsonValue( Real n )
        : m_type( Number )
        , m_bool( false )
        , m_number( n )
    {
    }

    cJsonValue( const std::string & s )
        : m_type( String )
        , m_bool( false )
        , m_number( 0.0 )
        , m_string( s )
    {
    }

    Type GetType() const
    {
        return m_type;
    }

    bool AsBool() const
    {
        if ( m_type != Bool )
        {
            throw std::runtime_error( "Type is not Bool" );
        }
        return m_bool;
    }

    Real AsNumber() const
    {
        if ( m_type != Number )
        {
            throw std::runtime_error( "Type is not Number" );
        }
        return m_number;
    }

    const std::string & AsString() const
    {
        if ( m_type != String )
        {
            throw std::runtime_error( "Type is not String" );
        }
        return m_string;
    }

    const std::vector<cJsonValue> & AsArray() const
    {
        if ( m_type != Array )
        {
            throw std::runtime_error( "Type is not Array" );
        }
        return m_array;
    }

    const std::map<std::string, cJsonValue> & AsObject() const
    {
        if ( m_type != Object )
        {
            throw std::runtime_error( "Type is not Object" );
        }
        return m_object;
    }

private:

    Type                                m_type;
    bool                                m_bool;
    Real                              m_number;
    std::string                         m_string;
    std::vector<cJsonValue>             m_array;
    std::map<std::string, cJsonValue>   m_object;

    friend class cJsonParser;
};

//-----------------------------------------------------------------------------
// cJsonParser
//-----------------------------------------------------------------------------

class cJsonParser
{
public:

    cJsonParser( const std::string & json )
        : m_json( json )
        , m_pos( 0 )
    {
        // strip UTF-8 BOM (0xEF,0xBB,0xBF) if present
        if ( m_json.size() >= 3
          && static_cast<unsigned char>(m_json[0]) == 0xEF
          && static_cast<unsigned char>(m_json[1]) == 0xBB
          && static_cast<unsigned char>(m_json[2]) == 0xBF )
        {
            m_pos = 3;
        }
    }

    cJsonValue Parse()
    {
        cJsonValue result = ParseValue();
        SkipWhitespace();
        if ( m_pos != m_json.size() )
        {
            throw std::runtime_error( "Extra data after JSON value" );
        }
        return result;
    }

private:

    const std::string &    m_json;
    size_t                 m_pos;

    void SkipWhitespace()
    {
        while ( m_pos < m_json.size() &&
                std::isspace( static_cast<unsigned char>(m_json[m_pos]) ) )
        {
            ++m_pos;
        }
    }

    char Peek() const
    {
        if ( m_pos < m_json.size() )
        {
            return m_json[m_pos];
        }
        return '\0';
    }

    char Get()
    {
        if ( m_pos >= m_json.size() )
        {
            throw std::runtime_error( "Unexpected end of input" );
        }
        return m_json[m_pos++];
    }

    bool Expect( const std::string & s )
    {
        size_t len = s.size();
        if ( m_json.substr( m_pos, len ) == s )
        {
            m_pos += len;
            return true;
        }
        return false;
    }

    cJsonValue ParseValue()
    {
        SkipWhitespace();
        char c = Peek();

        if ( c == '{' )
        {
            return ParseObject();
        }
        else if ( c == '[' )
        {
            return ParseArray();
        }
        else if ( c == '"' )
        {
            return cJsonValue( ParseString() );
        }
        else if ( c == 't' || c == 'f' )
        {
            return cJsonValue( ParseBool() );
        }
        else if ( c == 'n' )
        {
            ParseNull();
            return cJsonValue();
        }
        else if ( c == '-' || std::isdigit( static_cast<unsigned char>(c) ) )
        {
            return cJsonValue( ParseNumber() );
        }

        throw std::runtime_error( "Invalid JSON value" );
    }

    cJsonValue ParseObject()
    {
        cJsonValue obj;
        obj.m_type = cJsonValue::Object;

        Get(); // consume '{'
        SkipWhitespace();

        if ( Peek() == '}' )
        {
            Get();
            return obj;
        }

        while ( true )
        {
            SkipWhitespace();
            if ( Peek() != '"' )
            {
                throw std::runtime_error( "Expected string key" );
            }
            std::string key = ParseString();

            SkipWhitespace();
            if ( Get() != ':' )
            {
                throw std::runtime_error( "Expected ':' after key" );
            }

            cJsonValue val = ParseValue();
            obj.m_object[key] = val;

            SkipWhitespace();
            char c = Get();
            if ( c == '}' )
            {
                break;
            }
            else if ( c != ',' )
            {
                throw std::runtime_error( "Expected ',' or '}'" );
            }
        }

        return obj;
    }

    cJsonValue ParseArray()
    {
        cJsonValue arr;
        arr.m_type = cJsonValue::Array;

        Get(); // consume '['
        SkipWhitespace();

        if ( Peek() == ']' )
        {
            Get();
            return arr;
        }

        while ( true )
        {
            cJsonValue v = ParseValue();
            arr.m_array.push_back( v );

            SkipWhitespace();
            char c = Get();
            if ( c == ']' )
            {
                break;
            }
            else if ( c != ',' )
            {
                throw std::runtime_error( "Expected ',' or ']'" );
            }
        }

        return arr;
    }

    std::string ParseString()
    {
        std::string s;

        if ( Get() != '"' )
        {
            throw std::runtime_error( "Expected '\"'" );
        }

        while ( true )
        {
            char c = Get();
            if ( c == '"' )
            {
                break;
            }
            else if ( c == '\\' )
            {
                char e = Get();
                switch ( e )
                {
                    case '"': s.push_back( '"' ); break;
                    case '\\': s.push_back( '\\' ); break;
                    case '/': s.push_back( '/' ); break;
                    case 'b': s.push_back( '\b' ); break;
                    case 'f': s.push_back( '\f' ); break;
                    case 'n': s.push_back( '\n' ); break;
                    case 'r': s.push_back( '\r' ); break;
                    case 't': s.push_back( '\t' ); break;
                    default: throw std::runtime_error( "Invalid escape sequence" );
                }
            }
            else
            {
                s.push_back( c );
            }
        }

        return s;
    }

    Real ParseNumber()
    {
        size_t start = m_pos;
        if ( Peek() == '-' )
        {
            ++m_pos;
        }
        while ( std::isdigit( static_cast<unsigned char>(Peek()) ) )
        {
            ++m_pos;
        }
        if ( Peek() == '.' )
        {
            ++m_pos;
            while ( std::isdigit( static_cast<unsigned char>(Peek()) ) )
            {
                ++m_pos;
            }
        }
        if ( Peek() == 'e' || Peek() == 'E' )
        {
            ++m_pos;
            if ( Peek() == '+' || Peek() == '-' )
            {
                ++m_pos;
            }
            while ( std::isdigit( static_cast<unsigned char>(Peek()) ) )
            {
                ++m_pos;
            }
        }

        return std::atof( m_json.substr( start, m_pos - start ).c_str() );
    }

    bool ParseBool()
    {
        if ( Expect( "true" ) )
        {
            return true;
        }
        else if ( Expect( "false" ) )
        {
            return false;
        }
        throw std::runtime_error( "Invalid boolean literal" );
    }

    void ParseNull()
    {
        if ( ! Expect( "null" ) )
        {
            throw std::runtime_error( "Invalid null literal" );
        }
    }
};

static void _Extract
(
    const std::map<std::string,cJsonValue> & obj,
    const char *                             key,
    std::vector<Real>                   & out
)
{
    // Look up the array by key, resize 'out', then copy each element
    const std::vector<cJsonValue> & arr = obj.at(key).AsArray();
    size_t n = arr.size();
    out.resize(n);
    for ( size_t i = 0; i < n; ++i )
    {
        out[i] = arr[i].AsNumber();
    }
}


#endif

//void TestProfileChoiceSafe( const std::string & filename )
//{
//    // 1) Load file into a string
//    std::ifstream ifs( filename.c_str() );
//    if ( !ifs )
//    {
//        std::cerr << "Error: cannot open '" << filename << "'\n";
//        return;
//    }
//    std::ostringstream oss;
//    oss << ifs.rdbuf();
//    const std::string text = oss.str();
//
//    try
//    {
//        // 2) Parse JSON root
//        cJsonParser parser( text );
//        cJsonValue  root   = parser.Parse();
//        if ( root.GetType() != cJsonValue::Object )
//        {
//            std::cerr << "Error: root is not a JSON object\n";
//            return;
//        }
//        const std::map<std::string,cJsonValue> & rootObj = root.AsObject();
//
//        // 3) Optional volume_init
//        if ( rootObj.count( "volume_init" ) )
//        {
//            std::cout << "--- Volume Initialization ---\n";
//            const cJsonValue & volVal = rootObj.at( "volume_init" );
//            if ( volVal.GetType() == cJsonValue::Object )
//            {
//                const auto & volObj = volVal.AsObject();
//                if ( volObj.count( "uniform" ) )
//                {
//                    std::cout << "Uniform:\n";
//                    const auto & uniObj = volObj.at("uniform").AsObject();
//                    for ( auto & kv : uniObj )
//                    {
//                        std::cout << "  " << kv.first << " = ";
//                        if ( kv.second.GetType() == cJsonValue::Number )
//                        {
//                            std::cout << kv.second.AsNumber() << "\n";
//                        }
//                        else if ( kv.second.GetType() == cJsonValue::Array )
//                        {
//                            auto arr = kv.second.AsArray();
//                            std::cout << "[";
//                            for ( size_t i = 0; i < arr.size(); ++i )
//                            {
//                                std::cout << arr[i].AsNumber()
//                                          << ( i+1< arr.size() ? ", " : "" );
//                            }
//                            std::cout << "]\n";
//                        }
//                        else
//                        {
//                            std::cout << "<unsupported>\n";
//                        }
//                    }
//                }
//                else
//                {
//                    std::cout << "  (no uniform or profile_file)\n";
//                }
//            }
//            std::cout << "\n";
//        }
//
//        // 4) Boundaries (must exist)
//        if ( !rootObj.count( "boundaries" ) )
//        {
//            std::cerr << "Error: no \"boundaries\" section\n";
//            return;
//        }
//        const auto & bnds = rootObj.at( "boundaries" ).AsArray();
//        std::cout << "--- Boundaries (" << bnds.size() << ") ---\n";
//
//        // 5) Loop over each boundary
//        for ( size_t i = 0; i < bnds.size(); ++i )
//        {
//            if ( bnds[i].GetType() != cJsonValue::Object )
//                continue;
//            const auto & bnd = bnds[i].AsObject();
//
//            // 5a) Basic metadata
//            std::string name  = bnd.count("name")  ? bnd.at("name").AsString()  : "<no name>";
//            std::string type  = bnd.count("type")  ? bnd.at("type").AsString()  : "<no type>";
//            Real      rpm   = bnd.count("rotational_speed")
//                               ? bnd.at("rotational_speed").AsNumber()
//                               : 0.0;
//
//            std::cout << "\nBoundary [" << i << "]\n"
//                      << "  Name:             " << name << "\n"
//                      << "  Type:             " << type << "\n"
//                      << "  Rotational speed: " << rpm  << " rpm\n";
//
//            // 5b) init block (must exist)
//            if ( !bnd.count("init") )
//            {
//                std::cout << "  (no init section)\n";
//                continue;
//            }
//            const auto & initVal = bnd.at("init");
//            if ( initVal.GetType() != cJsonValue::Object )
//            {
//                std::cout << "  (init is not an object)\n";
//                continue;
//            }
//            const auto & initObj = initVal.AsObject();
//
//            // 5c) Inline radial profile?
//            if ( initObj.count("profile_data") )
//            {
//                std::cout << "  Inline profile_data:\n";
//                const auto & pd = initObj.at("profile_data").AsObject();
//
//                // radius array first
//                const auto & radius = pd.at("radius").AsArray();
//                size_t pts = radius.size();
//
//                // collect other keys
//                std::vector<std::string> keys;
//                for ( auto & kv : pd )
//                {
//                    if ( kv.first != "radius" )
//                        keys.push_back( kv.first );
//                }
//
//                // header
//                std::cout << "    radius";
//                for ( auto & k : keys )
//                    std::cout << ", " << k;
//                std::cout << "\n";
//
//                // rows
//                for ( size_t j = 0; j < pts; ++j )
//                {
//                    std::cout << "    " << radius[j].AsNumber();
//                    for ( auto & k : keys )
//                    {
//                        auto arr = pd.at(k).AsArray();
//                        std::cout << ", " << arr[j].AsNumber();
//                    }
//                    std::cout << "\n";
//                }
//            }
//            // 5d) External profile file?
//            else if ( initObj.count("profile_file") )
//            {
//                std::cout << "  Profile file: " 
//                          << initObj.at("profile_file").AsString() 
//                          << "\n";
//            }
//            // 5e) Uniform init?
//            else if ( initObj.count("uniform") )
//            {
//                std::cout << "  Uniform init:\n";
//                const auto & uniObj = initObj.at("uniform").AsObject();
//                for ( auto & kv : uniObj )
//                {
//                    std::cout << "    " << kv.first << " = ";
//                    if ( kv.second.GetType() == cJsonValue::Number )
//                    {
//                        std::cout << kv.second.AsNumber() << "\n";
//                    }
//                    else
//                    {
//                        std::cout << "<non‐scalar>\n";
//                    }
//                }
//            }
//            else
//            {
//                std::cout << "  (no profile_data, profile_file, or uniform)\n";
//            }
//        }
//    }
//    catch ( const std::exception & e )
//    {
//        // catch any map::at or type errors
//        std::cerr << "JSON error: " << e.what() << "\n";
//    }
//}
//
//int main( int argc, char* argv[] )
//{
//    if ( argc != 2 )
//    {
//        std::cerr << "Usage: " << argv[0]
//                  << " <boundary_profile_choice.json>\n";
//        return 1;
//    }
//
//    TestProfileChoiceSafe( argv[1] );
//    return 0;
//}
