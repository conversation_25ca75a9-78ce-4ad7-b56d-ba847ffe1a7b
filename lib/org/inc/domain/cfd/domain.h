#  ifndef _FDDOMAIN_
#  define _FDDOMAIN_

#  include <domain/domain.h>
#  include <domain/cfd/element/element.h>
#  include <domain/cfd/bndry/bndry.h>
#  include <topo/dgraph.h>
#  include <field/gas.h>
#  include <field/visc.h>
#  include <lin/matrix.h>
#  include <domain/cfd/init_sol/read_json.h>
#  ifdef PETSC
#  include <petscksp.h>
#  endif


#  define NTLEV 5
//#  define MXNFRE 30
//#  define MXNFRE 2

   class cFdDomain: public cDomain, public cGrad
  {
      protected:

         Int  niter; /**< Number of smoothing iterations. */
         Int  npre ; /**< Number of pre-smoothing iterations. */
         Int  npost; /**< Number of post-smoothing iterations. */
         Int  nstep; /**< Number of outer iterations. */
         Int  nstep_z; /**< Number of iterations for the frequency part. */
         Int  nout;  /**< Output frequency. */
         Int  nexc;  /**< Number of information exchange */
         Int  epsm;  /**< Output frequency. */
         Real cfl0;  /**< Starting CFL number. */
         Real cfl1;  /**< Final CFL number. */
         Real dcfl;  /**< CFL increment. */
         Real lmixmax;  /**< Maximum mixing length */

         bool unst;  /**< Unsteady flag. */
         Real   tm;  /**< Time */
         Real  dtm;  /**< Unsteady time step. */
         Int  ntlv;  /**< Number of time levels. */
         Int  spatial_order;  /**< Number of time levels. */

         Int            naux;
         Int           nauxf;
         Int            nlhs;
         Int            nvel;

         cGas          *fld;
         cVisc         *vsc;

         cFbndry      **bbj;

         Int           *nce[MxNSk];
         Int         ***icep[MxNSk];
         Int         ***iceq[MxNSk];

         cFElement     **elm;
         cFElement     **blm;

         Int           *siqb[MxNBG], **iqb[MxNBG]; cAu3xView<Int> iqb_view[MxNBG];
         Int            nc[MxNSk];
         Int           *sicp[MxNSk],**icp[MxNSk]; cAu3xView<Int> icp_view[MxNSk];
         Int           *sicc[MxNSk],**icc[MxNSk]; cAu3xView<Int> icc_view[MxNSk];
         Int           *sicq[MxNSk],**icq[MxNSk];

         Int           *siprc[MxNSk],**iprc[MxNSk];   cAu3xView<Int> iprc_view[MxNSk];
         Int           *siprdp[MxNSk],**iprdp[MxNSk]; cAu3xView<Int> iprdp_view[MxNSk];

         Int            nfc;
         Int           *sifq; cAu3xView<Int> ifq;


         cPdata        *dof;
         cPdata        *pts;
         cPdata        *prp;
         cPdata        *prd;
         cPdata        *pld[MxNSk];
         cPdata        *eld[MxNSk];
         cPdata        *bld[MxNBG][MxNSk];
         cPdata        *cnd[MxNSk];
         cPdata        *bdf[MxNBG];
         cPdata        *dsd[MxNBG];
         cPdata        *wdf;
         cPdata        *cnf;

         Real           cfl;

// wall distances
      
         Real          *sdst; cAu3xView<Real> dst;
         Int            ndst[MxNBG];
         Int           *sibdst[MxNBG]; cAu3xView<Int> ibdst[MxNBG];
         Int           *siqdst[MxNBG]; cAu3xView<Int> iqdst[MxNBG];

//  point arras

         Real          *sxdp; cAu3xView<Real> xdp;
         Real          *sxbp[MxNBG],*sxdbp[MxNBG];
         cAu3xView<Real> xbp[MxNBG], xdbp[MxNBG];

//  dof arrays

         Real          *sxdq; cAu3xView<Real> xdq;
         Real          *swq;cAu3xView<Real> wq;
         Real          *sq; cAu3xView<Real> q;
         Real          *sdq; cAu3xView<Real> dq;
         Real          *su[NTLEV]; cAu3xView<Real> u[NTLEV];
         Real          *saux; cAu3xView<Real> aux;
         Real          *sdaux; cAu3xView<Real> daux;
         Real          *srhs; cAu3xView<Real> rhs;
         Real          *sres; cAu3xView<Real> res;
         Real          *slhsa; cAu3xView<Real> lhsa;
         Real          *swrkq; cAu3xView<Real> wrkq;
         Real          *smgrhs; cAu3xView<Real> mgrhs;
         Real          *scsv; cAu3xView<Real> csv;
         cJacBlk       **lhs_jac, *jac_df_prd[2], *jac_df[2], *jac_inv_prd[2], *jac_vis_prd[2];

         Real          *sdxdx; cAu3xView<Real> dxdx;
         Real         *sdqdx; cAu3xView<Real> dqdx;
         Int           *ijdx[3];


// connection arrays

         Real          *sauxf; cAu3xView<Real> auxf;
         Real          *swnc; cAu3xView<Real> wnc;
         Real          *swxdc; cAu3xView<Real> wxdc;
         Real          *sxc; cAu3xView<Real> xc;

// boundary arrays

         Real          *sauxfb[MxNBG]; cAu3xView<Real> auxfb[MxNBG];
         Real          *swnb[MxNBG]; cAu3xView<Real> wnb[MxNBG];
         Real          *swxdb[MxNBG]; cAu3xView<Real> wxdb[MxNBG];

         Real           *sxqb[MxNBG]; cAu3xView<Real> xqb[MxNBG];
         Real           *sqb[MxNBG]; cAu3xView<Real> qb[MxNBG];
         Real           *sauxb[MxNBG]; cAu3xView<Real> auxb[MxNBG];
         Real           *srhsb[MxNBG]; cAu3xView<Real> rhsb[MxNBG];
         Real           *slhsb[MxNBG]; cAu3xView<Real> lhsb[MxNBG];
         Real           *sresb[MxNBG]; cAu3xView<Real> resb[MxNBG];

         Real           *sqb0[MxNBG]; cAu3xView<Real> qb0[MxNBG];
         Real           *sauxb0[MxNBG]; cAu3xView<Real> auxb0[MxNBG];

         Real           *sdqb[MxNBG]; cAu3xView<Real> dqb[MxNBG];
         Real           *sdauxb[MxNBG]; cAu3xView<Real> dauxb[MxNBG];

// periodic arrays

         Real          *sdxdxprd; cAu3xView<Real> dxdxprd;
         Real         *sdqdxprd; cAu3xView<Real> dqdxprd;
  
         Real           *swnprd,*swxdprd,*sxprd,*sauxfprd;
         //Real          **wnprd,**wxdprd,**xprd,**auxfprd;
         cAu3xView<Real>  wnprd,wxdprd,xprd,auxfprd;
         Real           *sxqprd,*sqprd,*sauxprd,*sdqprd,*srhsprd,*sresprd,*slhsprd,*sdauxprd;
         //Real          **xqprd,**qprd,**auxprd,**dqprd,**rhsprd,**resprd,**lhsprd,**dauxprd;
         cAu3xView<Real>  xqprd,qprd,auxprd,dqprd,rhsprd,resprd,lhsprd,dauxprd;

// boundary layering
         Int               nbl[MxNBG];
         Int              *ibpl[MxNBG];
         Int              *ibql[MxNBG]; cAu3xView<Int> ibql_view[MxNBG];

//frequency domain
         ofstream       flehist_z[MXNFRE];
         bool           bfredone;
         Real           cfl_z[MXNFRE];
         Int            nfre, bharm[MXNFRE][2];
         Real           freq0[MXNFRE], ibpa[MXNFRE];
         //Real           freq0, ibpa;
         Real          *sz_re[MXNFRE], *sz_im[MXNFRE]; cAu3xView<Real> z_re[MXNFRE]; cAu3xView<Real> z_im[MXNFRE];
         Real          *szc_re[MXNFRE], *szc_im[MXNFRE]; cAu3xView<Real> zc_re[MXNFRE]; cAu3xView<Real> zc_im[MXNFRE];
         Real          *szcold_re, *szcold_im, *sauxz_re,*sauxz_im;
         cAu3xView<Real> zcold_re,zcold_im, auxz_re, auxz_im;


         Real          *srhsz_re, *srhsz_im; cAu3xView<Real> rhsz_re; cAu3xView<Real> rhsz_im;
         Real         **sdzdx_re, **sdzdx_im; cAu3xView<Real> dzdx_re[MxNVs]; cAu3xView<Real> dzdx_im[MxNVs];

         Real         **sdzdxprd_re; cAu3xView<Real> dzdxprd_re[MxNVs];
         Real         **sdzdxprd_im; cAu3xView<Real> dzdxprd_im[MxNVs];
         Real          *srhszprd_re, *srhszprd_im; cAu3xView<Real> rhszprd_re; cAu3xView<Real> rhszprd_im;
         Real          *szprd_re, *szprd_im; cAu3xView<Real> zprd_re; cAu3xView<Real> zprd_im;
         Real          *szcprd_re, *szcprd_im; cAu3xView<Real> zcprd_re; cAu3xView<Real> zcprd_im;

         Real           *szb_re[MxNBG]; cAu3xView<Real> zb_re[MxNBG];
         Real           *szb_im[MxNBG]; cAu3xView<Real> zb_im[MxNBG];
         Real           *szcb_re[MxNBG]; cAu3xView<Real> zcb_re[MxNBG];
         Real           *szcb_im[MxNBG]; cAu3xView<Real> zcb_im[MxNBG];
         Real           *srhsbz_re[MxNBG]; cAu3xView<Real> rhsbz_re[MxNBG];
         Real           *srhsbz_im[MxNBG]; cAu3xView<Real> rhsbz_im[MxNBG];

         Real           *szb0_re[MXNFRE][MxNBG]; cAu3xView<Real> zb0_re[MXNFRE][MxNBG];
         Real           *szb0_im[MXNFRE][MxNBG]; cAu3xView<Real> zb0_im[MXNFRE][MxNBG];

/*       Real             *sxbvg[MxNBG],**xbvg[MxNBG];
         Real             *sqbvg[MxNBG],**qbvg[MxNBG];
         Real             *swbvg[MxNBG],**wbvg[MxNBG];*/

// boundary layering

         virtual void loadpart();

         virtual void faces();
         virtual void distance();
         virtual void layers();
         virtual void layerc( Int *n, Int ***icp, Int ***icq, Int *imrk, Int *cmrk, Int iold, Int ifree, Int inew );
         virtual void   getcost0( Real * );

//sampling maximum 20 arrays of samples, this should be enough
         bool           bprobed, bslice[20];
         Int            nprobe, nprobep[20], nprobeq[20], *probeq[20][3];
         Real          *probep[20][4];
         void           probesol();

//venk limiter
         Real          *sqmax, *sqmin, *svklim;
         cAu3xView<Real> qmax, qmin, vklim;
         Real          *sqface[2]; cAu3xView<Real> qface[2];
         Real          *sqprdface[2]; cAu3xView<Real> qprdface[2];
         Real          limfac;
         Real          venkqref[10], volref;
         void setvenkref();
         void initvenklim();
         void compvenklim();
         void compvenklim(Int iq, Int ic, cAu3xView<Real>& xf, cAu3xView<Real>& wnf);
         void gtrhfm_venk_z( Int ifre, cAu3xView<Real>& r_re, cAu3xView<Real>& r_im );

#  ifdef PETSC
//petsc
         //mean flow
         Vec  petsc_csv, petsc_dcsv, petsc_rhs, petsc_baserhs; /* approx solution, RHS*/
         Mat  petsc_A_mf, petsc_A_pre;                     /* linear system matrix */
         KSP  petsc_ksp;                                   /* linear solver context */
         PC   petsc_pc;                                    /* preconditioner context */
         Int  nghost, *ighost, *ig_mat, nlocal, *ighost_local, *ilocal;
         Real csv_mag_fd;
         Real init_residual, init_residual_lin[MXNFRE];
         bool gmres_glb;

         PetscErrorCode petsc_nk_init();
         PetscErrorCode petsc_setrhs( Vec rhs );
         PetscErrorCode petsc_nk_comp();
         PetscErrorCode petsc_qupdt( Vec delta );
         PetscErrorCode add_blk_to_petsc_matrix( Mat matrix, Int iql, Int iqr, cJacBlk blk, Int sign  );


         static PetscErrorCode FormFunction_mf(void *ctx, Vec q, Vec rhs);

         void petsc_fd_rhs();
         void petsc_assemble_jac_invi();
         void petsc_assemble_jac_invi2();
         void petsc_assemble_jac_visc();
         void petsc_assemble_jac_visc2();

         static PetscErrorCode mymult(Mat m ,Vec x, Vec y);
         PetscErrorCode petsc_fd_rhs2(Vec output);
         PetscErrorCode petsc_set_fd_dq(Vec var);
         PetscErrorCode add_pseudo_time_to_petsc_matrix( Mat matrix, Int iqs, Int iqe, Real *lhs, Real cfl, Int isign  );
         PetscErrorCode add_blk_to_petsc_matrix_z( Mat matrix, Int iql, Int iqr, cJacBlkZ blk, Real sign  );

         PetscErrorCode check_mat_multi_vec();

         //linearized flow
         Vec  petsc_dcsv_z, petsc_rhs_z; /* approx solution, RHS*/
         Mat  petsc_A_pre_copy;          /* linear system matrix */
         KSP  petsc_ksp_z;               /* linear solver context */
         bool gmres_glb_z[MXNFRE];

         PetscErrorCode petsc_nk_init_lin();
         PetscErrorCode petsc_nk_comp_lin();
         PetscErrorCode petsc_qupdt_z( Int ifre, Vec delta );
         PetscErrorCode petsc_setrhs_z( Vec var );
         PetscErrorCode check_lin_lhs();
         PetscErrorCode add_phase_peri_jac( Int ifre, Real sign );
         PetscErrorCode petsc_cleanup();
#  endif
         bool acc_started;
         void start_acc_device();
         void exit_acc_device();

         cAu3xView<Real> NULL_rview;
         cAu3xView<Int> NULL_iview;

//initialize volume solution, frame and bcs
         void uni_init(Real *q0);
         void q263_init(string prof_nm);
         void restart_vol_init();
         void spsg_init( string spsg_sol, Int npsg );
         void init_frame();
         void unibcs(Int ig, Real *q0);
         void subinbcs(Int ig, Real *q0);
         void suboutbcs(Int ig, Real *q0);
         void adiabcs(Int ig);
         void mixbcs(Int ig);
         void q263bcs(Int ig, string prof_nm);
         void restartbcs(Int ig);

         void json_config_sol();
         void json_config_vol(const map<string,cJsonValue> & rootObj);
         void json_config_bnd(const map<string,cJsonValue> & rootObj);

      public:
         cFdDomain();
        ~cFdDomain();

         virtual cField *field(){ return fld; };

         virtual void pickle( size_t *len, pickle_t *buf );
         virtual void unpickle( size_t *len, pickle_t  buf );

         virtual cFElement *newelement( Int );

         virtual void assgngas( cGas * );
         virtual void   assgnprq();

         virtual void elements( Int , cFElement **, Int, cFElement ** );
         virtual void  bndries( cFbndry ** );


         virtual void comp();
         virtual void comp2();
         virtual void comp3();
         virtual void prep( Int );
         virtual void rebuild( Int );
         virtual void rebuildunst( Int id, Int it );
         virtual void rebuildb( string fnm , Int id, Int ig, Int ng );
         virtual void loadpre( );
         virtual void init();
         virtual void bcs();

         virtual void weights();
         virtual void frame();
         virtual void movegrid();
         virtual void save();
         virtual void saveunst( Int it0 );

         virtual void gtrhf( cAu3xView<Real>& r ); // add arrays to be used as arguments q,qb,dqdx etc
         virtual void gtrhfm_venk( cAu3xView<Real>& r ); // add arrays to be used as arguments q,qb,dqdx etc
         virtual void gtrhm( cAu3xView<Real>& r );
         virtual void gtrhv( cAu3xView<Real>& r ); // add arrays to be used as arguments q,qb,dqdx etc

         virtual void gtlhs( cAu3xView<Real>& a ); // add arrays to be used as arguments q,qb,dqdx etc

         virtual void gtresd( cAu3xView<Real>& r, cAu3xView<Real>& s ); // add arrays to be used as arguments q,qb,dqdx etc
         virtual void invdg( cAu3xView<Real>& s, cAu3xView<Real>& d ); // add arrays to be used as arguments q,qb,dqdx etc
         virtual void qupdt( cAu3xView<Real>& d ); // add arrays to be used as arguments q,qb,dqdx etc
         virtual void gtres( cAu3xView<Real>& v, cAu3xView<Real>& r );


         virtual void mg_restrict();
         virtual void postprocess();
         virtual void postprocess( Int it );
         virtual void ppostprocess();
         virtual void outputjl09();
         virtual void smooth2();
         virtual void smooth3();
         virtual void advance2();
         virtual void prolong();

/*       virtual void gradx0( Int ics, Int ice, Int *icql, Real *xql[], Real *dxdxl[], 
                                                Int *icqr, Real *xqr[], Real *dxdxr[] );
         virtual void gradq0( Int ics, Int ice, Int *icql, Real *xql[], Real *ql[], Real *dqdxl[][3],
                                                Int *icqr, Real *xqr[], Real *qr[], Real *dqdxr[][3] );*/

         //virtual void grad( Real *v[], Real **dv[] );
         virtual void grad();
         //virtual void gradb( Real *v[], Real **dv[] );
         virtual void gradb();
         //virtual void grads( Real **dv[] );
         virtual void grads();
         virtual void grad0( Int ics, Int ice, Int *icql, Real *xql[], Real *ql[], Real *dxdxl[], Real **dqdxl[],
                                               Int *icqr, Real *xqr[], Real *qr[], Real *dxdxr[], Real **dqdxr[] );
         virtual void grad0( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xql, cAu3xView<Real>& ql, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl,
                                               cAu3xView<Int>& icqr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr );
         virtual void grad0( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& xq0, cAu3xView<Real>& q0, cAu3xView<Real>& dxdx0, cAu3xView<Real>& dqdx0 );
         virtual void deltq( Int iql,Int idl, Real *xql[], Real *ql[], Real *dxdxl[], Real **dqdxl[], 
                             Int iqr,Int idr, Real *xqr[], Real *qr[], Real *dxdxr[], Real **dqdxr[], 
                                              Real *xn, Real *wn,  Real *dql0, Real *dqr0, Real *dql, Real *dqr );
         virtual void deltq_exact( Int iql,Int idl, Real *xql[], Real *ql[], Real *dxdxl[], Real **dqdxl[], 
                                   Int iqr,Int idr, Real *xqr[], Real *qr[], Real *dxdxr[], Real **dqdxr[], 
                                                    Real *xn, Real *wn,  Real *dql0, Real *dqr0, Real *dql, Real *dqr );

         virtual void get( cTabData * );
         virtual void set( cTabData * );

         virtual void resd( cAu3xView<Real>& , Real * );
         virtual void resd2( cAu3xView<Real>& , Real * );
         virtual void gtrhs( cAu3xView<Real>& );
         virtual void debugf( Int n, cAu3xView<Real>& v, string fnm );
         virtual void rsmth( cAu3xView<Real>& v, cAu3xView<Real>& r, Int, Real );
         virtual void newton();
         virtual void maverage( bool bposix );

         virtual void request( Int, Int *, Int *, Int *, Real ** );
         virtual void service( Int, Int, Int, Int, Real *[], Real **, bool );
         virtual void service( Int ig, Int nxr, Int nvr, Int nqr, Real *sxr, Real **sqr );
         virtual void accept( Int, Int, Int, Real *, Real * );

         virtual void tecplot();
         virtual void saveunk();

         virtual void cell2node();
         void vtk();
         void vtk(Int nvar, Real *var[], string fnm);
         void output_cgns();

         virtual void check_diflx();
         virtual void check_dmflx();
         virtual void check_diflxmuscl();

//linearsed frequency  domain method
         void comp_z();
         void comp_z( Int ifre );
         void gtrhf_z( Int ifre, cAu3xView<Real>& r_re, cAu3xView<Real>& r_im );
         void gtrhm_z( Int ifre, cAu3xView<Real>& r_re, cAu3xView<Real>& r_im );
         void gtrhs_z( Int ifre );
         void couple_z( Int ifre );
         void grad_z( Int ifre, Real *v_re[], Real **dv_re[], Real *v_im[], Real **dv_im[] );
         void gradb_z( Int ifre, Real *v_re[], Real **dv_re[], Real *v_im[], Real **dv_im[] );
         void bcs_z( Int ifre );

         void grad0_z( Int ics, Int ice, Int *icql, Real *xql[], Real *zl_re[], Real *zl_im[], Real *dxdxl[], Real **dzdxl_re[], Real **dzdxl_im[],
                                         Int *icqr, Real *xqr[], Real *zr_re[], Real *zr_im[], Real *dxdxr[], Real **dzdxr_re[], Real **dzdxr_im[]  );
         virtual void grads_z( Real **dv_re[], Real **dv_im[] );
         void gtrhsds( cAu3xView<Real>& r );


         void tecplot_z( Int ifre, Int ishift );
         void vtk_z( Int ifre, Int ishift );
         virtual void save_z();
         virtual void read_z();
         virtual void rebuild_z( Int id );

         virtual void init_z();
         virtual void readzbnd( Int ig0 );
         virtual void readzsetup();
         virtual void fftbnd();
         virtual string get_resd_info();

//initialize solution and bc
         virtual void init_vol();
         virtual void init_bcs();

  };

   typedef void (*bpl_t)( cDomain *, string , Int , Int , Real *[], Int , Real *[] );

   void jactimerot( Int asct, Int nv, cJacBlk *blkjac, Real f);

#  endif
