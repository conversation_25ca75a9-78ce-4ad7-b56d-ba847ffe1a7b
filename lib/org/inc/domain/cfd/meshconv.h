#ifndef _MESHCONV_
#define _MESHCONV_

# include <iostream>
# include <string>
# include <domain/domain.h>
//# include <plotutils/pltdataset.h>
# include <field/grad.h>

   #define MxNGPs 100

   const Int tetface[3][4] = { { 1, 2, 0, 0 },
                               { 3, 3, 3, 1 },
                               { 2, 0, 1, 2 } };

   const Int pyraface3[3][4] = { { 0, 1, 2, 3 },
                                 { 4, 4, 4, 4 },
                                 { 1, 2, 3, 0 } };

   const Int pyraface4[4] = { 0, 1, 2, 3 };

   const Int prismface3[3][2] = { { 0, 3 },
                                  { 1, 5 },
                                  { 2, 4 } };

   const Int prismface4[4][3] = { { 0, 1, 2},
                                  { 3, 4, 5},
                                  { 4, 5, 3},
                                  { 1, 2, 0} };

   const Int hexface[4][6] = {  {0, 0, 1, 2, 3, 4},
                                {1, 4, 5, 6, 7, 7},
                                {2, 5, 6, 7, 4, 6},
                                {3, 1, 2, 3, 0, 5} };

   class cMeshconv
  {
     private:

        Int nx, np;
        Int ntri, nquad, ntet, npyra, nprism, nhex;
        Int *tri[3], *quad[4], *tet[4], *pyra[5], *prism[6], *hex[8];
        Int nbface3, nbface4;
        Int *bface3[6], *bface4[7];
        Int ng;
        string bgnm[MxNBG];
        Real *x[3];

        bool sameface_weak( Int ip0, Int ip1, Int ip2, Int ip3, Int jp0, 
                            Int jp1, Int jp2, Int jp3);
        bool sameface_weak( Int ip0, Int ip1, Int ip2, Int jp0, Int jp1, 
                            Int jp2 );
        void bface2ele();
        void readgrid( ifstream *fle, Int *nx, Int *gnp, Int *gne, Int *ng, 
                       string *bgnm, Int gnb[MxNBG][MxNSk], Real **gxp, 
                       Int *giep[MxNSk], Int *gibp[MxNBG][MxNSk]);
     public:
        cMeshconv();    
       ~cMeshconv();    
 
        void writeplt( string fnm, int nblade, int mblade );
        bool readgrid( string fnm );
        bool setgrid( Int gnx, Int gnp, Real *gxp, Int *gne, Int gng, 
                      string *gbgnm, Int gnb[MxNBG][MxNSk], Int *giep[MxNSk],
                      Int *gibp[MxNBG][MxNSk]);
  };


//   extern "C"
//  {
//     void outplt( int *lfle, const char *cfle, int *iel4, int *iel5, int *iel6,
//                  int *iel8, int *ifac3, int *ifac4, float *coord, int *nele4,
//                  int *nele5, int *nele6, int *nele8, int *npoin, int* nbous3, 
//                  int *nbou4, int *nstag, int *istag,int *nblade, int *mblade );
//  }
//
//   extern "C"
//  {
//     void outunk( int *lname, const char *cname, float *unkno, float *vrt, 
//                  float *dis, int *npoin, int *itime, int *dtstr, float *tmpgam,
//                  float *roref, float *uref, float *vrtref, float *disref, 
//                  int *nstag, float *omega, int *ivisc, int *iturbm, int *ivfl, 
//                  int *iret);
//  }

#endif
