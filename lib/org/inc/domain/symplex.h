#ifndef _SYMPLEX_
#define _SYMPLEX_

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 13:55:16 BST 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         abstract symplex representation

#  define        MxNPSs       20   // max number of points per symplex
#  define        MxNQSs       20   // max number of points per symplex
#  define        MxNSk        10   // max number of symplex kinds

#  include      <topo/topo.h>

   class cSymplex
  {
      protected:
         Int                              nsp,nsq;

      public:
                                          cSymplex();
         virtual                         ~cSymplex();

         virtual Int                      gettype(){ return -1; };
         virtual Int                      getnp(){ return nsp; };
         virtual Int                      getnq(){ return nsq; };

         virtual void                     graph( Int iss, Int ise, Int *isq[], Int *lgq[], Int **igq, Int *isg[] );


  };


#  endif
