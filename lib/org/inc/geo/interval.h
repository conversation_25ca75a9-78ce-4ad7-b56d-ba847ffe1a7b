#  ifndef _INTERVAL_
#  define _INTERVAL_

#  include <cprec.h>

/** Compute the signed distance between a point and an interval.
   @param  x0                                   Interval lower bound.
   @param  x1                                   Interval upper bound.
   @param  x                                    Point.
   @brief  Signed distance.
  */
   Real sgdst( Real x0, Real x1, Real x );

/** Compute the intersection between an interval and the left semi-interval.
   @param  x0                                   Interval lower bound.
   @param  x1                                   Interval upper bound.
   @param  y0                                   Semi-interval.
   @brief  Interval intersection.
  */
   void left( Real *x0, Real *x1, Real x );

/** Compute the intersection between an interval and the right semi-interval.
   @param  x0                                   Interval lower bound.
   @param  x1                                   Interval upper bound.
   @param  y0                                   Semi-interval.
   @brief  Interval intersection.
  */
   void right( Real *x0, Real *x1, Real x );

/** Compute the intersection between two intervals. The first interval is over-written with the intersection.
   @param  x0                                   First interval lower bound.
   @param  x1                                   First interval upper bound.
   @param  y0                                   Second interval lower bound.
   @param  y1                                   Second interval upper bound.
   @brief  Interval intersection.
  */
   void inters( Real *x0, Real *x1, Real y0, Real y1 );

   void sinters( Real *x0, Real *x1, Real x, Real d );

   bool inside( Real x0, Real x1,  Real x );
/** Compute whether intervals intersect. 
   @param  x0                                   First interval lower bound.
   @param  x1                                   First interval upper bound.
   @param  y0                                   Second interval lower bound.
   @param  y1                                   Second interval upper bound.
   @return TRUE if the intersection is non-empty.
   @brief  Interval intersection.
  */
   bool inters( Real x0, Real x1, Real y0, Real y1 );


#  endif
