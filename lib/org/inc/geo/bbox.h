#ifndef _BBOX_
#define _BBOX_

#   include <cprec.h>
#   include <const.h>
#   include <cmath>
#   include <iostream>
#   include <fstream>
#   include <utils/proto.h>
#   include <geo/interval.h>

/**@ingroup engineering
  *@{
 **/

/**@defgroup boxes Bounding boxes and cartesian intervals
  *@{
 **/

/** High aspect ratio: all boxes with aspect ratio lower than HIGHAR are considered thin boxes**/
#   define HIGHAR 1.e-4

/** Bounding boxes around 3-dimensional geometries */

    class cBbox
   {
       protected:

          bool            empty;                                     /**<Indicate whether the box is empty or not.
                                                                         True as soon as the box is instantiated.
                                                                         False as soon as it reaches finite size. **/

          Int             nx;                                        /**<Number of coordinates. **/

          Real            x0[3];                                     /**<Lower left corner.**/
          Real            x1[3];                                     /**<Top right corner.**/

       public:
//* Default constructor
          cBbox();
         ~cBbox(){};

/** setCorners() adds a single point to the box
   
   @param            n number of coordinates
   @param            x coordinates
   @return           no return type
   @brief            set box corners
   */ 
          virtual void setCorners( Int n, Real *x );


/** setCorners() finds the bounding box of a subset of points with Nx coordinates starting at entry ips and 
    ending at entry ipe 
   @param ips starting entry
   @param ipe last entry
   @param n number of coordinates
   @param x[] coordinates
   @return no return type
   @brief   set box corners
  */
          virtual void setCorners( Int ips, Int ipe, Int n, Real *x[] );

/** setCorners() finds the bounding box of a subset of points with Nx coordinates starting at entry ips and 
    ending at entry ipe accessing them through the list ipx. 
   @param ips starting entry
   @param ipe last entry
   @param ipx reference list
   @param Nx number of coordinates
   @param x[] coordinates
   @return no return type
   @brief set box corners
   */
          virtual void setCorners( Int, Int, Int *[], Int, Real *[] );

/** Resets the corners of a box. The box is returned to an empty state.
   @return no return type
 */
          virtual void resetCorners( );

/** Print the current state of the box for debugging purposes.
   @return no return type
 */
          virtual void print();

/** Area of the intersection with the box data.
   @param data a pointer to cBox
   @return return a Real
 */
          virtual Real area( cBbox *data );
//        virtual void global();

/** Length of the longest side of the box
   @return return Real
 */
          virtual Real length();

/** Length of the shortest side of the box
   @return return Real
 */
          virtual Real minlength();

/** Area of the box.
   @return return a Real
*/
          virtual Real area();

/** determines whether a point is inside or outside a box 
   @param x[] point coordinates
 */
         virtual bool inside( Real x[] );

/** determines whether a whole box is inside or outside a box
   @param data box
 */
         virtual bool inside( cBbox *data );

/** Find the union with the box data
   @param    data               box.
 **/
         virtual void add( cBbox *data );

/** Expand a box in all directions by the length data.
   @param     data              Growth length.
 **/
         virtual void grow( Real data );
   };

/** bintrsct() determines whether two boxes box1 and box2 intersect.
    If both box areas are non-vanishing, bintrsct returns true if the
    intersection area is larger than 99% of each area. If one of the boxes
    has vanishing area, bintrsct checkes that one the box is entirely contained
    inside the other.
   @param *box1 first box
   @param *box2 second box
   @brief determines whether two boxes intersect or not
 */
      bool bintrsct( cBbox *box1, cBbox *box2 );

/** boverlap() determines whether two boxes box1 and box2 overlap.
   @param *box1 first box
   @param *box2 second box
   @brief determines whether two boxes overlap or not
 */
      bool boverlap( cBbox *box1, cBbox *box2 );

/**
  *@}
 **/

/**
  *@}
 **/

#endif
