#  ifndef _KDTREE_
#  define _KDTREE_

#  include <iostream>
#  include <fstream>
#  include <string>
#  include <cmath>
#  include <cstdlib>
#  include <sort/proto.h>

#  include <geo/bbox.h>


/**@ingroup engineering
  *@{
 **/

/**@defgroup kdtree KD-Trees
    Binary search trees for nearest neighbour enquiries.
    KD-Trees operate starting from a set of points in D-dimensions. The space is recursively split along the D 
    available directions using the positions of the points. This define a set of boxes. The subdivision of space
    stops when all boxes contain at most one internal point. 
    See Numerical Recipies in C. (Reference to be completed)
  *@{
 **/

/** Signed distance between intervals 
 **/
   Real dist( Int, Real *, Int, Real *x[] );

/** A box in a KD tree.
 **/
   class cKdBox:public cBbox
  {
      protected:
         Int parent;                                        /**< Parent box**/
         Int child[2];                                      /**< Children **/
         Int ips,ipe;                                       /**< Range of indexes corresponding to points in the box**/
      public:
         cKdBox();
         cKdBox(Int Parent, Int Child0, Int Child1, Int Ips, Int Ipe ); 
         cKdBox(Real X0[], Real X1[], Int Parent, Int Child0, Int Child1, Int Ips, Int Ipe ); 
         void range( Int *Ips, Int *Ipe ){ *Ips=ips; *Ipe=ipe; };
         void corners( Real X0[], Real X1[] );
         void setChildren( Int Child0, Int Child1 ){ child[0]=Child0;child[1]= Child1; };
         void getChildren( Int *Child0, Int *Child1 ){ *Child0=child[0]; *Child1=child[1]; };
         Int getParent( ){ return parent; };
         void plot();
         Real dist( Int, Real * );
         Real dist( Int, Int, Real *[] );
  };


/** KD-Tree class **/

   class cKdTree
  {
      protected:
         Int            nx;                                  /**< number of space directions**/
         Int            np;                                  /**< Number of points **/
         Int            nbxs;                                /**< Number of boxes **/
         cKdBox        *bxs;
         Int           *iprm,*iprmi;
         Real          *xp[3];

//       void sift_down( Int , Real *, Int * );

      public:
         cKdTree();
/** Build a KdTree from a cloud of points
   @param           Nx         Number of physical coordinates
   @param           Np         Number of points
   @param           x          Coordinates
 **/
         void build( Int Nx,Int Np, Real *x[] );

         Int locate( Real * );
         Int locate( Int );
         Int  nearest( Real * );
         void nearest( Real *, Int *, Real * );
         void nearest( Int , Int , Int *, Real * );
         void nearest( Real *, Int, Int *, Real * );
         void print( Int );
         Real dist( Int, Int );
         Real dist( Real *, Int );
         void clean();
        ~cKdTree();
  };

/**@
  *@}
 **/
/**@
  *@}
 **/

#  endif
