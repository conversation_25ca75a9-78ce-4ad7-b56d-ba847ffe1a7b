#  ifndef _POLYSET_
#  define _POLYSET_

#  include <geo/2d/polyline.h>
#  include <geo/2d/fillet.h>
//#  include <plotutils/pltdataset.h>

/**@ingroup interp
  *@{
 **/


/** A collection of non-intersecting polygons.
 **/

   class cPolyset
  {
      public:    

         Real             *xdef[2];            /**< Points for display. This is actually wrong, shouldn't be here */

         Int                 np;               /**< Number of polygons */
         Int                 mp;               /**< Maximum number of polygons */
         cCPolyline        **pl;               /**< Polygons */
      public:
                             cPolyset();
         virtual            ~cPolyset();

/** Builds a new polygon set from the union or intersection of the polygon sets p1 and p2.
    If flag is true then union is performed, if flag is false then intersection is performed.
   @param               p1          First polygon set.
   @param               p2          Second polygon set.
   @param               flag        Union/intersection flag.
   @brief Boolean union/intesection constructor. 
 **/
                             cPolyset( cPolyset *p1, cPolyset *p2, bool flag );

/** Builds a new polyogin set from the union or intersection of the polygon sets p1 and p2 and
    returns the crossings between the two.
    If flag is true then union is performed, if flag is false then intersection is performed.
   @param               p1          First polygon set.
   @param               p2          Second polygon set.
   @param               flag        Union/intersection flag.
   @param               data        Crossings data structure.
   @brief Boolean union/intesection constructor:
   @see   crossings_t
 **/
                             cPolyset( cPolyset *p1, cPolyset *p2, bool flag, crossings_t *data );

/** Add a new polygon to the set. It assumes the polygon does not intersect any of the other polygons already
    in the set 
   @param               var        Polygon.
 **/
         virtual void        add( cCPolyline *var );

/** Find and report a set of consistent crossings between to sets of polygons.
   @param                p         The second set.
   @param                var       The crossings. 
   @param                flag      A flag that decides whther the crossings should be processed for union operations
                                   (true) or intersection operations (false).
 **/
         virtual void        crossings( cPolyset *p, crossings_t *var, bool flag );

/** Return true if the point is inside the polygon set.
   @param                 x         Point
   @return                          true if x is inside the polygon set.
 **/
         virtual bool        inside( Real *x );

/** Invert the definition of all polygons in the polygon set.
 **/
         virtual void        invert( );

/** Clear the definition of the polygon set. Destroys all polygons associated with the set.
 **/
         virtual void         clear( );

/** Rotate the polygon set around the point x0 by an angle dt
   @param              x0         Rotation center.
   @param              dt         Rotation angle.
 **/
         virtual void rotate( Real *x0, Real dt );

/** Translate the polygon set by a vector dx 
   @param               dx          Translation vector.
 **/
         virtual void translate( Real *dx );

/** Mirror the whole polygon set with respect to a line going through the point x0 with direction dx
   @param               x0          Point through the mirroring axis.
   @param               dx          Mirroring axis direction.
 **/
         virtual void mirror( Real *x0, Real *dx );

/** Scale the whole polygon set with respect to the point x0 by a factor f
   @param               x0          Scaling origin.
   @param               f           Scaling factor.
 **/
         virtual void scale( Real *x0, Real f );

/** Copy constructor.
   @param               p            Destination.
 **/
         virtual void copy( cPolyset **p );

/** Check the polygon set by generating output suitable for gnuplot visualisation.
   @param               fnme         Output file name.
 **/
         virtual void        check( string fnme );

/** Check the polygon sets by generating output suitable for gnuplot visualisation in one plot **/
         virtual void        gnuplot( string fname);

/** Exports all the sides in the polygon set.
    The sides can be accessed through a loop of the type
    ien=0
    for( i=0<n;i++ )
   {
       ist= ien;
       ien= l[i];
       for( j=ist;j<ien;j++ )
      {
          ptr= p[j];
      }
   }
   @param             n              Number of polygons.
   @param             l              Index to sides
   @param             p              Sides array.
 **/
         virtual void        sides( Int *n, Int *l, cPolyline *p[] );


/** Exports all the polygons in the polygon set.
   @param             n              number of polygons.
   @param             p              polygons.
 **/
         virtual void     polygons( Int *n, cCPolyline *p[] );


/** Perform boolean union between the current polygon set and polygon set p
   @param             p               The polygon defining the union.
 **/
         virtual void        add( cPolyset * );

/** Perform boolean intersection between the current polygon set and set p
   @param             p               The polygon set defining the intersection.
 **/
         virtual void  intersect( cPolyset * );


//         void getdataset( cPltDataSet *pds );

/** Retern the pointer to the entity tagging the side of the polygon set contained within a box.
   @param             data            Box
 **/
         cUid   *select( cBbox *data );

/** Create approximating polygons for the polylines in the data set.
   @param             data            Box
 **/
         void apprx();

  };

/**
  *@}
 **/

#  endif
