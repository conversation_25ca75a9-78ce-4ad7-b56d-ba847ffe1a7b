#  ifndef _2DUTILS_
#  define _2DUTILS_

#  include <geo/2d/interp.h>
#  include <geo/2d/fillet.h>
#  include <geo/2d/cap.h>
#  include <geo/2d/polyline.h>
#  include <geo/2d/circle.h>
#  include <geo/2d/bezier.h>
#  include <geo/2d/straight.h>
#  include <geo/2d/spline.h>

/**@ingroup interp 
  *@{
 **/

/**@defgroup inter_putils Utility constructors
  *@{
 **/

/** Construct a sequence of straight segments using a list of vertices.
   @param          n                 Number of vertices.
   @param          x                 Vertex coordinates.
   @param          p                 List of straight segments. On entry p must be of size >=n.
 **/
   void build( Int n, Real *x[], cInterp **p );

   void build( Int n, Real *x[], cPolyline **p );

   void build( Int n, fillet_t *fl, cInterp **p, cInterp **f, Real *s[2], Real *d[2] );

   void build( cInterp *p, cInterp **w, Real *s[], Int nd, Real *l, Real *d0, Real *d1, Real fct );

   void build( Int n, cInterp **p, fillet_t *f, cPolyline **v );
   void build( cInterp *p, Int ns, Real *ls, Real *ds0, Real *ds1, Real fct, fillet_t *fl, cPolyline **v );

   void build( cInterp *p0, cInterp *p1, cap_t *c0, cap_t *c1, cCPolyline **z );

/**
  *@}
 **/

/**
  *@}
 **/

#  endif

