#  ifndef _FILLET2D_
#  define _FILLET2D_

#  include <geo/2d/circle.h>
#  include <geo/2d/bezier.h>
#  include <geo/2d/straight.h>

/**@ingroup interp 
  *@{
 **/

/**@defgroup fillets Fillets in two space dimensions
  *@{
 **/


/** Enum to represent different fillet type */
   
   enum fillet_e{ fillet_bad=-1,          /**< Uninitialized fillet type */
                  fillet_arc,             /**< Circular arc fillet */
                  fillet_bzr,             /**< Bezier arc fillet */
                  fillet_cmf              /**< Linear chamfer fillet */
                };

/** A data structure for representing fillets in two space dimensions **/
   struct fillet_t
  {
      fillet_e   typ;  /**< Fillet type **/
      Real       r;    /**< Fillet radius **/
      Real       d;    /**< Fillet angle(?) for chamfered fillets **/
      inter_t    stat; /**< Fillet status **/
  };

 
/** Assign the value of data to the fillet type of var.
   @param        data           Fillet type 
   @param        var            Fillet
   @brief                       Fillet type
 **/
   void settype(Int data, fillet_t *var );

/** Check if the fillet var is valid
   @param        var            Fillet
   @return                      TRUE if the fillet is valid, FALSE otherwise
   @see                         stat_t
 **/
   bool valid( fillet_t *var );

/** Build a fillet between two interpolatable objects p0 and p1.
   @param                  fl             Fillet 
   @param                  z0             Fillet starting point on the first object. On entry: initial estimate. On exit: final position.
   @param                  p0             First object.
   @param                  i0             Direction multiplier for the first object. If false the direction of the first object is reversed.
   @param                  z1             Fillet starting point on the second object. On entry: initial estimate. On exit: final position.
   @param                  p1             Second object.
   @param                  i1             Direction multiplier for the second object. If false the direction of the second object is reversed.
   @param                  f              Interpolatable object containing the fillet.
   @param                  d0             Left limit for the fillet.
   @param                  d1             Right limit for the fillet.
 **/
   void fillet( fillet_t *fl, Real *z0, cInterp *p0, bool i0, Real *z1, cInterp *p1, bool i1, cInterp **f, Real *d0, Real *d1 );

/**
  *@}
 **/
/**
  *@}
 **/

#  endif
