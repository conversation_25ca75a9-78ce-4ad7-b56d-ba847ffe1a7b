#  ifndef _BEZIER_
#  define _BEZIER_

#  include <string>
#  include <iostream>
#  include <fstream>

#  include <geo/2d/interp.h>

/**@ingroup interp_primitives
  *@{
 **/

/** Bezier splines */

   class cBzr: public cInterp
  {

      protected:

         Real        *wrk0[2];                       /**<Workspace #1*/
         Real        *wrk1[2];                       /**<Workspace #2*/
         Real        *wrk2[2];                       /**<Workspace #3*/
         Real        *wrk3[2];                       /**<Workspace #4*/
         Real        *x[2];                          /**<Support-points*/
         Real        *s;                             /**<Distances between support-points*/
         Int          n;                               /**<Number of support points*/

/** Interpolates between x0 and x1 at t.
    The returned values are y= t*x1+ (1-t)*x0 and dy = x1-x0.
   @param        t          Bezier coordinate
   @param        x0         First control point
   @param        x1         Second control point
   @param       *y          Position
   @param       *dy         Tangent vector
   @brief                   Interpolation.
   */
//       void bzint0( Real t, Real *x0, Real *x1, Real *y, Real *dy );

/** Recursive Bezier interpolation along a set of control points.
   @param        i0         First control point index.
   @param        n1         Last control point index.
   @param        t          Bezier coordinate
   @param       *y          Position
   @param       *dy         Tangent vector
   @param        z          Control points coordinates.
   @param       dz          Control points tangent vectors.
   @brief                   Interpolation.
   */

         void bzint( Int i0,  Int n1, Real t, Real *y, Real *dy, Real *z[], Real *dz[] );
         void cleanup();


      public:
                             cBzr();
         virtual            ~cBzr();

         interp_e gettype(){ return interp_bezier; };

 /** Build a Bezier spline.
   @param        n          Number of control points
   @param        x          Control points coordinates.
   */
         void build( Int n, Real *x[] );

         void copy( cInterp ** );

         void interp( Real t, Real *y, Real *dy );
         Real length();

         virtual void rotate( Real *, Real );
         virtual void translate( Real * );
         virtual void mirror( Real *, Real * );
         virtual void scale( Real *, Real );

         virtual void fillet( Real, Real, cInterp *, Real , cInterp * );

         virtual void pickle( size_t *len, pickle_t *buf );
         virtual void unpickle( size_t *len, pickle_t  buf );

         virtual void get( cTabData *var );
         virtual void set( cTabData *var );
  };

#  endif
