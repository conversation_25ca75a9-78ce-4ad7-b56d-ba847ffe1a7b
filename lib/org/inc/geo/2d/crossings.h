#  ifndef _XINGS2D_
#  define _XINGS2D_

#  include <utils/proto.h>
#  include <sort/proto.h>
#  include <geo/2d/linear.h>
#  include <geo/tol.h>

/**@ingroup interp
  *@{
 **/

/**@defgroup xings Crossing points between polygon sets.
  *@{
 **/

/** Maximum number of crossings 
   @deprecated              Move to dynamic store.
 **/
#  define   MXNC 1000

/** Data structure for multiple crossings between interp objects. 
   @todo                    Dynamic store.
 **/

   struct crossings_t
  {
      Int                   n;          /**< Number of crossings **/
      Int                   m;          /**< Dynamic store size **/
      Int                idat[9][MXNC]; /**< Integer flags in the following order
                                             idat[0]: polygon 1, 
                                             idat[1]: polygon 2, 
                                             idat[2]: direction, 
                                             idat[3]: active crossing, 
                                             idat[4]: origin,
                                             idat[5]: cumulative direction for multiple crossings.
                                             idat[6]: side of polygon 1 (for multiple crossing identification purposes).
                                             idat[7]: side of polygon 2 (for multiple crossing identification purposes).
                                             idat[8]: polygon in new entity.
                                            **/
      Real                  x[2][MXNC]; /**< crossing locations **/
      Real                  s[3][MXNC]; /**< crossing curvilinear coordinates.
                                             s[0] curvilinear coordinate on polygon 1,
                                             s[1] curvilinear coordinate on polygon 2,
                                             s[2] curvilinear coordinate on final polygon 1.
                                          */
      Real                  r[MXNC];    /**< crossing residual. **/
  };

/** Initialise a crossings data structure.
   @param              var                       the crossings.
 **/
   void  init(           crossings_t *var );

/** Sort the crossings according to their polygon of origin and their curvilinea coordinate
   @param              var                       the crossings.
 **/
   void  sort(           crossings_t *var );

/** Print the crossings.
   @param              var                       the crossings.
 **/
   void print(           crossings_t *var );

/** Collect together crossings with the same location
   @param              var                       the crossings.
 **/
   void collect(         crossings_t *var );

/** Prune the crossings to remove degenerate cases. 
    Degenerate cases are sequences of independent crossings with identical orientation. 
    These are all collapsed into one crossing.
   @param              var                       The crossings.
   @param              flag                      true for union, false for intersection.
 **/
   void prune(           crossings_t *var, bool flag );

/** Prune the crossings from ist to ien to remove degenerate cases.
    Degenerate cases are sequences of independent crossings with identical orientation. 
    These are all collapsed into one crossing.
   @param              ist                       Starting index.
   @param              ien                       Ending index.
   @param              var                       The crossings.
   @param              flag                      true for union, false for intersection.
 **/
   void prune( Int, Int, crossings_t *var, bool flag );

/** Insert a new crossing into the crossing list.
   @param              stat                      the new crossing to insert.
   @param              var                       The crossing list.
   @return                                       TRUE if the crossing is a genuine new crossing and is inserted in the list..
 **/
   bool insert( inter_t *stat, crossings_t *var );

/** Find the next available crossing.  The next available crossing is definied as 
    a crossing with index larger than ist and with curvilinear coordinate no smaller than s, measured along polygon ic.
   @param              s                         Starting curvilinear coordinate.
   @param              ic                        0 if the next crossing is to be found along polygon 0, 1 if it is be
                                                 found along polygon 1.
   @param              ist                       Starting index.
   @param              var                       Crossing list.
   @return                                       The new crossing index or -1 if no suitable crossing is found.
 **/
   Int nextc( Real s, Int ic, Int ist, crossings_t *var );

/** Startup a new sequence at crossing ic.
   @param                 ic                     Starting location for new sequence.
   @param                 var                    Crossings list.
 **/
   void start( Int ic, crossings_t *var );

/** Swap two crossings at i0 and i1
   @param                 i0                     First index.
   @param                 i1                     Second index.
   @param                 var                    Crossing list.
 **/
   void swap( Int i0, Int i1, crossings_t *var );

/**
  *@}
 **/
/**
  *@}
 **/

#  endif
