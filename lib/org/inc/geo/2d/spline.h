//3456789 123456789 123456789 123456789 123456789 123456789 123456789 12
//       1         2         3         4         5         6         7

//    Author          <PERSON> <<EMAIL>>
//    Created         Sat Feb  9 14:23:42 GMT 2008
//    Changes history -
//    Next change(s)  ( work in progress )
//    Purpose         spline representation 
//    Arguments       -

#ifndef _SPLINE_
#define _SPLINE_

#     include        <iostream>
#     include        <fstream>
#     include        <cmath>
#     include        <geo/2d/interp.h>
#     include        <lin/thomas.h>

/**@ingroup interp_primitives
  *@{
 **/


/* unvariate natural splines */

   class cSpline: public cInterp
  {
      protected:

         Int        iord;       //!< spline order 

         Int        nv;         //!< number of variables.
         Int        np;         //!< number of support points.

         Real        l;         //!< spline length (based on distances between support points).
         Real       *s;         //!< curvilinear coordinates.
         Real      *sx;         //!< values.
         Real      *sz;         //!< interpolation coefficients.
         Real      **x;         //!< Values. Access pointer.
         Real      **z;         //!< Interpolation coefficients. Access pointer.
         Real      *dx0;        //!< tangent vector at first support point.
         Real      *dx1;        //!< tangent vector at last support point.
         bool      bdx0;        //!< Use natural spline at first support point.
         bool      bdx1;        //!< Use natural spline at last  support point.

         void    cleanup();
         virtual void fit( );
         virtual void wrap(Real * ){};

      public:

         cSpline();
         virtual ~cSpline();

         virtual interp_e gettype(){ return interp_spline; };
         virtual void build( Int, Int, Int, Real *[], Real *, Real * );

         virtual void interp( Real, Real *, Real * );
         Real length(){ return l; };

         virtual void displace(cInterp **var, Real ls[], Real d[], Real fct);
         virtual void displace(cInterp **var, Real *l0, Real d0, Real *l1, Real d1);

         virtual void copy( cInterp ** );

         virtual void rotate( Real *, Real );
         virtual void translate( Real * );
         virtual void mirror( Real *, Real * );
         virtual void scale( Real *, Real );

         virtual Real gets( Int );

         virtual void pickle( size_t *len, pickle_t *buf );
         virtual void unpickle( size_t *len, pickle_t buf );

         virtual void get( cTabData * );
         virtual void set( cTabData * );

         virtual void plot( Real l0, Real l1, ofstream *fle );

         virtual void property( string lbl, Real *var );
         virtual void property( string lbl, Int  *var );

  };

   class cCSpline: public cSpline
  {
      protected:

         void fit();
         void wrap(Real * );

      public:
	 cCSpline();
         virtual interp_e gettype(){ return interp_cspline; };
         virtual  bool periodic(){ return true; };
	 virtual ~cCSpline();

         virtual void copy( cInterp ** );

  };

/**
  *@}
 **/

#endif 
