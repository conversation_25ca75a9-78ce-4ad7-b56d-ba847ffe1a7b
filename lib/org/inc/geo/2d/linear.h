#  ifndef _LINEAR2D_
#  define _LINEAR2D_

#  include <cmath>
#  include <const.h>
#  include <lin/small.h>
#  include <geo/interval.h>

/**@ingroup engineering
  *@{
 **/

/**@defgroup lineargeo2d Linear geometry in two dimensions.
  *@{
 **/


/** A structure to hold information about intersections between lines and 2D entities.
 **/
   struct inter_t
  {
      Real        y1[2];            /**< Position of the intersection as interpolated on the first curve.**/
      Real        y2[2];            /**< Position of the intersection as interpolated on the second curve.**/
      Real        dy1[2];           /**< Tangent to the first curve at the intersection **/
      Real        dy2[2];           /**< Tangent to the second curve at the intersection **/
      Real        det;              /**< determinant of the matrix dy1 dy2 **/
      Real        ds1;              /**< Last correction on the first curve **/
      Real        ds2;              /**< Last correction on the second curve **/
      Real        res;              /**< Last residual (norminf y1-y2) **/
      Int         it;               /**< Iteration count upon exit **/
      Real        rdat[2][5];       /**< Set of real auxiliary values (User specified)**/
      Int         idat[2][5];       /**< Set of integer auxiliary values (User specified)**/

      Int         idnt;             /**< Identity tag **/
  };

/** Print information relative to a inter_t structure.
   @param  data 
 **/
   void print( inter_t data );

/** Determine whether an intersection is valid or not. In order to be valid an intersection must have
    residual less than ITPPTOL, be within maximum iteration count and have non-zero determinant 
   @param  data 
   @return  true if the intersection is valid.
 **/
   bool valid( inter_t data );


/** Intersection between two line segments. 
    The line segments have endpoints p0,p1 the first segment and q0,q1 the second segment.
    The function computes the curvilienar coordinates s and t of the intersection on the two lines, if it
    exists (s=0 corresponds to p0, s=1 to p1, similarly t=0 corresponds to q0,t=1 to q1. ).
    The function also computes the determinant of the matrix formed by the direction vectors of the two segments.
   @param             p0               First segment, left endpoint.
   @param             p1               First segment, right endpoint.
   @param             q0               Second segment, left endpoint.
   @param             q1               Second segment, right endpoint.
   @param             s                Curvilinear coordinate of the intersection on the first line.
   @param             q1               Curvilinear coordinate of the intersection on the second line.
   @param             d                Determinant.
 **/
   void llint( Real *p0, Real *p1, Real *q0, Real *q1, Real *s, Real *t, Real *det );

/** Intersection between two line segments. 
    The line segments have endpoints p0,p1 the first segment and p1 the second. The second segment is 
    further represented by its direction t1.
    The function computes the curvilienar coordinates s and t of the intersection on the two lines, if it
    exists (s=0 corresponds to p0, s=1 to p1 ).
    The function also computes the determinant of the matrix formed by the direction vectors of the two segments.
   @param             p0               First segment, left endpoint.
   @param             p1               First segment, right endpoint.
   @param             q0               Second segment, left endpoint.
   @param             q1               Second segment, direction.
   @param             s                Curvilinear coordinate of the intersection on the first line.
   @param             q1               Curvilinear coordinate of the intersection on the second line.
   @param             d                Determinant.
 **/
   void ldint( Real *p0, Real *t0, Real *p1, Real *t1, Real *s, Real *t, inter_t *stat );

   void lsinters( Real *x0, Real *l0, Real a0, Real a1, Real a2, Real *t0, Real *t1 );
   void ltinters( Real *x0, Real *l0, Real *t0, Real *t1 );
   void lqinters( Real *x0, Real *l0, Real *t0, Real *t1, Real *c0, Real *c1 );

   void box2( Real *x0, Real *x1 );
   void box2( Real *x0, Real *x1, Real *x2, Real *xmin, Real *xmax );

   void square2( Real *x0, Real *x1 );
   bool inside2( Real *x0, Real *x1, Real *x );
   bool inters2( Real *x0, Real *x1, Real *y0, Real *y1 );

/**@
  *@}
 **/
/**@
  *@}
 **/

# endif
