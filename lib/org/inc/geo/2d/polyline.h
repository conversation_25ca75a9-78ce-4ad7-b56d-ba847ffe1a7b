#  ifndef _POLYLINE_
#  define _POLYLINE_

#  include <geo/2d/interp.h>
#  include <geo/2d/crossings.h>
#  include <geo/2d/fillet.h>

/**@ingroup interp
  *@{
 **/

/** Lines built by patching together multiple segments with different local parameterizations.
   @todo                    Shear transformation, closest vertex (returns a point), baricenters, momenta 
 **/

   class cPolyline: public cInterp
  {
      protected:
         Int                                  ns;                 /**< Number of segments **/
         size_t                               ms;                 /**< Maximum number of segments in storage **/
         cInterp                           **sds;                 /**< Polyline sides **/
         Real                              *lims[3];              /**< Place-holders for the polyline sides.
                                                                       The polyline uses the representation
                                                                       of side i between the curvilinear coordinates
                                                                       lims[0][i] and lims[1][i]. lims[2][i] is the
                                                                       curvilinear coordinate at the end of the section
                                                                       represented by side i.
                                                                   **/
         virtual void                      local( Real *s, Int *i ); /**< Given a curvilinear coordinate s
                                                                          finds the side i representing the polyline
                                                                          around s and computes the corresponding
                                                                          local curvilinear coordinate on side i
                                                                         @param s       On entry: global curvilinear
                                                                                        coordinate.
                                                                                        On exit: local curvilinear
                                                                                        coordinate.
                                                                         @param i       On exit: side containing the
                                                                                        point with global coordinate s.
                                                                      **/
         virtual void                     global( Real *s, Int i   );/**< Given a curvilinear coordinate s
                                                                          and the side i containing it 
                                                                          computes the corresponding
                                                                          global curvilinear coordinate.
                                                                         @param s       On entry: local curvilinear
                                                                                        coordinate.
                                                                                        On exit: global curvilinear
                                                                                        coordinate.
                                                                         @param i       On entry: side containing the
                                                                                        point with local coordinate s.
                                                                      **/
         virtual Int                       which( Real );
         virtual void                      wrap( Real * ){};

      public:
                                               cPolyline();
                                              ~cPolyline();

         interp_e gettype(){ return interp_polyline; };

         virtual void                             displace(cPolyline **pl, Real ls[], Real d[], Real fct);

         virtual Real                             length();
         virtual void                                add( Real, Real , cInterp * );
         virtual void                             interp( Real, Real *, Real * );
         virtual void                             approx( Real, Real, Real, Real,  Int *, Real *[] );

         virtual void                               copy( cInterp ** );
//       virtual void                                add( cPolyline *, Int, Real, Int, Real );
         virtual void                                any( Real * );

         virtual void                             rotate( Real *, Real );
         virtual void                          translate( Real * );
         virtual void                             mirror( Real *, Real * );
         virtual void                              scale( Real *, Real );
         virtual void                              shear( Real *x0, Real *ds[]);
// this should be the final interface, temporarily replaced by the one with cPolyline *[] while valgrind is not available.
         virtual void                              sides( Int *, cPolyline *** );
         virtual void                              sides( Int *, cPolyline *[] );
         virtual Real                              where( Int i ){ return lims[2][i]; };

         virtual void                        invert();
//       virtual void                    crossings( cPolyline *, Int *, inter_t ** );
         virtual void                    crossings( cPolyline *, crossings_t *var );


         virtual void                         debug( string tab );

         virtual void                         plot(Real l0, Real l1, ofstream *fle );

/** Unpacks the interpolatable object into a sequence of elementary curves, if possible, otherwise only
    produces a copy of the object.
   @param          l0                  Lower curvilinear coordinate bound.
   @param          l1                  Upper curvilinear coordinate bound.
   @param           n                  Number of elementary curves.
   @param           m                  Storage size for elementary curves.
   @param         lim                  Limits
   @param         var                  Elementary curves
   @brief                              Unpack elementary curves.
 **/
         virtual void unpack( Real l0, Real l1, Int *n, Int *m, Real *lim[], cInterp ***var );

         virtual void pickle( size_t *len, pickle_t *buf );
         virtual void unpickle( size_t *len, pickle_t buf );

         virtual cUid *getsrc( Real s );
         virtual void settrace( cUid *obj, string data );

/** Insert fillets around a specified number of location along a polyline
   @param                  n                Number of fillets.
   @param                  f                Fillets.
   @param                  s                Corners.
 **/
         virtual void fillets( Int n, fillet_t *f, Real *s );

  };

   class cCPolyline: public cPolyline
  {
      protected:
         virtual void                      wrap( Real * );
      public:
                                       cCPolyline();
                                      ~cCPolyline();
         interp_e gettype(){ return interp_cpolyline; };
         virtual void                               copy( cInterp ** );

         virtual bool                          closed();
         virtual bool                          inside( Real * );
         virtual Real                          area();
         virtual bool                          periodic(){ return true; };

  };

#  endif
