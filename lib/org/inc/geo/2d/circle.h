#  ifndef _CIRCLE_
#  define _CIRCLE_

#  include <geo/2d/interp.h>

/**@ingroup interp_primitives
  *@{
 **/

/** Circular arcs **/

   class cCircle: public cInterp
  {
      protected:
         Real        dir;
         Real        ct,st;
         Real        r;
         Real        x[2];
         virtual void wrap( Real * );
      public:
                     cCircle();
                    ~cCircle();
         virtual Real length();


         interp_e gettype(){ return interp_circle; };

/** Build a circular arc given its radius, starting angle (as a cosine/sine pair) and centre.
   @param            r0                    Radius,
   @param            ct0                   Cosine of the starting angle.
   @param            st0                   Sine of the starting angle.
   @param            x0                    Centre.
 **/ 
         virtual void build( Real r0, Real ct0, Real st0, Real *x0 );

         virtual void displace(cInterp **var, Real ls[], Real d[], Real fct) { cout<<"THE CIRCLE OBJECT NEVER SUPPOSET TO USE SUCH DISPLACE METHOD! EXIT!\n"; exit(0); };


         virtual void copy( cInterp ** );

         virtual void interp( Real, Real *, Real * ); 

/** Build a circular arc as a fillet between two interpolatable objects.
   @param                     rf       Fillet radius.
   @param                     s0       Curvilinear coordinate on the first object.
                                       On entry: initial estimate. On exit: final computed vaue.
   @param                     p0       First object.
   @param                     k0       Direction flag for the first object.
   @param                     s1       Curvilinear coordinate on the second object.
                                       On entry: initial estimate. On exit: final computed vaue.
   @param                     p1       First object.
   @param                     k1       Direction flag for the second object.
   @param                     stat     Status.
 **/ 
         virtual void fillet( Real rf, Real *s0, cInterp *p0, bool k0, Real *s1, cInterp *p1, bool k1, inter_t *stat );


         virtual void rotate( Real *, Real );
         virtual void translate( Real * );
         virtual void mirror( Real *, Real * );
         virtual void scale( Real *, Real );
         virtual void displace( cInterp **var, Real *l0, Real d0, Real *l1, Real d1 );

         virtual bool periodic(){ return true; };

/** Unpacks the interpolatable object into a sequence of elementary curves, if possible, otherwise only
    produces a copy of the object.
   @param          l0                  Lower curvilinear coordinate bound.
   @param          l1                  Upper curvilinear coordinate bound.
   @param           n                  Number of elementary curves.
   @param           m                  Storage size for elementary curves.
   @param         lim                  Limits
   @param         var                  Elementary curves
   @brief                              Unpack elementary curves.
 **/
         virtual void unpack( Real l0, Real l1, Int *n, Int *m, Real *lim[], cInterp ***var );

         virtual void pickle( size_t *len, pickle_t *buf );
         virtual void unpickle( size_t *len, pickle_t buf );

         virtual void get( cTabData *var );
         virtual void set( cTabData *var );


         virtual void property( string lbl, Real *var );
         virtual void property( string lbl, Int  *var );

  };

   void fillets( Int, cInterp **, Real * );

/**
  *@}
 **/

#  endif

