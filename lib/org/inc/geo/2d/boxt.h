#  ifndef _BOXT_
#  define _BOXT_

#  include <cprec.h>
#  include <utils/proto.h>
#  include <geo/interval.h>
#  include <const.h>

   struct box_t
  {
      Real x0[2],x1[2]; 
  };

   void add( Int, Int, Real *[], box_t * );
   void add( Int, Int, cAu3xView<Real>&, box_t * );
   void reset( box_t * );
   void grow( Real, box_t * );
   void add(  box_t *b1, box_t *b2 );
   bool overlap( box_t *b1, box_t *b2 );
   void diag(  box_t b, Real *d );


#  endif
