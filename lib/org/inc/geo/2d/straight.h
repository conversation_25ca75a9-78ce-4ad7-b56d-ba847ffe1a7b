#  ifndef _STRAIGHT_
#  define _STRAIGHT_

#  include <geo/2d/interp.h>

/**@ingroup interp_primitives
  *@{
 **/

/** Straight line segments. The segment is specified by two points.
 **/

   class cStraight: public cInterp
  {
      protected:
         Real l;                                             /**< line length **/
         Real x0[2];                                         /**< First definition point. **/
         Real x1[2];                                         /**< Second definition point. **/
         Real dx[2];                                         /**< Distance vector between the two points. **/
      public:
         cStraight();
        ~cStraight();

         interp_e gettype(){ return interp_straight; };

         virtual void copy( cInterp ** );
         virtual void build( Real *, Real * );
         virtual Real length();
         virtual void interp( Real, Real *, Real * );

         virtual void rotate( Real *, Real );
         virtual void translate( Real * );
         virtual void mirror( Real *, Real * );
         virtual void scale( Real *, Real  );
         virtual void chamfer( Real f, Real *l0, cInterp *p0, Real *l1, cInterp *p1 );

         virtual void debug( string tab );

         virtual void shear( Real *, Real *[] );
         virtual void   pickle( size_t *len, pickle_t *buf );
         virtual void unpickle( size_t *len, pickle_t  buf );

         virtual void      get( cTabData * );
         virtual void      set( cTabData * );

         virtual void property( string lbl, Real *var );
         virtual void property( string lbl, Int  *var );
  };

/**
  *@}
 **/

#  endif
