#  ifndef _INTERP_
#  define _INTERP_

#  include <cmath>
#  include <cprec.h>
#  include <tag.h>
#  include <sort/proto.h>
#  include <lin/vect.h>
#  include <geo/bbox.h>
#  include <geo/boxtree.h>
#  include <geo/tol.h>
#  include <geo/2d/linear.h>

/**@ingroup engineering
  *@{
 **/

/**@defgroup interp Parametric curves in two space dimensions
  *@{
 **/

/** Minimum length of an interpolating segment for interpolatable objects 
 **/
#  define ITPMINLEN 1.e-18


/** Enumerator type for identifying new cInterp objects. 
   @brief      cInterp object type identifier
 **/
   enum interp_e { interp_bad=-1, interp_spline, 
                                  interp_straight, 
                                  interp_cspline, 
                                  interp_circle, 
                                  interp_bezier, 
                                  interp_polyline, 
                                  interp_cpolyline, 
                                  interp_num };

   const string interp_s[interp_num]={ "spline",
                                       "straight",
                                       "closed spline",
                                       "circular arc",
                                       "bezier",
                                       "polyline",
                                       "closed polyline"};

/** Abstract representation of parametric curves in two space dimensions
   @todo                    Simplified interp method with position only.
   @todo                    Extended interp method with analytical curvature estimate
 **/
   class cInterp:public cTag, public cTabSrc
  {
      protected:
 
         bool           indp;                                           /**< Boolean flag to check whether this object holds an independent representation (unused) **/
         Real           mxd;                                            /**< Maximum length of any approximating segment **/
         Real           mxa;                                            /**< Maximum angle between two consecutive approximating segments **/
         Int            na;                                             /**< Number of approximating segments **/
         Real          *xa[3];                                          /**< Approximating segments. xa[0] is the curvilinear coordinate, xa[1/2] are the x/y coordinates of each vertex on the approximating linear polygon **/
         cBoxTree      *bt;                                             /**< Box tree containing the approximating polygon. **/

/** Default constructor.
    This constructor is hidden to prevent direct allocation of cInterp objects. **/
         cInterp();                                             

      public:

/** Destructor **/
         virtual ~cInterp();

/** Return an enum identifying the type of interpolatable object for the purpose of pickling and unpickling.
   @return                      interp type
 **/
         virtual interp_e gettype(){ return interp_bad; };
 
/** Return true if the cInterp object holds an independent representation.
   @return      the independent representation state of the object.
   @brief       Independent representation eqnuiriy
 **/
         virtual bool indep(){ return indp; };


/** Instantiates *var and copies the current object into *var.
   @param       var             The destination of the copy operation. On entry, *var must be NULL, otherwise
                                the function will abort with an assertion. This behaviour is intendended to prevent
                                re-use of cInterp pointers which have not explicitly deallocated.
   @brief                       Copy constructor.
 **/
         virtual void copy( cInterp **var );

/** Interpolation of coordinates and tangent vectors along a one-dimensional curve.
    The parametric coordinate varies between 0 and 1 and is a normalised arc-length. 
    This is the main method of the cInterp class.
   @param             S              normalised arc-length.
   @param             Y              position at S.
   @param             dY             tangent vector dY= dY/dS evaluated at S.
   @brief                            Interpolation.
 **/
         virtual void interp( Real S, Real *Y, Real *dY ){};

/** Project orthogonally a point at position P onto the the current object. 
    The curvilinear coordinate of the projection point is returned in S, the nature of the projection point in 
    the structure stat.
   @param             P              Point to project.
   @param             S              On entry: the initial estimate of the curvilinear coordinate of the projection.
                                     On exit: the curvilinear coordinate of the projection.
   @param             stat           The status of the projection point.
   @brief             Orthogonal projection.
   @see               inter_t
 **/
         virtual void prjct( Real *P, Real *S, inter_t *stat );

/** Intersection between the current object and the spl cInterp object.
   @param             spl            Intersecting curve
   @param             S1             Curvilinear coordinate of the intersection on the first curve.
                                     On entry a fairly accurate initial estimate is needed.
   @param             S2             Curvilinear coordinate of the intersection on the second curve.
                                     On entry a fairly accurate initial estimate is needed.
   @param             stat           The status of the intersection.
   @brief             Intersection between curves.
   @see               inter_t 
 **/
         virtual void inters( cInterp *spl, Real *S1, Real *S2, inter_t * );


/** Intersection between the current object and a line starting at point P with direction L.
   @param             P               Line origin.
   @param             L               Line direction.
   @param             S1              Curvilinear coordinate of the intersection on the first curve.
                                      On entry a fairly accurate initial estimate is needed.
   @param             S2              Curvilinear coordinate of the intersection on the second curve.
                                      On entry a fairly accurate initial estimate is needed.
   @brief                             Curve line intersection.
 **/
         virtual void inters( Real *P, Real *L, Real *S1, Real *S2, inter_t * );

/** Return the local curvature (radius?) of the curve.
   @param             s               The curvature radius of the curve.
   @brief                             Local curvature. 
   @deprecated                        To be replaced by analytical curvature methods.
 **/
         virtual Real curv( Real s );

/** Approximation in straight segments. Builds the internal approximate representation.
    The internal approximate representation is based on a sequence of points, sorted in ascending curvilinear
    coordinate. The points approximate the curve in the sense that the distance between two consecutive points
    is less than mxd and the angle between two consecutive segments has sine less than mxa.
   @param             mxd             Maximum length of the approximating segment.
   @param             mxa             Maximum sine of the angle between consecutive segments.
 **/
         virtual void apprx( Real mxd, Real mxa );

/** Approximation in straight segments. Builds an external representation of the approximant polygon
    between the curvilinear coordinates l0 and l1.
   @param             l0              Starting curvilinear coordinate.
   @param             l1              Ending curvilinear coordinate.
   @param             mxd             Maximum length of the approximating segment.
   @param             mxa             Maximum sine of the angle between consecutive segments.
   @param             na              Number of approximating points.
   @param             xa              Approximating points. xa[0] are the curvilinear coordinates, xa[1/2] are the 
                                      cartesian x/y coordinates.
 **/
         virtual void approx( Real l0, Real l1, Real mxd, Real mxa, Int *na, Real *xa[] );

/** Builds the box tree around the internal representation of the approximant polygon.
 **/
         virtual void boxes( );

/** Writes to file a set of points representing the polygon.
    The output is suitable for gnuplot use.
   @brief             fle
 **/
         virtual void check( ofstream *fle );

         virtual void plot( Real l0, Real l1, ofstream *fle);


/** Returns the cartesian length of the curve.
 **/
         virtual Real length(){return 0;};

/** Rotate the object by an angle dt around the point x
   @param             x                Rotation center.
   @param             dt               Rotation angle.
   @brief                              Rotate.
 **/
         virtual void rotate( Real *x, Real dt ){};

/** Translate by a vector v.
   @param             v                Translation vector.
   @brief                              Translate.
 **/
         virtual void translate( Real *v ){};

/** Mirror the object with respect to a mirroring axis through the point x0 with direction l
   @param             x0               Mirroring axis origin.
   @param             l                Mirroring axis direction.
   @brief                              Mirroring.
 **/
         virtual void mirror( Real *x0, Real *l ){};

/** Scaling with factor l and origin x0.
   @param             x0               Scaling origin.
   @param             l                Scaling factor.
   @brief                              Scaling.
 **/
         virtual void scale( Real *x0, Real l ){};

/** Apply a shearing transformation with shear rate ds and origin x0.
   @param            x0                Shear origin.
   @param            ds                Shear tensor.
   @todo                               Implement for all objects. Currently only straight lines are sheared.
 **/
         virtual void shear( Real *x0, Real *ds[] );

/** this is the displace method for spline particularly **/
         virtual void displace( cInterp **p, Real [], Real [], Real ) {};

         virtual void displace( cInterp **p, Real *, Real, Real *, Real );

         virtual bool periodic(){ return false; };

         virtual cUid *getsrc( Real ){ return cTag::getsrc(); };

/** Return the pointer to the object that initially tagged the part of an interpolatable object contained in the
    box data
   @param              data           The bounding box.
 **/
         virtual cUid *select( cBbox *data );

/**find the minimum gap between two cInterp objects and record their curvilinear coordinates **/
         void  mindisf2(cInterp *obj, Real *s0, Real *s1,  Real *res);


/** Print to standard output the internal state of the object for debugging purposes.
   @param             left justification tab.
 **/
         virtual void debug( string tab );

/** Unpacks the interpolatable object into a sequence of elementary curves, if possible, otherwise only
    produces a copy of the object.
   @param          l0                  Lower curvilinear coordinate bound.
   @param          l1                  Upper curvilinear coordinate bound.
   @param           n                  Number of elementary curves.
   @param           m                  Storage size for elementary curves.
   @param         lim                  Limits
   @param         var                  Elementary curves
   @brief                              Unpack elementary curves.
 **/
         virtual void unpack( Real l0, Real l1, Int *n, Int *m, Real *lim[], cInterp ***var );

         virtual void   pickle( size_t *len, pickle_t *buf );
         virtual void unpickle( size_t *len, pickle_t  buf );

         virtual void   get( cTabData *var );
         virtual void   set( cTabData *var );
         virtual void property( string lbl, Real *var ){};
         virtual void property( string lbl, Int  *var ){};
  };


/** Find the tangent circle to two interpolatable objects p0 and p1, with tangency points at 
    curvilinear coordinates s0 (fixed) and s1 (variable). Returns the centre of the circle in y,
    its radius r and the final residual in res.
   @param                     p0               First interpolatable object.
   @param                     p1               Second interpolatable object.
   @param                     s0               Curvilinear coordinate along the first interpolatable object.
   @param                     s1               Curvilinear coordinate along the second interpolatable object.
                                               On entry it is the initial estimate.
   @param                     y                Centre of the circle tangent to the two curves.
   @param                     r                Radius of the circle tangent to the two curves.
   @param                     res              Residual.
 **/
   void incrl( cInterp *p0, cInterp *p1, Real s0, Real *s1, Real *y, Real *r, Real *res );

/** Create a new interpolatable object with type matching the value of the argument var.
    e.g. if var is interp_spline new object will be instantiated a spline.
   @param         var                  Type of the new object
 **/
   cInterp *newinterp( Int var );

/** Given a bounding box data, returns the pointer to the object tagging one (if any) of n interpolatable objects var
    intersecting the box.
   @param                     n        The number of interpolatable objects.
   @param                     var      The interpolatable objects.
   @param                     data     The bounding box selecting the objects.
 **/
   cUid *select( Int n, cInterp *var[], cBbox *data );

/**
  *@}
 **/

/**
  *@}
 **/


/**@ingroup interp 
  *@{
 **/
/**@defgroup interp_primitives 2D Geometric primitives
    Two dimensional primitives with uniform, analytic representation.
  *@{
 **/
/**
  *@}
 **/
/** 
  *@}
 **/

#  endif
