#  ifndef _STRETCHF_
#  define _STRETCHF_

#  include <cmath>
#  include <cprec.h>

/** One-dimensional stretch function with coarsest distribution in the middle and densest distribution at the ends.
   @param           ratio            Relative distance of the coarsest interval from the ends of the line.
   @param           b                Stretching parameter.
   @param           ss               Start curvilinear coordinate (between 0 and 1 ).
   @param           se               End curvilinear coordinate (between 0 and 1 ).
   @param           s0               Background density.
   @brief Stretch function.
 **/

   Real stretchf( Real ratio, Real b, Real ss, Real se, Real s0 );


#  endif
