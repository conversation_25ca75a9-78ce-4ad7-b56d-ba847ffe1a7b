#  ifndef _WEDGEDEF_
#  define _WEDGEDEF_

#  include <geo/3d/interp3d.h>

   class cWedgeDef
  {
      protected:
         bool      itg;
         bool      cls;
         Int         n;
         Real       *s;
         Real *y[2][2];
         Real    *x[3];
         Real        l;

         void      wrap( Real * );
         void      build( cInterp3d *, cInterp3d * );

      public:

         cWedgeDef();
        ~cWedgeDef();


         void build( bool it0, Int n, Real *s0[], bool b0, Real *s1[], bool b1, cInterp3d *srf0, cInterp3d *srf1 );
         void build( cWedgeDef *, Real, Real *, Real *, Real, Real *, Real *, cInterp3d *srf0, cInterp3d *srf1 );


         void inters1( cInterp3d *srf2, Real *w0, Real *y2, inter3d_t *stat, cInterp3d *srf0, cInterp3d *srf1 );
//, Real *dyp, cInterp3d *srf0, cInterp3d *srf1, Int is );
         void interp2( Real w0, Real *yp, Real *dyp, cInterp3d *srf0, cInterp3d *srf1, Int is );
         void interp3( Real w0, Real *xp, Real *dxp, cInterp3d *srf0, cInterp3d *srf1 );
         Real length(){ return l; };
         void head( Real * );
         void tail( Real * );

         void copy( cWedgeDef ** );
  };

   class cWedge
  {
      protected:
         Int        ib;
         Int        ii;
         Int        ig;
         bool       itg;
         Int        na;
         Real       *s;
         Real      *s0[2];
         Real      *s1[2];
         Real      *x0[3];
         Real      *x1[3];
         Real        l;
         bool        cls;

         cInterp3d  *sfb,*sfi;

      public:

         cWedge();
        ~cWedge();

         void build( Int ib, Int ii, bool it0, Int n, Real *s0[], bool b0, Real *s1[], bool b1, cInterp3d *srf[] );


         Int  surf( Int iws, Int iwe, cWedge *w[] );
         Int  next( Int iws, Int iwe, cWedge *w[] );
         Int  twin( Int iws, Int iwe, cWedge *w[] );

         void interp2( Real s, Real *y, Real *dy, cInterp3d *srf[] );
         void interp3( Real s, Real *y, Real *dy, cInterp3d *srf[] );

         Real length(){ return l; };

         void bind( cInterp3d*[] );
         void unbind();

// temporary methods: winged edges should not carry their own definition.
         void head( Real * );
         void tail( Real * );
         Int  surf(){ return ib; };
         Int  intr(){ return ii; };
  };

#  endif
