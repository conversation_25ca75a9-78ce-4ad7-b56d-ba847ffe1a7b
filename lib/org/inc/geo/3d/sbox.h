#  ifndef _SBOX_
#  define _SBOX_

#  include <cprec.h>
#  include <const.h>
#  include <cmath>

#  include <fstream>
   void sbprint( Real x0[3], Real x1[3], ofstream *fle );
   void corners( Real y00[3], Real dy000[3], Real dy100[3],
                 Real y10[3], Real dy010[3], Real dy110[3],
                 Real y01[3], Real dy001[3], Real dy101[3],
                 Real y11[3], Real dy011[3], Real dy111[3], Real x0[3], Real x1[3] );

#  endif
