#  ifndef _SURF_
#  define _SURF_

#  include <cprec.h>
#  include <const.h>
// include <tri/meshctrl.h>
#  include <geo/tol.h>
#  include <geo/2d/interp.h>
#  include <geo/2d/polyline.h>
#  include <geo/2d/spline.h>
#  include <geo/3d/linear.h>
#  include <geo/3d/sbox.h>
#  include <geo/vspline.h>
// include <geo/3d/wedge.h>
#  include <geo/3d/tri3d.h>
#  include <fstream>

/**@ingroup engineering
  *@{
 **/

/**@defgroup interp3d Parametric surfaces in three dimensions 
  *@{
 **/

/** Bi-parametric interpolatable surfaces.*/

   class cInterp3d
  {
      protected:

         Real                               mt0[2],mt1[2];

         ofstream                           fle;
      public:
                                           cInterp3d();
         virtual                          ~cInterp3d();


/** Compute position and tangent vectors at a give set of curvilinear coordinates on th surface.
   @param        s                        Curvilinear coordinates
   @param        y                        Position vector
   @param        dy0                      First tangent vector (partial derivative of y with respect to s[0]).
   @param        dy1                      Second tangent vector (partial derivative of y with respect to s[1]).
   @brief                                 Position and tangent vectors.
  */
         virtual void                     interp( Real *s, Real *y, Real *dy0, Real *dy1 ){};


                 void                       orth( Real *, Real *, Real [3][4] );
                 void                      prjct( Real *, Real *, Real [3][4] );
                 void                      prjct( Real *, Real *, Real *, Real * );
 
                 void                      discr( Real *s0, Real *s1, Int *n, Real *sbx[], Real *xbx[] );
                 void                      print();

         virtual void approx( Real, Real, Real *, Real *, Int *, Real *[] );
         virtual void    quick(){};
         virtual void accurate(){};


/** Copy constructor. Create a new cInterp3d object which is an exact replica of this.
   @param                        var       destination object.
   @brief                                  Copy constructor.
  */
         virtual void copy( cInterp3d **var ){ *var=NULL; };

/**
   @deprecated
 **/
         virtual void gsrc( Real *, Real *, Real *, Real * );

/**
   @deprecated
 **/
         virtual void tria( Real *, Real *, Real, Int *, Real *[], Real *[], Int *, Int *[],
                         Int *nb, Int *ibd[], Real *sbp[]  );
/**
   @deprecated
 **/
         virtual void triangulate( struct tri3d_t **var );

         virtual void check( string fnme );
         virtual void check( string fnme, Real );

/**
   @deprecated
 **/
         virtual bool refine( Real *, Real *, Real *, Real, Real * );
/**
   @deprecated
 **/
         virtual bool refine( Real *, Real *, Real, Real * );

         virtual void metrics( Real *, Real * );

/** Determines whether the surface is periodic in the directions s0, s1.
   @param varl                        Periodic flags. Output. var[0] (respectively [1] ) is true if the surface wraps 
                                      around in the direction s0 (respectively s1).
 **/
     
         virtual void periodic( bool *var ){ var[0]= false; var[1]= false; };

  };

/** Simple plane surfaces **/
   class cPlane: public cInterp3d
  {
      protected:
         Real                                  v[3][3]; /** Plane description.
                                                            v[0] is a point p0 belonging to the plane.
                                                            v[1] is the first vector v1 specifying the plane direction,
                                                            v[2] is the second vector v2 specifying the plane direction.
                                                         **/
      public:

/** Copy constructor. Create a new cInterp3d object which is an exact replica of this.
   @param                        var       destination object.
   @brief                                  Copy constructor.
  */
         virtual void                       copy( cInterp3d ** );


/** Build a planar surface given a point p0 and two vectors v1 and v2
   @param                     p0           Point through the plane. 
   @param                     v1           First vector on the plane.
   @param                     v2           Second vector on the plane.
  **/
         virtual void                      build( Real *p0, Real *v1, Real *v2 );

/** Compute position and tangent vectors at a give set of curvilinear coordinates on th surface.
   @param        s                        Curvilinear coordinates
   @param        y                        Position vector
   @param        dy0                      First tangent vector (partial derivative of y with respect to s[0]).
   @param        dy1                      Second tangent vector (partial derivative of y with respect to s[1]).
   @brief                                 Position and tangent vectors.
  */
         virtual void                     interp( Real *s, Real *y, Real *dy0, Real *dy1 );
  };


   class cQuadric: public cInterp3d
  {
      protected:
         Real                                  v[6][3];
      public:
         virtual void                       copy( cInterp3d ** );
         virtual void                      build( Real *, Real *, Real *, Real *, Real *, Real * );
         virtual void                     interp( Real *, Real *, Real *, Real * );
  };



/** Surfaces of revolution. The surface is described by a plane, a polyline on that plane, and an axis of revolution **/
   class cRevolution: public cInterp3d
  {
      protected:
         cPolyline*             pl;          /** The line being revolved                                              **/
         Real                   p0[3];       /** Point through the plane containing the polyline                      **/
         Real                   l0[3];       /** First vector on the plane containing the polyline                    **/
         Real                   m0[3];       /** Secons vector on the plane containing the polyline                   **/
         Real                   a0[3];       /** Point through the axis of revolution                                 **/
         Real                   ax[3];       /** Axis of revolution                                                   **/
      public:
                                cRevolution();
         virtual               ~cRevolution();
         virtual void                       copy( cInterp3d ** );
         virtual void                   periodic( bool *var ){ var[0]= false; var[1]= true; };

/** Construct a surface of revolution from  a planar polyline, a plane and an axis or revolution 
   @param              pl0                       Polyline. 
   @param              p                         Point through the plane containing the polyline.
   @param              l                         First vector on the plane containing the polyline.
   @param              m                         Second vector on the plane containing the polyline.
   @param              q                         Point through the axis of revolution.
   @param              x                         Axis or revolution.
 **/
         virtual void           build( cPolyline *pl0, Real *p, Real *l, Real *m, Real *q, Real *x );

/** Compute position and tangent vectors at a give set of curvilinear coordinates on th surface.
   @param        s                        Curvilinear coordinates
   @param        y                        Position vector
   @param        dy0                      First tangent vector (partial derivative of y with respect to s[0]).
   @param        dy1                      Second tangent vector (partial derivative of y with respect to s[1]).
   @brief                                 Position and tangent vectors.
  */
         virtual void                     interp( Real *s, Real *y, Real *dy0, Real *dy1 );
  };

   class cXrRevolution: public cInterp3d
  {
      protected:
         Real                   m0,m1;
         cVSpline*              pl;
      public:
                                cXrRevolution();
         virtual               ~cXrRevolution();
         virtual void                       copy( cInterp3d ** );
         virtual void           build( Real, Real, cVSpline * );
         virtual void           interp( Real *, Real *, Real *, Real * );
         virtual void         periodic( bool *var ){ var[0]= false; var[1]= true; };
  };


/** Surfaces swept by two-dimensional polylines **/
   class cExtruded: public cInterp3d
  {
      protected:
         cPolyline*             pl;                 /** Polyline                                                      **/
         Real                   l0[3];              /** First vector of the plane containing the polyline             **/
         Real                   m0[3];              /** Second vector of the plane containing the polyline            **/
         Real                   a0[3];              /** First point of the sweep vector                               **/
         Real                   a1[3];              /** Second point of the sweep vector                              **/
         Real                   da[3];              /** Sweep vector                                                  **/
      public:
                                cExtruded();
         virtual               ~cExtruded();
         virtual void           copy( cInterp3d ** );


/** Build a swept surface from a polyline, the plane containing the polyline and the two ends of the sweep vector
   @param                       pl0                     Polyline.
   @param                       l                       First vector in the plane containing the polyline.
   @param                       m                       Second vector in the plane containing the polyline.
   @param                       a                       First point of the sweep vector.
   @param                       b                       Second point of the sweep vector.
 **/
         virtual void           build( cPolyline *pl0, Real *l, Real *m, Real *a, Real *b );


         virtual void           interp( Real *, Real *, Real *, Real * );
         virtual void           periodic( bool *var ){ var[0]= pl->periodic(); var[1]= false; };
  };

   class cSections: public cInterp3d
  {
      protected:
         Int                    ns;
//       cSpline              **sec;
         cInterp              **sec;
         cSpline                tmp;
         Real                  *wrk[10];
         virtual void           clean();
      public:
         cSections();
        ~cSections();
         virtual void           copy( cInterp3d ** );
         virtual void           build( Int, cInterp ** );
         virtual void           interp( Real *, Real *, Real *, Real * );
         virtual void           periodic( bool *var ){ var[0]= sec[0]->periodic(); var[1]= false; };
  };

// void inters( cInterp3d *, cInterp3d *, cInterp3d *, Real *, Real *, Real * );

   void inters( cInterp3d *p0, cInterp3d *p1, cInterp3d *p2, Real *s0, Real *s1, Real *s2, inter3d_t *stat );
   void inters( cInterp3d *p0, cInterp3d *p1, Real *x0, Real *n0, Real *s0, Real *s1, inter3d_t *stat );
   void inters( cInterp3d *p0, Real *x0, Real *l0, Real *s0, Real *s1, inter3d_t *stat );

   void cont0( cInterp3d *p0, cInterp3d *p1, Real *s0, Real *s1, Real l, inter3d_t *stat );

   void cylcut( cInterp3d *p0, cVSpline *, Real *x0, Real *n0, Real *, Real *, inter3d_t *stat );
   void cont( cInterp3d *sf0, cInterp3d *sf1, Real *s0, Real *s1, Real l, Real a, inter3d_t *stat );
// void refine( cInterp3d *sf0, cInterp3d *sf1, Real *s0, Real *s1, Real l, Real a, inter3d_t *stat );

/**
  *@}
 **/

/**
  *@}
 **/

#  endif
