#  ifndef _TRI3D_
#  define _TRI3D_

#  include <cprec.h>
#  include <utils/proto.h>
#  include <topo/dgraph.h>
#  include <lin/vect.h>
#  include <lin/small.h>
#  include <cassert>
#  include <geo/3d/interp3d.h>
#  include <unmesh/2dcdt.h>

/**@ingroup engineering
  *@{
 **/

/**@defgroup tri3d Surface triangulations
    Data structures and utilities for surface triangulations of three-dimensional bodies.
  *@{
 **/

/** Surface triangulation data structure **/
   struct tri3d_t
  {
      bool          debug;              /**< Debug switch                                                             **/
      Int              ns;              /**< Number of surface patches                                                **/
      class cInterp3d **srf;            /**< Surface patches                                                          **/
      Int              np;              /**< Number of nodes in physical space                                        **/
      Int              mp;              /**< Number of nodes in parametric space                                      **/
      Int              ne;              /**< Number of triangles                                                      **/
      Real             *x[3];           /**< Node coordinates in physical space                                       **/
      Real             *y[2];           /**< Node coordinates in parametric space                                     **/

      Real           ***q;              /**< Local transformation matrix: QR factorized form, Q factor.               **/
      Real            **r;              /**< Local transformation matrix: QR factorized form, R factor.               **/
 
      Int            *ies;              /**< Triangle-surface association                                             **/
      Int            *iep[3];           /**< Triangle-node connectivity in physical space                             **/
      Int            *jep[3];           /**< Triangle-node connectivity in parametric space                           **/
      Int            *iee[3];           /**< Triangle-triangle connectivity                                           **/
      Real           *dee[3];           /**< Periodic information for triangle triangle connectivity                  **/

      Int             nb;
      Int            *lbp;
      Int            *ibp[2];
      string         *bnm;
  };

/** Initialise a surface triangulation 
   @param               var                  Surface triangulation.
 **/ 
   void init( tri3d_t *var );

/** Destroy a surface triangulation
   @param               var                  Surface triangulation.
 **/ 
   void destroy( tri3d_t *var );

/** Build a surface triangulation from a (FDNEUT) file.
   @param               s                    File name.
   @param               var                  Surface triangulation. 
   @deprecated                               Temporary only.
  **/
   void build( string s, tri3d_t *var );

/** Build a surface triangulation from a set of parametric surfaces.
   @param               n                    Number of parametric surfaces
   @param               s                    Parametric surfaces.
   @param               var                  Surface triangulation. 
   @todo                                     Obtain constrained Delaunay triangulation directly from surfaces.
  **/
   void build( Int n, class cInterp3d **s, tri3d_t *var );

/** Sort out local topology for a surface triangulation.
   @param               var                  Surface triangulation. 
  **/
   void build0( tri3d_t *var );

/** Gnuplot a surface triangulation.
   @param               s                    File name.
   @param               var                  Surface triangulation. 
  **/
   void gnuplot( string s, tri3d_t *var );

/** Gnuplot a surface triangulation.
   @param               s                    File name.
   @param               b                    Boundary name.
   @param               var                  Surface triangulation. 
  **/
   void gnuplot( string s, string b, tri3d_t *var );

/** Tecplot a surface triangulation.
   @param               s                    File name.
   @param               var                  Surface triangulation. 
  **/
   void tec( string s, tri3d_t *var );

/** Compute local transformation matrices for the surface triangulation.
   @param               var                  Surface triangulation.
 **/
   void normals( tri3d_t *var );

/** Initialise the local coordinates and triangle association of a cloud of points on a surface triangulation.
    The points are associated to the triangles based on orthogonal projection.
   @param               n                    Number of points. Input.
   @param               x                    Space coordinates of the cloud points. 
                                             x[0][i], x[1][i], x[2][i] are the x1,x2,x3 coordinates of point i, 
                                             respectively. Input.
   @param               y                    Triangle parametric coordinates of the cloud points. y[0][i],y[1][i]
                                             y[2][i] are the s1,s2,s3 coordinates of point i, respectively. Input.
   @param               ipe                  Triangle associated to each node.  ine[i] is the triangle containing the
                                             projection of node i. The parametric coordinates s1,s2,s3 refer to this 
                                             triangle.
   @param               var                  Surface triangulation.
 **/
   void project( Int n, Real *x[], Real *y[], Int *ipe, tri3d_t *var );

/** Initialise the local coordinates and triangle association of a point on a surface triangulation.
    The point is associated to the triangles based on orthogonal projection.
   @param               x                    Space coordinates of the point.  Input.
   @param               y                    Triangle parametric coordinates of the cloud points. y[0][i],y[1][i]
                                             y[2][i] are the s1,s2,s3 coordinates of point i, respectively. Input.
   @param               h                    Triangle associated to the node.
   @param               var                  Surface triangulation.
 **/
   void project( Real *x, Int *h, Real *y, tri3d_t *var );

/** Compute the local coordinates of a point in space with respect to triangle i.
   @param               x                    Cartesian coordinates. Input.
   @param               i                    Reference triangle. Input.
   @param               y                    Local coordinates. Output.
   @param               var                  Surface triangulation.
 **/
    void local( Real *x, Int i, Real *y, tri3d_t *var );

/** Compute the physical coordinates of a point in space with respect to triangle i.
   @param               y                    Local coordinates. Input
   @param               i                    Reference triangle. Input.
   @param               x                    Cartesian coordinates. Output.
   @param               var                  Surface triangulation.
 **/
    void global( Real *y, Int i, Real *x, tri3d_t *var );

/** Jump and walk search 
   @param               x                    Cartesian coordinates. Input.
   @param               i                    Reference triangle. Input/Output.
   @param               y                    Local coordinates. Output.
   @param               var                  Surface triangulation.
 **/
    void search( Real *x, Int *i, Real *y, tri3d_t *var );

/** Add a set of triangles, parametric and physical coordinates to the surface triangulation and
    attribute all the new triangles to the surface s.
   @param               n                    Number of new points.
   @param               y                    Parametric coordinates of the new points.
   @param               m                    Number of new triangles.
   @param               it                   Nodes of the new triangles.
   @param               s                    Surface.
   @param               var                  Surface triangulation.
 **/
    void add( Int n, Real *y[], Int m, Int *it[], class cInterp3d *s, tri3d_t *var );

/** Correct the position of a point on a surface triangulation to produce its true orthogonal projection
    on the underlying parametric surface. Iterative version.
   @param               x                    Point physical coordinates.
   @param               i                    Triangle associated to the point.
   @param               y                    Point parametric coordinates on the surface.
   @param               var                  Surface triangulation.
   @todo                                     Implement iteration control and improve convergence.
 **/
    void correct( Real *x, Int i, Real *y, tri3d_t *var );

/** Correct the position of a point on a surface triangulation to produce its true orthogonal projection
    on the underlying parametric surface. Non-iterative version: performs a single projection step.
   @param               x                    Point physical coordinates.
   @param               i                    Triangle associated to the point.
   @param               y                    Point parametric coordinates on the surface.
   @param               x0                   Updated point position.
   @param               dy                   Parametric coordinates correction.
   @param               var                  Surface triangulation.
 **/
    void correct( Real *x, Int i, Real *y, Real *x0, Real *dy, tri3d_t *var );


/** Assign boundary edges and groups.
   @param               mb                   Number of boundary groups.
   @param               lb                   Boundary edges indexing.
   @param               ib                   Boundary edges.
   @param                b                   Boundary names.
**/
    void boundary( Int mb, Int *ib[], string b, tri3d_t *var );

/** Rename a boundary **/
   void rename( string s, string b, tri3d_t *var );

/** Stitch two triangulations ensuring conformity at a shared boundary, indicated by the same group name.**/
   void stitch( tri3d_t *var0, tri3d_t *var1 );
/**
  *@}
 **/

/**
  *@}
 **/
#  endif
