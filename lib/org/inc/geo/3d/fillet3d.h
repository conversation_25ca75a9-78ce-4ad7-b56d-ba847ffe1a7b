#  ifndef _FILLET3D_
#  define _FILLET3D_

#  include <lin/vect.h>
#  include <geo/vspline.h>
#  include <geo/3d/interp3d.h>

   struct fillet3d_t
  {
      Real         s0[2];
      Real         s1[2];
      Real        ds0[2];
      Real        ds1[2];
      Real        dx0[2][3];
      Real        dx1[2][3];
      Real        dn0[2][3];
      Real        dn1[2][3];
      Real         x0[3];
      Real         x1[3];
      Real         n0[3];
      Real         n1[3];
      Real           res;
      Real           rlx;
      Int           info;
      Int            its;
      Real             a[16];//={ 0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0 };
      int           ipiv[4];//={0,0,0,0};
  };

   bool valid( fillet3d_t * );
   void reset( fillet3d_t * );
   void hints( fillet3d_t *, Real *, Real * );

   class cFillet3d: public cInterp3d
  {
      protected: 

         bool      fast;         // fast or accurate interpolation
         cInterp3d *sf0;         // surface 0;
         cInterp3d *sf1;         // surface 1;
         bool       cyl;         // closed surface fillets
         Int         ns;         // number of points on envelope support
         Real        f0;         // normal orientation for surface 0
         Real        f1;         // normal orientation for surface 1
         Real         r;         // fillet radius
         Real       *ss;         // curvilinear coordinate for envelop support
         Real       *xs[3];      // plane origins for envelope support
         Real       *ls[3];      // plane l-axis for envelope support
         Real       *s0s[2];     // surface coordinates of fillet edge on surface 0
         Real       *s1s[2];     // surface coordinates of fillet edge on surface 1
         Real       *x0s[3];     // cartesian coordinates of fillet edge on surface 0
         Real       *x1s[3];     // cartesian coordinates of fillet edge on surface 1
         Real       *n0s[3];     // local normals of surface 0 at fillet edge
         Real       *n1s[3];     // local normals of surface 1 at fillet edge

         void support( Real *x, Real *l, fillet3d_t *stat );
         void dsupport( Real *x, Real *l, Real *dx, Real *dl, fillet3d_t *stat, fillet3d_t *dstat );
         void store( Int n );
         void clean();
         void interp( Int i, Real w, Real *x, Real *dx );

      public: 
      
         cFillet3d();
         virtual ~cFillet3d();
         void build( cInterp3d *sf0, cInterp3d *sf1, Real f0, Real f1, Real r, 
                     Int n, Real *s, Real *x[], Real *l[], fillet3d_t *stat ); 

         void check( string );

         void interp( Real *w, Real *x, Real *dx0, Real *dx1 );

         void edges( Real *x0[], Real *x1[] );

         virtual void quick(){ fast=true; };
         virtual void accurate(){ fast=false; };
  };

#  endif
