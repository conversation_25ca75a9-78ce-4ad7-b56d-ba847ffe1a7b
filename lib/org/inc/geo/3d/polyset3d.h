#  ifndef _POLYSET3D_
#  define _POLYSET3D_

#  include <geo/2d/polyset.h>
#  include <geo/3d/wedge.h>
#  include <geo/3d/interp3d.h>
// include <tri/adf/jm28unstructured.h>

   bool tg( Int i0, Int i1, cPolyline *p[] );

   typedef  Int  *int2_t[2];
   typedef  Int  *int3_t[3];
   typedef  Int  *int4_t[4];

   typedef  Real *real2_t[2];
   typedef  Real *real3_t[3];
   typedef  Real *real4_t[4];
 

   void select0( Int n, Int *m, Int *indx, Int *iprm );

   Int insert3( Int *n, Real *x[], Real *x0 );
   Int insert2( Int *n, Real *x[], Real *x0 );
   Int insert2( Int *n, Int *idnt, Real *x[], Int id0, Real *x0 );

   Int nextI( Int ist, Int ien, Int i0, Int *iprv, Int *inxt, Int *idnt, Int *iprm );
   Int nextI( Int ist, Int ien, Int i0, Int *iprv, Int *inxt, Int *iprm );
   Int same( Int ist, Int ien, Int i0, Int *iks, Int *iprm );
   void sortsplt1( Int nsplt, Int *isplt[], Real *ssplt[], Int *nssplt, Int *lsplt[], Int *iss );
   void concat1( Int n, Int *iwrk[], Int *nl, Int *lprm );

   class cPolyset3d
  {
      protected:

// surfaces
         Int         ns;
         cInterp3d **srf;

// physical vertices
         Int         nx;
         Real       *xx[3];
       

// vertices
         Int         nv;
         Real       *yv[2];
         Int        *ivs;
         Int        *ivx;
         string     *comv;

// w-edge definitions
         Int         nd;
         Int        *ids[2];
         Int        *idv[4];
//       Int        *idrm;
         cWedgeDef **wdf;
         string     *comd;

// w-edge
         Int         nw;
         Int        *iwd[2];
         Int        *iws[2];
         Int        *iwv[2];
         Int        *iwl;
         Int        *itw;
         Int        *iwrm;
         string     *comw;

// loops
         Int         nl;
         Int        *ilw;
         Int        *llw;
         Int        *jlp;
//       Int        *ilrm;

// polyhedra;

         Int         np;
         Int        *lpl;
         Int        *ipl;

         Int        *llp;
         Int        *ilp[2];
         Int        *idx;
//       Int        *iprm;

         Int        *ibs;
         Int        *iis;

// approximation
         Int               *nfs;
         Int               *nfp;
         Int               *nfe;
         Int               *nfb;

         real2_t             *yfp;
         real3_t             *xfp;
         int2_t             *ifsp;
         int3_t             *ifep;
         int4_t             *ifbp;
         real2_t             *sfbp;

         void nullify();
         void wedges();
         void loops();
         void polyhedra();

         void build();
         void split( Int nsplt, Int *isplt[], Real *ssplt[], bool uni );
         void cap( Real *y0, Real *y1, Int is, Int *id, Int *idr, Real *sc, Real *d );
         void improve( Int ist, Int ien, Int *iwrk[], Real *rwrk[], Int is, Int js, Int *icap, Int *jcap, Real *scap,
                       Int *ntmp, Real *tmp[] );

         void removex( Int * );
         void removed( Int * );
         void removev( Int * );
         void dsummary();
         void vsummary();
         void lsummary();
         void wsummary();
         void psummary();

      public:

         bool dbg;
         string dbgstr;

         cPolyset3d();
        ~cPolyset3d();

         void build( Int nv0, Int *ivs0, Real *xv0[], Int nd0, Int *ids0[], Int *idv0[], cWedgeDef *wdf0[], Int ns0, cInterp3d *srf0[] );
         void build( cPolyset3d *, cPolyset3d * );

         void autos( cInterp3d * );
         void init();

         void tria1( Real );
         void tria2( Real );
         void tria( Int *ngs, Int *ngp, real2_t *ygp, real3_t *xgp, Int *nge, int3_t *igep, Int *ngb, int4_t *igbp, real2_t *sgbp );
         void crossings1( cPolyset3d *p0, cPolyset3d *p1, bool uni );
         void crossings1( cSections *p0, cInterp3d *p1[], bool uni );


         void summary();
         void tecplot( string );
         void gnuplot( string );

  };

   void sweep( cPolyset *pl, Real *l0, Real *m0, Real *q0, Real *q1, cPolyset3d ** );
   void revolve( cPolyset *pl, Real *l0, Real *m0, Real *q0, cPolyset3d ** );
   void sections( Int , cSpline **, cPolyset3d ** );

#  endif
