#  ifndef _LINEAR3D_
#  define _LINEAR3D_

#  include <cmath>
#  include <const.h>
#  include <lin/small.h>

/**@ingroup engineering
  *@{
 **/

/**@defgroup linear3d Linear geometry in three space dimension.
  *@{
 **/


/** Status of three dimensional intersections between linear entities.
    inter3d_t holds parametric and physical coordinates, local jacobians and their factorisations,
    residuals, updates and information that allows the crossing to be identified as a valid crossing
    or a degenerate crossing.
   @brief Status of three dimensional intersections.
 **/

   struct inter3d_t
  {
      Real        y0[3],y1[3],y2[3];
      Real        dy0[2][3],dy1[2][3],dy2[2][3];
      Real        q0[3][3],q1[3][3],q2[3][3];
      Real        r0[3],r1[3],r2[3];
      Real        det;
      Real        s0[2],s1[2],s2[2];
      Real        ds0[2],ds1[2],ds2[2];
      Real        a[3][4];
      Int         ipiv[3];
      Real        res;
      Real        rlx;
      Int         it;
      Real        rdat[3][5];
      Real        idat[3][5];
  };


/** Plane-line intersection. The plane is specified by three points, the line by two points.
   @param                  p0          First point through the plane.
   @param                  p1          Second point through the plane.
   @param                  p2          Third point through the plane.
   @param                  q0          First point through the line.
   @param                  q1          Second point through the line.
   @param                   s          Transformed coordinates of the intersection point on the plane.
   @param                   t          Transformed coordinates of the intersection point on the line.
   @param                stat          Intersection status.
 **/
   void plint( Real *p0, Real *p1, Real *p2, Real *q0, Real *q1, Real *s, Real *t, inter3d_t *stat );

/** Plane-line intersection. The plane is specified by one point and two vectors, the line by one point and one vector.
   @param                  p0          Point through the plane.
   @param                  v1          First vector through the plane.
   @param                  v2          Second vector through the plane.
   @param                  q0          Point through the line.
   @param                  v3          Line direction.
   @param                   s          Transformed coordinates of the intersection point on the plane.
   @param                   t          Transformed coordinates of the intersection point on the line.
   @param                stat          Intersection status.
 **/
   void p1l1int( Real *p0, Real *v1, Real *v2, Real *q0, Real *v3, Real *s, Real *t, inter3d_t *stat );

/** Plane-plane-plane intersection. 
    The planes are specified by one point and two vectors each.
   @param                  p0          Point through the first plane.
   @param                  l0          First vector through the first plane.
   @param                  m0          Second vector through the first plane.
   @param                  p1          Point through the second plane.
   @param                  l1          First vector through the second plane.
   @param                  m1          Second vector through the second plane.
   @param                  p2          Point through the third plane.
   @param                  l2          First vector through the third plane.
   @param                  m2          Second vector through the third plane.
   @param                 rhs          Crossings offset (used for Newton iterations).
   @param                  s0          Transformed coordinates of the intersection point on the first plane.
   @param                  s1          Transformed coordinates of the intersection point on the second plane.
   @param                  s2          Transformed coordinates of the intersection point on the third plane.
   @param                stat          Intersection status.
 **/

   void pppint( Real *p0, Real *l0, Real *m0, 
                Real *p1, Real *l1, Real *m1, 
                Real *p2, Real *l2, Real *m2, 
                Real *rhs,
                Real *s0, Real *s1, Real *s2,
                inter3d_t *stat );

/** Plane-plane-plane intersection. 
    The first two planes are specified by one point and two vectors each. The last plane is specified by a point and
    its normal.
   @param                  p0          Point through the first plane.
   @param                  l0          First vector through the first plane.
   @param                  m0          Second vector through the first plane.
   @param                  p1          Point through the second plane.
   @param                  l1          First vector through the second plane.
   @param                  m1          Second vector through the second plane.
   @param                  p2          Point through the third plane.
   @param                  n2          Normal to the third plane.
   @param                 rhs          Crossings offset (used for Newton iterations).
   @param                  s0          Transformed coordinates of the intersection point on the first plane.
   @param                  s1          Transformed coordinates of the intersection point on the second plane.
   @param                stat          Intersection status.
 **/

   void ppnint( Real *p0, Real *l0, Real *m0, 
                Real *p1, Real *l1, Real *m1, 
                Real *p2, Real *n2, 
                Real *rhs,
                Real *s0, Real *s1, 
                inter3d_t *stat );

/** Plane-plane-plane intersection. 
    Each plane is specified by a point and a normal vector. No parametric coordinates are returned.
   @param                  p0          Point through the first plane.
   @param                  n0          Normal to the first plane.
   @param                  p1          Point through the second plane.
   @param                  n1          Normal to the second plane.
   @param                  p2          Point through the third plane.
   @param                  n2          Normal to the third plane.
   @param                 rhs          Crossings offset (used for Newton iterations).
   @param                   x          Crossings position.
   @param                stat          Intersection status.
 **/
   void nnnint( Real *p0, Real *n0, 
                Real *p1, Real *n1, 
                Real *p2, Real *n2, 
                Real *rhs,
                Real *x, inter3d_t *stat );

/** Plane-plane intersection.
    Each plane is specified by a point and two vectors. The result is stored in stat as a line direction.
   @param                  p0          Point through the first plane.
   @param                  n0          Normal to the first plane.
   @param                  p1          Point through the second plane.
   @param                  n1          Normal to the second plane.
   @param                  p2          Point through the third plane.
   @param                  n2          Normal to the third plane.
   @param                stat          Intersection status.
 **/
   void ppint( Real *p0, Real *l0, Real *m0, 
               Real *p1, Real *l1, Real *m1, 
               inter3d_t *stat );

/** triangle-triangle intersection.
    Each triangle is specified by three vertices. The result is represented by the parametric coordinates of the
    intersection points, if they exist, on the two triangles. The orientation of the line connecting the two points
    is the orientation induced by the ordinary vector product rules between the normals to the two triangles.
   @param                  p00         First vertex, first triangle.
   @param                  p01         Second vertex, first triangle.
   @param                  p02         Third vertex, first triangle.
   @param                  p10         First vertex, second triangle.
   @param                  p11         Second vertex, second triangle.
   @param                  p12         Third vertex, second triangle.
   @param                  s00         First crossing representation on first triangle.
   @param                  s01         Second crossing representation on first triangle.
   @param                  s10         First crossing representation on second triangle.
   @param                  s11         Second crossing representation on second triangle.
   @param                stat          Intersection status.
 **/
   void ttint( Real *p00, Real *p01, Real *p02,
               Real *p10, Real *p11, Real *p12,
               Real *s00, Real *s01,
               Real *s10, Real *s11, 
               inter3d_t *stat );

/** Create a box around a triangle in three dimensions.
   @param                  x0          First vertex.
   @param                  x1          Second vertex.
   @param                  x2          Third vertex.
   @param                 xmin         Box left corner.
   @param                 xmax         Box right corner.
 **/
   void box3( Real *x0, Real *x1, Real *x2, Real *xmin, Real *xmax );

/** Check if two boxes intersect.
   @param                  x0          First box left coner.
   @param                  x1          First box right corner.
   @param                  y0          Second box left corner.
   @param                  y1          Second box right corner.
 **/
   bool inters3( Real *x0, Real *x1, Real *y0, Real *y1 );

/** Check if a set of planar coordinates identifies a point on a edge or a vertex within a tolerance tol.
   @param                  s           Planar coordinates.
   @param                  stat        Triangle status.
   @param                  tol         Tolerance.
   @return                             vertex or edge index, -1 otherwise.
 **/
    Int onedg3( Real *s, inter3d_t *stat, Real tol );

/**
  *@}
 **/

/**
  *@}
 **/


# endif
