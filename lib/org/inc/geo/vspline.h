//3456789 123456789 123456789 123456789 123456789 123456789 123456789 12
//       1         2         3         4         5         6         7

//    Author          <PERSON> <<EMAIL>>
//    Created         Sat Feb  9 14:23:42 GMT 2008
//    Changes history -
//    Next change(s)  ( work in progress )
//    Purpose         spline representation 
//    Arguments       -

#ifndef _VSPLINE_
#define _VSPLINE_

#     include        <iostream>
#     include        <fstream>
#     include        <cmath>
#     include        <lin/thomas.h>

/* unvariate natural splines */

   class cVSpline
  {
      protected:

         Int        iord;       //!< spline order 

         Int        nv;         //!< number of variables.
         Int        np;         //!< number of support points.

         Real        l;         //!< spline length (based on distances between support points).
         Real       *s;         //!< curvilinear coordinates.
         Real      *sx;         //!< values.
         Real      *sz;         //!< interpolation coefficients.
         Real      **x;         //!< Values. Access pointer.
         Real      **z;         //!< Interpolation coefficients. Access pointer.
         Real      *dx0;        //!< tangent vector at first support point.
         Real      *dx1;        //!< tangent vector at last support point.

         void    cleanup();
         virtual void fit( );

      public:

         cVSpline();
         virtual ~cVSpline();

         virtual void build( Int, Int, Int, Real *, Real *[], Real *, Real * );


         virtual void interp( Real, Real *, Real * );

         virtual void copy( cVSpline ** );


  };

#endif 
