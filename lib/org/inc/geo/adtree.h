#  ifndef _ADTREE_
#  define _ADTREE_

#  include <cstdlib>
#  include <const.h>
#  include <utils/proto.h>

#  define   ADTDSIZE 1000
#  define   ADTMX    10

/** cAdTree represents an alternating digital tree.
   @brief  Alternating digital tree.
  */

   class cAdTree
  {
      protected:

         Int         n;                                   /**< number of nodes. */
         Int         nl;                                  /**< number of labels. */
         Int         nx;                                  /**< number of dimensions. */
         Int         m;                                   /**< size of the linear storage arrays */
         Int         ml;                                  /**< Highest label in tree */

         Real        xmin[ADTMX];                         /**< Lower-left corner of the box containing the whole tree.*/
         Real        xmax[ADTMX];                         /**< Top-righ corner of the box containing the whole tree.*/
         Real       *x[ADTMX];                            /**< Node coordiantes. */
         Real       *xsep;                                /**< Node separations. */

         Int        *ichl[2];                             /**< Children: ichl[0][ip] is the left child of node ip, 
                                                                         ichl[1][ip] is the right child of node ip.*/
         Int        *iprn[2];                             /**< Parent:   iprn[0][ip] is the parent of node ip, 
                                                                         iprn[1][ip] is 0 if ip is its parent left 
                                                                         child.*/
         Int        *ilbl;                                /**< Node identifying label. */
         Int        *indx;                                /**< Label to node correspondence. */

         Int         iroot;                               /**< Index of the root node in the linear storage.
                                                               Normally it should be 0.*/
     public:

         bool        dbg;          

         cAdTree();
         virtual ~cAdTree();

/** Initialise the tree by assigning the number of dimensions and the corners of the box containing the tree.
    The values of nx, xmin and xmax cannot be modified while the tree is in use. Invoking init on a non empty tree
    will cause error. In order to re-assign nx,xmin or xmax, call clear first.
   @param      nx                    Number of coordinates.
   @param      xmin                  Lower-left corner of the box containing the tree.
   @param      xmax                  Top-right corner of the box containing the tree.
   @brief Tree initialisation.
   @see   clear
*/
         void init( Int nx, Real *xmin, Real *xmax ); 

/** Clears the tree internal storage and makes it available for re-use.
   @brief Cleanup.
*/
         void clear();

/** Insert a node with position y and associated label lbl
   @param             y         The coordinates of the new node
   @param             lbl       Label associated with the new node      
   @brief                       Node insertion.
*/
         void  insert( Real *y, Int lbl );

/** Removes node ip. Removed nodes are replaced with new nodes 
    inserted in the tree as soon as a suitable candidate is presented.
   @param             ip       The identity of the node 
   @return                     The final resting place of the removed node. 
                               Removed nodes are swapped with their latest child and hidden. 
                               User storage used in conjuncion with
                               the tree must be updated by swapping entry ip with the entry returned by remove().
   @brief                      Node de-activation.
  */
         Int   remove( Int ip ); 
         
/** Returns the number of children in the left (jc=0) or right (jc=1) dynasty of node ip.
   @param            jc        Left/right dynasty.
   @param            ip        Node identity.
   @brief            Head count.
  */
         Int   nchl( Int jc, Int ip );

/** Prints the tree starting from node ip indenting each level in the tree with tab.
   @param            ic        Node identity. 
   @param            tab       Indentation tab.
   @brief                      Standard output.
  */
         void print( Int ic, string );
 
/** Returns a list of nodes in the tree residing in the domain having bmin and bmax as botton-left and top-right corners, respectively. 
   @param            bmin        Domain bottom-left corner
   @param            bmax        Domain top-right corner
   @param            np          Number of nodes found in the domain.
   @param            ilp         Indexes of the points found in the domain.
   @brief                        Search.
  */
         void search( Real *bmin, Real *bmax, Int *np, Int *var );
  };

#  endif
