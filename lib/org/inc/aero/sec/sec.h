# ifndef _BLADE_
# define _BLADE_

# define MxNBlds 100

# include <geo/2d/interp.h>
# include <geo/vspline.h>
# include <cmath>
# include <cprec.h>
# include <const.h>
# include <utils/proto.h>


   void xttomt( Int npp, Int nbp, Real *xp, Real *rp, Real *xb, Real *rb, Real *mp, Real *mb );

   void xrtomm1( Int, Real *, Real *, Real *, Real * );
   void mrtox( Int, Real *, Real *, Real * );
   void xttomt( Int iflip, Int npp, Int nbp, Real *xp, Real *rp, Real *xb, Real *tb,
                Real *mp, Real *mb, Real *rb);


   class cSection
  {
      protected:

         Int                   flip;
         Int                   nb;

         cVSpline             *srfi; //!< x-r cooridnates of the passage line, and other quantities related to the streamtube

         Real                  stg; //!< stagger angle
         Real                  cmb; //!< camber angle

         Real                  ch;  //!< chord
         Real                  thck; //!< thickness

         Real                  lew; //!< leading edge wedge angle
         Real                  tew; //!< trailing edge wedge angle

         Real                  lea; //!< leading edge metal angle
         Real                  tea; //!< trailing edge metal angle

         Real                  ler; //!< leading edge radius
         Real                  ter; //!< trailing edge radius

         Real                  mo;  //!< aerofoil position for stacking
         Real                  to;  //!< aerofoil position for stacking

/*       Int                   ile; //!< Leading edge position on the stream tube definition
         Int                   ite; //!< Trailing edge position on the stream tube definition*/

         cInterp              *cmbi;//!< interpolatable representation of camber line
         cInterp              *airi;//!< interpolatable representation of aerofoil

         Real                  sle;//!< leading edge position on section interp.
         Real                  ste;//!< trailing edge position on section interp.

         Real                  zle;//!< leading edge position on camber interp.
         Real                  zte;//!< trailing edge position on camber interp.

         Real                  mle;//!< leading edge position on camber interp.
         Real                  mte;//!< trailing edge position on camber interp.

         Real                  min;//!< inlet position in m'  coordinates
         Real                  mex;//!< exit position in m' coordinates

         Real                  zin;//!< inlet position on the camber line definition
         Real                  zex;//!< exit position on the camber line definition

         virtual void          clean();
         virtual void          camberline();
         virtual void          copy0( cSection * );

      public:

         cSection();
         virtual ~cSection();

         virtual void copy( cSection ** );

         virtual cInterp *aerofoil(){ return airi; };
         virtual cInterp *camber(){ return cmbi; };
         virtual cVSpline *surface(){ return srfi; };

         virtual Real wang(){ return 0; }; 


         virtual Real getmle() { return mle; };
         virtual Real getmte() { return mte; };
         virtual Real getmin() { return min; };
         virtual Real getmex() { return mex; };
         virtual Real getsle() { return sle; };
         virtual Real getste() { return ste; };
         virtual Real getscle() { return zle; };
         virtual Real getscte() { return zte; };
         virtual Real getthck() { return thck; };
         virtual Real getptch() { return pi2/(Real)nb; };
         virtual Real getmthck() { return thck; };
         virtual Int  getflip()  { return flip; };
         virtual Int  getnb()  { return nb; };

         virtual void check( string name );
         virtual void check3d( string name );
         virtual void check3d( ofstream *fle );

         virtual void setnb( Int val ) { nb = val; };
         virtual void setflip( Int val ) { flip = val; };

         virtual Real mprime( Real x );
         virtual void replaceair( Int, Real *[] ){};
  };

   void interpsec( cSection* sec0, cSection *sec1, Real qw, cSection ** );
   void interpsec( cSection* sec0, cSection *sec1, Real qw, Real dw, cSection ** );
   void filletsec( cSection* sec0, cSection *sec1, Real rf, Real th, cSection **sec );

#endif
