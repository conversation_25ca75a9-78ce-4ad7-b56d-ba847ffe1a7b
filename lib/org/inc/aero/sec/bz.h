#  ifndef _BZSECTION_
#  define _BZSECTION_

#  define   NPLTE 30
#  define   NPCMB 40
#  define   NPPSS 50

#  include <aero/sec/sec.h>
#  include <geo/2d/bezier.h>
#  include <geo/2d/polyline.h>

/*! parametric representation of Controlled Diffusion Aerofoil sections */

   class cBZSec: public cSection
  {
      protected:

         Real                    lr,tr;

         Int                     nss;
         Real                  *xss[2]; // suction side definition
         Int                     nps;
         Real                  *xps[2]; // pressure side definition

         Real                   wngl; // wedge angle; temporary location. Keep le and te lines instead as data members

      public:
         cBZSec();
         virtual ~cBZSec();
         void build( Real stg0, Real ch0, Real mo0, Real to0, Real lr0, Real tr0, 
                     Int ns0, Real *xss0[], Int np0, Real *xps0[] );

         virtual Real wang(){ return wngl; };
  };


#  endif
