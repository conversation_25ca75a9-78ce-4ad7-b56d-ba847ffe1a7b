# ifndef _DT15_
# define _DT15_

# include <aero/sec/sec.h>
# include <geo/2d/spline.h>

   void dt15section( cSection **sec, Int flip, Int nb, ifstream *fle );


   class cDT15Sec: public cSection
  {
      protected:
       public:
         cDT15Sec();
        ~cDT15Sec();
         void build(  Int, Real *[], Int, Real *[] );
         virtual Real wang(){ cout << "FORBIDDEN\n"; exit(0); };
         virtual void copy( cSection ** );
         virtual void replaceair( Int n, Real *[] );
  };




#endif
