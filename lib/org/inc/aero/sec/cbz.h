#  ifndef _CBZSECTION_
#  define _CBZSECTION_

#  define   NPLTE 30
#  define   NPCMB 40
#  define   NPPSS 50

#  include <aero/sec/bz.h>

/*! parametric representation of Controlled Diffusion Aerofoil sections */

   class cCBZSec: public cBZSec
  {
      protected:

         Int                     nc;
         Real                  *xcm[2]; // camber line definition;
         Int                     nt;
         Real                  *xth[2]; // thickness distribution;

      public:
         cCBZSec();
         virtual ~cCBZSec();
         void build( Real stg0, Real ch0, Real mo0, Real to0, Real lr0, Real tr0, 
                     Int nc0, Real *xcm0[], Int nt0, Real *xth0[] );


/*       void setparam( Real *[], Real *[] );
	 void setparam( Real , Real , Real , Real , Real *[], Real *[] );

         void getparam( Real *[], Real *[] );
         void getparam( Real *, Real *, Real *, Real *, Real *[], Real *[] );

	 void read(ifstream *);
	 void sflip( Real *[] );
	 void report(ofstream *);
	 void write(ofstream *);
	 void setright(Real *, Real *, Real *);

	 // update chord for the desired cda-section
	 void setchord( Real Ch );


	 // set right number of blades based on solidity ratio
	 void setnb();
	 void setsol(Real );

         Real *getMbp(){ return  mbp; };
         Real *getTbp(){ return  tbp; };

         virtual void getdefprm( Int *, Real *[] );
         virtual void getcntrl( Int *, Real *[] );
         virtual void setcntrl( Int  , Real *[] );

	 void setxref( Real Xref){ xref= Xref; };
	 void getxref( Real *Xref){ *Xref= xref; };

         void recamber( Real ); */

  };


#  endif
