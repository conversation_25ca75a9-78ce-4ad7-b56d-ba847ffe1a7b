#  ifndef _BLADE_H
#  define _BLADE_H

#  include <fstream>
#  include <string>
#  include <aero/sec/sec.h>
#  include <geo/2d/spline.h>
//#  include <geo/3d/interp3d.h>
//#  include <geo/3d/fillet3d.h>
#  include <geo/vspline.h>
#  include <tab/proto.h>

#  define NFILLET 11
#  define LFILLET (Int)((NFILLET)*0.6)


   class cBlade: public cTabSrc
  {
     protected:
        Int                            ns; 
        Int                            nb; 
        Int                          flip;
        cSection                    **bld;
        //cInterp3d       *bsrf,*csrf,*hsrf;
        bool                          tf,hf;
        Int                          itf,ihf;
        Real                          *sh;
        Real                           bh;
/*      Real                          rft;
        Real                          rfh;*/

        void sspan();
        //virtual void secf( cInterp3d *sf0, cInterp3d *sf1, Real r, Int il0, Int il1, cInterp3d **flt, cInterp3d **jlt, cSection **sc );

     public:
        cBlade();
        virtual ~cBlade();        
        Int getNs() { return ns; };
        Int getnb() { return nb; };
        cSection *getsec( Int is ) { return bld[is]; };
        virtual void build( string fnm ){};
        
        Int getns() { return ns; };
        Int getflip() { return flip; };

        void fillets0( Real, Real, cBlade ** );
        //void fillets( cInterp3d *bs0, cInterp3d *bs1, cInterp3d *bs2, Real, Real, cBlade **, 
        //              cInterp3d **fl0, cInterp3d **fl1, cInterp3d **jl0, cInterp3d **jl1 );
        void get( cTabData * );
        void set( cTabData * );

        virtual void check( string );

        virtual Int getihflt(){ return ihf; };
        virtual Int getitflt(){ return itf; };

        virtual Real span(){ return bh; };       
        virtual Real span( Int i){ return sh[i]; };       

        //virtual void surfaces( cInterp3d **bs0, cInterp3d **bs1, cInterp3d **bs2, cInterp3d **bs3, cInterp3d **bs4 );

/*      virtual void hfillet( cInterp3d **var );
        virtual void hsurf( cInterp3d **var );*/
  };

#  endif
