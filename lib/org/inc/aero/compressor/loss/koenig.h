#  ifndef _KOENIG_
#  define _KOENIG_

#  include <gas/idgas.h>
#  include <lin/small.h>
#  include <aero/compressor/loss/sp36.h>

   void kng( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *w, Real *wrk );
   void dkngq( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi, Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dw, Real *wrk, Real *dwrk );
	void dkngw( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *dploss, Real *wrk, Real *dwrk );

#  endif
