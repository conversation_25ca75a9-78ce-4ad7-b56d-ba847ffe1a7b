#  ifndef _KNGDES_
#  define _KNGDES_

#  include <gas/idgas.h>
#  include <lin/small.h>
#  include <aero/compressor/loss/sp36.h>

   void liebl( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *w, Real *wrk );
   void dlieblq( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi, Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dw, Real *wrk, Real *dwrk );
   void dlieblw( cIdGas *gas, Real *q1, Real *aux1, Real *q2, Real *aux2, Real *dwloss, Real *wrk, Real *dwrk );

#  endif
