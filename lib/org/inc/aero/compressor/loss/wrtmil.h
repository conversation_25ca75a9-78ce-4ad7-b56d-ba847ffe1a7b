#  ifndef _WRTMIL_
#  define _WRTMIL_

#  include <gas/idgas.h>
#  include <lin/small.h>

   void wrtmil( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *w, Real *wrk );
   void dwrtmilq( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi, Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dw, Real *wrk, Real *dwrk );
   void dwrtmilw( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *	auxo, Real *dploss, Real *wrk, Real *dwrk );

   
#  endif
