#  ifndef _TIPGAPTST_
#  define _TIPGAPTST_

#  include <gas/idgas.h>
#  include <lin/small.h>

   void tipgap_tst( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *w, Real *wrk );
   void dtipgap_tstq( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi, Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dw, Real *wrk, Real *dwrk );
	void dtipgap_tstw( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *dploss, Real *wrk, Real *dwrk );

#  endif
