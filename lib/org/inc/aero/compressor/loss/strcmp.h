#  ifndef _STRCMP_
#  define _STRCMP_

#  include <gas/idgas.h>
#  include <lin/small.h>

   void strcmp( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *w, Real *wrk );
   void dstrcmpq( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi, Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dw, Real *wrk, Real *dwrk );
	void dstrcmpw( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *dploss, Real *wrk, Real *dwrk );

#  endif
