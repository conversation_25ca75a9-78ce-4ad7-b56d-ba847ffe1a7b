#  ifndef _WRTMILDEV_
#  define _WRTMILDEV_

#  include <gas/idgas.h>
#  include <lin/small.h>

   void wrtmild( cIdGas *, Real *q1, Real *aux1, Real *q2, Real *aux2, Real *dev, Real *wrk );
   void dwrtmildq( cIdGas *, Real *q1, Real *aux1, Real *dq1, Real *daux1, Real *q2, Real *aux2, Real *dq2, Real *daux2, Real *ddev, Real *wrk, Real *dwrk );
   void dwrtmildw( cIdGas *gas, Real *q1, Real *aux1, Real *q2, Real *aux2, Real *ddev, Real *wrk, Real *dwrk );

   void wrtmild_simpl( cIdGas *, Real *q1, Real *aux1, Real *q2, Real *aux2, Real *dev, Real *wrk );
   void dwrtmild_simplq( cIdGas *, Real *q1, Real *aux1, Real *dq1, Real *daux1, Real *q2, Real *aux2, Real *dq2, Real *daux2, Real *ddev, Real *wrk, Real *dwrk );
   void dwrtmild_simplw( cIdGas *gas, Real *q1, Real *aux1, Real *q2, Real *aux2, Real *ddev, Real *wrk, Real *dwrk );
 


# endif
