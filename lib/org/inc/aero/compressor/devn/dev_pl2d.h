#  ifndef _DEVPL2D_
#  define _DEVPL2D_

#  include <gas/idgas.h>
#  include <lin/small.h>

   void dev_pl2d( cIdGas *, Real *q1, Real *aux1, Real *q2, Real *aux2, Real *dev, Real *wrk );
   void ddev_pl2dq( cIdGas *, Real *q1, Real *aux1, Real *dq1, Real *daux1, Real *q2, Real *aux2, Real *dq2, Real *daux2, Real *ddev, Real *wrk, Real *dwrk );
   void ddev_pl2dw( cIdGas *gas, Real *q1, Real *aux1, Real *q2, Real *aux2, Real *ddev, Real *wrk, Real *dwrk );

 


# endif
