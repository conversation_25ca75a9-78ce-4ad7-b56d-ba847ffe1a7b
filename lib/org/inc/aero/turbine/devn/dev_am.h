#  ifndef _DEVAM_
#  define _DEVAM_

#  include <gas/idgas.h>
#  include <lin/small.h>

   void dev_am( cIdGas *, Real *q1, Real *aux1, Real *q2, Real *aux2, Real *dev, Real *wrk );
   void ddev_amq( cIdGas *, Real *q1, Real *aux1, Real *dq1, Real *daux1, Real *q2, Real *aux2, Real *dq2, Real *daux2, Real *ddev, Real *wrk, Real *dwrk );
   void ddev_amw( cIdGas *gas, Real *q1, Real *aux1, Real *q2, Real *aux2, Real *ddev, Real *wrk, Real *dwrk );

 


# endif
