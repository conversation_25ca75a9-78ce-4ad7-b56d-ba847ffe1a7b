#  ifndef _AM_
#  define _AM_

#  include <gas/idgas.h>
#  include <lin/small.h>
#  include <aero/compressor/loss/sp36.h>

   void am( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *w, Real *wrk );
   void damq( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi, Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dw, Real *wrk, Real *dwrk );
	void damw( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *dploss, Real *wrk, Real *dwrk );

#  endif
