#  ifndef _DOMDEV_
#  define _DOMDEV_

#  include <device/device.h>
#  include <domain/domain.h>

   class cDom: public cDevice
  {
      protected:

         string                            dlpl,dlpo;
         string                            drpl,drpo;
         string                            dppl,dppo;
         string                            dbpl,dbpo;
         string                            misc;

         cPlugin                          *dlp;
         cPlugin                          *drp;
         cPlugin                          *dbp;
         cPlugin                          *dpp;

         Int                               nlev;
         cDomain                          *dmn;

         virtual void                      loaddmn();
         virtual void                      loadpre();

         ofstream                          flg;
         bool                              b_get_resd_info;

      public:
                                              cDom();
                                             ~cDom();
         virtual Int                          gettype(){ return dev_dom; };

         virtual void checkc( string tab );
         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

         virtual void get( cTabData *rfl );
         virtual void set( cTabData *rfl );

         virtual void getcost( Real * );
         virtual void partition();// Int, Int *, Int *, Real *, Real );

         virtual void preprocess();
         virtual void compute();
         virtual void postprocess();
         virtual void postprocess( Int it );
         virtual void ppostprocess();
         virtual void outputjl09();
         virtual void starthist();
         virtual void endhist();

         
         virtual void makebox();
         virtual void makeboxes( Int *n, Int *m, box_t **b, Int **i, cDevice ***d, string **str );

         virtual void request( Int, Int *, Int *, Int *, Real ** );
         virtual void service( Int, Int, Int, Int, Real *[], Real **, bool );
         virtual void service( Int ig, Int nxr, Int nvr, Int nqr, Real *sxr, Real **sqr );
         virtual void accept( Int ig, Int nv, Int nq, Real *sq, Real *w );
  };

   typedef void (*dlp_t)( cDom *, void *, void *, cCase *, cDomain ** );
   typedef void (*drp_t)( cDom *, void *, void *, cCase *, cDomain ** );
   typedef void (*dbp_t)( cDom *, void *, void *, cCase *, cDomain  * );

   typedef void (*dpp_t)( cDomain *, cCase *, Int *, Int **, Int **, Int ** );
// typedef void (*dpp_t)( cDom *, cDomain *, Real tld, Real *, Int *, Int *, Int *, Int *, Real * );

// typedef void (*dpo_t)( cDom *, void *, void *, cCase *, cDomain  * );




#  endif
