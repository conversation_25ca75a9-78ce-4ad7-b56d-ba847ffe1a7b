#  ifndef _EMPTYDEV_
#  define _EMPTYDEV_

#  include <device/device.h>

   class cEmptyDevice: public cDevice
  {
      protected:
      public:
                                              cEmptyDevice(){};
                                             ~cEmptyDevice(){};

         virtual Int                          gettype(){ return dev_empty; };
         virtual void                         checkc( string tab );

  };


#  endif
