#  ifndef _BINIO_DEVICE_
#  define _BINIO_DEVICE_

#  include <cstdio>
#  include <device/device.h>
//#  include <base/server/binio/proto.h>

/**@ingroup servers 
  *Device binary I/O server 
  */

//   class cBinioDevice: public binio_server<cDevice>
//  {
//      public:
//         cBinioDevice( cDevice *var )
//        {
////            context( var );
//            uid=-1;
//        }
//  };

#  endif

