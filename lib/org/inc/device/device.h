#  ifndef _DEVICE_
#  define _DEVICE_

#  include <vector>
//#  include <base/proto.h>
#  include <case.h>
#  include <paral.h>
#  include <plugin.h>
#  include <geo/2d/boxt.h>
#  include <interf.h>

#  include <unistd.h>
#  ifdef _32
#     include <mingw/dir.h>
#  else
#     include <sys/dir.h>
#  endif
#  include <sys/param.h>
#  include <sys/stat.h>
#  include <sys/types.h>

   enum device_t { dev_bad=-1, dev_empty, dev_dom, dev_num };
   static const string device_s[dev_num]= { "empty","dom" };

/**@ingroup feature 
  *Abstract functional representation
  */

   class cDevice: public cAudited, public cParal
  {
      protected:
         pthread_mutex_t                qmutx; /**< Mutual exclusion lock for boundary values. This
                                                    prevents the thread running the interface
                                                    protocol from accessing the interface values
                                                    while the thread running the computation is
                                                    still evaluating them*/
         pthread_mutex_t                umutx; /**< Mutual exclusion lock for unsteady runs.
                                                    prevents the thread running the interface
                                                    protocol from trying to answer queries
                                                    before the correct time level has been reached by the master thread.
                                                    */

         box_t                             bx;

         Int                             nnod;
         Int                             ncre;
         string                          cpath;

         bool                            active;
         bool                            delegate;
         bool                            sync; /**< Use synchronous interfaces*/
         cCase                          *rcse;

         Int                             mtfs; /**< The size of the storage available for interface objects handled 
                                                    by the device.*/
         Int                             ntfs; /**< The count of interface objects handled by the device.*/
         class cInterf                  **ntf; /**< The interface objects handled by the device.*/

         Real                           stime; /**< Physical time for the simulation. */

//         cDevice                       *nxt,*prv,*prn,*chl;
         bool                           b_show_leaf_info;

         vector<cDevice*>               children;
         string                         full_name, dev_type;

      public:
                                         cDevice();
                                        ~cDevice();
                                         cDevice( cDevice * );
         virtual Int                     gettype(){ return -1; }
//         virtual string                  gettypes(){ return device_s[gettype()];};


         virtual Int                 pcklefamily(){ return 0; };
//         virtual cDevice*              newobject( Int );

         virtual void assigncase( cCase *data );
         virtual void dismisscase();

         virtual void get( cTabData * );
         virtual void set( cTabData * );

         virtual void picklec( size_t *len, pickle_t *buf );
         virtual void unpicklec( size_t *len, pickle_t buf );

         virtual void checkc( string );

         virtual void getcost( Real * );
         virtual string getcpath( ){ return cpath; };
         virtual void setcpath( string var ){ cpath=var; };

         virtual void partition();// Int , Int *, Int *, Real *, Real );
         virtual void occupy( Int *ist, Int mcpu );
         virtual void occupy();
         virtual void preprocess();
         virtual void compute();
         virtual void postprocess();
         virtual void postprocess( Int it );
         virtual void ppostprocess();
         virtual void shutdown();
         virtual void shutdown2();
         virtual void starthist();
         virtual void endhist();
         virtual void outputjl09();

         virtual void makebox();
         virtual void makeboxes( Int *n, Int *m, box_t **b, Int **i, cDevice ***d, string **str );

         virtual void interface( class cInterf * );
         virtual void request();
         virtual void request2();
         virtual void poll2();
         virtual void poll();

         virtual void request( Int, Int *, Int *, Int *, Real ** );
         virtual void service( Int, Int, Int, Int, Real *[], Real **, bool );
         virtual void service( Int ig, Int nxr, Int nvr, Int nqr, Real *sxr, Real **sqr );
         virtual void accept( Int ig, Int nv, Int nq, Real *sq, Real *w );

/** Lock the mutex variable associated to boundary values **/
         virtual void qlock( );

/** Release the mutex variable associated to boundary values **/
         virtual void qunlock( );

/** Lock the mutex variable associated to boundary values **/
         virtual void ulock( );

/** Release the mutex variable associated to boundary values **/
         virtual void uunlock( );

/** Tell whether this device requires synchronous interfaces or not
   @return                     true if the device requires synchronous exchanges, e.g. if it 
                               is being run as unsteady, false otherwise
 **/
         bool synchronous(){ return sync; };

         //device management
         virtual bool checkin_dev( cDevice *data );
         virtual void add_dev( cDevice *data );
         virtual void append_dev( cDevice *data );
         virtual cDevice* find_dev( string data );
         virtual void show_devtree();
         virtual void pickle_dev( size_t *len, pickle_t *buf );
         virtual void save_dev();
         virtual int nchl_dev();
         //virtual void create_folders( cCase *cse );
         virtual void set_rcse( cCase *var ) {rcse = var;};
         virtual void unpickle_dev( size_t *len, pickle_t buf );
         virtual void load_dev();
         virtual void show_leaf_info();


         //new device tree

//         virtual cDevice* newobject( string full_name );

         // Add a child device and mark this device as a delegate.
         void add_child(cDevice * child);
 
         // Find a direct child with the given full_name.
         cDevice* find_direct_child(const string & child_full_name);

         // Retrieve an existing child with the given full_name or create a new one.
         cDevice* get_or_create_child(const string & child_full_name);

         // Recursively print the device tree with indentation and attributes.
         void print_tree(const string & prefix);

         // Helper function to collect the full names of all devices in the subtree.
         void collect_all_names(vector<string> & names);

         // Print all devices (count and names).
         // Output format: number-of-nodes, node1-full_name, node2-full_name, ...
         void print_all_devices();

         // Helper function to collect the full names of all leaf devices in the subtree.
         void collect_leaf_names(vector<string> & names);

         // Print leaf devices (count and names).
         // Output format: number-of-leaf-nodes, leaf1-full_name, leaf2-full_name, ...
         void print_leaf_devices();

         void build_dev_tree(string line);

         // Return a vector of pointers to the leaf node devices.
         vector<cDevice*> get_leaf_devices() ;

         // New function: Create a case folder for this device.
         // The folder structure preserves the hierarchy of the device tree.
         // For a device with dev name "dev" and a case "0000", it creates "dev/0000".
         // The parent's path is passed recursively.
         void create_case_folders(const string & case_number, const string & parent_path = "");

         void set_fullname(string nm);

  };

#  endif
