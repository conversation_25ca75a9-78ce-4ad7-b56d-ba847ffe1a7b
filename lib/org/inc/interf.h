#  ifndef _INTERF_
#  define _INTERF_

#  include <device/device.h>

   void *intrfthrf( void *data );

   class cInterf: public cPickle
  {
      protected:
         Real           *werr;

         Int               nq;
         Int               nv;
         Int               nx;
         Int               ib;

         string          name;

         class cDevice  *ldev;
         pthread_t        tid;
         pthread_mutex_t *mtx;
         pthread_attr_t  attr;
         MPI_Comm        pcom;
         MPI_Request    *rreq;
         MPI_Request    *rxreq0, *rxreq1;
         MPI_Request    *sreq0;
         MPI_Request    *sreq1;
         MPI_Request    *sreq2;
         MPI_Request    *sreq3;
         MPI_Request    *sreq4;
         Int             ildr,irem;
         Int             irnk,tag;
         Int             ndst;
         Int             nrnk;
         Int            *idst;
         Int             ians,ierr,ireq,itrm;
         bool            lstn;
         bool            plld;
         bool            rlsd;
         bool           *rwait;
         bool           *swait;
         bool           *hwait;
         pickle_t        sbuf;
         size_t          slen;
         pickle_t       *rbuf;
         size_t         *rlen;
         pickle_t       *abuf;
         pickle_t       *rxbuf;
         size_t         *xlen;
         bool           *bxbuf;

         void            wait( MPI_Request * );
         void            wait( MPI_Request *, Int * );
         void            checkbuf( pickle_t buf );

         ofstream        fle;

         bool            bposix;

      public:
         cInterf();
         virtual ~cInterf();

         virtual void build( Int i0, class cDevice *dev0, string str0, Int i1, class cDevice *dev1, string str1, 
                             Int itag, bool bposix );
         virtual void boot();
         virtual void listen();
         virtual void halt();
         virtual void halt2();
         virtual void join();
         virtual void request();
         virtual void service(Int id);
         virtual bool poll();
         virtual bool poll2();
         virtual void service2( Int id);
         virtual void request2();
//       virtual bool busy(){ return lstn; };
         virtual bool released();
         virtual bool released2();
  };

#  endif
