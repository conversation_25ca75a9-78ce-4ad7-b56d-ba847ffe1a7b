
#  ifndef _MLINEASTATOR_
#  define _MLINEASTATOR_

#  include <mline/block/block.h>
#  include <aero/compressor/loss/fixed.h>


   class cMlineAStator: public cMlineBlock 
  {
      protected:
        virtual void  loss( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wloss, Real *wrk );
        virtual void dloss( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi,
                                         Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwloss, Real *wrk, Real *dwrk );
	virtual void dlossw( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *dwloss, Real *wrk, Real *dwrk );

        virtual void  devn( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wdev, Real *wrk );
        virtual void ddevn( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi,
                                         Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwdev, Real *wrk, Real *dwrk );
	virtual void ddevnw( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *dwdev, Real *wrk, Real *dwrk );

      public:
         cMlineAStator();
         virtual ~cMlineAStator();

         virtual void compute( cIdGas *, cInterp *,Real *[], Real *[], Real *[], Real *[]);
         virtual void dcomputeq( cIdGas *, cInterp *,Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[]);

         virtual string whatis(){ return "stator with losses. Analysis. Artyom"; };
         void blade( cIdGas *gas, cInterp *ml, cInterp *cl, cInterp *hl, Real *x[], Real *q[], Real *aux[], Real *data[] );                          
			virtual void astatorrhs( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real * );
			virtual void astatorlhs( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real *[] );         

			virtual void drhs( cIdGas *, cInterp *, Real *[], Real *[], Real *[], Real *[], 
									 Real *[], Real *[], Real *[], Real *[], Real *[]);

			virtual void astatordrhsq( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real *, Real *, Real *, Real *, Real * );
			virtual void astatordrhsw( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real *, Real * );

			virtual void checkflip( Real *, Int *);
			virtual void bflip( Real *, Real *, Real * );
			virtual void dbflip( Real *, Real *, Real *, Real *, Real *, Real * );
			void lossloop( cIdGas *gas, Real *q0, Real *aux0, Real *q1, Real *aux1, Real *wrk );

  };

#  endif
