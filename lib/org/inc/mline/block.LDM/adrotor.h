
#  ifndef _MLINEADROTOR_
#  define _MLINEADROTOR_

#  include <mline/block/block.h>
#  include <aero/compressor/loss/fixed.h>


   class cMlineADRotor: public cMlineBlock
  {
      protected:
        virtual void dlossw( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *dwloss, Real *wrk, Real *dwrk );
        virtual void  loss( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wloss, Real *wrk );
        virtual void dloss( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi,
	     		    Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwloss, Real *wrk, Real *dwrk );

        virtual void  devn( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wdev, Real *wrk );
        virtual void ddevn( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi,
                            Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwdev, Real *wrk, Real *dwrk );
	virtual void ddevnw( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *dwdev, Real *wrk, Real *dwrk );
      public:
         cMlineADRotor();
         virtual ~cMlineADRotor();

         virtual void compute( cIdGas *, cInterp *,Real *[], Real *[], Real *[], Real *[]);
			virtual void adrotorrhs( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real * );
			virtual void adrotorlhs( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real *[] );


			virtual void checkflip( Real *, Int *);
			virtual void bflip( Real *, Real *, Real * );
			virtual void eta_is(  cIdGas *, Real *, Real *, Real *, Real *, Real * );

         virtual string whatis(){ return "rotor with fixed losses"; };
         void blade( cIdGas *gas, cInterp *ml, cInterp *cl, cInterp *hl, Real *x[], Real *q[], Real *aux[], Real *data[] );                          
         void section( cIdGas *gas, cInterp *ml, Real *x[], Real *q[], Real *aux[], Real *data[] );
	 void testbld( cIdGas *gas, Real *q0, Real *aux0, Real *q1, Real *aux1, Real *wrk, Int nwrk, string wrkname );
  };

#  endif
