#  ifndef _MLINEBLOCK_
#  define _MLINEBLOCK_

#  include <gas/idgas.h>
#  include <geo/2d/interp.h>

#  define   MXBVRS 50
#  define   NXQ 10


   class cMlineBlock
  {
      protected: 
         Int               ir,ib;
         Int               i1,i2;
      public:
         cMlineBlock();
         virtual ~cMlineBlock();

         virtual void index( Int, Int, Int, Int );
         virtual void index( Int *, Int *, Int *, Int * );

         virtual void compute( cIdGas *, cInterp *, Real *[], Real *[], Real *[], Real *[]){};
         virtual void dcomputeq( cIdGas *, cInterp *,Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[]){};

			virtual void drhs( cIdGas *, cInterp *, Real *[], Real *[], Real *[], Real *[], 
									 Real *[], Real *[], Real *[], Real *[], Real *[]){};

         virtual string whatis(){ return "nothing"; };

  
         Real height( Real, Real );
         virtual void blade( cIdGas *gas, cInterp *ml, cInterp *cl, cInterp *hl, Real *x[], Real *q[], Real *aux[], Real *data[] ){};
         virtual void section( cIdGas *gas, cInterp *ml, Real *x[], Real *q[], Real *aux[], Real *data[] ){};
	 virtual void testbld( cIdGas *gas, Real *q0, Real *aux0, Real *q1, Real *aux1, Real *wrk, Int nwrk, string wrkname ){};
  };

#  endif
