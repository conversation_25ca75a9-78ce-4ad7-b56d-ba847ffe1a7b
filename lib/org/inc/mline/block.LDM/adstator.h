
#  ifndef _MLINEADSTATOR_
#  define _MLINEADSTATOR_

#  include <mline/block/block.h>
#  include <aero/compressor/loss/liebl.h>
#  include <aero/compressor/devn/wrtmil.h>

   class cMlineADStator: public cMlineBlock
  {
      protected:
	virtual void  loss( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wloss, Real *wrk );
	virtual void dloss( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi,
 	                    Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwloss, Real *wrk, Real *dwrk );
        virtual void dlossw( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *dwloss, Real *wrk, Real *dwrk );

        virtual void  devn( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wdev, Real *wrk );
        virtual void ddevn( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi,
                            Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwdev, Real *wrk, Real *dwrk );
	virtual void ddevnw( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo, Real *dwdev, Real *wrk, Real *dwrk );
      public:
         cMlineADStator();
         virtual ~cMlineADStator();

         virtual void compute( cIdGas *, cInterp *,Real *[], Real *[], Real *[], Real *[]);
			virtual void adstatorrhs( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real * );
			virtual void adstatorlhs( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real *[] );

			virtual void checkflip( Real *, Int *);
			virtual void bflip( Real *, Real *, Real * );

         virtual string whatis(){ return "stator fixed loss"; };
         void blade( cIdGas *gas, cInterp *ml, cInterp *cl, cInterp *hl, Real *x[], Real *q[], Real *aux[], Real *data[] );                          
         void section( cIdGas *gas, cInterp *ml, Real *x[], Real *q[], Real *aux[], Real *data[] );
	 void testbld( cIdGas *gas, Real *q0, Real *aux0, Real *q1, Real *aux1, Real *wrk, Int nwrk, string wrkname );
  };

#  endif
