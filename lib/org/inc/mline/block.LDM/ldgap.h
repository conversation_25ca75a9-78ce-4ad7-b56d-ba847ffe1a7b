#  ifndef _LDGAP_
#  define _LDGAP_

#  include <mline/block/block.h>
#  include <aero/compressor/loss/fixed.h>

   class cMlineLDGap: public cMlineBlock
  {
      protected:
         virtual void  loss( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wloss, Real *wrk );
         virtual void dloss( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi, 
                                          Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwloss, Real *wrk );

      public:
         cMlineLDGap();
         virtual ~cMlineLDGap();
         virtual void compute( cIdGas *, cInterp *, Real *[], Real *[], Real *[], Real *[]);
         virtual string whatis(){ return "gap with losses - new variables"; };
  };

#  endif
