#  ifndef _AGAP_
#  define _AGAP_

#  include <mline/block/block.h>
#  include <aero/compressor/loss/fixed.h>

   class cMlineAGap: public cMlineBlock
  {
      protected:
         virtual void  loss( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wloss, Real *wrk );
         virtual void dloss( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi, 
                                          Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwloss, Real *wrk, Real *dwrk );

      public:
         cMlineAGap();
         virtual ~cMlineAGap();
         virtual void compute( cIdGas *, cInterp *, Real *[], Real *[], Real *[], Real *[]);

	  // gets DRHS as input, provides DQ, DAUX and DDATA[20-30] as output
         virtual void dcomputeq( cIdGas *, cInterp *,Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[]);

         virtual string whatis(){ return "gap with losses. Analysis. Artyom."; };
			virtual void agaprhs( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real *, Real *, Real * );
			virtual void agaplhs( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real *, Real *[] );

	  // gets DQs and DDATA[0-20] as input, calculates DAUX and provides DRHS as output
	  // using "agapdrhsq" and "agapdrhsw".
			virtual void drhs( cIdGas *, cInterp *, Real *[], Real *[], Real *[], Real *[], 
									 Real *[], Real *[], Real *[], Real *[], Real *[]);

			virtual void agapdrhsq( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real *, Real *, Real *, Real *, Real * );
			virtual void agapdrhsw( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real *, Real * );
  };

#  endif
