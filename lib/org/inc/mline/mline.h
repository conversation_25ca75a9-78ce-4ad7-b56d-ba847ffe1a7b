#  ifndef _MLINEDEV_
#  define _MLINEDEV_

#  include <gas/idgas.h>
#  include <geo/2d/interp.h>
#  include <plugin.h>

#  include <mline/block/block.h>

   class cMlineDev
  {

      protected:

         bool             binfo;

         cIdGas          *gas;

         cPlugin         *inplg;
         cPlugin        *outplg;
         cPlugin        *visplg;
         cPlugin        *rltplg;

         cInterp         *ml;

         Int              ncp;
         Real            *xc[2];

         Int              nbr;
         Int          nv,naux,nwrk;
         Real             *xq[10];
         Real              *q[MXVARS];
         Real            *aux[MXVARS];
         cMlineBlock    **bld;

         Int              nrr,nsr,ngr;
         Int              nstgs;
         Int             *irbld;
         Int             *isbld;
         Int             *igbld;

         Int              nrbvrs,nsbvrs,ngbvrs;
         Real            *bvrs[MXBVRS];

         string           rbvnms[MXBVRS];
         string           sbvnms[MXBVRS];
         string           gbvnms[MXBVRS];

         Real             minl,mext,wdot,pr;

	 Int             *stgs[MXVARS];

//       string           stin,stout;


         void inlet();
         void ainlet();

         void aheight( Int i );

      public:

         cMlineDev();
         virtual ~cMlineDev();

         void plugins( string strl1, string stro1, string strl2, string stro2, 
                       string strl3, string stro3, string strl4, string stro4 );

         virtual void compute();

  };

	
#define _DECL_
      typedef void (*mlineplg_t)( cMlineDev *, void *, void *, void *,
#include                         <mline/args.h>
                                );
#undef _DECL_

   class cDMlineDev: public cMlineDev
  {
  protected:
		
		Real            *dbvrs[MXBVRS];

		Real             *dxq[10];
		Real              *dq[MXVARS];
		Real            *daux[MXVARS];

		Real            *drhs[MXVARS];
	         void dinlet();
  public:
		
		cDMlineDev();
		virtual ~cDMlineDev();
		
		virtual void compute();
  };

#define _DECL_
      typedef void (*dmlineplg_t)( cDMlineDev *, void *, void *, void *,
#include                         <mline/args.h>
#include                         <mline/dargs.h>
                                );
#undef _DECL_

   class cDPMlineDev: public cDMlineDev
  {
  protected:
		
		Real            *dbvrs1[MXBVRS];

		Real             *dxq1[10];
		Real              *dq1[MXVARS];
		Real            *daux1[MXVARS];

		Real            *drhs1[MXVARS];
//        void dinlet();
  public:
		
		cDPMlineDev();
		virtual ~cDPMlineDev();
		
		virtual void compute();
  };
	

#define _DECL_
      typedef void (*dpmlineplg_t)( cDMlineDev *, void *, void *, void *,
#include                         <mline/args.h>
#include                         <mline/dargs.h>
#include                         <mline/dpargs.h>
                                );
#undef _DECL_

#  endif
