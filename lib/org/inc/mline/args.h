#  ifdef _DECL_
         Real           *wdot,
         Real           *pr,
         Real           *minl,
         Real           *mext,
         cIdGas        **gas,
         cInterp       **ml,
         cMlineBlock ***bld,
         Int           *nv,
         Int           *naux,
         Int           *nwrk,
         Int           *nrbvrs, 
         Int           *nsbvrs, 
         Int           *ngbvrs,
         string        *rbvnms,
         string        *sbvnms,
         string        *gbvnms,
         Real          *bvrs[],
         Int           *nbr, 
         Int           *nrr, 
         Int           *nsr, 
         Int           *ngr,
         Int          **irbld, 
         Int          **isbld, 
         Int          **igbld,
         Real          *xq[],
         Real          *q[],
	 Real          *aux[],
	 Int           *stgs[],
	 Int           *nstgs
#  else
        &wdot,
        &pr,
        &minl,
        &mext,
        &gas,
        &ml,
        &bld,
        &nv,
        &naux,
        &nwrk,
        &nrbvrs, 
        &nsbvrs, 
        &ngbvrs,
         rbvnms,
         sbvnms,
         gbvnms,
         bvrs,
        &nbr, 
        &nrr, 
        &nsr, 
        &ngr,
        &irbld, 
        &isbld, 
        &igbld,
         xq,
         q,
	 aux,
	 stgs,
	&nstgs
#  endif
