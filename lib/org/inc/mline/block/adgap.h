#  ifndef _ADGAP_
#  define _ADGAP_

#  include <mline/block/block.h>
#  include <aero/compressor/loss/fixed.h>

   class cMlineADGap: public cMlineBlock
  {
      protected:
         virtual void  loss( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wloss, Real *wrk );
         virtual void dloss( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi, 
                                          Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwloss, Real *wrk );

      public:
         cMlineADGap();
         virtual ~cMlineADGap();
         virtual void compute( cIdGas *, cInterp *, Real *[], Real *[], Real *[], Real *[]);
			virtual void adgaprhs( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real * );
			virtual void adgaplhs( cIdGas *, Real *, Real *, Real *, Real *, Real *, Real *[] );


         virtual string whatis(){ return "Artyom's gap without losses - newest"; };
  };

#  endif
