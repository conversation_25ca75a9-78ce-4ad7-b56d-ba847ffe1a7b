
#  ifndef _MLINELDSTATOR_
#  define _MLINELDSTATOR_

#  include <mline/block/block.h>
#  include <aero/compressor/loss/liebl.h>
#  include <aero/compressor/devn/wrtmil.h>

   class cMlineLDStator: public cMlineBlock
  {
      protected:

        virtual void  loss( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wloss, Real *wrk );
        virtual void dloss( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi,
                                         Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwloss, 
                                         Real *wrk, Real *dwrk );

/*      virtual void  devn( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wloss, Real *wrk );
        virtual void ddevn( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi,
                                         Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwloss, Real *wrk );*/

      public:
         cMlineLDStator();
         virtual ~cMlineLDStator();

         virtual void compute( cIdGas *, cInterp *,Real *[], Real *[], Real *[], Real *[]);

         virtual string whatis(){ return "stator fixed loss"; };

  };

#  endif
