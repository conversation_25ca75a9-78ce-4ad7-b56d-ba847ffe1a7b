
#  ifndef _MLINELROTOR_
#  define _MLINELROTOR_

#  include <mline/block/block.h>
#  include <aero/compressor/loss/fixed.h>


   class cMlineLRotor: public cMlineBlock
  {
      protected:
         virtual void  loss( cIdGas *gas, Real *qi, Real *auxi, Real *qo, Real *auxo,  Real *wloss, Real *wrk );
         virtual void dloss( cIdGas *gas, Real *qi, Real *auxi, Real *dqi, Real *dauxi,
                                          Real *qo, Real *auxo, Real *dqo, Real *dauxo, Real *dwloss, Real *wrk );
      public:
         cMlineLRotor();
         virtual ~cMlineLRotor();

         virtual void compute( cIdGas *, cInterp *,Real *[], Real *[], Real *[], Real *[]);

         virtual string whatis(){ return "rotor with fixed losses"; };
         void blade( cIdGas *gas, cInterp *ml, cInterp *cl, cInterp *hl, Real *x[], Real *q[], Real *aux[], Real *data[] );     
			void section( cIdGas *gas, cInterp *ml, Real *x[], Real *q[], Real *aux[], Real *data[] );

  };

#  endif
