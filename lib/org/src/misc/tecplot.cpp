   using namespace std;

#  include <misc/tecplot.h>

   void tecplot2( Int ies, Int iee, Int nv, Int nep, Int *iep[], Real *x[], string fnme )
  {
      ofstream fle;
      fle.open( fnme.c_str() );
      tecplot2( ies, iee, nv, nep, iep, x, &fle );
      fle.close();
  }

   void tecplot2( Int ies, Int iee, Int nv, Int nep, Int *iep[], Real *x[], ofstream *fle )
  {

      Int *iwrk;
      Int *iprm;
      Int  imx;
      Int  np;
      Int  jp,ip,ie,iv;

      imx=-1;
      for( jp=0;jp<nep;jp++ )
     {
         for( ie=ies;ie<iee;ie++ )
        {
            ip= iep[jp][ie];
            imx= max( ip,imx );       
        }
     }
      imx++;
      iwrk= new Int[imx];
      iprm= new Int[imx];
      setv( (Int)0,imx, (Int)-1,iwrk );
   
      np= 0;
      for( jp=0;jp<nep;jp++ )
     {
         for( ie=ies;ie<iee;ie++ )
        {
            ip= iep[jp][ie];
            if( iwrk[ip] == -1 )
           {
               iwrk[ip]= np;
               iprm[np]= ip;
               np++;
           }
        }
     } 
      
     *fle << "VARIABLES = "; 
      for( iv=0;iv<nv;iv++ )
     {
        *fle << "\"V"+strc(iv)+"\"";
     }
     *fle << "\n";
     *fle << "ZONE" << " " << "N=" << np << " " << "E=" << iee-ies << " " << "F=FEPOINT" << " " << "ET=QUADRILATERAL\n";
      for(jp=0; jp<np; jp++)
     {
         ip= iprm[jp];
         for( iv=0;iv<nv;iv++ )
        {
           *fle << x[iv][ip] << " ";
        }
        *fle << "\n";
     }

     *fle << "\n";
      for( ie=ies;ie<iee;ie++ )
     {
         for( jp=0;jp<nep;jp++ )
        {
            ip= iep[jp][ie];
            ip= iwrk[ip];
           *fle << ip+1 <<" ";
        }
         for( jp=nep;jp<4;jp++ )
        {
            ip= iep[0][ie];
            ip= iwrk[ip];
           *fle << ip+1 <<" ";
           *fle << "\n";
        }
     }

      delete[] iwrk;
      delete[] iprm;
  };

