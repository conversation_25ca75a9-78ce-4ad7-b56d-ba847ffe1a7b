
   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <misc/grid.h>
#  include <topo/ugraph.h>

   void fdntbnms( Int ng, Int *igt[], string *gnms, Int *nm, string *mnms );
   void fdntnms( Int ng, Int *igt[], string *gnms, Int *nm, string *mnms );

   void fdntdof( Int nek, Int *ne, Int *nep, Int *neq, Int *siep[], Int *sieq[], Int nx, Real *xp[], Int *nq, Real **sxq );
   void fdntbdof( Int *nep, Int *neq, Int **iep[], Int **ieq[], Int *nce[], Int ***icep[],  Int ***iceq[],
                  cUgraph *ug, Int  *nb, Int nbk, Int *nbp, Int *nbq, Int *sibp[], Int *sibq[] );

   void fdntgather( Int ng, Int *npg, Int *neg, Int *igt[], string *gnms, Int *iwrk[], Int nm, string *mnms,
                    Int nek, Int *jek, Int  *ne, Int *nep, Int *siem[], Int *siep[], Int ipmsk[][MxNPSs] );
   void fdntgather( Int ng, Int *npg, Int *neg, Int *igt[], string *gnms, Int *iwrk[], string mnms,
                    Int nek, Int *jek, Int  *ne, Int *nep, Int *siep[], Int ipmsk[][MxNPSs] );

   void cFdneut::domain( cDomain *dmn )
  {      
      Int            ick,ibk,iek;
      Int            im,ig;
      cUgraph       *ug;
      Real          *xp[3];
      Int            nq;
      Int            ne[MxNSk];
      Int            nm,mb;
      string        *mnms;

      Int           *ihlp=NULL;

      Int           *sibp[MxNBG][MxNSk],**ibp;
      Int           *sibq[MxNBG][MxNSk],**ibq;
      Int           *sibb[MxNBG][MxNSk];

      Int           *siem[MxNSk];
      Int           ***iep;
      Int           *sieq[MxNSk],***ieq;
      Real          *sxb[MxNBG];

      Int            nb[MxNBG][MxNSk];

      Int            nbb[MxNBG];

      Real          *xwrk=NULL;
      Real         **xbwrk;





      string        *bnms=NULL;
      string        *blbl=NULL;
      string        *blbs=NULL;

      subv( nx,np, rwrk,xp );
      dmn->assgnxp( np,rwrk );

//    gather( nm,mnms, nek, siem,siep );
/*    dof( siep,sieq, nx,xp, &nq,&xwrk );

      dmn->assgnxq( nq,xwrk );

      ug= new cUgraph(np);
      iep= new Int**[nek];
      ieq= new Int**[nek];
      for( iek=0;iek<nek;iek++ )
     {
         iep[iek]= new Int*[nep[iek]];
         ieq[iek]= new Int*[neq[iek]];
         dmn->assgniep( iek, ne[iek], siep[iek],sieq[iek],siem[iek] );
         subv( nep[iek],ne[iek],iwrk[iek],iep[iek] );
         subv( neq[iek],ne[iek],sieq[iek],ieq[iek] );
         ug->build( ne[iek],nep[iek],iep[iek],iek ); 
     }
   
      fdntbnms( ng, igt, gnms, &mb,bnms );
      xbwrk= new Real*[mb];

      for( im=0;im<mb;im++ )
     {

         fdntgather( ng, npg, neg, igt, gnms, siep, bnms[im], nbk, jbk, nb[im], nbp, sibp[im], bpmsk );
         fdntdof( nbk,nb[im], nbp,nbd, sibp[im],sibb[im], nx,xp, &nbb[im],&xbwrk[im] );
         dmn->assgnxb( im,nbb[im],xbwrk[im] );

         fdntbdof( nep,neq,iep,ieq, nce,icep,iceq, ug, nb[im], nbk,nbp,nbq, sibp[im],sibq[im] );
         for( ibk=0;ibk<nbk;ibk++  )
        {
            dmn->assgnibp( im,ibk, nb[im][ibk], sibp[im][ibk],sibq[im][ibk],sibb[im][ibk] );
        }
     }
      delete ug;*/


/*    Int iprd0= inlst( string("PERIODIC.RIGHT"), mb,bnms );
      Int iprd1= inlst( string("PERIODIC.LEFT"), mb,bnms );

      if( iprd0 != -1 && iprd1 != -1 )
     {
         cout << "periodic surfaces are on groups "<<iprd0<<" "<<iprd1<<"\n";
         Real d;
         dmn->assgnprd( iprd0,iprd1, &d );
         cout << "periodic surfaces are on groups "<<iprd0<<" "<<iprd1<<" max periodic error "<<d<<"\n";
     }

      string slib= share+"/domains/cfd/init/libinit.so";
      string flib= share+"/domains/cfd/frame/libframe.so";
      dmn->assstp( slib,"uniinit", flib,"trivia", slib,"fallback" );*/

      delete[] neg;
      delete[] npg;
      delete[] igt[0];
      delete[] igt[1];
/*    for( ig=0;ig<ng;ig++ )
     {
         delete[] siep[ig];
     }*/
//    delete[] siep;
      delete[] ihlp;
      delete[] bnms;
      delete[] blbs;
      delete[] gnms;
      delete[] mnms;
      delete[] xbwrk;
      for( ick=0;ick<MxNSk;ick++ )
     {
         delete[] nce[ick];
         delete[] icep[ick];
         delete[] iceq[ick];
     }


      for( iek=0;iek<nek;iek++ )
     {
         delete[] iep[iek];
         delete[] ieq[iek];
     }
      delete[] iep;
      delete[] ieq;
//    delete[] bbj;

//    delete ctab; ctab=NULL;
      
  }

