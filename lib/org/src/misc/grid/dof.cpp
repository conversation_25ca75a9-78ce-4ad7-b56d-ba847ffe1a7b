
   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 20:38:15 BST 2010
// Changes History
// Next Change(s)  -

#  include <misc/grid.h>

   void cGrid::dof( Int nek, Int *ne, Int *nep, Int *neq, Int *siep[], Int *sieq[], Int nx, Real *xp[], Int *nq, Real **sxq )
  {      
      Int ix,iq,ie,iek,jq,ip,jp,kq;
      Int *iep[MxNPSs],*ieq[MxNPSs];
      Real *xq[3];
      Real x[3];
      Real dx[3];

// count the degrees of freedom
      iq=0;
      for( iek=0;iek<nek;iek++ )
     {
         iq+= neq[iek]*ne[iek];
         sieq[iek]= new Int[neq[iek]*ne[iek]];
     }
    (*nq)= iq;
    (*sxq)=new Real[nx*(*nq)]; 
      subv( nx,(*nq), (*sxq),xq );

      iq=0;
      for( iek=0;iek<nek;iek++ )
     {
         subv( neq[iek],ne[iek], sieq[iek],ieq );
         for( ie=0;ie<ne[iek];ie++ )
        {
            for( jq=0;jq<neq[iek];jq++ )
           {
               ieq[jq][ie]= iq;
               iq++;
           }
        }
     }

      for( iek=0;iek<nek;iek++ )
     {
         subv( neq[iek],ne[iek], sieq[iek],ieq );
         subv( nep[iek],ne[iek], siep[iek],iep );
         for( ie=0;ie<ne[iek];ie++ )
        {
            setv( (Int)0,(Int)3, (Real)0.,x );
            for( jp=0;jp<nep[iek];jp++ )
           {
               ip= iep[jp][ie];
               line( ip,nx,xp, dx );
               vsum( nx,dx,x, x );
// replace with appropriate vsum
           }
            for( ix=0;ix<nx;ix++ ) 
           {
               x[ix]/= (Real)nep[iek];
           }
            for( jq=0;jq<neq[iek];jq++ )
           {
               kq= ieq[jq][ie];
               for( ix=0;ix<nx;ix++ )
              {
                  xq[ix][kq]= x[ix];
              }
           }
        }
     }
  }


   void cGrid::dofb( Int *nep, Int *neq, Int **iep[], Int **ieq[], Int *nce[], Int ***icep[], Int ***iceq[],
                  cUgraph *ug,
                  Int  *nb, Int nbk, Int *nbp, Int *nbq, Int *sibp[], Int *sibq[] )
  {
      Int ibk,iek,ib,ie,jq,jb,iq;
      Int **ibp;
      Int **ibq;
      Int *ibe[3];
      for( ibk=0;ibk<nbk;ibk++ )
     {
         sibq[ibk]= new Int[nb[ibk]*nbq[ibk]];
            
         ibp= new Int*[nbp[ibk]];
         ibq= new Int*[nbq[ibk]];
         subv( nbp[ibk],nb[ibk], sibp[ibk],ibp );
         subv( nbq[ibk],nb[ibk], sibq[ibk],ibq );
         ibe[0]= new Int[nb[ibk]];
         ibe[1]= new Int[nb[ibk]];
         ibe[2]= new Int[nb[ibk]];
         ug->match( nb[ibk],nbp[ibk],ibp, ibe, nep,iep,nce[ibk],icep[ibk] );
        for( ib=0;ib<nb[ibk];ib++ )
       {
           iek= ibe[2][ib];
           if( neq[iek] == nbq[ibk] )
          {
               for( jq=0;jq<nbq[ibk];jq++ ) 
              {
                  ie=  ibe[0][ib];
                  jb=  ibe[1][ib];
                  iq= iceq[ibk][iek][jq][jb];
                  ibq[jq][ib]= ieq[iek][iq][ie];
              }
           }               
        }
         delete[] ibe[0];
         delete[] ibe[1];
         delete[] ibe[2];
         delete[] ibp;
         delete[] ibq;
     }
  }
