
   using namespace std;

#  include <misc/grid.h>

   cGrid::cGrid()
  {
      nx=        0;
      np=        0;
      ng=        0; 

      rwrk=   NULL; 
      iwrk=   NULL; 

      neg=    NULL; 
      npg=    NULL; 
      igt[0]= NULL; 
      igt[1]= NULL; 
      gnms=   NULL;

      setv( (Int)0,(Int)MxNSk, (Int)-1, jek );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nep );
      setv( (Int)0,(Int)MxNSk, (Int) 0, neq );

      setv( (Int)0,(Int)MxNSk, (Int)-1, jbk );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nbp );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nbq );
      setv( (Int)0,(Int)MxNSk, (Int) 0, nbd );

  }

   void cGrid::elems(){ cout << "you must implement this method\n"; assert( false ); };
