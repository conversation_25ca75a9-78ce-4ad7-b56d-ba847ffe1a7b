
   using namespace std;

#  include <misc/grid.h>

   void cGrid::gather( Int nm, string *mnms, Int *ne, Int *siem[], Int *siep[], Int *jek, Int *msk[MxNSk] )
  {
      Int           *jwrk[MxNPSs];
      Int           *iep[MxNPSs];
      Int            ig,kp,je,jp,ie,im;
      Int            iek;
      Int            me[MxNSk];

      setv( (Int)0,(Int)MxNSk, (Int)0,ne );
      setv( (Int)0,(Int)MxNSk, (Int)0,me );
      for( ig=0;ig<ng;ig++ )
     {
         if( igt[0][ig] == igt[1][ig] )
        {
            iek= igt[0][ig];
            iek= jek[iek];
            ne[iek]+= neg[ig];
        }
     }

      for( iek=0;iek<nek;iek++ )
     {
         siep[iek]= new Int[ne[iek]*nep[iek]];
         siem[iek]= new Int[ne[iek]         ];
     }

      for( ig=0;ig<ng;ig++ )
     {
         if( igt[0][ig] == igt[1][ig] )
        {
            iek= igt[0][ig];
            iek= jek[iek];
            im= inlst( gnms[ig], nm,mnms );
            subv( npg[ig],neg[ig], iwrk[ig], jwrk );
            subv( nep[iek],ne[iek], siep[iek], iep );
            ie= me[iek];
            for( je=0;je<neg[ig];je++ )
           {
               siem[iek][ie]= im;
               for( kp=0;kp<nep[iek];kp++ )
              {
                  jp= ipmsk[iek][kp];
                  iep[jp][ie]= jwrk[kp][je];
              }
               ie++;
           }
            me[iek]= ie;
        }
     }
  }


   void cGrid::gatherb( string  mnms, Int *ne, Int *siem[], Int *siep[], Int *jek, Int *msk[MxNSk] )
  {
      Int           *jwrk[MxNPSs];
      Int           *iep[MxNPSs];
      Int            ig,kp,je,jp,ie,im;
      Int            iek;
      Int            me[MxNSk];


      setv( (Int)0,(Int)MxNSk, (Int)0,ne );
      setv( (Int)0,(Int)MxNSk, (Int)0,me );
      setv( (Int)0,(Int)MxNSk, (Int*)0,siep );
      for( ig=0;ig<ng;ig++ )
     {
         if( gnms[ig] == mnms )
        {
            iek= igt[0][ig];
            iek= jek[iek];
            ne[iek]+= neg[ig];
        }
     }

      for( iek=0;iek<nek;iek++ )
     {
         siep[iek]= new Int[ne[iek]*nep[iek]];
     }

      for( ig=0;ig<ng;ig++ )
     {
         if( gnms[ig] == mnms )
        {
            iek= igt[0][ig];
            iek= jek[iek];
            subv( npg[ig],neg[ig], iwrk[ig], jwrk );
            subv( nep[iek],ne[iek], siep[iek], iep );
            ie= me[iek];
            for( je=0;je<neg[ig];je++ )
           {
               for( kp=0;kp<nep[iek];kp++ )
              {
                  jp= ipmsk[iek][kp];
                  iep[jp][ie]= jwrk[kp][je];
              }
               ie++;
           }
            me[iek]= ie;
        }
     }
  }
