include ../../Makefile.in

CSRC=   case/case.cpp \
        case/tabsrc.cpp \
        case/pickle.cpp \
	device/device.cpp \
	device/pickle.cpp \
	device/client.cpp \
	device/tabsrc.cpp \
	device/device_tree.cpp \
	device/worker/dom/dom.cpp \
	device/worker/dom/pickle.cpp \
	device/worker/dom/tabsrc.cpp \
	device/worker/dom/client.cpp \
	device/worker/empty/client.cpp \
	domain/domain.cpp \
	domain/pickle.cpp \
	domain/periodic.cpp \
	domain/symplex.cpp \
	domain/tabsrc.cpp \
	domain/fem/domain.cpp \
	domain/fem/prep/prep.cpp \
	domain/fem/prep/load.cpp \
	domain/fem/pickle.cpp \
	domain/fem/comp.cpp \
	domain/fem/gtlhs.cpp \
	domain/fem/element/element.cpp \
	domain/fem/element/p63.cpp \
	domain/fem/element/q43.cpp \
	domain/fem/element/q83.cpp \
	domain/fem/element/t33.cpp \
	domain/fem/element/t43.cpp \
	domain/fem/element/c13.cpp \
	domain/fem/element/l13.cpp \
	domain/cfd/domain.cpp \
	domain/cfd/cell2node.cpp \
	domain/cfd/rebuild.cpp \
	domain/cfd/tecplot.cpp \
	domain/cfd/postprocess.cpp \
	domain/cfd/maverage.cpp \
	domain/cfd/layers.cpp \
	domain/cfd/init.cpp \
	domain/cfd/save.cpp \
	domain/cfd/rsmth.cpp \
	domain/cfd/gtrhs.cpp \
	domain/cfd/debugf.cpp \
	domain/cfd/tabsrc.cpp \
	domain/cfd/periodic.cpp \
	domain/cfd/distance.cpp \
	domain/cfd/pickle.cpp \
	domain/cfd/comp.cpp \
	domain/cfd/petsc_nk.cpp \
	domain/cfd/weights.cpp \
	domain/cfd/frame.cpp \
	domain/cfd/movegrid.cpp \
	domain/cfd/resd.cpp \
	domain/cfd/bcs.cpp \
	domain/cfd/newton.cpp \
	domain/cfd/gtrhf.cpp \
	domain/cfd/gtrhv.cpp \
	domain/cfd/gtrhm.cpp \
	domain/cfd/samplesol.cpp \
	domain/cfd/gtlhs.cpp \
	domain/cfd/gtresd.cpp \
	domain/cfd/gtres.cpp \
	domain/cfd/invdg.cpp \
	domain/cfd/qupdt.cpp \
	domain/cfd/grad.cpp \
	domain/cfd/restrict.cpp \
	domain/cfd/smooth.cpp \
	domain/cfd/prolong.cpp \
	domain/cfd/venk.cpp \
	domain/cfd/acc_gpu.cpp \
	domain/cfd/element/element.cpp \
	domain/cfd/element/p63.cpp \
	domain/cfd/element/p53.cpp \
	domain/cfd/element/q43.cpp \
	domain/cfd/element/q83.cpp \
	domain/cfd/element/t33.cpp \
	domain/cfd/element/t43.cpp \
	domain/cfd/element/e23.cpp \
	domain/cfd/element/e22.cpp \
	domain/cfd/element/t32.cpp \
	domain/cfd/element/q42.cpp \
	domain/cfd/prep/prep.cpp \
	domain/cfd/prep/load.cpp \
	domain/cfd/prep/faces.cpp \
	domain/cfd/bndry/fbndry.cpp \
	domain/cfd/bndry/free.cpp \
	domain/cfd/bndry/free_subin.cpp \
	domain/cfd/bndry/free_subout.cpp \
	domain/cfd/bndry/free_massflow.cpp \
	domain/cfd/bndry/free_z.cpp \
	domain/cfd/bndry/free_fft.cpp \
	domain/cfd/bndry/free_nrbc.cpp \
	domain/cfd/bndry/inv.cpp \
	domain/cfd/bndry/visc.cpp \
	domain/cfd/bndry/wave.cpp \
	domain/cfd/bndry/slide.cpp \
	domain/cfd/bndry/thermal_wall.cpp \
	domain/cfd/bndry/newfbndry.cpp \
	domain/cfd/meshconv.cpp \
	domain/cfd/vtk.cpp \
	domain/cfd/cgns.cpp \
	domain/cfd/freq/grad.cpp \
	domain/cfd/freq/comp.cpp \
	domain/cfd/freq/gtres.cpp \
	domain/cfd/freq/bcs.cpp \
	domain/cfd/freq/tecplot.cpp \
	domain/cfd/freq/vtk.cpp \
	domain/cfd/freq/save.cpp \
	domain/cfd/freq/rebuild.cpp \
	domain/cfd/freq/gtrhm.cpp \
	domain/cfd/freq/gtrhsds.cpp \
	domain/cfd/freq/gtrhfm_venk.cpp \
	domain/cfd/freq/petsc_nk_lin.cpp \
	domain/cfd/init_sol/init_vol.cpp \
	domain/cfd/init_sol/init_frame.cpp \
	domain/cfd/init_sol/init_bc.cpp \
	domain/cfd/init_sol/q263.cpp \
	domain/cfd/init_sol/read_json.cpp \
	frontend/dutil.cpp \
	frontend/cutil.cpp \
        geo/bbox.cpp \
        geo/vspline.cpp \
        geo/slow.cpp \
        geo/adtree.cpp \
        geo/boxtree.cpp \
        geo/interval.cpp \
        geo/stretch.cpp \
        geo/2d/boxt.cpp \
        geo/2d/utils.cpp \
        geo/2d/fillet.cpp \
        geo/2d/cap.cpp \
        geo/2d/interp.cpp \
        geo/2d/linear.cpp \
        geo/2d/spline.cpp \
        geo/2d/circle.cpp \
        geo/2d/straight.cpp \
        geo/2d/bezier.cpp \
        geo/2d/polyline.cpp \
        geo/2d/crossings.cpp \
        geo/2d/polyset.cpp \
	interf.cpp

#interface.cpp \

COBJ= $(CSRC:.cpp=.o)
OBJS= $(COBJ) $(FOBJ)

BINS= ../liborg.so

$(BINS): $(OBJS)
	$(PCCMP) -shared $(OBJS) -o $@

.cpp.o:
	$(PCCMP) $(COPT) $(ORGI) $(ENGI) $(SYSI) $(PETSCI) $(CGNSI) $(HDF5I) -o $@ -c $<

clean:
	rm -f $(OBJS) $(BINS)
