   using namespace std;

#  include <env.h>
#  include <case.h>
#  include <device/client.h>
#  include <device/server.h>

   void au3x( cEnv *env, cCase *rcse, cDevice *dev )
  {

      string            arg;
      Int               ncpu;

      arg= env->getarg( "-d" );
      if( arg != "" )
     {
         cout << "partition\n";
     }
      arg= env->getarg( "-p" );
      if( arg != "" )
     {
         cout << "preprocess\n";

     }
      arg= env->getarg( "-r" );
      if( arg != "" )
     {
         cout << "run\n";
     }

  }
