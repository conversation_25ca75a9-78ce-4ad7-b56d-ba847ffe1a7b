   using namespace std;

#  include <case.h>
#  include <env.h>

   void cutil( cEnv *env, cCase *cse )
  {
      Int      i;
      cTabData *tab;
      string   *lbl=NULL;
      Int       nl;
      string    arg;

      tab= new cTabData();
      cse->get( tab );
      tab->labels( &nl,&lbl );
      for( i=0;i<nl;i++ )
     {
         arg= env->getarg( "-cse:"+lbl[i] );
         if( arg != "" )
        {
            tab->force( lbl[i],arg );
        }
 
     }
      delete[] lbl;
      cse->set( tab );
      delete tab;

/*    tab= new cTabData();
      cse->get( tab );
      tab->check();
      delete tab;*/

  }
