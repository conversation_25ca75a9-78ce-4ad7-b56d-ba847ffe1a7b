
   using namespace std;

#  include <env.h>
#  include <device/client.h>
#  include <device/server.h>

   void add( cEnv *env, cDevice *dsrv, cDevice *dev )
  {
      assert(0);
//      Int     ityp;
//      string syn= ":";         
//      string *w=NULL;
//      Int     n=0;
//      string  arg;
//      arg= env->getarg( "-add" );
//      if( arg != "" )
//     {
//         parse( arg,&n,&w,syn );
//         if( n == 3 )
//        { 
//            ityp= inlst( w[1],dev_num,(string*)device_s );
//            if( ityp != dev_bad )
//           { 
//               cDevice *data= dsrv->newobject( ityp );
//               data->setname( w[2] );
//               data->touch( "created" );
//               dev->checkin( data );
//               dev->check( "   " );
//           }
//        }
//         delete[] w;
//         n=0;
//         
//     }
  }
   void edit( cEnv *env, cDevice *dsrv, cDevice *dev )
  {
      assert(0);
//      Int     ityp;
//      string syn0= ":", syn1=",", syn2= "=";
//      string *w0=NULL, *w1=NULL, *w2=NULL;
//      Int     n=0,m=0;
//      string  arg;
//      arg= env->getarg( "-edit" );
//      cDevice *tmp=NULL;
//      cTabData tab;
//      if( arg != "" )
//     {
//         parse( arg,&n,&w0,syn0 );
//         if( n == 2 )
//        { 
//            cout << "device "<<w0[0]<<"\n";;
//            cout << "commands "<<w0[1]<<"\n";
//            tmp= dsrv->checkout( w0[0] );
//            if( tmp )
//           {
//               tmp->get( &tab );
//               cout << "target found at "<<tmp<<"\n";
//               n=0;
//               parse( w0[1],&n,&w1,syn1 );
//               cout << "commands \n";
//               for( Int i=0;i<n;i++ ) 
//              {
//                  m=0;
//                  w2=NULL;
//                  parse( w1[i],&m,&w2,syn2 );
//                  if( m == 2 )
//                 {
//                     cout << "label "<<w2[0]<<" value "<<w2[1]<<"\n";
//                     tab.force( w2[0],w2[1] );
//                 }
//                  delete[] w2;
//              }
//               tmp->set( &tab );
//               delete[] w1; w1=NULL;
//           }
//        }
//         delete[] w0; w0=NULL;
//         n=0;
//         
//     }
  }

   void dutil( cEnv *env, cDevice *dsrv, cDevice *dev )
  {
      add( env,dsrv,dev );
      edit( env,dsrv,dev );
  }
