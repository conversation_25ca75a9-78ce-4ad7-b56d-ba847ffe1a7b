   using namespace std;

#  include <domain/fem/domain.h>
#  include <fstream>

   cFeDomain::cFeDomain()
  {
      Int   ig,iek,ibk;

      sld= NULL;

      dof=  NULL;
      pts=  NULL;

      for( iek=0;iek<MxNSk;iek++ )
     {
         eld[iek]= NULL;
     }
      for( ig=0;ig<MxNBG;ig++ )
     {
         bdf[ig]= NULL;
         for( ibk=0;ibk<MxNSk;ibk++ )
        {
            bld[ig][ibk]= NULL;
        }
     }

      for( iek=0;iek<MxNSk;iek++ )
     {
         elm[iek]=NULL;
         slhse[iek]= NULL;lhse[iek]=NULL;
         swrke[iek]= NULL;wrke[iek]=NULL;
         sauxe[iek]= NULL;auxe[iek]=NULL;
     }


      for( ibk=0;ibk<MxNSk;ibk++ )
     {
         blm[ibk]=NULL;
         for( ig=0;ig<MxNBG;ig++ )
        {
            slhsb[ig][ibk]= NULL;lhsb[ig][ibk]=NULL;
            swrkb[ig][ibk]= NULL;wrkb[ig][ibk]=NULL;
            sauxb[ig][ibk]= NULL;auxb[ig][ibk]=NULL;
        }
     }
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,sqb );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,sdqb );
      setv( (Int)0,(Int)MxNBG, (Real**)NULL,qb );
      setv( (Int)0,(Int)MxNBG, (Real**)NULL,dqb );

      sq=NULL;q=NULL;
      sr=NULL;r=NULL;
      sp=NULL;p=NULL;
      sx=NULL;x=NULL;
      sax=NULL;ax=NULL;
      slhsd=NULL;lhsd=NULL;

  }

   cFeDomain::~cFeDomain()
  {
      Int   is,ig,iq,iek,ibk;

      dev=  NULL;

      delete sld; sld=NULL;

      delete dof; dof=  NULL;
      delete pts; pts=  NULL;

      for( iek=0;iek<MxNSk;iek++ )
     {
         delete eld[iek]; eld[iek]= NULL;
     }
      for( ig=0;ig<MxNBG;ig++ )
     {
         delete bdf[ig]; bdf[ig]= NULL;
         delete[] sqb[ig]; sqb[ig]= NULL; delete[] qb[ig]; qb[ig]=NULL;
         delete[] sdqb[ig]; sdqb[ig]= NULL; delete[] dqb[ig]; dqb[ig]=NULL;
         for( ibk=0;ibk<MxNSk;ibk++ )
        {
            delete bld[ig][ibk]; bld[ig][ibk]= NULL;
        }
     }


      for( iek=0;iek<MxNSk;iek++ )
     {
         delete elm[iek]; elm[iek]=NULL;
         delete[] slhse[iek]; slhse[iek]= NULL; delete[] lhse[iek]; lhse[iek]=NULL;
         delete[] swrke[iek]; swrke[iek]= NULL; delete[] wrke[iek]; wrke[iek]=NULL;
         delete[] sauxe[iek]; sauxe[iek]= NULL; delete[] auxe[iek]; auxe[iek]=NULL;
     }

      for( ibk=0;ibk<MxNSk;ibk++ )
     {
         delete blm[ibk]; blm[ibk]=NULL;
         for( ig=0;ig<ng;ig++ )
        {
            delete[] slhsb[ig][ibk]; slhsb[ig][ibk]= NULL; delete[] lhsb[ig][ibk]; lhsb[ig][ibk]= NULL;
            delete[] swrkb[ig][ibk]; swrkb[ig][ibk]= NULL; delete[] wrkb[ig][ibk]; wrkb[ig][ibk]= NULL;
            delete[] sauxb[ig][ibk]; sauxb[ig][ibk]= NULL; delete[] auxb[ig][ibk]; auxb[ig][ibk]= NULL;
            nb[ig][ibk]=0;
        }
     }

      delete[] sq; sq= NULL; delete[] q; q=NULL;
      delete[] sr; sr= NULL; delete[] r; r=NULL;
      delete[] sx; sx= NULL; delete[] x; x=NULL;
      delete[] sp; sp= NULL; delete[] p; p=NULL;
      delete[] sax; sax= NULL; delete[] ax; ax=NULL;
      delete[] slhsd; slhsd= NULL; delete[] lhsd; lhsd=NULL;

  }

   Int cFeDomain::addelem( cElement *el )
  {
      if( nek < MxNSk )
     {
         nep[nek]= el->getnp();
         neq[nek]= el->getnq();
         elm[nek]= el;
         nek++;
     }
      else
     {
         cout << "no more slots available for element types\n";
         exit(1);
     }
      return nek-1;
  }

   Int cFeDomain::addblem( cElement *bn )
  {
      if( nbk < MxNSk )
     {
         nbp[nbk]= bn->getnp();
         nbq[nbk]= bn->getnq();
         nbd[nbk]= bn->getnd();
         blm[nbk]= bn;
         nbk++;
     }
      else
     {
         cout << "no more slots available for boundary element types\n";
         exit(1);
     }
      return nbk-1;
  }

   void cFeDomain::getcost0( Real *cost )
  {
      Real val;
      Int  iek,iq;

      cout<< "cFeDomain getcost\n";

      delete[] qcst;
      qcst= new Real[nq];
      delete[] qcpu;
      qcpu= new Int[nq];

      setv( 0,nq, ZERO, qcst );

      for( iek=0;iek<nek;iek++ )
     {
         elm[iek]->getcost( 0,ne[iek], ieq[iek], qcst );
     }

      val=0;
      for( iq=0;iq<nq;iq++ )
     {
         val+= qcst[iq];
     }
      cout << "cost extimated for this domain is "<<val << "\n";
     *cost+= val;
  }


   cElement *cFeDomain::newelement( Int ityp )
  {
      cElement *val=NULL;
      switch( ityp )
     {
         case( element_q43 ):
        {
            val= new cq43();
            break;
        }
         case( element_t33 ):
        {
            val= new ct33();
            break;
        }
         case( element_q83 ):
        {
            val= new cq83();
            break;
        }
         case( element_p63 ):
        {
            val= new cp63();
            break;
        }
         case( element_t43 ):
        {
            val= new ct43();
            break;
        }
         case( element_c13 ):
        {
            val= new cc13();
            break;
        }
         case( element_l13 ):
        {
            val= new cl13();
            break;
        }
     }
      return val;
  }

   void cFeDomain::assgnsld( cSolid *s )
  {
      sld= s;
  }


   void cFeDomain::assgnauxe( Int iek, Real *aux )
  {
      if( sauxe[iek] )
     {
         delete[] sauxe[iek];
         delete[] auxe[iek];
     }
      if( nv == 0 )
     {
         cout << "cannot assign auxe without knowing how many variables you have\n";
         exit(1);
     }
      sauxe[iek]= aux;
      nauxe[iek]= elm[iek]->getnaux();
      auxe[iek]= new Real*[nauxe[iek]+1];
      subv( nauxe[iek],ne[iek], sauxe[iek],auxe[iek] );
  }
