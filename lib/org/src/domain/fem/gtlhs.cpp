
   using namespace std;


//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Sun Oct 24 22:16:28 BST 2010
// Changes History
// Next Change(s)  -
// Purpose         element stiffness matrix assembly

#  include <domain/fem/domain.h>

   void cFeDomain::gtlhs( )
  {
//      Int           ig,iek,ies,iee,ick,ibs,ibe,ie,ia,ja,id,ibk;
//      Int           ncpu;
//
//      ncpu= dev->getncpu();
//      setv( 0,np, nv*nv,ZERO,lhsd );
//
////    pts->exchange( sxp );
//      dof->exchange( sxq );
//      dof->exchange( sq );
//      while( dev->transit() )
//     {
//         for( iek=0;iek<nek;iek++ )
//        {
//            eld[iek]->range( dev->avail(), &ies,&iee );
//            elm[iek]->gtlhs( ies,iee, iem[iek][0],iep[iek],xp, ieq[iek],xq,q, lhse[iek],lhsd, auxe[iek],wrke[iek], coo,sld );
//        }
//     }
//      for( id=0;id<ncpu+1;id++ )
//     {
//         for( ig=0;ig<ng;ig++ )
//        {
//            for( ibk=0;ibk<nbk;ibk++ )
//           {
//               bld[ig][ibk]->range( id, &ibs,&ibe );
//               blm[ibk]->gtlhs( ibs,ibe, NULL,ibp[ig][ibk],xp, ibq[ig][ibk], xq,  q, lhsb[ig][ibk],lhsd, auxb[ig][ibk],wrkb[ig][ibk], coo,sld );
//           }
//        }
//     }
//
//     Int *siprm= new Int[nv*nv];
//     Int **iprm= new Int*[nv];
//     identv( nv*nv, siprm );
//     subv( nv,nv, siprm,iprm );
//
//      cout << "lhsd before getrf\n";
//      for( Int ip=0;ip<np;ip++ )
//     {
//         for( Int iv=0;iv<nv*nv;iv++ )
//        {
//            cout << lhsd[iv][ip]<<" ";
//        }
//         cout << "\n";
//     }
//      getrf( 0,np, nv,iprm, lhsd );
//      cout << "lhsd after gerf\n";
//      for( Int ip=0;ip<np;ip++ )
//     {
//         for( Int iv=0;iv<nv*nv;iv++ )
//        {
//            cout << lhsd[iv][ip]<<" ";
//        }
//         cout << "\n";
//     }
//      delete[] iprm;
//      delete[] siprm;
////    exit(0);
  }
