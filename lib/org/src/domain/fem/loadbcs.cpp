   using namespace std;

#  include <domain/fem/domain.h>

   void cFeDomain::loadbcs()
  {
      Int        ig,ibk,ib;
      cPlugin   *pl;
      bpl_t      bpl_f;
      for( ig=0;ig<ng;ig++ )
     {
         cout << "should be loading boundary conditions for "<<bgnm[ig]<<" using "<<bpl[ig]<<" "<<bpo[ig]<<"\n";
         pl= new cPlugin( bpl[ig],bpo[ig] );
         bpl_f= (bpl_t)(pl->Sub());
         cout << "the function is "<<bpl_f<<"\n";

       (*bpl_f)( this, bgnm[ig], nbb[ig], nx,xb[ig],nv,qb[ig] );
 
         delete pl; pl=NULL;

         cout << "boundary data, group "<<ig<<"\n";
         for( ib=0;ib<nbb[ig];ib++ )
        {
            cout << xb[ig][0][ib]<<" "<< xb[ig][1][ib]<<" "<< xb[ig][2][ib]<<" "<<
                    qb[ig][0][ib]<<" "<< qb[ig][1][ib]<<" "<< qb[ig][2][ib]<<"\n";
        }
     }
      
  }
