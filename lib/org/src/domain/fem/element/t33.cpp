   using namespace std;


//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

#  include <domain/fem/element/element.h>

   ct33::ct33()
  {
      nst=3;
      nsp=3;
      nsq=3;
      naux= 2;
      nlhs= 0;
      nwrk= 0;
      nbdv= 0;
      ng=0;
      yg[0]= NULL; 
      yg[1]= NULL; 
      yg[2]= NULL; 
      wg=    NULL; 
  }


   ct33::~ct33()
  {
  }

   void ct33::shpf( Real *l, Real *g, Real *dgdl[] ){};
   void ct33::shpx( Real *l, Real *g, Real *dgdl[] ){};

   void ct33::gtlhs( Int ies, Int iee, Int *iem, Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld )
  {
      Int            nx;
      Int            ie,jp,jq,iq,i0,i1,i2,j0,j1,j2;

      Real            a;
      Real            x[9];
      Real            l[2][4];
      Real            y[9];
      Real            dy[2][3];
      
      if( iee > ies )
     {
         setv( ies,iee, nlhs,ZERO, lhs );
         setv( ies,iee, nwrk,ZERO, wrk );
         cout << "t33::gtlhs quarantined\n";

         for( ie=ies;ie<iee;ie++ ) 
        {

            i0= iep[0][ie];
            i1= iep[1][ie];
            i2= iep[2][ie];
            
            x[0]= xp[0][i0];
            x[1]= xp[0][i1];
            x[2]= xp[0][i2];

            x[3]= xp[1][i0];
            x[4]= xp[1][i1];
            x[5]= xp[1][i2];

            x[6]= xp[2][i0];
            x[7]= xp[2][i1];
            x[8]= xp[2][i2];

// local frame: first axis in line with triangle basis
            l[0][0]= x[1]-x[0];
            l[0][1]= x[4]-x[3];
            l[0][2]= x[7]-x[6];
            l[0][3]= l[0][0]*l[0][0]+ l[0][1]*l[0][1]+ l[0][2]*l[0][2];
            l[0][3]= sqrt( l[0][3] );
            l[0][0]/= l[0][3];
            l[0][1]/= l[0][3];
            l[0][2]/= l[0][3];

// local frame: second axis via Gram-Schmidt orthogonalization
            l[1][0]= x[2]-x[1];
            l[1][1]= x[5]-x[4];
            l[1][2]= x[8]-x[7];
            l[1][3]= l[0][0]*l[1][0]+ l[0][1]*l[1][1]+ l[0][2]*l[1][2];
            l[1][0]-= l[0][0]*l[1][3];
            l[1][1]-= l[0][1]*l[1][3];
            l[1][2]-= l[0][2]*l[1][3];
            l[1][3]= l[1][0]*l[1][0]+ l[1][1]*l[1][1]+ l[1][2]*l[1][2];
            l[1][3]= sqrt( l[1][3] );
            l[1][0]/= l[1][3];
            l[1][1]/= l[1][3];
            l[1][2]/= l[1][3];

// local coordinates
            y[0]= 0;
            y[1]= ( x[1]- x[0] )*l[0][0]+ ( x[4]-x[3] )*l[0][1]+ ( x[7]-x[6] )*l[0][2];
            y[2]= ( x[2]- x[0] )*l[0][0]+ ( x[5]-x[3] )*l[0][1]+ ( x[8]-x[6] )*l[0][2];

            y[3]= 0;
            y[4]= ( x[1]- x[0] )*l[1][0]+ ( x[4]-x[3] )*l[1][1]+ ( x[7]-x[6] )*l[1][2];
            y[5]= ( x[2]- x[0] )*l[1][0]+ ( x[5]-x[3] )*l[1][1]+ ( x[8]-x[6] )*l[1][2];  

            dy[0][0]= y[4]-y[5];
            dy[0][1]= y[5]-y[3];
            dy[0][2]= y[3]-y[4];

            dy[1][0]= y[2]-y[1];
            dy[1][1]= y[0]-y[2];
            dy[1][2]= y[1]-y[0];

            a= dy[0][0]*dy[1][1]- dy[1][0]*dy[0][1];

// local gradients

            dy[0][0]/=a;
            dy[0][1]/=a;
            dy[0][2]/=a;

            dy[1][0]/=a;
            dy[1][1]/=a;
            dy[1][2]/=a;

// columns of the b-matrix

            wrk[ 0][ie]= l[0][0]*dy[0][0];                    // b(1,1)= l_x*dN1/dl
            wrk[ 1][ie]= l[1][0]*dy[1][0];                    // b(2,1)= m_x*dN1/dm
            wrk[ 2][ie]= l[1][0]*dy[0][0]+ l[0][0]*dy[1][0];  // b(3,1)= m_x*dN1/dl+ l_x*dN1/dm

            wrk[ 3][ie]= l[0][0]*dy[0][1];                    // b(1,2)= l_x*dN2/dl
            wrk[ 4][ie]= l[1][0]*dy[1][1];                    // b(2,2)= m_x*dN2/dm
            wrk[ 5][ie]= l[1][0]*dy[0][1]+ l[0][0]*dy[1][1];  // b(3,2)= m_x*dN2/dl+ l_x*dN2/dm

            wrk[ 6][ie]= l[0][0]*dy[0][2];                    // b(1,3)= l_x*dN3/dl
            wrk[ 7][ie]= l[1][0]*dy[1][2];                    // b(2,3)= m_x*dN3/dm
            wrk[ 8][ie]= l[1][0]*dy[0][2]+ l[0][0]*dy[1][2];  // b(3,3)= m_x*dN3/dl+ l_x*dN3/dm

            wrk[ 9][ie]= l[0][1]*dy[0][0];                    // b(1,4)= l_y*dN1/dl
            wrk[10][ie]= l[1][1]*dy[1][0];                    // b(2,4)= m_y*dN1/dm
            wrk[11][ie]= l[1][1]*dy[0][0]+ l[0][1]*dy[1][0];  // b(3,4)= m_y*dN1/dl+ l_y*dN1/dm

            wrk[12][ie]= l[0][1]*dy[0][1];                    // b(1,5)= l_y*dN2/dl
            wrk[13][ie]= l[1][1]*dy[1][1];                    // b(2,5)= m_y*dN2/dm
            wrk[14][ie]= l[1][1]*dy[0][1]+ l[0][1]*dy[1][1];  // b(3,5)= m_y*dN2/dl+ l_y*dN2/dm

            wrk[15][ie]= l[0][1]*dy[0][2];                    // b(1,6)= l_y*dN3/dl
            wrk[16][ie]= l[1][1]*dy[1][2];                    // b(2,6)= m_y*dN3/dm
            wrk[17][ie]= l[1][1]*dy[0][2]+ l[0][1]*dy[1][2];  // b(3,6)= m_y*dN3/dl+ l_y*dN3/dm

            wrk[18][ie]= l[0][2]*dy[0][0];                    // b(1,7)= l_z*dN1/dl
            wrk[19][ie]= l[1][2]*dy[1][0];                    // b(2,7)= m_z*dN1/dm
            wrk[20][ie]= l[1][2]*dy[0][0]+ l[0][2]*dy[1][0];  // b(3,7)= m_z*dN1/dl+ l_z*dN1/dm

            wrk[21][ie]= l[0][2]*dy[0][1];                    // b(1,8)= l_z*dN2/dl
            wrk[22][ie]= l[1][2]*dy[1][1];                    // b(2,8)= m_z*dN2/dm
            wrk[23][ie]= l[1][2]*dy[0][1]+ l[0][2]*dy[1][1];  // b(3,8)= m_z*dN2/dl+ l_z*dN2/dm

            wrk[24][ie]= l[0][2]*dy[0][2];                    // b(1,9)= l_z*dN3/dl
            wrk[25][ie]= l[1][2]*dy[1][2];                    // b(2,9)= m_z*dN3/dm
            wrk[26][ie]= l[1][2]*dy[0][2]+ l[0][2]*dy[1][2];  // b(3,9)= m_z*dN3/dl+ l_z*dN3/dm

            wrk[27][ie]= 0.5*a;

        }
     }

      for( jq=0;jq<9;jq++ )
     {
         sld->stress2( ies, iee, iem, wrk+3*jq, wrk+28 );

         for( ie=ies;ie<iee;ie++ )
        {
            
            lhs[0+9*jq][ie]= ( wrk[ 0][ie]*wrk[28][ie]+ wrk[ 1][ie]*wrk[29][ie]+ wrk[ 2][ie]*wrk[30][ie] )*wrk[27][ie]*aux[0][ie];
            lhs[1+9*jq][ie]= ( wrk[ 3][ie]*wrk[28][ie]+ wrk[ 4][ie]*wrk[29][ie]+ wrk[ 5][ie]*wrk[30][ie] )*wrk[27][ie]*aux[0][ie];
            lhs[2+9*jq][ie]= ( wrk[ 6][ie]*wrk[28][ie]+ wrk[ 7][ie]*wrk[29][ie]+ wrk[ 8][ie]*wrk[30][ie] )*wrk[27][ie]*aux[0][ie];
            lhs[3+9*jq][ie]= ( wrk[ 9][ie]*wrk[28][ie]+ wrk[10][ie]*wrk[29][ie]+ wrk[11][ie]*wrk[30][ie] )*wrk[27][ie]*aux[0][ie];
            lhs[4+9*jq][ie]= ( wrk[12][ie]*wrk[28][ie]+ wrk[13][ie]*wrk[29][ie]+ wrk[14][ie]*wrk[30][ie] )*wrk[27][ie]*aux[0][ie];
            lhs[5+9*jq][ie]= ( wrk[15][ie]*wrk[28][ie]+ wrk[16][ie]*wrk[29][ie]+ wrk[17][ie]*wrk[30][ie] )*wrk[27][ie]*aux[0][ie];
            lhs[6+9*jq][ie]= ( wrk[18][ie]*wrk[28][ie]+ wrk[19][ie]*wrk[29][ie]+ wrk[20][ie]*wrk[30][ie] )*wrk[27][ie]*aux[0][ie];
            lhs[7+9*jq][ie]= ( wrk[21][ie]*wrk[28][ie]+ wrk[22][ie]*wrk[29][ie]+ wrk[23][ie]*wrk[30][ie] )*wrk[27][ie]*aux[0][ie];
            lhs[8+9*jq][ie]= ( wrk[24][ie]*wrk[28][ie]+ wrk[25][ie]*wrk[29][ie]+ wrk[26][ie]*wrk[30][ie] )*wrk[27][ie]*aux[0][ie];
        }
     }

      for( ie=ies;ie<iee;ie++ )
     {
         j0= ieq[0][ie];
         j1= ieq[1][ie];
         j2= ieq[2][ie];

         lhsd[0][j0]+= lhs[ 0][ie];
         lhsd[1][j0]+= lhs[ 3][ie];
         lhsd[2][j0]+= lhs[ 6][ie];
         lhsd[3][j0]+= lhs[27][ie];
         lhsd[4][j0]+= lhs[30][ie];
         lhsd[5][j0]+= lhs[33][ie];
         lhsd[6][j0]+= lhs[54][ie];
         lhsd[7][j0]+= lhs[57][ie];
         lhsd[8][j0]+= lhs[60][ie];

         lhsd[0][j1]+= lhs[10][ie];
         lhsd[1][j1]+= lhs[13][ie];
         lhsd[2][j1]+= lhs[16][ie];
         lhsd[3][j1]+= lhs[37][ie];
         lhsd[4][j1]+= lhs[40][ie];
         lhsd[5][j1]+= lhs[43][ie];
         lhsd[6][j1]+= lhs[64][ie];
         lhsd[7][j1]+= lhs[67][ie];
         lhsd[8][j1]+= lhs[70][ie];

         lhsd[0][j2]+= lhs[20][ie];
         lhsd[1][j2]+= lhs[23][ie];
         lhsd[2][j2]+= lhs[26][ie];
         lhsd[3][j2]+= lhs[47][ie];
         lhsd[4][j2]+= lhs[50][ie];
         lhsd[5][j2]+= lhs[53][ie];
         lhsd[6][j2]+= lhs[74][ie];
         lhsd[7][j2]+= lhs[77][ie];
         lhsd[8][j2]+= lhs[80][ie];

     }
  }
/*
   void ct33::gtrhs( Int ies, Int iee, Real *xp[], Real *q[], Int *iep[], Int *ier[], Real *lhs[], Real *aux[], Real *wrk[], Real *rhs[], cCosystem *coo )
  {
      Int            ie,i0,i1,i2,j0,j1,j2;
      Real           u[9],r[9];
      
      if( iee > ies )
     {
         for( ie=ies;ie<iee;ie++ ) 
        {

            i0= iep[0][ie];
            i1= iep[1][ie];
            i2= iep[2][ie];

            j0= ier[0][ie];
            j1= ier[1][ie];
            j2= ier[2][ie];
            
            u[0]= q[0][i0];
            u[1]= q[0][i1];
            u[2]= q[0][i2];

            u[3]= q[1][i0];
            u[4]= q[1][i1];
            u[5]= q[1][i2];

            u[6]= q[2][i0];
            u[7]= q[2][i1];
            u[8]= q[2][i2];

            r[0]= lhs[ 0][ie]*u[0]+ lhs[ 1][ie]*u[1]+ lhs[ 2][ie]*u[2]+ lhs[ 3][ie]*u[3]+ lhs[ 4][ie]*u[4]+ lhs[ 5][ie]*u[5]+ lhs[ 6][ie]*u[6]+ lhs[ 7][ie]*u[7]+ lhs[ 8][ie]*u[8];
            r[1]= lhs[ 9][ie]*u[0]+ lhs[10][ie]*u[1]+ lhs[11][ie]*u[2]+ lhs[12][ie]*u[3]+ lhs[13][ie]*u[4]+ lhs[14][ie]*u[5]+ lhs[15][ie]*u[6]+ lhs[16][ie]*u[7]+ lhs[17][ie]*u[8];
            r[2]= lhs[18][ie]*u[0]+ lhs[19][ie]*u[1]+ lhs[20][ie]*u[2]+ lhs[21][ie]*u[3]+ lhs[22][ie]*u[4]+ lhs[23][ie]*u[5]+ lhs[24][ie]*u[6]+ lhs[25][ie]*u[7]+ lhs[26][ie]*u[8];

            r[3]= lhs[27][ie]*u[0]+ lhs[28][ie]*u[1]+ lhs[29][ie]*u[2]+ lhs[30][ie]*u[3]+ lhs[31][ie]*u[4]+ lhs[32][ie]*u[5]+ lhs[33][ie]*u[6]+ lhs[34][ie]*u[7]+ lhs[35][ie]*u[8];
            r[4]= lhs[36][ie]*u[0]+ lhs[37][ie]*u[1]+ lhs[38][ie]*u[2]+ lhs[39][ie]*u[3]+ lhs[40][ie]*u[4]+ lhs[41][ie]*u[5]+ lhs[42][ie]*u[6]+ lhs[43][ie]*u[7]+ lhs[44][ie]*u[8];
            r[5]= lhs[45][ie]*u[0]+ lhs[46][ie]*u[1]+ lhs[47][ie]*u[2]+ lhs[48][ie]*u[3]+ lhs[49][ie]*u[4]+ lhs[50][ie]*u[5]+ lhs[51][ie]*u[6]+ lhs[52][ie]*u[7]+ lhs[53][ie]*u[8];

            r[6]= lhs[54][ie]*u[0]+ lhs[55][ie]*u[1]+ lhs[56][ie]*u[2]+ lhs[57][ie]*u[3]+ lhs[58][ie]*u[4]+ lhs[59][ie]*u[5]+ lhs[60][ie]*u[6]+ lhs[61][ie]*u[7]+ lhs[62][ie]*u[8];
            r[7]= lhs[63][ie]*u[0]+ lhs[64][ie]*u[1]+ lhs[65][ie]*u[2]+ lhs[66][ie]*u[3]+ lhs[67][ie]*u[4]+ lhs[68][ie]*u[5]+ lhs[69][ie]*u[6]+ lhs[70][ie]*u[7]+ lhs[71][ie]*u[8];
            r[8]= lhs[72][ie]*u[0]+ lhs[73][ie]*u[1]+ lhs[74][ie]*u[2]+ lhs[75][ie]*u[3]+ lhs[76][ie]*u[4]+ lhs[77][ie]*u[5]+ lhs[78][ie]*u[6]+ lhs[79][ie]*u[7]+ lhs[80][ie]*u[8];

            rhs[0][j0]+= r[0];
            rhs[0][j1]+= r[1];
            rhs[0][j2]+= r[2];

            rhs[1][j0]+= r[3];
            rhs[1][j1]+= r[4];
            rhs[1][j2]+= r[5];

            rhs[2][j0]+= r[6];
            rhs[2][j1]+= r[7];
            rhs[2][j2]+= r[8];

  
        }
     }

  }

   void ct33::gtdrhs( Int ies, Int iee, Real *xp[], Real *q[], Real *dq[], Int *iep[], Int *ier[], Real *lhs[], Real *aux[], Real *wrk[], Real *drhs[], cCosystem *coo )
  {
      gtrhs( ies,iee, xp,dq, iep,ier, lhs,aux,wrk,drhs, coo );
  }
*/
