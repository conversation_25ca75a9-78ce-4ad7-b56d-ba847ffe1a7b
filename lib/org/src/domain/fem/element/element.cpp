   using namespace std;

#  include <domain/fem/element/element.h>

   cElement::cElement()
  {
      nlhs=0;
      nwrk=0;
      naux=0;
      nbdv=0;
      nst=0;
      ng=0;

      yg[0]= NULL;
      yg[1]= NULL;
      yg[2]= NULL;
      wg=NULL;
  }

   cElement::~cElement()
  {
      nlhs=0;
      nwrk=0;
      naux=0;
      nbdv=0;
      nst=0;
      ng=0;
      delete[] yg[0]; yg[0]= NULL;
      delete[] yg[1]; yg[1]= NULL;
      delete[] yg[2]; yg[2]= NULL;
      delete[] wg; wg=NULL;
  }

   void cElement::getcost( Int ies, Int iee, Int *ieq[], Real *qc )
  {
      Int ie,jq,iq;
      for( jq=0;jq<nsq;jq++ )
     {
         for( ie=ies;ie<iee;ie++ )
        {
            iq= ieq[jq][ie]; 
            qc[iq]+= 1.;
        }
     } 
  }

   Int cElement::getnd()
  {
      return nbdv;
  }

   Int cElement::getnlhs()
  {
      return nlhs;
  }

   Int cElement::getnwrk()
  {
      return nwrk;
  }

   Int cElement::getnaux()
  {
      return naux;
  }


   void cElement::sizes( cSolid *s )
  {
      Int ndof;
      ndof= nsq*s->getnv();
      nlhs= ndof*ndof;
      nwrk= nst*(ndof+1)+1;
  }

