
   using namespace std;


//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

#  include <domain/fem/element/element.h>

   cl13::cl13()
  {
      nsp=1;
      nsq=1;
      ng=0;
      nbdv=1;
      naux=0;
      nlhs=0;
      nwrk=0;
  }


   cl13::~cl13()
  {
  }

/* void cl13::gtrhs( Int ics, Int ice, Int *icp[], Int *icr[], Real *xp[], Real *q[], Real *qc[], Real *wrk[], Real *rhs[], cCosystem * )
  {
      Int ip,ic;
      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
             ip= icr[0][ic];
//           cout << "apply load to node "<<ip<<"\n";
             rhs[0][ip]+= qc[0][ic];
             rhs[1][ip]+= qc[1][ic];
             rhs[2][ip]+= qc[2][ic];
        }
     }
  }*/
