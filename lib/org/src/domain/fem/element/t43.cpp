   using namespace std;


#  include <domain/fem/element/element.h>

   ct43::ct43()
  {
      nst=6;
      nsp=4;
      nsq=4;
      ng=0;
      naux= 2;
      nwrk= 0;
      nlhs= 0;
      nbdv= 0;
  }


   ct43::~ct43()
  {
  }

   void ct43::shpf( Real *l, Real *g, Real *dgdl[] )
  {
      
      g[0]= l[0];
      g[1]= l[1];
      g[2]= l[2];
      g[3]= l[0]+l[1]+l[2];
      g[3]= 1.-g[3];

      dgdl[0][0]= 1.;
      dgdl[1][0]= 0.;
      dgdl[2][0]= 0.;
                
      dgdl[0][1]= 0.;
      dgdl[1][1]= 1.;
      dgdl[2][1]= 0.;
                
      dgdl[0][2]= 0.;
      dgdl[1][2]= 0.;
      dgdl[2][2]= 1.;
                
      dgdl[0][3]=-1.;
      dgdl[1][3]=-1.;
      dgdl[2][3]=-1.;
  }

   void ct43::shpx( Real *l, Real *g, Real *dgdl[] )
  {
      shpf( l,g,dgdl );
  }


   void ct43::gtlhs( Int ies, Int iee, Int *iem, Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld )

  {
      
      Int            ie,jp,jq,iq,i0,i1,i2,i3,j0,j1,j2,j3;

      
      Real            x[12];
      Real            dxdl[3][3];
      Real            det;
      Real            cdxdl[3][3];
      Real            volme;
      Real            tetcoord[3][3];
      Real            dFdx[3][4];
         Real lg[3]= {0.25, 0.25, 0.25};
         Real g[4];
         Real sdgdl[12]={0.0};
         Real *dgdl[3];
      
      subv(3, 4, sdgdl, dgdl);     

      if( iee > ies )
     {
         setv( ies,iee, nlhs,ZERO, lhs );
         setv( ies,iee, nwrk,ZERO, wrk );

         cout <<"tetra materials\n";
         for( ie=ies;ie<iee;ie++ )
        {
            cout << ie <<" "<<iem[ie]<<"\n";
        }
      
         shpf( lg, g, dgdl);
        
         for( ie=ies;ie<iee;ie++ ) 
        {

            i0= iep[0][ie];
            i1= iep[1][ie];
            i2= iep[2][ie];
            i3= iep[3][ie];
            
            x[0]= xp[0][i0];
            x[1]= xp[0][i1];
            x[2]= xp[0][i2];
            x[3]= xp[0][i3];

            x[4]= xp[1][i0];
            x[5]= xp[1][i1];
            x[6]= xp[1][i2];
            x[7]= xp[1][i3];

            x[8]= xp[2][i0];
            x[9]= xp[2][i1];
            x[10]= xp[2][i2];
            x[11]= xp[2][i3]; 

            tetcoord[0][0]= xp[0][i0]-xp[0][i1];
            tetcoord[1][0]= xp[1][i0]-xp[1][i1];
            tetcoord[2][0]= xp[2][i0]-xp[2][i1];              

            tetcoord[0][1]= xp[0][i1]-xp[0][i2];
            tetcoord[1][1]= xp[1][i1]-xp[1][i2];
            tetcoord[2][1]= xp[2][i1]-xp[2][i2];

            tetcoord[0][2]= xp[0][i2]-xp[0][i3];
            tetcoord[1][2]= xp[1][i2]-xp[1][i3];
            tetcoord[2][2]= xp[2][i2]-xp[2][i3];


            det= tetcoord[0][0]*tetcoord[1][1]*tetcoord[2][2]+  tetcoord[0][1]*tetcoord[1][2]*tetcoord[2][0]+ tetcoord[0][2]*tetcoord[1][0]*tetcoord[2][1]- tetcoord[0][0]*tetcoord[1][2]*tetcoord[2][1]- tetcoord[0][1]*tetcoord[1][0]*tetcoord[2][2]- tetcoord[0][2]*tetcoord[1][1]*tetcoord[2][0];
            volme= fabs(det)/6.0;
            
            dxdl[0][0]= dgdl[0][0]*x[0]+dgdl[0][1]*x[1]+dgdl[0][2]*x[2]+dgdl[0][3]*x[3];
            dxdl[0][1]= dgdl[0][0]*x[4]+dgdl[0][1]*x[5]+dgdl[0][2]*x[6]+dgdl[0][3]*x[7];
            dxdl[0][2]= dgdl[0][0]*x[8]+dgdl[0][1]*x[9]+dgdl[0][2]*x[10]+dgdl[0][3]*x[11];  

            dxdl[1][0]= dgdl[1][0]*x[0]+dgdl[1][1]*x[1]+dgdl[1][2]*x[2]+dgdl[1][3]*x[3];
            dxdl[1][1]= dgdl[1][0]*x[4]+dgdl[1][1]*x[5]+dgdl[1][2]*x[6]+dgdl[1][3]*x[7];
            dxdl[1][2]= dgdl[1][0]*x[8]+dgdl[1][1]*x[9]+dgdl[1][2]*x[10]+dgdl[1][3]*x[11]; 

            dxdl[2][0]= dgdl[2][0]*x[0]+dgdl[2][1]*x[1]+dgdl[2][2]*x[2]+dgdl[2][3]*x[3];
            dxdl[2][1]= dgdl[2][0]*x[4]+dgdl[2][1]*x[5]+dgdl[2][2]*x[6]+dgdl[2][3]*x[7];
            dxdl[2][2]= dgdl[2][0]*x[8]+dgdl[2][1]*x[9]+dgdl[2][2]*x[10]+dgdl[2][3]*x[11];

            cdxdl[0][0]= dxdl[1][1]* dxdl[2][2]-  dxdl[1][2]* dxdl[2][1];
            cdxdl[0][1]= dxdl[1][2]* dxdl[2][0]-  dxdl[1][0]* dxdl[2][2];
            cdxdl[0][2]= dxdl[1][0]* dxdl[2][1]-  dxdl[1][1]* dxdl[2][0];

            cdxdl[1][0]= dxdl[2][1]* dxdl[0][2]-  dxdl[2][2]* dxdl[0][1];
            cdxdl[1][1]= dxdl[2][2]* dxdl[0][0]-  dxdl[2][0]* dxdl[0][2];
            cdxdl[1][2]= dxdl[2][0]* dxdl[0][1]-  dxdl[2][1]* dxdl[0][0];

            cdxdl[2][0]= dxdl[0][1]* dxdl[1][2]-  dxdl[0][2]* dxdl[1][1];
            cdxdl[2][1]= dxdl[0][2]* dxdl[1][0]-  dxdl[0][0]* dxdl[1][2];
            cdxdl[2][2]= dxdl[0][0]* dxdl[1][1]-  dxdl[0][1]* dxdl[1][0];

            det=              cdxdl[0][0]* dxdl[0][0]+
                              cdxdl[0][1]* dxdl[0][1]+
                              cdxdl[0][2]* dxdl[0][2];
           
// this is the transpose!
            dxdl[0][0]= cdxdl[0][0]/det;
            dxdl[0][1]= cdxdl[0][1]/det;
            dxdl[0][2]= cdxdl[0][2]/det;

            dxdl[1][0]= cdxdl[1][0]/det;
            dxdl[1][1]= cdxdl[1][1]/det;
            dxdl[1][2]= cdxdl[1][2]/det;

            dxdl[2][0]= cdxdl[2][0]/det;
            dxdl[2][1]= cdxdl[2][1]/det;
            dxdl[2][2]= cdxdl[2][2]/det;

            dFdx[0][0]= dxdl[0][0]*dgdl[0][0]+ dxdl[1][0]*dgdl[1][0]+ dxdl[2][0]*dgdl[2][0];
            dFdx[0][1]= dxdl[0][0]*dgdl[0][1]+ dxdl[1][0]*dgdl[1][1]+ dxdl[2][0]*dgdl[2][1];    
            dFdx[0][2]= dxdl[0][0]*dgdl[0][2]+ dxdl[1][0]*dgdl[1][2]+ dxdl[2][0]*dgdl[2][2];
            dFdx[0][3]= dxdl[0][0]*dgdl[0][3]+ dxdl[1][0]*dgdl[1][3]+ dxdl[2][0]*dgdl[2][3];  
            
            dFdx[1][0]= dxdl[0][1]*dgdl[0][0]+ dxdl[1][1]*dgdl[1][0]+ dxdl[2][1]*dgdl[2][0];
            dFdx[1][1]= dxdl[0][1]*dgdl[0][1]+ dxdl[1][1]*dgdl[1][1]+ dxdl[2][1]*dgdl[2][1];    
            dFdx[1][2]= dxdl[0][1]*dgdl[0][2]+ dxdl[1][1]*dgdl[1][2]+ dxdl[2][1]*dgdl[2][2];
            dFdx[1][3]= dxdl[0][1]*dgdl[0][3]+ dxdl[1][1]*dgdl[1][3]+ dxdl[2][1]*dgdl[2][3];
            
            dFdx[2][0]= dxdl[0][2]*dgdl[0][0]+ dxdl[1][2]*dgdl[1][0]+ dxdl[2][2]*dgdl[2][0];
            dFdx[2][1]= dxdl[0][2]*dgdl[0][1]+ dxdl[1][2]*dgdl[1][1]+ dxdl[2][2]*dgdl[2][1];    
            dFdx[2][2]= dxdl[0][2]*dgdl[0][2]+ dxdl[1][2]*dgdl[1][2]+ dxdl[2][2]*dgdl[2][2];
            dFdx[2][3]= dxdl[0][2]*dgdl[0][3]+ dxdl[1][2]*dgdl[1][3]+ dxdl[2][2]*dgdl[2][3]; 

// columns of the b-matrix
            
            wrk[ 0][ie]= dFdx[0][0];
            wrk[ 3][ie]= dFdx[1][0];
            wrk[ 5][ie]= dFdx[2][0];

            wrk[ 6][ie]= dFdx[0][1];
            wrk[ 9][ie]= dFdx[1][1];
            wrk[11][ie]= dFdx[2][1];

            wrk[12][ie]= dFdx[0][2];
            wrk[15][ie]= dFdx[1][2];
            wrk[17][ie]= dFdx[2][2];
 
            wrk[18][ie]= dFdx[0][3];
            wrk[21][ie]= dFdx[1][3];
            wrk[23][ie]= dFdx[2][3];

////////////////////////////////////////////////////////////////////////////

            wrk[25][ie]= dFdx[1][0];
            wrk[27][ie]= dFdx[0][0];
            wrk[28][ie]= dFdx[2][0];

            wrk[31][ie]= dFdx[1][1];
            wrk[33][ie]= dFdx[0][1];
            wrk[34][ie]= dFdx[2][1];

            wrk[37][ie]= dFdx[1][2];
            wrk[39][ie]= dFdx[0][2];
            wrk[40][ie]= dFdx[2][2];
     
            wrk[43][ie]= dFdx[1][3];
            wrk[45][ie]= dFdx[0][3];
            wrk[46][ie]= dFdx[2][3];

//////////////////////////////////////////////////////////////////////////////
             
            wrk[50][ie]= dFdx[2][0];
            wrk[52][ie]= dFdx[1][0];
            wrk[53][ie]= dFdx[0][0];
            
            wrk[56][ie]= dFdx[2][1];
            wrk[58][ie]= dFdx[1][1];
            wrk[59][ie]= dFdx[0][1];

            wrk[62][ie]= dFdx[2][2];
            wrk[64][ie]= dFdx[1][2];
            wrk[65][ie]= dFdx[0][2];
 
            wrk[68][ie]= dFdx[2][3];
            wrk[70][ie]= dFdx[1][3];
            wrk[71][ie]= dFdx[0][3];

            wrk[72][ie]= volme;

        }
     }

      for( jq=0;jq<12;jq++ )
     {
         sld->stress3( ies, iee, iem, wrk+6*jq, wrk+73 );

         for( ie=ies;ie<iee;ie++ )
        {
            
            lhs[ 0+12*jq][ie]= ( wrk[ 0][ie]*wrk[73][ie]+ wrk[ 1][ie]*wrk[74][ie]+ wrk[ 2][ie]*wrk[75][ie]+ wrk[ 3][ie]*wrk[76][ie]+ wrk[ 4][ie]*wrk[77][ie]+ wrk[ 5][ie]*wrk[78][ie] )*wrk[72][ie];
            lhs[ 1+12*jq][ie]= ( wrk[ 6][ie]*wrk[73][ie]+ wrk[ 7][ie]*wrk[74][ie]+ wrk[ 8][ie]*wrk[75][ie]+ wrk[ 9][ie]*wrk[76][ie]+ wrk[10][ie]*wrk[77][ie]+ wrk[11][ie]*wrk[78][ie] )*wrk[72][ie];
            lhs[ 2+12*jq][ie]= ( wrk[12][ie]*wrk[73][ie]+ wrk[13][ie]*wrk[74][ie]+ wrk[14][ie]*wrk[75][ie]+ wrk[15][ie]*wrk[76][ie]+ wrk[16][ie]*wrk[77][ie]+ wrk[17][ie]*wrk[78][ie] )*wrk[72][ie];
            lhs[ 3+12*jq][ie]= ( wrk[18][ie]*wrk[73][ie]+ wrk[19][ie]*wrk[74][ie]+ wrk[20][ie]*wrk[75][ie]+ wrk[21][ie]*wrk[76][ie]+ wrk[22][ie]*wrk[77][ie]+ wrk[23][ie]*wrk[78][ie] )*wrk[72][ie];
            lhs[ 4+12*jq][ie]= ( wrk[24][ie]*wrk[73][ie]+ wrk[25][ie]*wrk[74][ie]+ wrk[26][ie]*wrk[75][ie]+ wrk[27][ie]*wrk[76][ie]+ wrk[28][ie]*wrk[77][ie]+ wrk[29][ie]*wrk[78][ie] )*wrk[72][ie];
            lhs[ 5+12*jq][ie]= ( wrk[30][ie]*wrk[73][ie]+ wrk[31][ie]*wrk[74][ie]+ wrk[32][ie]*wrk[75][ie]+ wrk[33][ie]*wrk[76][ie]+ wrk[34][ie]*wrk[77][ie]+ wrk[35][ie]*wrk[78][ie] )*wrk[72][ie];
            lhs[ 6+12*jq][ie]= ( wrk[36][ie]*wrk[73][ie]+ wrk[37][ie]*wrk[74][ie]+ wrk[38][ie]*wrk[75][ie]+ wrk[39][ie]*wrk[76][ie]+ wrk[40][ie]*wrk[77][ie]+ wrk[41][ie]*wrk[78][ie] )*wrk[72][ie];
            lhs[ 7+12*jq][ie]= ( wrk[42][ie]*wrk[73][ie]+ wrk[43][ie]*wrk[74][ie]+ wrk[44][ie]*wrk[75][ie]+ wrk[45][ie]*wrk[76][ie]+ wrk[46][ie]*wrk[77][ie]+ wrk[47][ie]*wrk[78][ie] )*wrk[72][ie];
            lhs[ 8+12*jq][ie]= ( wrk[48][ie]*wrk[73][ie]+ wrk[49][ie]*wrk[74][ie]+ wrk[50][ie]*wrk[75][ie]+ wrk[51][ie]*wrk[76][ie]+ wrk[52][ie]*wrk[77][ie]+ wrk[53][ie]*wrk[78][ie] )*wrk[72][ie];
            lhs[ 9+12*jq][ie]= ( wrk[54][ie]*wrk[73][ie]+ wrk[55][ie]*wrk[74][ie]+ wrk[56][ie]*wrk[75][ie]+ wrk[57][ie]*wrk[76][ie]+ wrk[58][ie]*wrk[77][ie]+ wrk[59][ie]*wrk[78][ie] )*wrk[72][ie];
            lhs[10+12*jq][ie]= ( wrk[60][ie]*wrk[73][ie]+ wrk[61][ie]*wrk[74][ie]+ wrk[62][ie]*wrk[75][ie]+ wrk[63][ie]*wrk[76][ie]+ wrk[64][ie]*wrk[77][ie]+ wrk[65][ie]*wrk[78][ie] )*wrk[72][ie];
            lhs[11+12*jq][ie]= ( wrk[66][ie]*wrk[73][ie]+ wrk[67][ie]*wrk[74][ie]+ wrk[68][ie]*wrk[75][ie]+ wrk[69][ie]*wrk[76][ie]+ wrk[70][ie]*wrk[77][ie]+ wrk[71][ie]*wrk[78][ie] )*wrk[72][ie];
        }
     }
      
      for( ie=ies;ie<iee;ie++ )
     {
         j0= ieq[0][ie];
         j1= ieq[1][ie];
         j2= ieq[2][ie]; 
         j3= ieq[3][ie];

         lhsd[0][j0]+= lhs[  0][ie];
         lhsd[1][j0]+= lhs[  4][ie];
         lhsd[2][j0]+= lhs[  8][ie];
         lhsd[3][j0]+= lhs[ 48][ie];
         lhsd[4][j0]+= lhs[ 52][ie];
         lhsd[5][j0]+= lhs[ 56][ie];
         lhsd[6][j0]+= lhs[ 96][ie];
         lhsd[7][j0]+= lhs[100][ie];
         lhsd[8][j0]+= lhs[104][ie];

         lhsd[0][j1]+= lhs[ 13][ie];
         lhsd[1][j1]+= lhs[ 17][ie];
         lhsd[2][j1]+= lhs[ 21][ie];
         lhsd[3][j1]+= lhs[ 61][ie];
         lhsd[4][j1]+= lhs[ 65][ie];
         lhsd[5][j1]+= lhs[ 69][ie];
         lhsd[6][j1]+= lhs[109][ie];
         lhsd[7][j1]+= lhs[113][ie];
         lhsd[8][j1]+= lhs[117][ie];

         lhsd[0][j2]+= lhs[ 26][ie];
         lhsd[1][j2]+= lhs[ 30][ie];
         lhsd[2][j2]+= lhs[ 34][ie];
         lhsd[3][j2]+= lhs[ 74][ie];
         lhsd[4][j2]+= lhs[ 78][ie];
         lhsd[5][j2]+= lhs[ 82][ie];
         lhsd[6][j2]+= lhs[122][ie];
         lhsd[7][j2]+= lhs[126][ie];
         lhsd[8][j2]+= lhs[130][ie];

         lhsd[0][j3]+= lhs[ 39][ie];
         lhsd[1][j3]+= lhs[ 43][ie];
         lhsd[2][j3]+= lhs[ 47][ie];
         lhsd[3][j3]+= lhs[ 87][ie];
         lhsd[4][j3]+= lhs[ 91][ie];
         lhsd[5][j3]+= lhs[ 95][ie];
         lhsd[6][j3]+= lhs[135][ie];
         lhsd[7][j3]+= lhs[139][ie];
         lhsd[8][j3]+= lhs[143][ie];

     }
  }

/* void ct43::gtrhs( Int ies, Int iee, Real *xp[], Real *q[], Int *iep[], Int *ier[], Real *lhs[], Real *aux[], Real *wrk[], Real *rhs[], cCosystem *coo )
  {
      Int            ie,i0,i1,i2,i3,j0,j1,j2,j3;
      Real           u[12],r[12];
      
      if( iee > ies )
     {
         for( ie=ies;ie<iee;ie++ ) 
        {

            i0= iep[0][ie];
            i1= iep[1][ie];
            i2= iep[2][ie];
            i3= iep[3][ie];

            j0= ier[0][ie];
            j1= ier[1][ie];
            j2= ier[2][ie];
            j3= ier[3][ie];

            u[0]= q[0][i0];
            u[1]= q[0][i1];
            u[2]= q[0][i2];
            u[3]= q[0][i3]; 
         
            u[4]= q[1][i0];
            u[5]= q[1][i1];
            u[6]= q[1][i2];
            u[7]= q[1][i3];
                    
            u[8]= q[2][i0];
            u[9]= q[2][i1];
            u[10]= q[2][i2];
            u[11]= q[2][i3];


            r[ 0]= lhs[  0][ie]*u[0]+ lhs[  1][ie]*u[1]+ lhs[  2][ie]*u[2]+ lhs[  3][ie]*u[3]+ lhs[  4][ie]*u[4]+ lhs[  5][ie]*u[5]+ lhs[  6][ie]*u[6]+ lhs[  7][ie]*u[7]+ lhs[  8][ie]*u[8]+ lhs[  9][ie]*u[9]+ lhs[ 10][ie]*u[10]+ lhs[ 11][ie]*u[11];
            r[ 1]= lhs[ 12][ie]*u[0]+ lhs[ 13][ie]*u[1]+ lhs[ 14][ie]*u[2]+ lhs[ 15][ie]*u[3]+ lhs[ 16][ie]*u[4]+ lhs[ 17][ie]*u[5]+ lhs[ 18][ie]*u[6]+ lhs[ 19][ie]*u[7]+ lhs[ 20][ie]*u[8]+ lhs[ 21][ie]*u[9]+ lhs[ 22][ie]*u[10]+ lhs[ 23][ie]*u[11];
            r[ 2]= lhs[ 24][ie]*u[0]+ lhs[ 25][ie]*u[1]+ lhs[ 26][ie]*u[2]+ lhs[ 27][ie]*u[3]+ lhs[ 28][ie]*u[4]+ lhs[ 29][ie]*u[5]+ lhs[ 30][ie]*u[6]+ lhs[ 31][ie]*u[7]+ lhs[ 32][ie]*u[8]+ lhs[ 33][ie]*u[9]+ lhs[ 34][ie]*u[10]+ lhs[ 35][ie]*u[11];
            r[ 3]= lhs[ 36][ie]*u[0]+ lhs[ 37][ie]*u[1]+ lhs[ 38][ie]*u[2]+ lhs[ 39][ie]*u[3]+ lhs[ 40][ie]*u[4]+ lhs[ 41][ie]*u[5]+ lhs[ 42][ie]*u[6]+ lhs[ 43][ie]*u[7]+ lhs[ 44][ie]*u[8]+ lhs[ 45][ie]*u[9]+ lhs[ 46][ie]*u[10]+ lhs[ 47][ie]*u[11];

            r[ 4]= lhs[ 48][ie]*u[0]+ lhs[ 49][ie]*u[1]+ lhs[ 50][ie]*u[2]+ lhs[ 51][ie]*u[3]+ lhs[ 52][ie]*u[4]+ lhs[ 53][ie]*u[5]+ lhs[ 54][ie]*u[6]+ lhs[ 55][ie]*u[7]+ lhs[ 56][ie]*u[8]+ lhs[ 57][ie]*u[9]+ lhs[ 58][ie]*u[10]+ lhs[ 59][ie]*u[11];
            r[ 5]= lhs[ 60][ie]*u[0]+ lhs[ 61][ie]*u[1]+ lhs[ 62][ie]*u[2]+ lhs[ 63][ie]*u[3]+ lhs[ 64][ie]*u[4]+ lhs[ 65][ie]*u[5]+ lhs[ 66][ie]*u[6]+ lhs[ 67][ie]*u[7]+ lhs[ 68][ie]*u[8]+ lhs[ 69][ie]*u[9]+ lhs[ 70][ie]*u[10]+ lhs[ 71][ie]*u[11];
            r[ 6]= lhs[ 72][ie]*u[0]+ lhs[ 73][ie]*u[1]+ lhs[ 74][ie]*u[2]+ lhs[ 75][ie]*u[3]+ lhs[ 76][ie]*u[4]+ lhs[ 77][ie]*u[5]+ lhs[ 78][ie]*u[6]+ lhs[ 79][ie]*u[7]+ lhs[ 80][ie]*u[8]+ lhs[ 81][ie]*u[9]+ lhs[ 82][ie]*u[10]+ lhs[ 83][ie]*u[11]; 
            r[ 7]= lhs[ 84][ie]*u[0]+ lhs[ 85][ie]*u[1]+ lhs[ 86][ie]*u[2]+ lhs[ 87][ie]*u[3]+ lhs[ 88][ie]*u[4]+ lhs[ 89][ie]*u[5]+ lhs[ 90][ie]*u[6]+ lhs[ 91][ie]*u[7]+ lhs[ 92][ie]*u[8]+ lhs[ 93][ie]*u[9]+ lhs[ 94][ie]*u[10]+ lhs[ 95][ie]*u[11];

            r[ 8]= lhs[ 96][ie]*u[0]+ lhs[ 97][ie]*u[1]+ lhs[ 98][ie]*u[2]+ lhs[ 99][ie]*u[3]+ lhs[100][ie]*u[4]+ lhs[101][ie]*u[5]+ lhs[102][ie]*u[6]+ lhs[103][ie]*u[7]+ lhs[104][ie]*u[8]+ lhs[105][ie]*u[9]+ lhs[106][ie]*u[10]+ lhs[107][ie]*u[11];
            r[ 9]= lhs[108][ie]*u[0]+ lhs[109][ie]*u[1]+ lhs[110][ie]*u[2]+ lhs[111][ie]*u[3]+ lhs[112][ie]*u[4]+ lhs[113][ie]*u[5]+ lhs[114][ie]*u[6]+ lhs[115][ie]*u[7]+ lhs[116][ie]*u[8]+ lhs[117][ie]*u[9]+ lhs[118][ie]*u[10]+ lhs[119][ie]*u[11];
            r[10]= lhs[120][ie]*u[0]+ lhs[121][ie]*u[1]+ lhs[122][ie]*u[2]+ lhs[123][ie]*u[3]+ lhs[124][ie]*u[4]+ lhs[125][ie]*u[5]+ lhs[126][ie]*u[6]+ lhs[127][ie]*u[7]+ lhs[128][ie]*u[8]+ lhs[129][ie]*u[9]+ lhs[130][ie]*u[10]+ lhs[131][ie]*u[11]; 
            r[11]= lhs[132][ie]*u[0]+ lhs[133][ie]*u[1]+ lhs[134][ie]*u[2]+ lhs[135][ie]*u[3]+ lhs[136][ie]*u[4]+ lhs[137][ie]*u[5]+ lhs[138][ie]*u[6]+ lhs[139][ie]*u[7]+ lhs[140][ie]*u[8]+ lhs[141][ie]*u[9]+ lhs[142][ie]*u[10]+ lhs[143][ie]*u[11];

            ////cout << "ele= " << ie << " r[0]= " << r[0] << " r[1]= " << r[1] << " r[2]= " << r[2] << " r[3]= " << r[3] << " r[4]= " << r[4] << " r[5]= " << r[5] << " r[6]= " << r[6] << " r[7]= " << r[7] << " r[8]= " << r[8] << " r[9]= " << r[9] << " r[10]= " << r[10] << " r[11]= " << r[11] << "\n";

            rhs[0][j0]+= r[0];
            rhs[0][j1]+= r[1];
            rhs[0][j2]+= r[2];
            rhs[0][j3]+= r[3]; 

            rhs[1][j0]+= r[4];
            rhs[1][j1]+= r[5];
            rhs[1][j2]+= r[6];
            rhs[1][j3]+= r[7];

            rhs[2][j0]+= r[8];
            rhs[2][j1]+= r[9];
            rhs[2][j2]+= r[10];
            rhs[2][j3]+= r[11];

        }
     }
  }

   void ct43::gtdrhs( Int ies, Int iee, Real *xp[], Real *q[], Real *dq[], Int *iep[], Int *ier[], Real *lhs[], Real *aux[], Real *wrk[], Real *drhs[], cCosystem *coo )
  {
      gtrhs( ies,iee, xp,dq, iep,ier, lhs,aux,wrk,drhs, coo );
  }
   */
