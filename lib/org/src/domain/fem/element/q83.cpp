   using namespace std;


#  include <domain/fem/element/element.h>

   cq83::cq83()
  {
      nst=6;
      nsp=8;
      nsq=8;
      naux= 2;
      nlhs= 0;
      nwrk= 0;
      nbdv= 0;
      ng=8;
      yg[0]= new Real[ng];
      yg[1]= new Real[ng];
      yg[2]= new Real[ng];
      wg=    new Real[ng];
      yg[0][0]= 0.211324865405e0; yg[1][0]= 0.211324865405e0; yg[2][0]= 0.211324865405e0; wg[0]= 0.125;
      yg[0][1]= 0.788675134595e0; yg[1][1]= 0.211324865405e0; yg[2][1]= 0.211324865405e0; wg[1]= 0.125;
      yg[0][2]= 0.788675134595e0; yg[1][2]= 0.788675134595e0; yg[2][2]= 0.211324865405e0; wg[2]= 0.125;
      yg[0][3]= 0.211324865405e0; yg[1][3]= 0.788675134595e0; yg[2][3]= 0.211324865405e0; wg[3]= 0.125;
      yg[0][4]= 0.211324865405e0; yg[1][4]= 0.211324865405e0; yg[2][4]= 0.788675134595e0; wg[4]= 0.125;
      yg[0][5]= 0.788675134595e0; yg[1][5]= 0.211324865405e0; yg[2][5]= 0.788675134595e0; wg[5]= 0.125;
      yg[0][6]= 0.788675134595e0; yg[1][6]= 0.788675134595e0; yg[2][6]= 0.788675134595e0; wg[6]= 0.125;
      yg[0][7]= 0.211324865405e0; yg[1][7]= 0.788675134595e0; yg[2][7]= 0.788675134595e0; wg[7]= 0.125;

  }


   cq83::~cq83()
  {
  }

   void cq83::shpf( Real *l, Real *g, Real *dgdl[] )
  {

         g[0]=      (1-l[0])*(1-l[1])*(1-l[2]);
         g[1]=         l[0] *(1-l[1])*(1-l[2]);
         g[2]=         l[0] *   l[1] *(1-l[2]);
         g[3]=      (1-l[0])*   l[1] *(1-l[2]);
         g[4]=      (1-l[0])*(1-l[1])*   l[2];
         g[5]=         l[0] *(1-l[1])*   l[2];
         g[6]=         l[0] *   l[1] *   l[2];
         g[7]=      (1-l[0])*   l[1] *   l[2];

      dgdl[0][0]=           -(1-l[1])*(1-l[2]);
      dgdl[1][0]=  -(1-l[0])*         (1-l[2]);
      dgdl[2][0]=  -(1-l[0])*(1-l[1]);
                
      dgdl[0][1]=            (1-l[1])*(1-l[2]);
      dgdl[1][1]=     -l[0] *         (1-l[2]);
      dgdl[2][1]=     -l[0] *(1-l[1]);
                
      dgdl[0][2]=               l[1] *(1-l[2]);
      dgdl[1][2]=      l[0] *         (1-l[2]);
      dgdl[2][2]=     -l[0] *   l[1] ;
                
      dgdl[0][3]=              -l[1] *(1-l[2]);
      dgdl[1][3]=   (1-l[0])*         (1-l[2]);
      dgdl[2][3]=  -(1-l[0])*   l[1] ;
                
      dgdl[0][4]=           -(1-l[1])*   l[2];
      dgdl[1][4]=  -(1-l[0])*            l[2];
      dgdl[2][4]=   (1-l[0])*(1-l[1]);
                
      dgdl[0][5]=            (1-l[1])*   l[2];
      dgdl[1][5]=     -l[0]*             l[2];
      dgdl[2][5]=      l[0] *(1-l[1]);
                
      dgdl[0][6]=               l[1] *   l[2];
      dgdl[1][6]=      l[0] *            l[2];
      dgdl[2][6]=      l[0] *   l[1] ;
                
      dgdl[0][7]=              -l[1] *   l[2];
      dgdl[1][7]=   (1-l[0])*            l[2];
      dgdl[2][7]=   (1-l[0])*   l[1];

  }

   void cq83::shpx( Real *l, Real *g, Real *dgdl[] )
  {
      shpx( l, g, dgdl );
  }


   void cq83::gtlhs( Int ies, Int iee, Int *iem, Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld )
  {
      Int            ie,ip,ig,jo,jp,jq,kp,i0,i1,i2,i3,i4,i5,i6,i7,j0,j1,j2,j3,j4,j5,j6,j7;      
      
      Real            x[24]={0.0};      
      Real            dxdl[3][3]={0.0};
      Real            det;
      Real            dldx[3][3]={0.0};
      
      Real            dFdx[3][8]={0.0};
      Real lg[3]= {0.0};

      Real g[8];
      Real sdgdl[24]={0.0};
      Real *dgdl[3];
      subv(3, 8, sdgdl, dgdl);     
      
      cout << "q83::gtlhs quarantined\n";

      if( iee > ies )
     {
         setv( ies,iee, nlhs,ZERO, lhs );

         cout << "hexa materials\n";
         for( ie=ies;ie<iee;ie++ )
        {
            cout << ie <<" "<<iem[ie]<<"\n";
        }

         for(ig=0; ig<ng; ig++)
        {
            setv( ies,iee, nwrk,ZERO, wrk );

            lg[0]= yg[0][ig];
            lg[1]= yg[1][ig];
            lg[2]= yg[2][ig];

            shpf( lg, g, dgdl);
             
            for( ie=ies;ie<iee;ie++ ) 
           {

               i0= iep[0][ie];
               i1= iep[1][ie];
               i2= iep[2][ie];
               i3= iep[3][ie];
               i4= iep[4][ie];
               i5= iep[5][ie];
               i6= iep[6][ie];
               i7= iep[7][ie]; 
               
               x[ 0]= xp[0][i0];
               x[ 1]= xp[0][i1];
               x[ 2]= xp[0][i2];
               x[ 3]= xp[0][i3];
               x[ 4]= xp[0][i4];
               x[ 5]= xp[0][i5];
               x[ 6]= xp[0][i6];
               x[ 7]= xp[0][i7];

               x[ 8]= xp[1][i0];
               x[ 9]= xp[1][i1];
               x[10]= xp[1][i2];
               x[11]= xp[1][i3];
               x[12]= xp[1][i4];
               x[13]= xp[1][i5];
               x[14]= xp[1][i6];
               x[15]= xp[1][i7];

               x[16]= xp[2][i0];
               x[17]= xp[2][i1];
               x[18]= xp[2][i2];
               x[19]= xp[2][i3]; 
               x[20]= xp[2][i4];
               x[21]= xp[2][i5];
               x[22]= xp[2][i6];
               x[23]= xp[2][i7];
                        
               dxdl[0][0]= dgdl[0][0]*x[ 0]+ dgdl[0][1]*x[ 1]+ dgdl[0][2]*x[ 2]+ dgdl[0][3]*x[ 3]+ dgdl[0][4]*x[ 4]+ dgdl[0][5]*x[ 5]+ dgdl[0][6]*x[ 6]+ dgdl[0][7]*x[ 7];
               dxdl[0][1]= dgdl[0][0]*x[ 8]+ dgdl[0][1]*x[ 9]+ dgdl[0][2]*x[10]+ dgdl[0][3]*x[11]+ dgdl[0][4]*x[12]+ dgdl[0][5]*x[13]+ dgdl[0][6]*x[14]+ dgdl[0][7]*x[15];
               dxdl[0][2]= dgdl[0][0]*x[16]+ dgdl[0][1]*x[17]+ dgdl[0][2]*x[18]+ dgdl[0][3]*x[19]+ dgdl[0][4]*x[20]+ dgdl[0][5]*x[21]+ dgdl[0][6]*x[22]+ dgdl[0][7]*x[23];  

               dxdl[1][0]= dgdl[1][0]*x[ 0]+ dgdl[1][1]*x[ 1]+ dgdl[1][2]*x[ 2]+ dgdl[1][3]*x[ 3]+ dgdl[1][4]*x[ 4]+ dgdl[1][5]*x[ 5]+ dgdl[1][6]*x[ 6]+ dgdl[1][7]*x[ 7];
               dxdl[1][1]= dgdl[1][0]*x[ 8]+ dgdl[1][1]*x[ 9]+ dgdl[1][2]*x[10]+ dgdl[1][3]*x[11]+ dgdl[1][4]*x[12]+ dgdl[1][5]*x[13]+ dgdl[1][6]*x[14]+ dgdl[1][7]*x[15];
               dxdl[1][2]= dgdl[1][0]*x[16]+ dgdl[1][1]*x[17]+ dgdl[1][2]*x[18]+ dgdl[1][3]*x[19]+ dgdl[1][4]*x[20]+ dgdl[1][5]*x[21]+ dgdl[1][6]*x[22]+ dgdl[1][7]*x[23];

               dxdl[2][0]= dgdl[2][0]*x[ 0]+ dgdl[2][1]*x[ 1]+ dgdl[2][2]*x[ 2]+ dgdl[2][3]*x[ 3]+ dgdl[2][4]*x[ 4]+ dgdl[2][5]*x[ 5]+ dgdl[2][6]*x[ 6]+ dgdl[2][7]*x[ 7];               
               dxdl[2][1]= dgdl[2][0]*x[ 8]+ dgdl[2][1]*x[ 9]+ dgdl[2][2]*x[10]+ dgdl[2][3]*x[11]+ dgdl[2][4]*x[12]+ dgdl[2][5]*x[13]+ dgdl[2][6]*x[14]+ dgdl[2][7]*x[15];
               dxdl[2][2]= dgdl[2][0]*x[16]+ dgdl[2][1]*x[17]+ dgdl[2][2]*x[18]+ dgdl[2][3]*x[19]+ dgdl[2][4]*x[20]+ dgdl[2][5]*x[21]+ dgdl[2][6]*x[22]+ dgdl[2][7]*x[23];

               dldx[0][0]= dxdl[1][1]* dxdl[2][2]-  dxdl[1][2]* dxdl[2][1];
               dldx[0][1]= dxdl[1][2]* dxdl[2][0]-  dxdl[1][0]* dxdl[2][2];
               dldx[0][2]= dxdl[1][0]* dxdl[2][1]-  dxdl[1][1]* dxdl[2][0];

               dldx[1][0]= dxdl[2][1]* dxdl[0][2]-  dxdl[2][2]* dxdl[0][1];
               dldx[1][1]= dxdl[2][2]* dxdl[0][0]-  dxdl[2][0]* dxdl[0][2];
               dldx[1][2]= dxdl[2][0]* dxdl[0][1]-  dxdl[2][1]* dxdl[0][0];

               dldx[2][0]= dxdl[0][1]* dxdl[1][2]-  dxdl[0][2]* dxdl[1][1];
               dldx[2][1]= dxdl[0][2]* dxdl[1][0]-  dxdl[0][0]* dxdl[1][2];
               dldx[2][2]= dxdl[0][0]* dxdl[1][1]-  dxdl[0][1]* dxdl[1][0];

               det= dldx[0][0]* dxdl[0][0]+
                    dldx[0][1]* dxdl[0][1]+
                    dldx[0][2]* dxdl[0][2];
           
// this is the transpose!
               dldx[0][0]= dldx[0][0]/det;
               dldx[0][1]= dldx[0][1]/det;
               dldx[0][2]= dldx[0][2]/det;

               dldx[1][0]= dldx[1][0]/det;
               dldx[1][1]= dldx[1][1]/det;
               dldx[1][2]= dldx[1][2]/det;

               dldx[2][0]= dldx[2][0]/det;
               dldx[2][1]= dldx[2][1]/det;
               dldx[2][2]= dldx[2][2]/det;

               dFdx[0][0]= dldx[0][0]*dgdl[0][0]+ dldx[1][0]*dgdl[1][0]+ dldx[2][0]*dgdl[2][0];
               dFdx[0][1]= dldx[0][0]*dgdl[0][1]+ dldx[1][0]*dgdl[1][1]+ dldx[2][0]*dgdl[2][1];    
               dFdx[0][2]= dldx[0][0]*dgdl[0][2]+ dldx[1][0]*dgdl[1][2]+ dldx[2][0]*dgdl[2][2];
               dFdx[0][3]= dldx[0][0]*dgdl[0][3]+ dldx[1][0]*dgdl[1][3]+ dldx[2][0]*dgdl[2][3];
               dFdx[0][4]= dldx[0][0]*dgdl[0][4]+ dldx[1][0]*dgdl[1][4]+ dldx[2][0]*dgdl[2][4];
               dFdx[0][5]= dldx[0][0]*dgdl[0][5]+ dldx[1][0]*dgdl[1][5]+ dldx[2][0]*dgdl[2][5]; 
               dFdx[0][6]= dldx[0][0]*dgdl[0][6]+ dldx[1][0]*dgdl[1][6]+ dldx[2][0]*dgdl[2][6];
               dFdx[0][7]= dldx[0][0]*dgdl[0][7]+ dldx[1][0]*dgdl[1][7]+ dldx[2][0]*dgdl[2][7];
            
               dFdx[1][0]= dldx[0][1]*dgdl[0][0]+ dldx[1][1]*dgdl[1][0]+ dldx[2][1]*dgdl[2][0];
               dFdx[1][1]= dldx[0][1]*dgdl[0][1]+ dldx[1][1]*dgdl[1][1]+ dldx[2][1]*dgdl[2][1];    
               dFdx[1][2]= dldx[0][1]*dgdl[0][2]+ dldx[1][1]*dgdl[1][2]+ dldx[2][1]*dgdl[2][2];
               dFdx[1][3]= dldx[0][1]*dgdl[0][3]+ dldx[1][1]*dgdl[1][3]+ dldx[2][1]*dgdl[2][3];
               dFdx[1][4]= dldx[0][1]*dgdl[0][4]+ dldx[1][1]*dgdl[1][4]+ dldx[2][1]*dgdl[2][4];
               dFdx[1][5]= dldx[0][1]*dgdl[0][5]+ dldx[1][1]*dgdl[1][5]+ dldx[2][1]*dgdl[2][5];
               dFdx[1][6]= dldx[0][1]*dgdl[0][6]+ dldx[1][1]*dgdl[1][6]+ dldx[2][1]*dgdl[2][6];
               dFdx[1][7]= dldx[0][1]*dgdl[0][7]+ dldx[1][1]*dgdl[1][7]+ dldx[2][1]*dgdl[2][7];             

               dFdx[2][0]= dldx[0][2]*dgdl[0][0]+ dldx[1][2]*dgdl[1][0]+ dldx[2][2]*dgdl[2][0];
               dFdx[2][1]= dldx[0][2]*dgdl[0][1]+ dldx[1][2]*dgdl[1][1]+ dldx[2][2]*dgdl[2][1];    
               dFdx[2][2]= dldx[0][2]*dgdl[0][2]+ dldx[1][2]*dgdl[1][2]+ dldx[2][2]*dgdl[2][2];
               dFdx[2][3]= dldx[0][2]*dgdl[0][3]+ dldx[1][2]*dgdl[1][3]+ dldx[2][2]*dgdl[2][3]; 
               dFdx[2][4]= dldx[0][2]*dgdl[0][4]+ dldx[1][2]*dgdl[1][4]+ dldx[2][2]*dgdl[2][4];
               dFdx[2][5]= dldx[0][2]*dgdl[0][5]+ dldx[1][2]*dgdl[1][5]+ dldx[2][2]*dgdl[2][5];  
               dFdx[2][6]= dldx[0][2]*dgdl[0][6]+ dldx[1][2]*dgdl[1][6]+ dldx[2][2]*dgdl[2][6];
               dFdx[2][7]= dldx[0][2]*dgdl[0][7]+ dldx[1][2]*dgdl[1][7]+ dldx[2][2]*dgdl[2][7];

// columns of the b-matrix

// columns 0-7
               wrk[    0][ie]= dFdx[0][0];
               wrk[    3][ie]= dFdx[1][0];
               wrk[    5][ie]= dFdx[2][0];

               wrk[  6+0][ie]= dFdx[0][1];
               wrk[  6+3][ie]= dFdx[1][1];
               wrk[  6+5][ie]= dFdx[2][1];

               wrk[ 12+0][ie]= dFdx[0][2];
               wrk[ 12+3][ie]= dFdx[1][2];
               wrk[ 12+5][ie]= dFdx[2][2];

               wrk[ 18+0][ie]= dFdx[0][3];
               wrk[ 18+3][ie]= dFdx[1][3];
               wrk[ 18+5][ie]= dFdx[2][3];

               wrk[ 24+0][ie]= dFdx[0][4];
               wrk[ 24+3][ie]= dFdx[1][4];
               wrk[ 24+5][ie]= dFdx[2][4];

               wrk[ 30+0][ie]= dFdx[0][5];
               wrk[ 30+3][ie]= dFdx[1][5];
               wrk[ 30+5][ie]= dFdx[2][5];

               wrk[ 36+0][ie]= dFdx[0][6];
               wrk[ 36+3][ie]= dFdx[1][6];
               wrk[ 36+5][ie]= dFdx[2][6];

               wrk[ 42+0][ie]= dFdx[0][7];
               wrk[ 42+3][ie]= dFdx[1][7];
               wrk[ 42+5][ie]= dFdx[2][7];

// columns 8 to 15

               wrk[ 48+1][ie]= dFdx[1][0];
               wrk[ 48+3][ie]= dFdx[0][0];
               wrk[ 48+4][ie]= dFdx[2][0];

               wrk[ 54+1][ie]= dFdx[1][1];
               wrk[ 54+3][ie]= dFdx[0][1];
               wrk[ 54+4][ie]= dFdx[2][1];

               wrk[ 60+1][ie]= dFdx[1][2];
               wrk[ 60+3][ie]= dFdx[0][2];
               wrk[ 60+4][ie]= dFdx[2][2];

               wrk[ 66+1][ie]= dFdx[1][3];
               wrk[ 66+3][ie]= dFdx[0][3];
               wrk[ 66+4][ie]= dFdx[2][3];

               wrk[ 72+1][ie]= dFdx[1][4];
               wrk[ 72+3][ie]= dFdx[0][4];
               wrk[ 72+4][ie]= dFdx[2][4];

               wrk[ 78+1][ie]= dFdx[1][5];
               wrk[ 78+3][ie]= dFdx[0][5];
               wrk[ 78+4][ie]= dFdx[2][5];

               wrk[ 84+1][ie]= dFdx[1][6];
               wrk[ 84+3][ie]= dFdx[0][6];
               wrk[ 84+4][ie]= dFdx[2][6];

               wrk[ 90+1][ie]= dFdx[1][7];
               wrk[ 90+3][ie]= dFdx[0][7];
               wrk[ 90+4][ie]= dFdx[2][7];

               wrk[ 96+2][ie]= dFdx[2][0];
               wrk[ 96+4][ie]= dFdx[1][0];
               wrk[ 96+5][ie]= dFdx[0][0];

               wrk[102+2][ie]= dFdx[2][1];
               wrk[102+4][ie]= dFdx[1][1];
               wrk[102+5][ie]= dFdx[0][1];

               wrk[108+2][ie]= dFdx[2][2];
               wrk[108+4][ie]= dFdx[1][2];
               wrk[108+5][ie]= dFdx[0][2];

               wrk[114+2][ie]= dFdx[2][3];
               wrk[114+4][ie]= dFdx[1][3];
               wrk[114+5][ie]= dFdx[0][3];

               wrk[120+2][ie]= dFdx[2][4];
               wrk[120+4][ie]= dFdx[1][4];
               wrk[120+5][ie]= dFdx[0][4];

               wrk[126+2][ie]= dFdx[2][5];
               wrk[126+4][ie]= dFdx[1][5];
               wrk[126+5][ie]= dFdx[0][5];

               wrk[132+2][ie]= dFdx[2][6];
               wrk[132+4][ie]= dFdx[1][6];
               wrk[132+5][ie]= dFdx[0][6];

               wrk[138+2][ie]= dFdx[2][7];
               wrk[138+4][ie]= dFdx[1][7];
               wrk[138+5][ie]= dFdx[0][7];
               
               wrk[144][ie]= wg[ig]*det;
           }

            for( jq=0;jq<24;jq++ )
           {
               sld->stress3( ies, iee, iem, wrk+6*jq, wrk+145 );
            
               Real cf=1.0; // correction factor
               for( ie=ies;ie<iee;ie++ )
              {
               
                  for(jp=0; jp<24; jp++)
                 {
                     for(jo=0; jo<6; jo++)
                    {
                        lhs[ jp+24*jq][ie]+= ( wrk[  jo+6*jp][ie]*wrk[145+jo][ie] )*wrk[144][ie]*cf;
                    }
                 }                
              }
           }
        }

         for( ie=ies;ie<iee;ie++ )
        {
            j0= ieq[0][ie];
            j1= ieq[1][ie];
            j2= ieq[2][ie]; 
            j3= ieq[3][ie];
            j4= ieq[4][ie]; 
            j5= ieq[5][ie];
            j6= ieq[6][ie]; 
            j7= ieq[7][ie];
   
            for(jq=0; jq<3; jq++)
           {
               
               lhsd[0+3*jq][j0]+= lhs[  0+192*jq][ie];
               lhsd[1+3*jq][j0]+= lhs[  8+192*jq][ie];
               lhsd[2+3*jq][j0]+= lhs[ 16+192*jq][ie];  

               lhsd[0+3*jq][j1]+= lhs[ 25+192*jq][ie];
               lhsd[1+3*jq][j1]+= lhs[ 33+192*jq][ie];
               lhsd[2+3*jq][j1]+= lhs[ 41+192*jq][ie];

               lhsd[0+3*jq][j2]+= lhs[ 50+192*jq][ie];
               lhsd[1+3*jq][j2]+= lhs[ 58+192*jq][ie];
               lhsd[2+3*jq][j2]+= lhs[ 66+192*jq][ie];

               lhsd[0+3*jq][j3]+= lhs[ 75+192*jq][ie];
               lhsd[1+3*jq][j3]+= lhs[ 83+192*jq][ie];
               lhsd[2+3*jq][j3]+= lhs[ 91+192*jq][ie];  

               lhsd[0+3*jq][j4]+= lhs[100+192*jq][ie];
               lhsd[1+3*jq][j4]+= lhs[108+192*jq][ie];
               lhsd[2+3*jq][j4]+= lhs[116+192*jq][ie];

               lhsd[0+3*jq][j5]+= lhs[125+192*jq][ie];
               lhsd[1+3*jq][j5]+= lhs[133+192*jq][ie];
               lhsd[2+3*jq][j5]+= lhs[141+192*jq][ie];
               
               lhsd[0+3*jq][j6]+= lhs[150+192*jq][ie];
               lhsd[1+3*jq][j6]+= lhs[158+192*jq][ie];
               lhsd[2+3*jq][j6]+= lhs[166+192*jq][ie];

               lhsd[0+3*jq][j7]+= lhs[175+192*jq][ie];
               lhsd[1+3*jq][j7]+= lhs[183+192*jq][ie];
               lhsd[2+3*jq][j7]+= lhs[191+192*jq][ie];

           }            
   
        } 
     }


  }

/* void cq83::gtrhs( Int ies, Int iee, Real *xp[], Real *q[], Int *iep[], Int *ier[], Real *lhs[], Real *aux[], Real *wrk[], Real *rhs[], cCosystem *coo )
  {
      Int            ie,i0,i1,i2,i3,i4,i5,i6,i7,j0,j1,j2,j3,j4,j5,j6,j7,jp,jq;
      Real           u[24],r[24];
      
      
      if( iee > ies )
     {
         for( ie=ies;ie<iee;ie++ ) 
        {

            for(jq=0; jq<24; jq++)
           {
               r[jq]= 0.0;
               u[jq]= 0.0; 
           }           

            i0= iep[0][ie];
            i1= iep[1][ie];
            i2= iep[2][ie];
            i3= iep[3][ie];
            i4= iep[4][ie];
            i5= iep[5][ie];
            i6= iep[6][ie];
            i7= iep[7][ie];  

            j0= ier[0][ie];
            j1= ier[1][ie];
            j2= ier[2][ie];
            j3= ier[3][ie];
            j4= ier[4][ie];
            j5= ier[5][ie];
            j6= ier[6][ie];
            j7= ier[7][ie];
            
            u[ 0]= q[0][i0];
            u[ 1]= q[0][i1];
            u[ 2]= q[0][i2];
            u[ 3]= q[0][i3]; 
            u[ 4]= q[0][i4];
            u[ 5]= q[0][i5];
            u[ 6]= q[0][i6];
            u[ 7]= q[0][i7];          

            u[ 8]= q[1][i0];
            u[ 9]= q[1][i1];
            u[10]= q[1][i2];
            u[11]= q[1][i3];
            u[12]= q[1][i4];
            u[13]= q[1][i5];
            u[14]= q[1][i6];
            u[15]= q[1][i7];
        
            u[16]= q[2][i0];
            u[17]= q[2][i1];
            u[18]= q[2][i2];
            u[19]= q[2][i3];
            u[20]= q[2][i4];
            u[21]= q[2][i5];
            u[22]= q[2][i6];
            u[23]= q[2][i7];

            for(jq=0; jq<24; jq++)
           {

               for(jp=0; jp<24; jp++)
              {
                  r[jq]+=  lhs[  jp+24*jq][ie]*u[ jp];
              }
           }            
            
            rhs[0][j0]+= r[ 0];
            rhs[0][j1]+= r[ 1];
            rhs[0][j2]+= r[ 2];
            rhs[0][j3]+= r[ 3]; 
            rhs[0][j4]+= r[ 4];
            rhs[0][j5]+= r[ 5]; 
            rhs[0][j6]+= r[ 6];
            rhs[0][j7]+= r[ 7];

            rhs[1][j0]+= r[ 8];
            rhs[1][j1]+= r[ 9];
            rhs[1][j2]+= r[10];
            rhs[1][j3]+= r[11];
            rhs[1][j4]+= r[12];
            rhs[1][j5]+= r[13];
            rhs[1][j6]+= r[14];
            rhs[1][j7]+= r[15];

            rhs[2][j0]+= r[16];
            rhs[2][j1]+= r[17];
            rhs[2][j2]+= r[18];
            rhs[2][j3]+= r[19];
            rhs[2][j4]+= r[20];
            rhs[2][j5]+= r[21];
            rhs[2][j6]+= r[22];
            rhs[2][j7]+= r[23];
        }
     }
  }

   void cq83::gtdrhs( Int ies, Int iee, Real *xp[], Real *q[], Real *dq[], Int *iep[], Int *ier[], Real *lhs[], Real *aux[], Real *wrk[], Real *drhs[], cCosystem *coo )
  {
      gtrhs( ies,iee, xp,dq, iep,ier, lhs,aux,wrk,drhs, coo );
  }
*/
