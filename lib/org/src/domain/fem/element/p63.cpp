   using namespace std;

#  include <domain/fem/element/element.h>

   cp63::cp63()
  {
      nst=6;
      nsp=6;
      nsq=6;
      naux= 2;
      nlhs= 0;
      nwrk= 0;
      nbdv= 0;
      ng= 6;
      yg[0]= new Real[ng];
      yg[1]= new Real[ng];
      yg[2]= new Real[ng];
      wg   = new Real[ng];
      yg[0][0]= 0.500000000000e0; yg[1][0]= 0.000000000000e0; yg[2][0]= 0.211324865405e0; wg[0]= 0.083333333333e0;
      yg[0][1]= 0.000000000000e0; yg[1][1]= 0.500000000000e0; yg[2][1]= 0.211324865405e0; wg[1]= 0.083333333333e0;
      yg[0][2]= 0.500000000000e0; yg[1][2]= 0.500000000000e0; yg[2][2]= 0.211324865405e0; wg[2]= 0.083333333333e0;
      yg[0][3]= 0.500000000000e0; yg[1][3]= 0.000000000000e0; yg[2][3]= 0.788675134595e0; wg[3]= 0.083333333333e0;
      yg[0][4]= 0.000000000000e0; yg[1][4]= 0.500000000000e0; yg[2][4]= 0.788675134595e0; wg[4]= 0.083333333333e0;
      yg[0][5]= 0.500000000000e0; yg[1][5]= 0.500000000000e0; yg[2][5]= 0.788675134595e0; wg[5]= 0.083333333333e0;

  }


   cp63::~cp63()
  {
  }

   void cp63::shpf( Real *l, Real *g, Real *dgdl[] )
  {

      g[0]=         l[0]* (1-l[2]);
      g[1]=         l[1]* (1-l[2]);
      g[2]= (1-l[0]-l[1])*(1-l[2]);
      g[3]=         l[0]* l[2];
      g[4]=         l[1]* l[2];
      g[5]= (1-l[0]-l[1])*l[2];

      dgdl[0][0]=    (1-l[2]);
      dgdl[1][0]=         0.;
      dgdl[2][0]=      -l[0];

      dgdl[0][1]=         0.;
      dgdl[1][1]=    (1-l[2]);
      dgdl[2][1]=      -l[1];

      dgdl[0][2]=   -(1-l[2]);
      dgdl[1][2]=   -(1-l[2]);
      dgdl[2][2]=   -(1-l[0]-l[1]);

      dgdl[0][3]=       l[2];
      dgdl[1][3]=         0.;
      dgdl[2][3]=       l[0];

      dgdl[0][4]=         0.;
      dgdl[1][4]=       l[2];
      dgdl[2][4]=       l[1];

      dgdl[0][5]=      -l[2];
      dgdl[1][5]=      -l[2];
      dgdl[2][5]=    (1-l[0]-l[1]);

  }

   void cp63::shpx( Real *l, Real *g, Real *dgdl[] )
  {
      shpf( l, g, dgdl );
  }


   void cp63::gtlhs( Int ies, Int iee, Int *iem, Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld )
  {


      Int            ie,ip, ig, jq, i0,i1,i2,i3,i4,i5,j0,j1,j2,j3,j4,j5;
      
      
      Real            x[18]={0.0};
      Real            dxdl[3][3]={0.0};
      Real            det;
      Real            dldx[3][3]={0.0};
      
      Real            dFdx[3][6]={0.0};
      Real            lg[3];
      Real            g[6];
      Real            sdgdl[18];
      Real            *dgdl[3];
      subv(3, 6, sdgdl, dgdl);     

      if( iee > ies )
     {
         setv( ies,iee, nlhs,ZERO, lhs );

         cout << "prism materials\n";
         for( ie=ies;ie<iee;ie++ ) 
        {
            cout << ie <<" "<<iem[ie]<<"\n";
        }

         for(ig=0; ig<ng; ig++)
        {

            setv( ies,iee, nwrk,ZERO, wrk );

            lg[0]= yg[0][ig];
            lg[1]= yg[1][ig];
            lg[2]= yg[2][ig];

            shpf( lg,g,dgdl );
             
            for( ie=ies;ie<iee;ie++ ) 
           {

               i0= iep[0][ie];
               i1= iep[1][ie];
               i2= iep[2][ie];
               i3= iep[3][ie];
               i4= iep[4][ie];
               i5= iep[5][ie];

               x[ 0]= xp[0][i0];
               x[ 1]= xp[0][i1];
               x[ 2]= xp[0][i2];
               x[ 3]= xp[0][i3];
               x[ 4]= xp[0][i4];
               x[ 5]= xp[0][i5];

               x[ 6]= xp[1][i0];
               x[ 7]= xp[1][i1];
               x[ 8]= xp[1][i2];
               x[ 9]= xp[1][i3];
               x[10]= xp[1][i4];
               x[11]= xp[1][i5];

               x[12]= xp[2][i0];
               x[13]= xp[2][i1];
               x[14]= xp[2][i2];
               x[15]= xp[2][i3]; 
               x[16]= xp[2][i4];
               x[17]= xp[2][i5];
                        
               dxdl[0][0]= dgdl[0][0]*x[ 0]+ dgdl[0][1]*x[ 1]+ dgdl[0][2]*x[ 2]+ dgdl[0][3]*x[ 3]+ dgdl[0][4]*x[ 4]+ dgdl[0][5]*x[ 5];
               dxdl[0][1]= dgdl[0][0]*x[ 6]+ dgdl[0][1]*x[ 7]+ dgdl[0][2]*x[ 8]+ dgdl[0][3]*x[ 9]+ dgdl[0][4]*x[10]+ dgdl[0][5]*x[11];
               dxdl[0][2]= dgdl[0][0]*x[12]+ dgdl[0][1]*x[13]+ dgdl[0][2]*x[14]+ dgdl[0][3]*x[15]+ dgdl[0][4]*x[16]+ dgdl[0][5]*x[17];  

               dxdl[1][0]= dgdl[1][0]*x[ 0]+ dgdl[1][1]*x[ 1]+ dgdl[1][2]*x[ 2]+ dgdl[1][3]*x[ 3]+ dgdl[1][4]*x[ 4]+ dgdl[1][5]*x[ 5];
               dxdl[1][1]= dgdl[1][0]*x[ 6]+ dgdl[1][1]*x[ 7]+ dgdl[1][2]*x[ 8]+ dgdl[1][3]*x[ 9]+ dgdl[1][4]*x[10]+ dgdl[1][5]*x[11];
               dxdl[1][2]= dgdl[1][0]*x[12]+ dgdl[1][1]*x[13]+ dgdl[1][2]*x[14]+ dgdl[1][3]*x[15]+ dgdl[1][4]*x[16]+ dgdl[1][5]*x[17]; 

               dxdl[2][0]= dgdl[2][0]*x[ 0]+ dgdl[2][1]*x[ 1]+ dgdl[2][2]*x[ 2]+ dgdl[2][3]*x[ 3]+ dgdl[2][4]*x[ 4]+ dgdl[2][5]*x[ 5];
               dxdl[2][1]= dgdl[2][0]*x[ 6]+ dgdl[2][1]*x[ 7]+ dgdl[2][2]*x[ 8]+ dgdl[2][3]*x[ 9]+ dgdl[2][4]*x[10]+ dgdl[2][5]*x[11];
               dxdl[2][2]= dgdl[2][0]*x[12]+ dgdl[2][1]*x[13]+ dgdl[2][2]*x[14]+ dgdl[2][3]*x[15]+ dgdl[2][4]*x[16]+ dgdl[2][5]*x[17];

               dldx[0][0]= dxdl[1][1]* dxdl[2][2]-  dxdl[1][2]* dxdl[2][1];
               dldx[0][1]= dxdl[1][2]* dxdl[2][0]-  dxdl[1][0]* dxdl[2][2];
               dldx[0][2]= dxdl[1][0]* dxdl[2][1]-  dxdl[1][1]* dxdl[2][0];

               dldx[1][0]= dxdl[2][1]* dxdl[0][2]-  dxdl[2][2]* dxdl[0][1];
               dldx[1][1]= dxdl[2][2]* dxdl[0][0]-  dxdl[2][0]* dxdl[0][2];
               dldx[1][2]= dxdl[2][0]* dxdl[0][1]-  dxdl[2][1]* dxdl[0][0];

               dldx[2][0]= dxdl[0][1]* dxdl[1][2]-  dxdl[0][2]* dxdl[1][1];
               dldx[2][1]= dxdl[0][2]* dxdl[1][0]-  dxdl[0][0]* dxdl[1][2];
               dldx[2][2]= dxdl[0][0]* dxdl[1][1]-  dxdl[0][1]* dxdl[1][0];

               det= dldx[0][0]* dxdl[0][0]+
                    dldx[0][1]* dxdl[0][1]+
                    dldx[0][2]* dxdl[0][2];
           
// this is the transpose!

               dldx[0][0]= dldx[0][0]/det;
               dldx[0][1]= dldx[0][1]/det;
               dldx[0][2]= dldx[0][2]/det;
                              
               dldx[1][0]= dldx[1][0]/det;
               dldx[1][1]= dldx[1][1]/det;
               dldx[1][2]= dldx[1][2]/det;
                              
               dldx[2][0]= dldx[2][0]/det;
               dldx[2][1]= dldx[2][1]/det;
               dldx[2][2]= dldx[2][2]/det;

               dFdx[0][0]= dldx[0][0]*dgdl[0][0]+ dldx[1][0]*dgdl[1][0]+ dldx[2][0]*dgdl[2][0];
               dFdx[0][1]= dldx[0][0]*dgdl[0][1]+ dldx[1][0]*dgdl[1][1]+ dldx[2][0]*dgdl[2][1];    
               dFdx[0][2]= dldx[0][0]*dgdl[0][2]+ dldx[1][0]*dgdl[1][2]+ dldx[2][0]*dgdl[2][2];
               dFdx[0][3]= dldx[0][0]*dgdl[0][3]+ dldx[1][0]*dgdl[1][3]+ dldx[2][0]*dgdl[2][3];
               dFdx[0][4]= dldx[0][0]*dgdl[0][4]+ dldx[1][0]*dgdl[1][4]+ dldx[2][0]*dgdl[2][4];
               dFdx[0][5]= dldx[0][0]*dgdl[0][5]+ dldx[1][0]*dgdl[1][5]+ dldx[2][0]*dgdl[2][5]; 
            
               dFdx[1][0]= dldx[0][1]*dgdl[0][0]+ dldx[1][1]*dgdl[1][0]+ dldx[2][1]*dgdl[2][0];
               dFdx[1][1]= dldx[0][1]*dgdl[0][1]+ dldx[1][1]*dgdl[1][1]+ dldx[2][1]*dgdl[2][1];    
               dFdx[1][2]= dldx[0][1]*dgdl[0][2]+ dldx[1][1]*dgdl[1][2]+ dldx[2][1]*dgdl[2][2];
               dFdx[1][3]= dldx[0][1]*dgdl[0][3]+ dldx[1][1]*dgdl[1][3]+ dldx[2][1]*dgdl[2][3];
               dFdx[1][4]= dldx[0][1]*dgdl[0][4]+ dldx[1][1]*dgdl[1][4]+ dldx[2][1]*dgdl[2][4];
               dFdx[1][5]= dldx[0][1]*dgdl[0][5]+ dldx[1][1]*dgdl[1][5]+ dldx[2][1]*dgdl[2][5];
            
               dFdx[2][0]= dldx[0][2]*dgdl[0][0]+ dldx[1][2]*dgdl[1][0]+ dldx[2][2]*dgdl[2][0];
               dFdx[2][1]= dldx[0][2]*dgdl[0][1]+ dldx[1][2]*dgdl[1][1]+ dldx[2][2]*dgdl[2][1];    
               dFdx[2][2]= dldx[0][2]*dgdl[0][2]+ dldx[1][2]*dgdl[1][2]+ dldx[2][2]*dgdl[2][2];
               dFdx[2][3]= dldx[0][2]*dgdl[0][3]+ dldx[1][2]*dgdl[1][3]+ dldx[2][2]*dgdl[2][3]; 
               dFdx[2][4]= dldx[0][2]*dgdl[0][4]+ dldx[1][2]*dgdl[1][4]+ dldx[2][2]*dgdl[2][4];
               dFdx[2][5]= dldx[0][2]*dgdl[0][5]+ dldx[1][2]*dgdl[1][5]+ dldx[2][2]*dgdl[2][5];  

// columns of the b-matrix
            
               wrk[  0][ie]= dFdx[0][0];
               wrk[  3][ie]= dFdx[1][0];
               wrk[  5][ie]= dFdx[2][0];

               wrk[  6][ie]= dFdx[0][1];
               wrk[  9][ie]= dFdx[1][1];
               wrk[ 11][ie]= dFdx[2][1];

               wrk[ 12][ie]= dFdx[0][2];
               wrk[ 15][ie]= dFdx[1][2];
               wrk[ 17][ie]= dFdx[2][2];
 
               wrk[ 18][ie]= dFdx[0][3];
               wrk[ 21][ie]= dFdx[1][3];
               wrk[ 23][ie]= dFdx[2][3];

               wrk[ 24][ie]= dFdx[0][4];
               wrk[ 27][ie]= dFdx[1][4];
               wrk[ 29][ie]= dFdx[2][4];
 
               wrk[ 30][ie]= dFdx[0][5];
               wrk[ 33][ie]= dFdx[1][5];
               wrk[ 35][ie]= dFdx[2][5];

               wrk[ 37][ie]= dFdx[1][0];
               wrk[ 39][ie]= dFdx[0][0];
               wrk[ 40][ie]= dFdx[2][0];

               wrk[ 43][ie]= dFdx[1][1];
               wrk[ 45][ie]= dFdx[0][1];
               wrk[ 46][ie]= dFdx[2][1];

               wrk[ 49][ie]= dFdx[1][2];
               wrk[ 51][ie]= dFdx[0][2];
               wrk[ 52][ie]= dFdx[2][2];
     
               wrk[ 55][ie]= dFdx[1][3];
               wrk[ 57][ie]= dFdx[0][3];
               wrk[ 58][ie]= dFdx[2][3];

               wrk[ 61][ie]= dFdx[1][4];
               wrk[ 63][ie]= dFdx[0][4];
               wrk[ 64][ie]= dFdx[2][4];
     
               wrk[ 67][ie]= dFdx[1][5];
               wrk[ 69][ie]= dFdx[0][5];
               wrk[ 70][ie]= dFdx[2][5];

               wrk[ 74][ie]= dFdx[2][0];
               wrk[ 76][ie]= dFdx[1][0];
               wrk[ 77][ie]= dFdx[0][0];
            
               wrk[ 80][ie]= dFdx[2][1];
               wrk[ 82][ie]= dFdx[1][1];
               wrk[ 83][ie]= dFdx[0][1];

               wrk[ 86][ie]= dFdx[2][2];
               wrk[ 88][ie]= dFdx[1][2];
               wrk[ 89][ie]= dFdx[0][2];
 
               wrk[ 92][ie]= dFdx[2][3];
               wrk[ 94][ie]= dFdx[1][3];
               wrk[ 95][ie]= dFdx[0][3];
     
               wrk[ 98][ie]= dFdx[2][4];
               wrk[100][ie]= dFdx[1][4];
               wrk[101][ie]= dFdx[0][4];
 
               wrk[104][ie]= dFdx[2][5];
               wrk[106][ie]= dFdx[1][5];
               wrk[107][ie]= dFdx[0][5];

               wrk[108][ie]= wg[ig]*det;
           }

            for( jq=0;jq<18;jq++ )
           {
               sld->stress3( ies, iee, iem, wrk+6*jq, wrk+109 );
               for( ie=ies;ie<iee;ie++ )
              {
               
                  lhs[ 0+18*jq][ie]+= ( wrk[  0][ie]*wrk[109][ie]+ wrk[  1][ie]*wrk[110][ie]+ wrk[  2][ie]*wrk[111][ie]+ wrk[  3][ie]*wrk[112][ie]+ wrk[  4][ie]*wrk[113][ie]+ wrk[  5][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[ 1+18*jq][ie]+= ( wrk[  6][ie]*wrk[109][ie]+ wrk[  7][ie]*wrk[110][ie]+ wrk[  8][ie]*wrk[111][ie]+ wrk[  9][ie]*wrk[112][ie]+ wrk[ 10][ie]*wrk[113][ie]+ wrk[ 11][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[ 2+18*jq][ie]+= ( wrk[ 12][ie]*wrk[109][ie]+ wrk[ 13][ie]*wrk[110][ie]+ wrk[ 14][ie]*wrk[111][ie]+ wrk[ 15][ie]*wrk[112][ie]+ wrk[ 16][ie]*wrk[113][ie]+ wrk[ 17][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[ 3+18*jq][ie]+= ( wrk[ 18][ie]*wrk[109][ie]+ wrk[ 19][ie]*wrk[110][ie]+ wrk[ 20][ie]*wrk[111][ie]+ wrk[ 21][ie]*wrk[112][ie]+ wrk[ 22][ie]*wrk[113][ie]+ wrk[ 23][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[ 4+18*jq][ie]+= ( wrk[ 24][ie]*wrk[109][ie]+ wrk[ 25][ie]*wrk[110][ie]+ wrk[ 26][ie]*wrk[111][ie]+ wrk[ 27][ie]*wrk[112][ie]+ wrk[ 28][ie]*wrk[113][ie]+ wrk[ 29][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[ 5+18*jq][ie]+= ( wrk[ 30][ie]*wrk[109][ie]+ wrk[ 31][ie]*wrk[110][ie]+ wrk[ 32][ie]*wrk[111][ie]+ wrk[ 33][ie]*wrk[112][ie]+ wrk[ 34][ie]*wrk[113][ie]+ wrk[ 35][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[ 6+18*jq][ie]+= ( wrk[ 36][ie]*wrk[109][ie]+ wrk[ 37][ie]*wrk[110][ie]+ wrk[ 38][ie]*wrk[111][ie]+ wrk[ 39][ie]*wrk[112][ie]+ wrk[ 40][ie]*wrk[113][ie]+ wrk[ 41][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[ 7+18*jq][ie]+= ( wrk[ 42][ie]*wrk[109][ie]+ wrk[ 43][ie]*wrk[110][ie]+ wrk[ 44][ie]*wrk[111][ie]+ wrk[ 45][ie]*wrk[112][ie]+ wrk[ 46][ie]*wrk[113][ie]+ wrk[ 47][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[ 8+18*jq][ie]+= ( wrk[ 48][ie]*wrk[109][ie]+ wrk[ 49][ie]*wrk[110][ie]+ wrk[ 50][ie]*wrk[111][ie]+ wrk[ 51][ie]*wrk[112][ie]+ wrk[ 52][ie]*wrk[113][ie]+ wrk[ 53][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[ 9+18*jq][ie]+= ( wrk[ 54][ie]*wrk[109][ie]+ wrk[ 55][ie]*wrk[110][ie]+ wrk[ 56][ie]*wrk[111][ie]+ wrk[ 57][ie]*wrk[112][ie]+ wrk[ 58][ie]*wrk[113][ie]+ wrk[ 59][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[10+18*jq][ie]+= ( wrk[ 60][ie]*wrk[109][ie]+ wrk[ 61][ie]*wrk[110][ie]+ wrk[ 62][ie]*wrk[111][ie]+ wrk[ 63][ie]*wrk[112][ie]+ wrk[ 64][ie]*wrk[113][ie]+ wrk[ 65][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[11+18*jq][ie]+= ( wrk[ 66][ie]*wrk[109][ie]+ wrk[ 67][ie]*wrk[110][ie]+ wrk[ 68][ie]*wrk[111][ie]+ wrk[ 69][ie]*wrk[112][ie]+ wrk[ 70][ie]*wrk[113][ie]+ wrk[ 71][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[12+18*jq][ie]+= ( wrk[ 72][ie]*wrk[109][ie]+ wrk[ 73][ie]*wrk[110][ie]+ wrk[ 74][ie]*wrk[111][ie]+ wrk[ 75][ie]*wrk[112][ie]+ wrk[ 76][ie]*wrk[113][ie]+ wrk[ 77][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[13+18*jq][ie]+= ( wrk[ 78][ie]*wrk[109][ie]+ wrk[ 79][ie]*wrk[110][ie]+ wrk[ 80][ie]*wrk[111][ie]+ wrk[ 81][ie]*wrk[112][ie]+ wrk[ 82][ie]*wrk[113][ie]+ wrk[ 83][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[14+18*jq][ie]+= ( wrk[ 84][ie]*wrk[109][ie]+ wrk[ 85][ie]*wrk[110][ie]+ wrk[ 86][ie]*wrk[111][ie]+ wrk[ 87][ie]*wrk[112][ie]+ wrk[ 88][ie]*wrk[113][ie]+ wrk[ 89][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[15+18*jq][ie]+= ( wrk[ 90][ie]*wrk[109][ie]+ wrk[ 91][ie]*wrk[110][ie]+ wrk[ 92][ie]*wrk[111][ie]+ wrk[ 93][ie]*wrk[112][ie]+ wrk[ 94][ie]*wrk[113][ie]+ wrk[ 95][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[16+18*jq][ie]+= ( wrk[ 96][ie]*wrk[109][ie]+ wrk[ 97][ie]*wrk[110][ie]+ wrk[ 98][ie]*wrk[111][ie]+ wrk[ 99][ie]*wrk[112][ie]+ wrk[100][ie]*wrk[113][ie]+ wrk[101][ie]*wrk[114][ie] )*wrk[108][ie];
                  lhs[17+18*jq][ie]+= ( wrk[102][ie]*wrk[109][ie]+ wrk[103][ie]*wrk[110][ie]+ wrk[104][ie]*wrk[111][ie]+ wrk[105][ie]*wrk[112][ie]+ wrk[106][ie]*wrk[113][ie]+ wrk[107][ie]*wrk[114][ie] )*wrk[108][ie];
                
              }
           }
        }

         for( ie=ies;ie<iee;ie++ )
        {
            j0= ieq[0][ie];
            j1= ieq[1][ie];
            j2= ieq[2][ie]; 
            j3= ieq[3][ie];
            j4= ieq[4][ie]; 
            j5= ieq[5][ie];
   
            lhsd[0][j0]+= lhs[  0][ie];
            lhsd[1][j0]+= lhs[  6][ie];
            lhsd[2][j0]+= lhs[ 12][ie];
            lhsd[3][j0]+= lhs[108][ie];
            lhsd[4][j0]+= lhs[114][ie];
            lhsd[5][j0]+= lhs[120][ie];
            lhsd[6][j0]+= lhs[216][ie];
            lhsd[7][j0]+= lhs[222][ie];
            lhsd[8][j0]+= lhs[228][ie];
   
            lhsd[0][j1]+= lhs[ 19][ie];
            lhsd[1][j1]+= lhs[ 25][ie];
            lhsd[2][j1]+= lhs[ 31][ie];
            lhsd[3][j1]+= lhs[127][ie];
            lhsd[4][j1]+= lhs[133][ie];
            lhsd[5][j1]+= lhs[139][ie];
            lhsd[6][j1]+= lhs[235][ie];
            lhsd[7][j1]+= lhs[241][ie];
            lhsd[8][j1]+= lhs[247][ie];
   
            lhsd[0][j2]+= lhs[ 38][ie];
            lhsd[1][j2]+= lhs[ 44][ie];
            lhsd[2][j2]+= lhs[ 50][ie];
            lhsd[3][j2]+= lhs[146][ie];
            lhsd[4][j2]+= lhs[152][ie];
            lhsd[5][j2]+= lhs[158][ie];
            lhsd[6][j2]+= lhs[254][ie];
            lhsd[7][j2]+= lhs[260][ie];
            lhsd[8][j2]+= lhs[266][ie];
   
            lhsd[0][j3]+= lhs[ 57][ie];
            lhsd[1][j3]+= lhs[ 63][ie];
            lhsd[2][j3]+= lhs[ 69][ie];
            lhsd[3][j3]+= lhs[165][ie];
            lhsd[4][j3]+= lhs[171][ie];
            lhsd[5][j3]+= lhs[177][ie];
            lhsd[6][j3]+= lhs[273][ie];
            lhsd[7][j3]+= lhs[279][ie];
            lhsd[8][j3]+= lhs[285][ie];
   
            lhsd[0][j4]+= lhs[ 76][ie];
            lhsd[1][j4]+= lhs[ 82][ie];
            lhsd[2][j4]+= lhs[ 88][ie];
            lhsd[3][j4]+= lhs[184][ie];
            lhsd[4][j4]+= lhs[190][ie];
            lhsd[5][j4]+= lhs[196][ie];
            lhsd[6][j4]+= lhs[292][ie];
            lhsd[7][j4]+= lhs[298][ie];
            lhsd[8][j4]+= lhs[304][ie];
   
            lhsd[0][j5]+= lhs[ 95][ie];
            lhsd[1][j5]+= lhs[101][ie];
            lhsd[2][j5]+= lhs[107][ie];
            lhsd[3][j5]+= lhs[203][ie];
            lhsd[4][j5]+= lhs[209][ie];
            lhsd[5][j5]+= lhs[215][ie];
            lhsd[6][j5]+= lhs[311][ie];
            lhsd[7][j5]+= lhs[317][ie];
            lhsd[8][j5]+= lhs[323][ie];
   
        } 
     }
  }

/* void cp63::gtrhs( Int ies, Int iee, Real *xp[], Real *q[], Int *iep[], Int *ier[], Real *lhs[], Real *aux[], Real *wrk[], Real *rhs[], cCosystem *coo )
  {
      Int            ie,i0,i1,i2,i3,i4,i5,j0,j1,j2,j3,j4,j5;
      Real           u[18],r[18];
      
      
      if( iee > ies )
     {
         for( ie=ies;ie<iee;ie++ ) 
        {

            i0= iep[0][ie];
            i1= iep[1][ie];
            i2= iep[2][ie];
            i3= iep[3][ie];
            i4= iep[4][ie];
            i5= iep[5][ie];

            j0= ier[0][ie];
            j1= ier[1][ie];
            j2= ier[2][ie];
            j3= ier[3][ie];
            j4= ier[4][ie];
            j5= ier[5][ie];

            u[ 0]= q[0][i0];
            u[ 1]= q[0][i1];
            u[ 2]= q[0][i2];
            u[ 3]= q[0][i3]; 
            u[ 4]= q[0][i4];
            u[ 5]= q[0][i5];
         
            u[ 6]= q[1][i0];
            u[ 7]= q[1][i1];
            u[ 8]= q[1][i2];
            u[ 9]= q[1][i3];
            u[10]= q[1][i4];
            u[11]= q[1][i5];
                    
            u[12]= q[2][i0];
            u[13]= q[2][i1];
            u[14]= q[2][i2];
            u[15]= q[2][i3];
            u[16]= q[2][i4];
            u[17]= q[2][i5];
            
            r[ 0]= lhs[  0][ie]*u[ 0]+ lhs[  1][ie]*u[ 1]+ lhs[  2][ie]*u[ 2]+ lhs[  3][ie]*u[ 3]+ lhs[  4][ie]*u[ 4]+ lhs[  5][ie]*u[ 5]+ 
                   lhs[  6][ie]*u[ 6]+ lhs[  7][ie]*u[ 7]+ lhs[  8][ie]*u[ 8]+ lhs[  9][ie]*u[ 9]+ lhs[ 10][ie]*u[10]+ lhs[ 11][ie]*u[11]+
                   lhs[ 12][ie]*u[12]+ lhs[ 13][ie]*u[13]+ lhs[ 14][ie]*u[14]+ lhs[ 15][ie]*u[15]+ lhs[ 16][ie]*u[16]+ lhs[ 17][ie]*u[17];

            r[ 1]= lhs[ 18][ie]*u[ 0]+ lhs[ 19][ie]*u[ 1]+ lhs[ 20][ie]*u[ 2]+ lhs[ 21][ie]*u[ 3]+ lhs[ 22][ie]*u[ 4]+ lhs[ 23][ie]*u[ 5]+ 
                   lhs[ 24][ie]*u[ 6]+ lhs[ 25][ie]*u[ 7]+ lhs[ 26][ie]*u[ 8]+ lhs[ 27][ie]*u[ 9]+ lhs[ 28][ie]*u[10]+ lhs[ 29][ie]*u[11]+
                   lhs[ 30][ie]*u[12]+ lhs[ 31][ie]*u[13]+ lhs[ 32][ie]*u[14]+ lhs[ 33][ie]*u[15]+ lhs[ 34][ie]*u[16]+ lhs[ 35][ie]*u[17];

            r[ 2]= lhs[ 36][ie]*u[ 0]+ lhs[ 37][ie]*u[ 1]+ lhs[ 38][ie]*u[ 2]+ lhs[ 39][ie]*u[ 3]+ lhs[ 40][ie]*u[ 4]+ lhs[ 41][ie]*u[ 5]+ 
                   lhs[ 42][ie]*u[ 6]+ lhs[ 43][ie]*u[ 7]+ lhs[ 44][ie]*u[ 8]+ lhs[ 45][ie]*u[ 9]+ lhs[ 46][ie]*u[10]+ lhs[ 47][ie]*u[11]+
                   lhs[ 48][ie]*u[12]+ lhs[ 49][ie]*u[13]+ lhs[ 50][ie]*u[14]+ lhs[ 51][ie]*u[15]+ lhs[ 52][ie]*u[16]+ lhs[ 53][ie]*u[17];
            
            r[ 3]= lhs[ 54][ie]*u[ 0]+ lhs[ 55][ie]*u[ 1]+ lhs[ 56][ie]*u[ 2]+ lhs[ 57][ie]*u[ 3]+ lhs[ 58][ie]*u[ 4]+ lhs[ 59][ie]*u[ 5]+ 
                   lhs[ 60][ie]*u[ 6]+ lhs[ 61][ie]*u[ 7]+ lhs[ 62][ie]*u[ 8]+ lhs[ 63][ie]*u[ 9]+ lhs[ 64][ie]*u[10]+ lhs[ 65][ie]*u[11]+
                   lhs[ 66][ie]*u[12]+ lhs[ 67][ie]*u[13]+ lhs[ 68][ie]*u[14]+ lhs[ 69][ie]*u[15]+ lhs[ 70][ie]*u[16]+ lhs[ 71][ie]*u[17];


            r[ 4]= lhs[ 72][ie]*u[ 0]+ lhs[ 73][ie]*u[ 1]+ lhs[ 74][ie]*u[ 2]+ lhs[ 75][ie]*u[ 3]+ lhs[ 76][ie]*u[ 4]+ lhs[ 77][ie]*u[ 5]+ 
                   lhs[ 78][ie]*u[ 6]+ lhs[ 79][ie]*u[ 7]+ lhs[ 80][ie]*u[ 8]+ lhs[ 81][ie]*u[ 9]+ lhs[ 82][ie]*u[10]+ lhs[ 83][ie]*u[11]+
                   lhs[ 84][ie]*u[12]+ lhs[ 85][ie]*u[13]+ lhs[ 86][ie]*u[14]+ lhs[ 87][ie]*u[15]+ lhs[ 88][ie]*u[16]+ lhs[ 89][ie]*u[17];
 
            r[ 5]= lhs[ 90][ie]*u[ 0]+ lhs[ 91][ie]*u[ 1]+ lhs[ 92][ie]*u[ 2]+ lhs[ 93][ie]*u[ 3]+ lhs[ 94][ie]*u[ 4]+ lhs[ 95][ie]*u[ 5]+ 
                   lhs[ 96][ie]*u[ 6]+ lhs[ 97][ie]*u[ 7]+ lhs[ 98][ie]*u[ 8]+ lhs[ 99][ie]*u[ 9]+ lhs[100][ie]*u[10]+ lhs[101][ie]*u[11]+
                   lhs[102][ie]*u[12]+ lhs[103][ie]*u[13]+ lhs[104][ie]*u[14]+ lhs[105][ie]*u[15]+ lhs[106][ie]*u[16]+ lhs[107][ie]*u[17];

            r[ 6]= lhs[108][ie]*u[ 0]+ lhs[109][ie]*u[ 1]+ lhs[110][ie]*u[ 2]+ lhs[111][ie]*u[ 3]+ lhs[112][ie]*u[ 4]+ lhs[113][ie]*u[ 5]+ 
                   lhs[114][ie]*u[ 6]+ lhs[115][ie]*u[ 7]+ lhs[116][ie]*u[ 8]+ lhs[117][ie]*u[ 9]+ lhs[118][ie]*u[10]+ lhs[119][ie]*u[11]+
                   lhs[120][ie]*u[12]+ lhs[121][ie]*u[13]+ lhs[122][ie]*u[14]+ lhs[123][ie]*u[15]+ lhs[124][ie]*u[16]+ lhs[125][ie]*u[17];

            r[ 7]= lhs[126][ie]*u[ 0]+ lhs[127][ie]*u[ 1]+ lhs[128][ie]*u[ 2]+ lhs[129][ie]*u[ 3]+ lhs[130][ie]*u[ 4]+ lhs[131][ie]*u[ 5]+ 
                   lhs[132][ie]*u[ 6]+ lhs[133][ie]*u[ 7]+ lhs[134][ie]*u[ 8]+ lhs[135][ie]*u[ 9]+ lhs[136][ie]*u[10]+ lhs[137][ie]*u[11]+
                   lhs[138][ie]*u[12]+ lhs[139][ie]*u[13]+ lhs[140][ie]*u[14]+ lhs[141][ie]*u[15]+ lhs[142][ie]*u[16]+ lhs[143][ie]*u[17];

            r[ 8]= lhs[144][ie]*u[ 0]+ lhs[145][ie]*u[ 1]+ lhs[146][ie]*u[ 2]+ lhs[147][ie]*u[ 3]+ lhs[148][ie]*u[ 4]+ lhs[149][ie]*u[ 5]+ 
                   lhs[150][ie]*u[ 6]+ lhs[151][ie]*u[ 7]+ lhs[152][ie]*u[ 8]+ lhs[153][ie]*u[ 9]+ lhs[154][ie]*u[10]+ lhs[155][ie]*u[11]+
                   lhs[156][ie]*u[12]+ lhs[157][ie]*u[13]+ lhs[158][ie]*u[14]+ lhs[159][ie]*u[15]+ lhs[160][ie]*u[16]+ lhs[161][ie]*u[17];

            r[ 9]= lhs[162][ie]*u[ 0]+ lhs[163][ie]*u[ 1]+ lhs[164][ie]*u[ 2]+ lhs[165][ie]*u[ 3]+ lhs[166][ie]*u[ 4]+ lhs[167][ie]*u[ 5]+ 
                   lhs[168][ie]*u[ 6]+ lhs[169][ie]*u[ 7]+ lhs[170][ie]*u[ 8]+ lhs[171][ie]*u[ 9]+ lhs[172][ie]*u[10]+ lhs[173][ie]*u[11]+
                   lhs[174][ie]*u[12]+ lhs[175][ie]*u[13]+ lhs[176][ie]*u[14]+ lhs[177][ie]*u[15]+ lhs[178][ie]*u[16]+ lhs[179][ie]*u[17];

            r[10]= lhs[180][ie]*u[ 0]+ lhs[181][ie]*u[ 1]+ lhs[182][ie]*u[ 2]+ lhs[183][ie]*u[ 3]+ lhs[184][ie]*u[ 4]+ lhs[185][ie]*u[ 5]+ 
                   lhs[186][ie]*u[ 6]+ lhs[187][ie]*u[ 7]+ lhs[188][ie]*u[ 8]+ lhs[189][ie]*u[ 9]+ lhs[190][ie]*u[10]+ lhs[191][ie]*u[11]+
                   lhs[192][ie]*u[12]+ lhs[193][ie]*u[13]+ lhs[194][ie]*u[14]+ lhs[195][ie]*u[15]+ lhs[196][ie]*u[16]+ lhs[197][ie]*u[17];

            r[11]= lhs[198][ie]*u[ 0]+ lhs[199][ie]*u[ 1]+ lhs[200][ie]*u[ 2]+ lhs[201][ie]*u[ 3]+ lhs[202][ie]*u[ 4]+ lhs[203][ie]*u[ 5]+ 
                   lhs[204][ie]*u[ 6]+ lhs[205][ie]*u[ 7]+ lhs[206][ie]*u[ 8]+ lhs[207][ie]*u[ 9]+ lhs[208][ie]*u[10]+ lhs[209][ie]*u[11]+
                   lhs[210][ie]*u[12]+ lhs[211][ie]*u[13]+ lhs[212][ie]*u[14]+ lhs[213][ie]*u[15]+ lhs[214][ie]*u[16]+ lhs[215][ie]*u[17];

            r[12]= lhs[216][ie]*u[ 0]+ lhs[217][ie]*u[ 1]+ lhs[218][ie]*u[ 2]+ lhs[219][ie]*u[ 3]+ lhs[220][ie]*u[ 4]+ lhs[221][ie]*u[ 5]+ 
                   lhs[222][ie]*u[ 6]+ lhs[223][ie]*u[ 7]+ lhs[224][ie]*u[ 8]+ lhs[225][ie]*u[ 9]+ lhs[226][ie]*u[10]+ lhs[227][ie]*u[11]+
                   lhs[228][ie]*u[12]+ lhs[229][ie]*u[13]+ lhs[230][ie]*u[14]+ lhs[231][ie]*u[15]+ lhs[232][ie]*u[16]+ lhs[233][ie]*u[17];

            r[13]= lhs[234][ie]*u[ 0]+ lhs[235][ie]*u[ 1]+ lhs[236][ie]*u[ 2]+ lhs[237][ie]*u[ 3]+ lhs[238][ie]*u[ 4]+ lhs[239][ie]*u[ 5]+ 
                   lhs[240][ie]*u[ 6]+ lhs[241][ie]*u[ 7]+ lhs[242][ie]*u[ 8]+ lhs[243][ie]*u[ 9]+ lhs[244][ie]*u[10]+ lhs[245][ie]*u[11]+
                   lhs[246][ie]*u[12]+ lhs[247][ie]*u[13]+ lhs[248][ie]*u[14]+ lhs[249][ie]*u[15]+ lhs[250][ie]*u[16]+ lhs[251][ie]*u[17];
            
            r[14]= lhs[252][ie]*u[ 0]+ lhs[253][ie]*u[ 1]+ lhs[254][ie]*u[ 2]+ lhs[255][ie]*u[ 3]+ lhs[256][ie]*u[ 4]+ lhs[257][ie]*u[ 5]+ 
                   lhs[258][ie]*u[ 6]+ lhs[259][ie]*u[ 7]+ lhs[260][ie]*u[ 8]+ lhs[261][ie]*u[ 9]+ lhs[262][ie]*u[10]+ lhs[263][ie]*u[11]+
                   lhs[264][ie]*u[12]+ lhs[265][ie]*u[13]+ lhs[266][ie]*u[14]+ lhs[267][ie]*u[15]+ lhs[268][ie]*u[16]+ lhs[269][ie]*u[17];

            r[15]= lhs[270][ie]*u[ 0]+ lhs[271][ie]*u[ 1]+ lhs[272][ie]*u[ 2]+ lhs[273][ie]*u[ 3]+ lhs[274][ie]*u[ 4]+ lhs[275][ie]*u[ 5]+ 
                   lhs[276][ie]*u[ 6]+ lhs[277][ie]*u[ 7]+ lhs[278][ie]*u[ 8]+ lhs[279][ie]*u[ 9]+ lhs[280][ie]*u[10]+ lhs[281][ie]*u[11]+
                   lhs[282][ie]*u[12]+ lhs[283][ie]*u[13]+ lhs[284][ie]*u[14]+ lhs[285][ie]*u[15]+ lhs[286][ie]*u[16]+ lhs[287][ie]*u[17];

            r[16]= lhs[288][ie]*u[ 0]+ lhs[289][ie]*u[ 1]+ lhs[290][ie]*u[ 2]+ lhs[291][ie]*u[ 3]+ lhs[292][ie]*u[ 4]+ lhs[293][ie]*u[ 5]+ 
                   lhs[294][ie]*u[ 6]+ lhs[295][ie]*u[ 7]+ lhs[296][ie]*u[ 8]+ lhs[297][ie]*u[ 9]+ lhs[298][ie]*u[10]+ lhs[299][ie]*u[11]+
                   lhs[300][ie]*u[12]+ lhs[301][ie]*u[13]+ lhs[302][ie]*u[14]+ lhs[303][ie]*u[15]+ lhs[304][ie]*u[16]+ lhs[305][ie]*u[17];

            r[17]= lhs[306][ie]*u[ 0]+ lhs[307][ie]*u[ 1]+ lhs[308][ie]*u[ 2]+ lhs[309][ie]*u[ 3]+ lhs[310][ie]*u[ 4]+ lhs[311][ie]*u[ 5]+ 
                   lhs[312][ie]*u[ 6]+ lhs[313][ie]*u[ 7]+ lhs[314][ie]*u[ 8]+ lhs[315][ie]*u[ 9]+ lhs[316][ie]*u[10]+ lhs[317][ie]*u[11]+
                   lhs[318][ie]*u[12]+ lhs[319][ie]*u[13]+ lhs[320][ie]*u[14]+ lhs[321][ie]*u[15]+ lhs[322][ie]*u[16]+ lhs[323][ie]*u[17];


            rhs[0][j0]+= r[ 0];
            rhs[0][j1]+= r[ 1];
            rhs[0][j2]+= r[ 2];
            rhs[0][j3]+= r[ 3]; 
            rhs[0][j4]+= r[ 4];
            rhs[0][j5]+= r[ 5];
 
            rhs[1][j0]+= r[ 6];
            rhs[1][j1]+= r[ 7];
            rhs[1][j2]+= r[ 8];
            rhs[1][j3]+= r[ 9];
            rhs[1][j4]+= r[10];
            rhs[1][j5]+= r[11];

            rhs[2][j0]+= r[12];
            rhs[2][j1]+= r[13];
            rhs[2][j2]+= r[14];
            rhs[2][j3]+= r[15];
            rhs[2][j4]+= r[16];
            rhs[2][j5]+= r[17];
            
        }
     }   
  }

   void cp63::gtdrhs( Int ies, Int iee, Real *xp[], Real *q[], Real *dq[], Int *iep[], Int *ier[], Real *lhs[], Real *aux[], Real *wrk[], Real *drhs[], cCosystem *coo )
  {
      gtrhs( ies,iee, xp,dq, iep,ier, lhs,aux,wrk,drhs, coo );
  }*/

