
   using namespace std;


//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

#  include <domain/fem/element/element.h>

   cc13::cc13()
  {
      nsp=1;
      nsq=1;
      ng=0;
      naux=0;
      nlhs=0;
      nwrk=0;
      nbdv=1;
  }


   cc13::~cc13()
  {
  }


/* void cc13::gtrhs( Int ics, Int ice, Int *icp[], Int *icr[], Real *xp[], Real *q[], Real *qc[], Real *wrk[], Real *rhs[], cCosystem * )
  {
      Int ip,ic;
      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
             ip= icr[0][ic];
             rhs[0][ip]= ( q[0][ip]- qc[0][ic] );
             rhs[1][ip]= ( q[1][ip]- qc[1][ic] );
             rhs[2][ip]= ( q[2][ip]- qc[2][ic] );
             rhs[0][ip]= 0;
             rhs[1][ip]= 0;
             rhs[2][ip]= 0;
        }
     }
  }

   void cc13::gtdrhs( Int ics, Int ice, Int *icp[], Int *icr[], Real *xp[], Real *q[], Real *dq[], Real *qc[], Real *wrk[], Real *drhs[], cCosystem * )
  {
      Int ip,ic;
      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
             ip= icr[0][ic];
             drhs[0][ip]= dq[0][ip];
             drhs[1][ip]= dq[1][ip];
             drhs[2][ip]= dq[2][ip];
             drhs[0][ip]= 0;
             drhs[1][ip]= 0;
             drhs[2][ip]= 0;
        }
     }
  }*/

   void cc13::gtlhs( Int ies, Int iee, Int *iem, Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld )
  {
      Int iq,ie;
      if( iee > ies )
     {
//       cout << "connections go from "<<ics<<" "<<ice<<"\n";
         for( ie=ies;ie<iee;ie++ )
        {
             iq= ieq[0][ie];
//           cout << "apply constraint lhs to node "<<iq<<"\n";
             lhsd[0][iq]= 1;
             lhsd[1][iq]= 0;
             lhsd[2][iq]= 0;

             lhsd[3][iq]= 0;
             lhsd[4][iq]= 1;
             lhsd[5][iq]= 0;
       
             lhsd[6][iq]= 0;
             lhsd[7][iq]= 0;
             lhsd[8][iq]= 1;              
        }
     }
  }


    void cc13::apply( Int ist, Int ien, Int *ibq[],     Int *ibb[],      Real *xq[], Real *q[], Real *xb[], Real *qb[], Real *rhs[], cCosystem * )
   {
       Int ic,ib,iq;
       for( ic=ist;ic<ien;ic++ )
      {
          iq= ibq[0][ic];
          ib= ibb[0][ic];
          q[0][iq]= qb[0][ib];
          q[1][iq]= qb[1][ib];
          q[2][iq]= qb[2][ib];
          rhs[0][iq]= 0;
          rhs[1][iq]= 0;
          rhs[2][iq]= 0;
      }
   }

