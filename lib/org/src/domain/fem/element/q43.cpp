   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

#  include <domain/fem/element/element.h>


   cq43::cq43()
  {
      nst=3;
      nsp=4;
      nsq=4;
      naux=2;
      nlhs=0;
      nwrk=0;
      nbdv=0;

      ng=4;
      yg[0]= new Real[ng];
      yg[1]= new Real[ng];
      yg[2]= new Real[ng];
      wg=    new Real[ng];

      yg[0][0]= 0.211324865405e0;  yg[1][0]= 0.211324865405e0; wg[0]= 0.25e0;
      yg[0][1]= 0.788675134595e0;  yg[1][1]= 0.211324865405e0; wg[1]= 0.25e0;
      yg[0][2]= 0.788675134595e0;  yg[1][2]= 0.788675134595e0; wg[2]= 0.25e0;
      yg[0][3]= 0.211324865405e0;  yg[1][3]= 0.788675134595e0; wg[3]= 0.25e0;

  }


   cq43::~cq43()
  {
  }

   void cq43::shpf( Real *l, Real *g, Real *dgdl[] )
  {

      Int                  ig,il;

      g[0]= (1.-l[0])*(1.-l[1]);
      g[1]=     l[0]* (1.-l[1]);
      g[2]=     l[0]*     l[1];
      g[3]= (1.-l[0])*    l[1];

      dgdl[0][0]= -(1.-l[1]);
      dgdl[1][0]= -(1.-l[0]);

      dgdl[0][1]=  (1.-l[1]);
      dgdl[1][1]=     -l[0];

      dgdl[0][2]=      l[1];
      dgdl[1][2]=      l[0];

      dgdl[0][3]=     -l[1];
      dgdl[1][3]=  (1.-l[0]);

  }

   void cq43::shpx( Real *l, Real *g, Real *dgdl[] )
  {
      shpf( l,g,dgdl );
  }


   void cq43::gtlhs( Int ies, Int iee, Int *iem, Int *iep[], Real *xp[], Int *ieq[], Real *xq[], Real *q[], Real *lhs[], Real *lhsd[], Real *aux[], Real *wrk[], cCosystem *coo, cSolid *sld )

  {
      Int            i,j,ie,jo,jp,jq,iq,i0,i1,i2,i3,j0,j1,j2,j3,ig;
      
      Real            a,det;
      Real            x[24];
      Real            g[4];
      Real           dxdl[2][3],dldx[2][3];
      Real          *dgdl[2],dgdl0[4],dgdl1[4];
      Real           dgdx[2][4]={0.0};
      Real           R[2][2]={0.0};
      Real           RI[2][2]={0.0};     
      Real           lg[2]={0.0};
      Real           l[2][4]={0.0};
      Real           dgdlu[2][4]={0.0};

      dgdl[0]= dgdl0;
      dgdl[1]= dgdl1;

      cout << "q43::gtlhs quarantined\n";
      if( iee > ies )
     {
         setv( ies,iee, nlhs,ZERO, lhs );
     
         for( ig=0;ig<ng;ig++ )
        {

            setv( ies,iee, nwrk,ZERO, wrk );

            lg[0]= yg[0][ig];
            lg[1]= yg[1][ig];
            shpf( lg, g, dgdl );
            
            for( ie=ies;ie<iee;ie++ ) 
           {

               i0= iep[0][ie];
               i1= iep[1][ie];
               i2= iep[2][ie];
               i3= iep[3][ie];

               //cout << "index "<<i0<<" "<<i1<<" "<<i2<<" "<<i3<<"\n";

               x[ 0]= xp[0][i0];
               x[ 1]= xp[0][i1];
               x[ 2]= xp[0][i2];
               x[ 3]= xp[0][i3];

               x[ 4]= xp[1][i0];
               x[ 5]= xp[1][i1];
               x[ 6]= xp[1][i2];
               x[ 7]= xp[1][i3];
                
               x[ 8]= xp[2][i0];
               x[ 9]= xp[2][i1];
               x[10]= xp[2][i2];
               x[11]= xp[2][i3];
 
               dxdl[0][0]= x[0]*dgdl[0][0]+ x[1]*dgdl[0][1]+ x[ 2]*dgdl[0][2]+ x[ 3]*dgdl[0][3]; // dxdl1
               dxdl[0][1]= x[4]*dgdl[0][0]+ x[5]*dgdl[0][1]+ x[ 6]*dgdl[0][2]+ x[ 7]*dgdl[0][3]; // dydl1
               dxdl[0][2]= x[8]*dgdl[0][0]+ x[9]*dgdl[0][1]+ x[10]*dgdl[0][2]+ x[11]*dgdl[0][3]; // dzdl1

               dxdl[1][0]= x[0]*dgdl[1][0]+ x[1]*dgdl[1][1]+ x[ 2]*dgdl[1][2]+ x[ 3]*dgdl[1][3]; // dxdl2
               dxdl[1][1]= x[4]*dgdl[1][0]+ x[5]*dgdl[1][1]+ x[ 6]*dgdl[1][2]+ x[ 7]*dgdl[1][3]; // dydl2
               dxdl[1][2]= x[8]*dgdl[1][0]+ x[9]*dgdl[1][1]+ x[10]*dgdl[1][2]+ x[11]*dgdl[1][3]; // dzdl2

               // replace following with correct call to qrf/qrs
               // local frame: first axis 
               l[0][0]= dxdl[0][0];
               l[0][1]= dxdl[0][1];
               l[0][2]= dxdl[0][2];
               l[0][3]= l[0][0]*l[0][0]+ l[0][1]*l[0][1]+ l[0][2]*l[0][2];
               l[0][3]= sqrt( l[0][3] ); // alpha in the R matrix (entry 1,1 )

               R[0][0]= l[0][3];

               l[0][0]/= l[0][3]; // l vector, also Q1
               l[0][1]/= l[0][3];
               l[0][2]/= l[0][3];

               // local frame: second axis via Gram-Schmidt orthogonalization
               l[1][0]= dxdl[1][0];
               l[1][1]= dxdl[1][1];
               l[1][2]= dxdl[1][2];
               l[1][3]= l[0][0]*l[1][0]+ l[0][1]*l[1][1]+ l[0][2]*l[1][2]; // beta in the R matrix (entry 1,2 )

               R[0][1]= l[1][3];

               l[1][0]-= l[0][0]*l[1][3];
               l[1][1]-= l[0][1]*l[1][3];
               l[1][2]-= l[0][2]*l[1][3];
               l[1][3]= l[1][0]*l[1][0]+ l[1][1]*l[1][1]+ l[1][2]*l[1][2];
               l[1][3]= sqrt( l[1][3] ); // gamma in the R matrix (entry 2,2 ) !! entry 2,1 is zero

               R[1][0]= 0.0;
               R[1][1]= l[1][3];

               l[1][0]/= l[1][3]; // m vector, also Q2
               l[1][1]/= l[1][3];
               l[1][2]/= l[1][3];               

               a= R[0][0]*R[1][1];
 
               RI[0][0]= R[1][1]/a;
               RI[0][1]= -R[0][1]/a;
               RI[1][0]= 0.0;  
               RI[1][1]= R[0][0]/a;                          

               dgdlu[0][0]= dgdl[0][0]*RI[0][0]+ dgdl[1][0]*RI[1][0] ; // dF(0)/dl
               dgdlu[0][1]= dgdl[0][1]*RI[0][0]+ dgdl[1][1]*RI[1][0] ;
               dgdlu[0][2]= dgdl[0][2]*RI[0][0]+ dgdl[1][2]*RI[1][0] ;  
               dgdlu[0][3]= dgdl[0][3]*RI[0][0]+ dgdl[1][3]*RI[1][0] ;  
               

               dgdlu[1][0]= dgdl[0][0]*RI[0][1]+ dgdl[1][0]*RI[1][1] ; // dF(0)/dm
               dgdlu[1][1]= dgdl[0][1]*RI[0][1]+ dgdl[1][1]*RI[1][1] ;
               dgdlu[1][2]= dgdl[0][2]*RI[0][1]+ dgdl[1][2]*RI[1][1] ;  
               dgdlu[1][3]= dgdl[0][3]*RI[0][1]+ dgdl[1][3]*RI[1][1] ;  
               
               //exit(0);

               ////////////////////////////////////// x  ////////////////////////////////////////

               wrk[ 0][ie]= dgdlu[0][0]*l[0][0];
               wrk[ 1][ie]= dgdlu[1][0]*l[1][0];
               wrk[ 2][ie]= dgdlu[0][0]*l[1][0]+ dgdlu[1][0]*l[0][0];
               
               wrk[ 3][ie]= dgdlu[0][1]*l[0][0];
               wrk[ 4][ie]= dgdlu[1][1]*l[1][0];
               wrk[ 5][ie]= dgdlu[0][1]*l[1][0]+ dgdlu[1][1]*l[0][0];

               wrk[ 6][ie]= dgdlu[0][2]*l[0][0];
               wrk[ 7][ie]= dgdlu[1][2]*l[1][0];
               wrk[ 8][ie]= dgdlu[0][2]*l[1][0]+ dgdlu[1][2]*l[0][0];

               wrk[ 9][ie]= dgdlu[0][3]*l[0][0];
               wrk[10][ie]= dgdlu[1][3]*l[1][0];
               wrk[11][ie]= dgdlu[0][3]*l[1][0]+ dgdlu[1][3]*l[0][0];  
                               

               ////////////////////////////////     y /////////////////////////////////////////////////////

               wrk[12][ie]= dgdlu[0][0]*l[0][1];                    
               wrk[13][ie]= dgdlu[1][0]*l[1][1];                    
               wrk[14][ie]= dgdlu[0][0]*l[1][1]+ dgdlu[1][0]*l[0][1];
               
               wrk[15][ie]= dgdlu[0][1]*l[0][1];                    
               wrk[16][ie]= dgdlu[1][1]*l[1][1];                    
               wrk[17][ie]= dgdlu[0][1]*l[1][1]+ dgdlu[1][1]*l[0][1];
             
               wrk[18][ie]= dgdlu[0][2]*l[0][1];                    
               wrk[19][ie]= dgdlu[1][2]*l[1][1];                    
               wrk[20][ie]= dgdlu[0][2]*l[1][1]+ dgdlu[1][2]*l[0][1];
             
               wrk[21][ie]= dgdlu[0][3]*l[0][1];                    
               wrk[22][ie]= dgdlu[1][3]*l[1][1];                    
               wrk[23][ie]= dgdlu[0][3]*l[1][1]+ dgdlu[1][3]*l[0][1];
                              
            
               ////////////////////////////////     z /////////////////////////////////////////////////////  

               wrk[24][ie]= dgdlu[0][0]*l[0][2];                    
               wrk[25][ie]= dgdlu[1][0]*l[1][2];                    
               wrk[26][ie]= dgdlu[0][0]*l[1][2]+ dgdlu[1][0]*l[0][2];    
            
               wrk[27][ie]= dgdlu[0][1]*l[0][2];                    
               wrk[28][ie]= dgdlu[1][1]*l[1][2];                    
               wrk[29][ie]= dgdlu[0][1]*l[1][2]+ dgdlu[1][1]*l[0][2];
 
               wrk[30][ie]= dgdlu[0][2]*l[0][2];                    
               wrk[31][ie]= dgdlu[1][2]*l[1][2];                    
               wrk[32][ie]= dgdlu[0][2]*l[1][2]+ dgdlu[1][2]*l[0][2];
 
               wrk[33][ie]= dgdlu[0][3]*l[0][2];                    
               wrk[34][ie]= dgdlu[1][3]*l[1][2];                    
               wrk[35][ie]= dgdlu[0][3]*l[1][2]+ dgdlu[1][3]*l[0][2];    
                           
               
               wrk[36][ie]= wg[ig]*a;


           }
            for( jq=0;jq<12;jq++ )
           {
               sld->stress2( ies, iee, iem, wrk+3*jq, wrk+37 );
               for( ie=ies;ie<iee;ie++ )
              {

                  for(jp=0; jp<12; jp++)
                 {
                     for(jo=0; jo<3; jo++)
                    {
                        lhs[ jp+12*jq][ie]+= ( wrk[jo+3*jp][ie]*wrk[37+jo][ie] )*wrk[36][ie]*aux[0][ie];
                    }
                 }    
                  
              }
           }
        }
     }

      for( ie=ies;ie<iee;ie++ )
     {
         j0= ieq[0][ie];
         j1= ieq[1][ie];
         j2= ieq[2][ie];
         j3= ieq[3][ie];

         lhsd[0][j0]+= lhs[  0][ie];
         lhsd[1][j0]+= lhs[  4][ie];
         lhsd[2][j0]+= lhs[  8][ie];
         lhsd[3][j0]+= lhs[ 48][ie];
         lhsd[4][j0]+= lhs[ 52][ie];
         lhsd[5][j0]+= lhs[ 56][ie];
         lhsd[6][j0]+= lhs[ 96][ie];
         lhsd[7][j0]+= lhs[100][ie];
         lhsd[8][j0]+= lhs[104][ie];

         lhsd[0][j1]+= lhs[ 13][ie];
         lhsd[1][j1]+= lhs[ 17][ie];
         lhsd[2][j1]+= lhs[ 21][ie];
         lhsd[3][j1]+= lhs[ 61][ie];
         lhsd[4][j1]+= lhs[ 65][ie];
         lhsd[5][j1]+= lhs[ 69][ie];
         lhsd[6][j1]+= lhs[109][ie];
         lhsd[7][j1]+= lhs[113][ie];
         lhsd[8][j1]+= lhs[117][ie];

         lhsd[0][j2]+= lhs[ 26][ie];
         lhsd[1][j2]+= lhs[ 30][ie];
         lhsd[2][j2]+= lhs[ 34][ie];
         lhsd[3][j2]+= lhs[ 74][ie];
         lhsd[4][j2]+= lhs[ 78][ie];
         lhsd[5][j2]+= lhs[ 82][ie];
         lhsd[6][j2]+= lhs[122][ie];
         lhsd[7][j2]+= lhs[126][ie];
         lhsd[8][j2]+= lhs[130][ie];

         lhsd[0][j3]+= lhs[ 39][ie];
         lhsd[1][j3]+= lhs[ 43][ie];
         lhsd[2][j3]+= lhs[ 47][ie];
         lhsd[3][j3]+= lhs[ 87][ie];
         lhsd[4][j3]+= lhs[ 91][ie];
         lhsd[5][j3]+= lhs[ 95][ie];
         lhsd[6][j3]+= lhs[135][ie];
         lhsd[7][j3]+= lhs[139][ie];
         lhsd[8][j3]+= lhs[143][ie];
     }

  }

/* void cq43::gtrhs( Int ies, Int iee, Real *xp[], Real *q[], Int *iep[], Int *ier[], Real *lhs[], Real *aux[], Real *wrk[], Real *rhs[], cCosystem *coo )
  {
      Int            ie,i0,i1,i2,i3,j0,j1,j2,j3;
      Real           u[24],r[24];
      
      if( iee > ies )
     {
         for( ie=ies;ie<iee;ie++ ) 
        {
            for(Int jq=0; jq<12; jq++)
           {
               r[jq]= 0.0;
               u[jq]= 0.0; 
           } 

            i0= iep[0][ie];
            i1= iep[1][ie];
            i2= iep[2][ie];
            i3= iep[3][ie];

            j0= ier[0][ie];
            j1= ier[1][ie];
            j2= ier[2][ie];
            j3= ier[3][ie];

            u[0]= q[0][i0];
            u[1]= q[0][i1];
            u[2]= q[0][i2];
            u[3]= q[0][i3];

            u[4]= q[1][i0];
            u[5]= q[1][i1];
            u[6]= q[1][i2];
            u[7]= q[1][i3];

            u[8]= q[2][i0];
            u[9]= q[2][i1];
            u[10]= q[2][i2];
            u[11]= q[2][i3];

            for(Int jq=0; jq<12; jq++)
           {

               for(Int jp=0; jp<12; jp++)
              {
                  r[jq]+=  lhs[  jp+12*jq][ie]*u[ jp];
              }
           }

            
            rhs[0][j0]+= r[0];
            rhs[0][j1]+= r[1];
            rhs[0][j2]+= r[2];
            rhs[0][j3]+= r[3]; 

            rhs[1][j0]+= r[4];
            rhs[1][j1]+= r[5];
            rhs[1][j2]+= r[6];
            rhs[1][j3]+= r[7];

            rhs[2][j0]+= r[8];
            rhs[2][j1]+= r[9];
            rhs[2][j2]+= r[10];
            rhs[2][j3]+= r[11];
        }
     }
  }

   void cq43::gtdrhs( Int ies, Int iee, Real *xp[], Real *q[], Real *dq[], Int *iep[], Int *ier[], Real *lhs[], Real *aux[], Real *wrk[], Real *drhs[], cCosystem *coo )
  {
      gtrhs( ies,iee, xp,dq, iep,ier, lhs,aux,wrk,drhs, coo );
  }*/


