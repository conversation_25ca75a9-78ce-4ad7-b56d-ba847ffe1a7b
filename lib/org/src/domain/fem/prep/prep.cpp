   using namespace std;

#  include <domain/fem/domain.h>

   void cFeDomain::loadpart()
  {
//      FILE  *f;
//      Int    iq,ip,ib,ig;
//      Real   d;
//      Real   y[3];
//      string path;
//      string fnme;
//
//      if( !qcpu )
//     {
//         qcpu= new Int[nq];
//
//         path= dev->getcpath();
//         fnme= path+ "/part.dat";
//
//         f=fopen( fnme.c_str(), "r" );
//         fread( (void*)qcpu, sizeof(*qcpu),(size_t)nq, f );
//         fclose( f );
//
//         delete[] pcpu; 
//         pcpu= new Int[np];
//         for( ig=0;ig<ng;ig++ )
//        {
//            delete[] bcpu[ig];
//            bcpu[ig]= new Int[nbb[ig]];
//        }
//
//         cKdTree *kdt;
//         kdt= new cKdTree();
//         kdt->build( nx, nq,xq );
//
//         for( ip=0;ip<np;ip++ )
//        {
//            line( ip,nx,xp, y );
//            kdt->nearest( y,&iq,&d );
//            cout << "node "<<ip<<" is nearest to "<<iq<<"\n";
//            pcpu[ip]= qcpu[iq];
//        }
//
//         for( ig=0;ig<ng;ig++ )
//        {
//            for( ib=0;ib<nbb[ig];ib++ )
//           {
//               line( ib,nx,xb[ig], y );
//               kdt->nearest( y,&iq,&d );
//               cout << "boundary "<<ib<<" is nearest to "<<iq<<"\n";
//               bcpu[ig][ib]= qcpu[iq];
//           }
//        }
//        
//         delete kdt; kdt= NULL;
// 
//     }
//
//      
  }

   void cFeDomain::prep( Int icpu )
  {
//
//      Int        iek,ibk;
//      Int        ig,iq,ip,jp,ie,ib;
//
//      ofstream   lgf;
//      string     path,fnme;
//
//      Real *lxp,*lxq;
//      Int  *liep[MxNSk],*lieq[MxNSk],*liem[MxNSk];
//      Real *lauxe[MxNSk];
//      Int  *libp[MxNBG][MxNSk],*libq[MxNBG][MxNSk],*libb[MxNBG][MxNSk];
//      Real *lxb[MxNBG];
//
//      bool       val;
//      path= dev->getcpath();
//      fnme= path+ "/domain.preprocessor.log."+strc( icpu );
//      lgf.open( fnme.c_str() );
//
//      loadpart();
//      cout << "cFeDomain class should be preprocessing cpu "<<icpu<<"\n";
//
//      dof= new cPdata( nq,dev );
//      pts= new cPdata( np,dev );
//      for( iek=0;iek<nek;iek++ )
//     {
//         eld[iek]= new cPdata( ne[iek],dev );
//     }
//      for( ig=0;ig<ng;ig++ )
//     {
//         bdf[ig]= new cPdata( nbb[ig],dev );
//         for( ibk=0;ibk<nbk;ibk++ )
//        {
//            bld[ig][ibk]= new cPdata( nb[ig][ibk],dev ); 
//        }
//     }
//
//      for( iek=0;iek<nek;iek++ )
//     {
//         eld[iek]->wrloop( dof, neq[iek],ieq[iek] );
//         eld[iek]->wrloop( pts, nep[iek],iep[iek] );
//     }
//      for( ig=0;ig<ng;ig++ )
//     {
//         for( ibk=0;ibk<nbk;ibk++ )
//        {
//            bld[ig][ibk]->wrloop( dof,     nbq[ibk],ibq[ig][ibk] );
//            bld[ig][ibk]->wrloop( pts,     nbp[ibk],ibp[ig][ibk] );
//            bld[ig][ibk]->wrloop( bdf[ig], nbd[ibk],ibb[ig][ibk] );
//        }
//     }
//
//      for( iek=0;iek<nek;iek++ )
//     {
//         eld[iek]->rdloop( dof,neq[iek],ieq[iek] );
//         eld[iek]->rdloop( pts,nep[iek],iep[iek] );
//     }
//      for( ig=0;ig<ng;ig++ )
//     {
//         for( ibk=0;ibk<nbk;ibk++ )
//        {
//            bld[ig][ibk]->rdloop( dof,    nbq[ibk],ibq[ig][ibk] );
//            bld[ig][ibk]->rdloop( pts,    nbp[ibk],ibp[ig][ibk] );
//            bld[ig][ibk]->rdloop( bdf[ig],nbd[ibk],ibb[ig][ibk] );
//        }
//     }
//
//      pts->partition( );
//      dof->partition( );
//
//      for( iek=0;iek<nek;iek++ )
//     {
//         eld[iek]->partition( );
//     }
//      for( ig=0;ig<ng;ig++ )
//     {
//         bdf[ig]->partition( );
//         for( ibk=0;ibk<nbk;ibk++ )
//        {
//            bld[ig][ibk]->partition( );
//        }
//     }
//
//      lxp=NULL;
//      lxq=NULL;
//      pts->makelocal( nx,xp, &lxp );
//      dof->makelocal( nx,xq, &lxq );
//
//      for( iek=0;iek<nek;iek++ )
//     {
//         liem[iek]=NULL;
//         liep[iek]=NULL;
//         lieq[iek]=NULL;
//         lauxe[iek]=NULL;
//         eld[iek]->makelocal(          1, iem[iek],&(liem[iek]) );
//         eld[iek]->makelocal(   nep[iek], iep[iek],&(liep[iek]), pts );
//         eld[iek]->makelocal(   neq[iek], ieq[iek],&(lieq[iek]), dof );
//         eld[iek]->makelocal( nauxe[iek], auxe[iek],&(lauxe[iek]) );
//     }
//      for( ig=0;ig<ng;ig++ )
//     {
//         lxb[ig]= NULL;
//         bdf[ig]->makelocal( nx,xb[ig], &(lxb[ig]) );
//         for( ibk=0;ibk<nbk;ibk++ )
//        {
//            libp[ig][ibk]=NULL;
//            libq[ig][ibk]=NULL;
//            libb[ig][ibk]=NULL;
//            bld[ig][ibk]->makelocal( nbp[ibk], ibp[ig][ibk],&(libp[ig][ibk]), pts );
//            bld[ig][ibk]->makelocal( nbq[ibk], ibq[ig][ibk],&(libq[ig][ibk]), dof );
//            bld[ig][ibk]->makelocal( nbd[ibk], ibb[ig][ibk],&(libb[ig][ibk]), bdf[ig] );
//        }
//     }
//
//// dump to file
//
//      size_t len;
//      pickle_t buf;
//      fnme= path+"/preprocessor."+strc( icpu );
//
//      len=0;
//      buf=NULL;
//
//      pickle( &len,&buf );
//
//      FILE *f=fopen( fnme.c_str(),"w" );
//      fwrite( (void*)&len,  1,sizeof( len), f );
//      fwrite( (void*) buf,len,sizeof(*buf), f );
//      free( buf );buf=NULL;len=0;
//
//      pts->write( nx,lxp,f );
//      dof->write( nx,lxq,f );
//      for( iek=0;iek<nek;iek++ )
//     {
//         eld[iek]->write(        1,liem[iek], f );
//         eld[iek]->write( nep[iek],liep[iek], f );
//         eld[iek]->write( neq[iek],lieq[iek], f );
//         eld[iek]->write( nauxe[iek],lauxe[iek], f );
//     }
//      
//      for( ig=0;ig<ng;ig++ )
//     {
//         bdf[ig]->write( nx,lxb[ig], f );
//         for( ibk=0;ibk<nbk;ibk++ )
//        {
//            bld[ig][ibk]->write( nbp[ibk],libp[ig][ibk], f );
//            bld[ig][ibk]->write( nbq[ibk],libq[ig][ibk], f );
//            bld[ig][ibk]->write( nbd[ibk],libb[ig][ibk], f );
//        }
//     }
//
//      fclose(f);
//
//// cleanup
//
//
//      for( iek=0;iek<nek;iek++ )
//     {
//         eld[iek]->destroy( &liem[iek] );
//         eld[iek]->destroy( &liep[iek] );
//         eld[iek]->destroy( &lieq[iek] );
//         eld[iek]->destroy( &lauxe[iek] );
//         delete eld[iek]; eld[iek]= NULL;
//     }
//
//      for( ig=0;ig<ng;ig++ )
//     {
//         bdf[ig]->destroy( &lxb[ig] );
//         delete bdf[ig]; bdf[ig]= NULL;
//         for( ibk=0;ibk<nbk;ibk++ )
//        {
//            bld[ig][ibk]->destroy( &libp[ig][ibk] );
//            bld[ig][ibk]->destroy( &libq[ig][ibk] );
//            bld[ig][ibk]->destroy( &libb[ig][ibk] );
//            delete bld[ig][ibk]; bld[ig][ibk]= NULL;
//        }
//     }
//      dof->destroy( &lxq );
//      pts->destroy( &lxp );
//      delete dof; dof= NULL;
//      delete pts; pts= NULL;
//
//      lgf.close();
// 
  }

