   using namespace std;

#  include <domain/fem/domain.h>

   void cFeDomain::loadpre()
  {
//      size_t len;
//      pickle_t buf;
//
//      Int        ig,iek,ibk;
//      Int        icpu;
//
//      ofstream   lgf;
//      string     path,fnme;
//
//
//      icpu= dev->getrank();
//      path= dev->getcpath();
//
//      fnme= path+"/preprocessor."+strc( icpu );
//      FILE *f=fopen( fnme.c_str(),"r" );
//      cout << "should be opening file "<<fnme<<"\n";
//      cout << "f is "<<f<<"\n";
//      Int *tmpe=NULL,*tmpb=NULL; 
//     
//      len=0;
//      buf=NULL;
//      fread( (void*)&len,  1,sizeof( len), f ); buf=new pickle_v[len]; buf=(pickle_t)malloc( len*sizeof(*buf) );
//      cout << "pickled size of the domain object is "<<len<<"\n";
//      fread( (void*) buf,len,sizeof(*buf), f );
//
//      len=0;
//      unpickle( &len,buf );
//      free( buf );buf=NULL;len=0;
//
//      cout << "after unpickle \n";
//      cout << "nx "<< nx << "\n";
//      cout << "nv "<< nv << "\n";
//      cout << "nek "<< nek << "\n";
//      cout << "element sizes\n";
//      for( iek=0;iek<nek;iek++ )
//     {
//         cout << iek <<" "<<ne[iek]<<" "<<nep[iek]<<" "<<neq[iek]<<" "<<nlhse[iek]<<" "<<nwrke[iek]<<" "<<nauxe[iek]<<"\n";
//     }
//      cout << "coordinate system "<<coo<<"\n";
//      cout << "material set      "<<sld<<"\n";
//
//      pts->read( nx,&sxp,f ); subv( nx,np, sxp,xp );
//      dof->read( nx,&sxq,f ); subv( nx,nq, sxq,xq );
//      q=    new Real*[nv];
//      r=    new Real*[nv];
//      p=    new Real*[nv];
//      x=    new Real*[nv];
//      ax=   new Real*[nv];
//      lhsd= new Real*[nv*nv];
//      dof->create( nv,&sq  ); subv( nv,nq,  sq, q ); setv( 0,nv*nq, ZERO,sq );
//      dof->create( nv,&sr  ); subv( nv,nq,  sr, r ); setv( 0,nv*nq, ZERO,sr );
//      dof->create( nv,&sp  ); subv( nv,nq,  sp, p ); setv( 0,nv*nq, ZERO,sp );
//      dof->create( nv,&sx  ); subv( nv,nq,  sx, x ); setv( 0,nv*nq, ZERO,sx );
//      dof->create( nv,&sax ); subv( nv,nq,  sax,ax ); setv( 0,nv*nq,ZERO,sax );
//      dof->create( nv*nv,&slhsd ); subv( nv*nv,nq,  slhsd,lhsd ); setv( 0,nv*nv*nq,ZERO,slhsd );
//      cout << nx << " "<<np<<" "<<nq<<" "<<nek<<" "<<nbk<<" "<<ne[0]<<" "<<ne[1]<<" "<<ne[2]<<" "<<nb[0]<<"\n";
//
//      for( iek=0;iek<nek;iek++ )
//     {
//         auxe[iek]= new Real*[nauxe[iek]+1];
//         wrke[iek]= new Real*[nwrke[iek]+1];
//         lhse[iek]= new Real*[nlhse[iek]+1];
//         iem[iek]= new Int*[1];
//         iep[iek]= new Int*[nep[iek]];
//         ieq[iek]= new Int*[neq[iek]];
//         eld[iek]->read(        1,&(siem[iek]),f ); subv(   (Int)1,ne[iek], siem[iek],iem[iek] );
//         eld[iek]->read( nep[iek],&(siep[iek]),f ); subv( nep[iek],ne[iek], siep[iek],iep[iek] );
//         eld[iek]->read( neq[iek],&(sieq[iek]),f ); subv( neq[iek],ne[iek], sieq[iek],ieq[iek] );
//         eld[iek]->read( nauxe[iek],&(sauxe[iek]),f ); subv( nauxe[iek],ne[iek], sauxe[iek],auxe[iek] );
//
//         eld[iek]->create( nwrke[iek],&(swrke[iek]) ); subv( nwrke[iek],ne[iek], swrke[iek],wrke[iek] );
//         eld[iek]->create( nlhse[iek],&(slhse[iek]) ); subv( nlhse[iek],ne[iek], slhse[iek],lhse[iek] );
//
//     }
//      for( ig=0;ig<ng;ig++ )
//     {
//         qb[ig]=  new Real*[nv];
//         dqb[ig]= new Real*[nv];
//         bdf[ig]->read( nx,&(sxb[ig]),f ); subv( nx,nbb[ig],sxb[ig],xb[ig] );
//         bdf[ig]->create( nq,&(sqb[ig]) ); subv( nv,nbb[ig],sqb[ig],qb[ig] );
//         bdf[ig]->create( nq,&(sdqb[ig]) ); subv( nv,nbb[ig],sdqb[ig],dqb[ig] );
//         for( ibk=0;ibk<nbk;ibk++ )
//        {
//            auxb[ig][ibk]= new Real*[nauxb[ibk]+1];
//            wrkb[ig][ibk]= new Real*[nwrkb[ibk]+1];
//            lhsb[ig][ibk]= new Real*[nlhsb[ibk]+1];
//            cout << "this boundary element kind holds "<<nbd[ibk]<<" values\n";
//            bld[ig][ibk]->read( nbp[ibk],&(sibp[ig][ibk]),f ); subv( nbp[ibk],nb[ig][ibk], sibp[ig][ibk],ibp[ig][ibk] );
//            bld[ig][ibk]->read( nbq[ibk],&(sibq[ig][ibk]),f ); subv( nbq[ibk],nb[ig][ibk], sibq[ig][ibk],ibq[ig][ibk] );
//            bld[ig][ibk]->read( nbd[ibk],&(sibb[ig][ibk]),f ); subv( nbd[ibk],nb[ig][ibk], sibb[ig][ibk],ibb[ig][ibk] );
//
//            bld[ig][ibk]->create( nauxb[ibk],&(sauxb[ig][ibk]) ); subv( nauxb[ibk],nb[ig][ibk], sauxb[ig][ibk],auxb[ig][ibk] );
//            bld[ig][ibk]->create( nwrkb[ibk],&(swrkb[ig][ibk]) ); subv( nwrkb[ibk],nb[ig][ibk], swrkb[ig][ibk],wrkb[ig][ibk] );
//            bld[ig][ibk]->create( nlhsb[ibk],&(slhsb[ig][ibk]) ); subv( nlhsb[ibk],nb[ig][ibk], slhsb[ig][ibk],lhsb[ig][ibk] );
//        }
//     }
//
//      fclose(f);
//
//      dof->start();
////    pts->start();
//
//      cout << "nv is "<<nv<<"\n";
////    stophere();
  }
