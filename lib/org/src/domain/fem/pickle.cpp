
   using namespace std;

#  include <domain/fem/domain.h>

   void cFeDomain::pickle( size_t *len, pickle_t *buf )
  {
      Int        tmpe[MxNSk],tmpb[MxNSk];
      Int        ig;
      Int        iek,ibk,isk,ick;
      bool       val;

      cDomain::pickle( len,buf );

      ick= coo->gettype();
      pckle( len, ick, buf );
      coo->pickle( len,buf );

      isk= sld->gettype();
      pckle( len, isk, buf );
      sld->pickle( len,buf );

      for( iek=0;iek<nek;iek++ )
     {
         tmpe[iek]= elm[iek]->gettype();
     }
      for( ibk=0;ibk<nbk;ibk++ )
     {
         tmpb[ibk]= blm[ibk]->gettype();
     }

      pckle(  len, nx,      buf );
      pckle(  len, nv,      buf );
      pckle(  len,nek,tmpe, buf );
      pckle(  len,nbk,tmpb, buf );
//    pckle(  len, ng,      buf );

      val= (dof);
      pckle(  len, val,     buf );
      if( val )
     {

         dof->pickle(  len, buf );
         pts->pickle(  len, buf );
         for( iek=0;iek<nek;iek++ )
        {
            eld[iek]->pickle(  len, buf );
        }
         for( ig=0;ig<ng;ig++ )
        {
/*          pckle( len,bgnm[ig],buf );
            pckle( len,bpl[ig],buf );
            pckle( len,bpo[ig],buf );*/
            bdf[ig]->pickle(  len, buf );
            for( ibk=0;ibk<nbk;ibk++ )
           {
               bld[ig][ibk]->pickle(  len, buf );
           }
        }
     }
  }

   void cFeDomain::unpickle( size_t *len, pickle_t buf )
  {
      Int        ig,iek,ibk,isk,ick;
      Int       *tmpe=NULL,*tmpb=NULL; 
      bool       val;

      cDomain::unpickle( len,buf );

      unpckle( len, &ick, buf );
      coo= newcosystem( ick );
      coo->unpickle( len,buf );

      unpckle( len, &isk, buf );
      sld= newsolid( isk );
      sld->unpickle( len,buf );
      sld->sizes( coo );

      unpckle(  len,& nx,buf );
      unpckle(  len,& nv,buf );
      unpckle(  len,&nek,&tmpe,buf ); 
      unpckle(  len,&nbk,&tmpb,buf ); 

      for( iek=0;iek<nek;iek++ )
     { 
         elm[iek]= newelement( tmpe[iek] ); 
         nep[iek]= elm[iek]->getnp();
         neq[iek]= elm[iek]->getnq();
         elm[iek]->sizes( sld );
         nauxe[iek]= elm[iek]->getnaux( );
         nwrke[iek]= elm[iek]->getnwrk( );
         nlhse[iek]= elm[iek]->getnlhs( );
     };
      for( ibk=0;ibk<nbk;ibk++ )
     { 
         blm[ibk]= newelement( tmpb[ibk] ); 
         nbp[ibk]= blm[ibk]->getnp();
         nbq[ibk]= blm[ibk]->getnq();
         nbd[ibk]= blm[ibk]->getnd();
         nauxb[ibk]= blm[ibk]->getnaux( );
         nwrkb[ibk]= blm[ibk]->getnwrk( );
         nlhsb[ibk]= blm[ibk]->getnlhs( );
         nwrkb[ibk]= blm[ibk]->getnwrk( );
         nlhsb[ibk]= blm[ibk]->getnlhs( );
     };
      delete[] tmpe; tmpe=NULL;
      delete[] tmpb; tmpb=NULL;

//    unpckle(  len,&ng,      buf );

      unpckle(  len,&val,buf );

      if( val )
     {
         dof=new cPdata( dev );
         dof->unpickle(  len,buf );

         pts=new cPdata( dev );
         pts->unpickle(  len,buf );

         np= pts->size();
         nq= dof->size();

         for( iek=0;iek<nek;iek++ )
        {
            eld[iek]=new cPdata( dev );
            eld[iek]->unpickle(  len,buf );
            ne[iek]= eld[iek]->size();
        }
         for( ig=0;ig<ng;ig++ )
        {

/*          unpckle( len,&(bgnm[ig]),buf );
            unpckle( len, &(bpl[ig]),buf );
            unpckle( len, &(bpo[ig]),buf );*/

            bdf[ig]= new cPdata( dev );
            bdf[ig]->unpickle( len,buf );
            nbb[ig]= bdf[ig]->size();
            for( ibk=0;ibk<nbk;ibk++ )
           {
               bld[ig][ibk]=new cPdata( dev );
               bld[ig][ibk]->unpickle(  len,buf );
               nb[ig][ibk]= bld[ig][ibk]->size();
           }
        }
     }
  }
