
   using namespace std;

#  include <domain/fem/domain.h>

   void cFeDomain::comp()
  {
      Int iq;
      Real *r,*q;
      Real  res;

      cout << "cFeDomain compute\n";
      cout << "dev->ncpu "<<dev->getncpu()<<"\n";
      cout << "dev->irnk "<<dev->getrank()<<"\n";

      cout << "ng "<<ng<<"\n";
      Int ig;
      for( ig=0;ig<ng;ig++ )
     {
         cout << bgnm[ig]<<" "<<bpl[ig]<<" "<<bpo[ig]<<"\n";
     }
      
//    loadbcs();
      cout << "incorporate loadbcs with loadpre\n";
      gtlhs();

 
/*    r= new Real[nq];
      q= new Real[nq];
      setv( 0,nq, 0., r );
      r[19]= 1.;
      r[24]= 1.;
      r[25]= 1.;
      r[26]= 1.;
      setv( 0,nq, 0., q );

      for( Int jt=0;jt<1000;jt++ )
     {
         gssd( q,r, &res );
         cout << jt << " "<<res<<"\n";
     }

      ofstream fle;
      fle.open( "solution.dat" );
      for( iq=0;iq<nq;iq++ )
     {
         fle << xp[0][iq]<<" "<<xp[1][iq]<<" "<<q[indx[iq]]<<"\n";
     }
      fle.close();
      delete[] r; r= NULL;
      delete[] q; q= NULL;*/
  }
