   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 13:55:16 BST 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         abstract symplex representation

#  include <domain/symplex.h>

   cSymplex::cSymplex()
  {
      nsp=0;
      nsq=0;
  }

   cSymplex::~cSymplex()
  {
      nsp=0;
      nsq=0;
  }

   void cSymplex::graph( Int iss, Int ise, Int *isq[], Int *lgq[], Int **igq, Int *isg[] )
  {
      tpgraph( iss,ise,nsq,isq,lgq,igq,isg );
  }

