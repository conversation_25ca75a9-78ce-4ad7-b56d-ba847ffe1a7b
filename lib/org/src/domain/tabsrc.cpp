   using namespace std;

#  include <domain/domain.h>

   void cDomain::get( cTabData *rfl )
  {
      cTabItem *tmp;

//      tmp= new cTabItem( sidl ); rfl->append( "solution-initialiser-library", tmp );
//      tmp= new cTabItem( sido ); rfl->append( "solution-initialiser-object",  tmp );
//      tmp= new cTabItem( swdl ); rfl->append( "solution-writer-library",      tmp );
//      tmp= new cTabItem( swdo ); rfl->append( "solution-writer-object",       tmp );
//      tmp= new cTabItem( fidl ); rfl->append( "frame-initialiser-library",    tmp );
//      tmp= new cTabItem( fido ); rfl->append( "frame-initialiser-object",     tmp );
//
//      rfl->set( "solution-initialiser-library",sidl );
//      rfl->set( "solution-initialiser-object", sido );
//      rfl->set( "solution-writer-library",     swdl );
//      rfl->set( "solution-writer-object",      swdo );
//      rfl->set( "frame-initialiser-library",   fidl );
//      rfl->set( "frame-initialiser-object",    fido );

  }

   void cDomain::set( cTabData *rfl )
  {
//      rfl->get( "solution-initialiser-library",&sidl );
//      rfl->get( "solution-initialiser-object", &sido );
//      rfl->get( "solution-writer-library",     &swdl );
//      rfl->get( "solution-writer-object",      &swdo );
//      rfl->get( "frame-initialiser-library",   &fidl );
//      rfl->get( "frame-initialiser-object",    &fido );

  }

