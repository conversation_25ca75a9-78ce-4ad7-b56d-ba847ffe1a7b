
   using namespace std;

#  include <domain/domain.h>
#  include <topo/ugraph.h>
#  include <fstream>


   void cDomain::assgnprd( Int i0, Int i1, Real *dmax )
  {

      cUgraph *ug;
      Int   ibk,ip,jp,ib,ir,jr,ix,ip0,ip1,jb,hb,kb,iq0,iq1,jq,kq;
      Int  ***jbp;
      Int  **sjbp;

      Real *sy[2]={NULL,NULL};
      Real *y[2][3]={{NULL,NULL,NULL},{NULL,NULL,NULL}};

      Int  *ihlp[2];
      Real  tmp[3];
      cKdTree *kdt;
      Real d;
      Int jmin;
 
      Int   nn[2]={0,0};

      ipr0= i0;
      ipr1= i1;

      sjbp= new Int*[nbk];
      jbp= new Int**[nbk];
      for( ibk=0;ibk<nbk;ibk++ )
     {
         nprb[ibk]= nb[ipr0][ibk];
         sjbp[ibk]= new Int[nprb[ibk]*nbp[ibk]];
         jbp[ibk]= new Int*[nbp[ibk]];
         subv( nbp[ibk],nprb[ibk], sjbp[ibk],jbp[ibk] );
     }

      ihlp[0]= new Int[np];
      ihlp[1]= new Int[np];
      setv( (Int)0,np, (Int)-1, ihlp[0] );
      setv( (Int)0,np, (Int)-1, ihlp[1] );
      for( ibk=0;ibk<nbk;ibk++ )
     {
         if( nprb[ibk] == nb[ipr1][ibk] )
        {
            for( jp=0;jp<nbp[ibk];jp++ )
           {
               for( ib=0;ib<nprb[ibk];ib++ )
              {
                  ip= ibp[ipr0][ibk][jp][ib];
                  ihlp[0][ip]= 1;
              }
               for( ib=0;ib<nb[ipr1][ibk];ib++ )
              {
                  ip= ibp[ipr1][ibk][jp][ib];
                  ihlp[1][ip]= 1;
              }
           }
        }
         else
        {
            cout << "numbers of periodic faces do not match\n";
            exit(0);
        }
     }

      for( jr=0;jr<2;jr++ )
     {
         for( ip=0;ip<np;ip++ )
        {
            if( ihlp[jr][ip] == 1 )
           {
               nn[jr]++;
           }
        }
     }
      if( nn[0] != nn[1] )
     {
         cout << "number of periodic points do not match\n";
         exit(0);
     }

      nprp= nn[0]; 
      siprp= new Int[nprp*2];
      subv( (Int)2,nprp,siprp,iprp );

      for( jr=0;jr<2;jr++ )
     {
         sy[jr]= new Real[nx*nn[jr]];
         subv( nx,nn[jr], sy[jr],y[jr] );
         ir=0;
         for( ip=0;ip<np;ip++ )
        {
            if( ihlp[jr][ip] == 1 )
           {
               iprp[jr][ir]= ip;
               line( ip,nx,xp, tmp );
               for( ix=0;ix<nx;ix++ )
              {
                  y[jr][ix][ir]= xp(ix,ip);
              }
               ir++;
           }
        }
     }

      coo->coffset( nprp,1.,sy[0] );

      kdt= new cKdTree();
      kdt->build( nx,nprp,y[0] );
      
      Int *iprm;
      iprm= new Int[nprp];
      setv( (Int)0,nprp, (Int)-1, iprm );

     *dmax=-big;
      for( ir=0;ir<nprp;ir++ )
     {
         line( ir,nx,y[1], tmp );
         kdt->nearest( tmp, &jr,&d );
         if( *dmax < d )
        {
           *dmax=d;
        }
         
         if( iprm[ir] != -1 )
        {
            cout << "trying to assign periodic point twice\n";
            exit(0);
        }
         iprm[ir]= jr;
     }

      for( ir=0;ir<nprp;ir++ )
     {
         if( iprm[ir] == -1 )
        {   
            cout << "unassigned periodic point\n";
            exit(0);
        }
     }
      permute( nprp,iprp[0],iprm );

//    cout << "POINT CHECK\n";
      for( ir=0;ir<nprp;ir++ )
     {
         ip0= iprp[0][ir];
         ip1= iprp[1][ir];
         ihlp[0][ip0]= ip1;
         ihlp[1][ip1]= ip0;
/*       cout << "\n";
         cout << "#\n";
         cout << "\n";
         cout << ip0<<" "<< xp[0][ip0]<<" "<<xp[1][ip0]<<" "<<xp[2][ip0]<<"\n";
         cout << ip1<<" "<<xp[0][ip1]<<" "<<xp[1][ip1]<<" "<<xp[2][ip1]<<"\n";*/
     }
//    cout << "\n";
//    cout << "POINT CHECK - COINCISE\n";
      for( ir=0;ir<nprp;ir++ )
     {
         ip0= iprp[0][ir];
         ip1= iprp[1][ir];
//       cout << ip0<<" "<< ip1<<"\n";
     }
//    cout << "\n";
      for( ibk=0;ibk<nbk;ibk++ )
     {
         for( jp=0;jp<nbp[ibk];jp++ )
        {
            for( ib=0;ib<nprb[ibk];ib++ )
           {
               ip= ibp[ipr1][ibk][jp][ib]; 
               ip= ihlp[1][ip];
               jbp[ibk][jp][ib]= ip;
           }
        }
     }

      Int  *ibe[3]={NULL,NULL,NULL};
      Int neb=1;
      Int ***ieb;
      ieb= new Int**[1];
 
      for( ibk=0;ibk<nbk;ibk++ )
     {
         ibe[0]= new Int[nprb[ibk]]; setv( (Int)0,nprb[ibk], (Int)-1, ibe[0] );
         ibe[1]= new Int[nprb[ibk]]; setv( (Int)0,nprb[ibk], (Int)-1, ibe[1] );
         ibe[2]= new Int[nprb[ibk]]; setv( (Int)0,nprb[ibk], (Int)-1, ibe[2] );

         ug= new cUgraph(np);
         ug->build( nprb[ibk],nbp[ibk],ibp[ipr0][ibk], 0 );

         ieb[0]= new Int*[nbp[ibk]];
 
         for( jp=0;jp<nbp[ibk];jp++ )
        {
            ieb[0][jp]= new Int[1];
            ieb[0][jp][0]= jp;
        }

         ug->match( nprb[ibk],nbp[ibk],jbp[ibk], ibe, nbp+ibk, ibp[ipr0]+ibk, &neb,ieb );
         siprb[ibk]= new Int[2*nprb[ibk]];
         subv( 2,nprb[ibk],siprb[ibk],iprb[ibk] );

         for( ib=0;ib<nprb[ibk];ib++ )
        {
            jb=ibe[0][ib];
            iprb[ibk][0][ib]= jb;
            iprb[ibk][1][ib]= ib;
        }
//       cout << "RAW PERIODIC FACES \n";
//       cout << "RIGHT SIDE\n";
         for( ib=0;ib<nprb[ibk];ib++ )
        {
            jb= ib;
//          cout << ib <<" "<< jb <<"( ";
 
            for( jp=0;jp<nbp[ibk];jp++ )
           {
//             cout << ibp[ipr0][ibk][jp][jb]<<" ";
           }
//          cout <<" ) "<<ibq[ipr0][ibk][0][jb]<<" \n";
        }
//       cout << "LEFT SIDE\n";
         for( ib=0;ib<nprb[ibk];ib++ )
        {
            jb= ib;
//          cout <<jb<<" ( ";
            for( jp=0;jp<nbp[ibk];jp++ )
           {
//             cout << ibp[ipr1][ibk][jp][jb]<<" ";
           }
//          cout <<" ) "<<ibq[ipr1][ibk][0][jb]<<" \n";
        }
//       cout << "END RAW PERIODIC FACES\n";
//       cout << "PERIODIC BOUNDARY CHECK\n";
         for( ib=0;ib<nprb[ibk];ib++ )
        {
            jb= iprb[ibk][0][ib];
//          cout << ib <<" "<< jb <<"( ";
 
            for( jp=0;jp<nbp[ibk];jp++ )
           {
//             cout << ibp[ipr0][ibk][jp][jb]<<" ";
           }
//          cout <<" ) "<<ibq[ipr0][ibk][0][jb]<<" | ";
            jb= iprb[ibk][1][ib];
//          cout <<jb<<" ( ";
            for( jp=0;jp<nbp[ibk];jp++ )
           {
//             cout << ibp[ipr1][ibk][jp][jb]<<" ";
           }
//          cout <<" ) "<<ibq[ipr1][ibk][0][jb]<<" \n";
        }
//       cout << "END\n";

         for( jp=0;jp<nbp[ibk];jp++ )
        {
            delete[] ieb[0][jp];
        }
         delete[] ieb[0];

         delete[] ibe[0]; ibe[0]=NULL;
         delete[] ibe[1]; ibe[1]=NULL;
         delete[] ibe[2]; ibe[2]=NULL;

         delete ug; ug= NULL;

     }

      delete[] ieb;

      assgnprq();

      for( ibk=0;ibk<nbk;ibk++ )
     {
         delete[] jbp[ibk]; jbp[ibk]= NULL; 
         delete[] sjbp[ibk]; sjbp[ibk]= NULL;
     }
      delete[] jbp;
      delete[] sjbp;
      delete[] sy[0];
      delete[] sy[1];
      delete[] iprm; iprm= NULL;
      delete kdt;

      delete[] ihlp[0];
      delete[] ihlp[1];
  }

   void cDomain::assgnprq()
  {
      Int ib,jq,kq,ibk,iq0,iq1,jb,iq;
      Int n0,n1;
      Int *ihlq[2];
      ihlq[0]= new Int[nq];
      ihlq[1]= new Int[nq];
      setv( (Int)0,nq, (Int)-1,ihlq[0] );
      setv( (Int)0,nq, (Int)-1,ihlq[1] );
      n0=0;
      n1=0;
      for( ibk=0;ibk<nbk;ibk++ )
     {
         kq= nbq[ibk];
         for( jq=0;jq<nbq[ibk];jq++ )
        {
            kq--;
            for( ib=0;ib<nprb[ibk];ib++ )
           {
               jb= iprb[ibk][0][ib];
               iq0= ibq[ipr0][ibk][jq][jb];
               jb= iprb[ibk][1][ib];
               iq1= ibq[ipr1][ibk][kq][jb];

               if( ihlq[0][iq1] == -1 )
              {
                  ihlq[0][iq1]= iq0; 
                  n0++;   
              }
               else
              {
                  if( ihlq[0][iq1]!= iq0 )
                 {
                     cout << "inconsistent dof ordering on faces\n";
                 }
              }
               if( ihlq[1][iq0] == -1 )
              {
                  ihlq[1][iq0]= iq1; 
                  n1++;
              }
               else
              {
                  if( ihlq[1][iq0]!= iq1 )
                 {
                     cout << "inconsistent dof ordering on faces\n";
                 }
              }
           }
        }
     }
      if( n0 != n1 )
     {
         cout << "number of periodic dofs does not match\n";
     }
      else
     {
         nprq= n0;
         siprq= new Int[2*nprq];
         subv( 2,nprq, siprq,iprq );
         jq=0;
         for( iq=0;iq<nq;iq++ )
        {
            iq1= ihlq[0][iq];
            if( iq1 != -1 )
           {
               iprq[0][jq]= iq1;
               iprq[1][jq]= iq;
               jq++;
           }
        }
     }
//    cout << "PERIODIC DOF\n";
      for( iq=0;iq<nprq;iq++ )
     {
//       cout << iq<<" "<<iprq[0][iq]<<" "<<iprq[1][iq]<<"\n";
     }
//    cout << "END PERIODIC DOF\n";
      delete[] ihlq[0];
      delete[] ihlq[1];
  }
