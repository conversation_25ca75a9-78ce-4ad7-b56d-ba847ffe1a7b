   using namespace std;

#  include <domain/domain.h>
#  include <geo/slow.h>
#  include <fstream>

   cDomain::cDomain()
  {
      Int   ig,iek,ibk;

      ilev=0;
      bcrs=false;
      crs= NULL;
      fne= NULL;
      iqcrs=NULL;

      omega=0;
      setv( (Int)0,(Int)MxNBG,ZERO,omegb);
      

      dev=  NULL;
      coo=  NULL;

      ipr0= -1;
      ipr1= -1;

      ng=0;
      np=0;
      nq=0;
      nx=0;
      nv=0;
      nek=0;
      nbk=0;

      for( iek=0;iek<MxNSk;iek++ )
     {
         ne[iek]=0;
         nep[iek]=0;
         neq[iek]=0;
         siem[iek]=NULL; iem[iek]= NULL;
         siep[iek]=NULL; iep[iek]= NULL;
         sieq[iek]=NULL; ieq[iek]= NULL;
         nlhse[iek]= 0;
         nwrke[iek]= 0;
         nauxe[iek]= 0;
         smedium_marker[iek]=NULL;
         medium_marker[iek]=NULL;
     }


      for( ibk=0;ibk<MxNSk;ibk++ )
     {
         nbd[ibk]=0;
         nbp[ibk]=0;
         nbq[ibk]=0;
         nlhsb[ibk]= 0;
         nwrkb[ibk]= 0;
         nauxb[ibk]= 0;
     }
      for( ibk=0;ibk<MxNSk;ibk++ )
     {
         for( ig=0;ig<MxNBG;ig++ )
        {
            
            nb[ig][ibk]=0;
            sibp[ig][ibk]=NULL; ibp[ig][ibk]=NULL;
            sibq[ig][ibk]=NULL; ibq[ig][ibk]=NULL;
            sibb[ig][ibk]=NULL; ibb[ig][ibk]=NULL;
            sipbp[ig][ibk]=NULL; //ipbp[ig][ibk]=NULL;
        }
     }

      nprp=0;
      nprq=0;
      siprp=NULL;
      siprq=NULL;
      siprq0=NULL;
      siprq1=NULL;
      setv( (Int)0,(Int)MxNSk, (Int)0, nprb );
      setv( (Int)0,(Int)MxNSk, (Int*)NULL, siprb );
      

      setv( (Int)0,(Int)MxNBG, (Int)0,      nbb  );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL, sxb  );
      setv( (Int)0,(Int)MxNBG, string(""),  bgnm );
      setv( (Int)0,(Int)MxNBG, (Int*)NULL,  bcpu );

      sxp=NULL;
      sxq=NULL;
      qcst=NULL;
      qcpu=NULL;
      pcpu=NULL;
      rcpu=NULL;
 
      sidl="fallback";
      sido="fallback";
      swdl="fallback";
      swdo="fallback";
      fidl="fallback";
      fido="fallback";

  }

   cDomain::~cDomain()
  {
      Int   is,ig,iq,iek,ibk;

      dev=  NULL;

      for( ig=0;ig<MxNBG;ig++ )
     {
         delete[] sxb[ig]; sxb[ig]= NULL;
         delete[] bcpu[ig]; bcpu[ig]= NULL;
         nbb[ig]= 0;
         npb[ig]=0;
     }


      for( iek=0;iek<MxNSk;iek++ )
     {
         delete[] siem[iek]; siem[iek]=NULL; delete[] iem[iek]; iem[iek]=NULL;
         delete[] siep[iek]; siep[iek]=NULL; delete[] iep[iek]; iep[iek]=NULL;
         delete[] sieq[iek]; sieq[iek]=NULL; delete[] ieq[iek]; ieq[iek]=NULL;
         delete[] smedium_marker[iek]; smedium_marker[iek]=NULL; 
         delete[] medium_marker[iek]; medium_marker[iek]=NULL; 
//       delete[] sieg[iek]; sieg[iek]=NULL;
         ne[iek]=0;
         nep[iek]=0;
         nlhse[iek]= 0;
         nwrke[iek]= 0;
         nauxe[iek]= 0;
     }

      for( ibk=0;ibk<MxNSk;ibk++ )
     {
         for( ig=0;ig<ng;ig++ )
        {
            delete[] sibp[ig][ibk]; sibp[ig][ibk]=NULL; delete[] ibp[ig][ibk]; ibp[ig][ibk]=NULL;
            delete[] sibq[ig][ibk]; sibq[ig][ibk]=NULL; delete[] ibq[ig][ibk]; ibq[ig][ibk]=NULL;
            delete[] sibb[ig][ibk]; sibb[ig][ibk]=NULL; delete[] ibb[ig][ibk]; ibb[ig][ibk]=NULL;
            delete[] sipbp[ig][ibk]; sipbp[ig][ibk]=NULL; //delete[] ipbp[ig][ibk]; ipbp[ig][ibk]=NULL;


            nb[ig][ibk]=0;

//          delete[] sibg[ig][ibk]; sibg[ig][ibk]=NULL;
        }
         nbp[ibk]=0;
         nbq[ibk]=0;
         nlhsb[ibk]= 0;
         nwrkb[ibk]= 0;
         nauxb[ibk]= 0;
     }
      setv( (Int)0,(Int)MxNBG,(Int)0,nbb );
      ng=0;

      nprp=0;
      nprq=0;
      delete[] siprp; siprp=NULL;
      delete[] siprq; siprq=NULL;
      delete[] siprq0; siprq0=NULL;
      delete[] siprq1; siprq1=NULL;
      setv( (Int)0,(Int)MxNSk, (Int)0, nprb );
      for( ibk=0;ibk<nbk;ibk++ )
     {
         delete[] siprb[ibk]; siprb[ibk]=NULL;
     }
      

      delete[] sxp; sxp=NULL;
      delete[] sxq; sxq=NULL;
      delete[] qcst; qcst=NULL;
      delete[] qcpu; qcpu=NULL;
      delete[] pcpu; pcpu=NULL;
      delete[] rcpu; rcpu=NULL;

      np=0;
      nq=0;
      nx=0;
      nv=0;
      nek=0;
      nbk=0;

      delete coo; coo=NULL;
      omega=0;
      setv( (Int)0,(Int)MxNBG,ZERO,omegb);

      delete crs; crs= NULL;
      fne= NULL;
      delete[] iqcrs; iqcrs=NULL;
      ilev=0;
  }

   void cDomain::assgniep( Int iek, Int n, Int *ip, Int *iq, Int *im, Int *marker )
  {
      if( (ne[iek] == 0) && (!siep[iek]) && (!sieq[iek]) && (!siem[iek]) )
     {
         ne[iek]=  n;
         siep[iek]= ip;
         sieq[iek]= iq;
         siem[iek]= im;
         iep[iek]= new Int*[nep[iek]];
         ieq[iek]= new Int*[neq[iek]];
         iem[iek]= new Int*[1];
         subv( nep[iek],ne[iek], siep[iek],iep[iek] );
         subv( neq[iek],ne[iek], sieq[iek],ieq[iek] );
         subv(   (Int)1,ne[iek], siem[iek],iem[iek] );

         smedium_marker[iek] = marker;
         medium_marker[iek]= new Int*[1];
         subv( (Int)1,ne[iek], smedium_marker[iek],medium_marker[iek] ); 
     }
      else
     {
         cout << "connectivity for element type already assigned\n";
         exit(1);
     }
  }

   void cDomain::assgnibp( Int ig, Int ibk, Int n, Int *ip, Int *iq, Int *ib )
  {
      if( (nb[ig][ibk] == 0) && (!sibp[ig][ibk]) && (!sibq[ig][ibk]) && (!sibq[ig][ibk]) )
     {
         nb[ig][ibk]=  n;
         sibp[ig][ibk]= ip;
         sibq[ig][ibk]= iq;
         sibb[ig][ibk]= ib;

         ibp[ig][ibk]= new Int*[nbp[ibk]];
         ibq[ig][ibk]= new Int*[nbq[ibk]];
         ibb[ig][ibk]= new Int*[nbd[ibk]];
      
         subv( nbp[ibk],nb[ig][ibk], sibp[ig][ibk],ibp[ig][ibk] );
         subv( nbq[ibk],nb[ig][ibk], sibq[ig][ibk],ibq[ig][ibk] );
         subv( nbd[ibk],nb[ig][ibk], sibb[ig][ibk],ibb[ig][ibk] );

     }
      else
     {
         cout << "connectivity for boundary element type already assigned\n";
         exit(1);
     }
  }

   void cDomain::assgnipb()
  {
      Int ig, ibk, ip, jp, ib;
      Int *ihlp;

      ihlp = new Int [np];
      for(ig=0; ig<ng; ig++)
     {
         setv(0, np, 0, ihlp);
         for(ibk=0; ibk<nbk; ibk++)
        {
            for(ip=0; ip<nbp[ibk]; ip++)
           {
               for(ib=0; ib<nb[ig][ibk]; ib++)
              {
                  jp = ibp[ig][ibk][ip][ib];
                  ihlp[jp] = 1;
              }
           }

        }

         jp=0;
         for(ip=0; ip<np; ip++)
        {
            if(ihlp[ip]==1)
           {
               ihlp[ip] = jp;
               jp++;
           }
        }

         for(ibk=0; ibk<nbk; ibk++)
        {
            sipbp[ig][ibk] = new Int [nbp[ibk]*nb[ig][ibk]];
            //ipbp[ig][ibk] = new Int *[nbp[ibk]];
            //subv( nbp[ibk],nb[ig][ibk], sipbp[ig][ibk],ipbp[ig][ibk] );
            ipbp[ig][ibk].subv( nbp[ibk],nb[ig][ibk], sipbp[ig][ibk] );
            for(ip=0; ip<nbp[ibk]; ip++)
           {
                for(ib=0; ib<nb[ig][ibk]; ib++)
               {
                   jp = ibp[ig][ibk][ip][ib];
                   //ipbp[ig][ibk][ip][ib] = ihlp[jp];
                   ipbp[ig][ibk](ip,ib) = ihlp[jp];
               }
           }
        }
     }
      delete[] ihlp; ihlp=NULL;
  }

   void cDomain::assgnxp( Int n, Real *x )
  { 
      if( np == 0 )
     {
         np= n; 
         sxp=x; 
         xp.subv( nx,np, sxp );
     }
      else
     {
         cout << "points already assigned\n";
         exit(1);
     }
  };

   void cDomain::assgnxq( Int n, Real *x )
  { 
      if( nq == 0 )
     {
         nq= n; 
         sxq=x; 
         xq.subv( nx,nq, sxq );
     }
      else
     {
         cout << "points already assigned\n";
         exit(1);
     }
  };

/* void cDomain::getxp( Int *n, Int *m, Real **x )
  {
     *n= np;
     *m= nx;
     *x= sxp;
  }*/

   void cDomain::getxq( Int *n, Int *m, Real **y )
  {
      if( crs )
     {
         crs->getxq( n,m,y );
     }
      else
     {
        *n= nq;
        *m= nx;
        *y= sxq;
     }
  }

   void cDomain::getselfxq( Int *n, Real *var[3] )
  {
     *n= nq;
      subv(nx, nq, sxq, var);
  }


   void cDomain::getqcost( Real **var )
  {
      Real *wrk;
      if( crs )
     {
         crs->getqcost( var );
     }
      else
     {
        *var= qcst; 
     }
  }


   void cDomain::getqcpu( Int **var )
  { 
     *var= qcpu; 
  };


   void cDomain::assgndev( cDevice *d )
  {
      if( !dev )
     {
         dev= d;
     }
      else
     {
         cout << "cannot reassign device\n";
         exit(1);
     }
  }

   void cDomain::assgnv( Int mv )
  {
      nv= mv;
  }

   void cDomain::assgncoo( cCosystem *var )
  {
      if( !coo )
     {
         coo= var;
         nx= coo->getnx();
     }
      else
     {
         cout << "coordinate system already set\n";
         exit(1);
     }
  }

   void cDomain::boundaries( Int n, string *data, string *lib, string *obj )
  {
      Int ig;
      if( ng == 0 )
     {
         ng= n;
         for( ig=0;ig<ng;ig++ )
        {
            bgnm[ig]= data[ig];
            bpl[ig]= lib[ig];
            bpo[ig]= obj[ig];
        }
     }
      else
     {
         cout <<"boundaries already assigned\n";
         exit(0);
     }
  }

   void cDomain::assgnxb( Int ig,  Int n, Real *x )
  {
      if( !sxb[ig] )
     {
         nbb[ig]= n;
         sxb[ig]= x; xb[ig].subv( nx,nbb[ig],sxb[ig] ); 
     }
      else
     {
         cout << "sxb has already been assigned\n";
         exit(0);
     }
  }



   void cDomain::attach( cDomain *dmn )
  {
//      Int      iq,ix,id,jq;
//      cKdTree *kdt;
//      Real     x0[3],x1[3];
//      Real     d,dmin;
//
//      ifstream  fle;
//      string    fnme, path;
//
//      if( !crs )
//     {
//        
//         id= dev->getrank();
//
//         bcrs= true;
//         crs= dmn;
//         crs->ilev= ilev+1;
//
//         crs->fne= this;
//
//         iqcrs= new Int[nq];
//
//         cout << "attach domain "<<dmn<<" as coarser level to "<<this<<"\n";
//         cout << "construction of iqcrs goes here\n";
//
//
//         cout << "this should be partitioning the coarser domain\n";
//         cout << "crs nq is "<<crs->nq<<"\n";
//         cout << "fine nq is "<<nq<<"\n";
//
//         if( crs->nq < 5 )
//        {
//            for( iq=0;iq<nq;iq++ )
//           {
//               line( iq, nx,xq, x0 );
//               slow( 0,crs->nq,nx,crs->xq,x0,&id,&d );
//               iqcrs[iq]=id;
//           }
//        }
//         else
//        {
//            path= dev->getcpath();
//            fnme= path+ "/" + dev->getname() + ".mg."+strc( ilev );
//            cout << "open multigrid " << fnme << "\n";
//            fle.open(fnme.c_str());
//            if(!fle.good())
//           {
//               cout << "Error: cannot open multigrid file " << fnme << "\n";
//               exit(0);
//           }
//            for(iq=0; iq<nq; iq++)
//           {
//               fle >> jq;
//               iqcrs[iq] = jq;
//           }
//            fle.close();
//
///*            kdt= new cKdTree();
//            kdt->build( nx,crs->nq, crs->xq );
//            for( iq=0;iq<nq;iq++ )
//           {
//               line( iq, nx,xq, x0 );
//               kdt->nearest( x0, &id, &d );
//               iqcrs[iq]=id;
//           }
//            delete kdt; kdt=NULL;*/
//        }
//                  
///*       ofstream  fle;
//         fnme= "coarsening.links.dat."+strc(ilev)+"."+strc(id);
//         fle.open( fnme.c_str() );
//         for( iq=0;iq<nq;iq++ )
//        {
//            cout << "looking for "<<x0[0]<<" "<<x0[1]<<" found near "<<id<<" "<<iqcrs[iq]<<"\n";
//            fle << "\n";
//            fle << "#\n";
//            fle << "\n";
//            for( ix=0;ix<nx;ix++ ){ fle << xq[ix][iq]<<" "; }; fle << "\n";
//            for( ix=0;ix<nx;ix++ ){ fle << crs->xq[ix][iqcrs[iq]]<<" "; }; fle << "\n";
//        }
//         fle.close();*/
//
//     }
//      else
//     {
//         cout << "coarser level already attached "<<this<<" "<<crs<<"\n";
//     }
////    stophere();
  }

   void cDomain::readmgpart( cDomain *dmn )
  {
//      Int      iq,ix,id,jq, icpu, tmpnq;
//      cKdTree *kdt;
//      Real     x0[3],x1[3];
//      Real     d,dmin;
//      Int     *ihlp=NULL;
//
//      ifstream  fle;
//      string    fnme, path;
//
//      if( !crs )
//     {
//        
//         icpu= dev->getrank();
//
//         bcrs= true;
//         crs= dmn;
//         crs->ilev= ilev+1;
//
//         crs->fne= this;
//
//         iqcrs= new Int[nq];
//
//
//         if( crs->nq < 5 )
//        {
//            for( iq=0;iq<nq;iq++ )
//           {
//               line( iq, nx,xq, x0 );
//               slow( 0,crs->nq,nx,crs->xq,x0,&id,&d );
//               iqcrs[iq]=id;
//           }
//        }
//         else
//        {
//            kdt= new cKdTree();
//            kdt->build( nx,crs->nq, crs->xq );
//
//            ihlp = new Int [nq];
//
//            path= dev->getcpath();
//            fnme= path+ "/mg."+strc( icpu )+"."+strc( ilev );
//            cout << "read partitioned multigrid " << fnme << "\n";
//            fle.open(fnme.c_str());
//            if(!fle.good())
//           {
//               cout << "Error: cannot open partitioned multigrid file " << fnme << "\n";
//               exit(0);
//           }
//            fle >> tmpnq;
//            for(iq=0; iq<tmpnq; iq++)
//           {
//               fle >> jq;
//               for(ix=0; ix<nx; ix++)
//              {
//                  fle >> x0[ix];
//              }
//               kdt->nearest( x0, &id, &d );
//               iqcrs[jq]=id;
//               ihlp[iq] = jq;
//           }
//            delete kdt; kdt=NULL;
//        }
//                  
//         ofstream  ofle;
//         fnme= "coarsening.links.dat."+strc(icpu)+"."+strc(ilev);
//         ofle.open( fnme.c_str() );
//         for( iq=0;iq<tmpnq;iq++ )
//        {
//            jq = ihlp[iq];
//            //cout << "looking for "<<x0[0]<<" "<<x0[1]<<" found near "<<id<<" "<<iqcrs[iq]<<"\n";
//            ofle << "\n";
//            ofle << "#\n";
//            ofle << "\n";
//            for( ix=0;ix<nx;ix++ ){ ofle << xq(ix,jq)<<" "; }; ofle << "\n";
//            for( ix=0;ix<nx;ix++ ){ ofle << crs->xq(ix,iqcrs[jq])<<" "; }; ofle << "\n";
//        }
//         ofle.close();
//
//     }
//      else
//     {
//         cout << "coarser level already attached "<<this<<" "<<crs<<"\n";
//     }
////    stophere();
  }

   void cDomain::getcost( Real *cost )
  {
      Real wrk=0.;
      Int  iq;

      getcost0( &wrk ); 
      cout << "at entry to getcost "<<wrk<<"\n";
      cout << "at entry to getcost "<<*cost<<"\n";
      if( fne )
     {
         for( iq=0;iq<fne->nq ;iq++ )
        {
            qcst[ fne->iqcrs[iq] ]+= fne->qcst[iq];
        }
     }
      else
     {
        *cost=0;
     }
      if( crs )
     {
         crs->getcost( &wrk );
     }
    (*cost)+= wrk;
      cout << "at exit to getcost "<<wrk<<" "<<*cost<<"\n";
  }

   void cDomain::assstp( string sil, string sio, string fil, string fio, string swl, string swo )
  {
      sidl= sil; 
      sido= sio; 
      fidl= fil;  
      fido= fio; 
      swdl= swl; 
      swdo= swo;
  }


   void cDomain::save()
  {
      if( crs )
     {
         crs->save();
     }
  }

   void cDomain::makebox( box_t *b )
  {
      Int ix,ip;
      
      Real *sx;
      cAu3xView<Real> x;
      sx= new Real[nx*np];
      x.subv( nx,np, sx );
      coo->bcoor( 0,np, xp,x );

      reset( b );
      add( 0,np, x,b );

      delete[] sx;
  }

   void cDomain::makeboxes( Int *n, Int *m, box_t **b, Int **i, string **str )
  {

      box_t      bx;
      Int        dsz=5;
      Int        ist,ien;
      Int        ib,ig,ip,jp,ibk;
      Int       *sipb,*ipb[1];
      Real      *sy;
      cAu3xView<Real> y;

      for( ig=0;ig<ng;ig++ )
     {
         ist= 0;
         bpts( ig, &ien, &sipb );

         sy=   new Real[ien*3];
         y.subv( 3,ien, sy );
         subv( 1,ien, sipb,ipb ); 


         coo->bcoor( ist,ien, ipb,xp, y );
         reset( &bx );
         add( ist,ien, y, &bx );

         dev->gmin( 2, bx.x0 );
         dev->gmax( 2, bx.x1 );

         grow( 1.e-5,&bx );

//       cout << dev->getname()<<" surface "<<ig<<" "<< bx.x0[0]<<" "<<bx.x0[1]<<" "<<bx.x1[0]<<" "<<bx.x1[1]<<"\n";
         if( *n == *m )
        {
           *m=*n; realloc( m,dsz, b );
           *m=*n; realloc( m,dsz, i );
           *m=*n; realloc( m,dsz, str );
        }
       (*i)[(*n)]=   ig;  
       (*b)[(*n)]=   bx;
       (*str)[(*n)]= bgnm[ig];
       (*n)++;
         delete[] sipb;
         delete[] sy;

     }
      

  }

   void cDomain::bpts( Int ig, Int *n, Int **sipb )
  {
      Int        ist,ien;
      Int        ib,ip,jp,ibk;
      Int       *ipb[1];
      Int       *ihlp;

      ihlp= new Int[np];

      setv( 0,np, (Int)0,ihlp );
      for( ibk=0;ibk<nbk;ibk++ )
     {
        for( jp=0;jp<nbp[ibk];jp++ )
       {
           for( ib=0;ib<nb[ig][ibk];ib++ )
          {
              ip= ibp[ig][ibk][jp][ib];
              ihlp[ip]=1;
          }
       }
     }

      jp=0;
      for( ip=0;ip<np;ip++ ) 
     {
         if( ihlp[ip] == 1 )
        {
            jp++;
        }
     }

     *n= jp;
     *sipb= new Int[jp];
      subv( 1,jp, *sipb,ipb );
      setv( 0,jp, (Int)-1,ipb[0] );

      jp=0;
      for( ip=0;ip<np;ip++ ) 
     {
         if( ihlp[ip] == 1 )
        {
            ipb[0][jp]= ip;
            jp++;
        }
     }
  
      delete[] ihlp;
  }

   void cDomain::request( Int ig, Int *n, Int *m, Int *l, Real **x )
  {
      cout << "DOMAIN BCOORD\n";
      *n=0;
      *m=0;
      *x=NULL;
  }

   void cDomain::service( Int ig, Int nx, Int nv, Int nq, Real *x[], Real **q, bool bposix )
  {
      cout << "DOMAIN BVALS\n";
      if( nq > 0 )
     {
        *q= new Real[nq*(nv+1)];
         setv( 0,nq*(nv+1), ZERO, *q );
     }
      else
     {
         *q=NULL;
     }
  }

   void cDomain::service( Int ig, Int nx, Int nv, Int nq, Real *x, Real **q )
  {
      cout << "DOMAIN BVALS\n";
      if( nq > 0 )
     {
        *q= new Real[nq*(nv+1)];
         setv( 0,nq*(nv+1), ZERO, *q );
     }
      else
     {
         *q=NULL;
     }
  }

   void cDomain::accept( Int ig, Int nv, Int nq, Real *sq, Real *w )
  {
//       virtual void  accept( Int, Int, Int, Int, Real *[], Real *[] );
  }
