   using namespace std;

#  include <domain/domain.h>

   void cDomain::pickle( size_t *len, pickle_t *buf )
  {
      Int    ig;
      pckle( len, sidl,buf );
      pckle( len, sido,buf );
      pckle( len, fidl,buf );
      pckle( len, fido,buf );
      pckle( len, swdl,buf );
      pckle( len, swdo,buf );

      pckle( len,ng, buf );
      for( ig=0;ig<ng;ig++ )
     {
         pckle( len,bgnm[ig],buf );
         pckle( len, bpl[ig],buf );
         pckle( len, bpo[ig],buf );
     }
  }

   void cDomain::unpickle( size_t *len, pickle_t buf )
  {
      Int    ig;
      unpckle( len, &sidl,buf );
      unpckle( len, &sido,buf );
      unpckle( len, &fidl,buf );
      unpckle( len, &fido,buf );
      unpckle( len, &swdl,buf );
      unpckle( len, &swdo,buf );

      unpckle( len,&ng, buf );
      for( ig=0;ig<ng;ig++ )
     {
         unpckle( len,bgnm+ig,buf );
         unpckle( len, bpl+ig,buf );
         unpckle( len, bpo+ig,buf );
     }
  }
