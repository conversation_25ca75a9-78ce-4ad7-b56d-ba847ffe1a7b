   using namespace std;

#ifdef CGNS
#  include <cgnslib.h>
#endif
#  include <domain/cfd/domain.h>

   void cFdDomain::output_cgns()
  {
#ifdef CGNS
      cgsize_t isize[3][1];
      cgsize_t nelem_start,nelem_end;
      Int index_file,icelldim,iphysdim;
      Int index_base,index_zone,index_coord;
      Int nbdyelem,index_section;
      char basename[33],zonename[33];
      Real *coord[3], *qsol[20];
 
      string path, fnm1, devnm, bnm;
      Int ip, iek, ne_all, ie, ick, ig; 
      Int npe[6], npc[3], nsection;

      const Int MXNSEC=1000;
      cgsize_t *tri, *quad, *tet, *hex, *pyra, *prism;
      cgsize_t *btri[MXNSEC], *bquad[MXNSEC], *bseg[MXNSEC];

      tri  = NULL;
      quad = NULL;
      tet  = NULL;
      hex  = NULL;
      pyra = NULL;
      prism= NULL;
      for(ie=0; ie<MXNSEC; ie++)
     {
         btri[ie]  = NULL;
         bquad[ie] = NULL;
         bseg[ie]  = NULL;
     }

      npc[0] = 2;
      npc[1] = 3;
      npc[2] = 4;
 
      npe[0] = 3;
      npe[1] = 4;
      npe[2] = 4;
      npe[3] = 5;
      npe[4] = 6;
      npe[5] = 8;
 
      ne_all = 0;
      for(iek=0; iek<6; iek++)
     {
         if(ne[iek]>0)
        {
                 if(iek==0)   tri = new cgsize_t [ne[iek]*npe[iek]];
            else if(iek==1)  quad = new cgsize_t [ne[iek]*npe[iek]];
            else if(iek==2)   tet = new cgsize_t [ne[iek]*npe[iek]];
            else if(iek==3)  pyra = new cgsize_t [ne[iek]*npe[iek]];
            else if(iek==4) prism = new cgsize_t [ne[iek]*npe[iek]];
            else if(iek==5)   hex = new cgsize_t [ne[iek]*npe[iek]];
 
            for(ie=0; ie<ne[iek]; ie++)
           {
              for(ip=0; ip<npe[iek]; ip++)
             {
                     if(iek==0)   tri[ie*npe[iek] + ip] = iep[iek][ip][ie]+1;
                else if(iek==1)  quad[ie*npe[iek] + ip] = iep[iek][ip][ie]+1;
                else if(iek==2)   tet[ie*npe[iek] + ip] = iep[iek][ip][ie]+1;
                else if(iek==3)  pyra[ie*npe[iek] + ip] = iep[iek][ip][ie]+1;
                else if(iek==4) prism[ie*npe[iek] + ip] = iep[iek][ip][ie]+1;
                else if(iek==5)   hex[ie*npe[iek] + ip] = iep[iek][ip][ie]+1;
             }
           }
            ne_all += ne[iek];   
        }
     }   
   
  /* WRITE X, Y, Z GRID POINTS TO CGNS FILE */
  /* open CGNS file for write */
      //fnm1 = "final.cgns";
      fnm1= dev->getcpath()+"/"+dev->getname()+".cgns";

      if (cg_open(fnm1.c_str(),CG_MODE_WRITE,&index_file)) cg_error_exit();
  /* create base (user can give any name) */
      strcpy(basename,"Base");
      icelldim=nx;
      iphysdim=nx;
      cg_base_write(index_file,basename,icelldim,iphysdim,&index_base);
  /* define zone name (user can give any name) */
      strcpy(zonename,(dev->getname()).c_str());
  /* vertex size */
      isize[0][0]=np;
  /* cell size */
      isize[1][0]=ne_all;
  /* boundary vertex size (zero if elements not sorted) */
      isize[2][0]=0;
 
  /* create zone */
      cg_zone_write(index_file,index_base,zonename,isize[0],CGNS_ENUMV(Unstructured),&index_zone);


  /* write grid coordinates (user must use SIDS-standard names here) */
      subv( nx,np, sxp, coord );
#ifdef FP32
      cg_coord_write(index_file,index_base,index_zone,CGNS_ENUMV(RealSingle),"CoordinateX",
          coord[0],&index_coord);
      cg_coord_write(index_file,index_base,index_zone,CGNS_ENUMV(RealSingle),"CoordinateY",
          coord[1],&index_coord);
      cg_coord_write(index_file,index_base,index_zone,CGNS_ENUMV(RealSingle),"CoordinateZ",
          coord[2],&index_coord);
#else
      cg_coord_write(index_file,index_base,index_zone,CGNS_ENUMV(RealDouble),"CoordinateX",
          coord[0],&index_coord);
      cg_coord_write(index_file,index_base,index_zone,CGNS_ENUMV(RealDouble),"CoordinateY",
          coord[1],&index_coord);
      cg_coord_write(index_file,index_base,index_zone,CGNS_ENUMV(RealDouble),"CoordinateZ",
          coord[2],&index_coord);
#endif
  
  /* set element connectivity: */
 
  /* unsorted boundary elements */
      nsection=0;
      nbdyelem=0;
      nelem_start=1;
      for(iek=0; iek<6; iek++)
     { 
         if(ne[iek]>0)
        {
            nelem_end=nelem_start+ne[iek] - 1; //watch out this "-1"
            if(iek==0)
           {
              cg_section_write(index_file,index_base,index_zone,"Elem_tri",CGNS_ENUMV(TRI_3),nelem_start,
                               nelem_end,nbdyelem,tri,&index_section);
           }
            else if(iek==1)
           {
              cg_section_write(index_file,index_base,index_zone,"Elem_qua",CGNS_ENUMV(QUAD_4),nelem_start,
                               nelem_end,nbdyelem,quad,&index_section);
           }
            else if(iek==2)
           {
              cg_section_write(index_file,index_base,index_zone,"Elem_tet",CGNS_ENUMV(TETRA_4),nelem_start,
                               nelem_end,nbdyelem,tet,&index_section);
           }
            else if(iek==3)
           {
              cg_section_write(index_file,index_base,index_zone,"Elem_pyr",CGNS_ENUMV(PYRA_5),nelem_start,
                               nelem_end,nbdyelem,pyra,&index_section);
           }
            else if(iek==4)
           {
              cg_section_write(index_file,index_base,index_zone,"Elem_pri",CGNS_ENUMV(PENTA_6),nelem_start,
                               nelem_end,nbdyelem,prism,&index_section);
           }
            else if(iek==5)
           {
              cg_section_write(index_file,index_base,index_zone,"Elem_hex",CGNS_ENUMV(HEXA_8),nelem_start,
                               nelem_end,nbdyelem,hex,&index_section);
           }
            cout << "volume element type " << iek << ":  " << nelem_start << " ------> " << nelem_end << "\n";
            nelem_start=nelem_end+1; //watch out this "+1"
            nsection++;
        }
     }
 
      for(ig=0; ig<ng; ig++)
     {
         for(ick=0; ick<3; ick++)
        { 
            if(nb[ig][ick]>0)
           {
               nelem_end=nelem_start+nb[ig][ick] - 1; //watch out this "-1"
                    if(ick==0) { bseg[nsection] = new cgsize_t [nb[ig][ick]*npc[ick]];}
               else if(ick==1) { btri[nsection] = new cgsize_t [nb[ig][ick]*npc[ick]];}
               else if(ick==2) {bquad[nsection] = new cgsize_t [nb[ig][ick]*npc[ick]];}
 
               for(ie=0; ie<nb[ig][ick]; ie++)
              {
                 for(ip=0; ip<npc[ick]; ip++)
                {
                        if(ick==0)   bseg[nsection][ie*npc[ick] + ip] = ibp[ig][ick][ip][ie]+1;
                   else if(ick==1)   btri[nsection][ie*npc[ick] + ip] = ibp[ig][ick][ip][ie]+1;
                   else if(ick==2)  bquad[nsection][ie*npc[ick] + ip] = ibp[ig][ick][ip][ie]+1;
                }
              }
 
               //cgns are expecting unique names for each section
                    if(ick==0) bnm = bgnm[ig] + "_seg";
               else if(ick==1) bnm = bgnm[ig] + "_tri";
               else if(ick==2) bnm = bgnm[ig] + "_quad";
               if(ick==0)
              {
                  cg_section_write(index_file,index_base,index_zone,bnm.c_str(),CGNS_ENUMV(BAR_2),nelem_start,
                                   nelem_end,nbdyelem,bseg[nsection],&index_section);
              }
               else if(ick==1)
              {
                  cg_section_write(index_file,index_base,index_zone,bnm.c_str(),CGNS_ENUMV(TRI_3),nelem_start,
                                   nelem_end,nbdyelem,btri[nsection],&index_section);
              }
               else if(ick==2)
              {
                  cg_section_write(index_file,index_base,index_zone,bnm.c_str(),CGNS_ENUMV(QUAD_4),nelem_start,
                                   nelem_end,nbdyelem,bquad[nsection],&index_section);
              }
               cout << "boundary element type " << ick << ":  " << nelem_start << " ------> " << nelem_end 
                    << " with label " << bnm << "\n";
               nelem_start=nelem_end+1; //watch out this "+1"
               nsection++;
           }
        }
     }
 
      //flow solution
      Int index_flow;
      Int iv, nv0;
      string varnm[20];
      nv0 = fld->getnv0();
      varnm[0] = "VelocityX";
      varnm[1] = "VelocityY";
      varnm[2] = "VelocityZ";
      varnm[3] = "Temperature";
      varnm[4] = "Pressure";
      if(fld->gettype()==mfroe_gas_cht)
     {
         varnm[5] = "Medium";
     }
      if(fld->gettype()==mfreacting_gas)
     {
         varnm[5] = "Mixture-fraction";
     }
      if(vsc->gettype()==komegalowre_visc)
     {
         varnm[nv0] = "K";
         varnm[nv0+1] = "Omega";
     }
 
     cg_sol_write(index_file,index_base,index_zone,"FlowSolution",CGNS_ENUMV(CellCenter),&index_flow);
 
 /* write flow solution (user must use SIDS-standard names here) */
     subv( nv,nq, sq, qsol );
     for(iv=0; iv<nv; iv++)
    {
        Int index_field;
#ifdef FP32
        cg_field_write(index_file,index_base,index_zone,index_flow,
                       CGNS_ENUMV(RealSingle),varnm[iv].c_str(),qsol[iv],&index_field);
#else
        cg_field_write(index_file,index_base,index_zone,index_flow,
                       CGNS_ENUMV(RealDouble),varnm[iv].c_str(),qsol[iv],&index_field);
#endif
    }
 
  /* close CGNS file */
      cg_close(index_file);
      cout << "\nSuccessfully wrote unstructured grid to file " << fnm1 << "\n";
  
      delete[] tri  ;tri  = NULL;
      delete[] quad ;quad = NULL;
      delete[] tet  ;tet  = NULL;
      delete[] hex  ;hex  = NULL;
      delete[] pyra ;pyra = NULL;
      delete[] prism;prism= NULL;
      for(ie=0; ie<MXNSEC; ie++)
     {
         delete[] btri[ie] ; btri[ie]  = NULL;
         delete[] bquad[ie]; bquad[ie] = NULL;
         delete[] bseg[ie] ; bseg[ie]  = NULL;
     }
#endif
  }

