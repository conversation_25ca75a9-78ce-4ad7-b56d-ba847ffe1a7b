
   using namespace std;
#  include <iomanip>
#  include <domain/cfd/domain.h>

   void cFdDomain::smooth2()
  {
      Int in;

      //cout << "mutex free smooth\n";

      if( unst )
     {
         advance2();
     }
      else
     {
         assert(0);
         maverage(false);
     }
  }

   void cFdDomain::advance2()
  {
      Int in;
      Int iq,iv, il;
      Real *r1;

      //cout << "mutex free advance\n";

// save old conserved variables
      if(ntlv==3)
     {
         for(il=ntlv-1; il>0; il--)
        {
           #pragma acc parallel loop gang vector \
            present(su[0][0:nv*nq],su[1][0:nv*nq],su[2][0:nq],this) \
            default(none)
            for( iq=0;iq<nq;iq++ )
           {
               for( iv=0;iv<nv;iv++ )
              {
                  //u[il][iv][iq]= u[il-1][iv][iq];
                  su[il][ADDR(iv,iq,nq)]= su[il-1][ADDR(iv,iq,nq)];
              }
           }
        }
     }
      else
     {
        #pragma acc parallel loop gang vector\
         present(su[0][0:nv*nprq],su[1][0:nv*nq],su[2][0:nq],this) \
         default(none)
         for( iq=0;iq<nq;iq++ )
        {
            for( iv=0;iv<nv;iv++ )
           {
               //u[1][iv][iq]= u[0][iv][iq];
               su[1][ADDR(iv,iq,nq)]= su[0][ADDR(iv,iq,nq)];
           }
        }
     }

      r1 = new Real [nv];

      for( in=0;in<npre;in++ )
     {
         //cout << "newton iteration "<<in<<"\n";
         cfl= min( cfl1,cfl*dcfl );
         newton();

         resd( rhs,r1 );
         if(dev->getrank()==0 && ilev==0)
        {
           *flg << cfl<<" ";
           for( iv=0;iv<nv;iv++ )
          {
              *flg << r1[iv]<<" ";
          }
           *flg << "\n";
           flg->flush();
        }
         nexc++;
         if(dev->getrank()==0 && ilev==0)
        {
            cout << " Iteration " << std::setw(8)<< nexc << " ";
            cout << std::setw(8)<< dev->getname() << " ";
            cout << std::scientific << std::setprecision(7)
              << std::setw(16);
            for(iv=0; iv<nv; iv++)
           {
               cout << r1[iv] << " ";
           }
            cout << "\n";
        }
     } 

      Int i;
      i = tm/dtm;
      //if(i%10==0) tecplot();
      //tecplot();

      //cout<<"physical time " << tm << " with time increment " << dtm << "\n";
      //cout << "Time level " << tm/dtm << " with time step " << dtm << "\n";
      tm+= dtm;

      delete[] r1; r1=NULL;
  }

   void cFdDomain::smooth3()
  {
      Int in;
      Real r1_norm;

//      start_acc_device();
      //cout << "mutex free smooth\n";

      Real *r1;
      Int iv;

      r1= new Real[nv];

      for( in=0;in<npre;in++ )
     {
         cfl= min( cfl1,cfl*dcfl );
         newton();

         resd( rhs,r1 );
         //r1_norm = 0;
         //resd2( rhs, &r1_norm );
         //r1_norm = sqrt(r1_norm);
         if(dev->getrank()==0 && ilev==0)
        {
           *flg << cfl<<" ";
           for( iv=0;iv<nv;iv++ )
          {
              *flg << r1[iv]<<" ";
          }
           *flg << "\n";
          // *flg << r1_norm<< "\n";
           flg->flush();
        }

         nexc++;
         if(dev->getrank()==0 && ilev==0)  
        {
            cout << " Iteration " << std::setw(12)<< nexc << " ";
            cout << std::setw(8)<< dev->getname() << " ";
            cout << std::scientific << std::setprecision(10)
              << std::setw(16);
            for(iv=0; iv<nv; iv++)
           {
               cout << r1[iv] << " ";
           }
            cout << "\n";
        }
     } 
//      if( crs )
//     {
//         mg_restrict();
//         crs->smooth3();
//         prolong();
//     }
//      for( in=0;in<npost;in++ )
//     {
//         cfl= min( cfl1,cfl*dcfl );
//         newton();
//
//         resd( rhs,r1 );
//         //r1_norm = 0;
//         //resd2( rhs, &r1_norm );
//         //r1_norm = sqrt(r1_norm);
//         if(dev->getrank()==0 && ilev==0)
//        {
//           *flg << cfl<<" ";
//           for( iv=0;iv<nv;iv++ )
//          {
//              *flg << r1[iv]<<" ";
//          }
//           *flg << "\n";
//          // *flg << r1_norm<< "\n";
//           flg->flush();
//        }
//
//         nexc++;
//         if(dev->getrank()==0 && ilev==0)  
//        {
//            cout << " Iteration " << std::setw(8)<< nexc << " ";
//            cout << std::setw(8)<< dev->getname() << " ";
//            cout << std::scientific << std::setprecision(7)
//              << std::setw(16);
//            for(iv=0; iv<nv; iv++)
//           {
//               cout << r1[iv] << " ";
//           }
//            cout << "\n";
//        }
//     }
      delete[] r1; r1=NULL;
      maverage(false);
//      exit_acc_device();

  }
