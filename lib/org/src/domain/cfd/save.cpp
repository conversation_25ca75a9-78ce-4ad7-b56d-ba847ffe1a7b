   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::save()
  {
      size_t       l=0;
      size_t       len=0;
      pickle_t     buf=NULL;
      string       fnme;
      Int          icpu,ig;

      FILE        *f;   

      cPlugin     *vwp;
// typedef void (*swl_t)( cDomain *, Int , Int , Real *[], Int , Real *[] );
   typedef void (*vwl_t)( cFdDomain *, Int , Int , Int , Int , Int , Int , Int * , Int * , Int **[] , Int **[] , Real *[], Real *[], Real *[], Real *[], Int , Int , Int * , Int [MxNBG][MxNSk], Int **[MxNBG][MxNSk] );
      vwl_t        vwl_f;

      cDomain::save();

     #ifdef _OPENACC
          Real *fptr;
         #pragma acc update host (sq[0:nv*nq])
         #pragma acc update host (saux[0:naux*nq])
          fptr = su[0];
         #pragma acc update host (fptr[0:nv*nq])
          fptr = su[1];
         #pragma acc update host (fptr[0:nv*nq])
          fptr = su[2];
         #pragma acc update host (fptr[0:nv*nq])
          for( ig=0;ig<ng;ig++ )
         {
              fptr = sqb[ig];
             #pragma acc update host (fptr[0:nv*nbb[ig]])
              fptr = sauxb[ig];
             #pragma acc update host (fptr[0:naux*nbb[ig]])
         }
     #endif


      fnme= dev->getcpath();
      icpu= dev->getrank();
      fnme= fnme+"/"+dev->getname()+".restart.q."+strc(ilev)+"."+strc(icpu);
      //cout << "save solution to " << fnme << "\n";

     // string line;
     // line= "mv -f "+fnme+" "+fnme+".bck";
     // system( line.c_str() );


      f= fopen(fnme.c_str(),"w");
      dof->pickle( &len,&buf );
      pts->pickle( &len,&buf );

      l= fwrite( &tm,1,sizeof(tm),f );
      l= fwrite( &len,1,sizeof(len),f );
      l= fwrite(  buf,1,        len,f );
      delete[] buf;buf= NULL; len=0;

      pts->write( nx,sxp,f );
      dof->write( nx,sxq,f );
      dof->write( nv,sq,f );
      dof->write( naux,saux,f );
      l= fwrite( &ntlv,1,sizeof(ntlv),f );
      for(Int il=0; il<ntlv; il++)
     {
         dof->write( nv,su[il],f );
     }
      l= fwrite( &cfl,1,sizeof(cfl),f );

      fclose(f);

      for( ig=0;ig<ng;ig++ )
     {
         fnme= dev->getcpath();
         fnme= fnme+"/"+dev->getname()+".restart.b."+bgnm[ig]+"."+strc(ilev)+"."+strc(icpu);
         //cout << "save solution to " << fnme << "\n";

         //line= "mv -f "+fnme+" "+fnme+".bck";
         //system( line.c_str() );

         f= fopen(fnme.c_str(),"w");
         bdf[ig]->pickle( &len,&buf );
         l= ::fwrite( &tm,1, sizeof(tm),f );
         l= ::fwrite( &len,1,sizeof(len),f );
         l= ::fwrite(  buf,1,        len,f );
         delete[] buf;buf= NULL; len=0;

         bdf[ig]->write( nx,sxqb[ig],f );
         bdf[ig]->write( nv,sqb[ig],f );
         bdf[ig]->write( naux,sauxb[ig],f );

         fclose(f);
     }

// plugin section
//      vwp= new cPlugin( swdl,swdo );
//      vwl_f= (vwl_t)vwp->Sub();
//    (*vwl_f)( this, nx,nv,naux,nq,np, nek ,nep ,ne ,iep ,ieq, xp,wq,q,aux, ng,nbk,nbp,nb,ibp );
//      delete vwp; vwp=NULL;

  }

   void cFdDomain::saveunst( Int it0 )
  {
      size_t       l=0;
      size_t       len=0;
      pickle_t     buf=NULL;
      string       fnme;
      Int          icpu,ig, jg;
      ifstream     fle;
      Int          nbnd;
      string       bndnm[100];
      bool         bsaveq;

      FILE        *f;   

      cDomain::save();

      icpu= dev->getrank();
      fnme= dev->getcpath();
      fnme = fnme + "/" + dev->getname() + ".usave";
      fle.open(fnme.c_str());

      if(!fle.good())
     {
         return;
     }
 
      bsaveq = false;
      nbnd=0;
      fle >> bsaveq;
      fle >> nbnd;
      //cout << "number of boundaries to save " << nbnd << "\n";
      for(ig=0; ig<nbnd; ig++)
     {
         fle >> bndnm[ig];
         //cout << "boundary " << ig << " " << bndnm[ig] << "\n";
     }
      fle.close();

      if(bsaveq)
     {
        //save whole solution at time level it0
        fnme= dev->getcpath();
        icpu= dev->getrank();
        fnme= fnme+"/unstsol/"+dev->getname()+".restart.q."+strc(ilev)+"."+strc(icpu) + "." + strc(it0);

        f= fopen(fnme.c_str(),"w");
        dof->pickle( &len,&buf );
        pts->pickle( &len,&buf );

        l= fwrite( &tm,1,sizeof(tm),f );
        l= fwrite( &len,1,sizeof(len),f );
        l= fwrite(  buf,1,        len,f );
        delete[] buf;buf= NULL; len=0;

        pts->write( nx,sxp,f );
        dof->write( nx,sxq,f );
        dof->write( nv,sq,f );
//saux and su are not saved to save space
//      dof->write( naux,saux,f );
//      l= fwrite( &ntlv,1,sizeof(ntlv),f );
//      for(Int il=0; il<ntlv; il++)
//     {
//         dof->write( nv,su[il],f );
//     }
//      l= fwrite( &cfl,1,sizeof(cfl),f );

        fclose(f);
     }

      //save selected boundaries at time level it0
      if(nbnd>0)
     {
        for( ig=0;ig<ng;ig++ )
       {
           bool btmp;
           btmp = false; 
           for(jg=0; jg<nbnd; jg++)
          {
              if(bndnm[jg] == bgnm[ig])
             {
                 btmp = true; 
             } 
          }
           if(!btmp) continue;
  
           fnme= dev->getcpath();
           fnme= fnme+"/unstsol/"+dev->getname()+".restart.b."+bgnm[ig]+"."+strc(ilev)+"."+strc(icpu) + "." + strc(it0);
           //cout << "save solution to " << fnme << "\n";

           //line= "mv -f "+fnme+" "+fnme+".bck";
           //system( line.c_str() );

           f= fopen(fnme.c_str(),"w");
           bdf[ig]->pickle( &len,&buf );
           l= ::fwrite( &tm,1, sizeof(tm),f );
           l= ::fwrite( &len,1,sizeof(len),f );
           l= ::fwrite(  buf,1,        len,f );
           delete[] buf;buf= NULL; len=0;

           bdf[ig]->write( nx,sxqb[ig],f );
           bdf[ig]->write( nv,sqb[ig],f );
           bdf[ig]->write( naux,sauxb[ig],f );
  
           fclose(f);
       }
     }
  }
