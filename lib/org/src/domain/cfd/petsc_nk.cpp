using namespace std;

#include <iomanip>      // std::setprecision
#  include <domain/cfd/domain.h>

#ifdef PETSC
   PetscErrorCode cFdDomain::petsc_nk_init()
  {
      cout << "initialize petsc\n";
      PetscErrorCode ierr;
      Int iq, iqs, iqe;
      PetscInt blocksize;
      MPI_Comm* A_COMM_WORLD;
      PetscInt maxneig=8;
      KSPType ksp_type;
      PetscInt gmres_restart, asm_ovlap;
      PCType pc_type;

      A_COMM_WORLD = dev->getcomm();

      dof->range( dev->getrank(), &iqs,&iqe );

      //number of ghost cells and their global indexes (in the new numbering)
      ig_mat = dof->get_igmat();
      ighost       = new Int [dof->size()];
      ighost_local = new Int [dof->size()];
      ilocal       = new Int [dof->size()];
      nghost=0;
      nlocal=0;
      for(iq=0; iq<dof->size(); iq++)
     {
         if(iq>=iqs && iq<iqe)    
        {
            //non-halo cells
            ilocal[nlocal] = iq; 
//            cout << "non-halo point " << dev->getrank() << " " << nlocal << " " << iq << " " << ig_mat[iq] << "\n";
            nlocal++; 
        }
         else
        {
            ighost[nghost] = ig_mat[iq];
            ighost_local[nghost] = iq;
//            cout << "halo point " << dev->getrank() << " " << nghost << " " << iq << " " << ig_mat[iq] << "\n";
            nghost++;    
        }
     }
//      cout << "rank " << dev->getrank() << " nlocal " << " " << nlocal << "\n";
//      cout << "rank " << dev->getrank() << " nghost " << " " << nghost << "\n";
      blocksize = nv;

      //create vectors to store right hand sides and flow variables
//      ierr = VecCreate(*A_COMM_WORLD, &petsc_dcsv);CHKERRQ(ierr);
//      ierr = VecSetSizes(petsc_dcsv, nlocal*blocksize, PETSC_DECIDE);CHKERRQ(ierr);
//      ierr = VecSetBlockSize(petsc_dcsv, blocksize);CHKERRQ(ierr);
//      ierr = VecSetType(petsc_dcsv, VECMPI);CHKERRQ(ierr);
//      ierr = VecMPISetGhost(petsc_dcsv,nghost,ighost);CHKERRQ(ierr);
      ierr = VecCreateGhostBlock(*A_COMM_WORLD, blocksize, blocksize*nlocal, PETSC_DECIDE ,nghost, ighost, &petsc_dcsv); CHKERRQ(ierr);
      ierr = VecSetFromOptions(petsc_dcsv);CHKERRQ(ierr);

      //duplicate 
      ierr = VecDuplicate(petsc_dcsv, &petsc_rhs);CHKERRQ(ierr);
//      ierr = VecDuplicate(petsc_dcsv, &petsc_csv);CHKERRQ(ierr);
//      ierr = VecDuplicate(petsc_dcsv, &petsc_baserhs);CHKERRQ(ierr);

      //create preconditioning matrix
      ierr = MatCreateBAIJ(*A_COMM_WORLD, blocksize, nlocal*blocksize, nlocal*blocksize, PETSC_DETERMINE, PETSC_DETERMINE, 
                            maxneig, NULL, maxneig, NULL, &petsc_A_pre); CHKERRQ(ierr);
      ierr = MatSetOption(petsc_A_pre, MAT_STRUCTURALLY_SYMMETRIC, PETSC_TRUE); CHKERRQ(ierr);

      //create matrix-free jacobian matrix 
//      ierr = MatCreateMFFD(*A_COMM_WORLD, iqe*blocksize, iqe*blocksize, PETSC_DETERMINE, PETSC_DETERMINE, &petsc_A_mf); CHKERRQ(ierr);
//      ierr = MatMFFDSetFunction(petsc_A_mf, FormFunction_mf, this); CHKERRQ(ierr);
      ierr = MatCreateShell(*A_COMM_WORLD, nlocal*blocksize, nlocal*blocksize, PETSC_DETERMINE, PETSC_DETERMINE, this, &petsc_A_mf); CHKERRQ(ierr);
      ierr = MatShellSetOperation(petsc_A_mf,MATOP_MULT,(void(*)(void))mymult); CHKERRQ(ierr);


      //create linear solver
      ierr = KSPCreate(*A_COMM_WORLD, &petsc_ksp); CHKERRQ(ierr);

      // set operators for ksp
      ierr = KSPSetOperators(petsc_ksp, petsc_A_mf, petsc_A_pre); CHKERRQ(ierr);
      //ierr = KSPSetOperators(petsc_ksp, petsc_A_pre, petsc_A_pre); CHKERRQ(ierr);

      ksp_type = "gmres";
      gmres_restart = 80;
      asm_ovlap = 1;
      pc_type = "asm";

      ierr = KSPSetType(petsc_ksp, ksp_type); CHKERRQ(ierr);
      ierr = KSPGMRESSetRestart(petsc_ksp, gmres_restart); CHKERRQ(ierr);
      ierr = KSPGMRESSetCGSRefinementType(petsc_ksp, KSP_GMRES_CGS_REFINE_NEVER); CHKERRQ(ierr);
      ierr = KSPGetPC(petsc_ksp,&petsc_pc);CHKERRQ(ierr); CHKERRQ(ierr);
      ierr = PCSetType(petsc_pc,pc_type); CHKERRQ(ierr);
      ierr = PCASMSetOverlap(petsc_pc,asm_ovlap); CHKERRQ(ierr);
      ierr = KSPSetPCSide(petsc_ksp,PC_RIGHT); CHKERRQ(ierr);
      ierr = KSPSetTolerances(petsc_ksp,0.5e-1,PETSC_DEFAULT,PETSC_DEFAULT,PETSC_DEFAULT);CHKERRQ(ierr);

      ierr = KSPSetFromOptions(petsc_ksp); CHKERRQ(ierr);

      cout << "done with initialize petsc\n";
      ierr = 0;

      return ierr;
  }

   PetscErrorCode cFdDomain::mymult(Mat m ,Vec x, Vec y)
  {
      PetscErrorCode ierr;
      void *ctx;
      cFdDomain *myctx;
      Vec localin;

      MatShellGetContext(m, &ctx);
      myctx = (cFdDomain*)ctx;

      VecGhostUpdateBegin(x, INSERT_VALUES, SCATTER_FORWARD);
      VecGhostUpdateEnd(x, INSERT_VALUES, SCATTER_FORWARD);
      VecGhostGetLocalForm(x,&localin);

//      cout << "call mymult \n";
//      cout << myctx << "\n";
      ierr = myctx->petsc_set_fd_dq(localin); CHKERRQ(ierr);
      ierr = myctx->petsc_fd_rhs2(y); CHKERRQ(ierr);

      VecGhostRestoreLocalForm(x,&localin);

      return ierr;
  }

   PetscErrorCode cFdDomain::petsc_set_fd_dq(Vec var)
  {
      Int iv, iq, iqs, iqe, jq;
      PetscErrorCode ierr;
      const PetscScalar *array;

      ierr = VecGetArrayRead(var,&array); CHKERRQ(ierr);

      for(iv=0; iv<nv; iv++)  
     {
         for(iq=0; iq<nlocal; iq++) 
        {
            jq = ilocal[iq];
            dq[iv][jq] = array[iv + nv*iq].real();    
        }

         for(iq=nlocal; iq<nlocal+nghost; iq++) 
        {
            jq = ighost_local[iq-nlocal];
            dq[iv][jq] = array[iv + nv*iq].real();    
        }
     }

      ierr = VecRestoreArrayRead(var,&array); CHKERRQ(ierr);

      ierr = 0;
      return ierr;
  }

   PetscErrorCode cFdDomain::petsc_fd_rhs2(Vec var)
  {
      Int iqs, iqe;
      Int iv, iq, jq;
      Real delta_rhs;
      PetscErrorCode ierr;
      PetscScalar *array;
      Real eps = 1e-6;
      Real dq_norm;

      dof->range( dev->getrank(), &iqs,&iqe );

      dq_norm = 0;
      for(iq=iqs; iq<iqe; iq++)
     {
         for(iv=0; iv<nv; iv++)
        {
            dq_norm += dq[iv][iq]*dq[iv][iq];
        }
     }
      dev->gsum(1, &dq_norm);
      dq_norm = sqrt(dq_norm/dof->gsize());
      //eps  = sqrt(1+csv_mag_fd)*1.e-4/dq_norm;
      eps  = 1.e-5/dq_norm;
      cout << eps << "\n";

//compute rhs with unperturbed flow states
      setv( 0,dof->size(), nv, ZERO, rhs );

      grad( q,dqdx );
      gradb( q,dqdx );

      if(limtype==1)
     {
         grads(dqdx);

         initvenklim();
         compvenklim();
         gtrhfm_venk(rhs);
     }
      else
     {
         gtrhf( rhs );
         gtrhm( rhs );
     }
      gtrhv( rhs );

      //deterministic stress contribution
      gtrhsds(rhs);

      //add contribution of the diagonal term
      fld->cnsv( iqs,iqe, q, aux, csv );
      for(iv=0; iv<nv; iv++)
     {
         for(iq=iqs; iq<iqe; iq++)
        {
            //use conservative variables here
            rhs[iv][iq] = -rhs[iv][iq] + csv[iv][iq]*lhsa[0][iq]/cfl;
        }
     }


//perturb q, halo and non-halo cells
//convert to primitive variable perturbations
      fld->dvar( 0,dof->size(), q, aux, dq, daux );
      for(iv=0; iv<nv; iv++)
     {
         for(iq=0; iq<dof->size(); iq++)
        {
            q[iv][iq] += daux[iv][iq]*eps;
        }
     } 

//compute rhs with perturbed flow states
      setv( 0,dof->size(), nv, ZERO, wrkq );

      grad( q,dqdx );
      gradb( q,dqdx );

      if(limtype==1)
     {
         grads(dqdx);

         initvenklim();
         compvenklim();
         gtrhfm_venk(wrkq);
     }
      else
     {
         gtrhf( wrkq );
         gtrhm( wrkq );
     }
      gtrhv( wrkq );

      //deterministic stress contribution
      gtrhsds(wrkq);

      //add contribution of the diagonal term
      fld->cnsv( iqs,iqe, q, aux, csv );
      for(iv=0; iv<nv; iv++)
     {
         for(iq=iqs; iq<iqe; iq++)
        {
            //use conservative variables here
            wrkq[iv][iq] = -wrkq[iv][iq] + csv[iv][iq]*lhsa[0][iq]/cfl;
        }
     }

//matrix-vector product
      ierr = VecGetArray(var,&array); CHKERRQ(ierr);

      for(iv=0; iv<nv; iv++)  
     {
         for(iq=0; iq<nlocal; iq++) 
        {
            jq = ilocal[iq];
            array[iv + nv*iq] = (wrkq[iv][jq] - rhs[iv][jq])/eps;
        }
     }

      ierr = VecRestoreArray(var,&array); CHKERRQ(ierr);

///reset q
      for(iv=0; iv<nv; iv++)
     {
         for(iq=0; iq<dof->size(); iq++)
        {
            q[iv][iq] -= daux[iv][iq]*eps;
        }
     } 

      ierr = 0;
      return ierr;
  }


   PetscErrorCode cFdDomain::petsc_nk_comp()
  {
      Int iv, iq, iqs, iqe, jv, nnz, it;
      Real *r1, r1_norm;
      PetscErrorCode ierr;
      PetscInt niter_ksp;
      KSPConvergedReason ksp_reason;
      cJacBlk blk;
      PetscInt idxm[10], idxn[10];
      PetscScalar values[100];

      r1 = new Real [nv];

      movegrid();
      frame();
      weights();

      //cout << scientific << setprecision(10) << "\n";

      if(nexc<8000)
     {
         gmres_glb = true;
     }
      else
     {
         gmres_glb = false;
     }

      //if running FNLH, assume it restarts from a partially or fully converged mixing plane solution
      if(nfre>0) gmres_glb = false;

      if(gmres_glb)
     {
         for( it=0;it<npre;it++ )
        {
            cfl= min( cfl1,cfl*dcfl );

            newton2();

            r1_norm = 0; 
            resd2( rhs, &r1_norm );
            r1_norm = sqrt(r1_norm);
            if(nexc==0) init_residual = r1_norm;
            if(dev->getrank()==0 && ilev==0)
           {
              *flg << cfl<<" ";
             // for( iv=0;iv<nv;iv++ )
             //{
             //    *flg << r1[iv]<<" ";
             //}
              *flg << r1_norm <<" ";
              if(nfre==0)
             {
                 *flg << "\n";
                  flg->flush();
             }
           }
            nexc++;
            cout << "iteration " << nexc << "\n";
        } 
         maverage(false);
     }
      else
     {
         for( it=0;it<nstep;it++ )
        {
            dof->range( dev->getrank(), &iqs,&iqe );

            cfl= max( cfl1,cfl*dcfl );
            cfl= min( 100.,cfl*dcfl );

//assemble right hand side
            gtrhs(rhs);
            ierr = petsc_setrhs(petsc_rhs); CHKERRQ(ierr);

//write residuals to "hist.dat"
            //for(iv=0; iv<nv; iv++) r1[iv]=0;
            //resd( rhs, r1 );
            r1_norm = 0; 
            resd2( rhs, &r1_norm );
            r1_norm = sqrt(r1_norm);
            if(nexc==0) init_residual = r1_norm;
            if(dev->getrank()==0 && ilev==0)
           {
              *flg << cfl<<" ";
             // for( iv=0;iv<nv;iv++ )
             //{
             //    *flg << r1[iv]<<" ";
             //}
              *flg << r1_norm <<" ";
              if(nfre==0)
             {
                 *flg << "\n";
                  flg->flush();
             }
           }
   
            fld->cnsv( iqs,iqe, q, aux, csv);
            csv_mag_fd = 0;
            for(iq=iqs; iq<iqe; iq++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  csv_mag_fd += csv[iv][iq]*csv[iv][iq];
              }
           }
            dev->gsum(1, &csv_mag_fd);
            csv_mag_fd = sqrt(csv_mag_fd/dof->gsize());

//assemble the pre-conditioning matrix here
            ierr = MatZeroEntries(petsc_A_pre); CHKERRQ(ierr);
            //petsc_assemble_jac_invi(); 
            //petsc_assemble_jac_visc();
            petsc_assemble_jac_invi2(); 
            petsc_assemble_jac_visc2();
            //add contribution of time-stepping
           // for(iq=iqs; iq<iqe; iq++)
           //{
           //    nnz=0;
           //    for(iv=0; iv<nv; iv++)
           //   {
           //       for(jv=0; jv<nv; jv++) 
           //      {
           //          if(iv==jv) values[nnz++] = lhsa[0][iq]/cfl;
           //          else       values[nnz++] = 0;
           //      }
           //   }
           //    idxm[0] = ig_mat[iq];
           //    idxn[0] = ig_mat[iq];
           //    ierr = MatSetValuesBlocked(petsc_A_pre, 1, idxm, 1, idxn, values, ADD_VALUES); 
           //    CHKERRQ(ierr);
           //}
            add_pseudo_time_to_petsc_matrix( petsc_A_pre, iqs, iqe, lhsa[0], cfl, 1  ); 
   
   
            ierr = MatAssemblyBegin(petsc_A_pre,MAT_FINAL_ASSEMBLY);CHKERRQ(ierr);
            ierr = MatAssemblyEnd(petsc_A_pre,MAT_FINAL_ASSEMBLY);CHKERRQ(ierr);
   
            ierr = VecAssemblyBegin(petsc_dcsv);CHKERRQ(ierr);
            ierr = VecAssemblyEnd(petsc_dcsv);CHKERRQ(ierr);
   
            ierr = VecAssemblyBegin(petsc_rhs);CHKERRQ(ierr);
            ierr = VecAssemblyEnd(petsc_rhs);CHKERRQ(ierr);
   
//solve
            ierr = KSPSolve(petsc_ksp, petsc_rhs, petsc_dcsv);   
            ierr = KSPGetIterationNumber(petsc_ksp, &niter_ksp); CHKERRQ(ierr);
            ierr = KSPGetConvergedReason(petsc_ksp, &ksp_reason); CHKERRQ(ierr);

//solve linearized flow
            if(nfre>0)
           {
               //undo cfl contribution in the jacobian matrix
               add_pseudo_time_to_petsc_matrix( petsc_A_pre, iqs, iqe, lhsa[0], cfl, -1  ); 
               ierr = petsc_nk_comp_lin();
           }

//update the solution
            ierr = petsc_qupdt(petsc_dcsv); CHKERRQ(ierr);
        }



         //if(it==1) exit(0);
         nexc++;
         cout << "iteration " << nexc << "\n";
         maverage(false);
     }

      save();
      delete[] r1; r1=NULL;

      ierr = 0;
      return ierr;
  }

   PetscErrorCode cFdDomain::check_mat_multi_vec()
  {
      Int iv, iq, iqs, iqe, jv, nnz;
      Real *r1;
      PetscErrorCode ierr;
      PetscInt niter_ksp;
      KSPConvergedReason ksp_reason;
      cJacBlk blk;
      PetscInt idxm[10], idxn[10];
      PetscScalar values[100];

      movegrid();
      frame();
      weights();

      //cout << scientific << setprecision(10) << "\n";

      dof->range( dev->getrank(), &iqs,&iqe );

//assemble right hand side
      gtrhs(rhs);
      ierr = petsc_setrhs(petsc_rhs); CHKERRQ(ierr);

//assemble the pre-conditioning matrix here
      ierr = MatZeroEntries(petsc_A_pre); CHKERRQ(ierr);
      //petsc_assemble_jac_invi(); 
      //petsc_assemble_jac_visc();
      petsc_assemble_jac_invi2(); 
      petsc_assemble_jac_visc2();
      //add contribution of time-stepping
      for(iq=iqs; iq<iqe; iq++)
     {
         nnz=0;
         for(iv=0; iv<nv; iv++)
        {
            for(jv=0; jv<nv; jv++) 
           {
               if(iv==jv) values[nnz++] = lhsa[0][iq]/cfl;
               else       values[nnz++] = 0;
           }
        }
         idxm[0] = ig_mat[iq];
         idxn[0] = ig_mat[iq];
         ierr = MatSetValuesBlocked(petsc_A_pre, 1, idxm, 1, idxn, values, ADD_VALUES); 
         CHKERRQ(ierr);
     }
      for(iq=0; iq<nq; iq++) cout << lhsa[0][iq] << "\n";

      ierr = MatAssemblyBegin(petsc_A_pre,MAT_FINAL_ASSEMBLY);CHKERRQ(ierr);
      ierr = MatAssemblyEnd(petsc_A_pre,MAT_FINAL_ASSEMBLY);CHKERRQ(ierr);

      ierr = VecAssemblyBegin(petsc_dcsv);CHKERRQ(ierr);
      ierr = VecAssemblyEnd(petsc_dcsv);CHKERRQ(ierr);

      ierr = VecAssemblyBegin(petsc_rhs);CHKERRQ(ierr);
      ierr = VecAssemblyEnd(petsc_rhs);CHKERRQ(ierr);

      //matrix-vector multiplication
      ierr = VecSet(petsc_dcsv, 1e-2); CHKERRQ(ierr);

//      //shell matrix
//      ierr = VecSet(petsc_rhs, 0); CHKERRQ(ierr);
//      ierr = mymult(petsc_A_mf, petsc_dcsv, petsc_rhs); CHKERRQ(ierr);
//      ierr = VecView(petsc_rhs, PETSC_VIEWER_STDOUT_WORLD); CHKERRQ(ierr);

      //pre-conditioning matrix
      ierr = VecSet(petsc_rhs, 0); CHKERRQ(ierr);
      ierr = MatMult(petsc_A_pre, petsc_dcsv,petsc_rhs);
      ierr = VecView(petsc_rhs, PETSC_VIEWER_STDOUT_WORLD); CHKERRQ(ierr);

exit(0);

      ierr = 0;
      return ierr;
  }

   PetscErrorCode cFdDomain::petsc_setrhs( Vec var )
  {
      Int iv, iq, jq;
      PetscErrorCode ierr;
      PetscScalar *array;

      ierr = VecGetArray(var,&array); CHKERRQ(ierr);

      for(iv=0; iv<nv; iv++)  
     {
         for(iq=0; iq<nlocal; iq++) 
        {
            jq = ilocal[iq];
            array[iv + nv*iq] = rhs[iv][jq];    
        }
     }
 
      ierr = VecRestoreArray(var,&array); CHKERRQ(ierr);

      ierr = 0;
      return ierr;
  }

   PetscErrorCode cFdDomain::petsc_qupdt( Vec delta )
  {
      Int iv, iq, iqs, iqe, jq;
      PetscErrorCode ierr;
      PetscScalar *array;

      ierr = VecGetArray(delta,&array); CHKERRQ(ierr);

      dof->range( dev->getrank(), &iqs,&iqe );
      for(iv=0; iv<nv; iv++)  
     {
         for(iq=0; iq<nlocal; iq++) 
        {
            jq = ilocal[iq];
            dq[iv][jq] = array[iv + iq*nv].real();    
        }
     }
      fld->dvar( iqs,iqe, q, aux, dq, daux );
//      for(iq=0; iq<nq; iq++)
//     {
//         if(iq%1000==0)cout << q[0][iq] << " " << q[1][iq] << " " << dq[0][iq] << " " << dq[1][iq] << "\n";
//     }
      fld->qupd( iqs,iqe, q,aux, dq,daux );
 
      ierr = VecRestoreArray(delta,&array); CHKERRQ(ierr);

      ierr = 0;
      return ierr;
  }


   void cFdDomain::petsc_assemble_jac_visc()
  {
      Int ibs, ibe, ib, iq, iv, jv, ic, ics, ice, iql, iqr, ig;
      cJacBlk blkjac, blkjacl, blkjacr;
      Real factor = 1.0e-7; //set a small number, so the linearization is more accurate

      cTabData data;
      Int asct;
      coo->get( &data );
      data.get( "assembly-sectors", &asct );

      dof->exchange( sq );
      while( dev->transit() )
     {
//here
//dq: perturbations of conservative variables
//daux: perturbations of primitive variables

//boundary
         for( ig=0;ig<ng;ig++ )
        {
            bdf[ig]->range( dev->avail(), &ibs,&ibe );
            setv( ibs,ibe, nv, ZERO, dqb[ig] );
            setv( ibs,ibe, nv, ZERO, dauxb[ig] );
            setv( ibs,ibe, nv, ZERO, resb[ig] );
            for(ib=ibs; ib<ibe; ib++)
           {
               iqr = iqb[ig][0][ib];
               for(iv=0; iv<nv; iv++)
              {
                  for(jv=0; jv<nv; jv++)
                 {
                     if(iv==jv) dq[jv][iqr] = factor;
                     else       dq[jv][iqr] = 0;
                     res[jv][iqr] = 0;
                 }

                  fld->dvar( iqr,iqr+1, q, aux, dq, daux );
                  bbj[ig]->dmflx( ib, ib+1, xb[ig],qb[ig],auxb[ig], dqb[ig],dauxb[ig], resb[ig], iqb[ig], xq,q,aux, dq,daux,res,
                                  wnb[ig], wxdb[ig], auxfb[ig] );

//                  for(jv=0; jv<nv; jv++)
//                 {
//                     lhs_jac[iqr][iqr].jac[jv][iv]+= res[jv][iqr]/factor;
//                 }

                  for(jv=0; jv<nv; jv++)
                 {
                     blkjac.jac[jv][iv] = res[jv][iqr]/factor;
                 }
              }
               add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, blkjac, 1);
           }
        }

//periodic faces, shift upper to lower
         prd->range( dev->avail(), &ics,&ice );
         coo->coffset( ics,ice, -1.,       iprq[1],xq,                      NULL, xqprd );
         fld->voffset( ics,ice, -1.,       iprq[1], q,                      NULL,  qprd );
         for( iv=0;iv<naux;iv++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iq= iprq[1][ic];
               auxprd[iv][ic]= aux[iv][iq];
           }
        }
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(iv=0; iv<nv; iv++)
           {
               //perturb right state
               for(jv=0; jv<nv; jv++)
              {
                  if(iv==jv) dq[jv][iqr] = factor;
                  else       dq[jv][iqr] = 0;
                  res[jv][iqr] = 0;

                  rhsprd[jv][ic] = 0;
                  dqprd[jv][ic] = 0;
                  dauxprd[jv][ic] = 0;
              }
               fld->dvar( iqr,iqr+1, q, aux, dq, daux );
               fld->dmflx( ic,ic+1, NULL, xqprd,qprd,auxprd,dqprd,dauxprd,rhsprd, iprq[0], xq,q,aux,dq,daux,res, xprd, wnprd, wxdprd, auxfprd );

//               for(jv=0; jv<nv; jv++)
//              {
//                  lhs_jac[iqr][iqr].jac[jv][iv]+= res[jv][iqr]/factor;
//              }

               for(jv=0; jv<nv; jv++)
              {
                  blkjac.jac[jv][iv] = res[jv][iqr]/factor;
              }
           }
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, blkjac, 1);


            for(iv=0; iv<nv; iv++)
           {
               //perturb left stage
               for(jv=0; jv<nv; jv++)
              {
                  if(iv==jv) dqprd[jv][ic] = factor;
                  else       dqprd[jv][ic] = 0;
                  rhsprd[jv][ic] = 0;

                  res[jv][iqr] = 0;
                  dq[jv][iqr] = 0;
                  daux[jv][iqr] = 0;
              }
               fld->dvar( ic,ic+1, qprd, auxprd, dqprd, dauxprd );
               fld->dmflx( ic,ic+1, NULL, xqprd,qprd,auxprd,dqprd,dauxprd,rhsprd, iprq[0], xq,q,aux,dq,daux,res, xprd, wnprd, wxdprd, auxfprd );
               for(jv=0; jv<nv; jv++)
              {
                  blkjac.jac[jv][iv] = res[jv][iqr]/factor;
              }
           }
            jactimerot(asct, nv, &blkjac, -1.);
//            for(iv=0; iv<nv; iv++)
//           {
//               for(jv=0; jv<nv; jv++)
//              {
//                  lhs_jac[iqr][iql].jac[jv][iv]+= blkjac.jac[jv][iv];
//              }
//           }
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iql, blkjac, 1);

        }

//periodic face, shift lower to upper
         coo->coffset( ics,ice, 1.,NULL,wnprd,NULL,wnprd ); //rotate it to the LEFT
         coo->coffset( ics,ice, 1.,NULL,xprd,NULL,xprd ); //rotate it to the LEFT
         coo->coffset( ics,ice, 1.,       iprq[0],xq,                      NULL, xqprd );
         fld->voffset( ics,ice, 1.,       iprq[0], q,                      NULL,  qprd );
         for( iv=0;iv<naux;iv++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iq= iprq[0][ic];
               auxprd[iv][ic]= aux[iv][iq];
           }
        }
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(iv=0; iv<nv; iv++)
           {
               //perturb left state
               for(jv=0; jv<nv; jv++)
              {
                  if(iv==jv) dq[jv][iql] = factor;
                  else       dq[jv][iql] = 0;
                  res[jv][iql] = 0;

                  rhsprd[jv][ic] = 0;
                  dqprd[jv][ic] = 0;
                  dauxprd[jv][ic] = 0;
              }
               fld->dvar( iql,iql+1, q, aux, dq, daux );
               fld->dmflx( ic,ic+1, iprq[1], xq,q,aux,dq,daux,res, 
                                    NULL, xqprd,qprd,auxprd,dqprd,dauxprd,rhsprd, 
                                    xprd, wnprd, wxdprd, auxfprd );

//               for(jv=0; jv<nv; jv++)
//              {
//                  lhs_jac[iql][iql].jac[jv][iv]+= res[jv][iql]/factor; //res is now on the left, it already has a minus sign
//              }
               for(jv=0; jv<nv; jv++)
              {
                  blkjac.jac[jv][iv] = res[jv][iql]/factor; //res is now on the left, it already has a minus sign
              }
            }
             add_blk_to_petsc_matrix(petsc_A_pre, iql, iql, blkjac, 1);


             for(iv=0; iv<nv; iv++)
            {
               //perturb right state
               for(jv=0; jv<nv; jv++)
              {
                  if(iv==jv) dqprd[jv][ic] = factor;
                  else       dqprd[jv][ic] = 0;
                  rhsprd[jv][ic] = 0;

                  res[jv][iql] = 0;
                  dq[jv][iql] = 0;
                  daux[jv][iql] = 0;
              }
               fld->dvar( ic,ic+1, qprd, auxprd, dqprd, dauxprd );
               fld->dmflx( ic,ic+1, iprq[1], xq,q,aux,dq,daux,res, 
                                    NULL, xqprd,qprd,auxprd,dqprd,dauxprd,rhsprd, 
                                    xprd, wnprd, wxdprd, auxfprd );


               for(jv=0; jv<nv; jv++)
              {
                  blkjac.jac[jv][iv] = res[jv][iql]/factor;
              }

           }
            jactimerot(asct, nv, &blkjac, 1.);
//            for(iv=0; iv<nv; iv++)
//           {
//               for(jv=0; jv<nv; jv++)
//              {
//                  lhs_jac[iql][iqr].jac[jv][iv]+= blkjac.jac[jv][iv];
//              }
//           }
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iqr, blkjac, 1);

        }
         coo->coffset( ics,ice, -1.,NULL,wnprd,NULL,wnprd );//roate it back to RIGHT
         coo->coffset( ics,ice, -1.,NULL,xprd,NULL,xprd );//roate it back to RIGHT


//internal faces
         cnf->range( dev->avail(), &ics,&ice );
         for(ic=ics; ic<ice; ic++)
        {
            //perturb left and right in turn
            iql = ifq[0][ic];
            iqr = ifq[1][ic];

            for(iv=0; iv<nv; iv++)
           {
               for(jv=0; jv<nv; jv++)
              {
                  if(iv==jv) dq[jv][iqr] = factor;
                  else       dq[jv][iqr] = 0;
                  res[jv][iqr] = 0; 

                  dq[jv][iql] = 0;
                  daux[jv][iql] = 0;
                  res[jv][iql] = 0;
              }

               fld->dvar( iqr,iqr+1, q, aux, dq, daux );
               fld->dmflx( ic, ic+1, ifq[0], xq,q,aux,dq,daux,res, 
                                     ifq[1], xq,q,aux,dq,daux,res, 
                                     xc, wnc, wxdc, auxf );
//               for(jv=0; jv<nv; jv++)
//              {
//                  lhs_jac[iql][iqr].jac[jv][iv]+= res[jv][iql]/factor; //res[jv][iql] is negative
//                  lhs_jac[iqr][iqr].jac[jv][iv]+= res[jv][iqr]/factor;
//                  //cout << res[jv][iqr] << " " << res[jv][iql] << "\n";
//              }
               for(jv=0; jv<nv; jv++)
              {
                  blkjacl.jac[jv][iv] = res[jv][iql]/factor; //res[jv][iql] is negative
                  blkjacr.jac[jv][iv] = res[jv][iqr]/factor;
                  //cout << res[jv][iqr] << " " << res[jv][iql] << "\n";
              }
           }
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iqr, blkjacl, 1);
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, blkjacr, 1);


            for(iv=0; iv<nv; iv++)
           {
               for(jv=0; jv<nv; jv++)
              {
                  if(iv==jv) dq[jv][iql] = factor;
                  else       dq[jv][iql] = 0;
                  res[jv][iql] = 0;

                  dq[jv][iqr] = 0;
                  daux[jv][iqr] = 0;
                  res[jv][iqr] = 0;
              }

               fld->dvar( iql,iql+1, q, aux, dq, daux );
               fld->dmflx( ic, ic+1, ifq[0], xq,q,aux,dq,daux,res, 
                                     ifq[1], xq,q,aux,dq,daux,res, 
                                     xc, wnc, wxdc, auxf );
//               for(jv=0; jv<nv; jv++)
//              {
//                  lhs_jac[iql][iql].jac[jv][iv]+= res[jv][iql]/factor; //res[jv][iql] is negative
//                  lhs_jac[iqr][iql].jac[jv][iv]+= res[jv][iqr]/factor;
//                  //cout << res[jv][iqr] << " " << res[jv][iql] << "\n";
//              }
               for(jv=0; jv<nv; jv++)
              {
                  blkjacl.jac[jv][iv] = res[jv][iql]/factor; //res[jv][iql] is negative
                  blkjacr.jac[jv][iv] = res[jv][iqr]/factor;
                  //cout << res[jv][iqr] << " " << res[jv][iql] << "\n";
              }
           }
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iql, blkjacl, 1);
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iql, blkjacr, 1);
        }
     }

  }

   void cFdDomain::petsc_assemble_jac_visc2()
  {
      Int ibs, ibe, ib, iq, iv, jv, ic, ics, ice, iql, iqr, ig;
      cJacBlk blkjac, blkjacl, blkjacr;
      Real factor = 1.0e-7; //set a small number, so the linearization is more accurate

      cTabData data;
      Int asct;
      coo->get( &data );
      data.get( "assembly-sectors", &asct );

      dof->exchange( sq );
      while( dev->transit() )
     {
//here
//dq: perturbations of conservative variables
//daux: perturbations of primitive variables

//boundary
         for( ig=0;ig<ng;ig++ )
        {
            bdf[ig]->range( dev->avail(), &ibs,&ibe );
            setv( ibs,ibe, nv, ZERO, dqb[ig] );
            setv( ibs,ibe, nv, ZERO, dauxb[ig] );
            setv( ibs,ibe, nv, ZERO, resb[ig] );
            for(ib=ibs; ib<ibe; ib++)
           {
               iqr = iqb[ig][0][ib];
               for(iv=0; iv<nv; iv++)
              {
                  for(jv=0; jv<nv; jv++)
                 {
                     if(iv==jv) dq[jv][iqr] = factor;
                     else       dq[jv][iqr] = 0;
                     res[jv][iqr] = 0;
                 }

                  fld->dvar( iqr,iqr+1, q, aux, dq, daux );
                  bbj[ig]->dmflx( ib, ib+1, xb[ig],qb[ig],auxb[ig], dqb[ig],dauxb[ig], resb[ig], iqb[ig], xq,q,aux, dq,daux,res,
                                  wnb[ig], wxdb[ig], auxfb[ig] );

//                  for(jv=0; jv<nv; jv++)
//                 {
//                     lhs_jac[iqr][iqr].jac[jv][iv]+= res[jv][iqr]/factor;
//                 }

                  for(jv=0; jv<nv; jv++)
                 {
                     blkjac.jac[jv][iv] = res[jv][iqr]/factor;
                 }
              }
               add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, blkjac, 1);
           }
        }

//periodic faces, shift upper to lower
         prd->range( dev->avail(), &ics,&ice );
         coo->coffset( ics,ice, -1.,       iprq[1],xq,                      NULL, xqprd );
         fld->voffset( ics,ice, -1.,       iprq[1], q,                      NULL,  qprd );
         for( iv=0;iv<naux;iv++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iq= iprq[1][ic];
               auxprd[iv][ic]= aux[iv][iq];
           }
        }
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(iv=0; iv<nv; iv++)
           {
               //perturb right state
               for(jv=0; jv<nv; jv++)
              {
                  if(iv==jv) dq[jv][iqr] = factor;
                  else       dq[jv][iqr] = 0;
                  res[jv][iqr] = 0;

                  rhsprd[jv][ic] = 0;
                  dqprd[jv][ic] = 0;
                  dauxprd[jv][ic] = 0;
              }
               fld->dvar( iqr,iqr+1, q, aux, dq, daux );
               fld->dmflx( ic,ic+1, NULL, xqprd,qprd,auxprd,dqprd,dauxprd,rhsprd, iprq[0], xq,q,aux,dq,daux,res, xprd, wnprd, wxdprd, auxfprd );

//               for(jv=0; jv<nv; jv++)
//              {
//                  lhs_jac[iqr][iqr].jac[jv][iv]+= res[jv][iqr]/factor;
//              }

               for(jv=0; jv<nv; jv++)
              {
                  blkjac.jac[jv][iv] = res[jv][iqr]/factor;
                  jac_df_prd[1][ic].jac[jv][iv] = blkjac.jac[jv][iv];
                  jac_vis_prd[1][ic].jac[jv][iv] = blkjac.jac[jv][iv];
              }
           }
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, blkjac, 1);


            for(iv=0; iv<nv; iv++)
           {
               //perturb left stage
               for(jv=0; jv<nv; jv++)
              {
                  if(iv==jv) dqprd[jv][ic] = factor;
                  else       dqprd[jv][ic] = 0;
                  rhsprd[jv][ic] = 0;

                  res[jv][iqr] = 0;
                  dq[jv][iqr] = 0;
                  daux[jv][iqr] = 0;
              }
               fld->dvar( ic,ic+1, qprd, auxprd, dqprd, dauxprd );
               fld->dmflx( ic,ic+1, NULL, xqprd,qprd,auxprd,dqprd,dauxprd,rhsprd, iprq[0], xq,q,aux,dq,daux,res, xprd, wnprd, wxdprd, auxfprd );
               for(jv=0; jv<nv; jv++)
              {
                  blkjac.jac[jv][iv] = res[jv][iqr]/factor;
                  jac_df_prd[0][ic].jac[jv][iv] = blkjac.jac[jv][iv];
                  jac_vis_prd[0][ic].jac[jv][iv] = blkjac.jac[jv][iv];
              }
           }
            jactimerot(asct, nv, &blkjac, -1.);
//            for(iv=0; iv<nv; iv++)
//           {
//               for(jv=0; jv<nv; jv++)
//              {
//                  lhs_jac[iqr][iql].jac[jv][iv]+= blkjac.jac[jv][iv];
//              }
//           }
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iql, blkjac, 1);

        }

         coo->jacoffset(ics, ice, 1., nv, jac_df_prd[0]);
         coo->jacoffset(ics, ice, 1., nv, jac_df_prd[1]);
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac.jac[jv][iv] = jac_df_prd[1][ic].jac[jv][iv];
              }
           }

            add_blk_to_petsc_matrix(petsc_A_pre, iql, iql, jac_df_prd[0][ic], -1);

            jactimerot(asct, nv, &blkjac, 1.);   
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iqr, blkjac, -1);
        }


//internal faces
         cnf->range( dev->avail(), &ics,&ice );
         for(ic=ics; ic<ice; ic++)
        {
            //perturb left and right in turn
            iql = ifq[0][ic];
            iqr = ifq[1][ic];

            for(iv=0; iv<nv; iv++)
           {
               for(jv=0; jv<nv; jv++)
              {
                  if(iv==jv) dq[jv][iqr] = factor;
                  else       dq[jv][iqr] = 0;
                  res[jv][iqr] = 0; 

                  dq[jv][iql] = 0;
                  daux[jv][iql] = 0;
                  res[jv][iql] = 0;
              }

               fld->dvar( iqr,iqr+1, q, aux, dq, daux );
               fld->dmflx( ic, ic+1, ifq[0], xq,q,aux,dq,daux,res, 
                                     ifq[1], xq,q,aux,dq,daux,res, 
                                     xc, wnc, wxdc, auxf );
//               for(jv=0; jv<nv; jv++)
//              {
//                  lhs_jac[iql][iqr].jac[jv][iv]+= res[jv][iql]/factor; //res[jv][iql] is negative
//                  lhs_jac[iqr][iqr].jac[jv][iv]+= res[jv][iqr]/factor;
//                  //cout << res[jv][iqr] << " " << res[jv][iql] << "\n";
//              }
               for(jv=0; jv<nv; jv++)
              {
                  blkjacl.jac[jv][iv] = res[jv][iql]/factor; //res[jv][iql] is negative
                  blkjacr.jac[jv][iv] = res[jv][iqr]/factor;
                  //cout << res[jv][iqr] << " " << res[jv][iql] << "\n";
              }
           }
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iqr, blkjacl, 1);
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, blkjacr, 1);


            for(iv=0; iv<nv; iv++)
           {
               for(jv=0; jv<nv; jv++)
              {
                  if(iv==jv) dq[jv][iql] = factor;
                  else       dq[jv][iql] = 0;
                  res[jv][iql] = 0;

                  dq[jv][iqr] = 0;
                  daux[jv][iqr] = 0;
                  res[jv][iqr] = 0;
              }

               fld->dvar( iql,iql+1, q, aux, dq, daux );
               fld->dmflx( ic, ic+1, ifq[0], xq,q,aux,dq,daux,res, 
                                     ifq[1], xq,q,aux,dq,daux,res, 
                                     xc, wnc, wxdc, auxf );
//               for(jv=0; jv<nv; jv++)
//              {
//                  lhs_jac[iql][iql].jac[jv][iv]+= res[jv][iql]/factor; //res[jv][iql] is negative
//                  lhs_jac[iqr][iql].jac[jv][iv]+= res[jv][iqr]/factor;
//                  //cout << res[jv][iqr] << " " << res[jv][iql] << "\n";
//              }
               for(jv=0; jv<nv; jv++)
              {
                  blkjacl.jac[jv][iv] = res[jv][iql]/factor; //res[jv][iql] is negative
                  blkjacr.jac[jv][iv] = res[jv][iqr]/factor;
                  //cout << res[jv][iqr] << " " << res[jv][iql] << "\n";
              }
           }
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iql, blkjacl, 1);
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iql, blkjacr, 1);
        }
     }

  }

   void cFdDomain::petsc_assemble_jac_invi()
  {
      Int iqs,iqe, ibs,ibe, ics,ice;
      Int ic,iv,il,ig,iq, iql, iqr, jv, ib;
      cJacBlk blkjac;
      cTabData data;
      Int asct;
      coo->get( &data );
      data.get( "assembly-sectors", &asct );

      dof->exchange( sq ); //I think exchange(sq) is already called in grad, so I don't need to do it here
      while( dev->transit() )
     {

// boundary faces
         for( ig=0;ig<ng;ig++ )
        {
            bdf[ig]->range( dev->avail(), &ibs,&ibe );
            setv( ibs,ibe, nlhs, ZERO,lhsb[ig] );
            for(ib=ibs; ib<ibe; ib++)
           {
               for(jv=0; jv<nv; jv++)
              {
                  for(iv=0; iv<nv; iv++) 
                 {
                     jac_df[0][ib].jac[jv][iv] = 0;
                     jac_df[1][ib].jac[jv][iv] = 0;
                 }
              }
           }
            bbj[ig]->ilhs( ibs,ibe, xb[ig],qb[ig],auxb[ig],lhsb[ig], 
                           iqb[ig], xq,    q,     aux,     lhsa,     wnb[ig],wxdb[ig],auxfb[ig], jac_df );

//            for(ib=ibs; ib<ibe; ib++)
//           {
//               iqr = iqb[ig][0][ib];
//               for(jv=0; jv<nv; jv++)
//              {
//                  for(iv=0; iv<nv; iv++) 
//                 {
//                     lhs_jac[iqr][iqr].jac[jv][iv]+= jac_df[1][ib].jac[jv][iv];
//                 }
//              }
//           }

            for(ib=ibs; ib<ibe; ib++)
           {
               iqr = iqb[ig][0][ib];
               add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, jac_df[1][ib], 1);
           }

        }


//periodic boundary, shift upper to lower
         prd->range( dev->avail(), &ics,&ice );
         fld->voffset( ics,ice, -1.,iprq[1],q,NULL,qprd );
         for( iv=0;iv<naux;iv++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iq= iprq[1][ic];
               auxprd[iv][ic]= aux[iv][iq];
           }
        }
         fld->ilhs( ics,ice, NULL   , qprd,auxprd,lhsprd,
                             iprq[0], q,   aux,   lhsa, 
                             wnprd,wxdprd,auxfprd, jac_df_prd );
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac.jac[jv][iv] = jac_df_prd[0][ic].jac[jv][iv];
                  //lhs_jac[iqr][iqr].jac[jv][iv]+= jac_df_prd[1][ic].jac[jv][iv];
              }
           }
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, jac_df_prd[1][ic], 1);

            jactimerot(asct, nv, &blkjac, -1.);
//            for(jv=0; jv<nv; jv++)
//           {
//               for(iv=0; iv<nv; iv++)
//              {
//                  cout << jac_df_prd[0][ic].jac[jv][iv] << " " << jac_df_prd[1][ic].jac[jv][iv] << " " 
//                       << blkjac.jac[jv][iv] << "\n";;
//              }
//           }
//            for(jv=0; jv<nv; jv++)
//           {
//               for(iv=0; iv<nv; iv++)
//              {
//                  lhs_jac[iqr][iql].jac[jv][iv]+= blkjac.jac[jv][iv];
//              }
//           }
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iql, blkjac, 1);
        }

//periodic, shift lower to upper
         coo->coffset( ics,ice, 1.,NULL,wnprd,NULL,wnprd ); //rotate it to the LEFT
         fld->voffset( ics,ice, 1.,iprq[0],q,NULL,qprd );
         for( iv=0;iv<naux;iv++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iq= iprq[0][ic];
               auxprd[iv][ic]= aux[iv][iq];
           }
        }
         fld->ilhs( ics,ice, iprq[1], q,   aux,   lhsa, 
                             NULL   , qprd,auxprd,lhsprd,
                             wnprd,wxdprd,auxfprd, jac_df_prd );
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac.jac[jv][iv] = jac_df_prd[1][ic].jac[jv][iv];
//                  lhs_jac[iql][iql].jac[jv][iv]-= jac_df_prd[0][ic].jac[jv][iv];
              }
           }
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iql, jac_df_prd[0][ic], -1);

            jactimerot(asct, nv, &blkjac, 1.);   
//            for(jv=0; jv<nv; jv++)
//           {
//               for(iv=0; iv<nv; iv++)
//              {
//                  lhs_jac[iql][iqr].jac[jv][iv]-= blkjac.jac[jv][iv];
//              }
//           }
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iqr, blkjac, -1);
        }
         coo->coffset( ics,ice, -1.,NULL,wnprd,NULL,wnprd );//roate it back to RIGHT

//internal faces
         cnf->range( dev->avail(),&ics,&ice );
         fld->ilhs( ics,ice, ifq[0], q,aux,lhsa,  
                             ifq[1], q,aux,lhsa,wnc,wxdc,auxf, jac_df );

         for(ic=ics; ic<ice; ic++)
        {
            iql = ifq[0][ic];
            iqr = ifq[1][ic];
//            for(jv=0; jv<nv; jv++)
//           {
//               for(iv=0; iv<nv; iv++) 
//              {
//                  lhs_jac[iql][iql].jac[jv][iv]-= jac_df[0][ic].jac[jv][iv];
//                  lhs_jac[iql][iqr].jac[jv][iv]-= jac_df[1][ic].jac[jv][iv];
//
//                  lhs_jac[iqr][iql].jac[jv][iv]+= jac_df[0][ic].jac[jv][iv];
//                  lhs_jac[iqr][iqr].jac[jv][iv]+= jac_df[1][ic].jac[jv][iv];
//              }
//           }
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iql, jac_df[0][ic], -1);
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iqr, jac_df[1][ic], -1);
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iql, jac_df[0][ic],  1);
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, jac_df[1][ic],  1);
        }

         dof->range( dev->avail(), &iqs,&iqe );
         Real factor=1e-7;
         for(Int iq=iqs; iq<iqe; iq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               for(jv=0; jv<nv; jv++)
              {
                  if(jv==iv) dq[jv][iq] = factor;
                  else       dq[jv][iq] = 0;

                  res[jv][iq] = 0;
                  daux[jv][iq] = 0;
              }

               fld->dvar( iq,iq+1, q, aux, dq, daux );
               fld->daccel( iq,iq+1, omega, wq, xq,q,dq,daux, aux, xdq,res );

               for(jv=0; jv<nv; jv++)
              {
                  blkjac.jac[jv][iv] = res[jv][iq]/factor;
              }
           }
            add_blk_to_petsc_matrix(petsc_A_pre, iq, iq, blkjac,  1);
        }

     }

  }

   void cFdDomain::petsc_assemble_jac_invi2()
  {
      Int iqs,iqe, ibs,ibe, ics,ice;
      Int ic,iv,il,ig,iq, iql, iqr, jv, ib;
      cJacBlk blkjac;
      cTabData data;
      Int asct;
      coo->get( &data );
      data.get( "assembly-sectors", &asct );

      dof->exchange( sq ); //I think exchange(sq) is already called in grad, so I don't need to do it here
      while( dev->transit() )
     {

// boundary faces
         for( ig=0;ig<ng;ig++ )
        {
            bdf[ig]->range( dev->avail(), &ibs,&ibe );
            setv( ibs,ibe, nlhs, ZERO,lhsb[ig] );
            for(ib=ibs; ib<ibe; ib++)
           {
               for(jv=0; jv<nv; jv++)
              {
                  for(iv=0; iv<nv; iv++) 
                 {
                     jac_df[0][ib].jac[jv][iv] = 0;
                     jac_df[1][ib].jac[jv][iv] = 0;
                 }
              }
           }
            bbj[ig]->ilhs( ibs,ibe, xb[ig],qb[ig],auxb[ig],lhsb[ig], 
                           iqb[ig], xq,    q,     aux,     lhsa,     wnb[ig],wxdb[ig],auxfb[ig], jac_df );

//            for(ib=ibs; ib<ibe; ib++)
//           {
//               iqr = iqb[ig][0][ib];
//               for(jv=0; jv<nv; jv++)
//              {
//                  for(iv=0; iv<nv; iv++) 
//                 {
//                     lhs_jac[iqr][iqr].jac[jv][iv]+= jac_df[1][ib].jac[jv][iv];
//                 }
//              }
//           }

            for(ib=ibs; ib<ibe; ib++)
           {
               iqr = iqb[ig][0][ib];
               add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, jac_df[1][ib], 1);
           }

        }


//periodic boundary, shift upper to lower
         prd->range( dev->avail(), &ics,&ice );
         fld->voffset( ics,ice, -1.,iprq[1],q,NULL,qprd );
         setv( ics,ice, nlhs, ZERO,lhsprd );
         for( iv=0;iv<naux;iv++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iq= iprq[1][ic];
               auxprd[iv][ic]= aux[iv][iq];
           }
        }
         fld->ilhs( ics,ice, NULL   , qprd,auxprd,lhsprd,
                             iprq[0], q,   aux,   lhsa, 
                             wnprd,wxdprd,auxfprd, jac_df_prd );
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac.jac[jv][iv] = jac_df_prd[0][ic].jac[jv][iv];
                  //lhs_jac[iqr][iqr].jac[jv][iv]+= jac_df_prd[1][ic].jac[jv][iv];
              }
           }
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, jac_df_prd[1][ic], 1);

            jactimerot(asct, nv, &blkjac, -1.);
//            for(jv=0; jv<nv; jv++)
//           {
//               for(iv=0; iv<nv; iv++)
//              {
//                  cout << jac_df_prd[0][ic].jac[jv][iv] << " " << jac_df_prd[1][ic].jac[jv][iv] << " " 
//                       << blkjac.jac[jv][iv] << "\n";;
//              }
//           }
//            for(jv=0; jv<nv; jv++)
//           {
//               for(iv=0; iv<nv; iv++)
//              {
//                  lhs_jac[iqr][iql].jac[jv][iv]+= blkjac.jac[jv][iv];
//              }
//           }
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iql, blkjac, 1);

            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  jac_inv_prd[0][ic].jac[jv][iv] = jac_df_prd[0][ic].jac[jv][iv];
                  jac_inv_prd[1][ic].jac[jv][iv] = jac_df_prd[1][ic].jac[jv][iv];
              }
           }
        }

         coo->jacoffset(ics, ice, 1., nv, jac_df_prd[0]);
         coo->jacoffset(ics, ice, 1., nv, jac_df_prd[1]);
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac.jac[jv][iv] = jac_df_prd[1][ic].jac[jv][iv];
              }
           }

            add_blk_to_petsc_matrix(petsc_A_pre, iql, iql, jac_df_prd[0][ic], -1);

            jactimerot(asct, nv, &blkjac, 1.);   
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iqr, blkjac, -1);

            lhsa[0][iql] += lhsprd[0][ic];
        }

//periodic, shift lower to upper
//         coo->coffset( ics,ice, 1.,NULL,wnprd,NULL,wnprd ); //rotate it to the LEFT
//         fld->voffset( ics,ice, 1.,iprq[0],q,NULL,qprd );
//         for( iv=0;iv<naux;iv++ )
//        {
//            for( ic=ics;ic<ice;ic++ )
//           {
//               iq= iprq[0][ic];
//               auxprd[iv][ic]= aux[iv][iq];
//           }
//        }
//         fld->ilhs( ics,ice, iprq[1], q,   aux,   lhsa, 
//                             NULL   , qprd,auxprd,lhsprd,
//                             wnprd,wxdprd,auxfprd, jac_df_prd );
//         for(ic=ics; ic<ice; ic++)
//        {
//            iql = iprq[1][ic];
//            iqr = iprq[0][ic];
//            for(jv=0; jv<nv; jv++)
//           {
//               for(iv=0; iv<nv; iv++)
//              {
//                  blkjac.jac[jv][iv] = jac_df_prd[1][ic].jac[jv][iv];
////                  lhs_jac[iql][iql].jac[jv][iv]-= jac_df_prd[0][ic].jac[jv][iv];
//              }
//           }
//            add_blk_to_petsc_matrix(petsc_A_pre, iql, iql, jac_df_prd[0][ic], -1);
//
//            jactimerot(asct, nv, &blkjac, 1.);   
////            for(jv=0; jv<nv; jv++)
////           {
////               for(iv=0; iv<nv; iv++)
////              {
////                  lhs_jac[iql][iqr].jac[jv][iv]-= blkjac.jac[jv][iv];
////              }
////           }
//            add_blk_to_petsc_matrix(petsc_A_pre, iql, iqr, blkjac, -1);
//        }
//         coo->coffset( ics,ice, -1.,NULL,wnprd,NULL,wnprd );//roate it back to RIGHT

//internal faces
         cnf->range( dev->avail(),&ics,&ice );
         fld->ilhs( ics,ice, ifq[0], q,aux,lhsa,  
                             ifq[1], q,aux,lhsa,wnc,wxdc,auxf, jac_df );

         for(ic=ics; ic<ice; ic++)
        {
            iql = ifq[0][ic];
            iqr = ifq[1][ic];
//            for(jv=0; jv<nv; jv++)
//           {
//               for(iv=0; iv<nv; iv++) 
//              {
//                  lhs_jac[iql][iql].jac[jv][iv]-= jac_df[0][ic].jac[jv][iv];
//                  lhs_jac[iql][iqr].jac[jv][iv]-= jac_df[1][ic].jac[jv][iv];
//
//                  lhs_jac[iqr][iql].jac[jv][iv]+= jac_df[0][ic].jac[jv][iv];
//                  lhs_jac[iqr][iqr].jac[jv][iv]+= jac_df[1][ic].jac[jv][iv];
//              }
//           }
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iql, jac_df[0][ic], -1);
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iqr, jac_df[1][ic], -1);
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iql, jac_df[0][ic],  1);
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iqr, jac_df[1][ic],  1);
        }

         dof->range( dev->avail(), &iqs,&iqe );
         Real factor=1e-7;
         for(Int iq=iqs; iq<iqe; iq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               for(jv=0; jv<nv; jv++)
              {
                  if(jv==iv) dq[jv][iq] = factor;
                  else       dq[jv][iq] = 0;

                  res[jv][iq] = 0;
                  daux[jv][iq] = 0;
              }

               fld->dvar( iq,iq+1, q, aux, dq, daux );
               fld->daccel( iq,iq+1, omega, wq, xq,q,dq,daux, aux, xdq,res );

               for(jv=0; jv<nv; jv++)
              {
                  blkjac.jac[jv][iv] = res[jv][iq]/factor;
              }
           }
            add_blk_to_petsc_matrix(petsc_A_pre, iq, iq, blkjac,  1);
        }

     }

  }

   PetscErrorCode cFdDomain::add_blk_to_petsc_matrix( Mat matrix, Int iql, Int iqr, cJacBlk blk, Int sign  )
  {
      Int iv, jv, nnz;
      PetscScalar values[100]; //should be big enough
      PetscErrorCode ierr;
      PetscInt idxm[10], idxn[10];
      Int iqs, iqe;

      dof->range(dev->getrank(), &iqs, &iqe);
      //only assign rows related to the current rank
      if(iql>=iqs && iql<iqe)
     {
         nnz=0;
         for(jv=0; jv<nv; jv++)
        {
            for(iv=0; iv<nv; iv++)
           {
               values[nnz] = -1*sign*blk.jac[jv][iv]; //"-1" because the left hand side is [I/dt + (-J)]
               nnz++;
           }
        }

         idxm[0] = ig_mat[iql];
         idxn[0] = ig_mat[iqr];
         ierr = MatSetValuesBlocked(matrix, 1, idxm, 1, idxn, values, ADD_VALUES); 
         CHKERRQ(ierr);
     }

      ierr = 0;
      return ierr;
  }

   PetscErrorCode cFdDomain::add_blk_to_petsc_matrix_z( Mat matrix, Int iql, Int iqr, cJacBlkZ blk, Real sign  )
  {
      Int iv, jv, nnz;
      PetscScalar values[100]; //should be big enough
      PetscErrorCode ierr;
      PetscInt idxm[10], idxn[10];
      Int iqs, iqe;

      dof->range(dev->getrank(), &iqs, &iqe);
      //only assign rows related to the current rank
      if(iql>=iqs && iql<iqe)
     {
         nnz=0;
         for(jv=0; jv<nv; jv++)
        {
            for(iv=0; iv<nv; iv++)
           {
               values[nnz] = -1.*sign*blk.jac[jv][iv]; //"-1" because the left hand side is [I/dt + (-J)]
               nnz++;
           }
        }

         idxm[0] = ig_mat[iql];
         idxn[0] = ig_mat[iqr];
         ierr = MatSetValuesBlocked(matrix, 1, idxm, 1, idxn, values, ADD_VALUES); 
         CHKERRQ(ierr);
     }

      ierr = 0;
      return ierr;
  }

   PetscErrorCode cFdDomain::add_pseudo_time_to_petsc_matrix( Mat matrix, Int iqs, Int iqe, Real *lhs, Real cfl, Int isign  )
  {    
      Int iq;
      Int iv, jv, nnz;
      PetscScalar values[100]; //should be big enough
      PetscInt idxm[10], idxn[10];
      PetscErrorCode ierr;

      for(iq=iqs; iq<iqe; iq++)
     {
         nnz=0;
         for(iv=0; iv<nv; iv++)
        {
            for(jv=0; jv<nv; jv++) 
           {
               if(iv==jv) values[nnz++] = isign*lhs[iq]/cfl;
               else       values[nnz++] = 0;
           }
        }
         idxm[0] = ig_mat[iq];
         idxn[0] = ig_mat[iq];
         ierr = MatSetValuesBlocked(matrix, 1, idxm, 1, idxn, values, ADD_VALUES); 
         CHKERRQ(ierr);
     }
  }

   PetscErrorCode cFdDomain::petsc_cleanup()
  {
      PetscErrorCode ierr;
      delete[] ilocal;       ilocal=NULL;
      delete[] ighost_local; ighost_local=NULL;
      delete[] ighost;       ighost=NULL;
      //must destroy these stuff before calling PetscFinalize
      VecDestroy(&petsc_csv);
      VecDestroy(&petsc_dcsv);
      VecDestroy(&petsc_rhs);
      VecDestroy(&petsc_baserhs);
      MatDestroy(&petsc_A_mf);
      MatDestroy(&petsc_A_pre);
      KSPDestroy(&petsc_ksp);

      VecDestroy(&petsc_dcsv_z);
      VecDestroy(&petsc_rhs_z);
      KSPDestroy(&petsc_ksp_z);
      MatDestroy(&petsc_A_pre_copy);
      ierr = PetscFinalize();
      //CHKERRQ(ierr);
      return ierr;
  }

# endif
