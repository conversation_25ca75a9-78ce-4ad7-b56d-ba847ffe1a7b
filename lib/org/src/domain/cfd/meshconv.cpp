using namespace std;

# include <iostream>
# include <fstream>
# include <string>
# include <map>
# include <vector>
# include <domain/cfd/meshconv.h>

const Int npc[3] = {2,3,4};

   cMeshconv::cMeshconv()
  {
     Int ip;

     np=0;
     x[0] = NULL;
     x[1] = NULL;
     x[2] = NULL;

     ntri=0;
     for(ip=0; ip<3; ip++) tri[ip] = NULL;

     nquad=0;
     for(ip=0; ip<4; ip++) quad[ip] = NULL;

     ntet=0;
     for(ip=0; ip<4; ip++) tet[ip] = NULL;

     npyra=0;
     for(ip=0; ip<5; ip++) pyra[ip] = NULL;

     nprism=0;
     for(ip=0; ip<6; ip++) prism[ip] = NULL;

     nhex=0;
     for(ip=0; ip<8; ip++) hex[ip] = NULL;

     ng=0;

     nbface3=0;
     bface3[0] = NULL;
     bface3[1] = NULL;
     bface3[2] = NULL;
     bface3[3] = NULL;
     bface3[4] = NULL;
     bface3[5] = NULL;

     nbface4=0;
     bface4[0] = NULL;
     bface4[1] = NULL;
     bface4[2] = NULL;
     bface4[3] = NULL;
     bface4[4] = NULL;
     bface4[5] = NULL;
     bface4[6] = NULL;
  }

   cMeshconv::~cMeshconv()
  {
     Int ip;

     for(ip=0; ip<3; ip++) { delete[] tri[ip];   tri[ip] = NULL;  }
     for(ip=0; ip<4; ip++) { delete[] quad[ip];  quad[ip] = NULL; }
     for(ip=0; ip<4; ip++) { delete[] tet[ip];   tet[ip] = NULL;  }
     for(ip=0; ip<5; ip++) { delete[] pyra[ip];  pyra[ip] = NULL;  }
     for(ip=0; ip<6; ip++) { delete[] prism[ip]; prism[ip] = NULL;  }
     for(ip=0; ip<8; ip++) { delete[] hex[ip];   hex[ip] = NULL;  }
     delete[] bface3[0]; bface3[0]=NULL;
     delete[] bface3[1]; bface3[1]=NULL;
     delete[] bface3[2]; bface3[2]=NULL;
     delete[] bface3[3]; bface3[3]=NULL;
     delete[] bface3[4]; bface3[4]=NULL;
     delete[] bface3[5]; bface3[5]=NULL;
     delete[] bface4[0]; bface4[0]=NULL;
     delete[] bface4[1]; bface4[1]=NULL;
     delete[] bface4[2]; bface4[2]=NULL;
     delete[] bface4[3]; bface4[3]=NULL;
     delete[] bface4[4]; bface4[4]=NULL;
     delete[] bface4[5]; bface4[5]=NULL;
     delete[] bface4[6]; bface4[6]=NULL;
     delete[] x[0]; x[0]=NULL;
     delete[] x[1]; x[1]=NULL;
     delete[] x[2]; x[2]=NULL;
  }

   bool cMeshconv::readgrid( string fnm )
  {
     ifstream fle;
     Int gnx, gnp, gne[MxNSk], gng;
     Int gnb[MxNBG][MxNSk], *giep[MxNSk], *gibp[MxNBG][MxNSk];
     string gbgnm[MxNGPs];    
     Real *gxp;
     Int ip, iek, ie, ig, ick;

     fnm = fnm + ".grid";
     fle.open(fnm.c_str());
     if(!fle.good())
    {
       cout <<"Error: can not read in grid file " << fnm << "\n";
       return false;
    }

     readgrid( &fle, &gnx, &gnp, gne, &gng, gbgnm, gnb, &gxp, giep, gibp);
     fle.close();

     //coordinates
     nx = gnx;
     np = gnp;
                x[0] = new Real [gnp];
                x[1] = new Real [gnp];
     if(nx==3) x[2] = new Real [gnp];
                for(ip=0; ip<gnp; ip++)    x[0][ip] = gxp[ip      ];  
                for(ip=0; ip<gnp; ip++)    x[1][ip] = gxp[ip+  gnp];
     if(nx==3) for(ip=0; ip<gnp; ip++)    x[2][ip] = gxp[ip+2*gnp];
    

     //elements 
     for(iek=0; iek<MxNSk; iek++)
    {
       if(iek==0)
      {
         //triangle
         ntri = 0;
      }
       else if(iek==1)
      {
         //quad
         nquad = 0;
      }
       else if(iek==2)
      {
         //tetrahedron
         if(gne[iek]>0)
        {
           ntet = gne[iek];      
           tet[0] = new Int [ntet];
           tet[1] = new Int [ntet];
           tet[2] = new Int [ntet];
           tet[3] = new Int [ntet];
           for(ie=0; ie<gne[iek]; ie++)  tet[0][ie] = giep[iek][ie           ];
           for(ie=0; ie<gne[iek]; ie++)  tet[1][ie] = giep[iek][ie+1*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  tet[2][ie] = giep[iek][ie+2*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  tet[3][ie] = giep[iek][ie+3*gne[iek]];
        }
      }
       else if(iek==3)
      {
         //pyramid
         if(gne[iek]>0)
        {
           npyra = gne[iek];      
           pyra[0] = new Int [npyra];
           pyra[1] = new Int [npyra];
           pyra[2] = new Int [npyra];
           pyra[3] = new Int [npyra];
           pyra[4] = new Int [npyra];
           for(ie=0; ie<gne[iek]; ie++)  pyra[0][ie] = giep[iek][ie];
           for(ie=0; ie<gne[iek]; ie++)  pyra[1][ie] = giep[iek][ie+1*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  pyra[2][ie] = giep[iek][ie+2*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  pyra[3][ie] = giep[iek][ie+3*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  pyra[4][ie] = giep[iek][ie+4*gne[iek]];
        }
      }
       else if(iek==4)
      {
         //prism
         if(gne[iek]>0)
        {
           nprism = gne[iek];      
           prism[0] = new Int [nprism];
           prism[1] = new Int [nprism];
           prism[2] = new Int [nprism];
           prism[3] = new Int [nprism];
           prism[4] = new Int [nprism];
           prism[5] = new Int [nprism];
           for(ie=0; ie<gne[iek]; ie++) prism[0][ie] = giep[iek][ie];
           for(ie=0; ie<gne[iek]; ie++) prism[1][ie] = giep[iek][ie+1*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++) prism[2][ie] = giep[iek][ie+2*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++) prism[3][ie] = giep[iek][ie+3*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++) prism[4][ie] = giep[iek][ie+4*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++) prism[5][ie] = giep[iek][ie+5*gne[iek]];
        }
      }
       else if(iek==5)
      {
         //hex
         if(gne[iek]>0)
        {
           nhex = gne[iek];      
           hex[0] = new Int [nhex];
           hex[1] = new Int [nhex];
           hex[2] = new Int [nhex];
           hex[3] = new Int [nhex];
           hex[4] = new Int [nhex];
           hex[5] = new Int [nhex];
           hex[6] = new Int [nhex];
           hex[7] = new Int [nhex];
           for(ie=0; ie<gne[iek]; ie++)  hex[0][ie] = giep[iek][ie];
           for(ie=0; ie<gne[iek]; ie++)  hex[1][ie] = giep[iek][ie+1*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[2][ie] = giep[iek][ie+2*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[3][ie] = giep[iek][ie+3*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[4][ie] = giep[iek][ie+4*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[5][ie] = giep[iek][ie+5*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[6][ie] = giep[iek][ie+6*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[7][ie] = giep[iek][ie+7*gne[iek]];
        }
      }
    }


     //boudnary faces
     ng = gng;
     for(ig=0; ig<gng; ig++)
    {
       for(ick=0; ick<MxNSk; ick++)
      {
         if(ick==1)
        {
           if(gnb[ig][ick]>0) (nbface3)+=gnb[ig][ick];
        }
         else if(ick==2)
        {
           if(gnb[ig][ick]>0) (nbface4)+=gnb[ig][ick];
        }
      }
    } 
     bface3[0] = new Int [nbface3];
     bface3[1] = new Int [nbface3];
     bface3[2] = new Int [nbface3];
     bface3[3] = new Int [nbface3];
     bface3[4] = new Int [nbface3];
     bface3[5] = new Int [nbface3];
     bface4[0] = new Int [nbface4];
     bface4[1] = new Int [nbface4];
     bface4[2] = new Int [nbface4];
     bface4[3] = new Int [nbface4];
     bface4[4] = new Int [nbface4];
     bface4[5] = new Int [nbface4];
     bface4[6] = new Int [nbface4];

     nbface3=0;
     nbface4=0;
     for(ig=0; ig<gng; ig++)
    {
       for(ick=0; ick<MxNSk; ick++)
      {
         if(ick==1)
        {
           if(gnb[ig][ick]>0)
          {
             for(ie=0; ie<gnb[ig][ick]; ie++) 
            {
               bface3[0][nbface3] = gibp[ig][ick][ie               ];
               bface3[1][nbface3] = gibp[ig][ick][ie+  gnb[ig][ick]];
               bface3[2][nbface3] = gibp[ig][ick][ie+2*gnb[ig][ick]];
               bface3[3][nbface3] = ig;
               bface3[4][nbface3] = -1;
               bface3[5][nbface3] = -1;
               (nbface3)++;
            }
          }
        }
         else if(ick==2)
        {
           if(gnb[ig][ick]>0)
          {
             for(ie=0; ie<gnb[ig][ick]; ie++) 
            {
               bface4[0][nbface4] = gibp[ig][ick][ie               ];
               bface4[1][nbface4] = gibp[ig][ick][ie+  gnb[ig][ick]];
               bface4[2][nbface4] = gibp[ig][ick][ie+2*gnb[ig][ick]];
               bface4[3][nbface4] = gibp[ig][ick][ie+3*gnb[ig][ick]];
               bface4[4][nbface4] = ig;
               bface4[5][nbface4] = -1;
               bface4[6][nbface4] = -1;
               (nbface4)++;
            }
          }
        }
      }
    } 

     //attach bfaces to volume elements
     bface2ele();

     delete[] gxp; gxp=NULL;
     for(iek=0; iek<MxNSk; iek++)
    {
       if( gne[iek] > 0 )
      {
         delete[] giep[iek]; giep[iek]=NULL;
      }
    }
     for(ig=0; ig<gng; ig++)
    {
       for(ick=0; ick<MxNSk; ick++)
      {
          if( gnb[ig][ick] > 0 )
         {
           delete[] gibp[ig][ick]; gibp[ig][ick]=NULL;
        }
      }
    }

     return true;
  }

   bool cMeshconv::setgrid( Int gnx, Int gnp, Real *gxp, Int *gne, Int gng, 
                            string *gbgnm, Int gnb[MxNBG][MxNSk], 
                            Int *giep[MxNSk], Int *gibp[MxNBG][MxNSk] )
  {
     ifstream fle;
     Int ip, iek, ie, ig, ick;

     //coordinates
     nx = gnx;
     np = gnp;
                x[0] = new Real [gnp];
                x[1] = new Real [gnp];
     if(nx==3)  x[2] = new Real [gnp];
                for(ip=0; ip<gnp; ip++)    x[0][ip] = gxp[ip      ];  
                for(ip=0; ip<gnp; ip++)    x[1][ip] = gxp[ip+  gnp];
     if(nx==3)  for(ip=0; ip<gnp; ip++)    x[2][ip] = gxp[ip+2*gnp];
    

     //elements 
     for(iek=0; iek<MxNSk; iek++)
    {
       if(iek==0)
      {
         //triangle
         ntri = 0;
      }
       else if(iek==1)
      {
         //quad
         nquad = 0;
      }
       else if(iek==2)
      {
         //tetrahedron
         if(gne[iek]>0)
        {
           ntet = gne[iek];      
           tet[0] = new Int [ntet];
           tet[1] = new Int [ntet];
           tet[2] = new Int [ntet];
           tet[3] = new Int [ntet];
           for(ie=0; ie<gne[iek]; ie++)  tet[0][ie] = giep[iek][ie           ];
           for(ie=0; ie<gne[iek]; ie++)  tet[1][ie] = giep[iek][ie+1*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  tet[2][ie] = giep[iek][ie+2*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  tet[3][ie] = giep[iek][ie+3*gne[iek]];
        }
      }
       else if(iek==3)
      {
         //pyramid
         if(gne[iek]>0)
        {
           npyra = gne[iek];      
           pyra[0] = new Int [npyra];
           pyra[1] = new Int [npyra];
           pyra[2] = new Int [npyra];
           pyra[3] = new Int [npyra];
           pyra[4] = new Int [npyra];
           for(ie=0; ie<gne[iek]; ie++)  pyra[0][ie] = giep[iek][ie];
           for(ie=0; ie<gne[iek]; ie++)  pyra[1][ie] = giep[iek][ie+1*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  pyra[2][ie] = giep[iek][ie+2*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  pyra[3][ie] = giep[iek][ie+3*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  pyra[4][ie] = giep[iek][ie+4*gne[iek]];
        }
      }
       else if(iek==4)
      {
         //prism
         if(gne[iek]>0)
        {
           nprism = gne[iek];      
           prism[0] = new Int [nprism];
           prism[1] = new Int [nprism];
           prism[2] = new Int [nprism];
           prism[3] = new Int [nprism];
           prism[4] = new Int [nprism];
           prism[5] = new Int [nprism];
           for(ie=0; ie<gne[iek]; ie++) prism[0][ie] = giep[iek][ie];
           for(ie=0; ie<gne[iek]; ie++) prism[1][ie] = giep[iek][ie+1*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++) prism[2][ie] = giep[iek][ie+2*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++) prism[3][ie] = giep[iek][ie+3*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++) prism[4][ie] = giep[iek][ie+4*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++) prism[5][ie] = giep[iek][ie+5*gne[iek]];
        }
      }
       else if(iek==5)
      {
         //hex
         if(gne[iek]>0)
        {
           nhex = gne[iek];      
           hex[0] = new Int [nhex];
           hex[1] = new Int [nhex];
           hex[2] = new Int [nhex];
           hex[3] = new Int [nhex];
           hex[4] = new Int [nhex];
           hex[5] = new Int [nhex];
           hex[6] = new Int [nhex];
           hex[7] = new Int [nhex];
           for(ie=0; ie<gne[iek]; ie++)  hex[0][ie] = giep[iek][ie];
           for(ie=0; ie<gne[iek]; ie++)  hex[1][ie] = giep[iek][ie+1*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[2][ie] = giep[iek][ie+2*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[3][ie] = giep[iek][ie+3*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[4][ie] = giep[iek][ie+4*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[5][ie] = giep[iek][ie+5*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[6][ie] = giep[iek][ie+6*gne[iek]];
           for(ie=0; ie<gne[iek]; ie++)  hex[7][ie] = giep[iek][ie+7*gne[iek]];
        }
      }
    }


     //boudnary faces
     ng = gng;
     for(ig=0; ig<gng; ig++)
    {
       for(ick=0; ick<MxNSk; ick++)
      {
         if(ick==1)
        {
           if(gnb[ig][ick]>0) (nbface3)+=gnb[ig][ick];
        }
         else if(ick==2)
        {
           if(gnb[ig][ick]>0) (nbface4)+=gnb[ig][ick];
        }
      }
    }

     Int hlp[MxNSk], rhlp[MxNSk];
     for(ig=0; ig<gng; ig++)
    {
        if( gbgnm[ig] == "INLET" ||
            gbgnm[ig] == "WIN" ||
            gbgnm[ig] == "MIN" ||
            gbgnm[ig] == "NRIN" ||
            gbgnm[ig] == "SPIN")
       {
           hlp[ig] = 0;
           rhlp[0] = ig;
       }
        else if( gbgnm[ig] == "EXIT" ||
                 gbgnm[ig] == "WEX" ||
                 gbgnm[ig] == "MEX" ||
                 gbgnm[ig] == "NREX" ||
                 gbgnm[ig] == "SPEX")
       {
           hlp[ig] = 1;
           rhlp[1] = ig;
       }
        else if( gbgnm[ig] == "RIGHT" ||
                 gbgnm[ig] == "PERIODIC.RIGHT" )
       {
           hlp[ig] = 2;
           rhlp[2] = ig;
       }
        else if( gbgnm[ig] == "LEFT" ||
                 gbgnm[ig] == "PERIODIC.LEFT" )
       {
           hlp[ig] = 3;
           rhlp[3] = ig;
       }
        else if( gbgnm[ig] == "HUB"      ||
                 gbgnm[ig] == "INVI.HUB" ||
                 gbgnm[ig] == "INVIHUB"  )
       {
           hlp[ig] = 4;
           rhlp[4] = ig;
       }
        else if( gbgnm[ig] == "CASING"      ||
                 gbgnm[ig] == "INVI.CASING" ||
                 gbgnm[ig] == "INVICASING" )
       {
           hlp[ig] = 5;
           rhlp[5] = ig;
       }
        else if( gbgnm[ig] == "BLADE" ||
                 gbgnm[ig] == "INVI.BLADE" ||
                 gbgnm[ig] == "INVIBLADE" )
       {
           hlp[ig] = 6;
           rhlp[6] = ig;
       }
        else if( gbgnm[ig] == "FREX.INLETH" ||
                 gbgnm[ig] == "FREX.EXITH" )
       {
           //fool the program so that it will go through jm44 and jl09
           hlp[ig] = 4;
           rhlp[4] = ig;
       }
        else if( gbgnm[ig] == "FREX.INLETC" ||
                 gbgnm[ig] == "FREX.EXITC" )
       {
           //fool the program so that it will go through jm44 and jl09
           hlp[ig] = 5;
           rhlp[5] = ig;
       }
        else
       {
          hlp[ig] = ig;
          rhlp[ig] = ig;
       }
    }
     for(ig=0; ig<gng; ig++)
    {
    //   cout << "boundary " << ig << ": " << gbgnm[rhlp[ig]] << "\n";
    } 
 
     bface3[0] = new Int [nbface3];
     bface3[1] = new Int [nbface3];
     bface3[2] = new Int [nbface3];
     bface3[3] = new Int [nbface3];
     bface3[4] = new Int [nbface3];
     bface3[5] = new Int [nbface3];
     bface4[0] = new Int [nbface4];
     bface4[1] = new Int [nbface4];
     bface4[2] = new Int [nbface4];
     bface4[3] = new Int [nbface4];
     bface4[4] = new Int [nbface4];
     bface4[5] = new Int [nbface4];
     bface4[6] = new Int [nbface4];

     nbface3=0;
     nbface4=0;
     for(ig=0; ig<gng; ig++)
    {
       for(ick=0; ick<MxNSk; ick++)
      {
         if(ick==1)
        {
           if(gnb[ig][ick]>0)
          {
             for(ie=0; ie<gnb[ig][ick]; ie++) 
            {
               bface3[0][nbface3] = gibp[ig][ick][ie               ];
               bface3[1][nbface3] = gibp[ig][ick][ie+  gnb[ig][ick]];
               bface3[2][nbface3] = gibp[ig][ick][ie+2*gnb[ig][ick]];
               bface3[3][nbface3] = hlp[ig];
               bface3[4][nbface3] = -1;
               bface3[5][nbface3] = -1;
               (nbface3)++;
            }
          }
        }
         else if(ick==2)
        {
           if(gnb[ig][ick]>0)
          {
             for(ie=0; ie<gnb[ig][ick]; ie++) 
            {
               bface4[0][nbface4] = gibp[ig][ick][ie               ];
               bface4[1][nbface4] = gibp[ig][ick][ie+  gnb[ig][ick]];
               bface4[2][nbface4] = gibp[ig][ick][ie+2*gnb[ig][ick]];
               bface4[3][nbface4] = gibp[ig][ick][ie+3*gnb[ig][ick]];
               bface4[4][nbface4] = hlp[ig];
               bface4[5][nbface4] = -1;
               bface4[6][nbface4] = -1;
               (nbface4)++;
            }
          }
        }
      }
    } 

     //attach bfaces to volume elements
     bface2ele();
     return true;
  }

   void cMeshconv::bface2ele()
  {
     Int ie, je, ic, ip, jc;
     Int ip0, ip1, ip2, ip3, jp0, jp1, jp2, jp3;
     Int ntmp;
     multimap<Int,Int> face3, face4;
     vector<Int> tmpface;
     pair<multimap<Int, Int>::iterator, multimap<Int,Int>::iterator> ret;
     multimap<Int,Int>::iterator it;
     size_t i;

     for(Int ie=0; ie<nbface3; ie++)
    {
       face3.insert(pair<Int,Int>(bface3[0][ie],ie));
       face3.insert(pair<Int,Int>(bface3[1][ie],ie));
       face3.insert(pair<Int,Int>(bface3[2][ie],ie));
    }
     for(Int ie=0; ie<nbface4; ie++)
    {
       face4.insert(pair<Int,Int>(bface4[0][ie],ie));
       face4.insert(pair<Int,Int>(bface4[1][ie],ie));
       face4.insert(pair<Int,Int>(bface4[2][ie],ie));
       face4.insert(pair<Int,Int>(bface4[3][ie],ie));
    }

     //attach tri faces to tet
     if(ntet>0)
    {
       for(ie=0; ie<ntet; ie++)
      {
         for(ic=0; ic<4; ic++)
        {
           ip0 = tetface[0][ic]; 
           ip1 = tetface[1][ic]; 
           ip2 = tetface[2][ic]; 
           ip0 = tet[ip0][ie];
           ip1 = tet[ip1][ie];
           ip2 = tet[ip2][ie];

           tmpface.clear();
           ret = face3.equal_range(ip0); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face3.equal_range(ip1); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face3.equal_range(ip2); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);

           for(i=0; i<tmpface.size(); i++)
          {
             je = tmpface[i];
             jp0 = bface3[0][je];
             jp1 = bface3[1][je];
             jp2 = bface3[2][je];

             if(sameface_weak(ip0, ip1, ip2, jp0, jp1, jp2))
            {
               //make sure the normal of the face poInt towards the flow domain
               bface3[0][je] = ip0;
               bface3[1][je] = ip1;
               bface3[2][je] = ip2;
               bface3[4][je] = 1;
               bface3[5][je] = ie;
               break;
            }
          }
        }
      }
    }

     //attach tri faces to pyramid
     if(npyra>0)
    {
       for(ie=0; ie<npyra; ie++)
      {
         for(ic=0; ic<4; ic++)
        {
           ip0 = pyraface3[0][ic]; 
           ip1 = pyraface3[1][ic]; 
           ip2 = pyraface3[2][ic]; 
           ip0 = pyra[ip0][ie];
           ip1 = pyra[ip1][ie];
           ip2 = pyra[ip2][ie];

           tmpface.clear();
           ret = face3.equal_range(ip0); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face3.equal_range(ip1); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face3.equal_range(ip2); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);

           for(i=0; i<tmpface.size(); i++)
          {
             je = tmpface[i];
             jp0 = bface3[0][je];
             jp1 = bface3[1][je];
             jp2 = bface3[2][je];

             if(sameface_weak(ip0, ip1, ip2, jp0, jp1, jp2))
            {
               //make sure the normal of the face poInt towards the flow domain
               bface3[0][je] = ip0;
               bface3[1][je] = ip1;
               bface3[2][je] = ip2;
               bface3[4][je] = 2;
               bface3[5][je] = ie;
               break;
            }
          }
        }
      }
    }

     //attach tri faces to prism
     if(nprism>0)
    {
       for(ie=0; ie<nprism; ie++)
      {
         for(ic=0; ic<2; ic++)
        {
           ip0 = prismface3[0][ic]; 
           ip1 = prismface3[1][ic]; 
           ip2 = prismface3[2][ic]; 
           ip0 = prism[ip0][ie];
           ip1 = prism[ip1][ie];
           ip2 = prism[ip2][ie];

           tmpface.clear();
           ret = face3.equal_range(ip0); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face3.equal_range(ip1); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face3.equal_range(ip2); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);

           for(i=0; i<tmpface.size(); i++)
          {
             je = tmpface[i];
             jp0 = bface3[0][je];
             jp1 = bface3[1][je];
             jp2 = bface3[2][je];

             if(sameface_weak(ip0, ip1, ip2, jp0, jp1, jp2))
            {
               //make sure the normal of the face poInt towards the flow domain
               bface3[0][je] = ip0;
               bface3[1][je] = ip1;
               bface3[2][je] = ip2;
               bface3[4][je] = 0;
               bface3[5][je] = ie;
               break;
            }
          }
        }
      }
    }

     //attach quad faces to pyramid
     if(npyra>0)
    {
       for(ie=0; ie<npyra; ie++)
      {
         ip0 = pyraface4[0]; 
         ip1 = pyraface4[1]; 
         ip2 = pyraface4[2]; 
         ip3 = pyraface4[2]; 
         ip0 = pyra[ip0][ie];
         ip1 = pyra[ip1][ie];
         ip2 = pyra[ip2][ie];
         ip3 = pyra[ip3][ie];

         tmpface.clear();
         ret = face4.equal_range(ip0); 
         for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
         ret = face4.equal_range(ip1); 
         for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
         ret = face4.equal_range(ip2); 
         for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
         ret = face4.equal_range(ip3); 
         for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);

         for(i=0; i<tmpface.size(); i++)
        {
           je = tmpface[i];
           jp0 = bface4[0][je];
           jp1 = bface4[1][je];
           jp2 = bface4[2][je];
           jp3 = bface4[3][je];

           if(sameface_weak(ip0, ip1, ip2, ip3, jp0, jp1, jp2, jp3))
          {
             //make sure the normal of the face poInt towards the flow domain
             bface4[0][je] = ip0;
             bface4[1][je] = ip1;
             bface4[2][je] = ip2;
             bface4[3][je] = ip3;
             bface4[5][je] = 2;
             bface4[6][je] = ie;
             break;
          }
        }
      }
    }

     //attach quad faces to prism
     if(nprism>0)
    {
       for(ie=0; ie<nprism; ie++)
      {
         for(ic=0; ic<3; ic++)
        {
           ip0 = prismface4[0][ic]; 
           ip1 = prismface4[1][ic]; 
           ip2 = prismface4[2][ic]; 
           ip3 = prismface4[3][ic]; 
           ip0 = prism[ip0][ie];
           ip1 = prism[ip1][ie];
           ip2 = prism[ip2][ie];
           ip3 = prism[ip3][ie];

           tmpface.clear();
           ret = face4.equal_range(ip0); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face4.equal_range(ip1); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face4.equal_range(ip2); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face4.equal_range(ip3); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);

           for(i=0; i<tmpface.size(); i++)
          {
             je = tmpface[i];
             jp0 = bface4[0][je];
             jp1 = bface4[1][je];
             jp2 = bface4[2][je];
             jp3 = bface4[3][je];
 
             if(sameface_weak(ip0, ip1, ip2, ip3, jp0, jp1, jp2, jp3))
            {
               //make sure the normal of the face poInt towards the flow domain
               bface4[0][je] = ip0;
               bface4[1][je] = ip1;
               bface4[2][je] = ip2;
               bface4[3][je] = ip3;
               bface4[5][je] = 1;
               bface4[6][je] = ie;
               break;
            }
          }
        }
      }
    }

     //attach quad faces to hexes
     if(nhex>0)
    {
       for(ie=0; ie<nhex; ie++)
      {
         //cout << "ie " << ie << "\n";
         for(ic=0; ic<6; ic++)
        {
           ip0 = hexface[0][ic]; 
           ip1 = hexface[1][ic]; 
           ip2 = hexface[2][ic]; 
           ip3 = hexface[3][ic]; 
           ip0 = hex[ip0][ie];
           ip1 = hex[ip1][ie];
           ip2 = hex[ip2][ie];
           ip3 = hex[ip3][ie];

           tmpface.clear();
           ret = face4.equal_range(ip0); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face4.equal_range(ip1); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face4.equal_range(ip2); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);
           ret = face4.equal_range(ip3); 
           for(it=ret.first; it!=ret.second; it++) tmpface.push_back(it->second);

           for(i=0; i<tmpface.size(); i++)
          {
             je = tmpface[i];
             jp0 = bface4[0][je];
             jp1 = bface4[1][je];
             jp2 = bface4[2][je];
             jp3 = bface4[3][je];
 
             if(sameface_weak(ip0, ip1, ip2, ip3, jp0, jp1, jp2, jp3))
            {
               //make sure the normal of the face poInt towards the flow domain
               bface4[0][je] = ip0;
               bface4[1][je] = ip1;
               bface4[2][je] = ip2;
               bface4[3][je] = ip3;
               bface4[5][je] = 0;
               bface4[6][je] = ie;
               break;
            }
          }
        }
      }
    }
  }


   bool cMeshconv::sameface_weak( Int ip0, Int ip1, Int ip2, Int jp0, Int jp1, Int jp2 )
  {
     Int ntmp;

     ntmp=0;
     if(ip0==jp0 || ip0==jp1 || ip0==jp2) ntmp++;
     if(ip1==jp0 || ip1==jp1 || ip1==jp2) ntmp++;
     if(ip2==jp0 || ip2==jp1 || ip2==jp2) ntmp++;
 
     return (ntmp==3);
  }

   bool cMeshconv::sameface_weak( Int ip0, Int ip1, Int ip2, Int ip3, Int jp0, 
                                  Int jp1, Int jp2, Int jp3 )
  {
     Int ntmp;

     ntmp=0;
     if(ip0==jp0 || ip0==jp1 || ip0==jp2 || ip0==jp3) ntmp++;
     if(ip1==jp0 || ip1==jp1 || ip1==jp2 || ip1==jp3) ntmp++;
     if(ip2==jp0 || ip2==jp1 || ip2==jp2 || ip2==jp3) ntmp++;
     if(ip3==jp0 || ip3==jp1 || ip3==jp2 || ip3==jp3) ntmp++;

     return (ntmp==4);
  }

   void cMeshconv::readgrid( ifstream *fle, Int *nx, Int *gnp, Int *gne, 
                             Int *ng, string *bgnm, Int gnb[MxNBG][MxNSk],
                             Real **gxp, Int *giep[MxNSk],
                             Int *gibp[MxNBG][MxNSk])
  {

     Int          i, iek, ig, ick;
     Real        *rbuf;
     Int         *ibuf;
     Int          npe[MxNSk];

     *fle >> *nx;
     *fle >> *gnp;
     *gxp= new Real[(*nx)*(*gnp)];
      for( i=0;i<(*gnp)*(*nx);i++ )
     {
       *fle >> (*gxp)[i];
     }

      for(iek=0; iek<MxNSk; iek++)
     {
         *fle >> gne[iek];
         *fle >> npe[iek];
         if( gne[iek] > 0 )
        {
            //cout << "RETRIEVED "<<gne[iek]<<" "<<npe[iek]<<"-NODED ELEMENTS FROM THE JM28 GRID\n";
            giep[iek]= new Int[ npe[iek]*gne[iek] ];
            for( i=0;i<npe[iek]*gne[iek];i++ )
           {
               *fle >> giep[iek][i];
           }
        }
     }

     *fle >> *ng;
     for(ig=0; ig<*ng; ig++)
    {
        *fle >> bgnm[ig];
        for(ick=0; ick<MxNSk; ick++)
       {
          *fle >> gnb[ig][ick];
           if( gnb[ig][ick] > 0 )
          {
             //cout << "RETRIEVED "<<gnb[ig][ick]<<" "<<npc[ick]<<"-NODED ELEMENTS FROM THE JM28 GRID FOR GROUP "<<ig<<"\n";
             gibp[ig][ick]= new Int[ npc[ick]*gnb[ig][ick] ];
             for( i=0;i<npc[ick]*gnb[ig][ick];i++ )
            {
                 *fle >> gibp[ig][ick][i];
            }
         }
       }
    }
  }

   void cMeshconv::writeplt( string fnm, int qnblade, int qmblade  )
  {
     Int ip,ie,ix, ntmp;
     const char* cname;
     int lname;
     int nstag, istag[1], nblade[1], mblade[1];

     float *tmpx;
     int tmpnp, tmpntet, tmpnpyra, tmpnprism, tmpnhex, tmpnbface3, tmpnbface4;
     int *tmptet, *tmppyra, *tmpprism, *tmphex, *tmpbface3, *tmpbface4;

     //prepare arrays for fortran
     tmpnp = np;
     ntmp=0;
     tmpx = new float [nx*np];
     for(ip=0; ip<np; ip++)
    {
       tmpx[ntmp++] = x[0][ip];
       tmpx[ntmp++] = x[1][ip];
       tmpx[ntmp++] = x[2][ip];
    }


     tmpntet = ntet;
     if(ntet>0)
    {
       ntmp=0;
       tmptet = new int [4*ntet];
       for(ie=0; ie<ntet; ie++)
      {
          tmptet[ntmp++] = tet[0][ie] + 1;
          tmptet[ntmp++] = tet[1][ie] + 1;
          tmptet[ntmp++] = tet[2][ie] + 1;
          tmptet[ntmp++] = tet[3][ie] + 1;
      }
    }

     tmpnpyra = npyra;
     if(npyra>0)
    {
       tmppyra = new int [5*npyra];

       ntmp=0;
       for(ie=0; ie<npyra; ie++) 
      {
         tmppyra[ntmp++] = pyra[0][ie] + 1;
         tmppyra[ntmp++] = pyra[1][ie] + 1;
         tmppyra[ntmp++] = pyra[2][ie] + 1;
         tmppyra[ntmp++] = pyra[3][ie] + 1;
         tmppyra[ntmp++] = pyra[4][ie] + 1;
      }
    }

     tmpnprism = nprism;
     if(nprism>0)
    {
       tmpprism = new int [6*nprism];
  
       ntmp=0;
       for(ie=0; ie<nprism; ie++)
      {
         tmpprism[ntmp++] = prism[0][ie] + 1;
         tmpprism[ntmp++] = prism[1][ie] + 1;
         tmpprism[ntmp++] = prism[2][ie] + 1;
         tmpprism[ntmp++] = prism[3][ie] + 1;
         tmpprism[ntmp++] = prism[4][ie] + 1;
         tmpprism[ntmp++] = prism[5][ie] + 1;
      }
    }

     tmpnhex = nhex;
     if(nhex>0)
    {
       tmphex = new int [8*nhex];

       ntmp=0;
       for(ie=0; ie<nhex; ie++) 
      {
         tmphex[ntmp++] = hex[0][ie] + 1;
         tmphex[ntmp++] = hex[1][ie] + 1;
         tmphex[ntmp++] = hex[2][ie] + 1;
         tmphex[ntmp++] = hex[3][ie] + 1;
         tmphex[ntmp++] = hex[4][ie] + 1;
         tmphex[ntmp++] = hex[5][ie] + 1;
         tmphex[ntmp++] = hex[6][ie] + 1;
         tmphex[ntmp++] = hex[7][ie] + 1;
      }
    }

     tmpnbface3 = nbface3;
     if(nbface3>0)
    {
       tmpbface3 = new int [6*nbface3];

       ntmp=0;
       for(ie=0; ie<nbface3; ie++) 
      {
          tmpbface3[ntmp++] = bface3[0][ie] + 1;
          tmpbface3[ntmp++] = bface3[1][ie] + 1;
          tmpbface3[ntmp++] = bface3[2][ie] + 1;
          //tmpbface3[ntmp++] = bface3[3][ie] + 1;
          //tmpbface3[ntmp++] = bface3[4][ie];
          tmpbface3[ntmp++] = bface3[4][ie];
          tmpbface3[ntmp++] = bface3[3][ie] + 1;
          tmpbface3[ntmp++] = bface3[5][ie] + 1;
      }
    }
    
     tmpnbface4 = nbface4;
     if(nbface4>0)
    {
       tmpbface4 = new int [7*nbface4];

       ntmp=0;
       for(ie=0; ie<nbface4; ie++) 
      {
         tmpbface4[ntmp++] = bface4[0][ie] + 1;
         tmpbface4[ntmp++] = bface4[1][ie] + 1;
         tmpbface4[ntmp++] = bface4[2][ie] + 1;
         tmpbface4[ntmp++] = bface4[3][ie] + 1;
         //tmpbface4[ntmp++] = bface4[4][ie] + 1;
         //tmpbface4[ntmp++] = bface4[5][ie];
         tmpbface4[ntmp++] = bface4[5][ie];
         tmpbface4[ntmp++] = bface4[4][ie] + 1;
         tmpbface4[ntmp++] = bface4[6][ie] + 1;
         //cout << bface4[0][ie]+1 << " " << bface4[1][ie] +1 << " " << bface4[2][ie] +1 << " " 
         //     << bface4[3][ie]+1 << "==================\n";
      }
    }

    // for(ip=0; ip<3*np; ip+=3)
    //{
    //   cout << ip/3+1 << ": " << tmpx[ip] << " " << tmpx[ip+1] << " " << tmpx[ip+2] << "\n";
    //}

    // for(ie=0; ie<8*nhex; ie+=8)
    //{
    // cout << ie/8+1 << ":  " << tmphex[ie+0] << " " << tmphex[ie+1] << " " << tmphex[ie+2] << " "
    //      << tmphex[ie+3]  << " " << tmphex[ie+4] << " " << tmphex[ie+5] << " " << tmphex[ie+6] 
    //        << " " << tmphex[ie+7] << "\n";
    //        
    //}     

    // cout << "boundary faces\n"; 
    // for(ie=0; ie<7*nbface4; ie+=7)
    //{
    // cout << ie/7+1 << ": " << tmpbface4[ie+0] << " " << tmpbface4[ie+1] << " " << tmpbface4[ie+2]
    //         << " "
    //        << tmpbface4[ie+3] << " " << tmpbface4[ie+4] << " " << tmpbface4[ie+5] << " "
    //        << tmpbface4[ie+6] << "\n";   
    //}
     

     nstag = 1;
     nblade[0] = qnblade;
     mblade[0] = qmblade;
     istag[0] = np;
     //cout << "nblade " << nblade[0] << "\n";
     //cout << "mblade " << mblade[0] << "\n";
     //cout << "istag " << istag[0] << "\n";
     cname= fnm.c_str();
     lname= fnm.length();
//     outplt( &lname, cname, tmptet, tmppyra, tmpprism, tmphex,
//             tmpbface3, tmpbface4, tmpx, &tmpntet, &tmpnpyra, &tmpnprism,
//             &tmpnhex, &tmpnp, &tmpnbface3, &tmpnbface4, &nstag, istag, 
//             nblade, mblade );

     //cleanup
     delete[] tmpx; tmpx=NULL;
     if(ntet>0)
    {
       delete[] tmptet; tmptet=NULL;
    }

     if(npyra>0)
    {
       delete[] tmppyra; tmppyra=NULL;
    }

     if(nprism>0)
    {
       delete[] tmpprism; tmpprism=NULL;
    }

     if(nhex>0)
    {
       delete[] tmphex; tmphex=NULL;
    }

     if(nbface3>0)
    {
       delete[] tmpbface3; tmpbface3=NULL;
    }
    
     if(nbface4>0)
    {
       delete[] tmpbface4; tmpbface4=NULL;
    }
  }

