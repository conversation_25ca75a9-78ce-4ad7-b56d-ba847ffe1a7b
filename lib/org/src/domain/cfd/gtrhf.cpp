
using namespace std;

#  include <domain/cfd/domain.h>
#  include <assert.h>

   void cFdDomain::gtrhf( cAu3xView<Real>& r )
  {
      Int iqs,iqe,ibs,ibe,ics,ice;
      Int ix,ig,iv,iq,ic;
      Int iorder = spatial_order;

      for( iv=0;iv<nv;iv++ )
     {
         dof->exchange( sdqdx+iv*nx*nq );
     }
      dof->exchange( sdxdx );
      dof->exchange( swq );

      while( dev->transit( ) )
     {
//         #pragma acc enter data copyin ( iprq0[0:nprq],iprq1[0:nprq])
//         #pragma acc enter data copyin ( swq[0:(nx+1)*nq],sxdq[0:nvel*nq] )
//         #pragma acc enter data copyin ( sxqprd[0:nx*nprq],sqprd[0:nv*nprq],sdxdxprd[0:nx*nx*nprq],sdqdxprd[0:nx*nv*nprq],sauxprd[0:naux*nprq],srhsprd[0:nv*nprq], \
//                                         sxprd[0:nx*nprq], swnprd[0:(nx+1)*nprq], swxdprd[0:nprq], sauxfprd[0:nauxf*nprq])
//         #pragma acc enter data copyin (sifq[0:2*nfc], sxq[0:nx*nq], sq[0:nv*nq], sdxdx[0:nx*nx*nq], sdqdx[0:nx*nv*nq], saux[0:naux*nq], srhs[0:nv*nq], sxc[0:nx*nfc], \
//                                        swnc[0:(nx+1)*nfc], swxdc[0:nfc], sauxf[0:nauxf*nfc])
//         for( ig=0;ig<ng;ig++ )
//        {
//            #pragma acc enter data copyin(sxb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],srhsb[ig][0:nv*nbb[ig]], siqb[ig][0:nbb[ig]], \
//                                          sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],srhs[0:nv*nq], \
//                                          swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]],sauxfb[ig][0:nauxf*nbb[ig]])
//        }
//         #pragma acc enter data copyin ( this)
//         start_acc_device();

// auxiliary variables for the degrees of freedom
         dof->range( dev->avail(), &iqs,&iqe );
         //fld->auxv( iqs,iqe, q,aux ); 
         fld->auxv( iqs,iqe, q,aux, "d" ); 
//
         //fld->accel( iqs,iqe, omega, wq, xq,q,aux, xdq,r );
         fld->accel( iqs,iqe, omega, wq, xq,q,aux, xdq,rhs );

// save conserved variables
         //if( unst ){ fld->cnsv( iqs,iqe, q, aux, u[0] ); };
         if( unst ){ fld->cnsv( iqs,iqe, q, aux, u[0],"d" ); };

// boundary faces
         for( ig=0;ig<ng;ig++ )
        {
            bdf[ig]->range( dev->avail(), &ibs,&ibe );
            bbj[ig]->auxv( ibs,ibe, qb[ig],auxb[ig], "d" );
            setv( ibs,ibe, nv, ZERO, rhsb[ig],"d" );
            bbj[ig]->iflx( ibs,ibe, xb[ig],qb[ig],auxb[ig],rhsb[ig], iqb_view[ig],xq,q,aux,rhs, wnb[ig],wxdb[ig],auxfb[ig] );
        }


// Inviscid fluxes - periodic faces
         prd->range( dev->avail(), &ics,&ice );
         setv( ics,ice, nv, ZERO,rhsprd,"d" );
        #pragma acc parallel loop \
         present(sauxprd[0:naux*nprq],saux[0:naux*nq],siprq1[0:nprq],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iq= siprq1[ic];
            for( iv=0;iv<naux;iv++ )
           {
               //auxprd[iv][ic]= aux[iv][iq];
               sauxprd[ADDR(iv,ic,nprq)]= saux[ADDR(iv,iq,nq)];
           }
        }
         coo->coffset( ics,ice, -1.,       iprq1,xq,                      NULL_iview, xqprd );
         //fld->voffset( ics,ice, -1.,       iprq[1], q,                      NULL,  qprd );
         fld->voffset( ics,ice, -1.,       iprq1, q,                      NULL_iview,  qprd );
         //fld->goffset( ics,ice, -1., ijdx, iprq[1], dxdx,dqdx, NULL, dxdxprd, dqdxprd );
         fld->goffset( ics,ice, -1., iprq1, dxdx,dqdx, NULL_iview, dxdxprd, dqdxprd );
         if( ilev == 0 )
//       if( false )
        {
//            fld->iflx( ics,ice, NULL, qprd,auxprd,rhsprd, iprq[0], q,aux,r, wnprd,wxdprd,auxfprd );
            fld->iflxmuscl( ics,ice, NULL_iview,   -1, xqprd,qprd,dxdxprd,dqdxprd,auxprd,rhsprd, 
                                     iprq0,        -1, xq,   q,   dxdx,   dqdx,   aux,   rhs,   
                                     xprd,wnprd,wxdprd,auxfprd, iorder );
        }
         else
        {
            //fld->iflx( ics,ice, NULL, qprd,auxprd,rhsprd, siprq0, q,aux,r, wnprd,wxdprd,auxfprd );
            assert(0);
        }
         //fld->roffset( ics,ice,  1, NULL, rhsprd, NULL, rhsprd );
         fld->roffset( ics,ice,  1, rhsprd );
        #pragma acc parallel loop \
         present(srhsprd[0:nv*nprq],srhs[0:nv*nq],siprq1[0:nprq],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iq= siprq1[ic];
            for( iv=0;iv<nv;iv++ )
           {
               //r[iv][iq]+= rhsprd[iv][ic];
               srhs[ADDR(iv,iq,nq)]+= srhsprd[ADDR(iv,ic,nprq)];
           }
        }

// Inviscid fluxes - inner faces

         cnf->range( dev->avail(), &ics,&ice );
         if( ilev == 0 )
//       if( false )
        {
//              fld->iflx(  ics,ice, ifq[0], q,aux,r, ifq[1], q,aux,r, wnc,wxdc, auxf );
            fld->iflxmuscl( ics,ice, ifq, xq,q,dxdx,dqdx,aux,rhs, 
                            xc,wnc,wxdc,auxf, iorder );
        }
         else
        {
              //fld->iflx(  ics,ice, ifq[0], q,aux,r, ifq[1], q,aux,r, wnc,wxdc, auxf );
              //fld->iflx(  ics,ice, ifq, q,aux,r,wnc,wxdc, auxf );
              assert(0);
        }
//         #pragma acc exit data copyout ( iprq0[0:nprq],iprq1[0:nprq])
//         #pragma acc exit data copyout ( swq[0:(nx+1)*nq],sxdq[0:nvel*nq] )
//         #pragma acc exit data copyout ( sxqprd[0:nx*nprq],sqprd[0:nv*nprq],sdxdxprd[0:nx*nx*nprq],sdqdxprd[0:nx*nv*nprq],sauxprd[0:naux*nprq],srhsprd[0:nv*nprq], \
//                                         sxprd[0:nx*nprq], swnprd[0:(nx+1)*nprq], swxdprd[0:nprq], sauxfprd[0:nauxf*nprq])
//         #pragma acc exit data copyout (sifq[0:2*nfc], sxq[0:nx*nq], sq[0:nv*nq], sdxdx[0:nx*nx*nq], sdqdx[0:nx*nv*nq], saux[0:naux*nq], srhs[0:nv*nq], sxc[0:nx*nfc], \
//                                        swnc[0:(nx+1)*nfc], swxdc[0:nfc], sauxf[0:nauxf*nfc])
//         for( ig=0;ig<ng;ig++ )
//        {
//            #pragma acc exit data copyout(sxb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],srhsb[ig][0:nv*nbb[ig]], siqb[ig][0:nbb[ig]], \
//                                          sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],srhs[0:nv*nq], \
//                                          swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]],sauxfb[ig][0:nauxf*nbb[ig]])
//        }
//         #pragma acc exit data copyout ( this)
//         exit_acc_device();
     } 

//      for(int iq=0; iq<nq; iq++)
//     {
//         cout << "iq " << iq << " ";
//         for(iv=0; iv<nv; iv++)
//        {
//            cout << rhs(iv,iq) << " ";
//        }
//         cout << " gtrhf \n";
//     }
//exit(0);

  }

