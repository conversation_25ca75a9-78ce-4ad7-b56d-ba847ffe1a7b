using namespace std;

#include <domain/cfd/domain.h>


   void cFdDomain::movegrid()
  {
      Int ig, ix, ist, ien, ip, jp;
      Int *tmpsipb;

      setv( 0,pts->size(), nx,ZERO, xdp, "d" );

      //this only set the values of xbp and xdbp, more work is needed to move the mesh
      for( ig=0;ig<ng;ig++ )
     {
         ist=0; 
         ien=0;
         bpts( ig, &ien, &tmpsipb );

        #pragma acc enter data copyin (tmpsipb[0:npb[ig]])
        #pragma acc enter data copyin (npb[0:ng])

        #pragma acc parallel loop gang vector\
         present(npb[0:ng],tmpsipb[0:npb[ig]],sxbp[ig][0:nx*npb[ig]],sxp[0:nx*np],sxdbp[ig][0:nvel*npb[ig]],sxdp[0:nx*np],this)\
         default(none)
         for(ip=ist; ip<ien; ip++)
        {
            jp = tmpsipb[ip];    
            for( ix=0;ix<nx;ix++ )
           {
               //xbp[ig](ix,ip) = xp(ix,jp);
               sxbp[ig][ADDR(ix,ip,npb[ig])] = sxp[ADDR(ix,jp,np)];
           } 

            for( ix=0;ix<nvel;ix++ )
           {
               //xdbp[ig](ix,ip)=xdp(ix,jp);
               sxdbp[ig][ADDR(ix,ip,npb[ig])]=sxdp[ADDR(ix,jp,np)];
           }
        }
        #pragma acc exit data copyout(tmpsipb[0:npb[ig]])
        #pragma acc exit data copyout(npb[0:ng])
         delete[] tmpsipb; tmpsipb=NULL;
     }

      if( crs )
     {
         ((cFdDomain*)crs)->movegrid();
     }

  }

