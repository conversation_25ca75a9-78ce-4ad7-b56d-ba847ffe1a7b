   using namespace std; 

#  include <domain/cfd/domain.h>

   void cFdDomain::cell2node()
  {
     Int iv, iek, jp, ie,ip, iq, ix;
     Int ig, ick, ib;
     Int ip0, ip1, ip2, ip3;

     Real *qp[MxNVs];
     Real *wp[1];

     wp[0] = new Real [np];
     for(iv=0; iv<nv; iv++)
    {
       qp[iv] = new Real [np];
    }

     for(iv=0; iv<nv; iv++)
    {
       for(ip=0; ip<np; ip++)
      {
         qp[iv][ip] = 0;
         wp[0][ip]= 0;
      }
    }

       for(iek=0; iek<MxNSk; iek++)
      {
         if(ne[iek]>0)
        {
           for(jp=0; jp<nep[iek]; jp++)
          {
             for(ie=0; ie<ne[iek]; ie++)
            {
               ip = iep[iek][jp][ie];
               iq = ieq[iek][0][ie];
              for(iv=0; iv<nv; iv++)
             {  

               qp[iv][ip] += q(iv,iq)*wq(0,iq);
            }
               wp[0][ip] += wq(0,iq);
          }
        }
      }
    }

     for(iv=0; iv<nv; iv++)
    {
       for(ip=0; ip<np; ip++)
      {
          qp[iv][ip]/=wp[0][ip];
 //       qp[iv][ip]=0;
      }
    }
//output
//  Int nb[MxNGrp][MxNSk];

    string fname;
    fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+".tec";
    ofstream fle( fname.c_str() );

    Int *itmp, *itmp1, *itmp2;
    itmp= new Int[np];
    itmp1= new Int[np];
    itmp2= new Int[np];
    Int  nbp=0;
    Int tmpnpe[3]= {2,3,4};


    if( nx == 3 )
   {
       fle << "VARIABLES = ";
       for( ix=0;ix<nx;ix++ )
      {
          fle << "\"X"<<strc(ix)<<"\" ";
      } 
       for( iv=0;iv<nv;iv++ )
      {
          fle << "\"V"<<strc(iv)<<"\" ";
      } 
       for(ig=0; ig<ng; ig++)
      { 
         for(ick=0; ick<MxNSk; ick++)
        {
//         nb[ig][ick]= bld[ig][ick]->size();
           if(nb[ig][ick]>0)
          {
             for( ip=0;ip<np;ip++ )
            {
                itmp[ip]=-1;
            }
             for( jp=0;jp<tmpnpe[ick];jp++ )
            {
                for( ib=0;ib<nb[ig][ick];ib++ )
               {
                   ip= ibp[ig][ick][jp][ib]; 
                   itmp[ip]=1;
               }
            }
   
             nbp=0;
             for( ip=0;ip<np;ip++ )
            {
                if( itmp[ip] == 1 )
               {
                   itmp[ip]= nbp++;
               }
            }
   
             for( jp=0;jp<tmpnpe[ick];jp++ )
            {
                for( ib=0;ib<nb[ig][ick];ib++ )
               {
                   ip= ibp[ig][ick][jp][ib]; 
                   if(itmp[ip]!=-1)
                  {
                     itmp1[itmp[ip]] = ip;
                     itmp2[ip] = itmp[ip];
                  }
               }
            }
   

             fle << "ZONE" << " " << "N=" << nbp << " " << "E=" << nb[ig][ick] << " " << "F=FEPOINT" << " " << "ET=QUADRILATERAL\n";

          //coordinates
             for(ip=0; ip<np; ip++)
            {
//            ip0 = itmp1[ip];
                 if( itmp[ip] != -1 )
                {
                    for( ix=0;ix<nx;ix++ )
                   {
                       fle << xp(ix,ip) << " ";
                   }
                    for( iv=0;iv<nv;iv++ )
                   {
                       fle << qp[iv][ip] << " ";
                   }
                    fle << "\n";
                }
            }
   
             fle << "\n";
             for(ib=0; ib<nb[ig][ick]; ib++)
            {
                if(ick==1) 
               {
                   ip0 = ibp[ig][ick][0][ib];
                   ip1 = ibp[ig][ick][1][ib];
                   ip2 = ibp[ig][ick][2][ib];
                   ip3 = ibp[ig][ick][2][ib];

                   ip0 = itmp[ip0];      
                   ip1 = itmp[ip1];      
                   ip2 = itmp[ip2];  
                   ip3 = itmp[ip3];  

                   ip0++; 
                   ip1++; 
                   ip2++; 
                   ip3++; 

                   fle << ip0 << " " << ip1 << " " << ip2 << " " << ip3 << "\n";
               }
                else if(ick==2) 
               {
                   ip0 = ibp[ig][ick][0][ib];
                   ip1 = ibp[ig][ick][1][ib];
                   ip2 = ibp[ig][ick][2][ib];
                   ip3 = ibp[ig][ick][3][ib];
   
                   ip0 = itmp[ip0];      
                   ip1 = itmp[ip1];      
                   ip2 = itmp[ip2];      
                   ip3 = itmp[ip3];      
   
                   ip0++; 
                   ip1++; 
                   ip2++; 
                   ip3++; 
   
                   fle << ip0 << " " << ip1 << " " << ip2 << " " << ip3 << "\n";
               }
            }
          }
        }
     }
   }
    else
   {
       fle << "VARIABLES = ";
       for( ix=0;ix<3;ix++ )
      {
          fle << "\"X"<<strc(ix)<<"\" ";
      } 
       for( iv=0;iv<nv;iv++ )
      {
          fle << "\"V"<<strc(iv)<<"\" ";
      } 
      for(iek=0; iek<nek; iek++)
     {
        if(ne[iek]>0)
       {
          for( ip=0;ip<np;ip++ )
         {
             itmp[ip]=-1;
         }
          for( jp=0;jp<nep[iek];jp++ )
         {
             for( ib=0;ib<ne[iek];ib++ )
            {
                ip= iep[iek][jp][ib]; 
                itmp[ip]=1;
            }
         }

          nbp=0;
          for( ip=0;ip<np;ip++ )
         {
             if( itmp[ip] == 1 )
            {
                itmp[ip]= nbp++;
            }
         }

          for( jp=0;jp<nep[iek];jp++ )
         {
             for( ib=0;ib<ne[iek];ib++ )
            {
                ip= iep[iek][jp][ib]; 
                if(itmp[ip]!=-1)
               {
                  itmp1[itmp[ip]] = ip;
                  itmp2[ip] = itmp[ip];
               }
            }
         }


          fle << "ZONE" << " " << "N=" << nbp << " " << "E=" << ne[iek] << " " << "F=FEPOINT" << " " << "ET=QUADRILATERAL\n";

       //coordinates
          for(ip=0; ip<np; ip++)
         {
//            ip0 = itmp1[ip];
              if( itmp[ip] != -1 )
             {
/*               for( ix=0;ix<nx;ix++ )
                {
                    fle << xp[ix][ip] << " ";
                }*/
                 fle << xp(0,ip) << " ";
                 fle << 0. << " ";
                 fle << xp(1,ip) << " ";
                 for( iv=0;iv<nv;iv++ )
                {
                    fle << qp[iv][ip] << " ";
                }
                 fle << "\n";
             }
         }

          fle << "\n";
          for(ib=0; ib<ne[iek]; ib++)
         {
             if(nep[iek]==3) 
            {
                ip0 = iep[iek][0][ib];
                ip1 = iep[iek][1][ib];
                ip2 = iep[iek][2][ib];
                ip3 = iep[iek][2][ib];

                ip0 = itmp[ip0];      
                ip1 = itmp[ip1];      
                ip2 = itmp[ip2];  
                ip3 = itmp[ip3];  

                ip0++; 
                ip1++; 
                ip2++; 
                ip3++; 

                fle << ip0 << " " << ip1 << " " << ip2 << " " << ip3 << "\n";
            }
             else if(nep[iek]==4) 
            {
                ip0 = iep[iek][0][ib];
                ip1 = iep[iek][1][ib];
                ip2 = iep[iek][2][ib];
                ip3 = iep[iek][3][ib];

                ip0 = itmp[ip0];      
                ip1 = itmp[ip1];      
                ip2 = itmp[ip2];      
                ip3 = itmp[ip3];      

                ip0++; 
                ip1++; 
                ip2++; 
                ip3++; 

                fle << ip0 << " " << ip1 << " " << ip2 << " " << ip3 << "\n";
            }
         }
       }
     }

   }

     fle.close();
    

     for(iv=0; iv<nv; iv++)
    {
       delete[] qp[iv]; qp[iv]=NULL;
    }
     delete[] wp[0]; wp[0]=NULL;

    delete[] itmp; itmp=NULL;
    delete[] itmp1; itmp1=NULL;
    delete[] itmp2; itmp2=NULL;
  }
