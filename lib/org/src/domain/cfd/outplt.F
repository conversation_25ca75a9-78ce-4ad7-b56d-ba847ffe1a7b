# include <fprec.h>
c
c********************************
c output plt file
c********************************
c
      subroutine outplt(lfle, cfle,iel4,iel5,iel6,iel8,ifac3,ifac4,
     &                  coord,nele4,
     &                  nele5,nele6,nele8,npoin,nbou3,nbou4,nstag,
     &                  istag,nblade,mblade)
c jm35 needs real and integer, not Real and Int
      real    coord(3,*)
c
c 

      integer   iel4(4,*)   
      integer   iel5(5,*)   
      integer   iel6(6,*)  
      integer   iel8(8,*)
      integer  ifac3(6,*)  
      integer  ifac4(7,*)
c
      integer    istag(*)
      integer   nblade(*)
      integer   mblade(*)
c
      integer             lfle
      character(len=lfle) cfle
      integer   in
      character*500       fnm

c      fnm= trim(cfle)//'.plt'
      in = 20
      fnm= trim(cfle)
      open(in,
     &     file  =trim(fnm),
     &     form  ='unformatted',
     &     iostat = ioerror)

      write(*,'(/,5x,  a)')'New mesh contains ======================'
      write(*,'(5x,a,i10)')'Nodes                        :', npoin
      write(*,'(5x,a,i10)')'Tetrahedra                   :', nele4
      write(*,'(5x,a,i10)')'Pentahedra                   :', nele5
      write(*,'(5x,a,i10)')'Prisms                       :', nele6
      write(*,'(5x,a,i10)')'Hexahedra                    :', nele8
      write(*,'(5x,a,i10)')'Tri. faces                   :', nbou3
      write(*,'(5x,a,i10)')'Quad. faces                  :', nbou4
      write(*,'(5x,a,i10)')'Nstag                        :', nstag
      do i = 1,nstag
      write(*,'(5x,i4,a,2i5)')i,'  Nblade & Mblade        :',
     &         nblade(i),mblade(i) 
      enddo
      write(*,'(5x,    a)')'========================================'

      write(in,iostat=ioerror)nele4,nele5,nele6,nele8,npoin,
     &                        nbou3,nbou4
      if(ioerror/=0)goto 10
      if(nele4.gt.0) write(in,iostat=ioerror) 
     &               ((iel4(j,i),  i=1,nele4),j=1,4)
      if(ioerror/=0)goto 10
      if(nele5.gt.0) write(in,iostat=ioerror) 
     &               ((iel5(j,i),  i=1,nele5),j=1,5)
      if(ioerror/=0)goto 10
      if(nele6.gt.0) write(in,iostat=ioerror) 
     &               ((iel6(j,i),  i=1,nele6),j=1,6)
      if(ioerror/=0)goto 10
      if(nele8.gt.0) write(in,iostat=ioerror) 
     &               ((iel8(j,i),  i=1,nele8),j=1,8)
      if(ioerror/=0)goto 10
      write(in,iostat=ioerror) ((coord(j,i), i=1,npoin),j=1,3)
      if(ioerror/=0)goto 10
      if(nbou3.gt.0) write(in,iostat=ioerror) 
     &               ((ifac3(j,i), i=1,nbou3),j=1,6)
      if(ioerror/=0)goto 10
      if(nbou4.gt.0) write(in,iostat=ioerror) 
     &               ((ifac4(j,i), i=1,nbou4),j=1,7)
      if(ioerror/=0)goto 10
c
      write(in,iostat=ioerror) nstag
      if(ioerror/=0)goto 10
      write(in,iostat=ioerror)(istag(i), i=1,nstag)
      if(ioerror/=0)goto 10
      write(in,iostat=ioerror)(nblade(i),i=1,nstag)
      if(ioerror/=0)goto 10
      write(in,iostat=ioerror)(mblade(i),i=1,nstag)
      if(ioerror/=0)goto 10
c      if (nsparse.gt.0) then
c        write(in,iostat=ioerror)(msecsprs(i),i=1,nsparse)
c        if(ioerror/=0)goto 10
c      endif


c
c     write(*,'(/,a)') ' ... plt file  written'
      close(in)
      return
c
 10   write(*,'(a)')' !! ERROR : in writing plt file'
      iret = 999
c
      return
      end
