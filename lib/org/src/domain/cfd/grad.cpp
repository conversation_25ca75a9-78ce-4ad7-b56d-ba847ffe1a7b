   using namespace std;

#  include <domain/cfd/domain.h>

   void limiter( Int nv, Real *dx, Real *dy, Real *dval )
  {
     Int iv;
     Real lim, dlim;
     Real eps = small;

     for(iv=0; iv<nv; iv++)
    {
       if((dx[iv]*dy[iv])<0)
      {
         dval[iv] = 0;
      }
       else
      {
         lim=( dx[iv]*(dy[iv]*dy[iv]+eps)+ dy[iv]*(dx[iv]*dx[iv]+eps))/( dy[iv]*dy[iv]+ dx[iv]*dx[iv]+ 2*eps );
         //lim= fmax(0, fmin(1, (dy[iv]+eps)/(dx[iv]+eps))) * dx[iv];

         dval[iv] = lim;
      }
    }
  }

   Real valbada( Real r1, Real r2 )
  {
      Real val;
      val= r1*r2;
      Real eps= small;
   
      if( val <= 0 )
     {
         val= 0;
     }
      else
     {
         val= ( r2*(r1*r1+eps)+r1*(r2*r2+ eps) )/( r1*r1+ r2*r2+ 2*eps );
     }
      return(val);
  }

   inline void valbada( Int nv, Real *r1, Real *r2, Real *eps, Real *val )
  {
      Int iv;
      Real r;
      for( iv=0;iv<nv;iv++ )
     {
         r= r1[iv]*r2[iv];
         if( r <= 0 )
        {
            val[iv]= 0;
        }
         else
        {
            val[iv]= ( r2[iv]*(r1[iv]*r1[iv]+eps[iv])+r1[iv]*(r2[iv]*r2[iv]+ eps[iv]) )/
                     ( r1[iv]*r1[iv]+ r2[iv]*r2[iv]+ 2*eps[iv] );
        }
     }
  }

//   void cFdDomain::grad( Real *v[], Real **dv[] )
   void cFdDomain::grad()
  {
      Int iqs,iqe, ibs,ibe, ics,ice;
      Int ix,jx,ig,ic,iv,iq,iql,iqr,ib,id;
      Int i;
      Real eps=1.e-9;
     // Real eps=1.e-2;
      Real d;
      Real w;

      dof->exchange( sq );
      dof->exchange( sxq );

//     #pragma acc enter data copyin ( iprq0[0:nprq],iprq1[0:nprq])
//     #pragma acc enter data copyin ( sxqprd[0:nx*nprq],sqprd[0:nv*nprq],sdxdxprd[0:nx*nx*nprq],sdqdxprd[0:nx*nv*nprq])
//     #pragma acc enter data copyin ( sxq[0:nx*nq], sq[0:nv*nq], sdxdx[0:nx*nx*nq], sdqdx[0:nx*nv*nq])
//     #pragma acc enter data copyin ( sifq[0:2*nfc])
//     #pragma acc enter data copyin ( this)
//      start_acc_device();
     // setv( 0,nq, nx*nx,0.,dxdx ); 
     // setv( 0,prd->size(),nx*nx,0.,dxdxprd );
     // for( ix=0;ix<nx;ix++ )
     //{
     //    setv( 0,nq, 1,eps,dxdx+ijdx[ix][ix] );
     //    setv( 0,prd->size(),1,eps,dxdxprd+ijdx[ix][ix] );
     //}
     // for( iv=0;iv<nv;iv++ )
     //{
     //    setv( 0,nq, nx,0.,dv[iv] ); 
     //    setv( 0,prd->size(), nx,0.,dqdxprd[iv] ); 
     //}
      setv_3d( 0,nq, nx, nx,ZERO,dxdx, "d" ); 
      setv_3d( 0,prd->size(),nx, nx,ZERO,dxdxprd, "d" );
     #pragma acc parallel loop \
      present(sdxdx[0:nx*nx*nq],this) \
      default(none)
      for(iq=0;iq<nq;iq++)
     {
         sdxdx[ADDR(0,0,iq,nq)] = eps;
         sdxdx[ADDR(1,1,iq,nq)] = eps;
         sdxdx[ADDR(2,2,iq,nq)] = eps;
     }
     #pragma acc parallel loop \
      present(sdxdxprd[0:nx*nx*nprq],this) \
      default(none)
      for(iq=0;iq<nprq;iq++)
     {
         sdxdxprd[ADDR(0,0,iq,nprq)] = eps;
         sdxdxprd[ADDR(1,1,iq,nprq)] = eps;
         sdxdxprd[ADDR(2,2,iq,nprq)] = eps;
     }
      setv_3d( 0,nq, nv, nx,ZERO,dqdx, "d" ); 
      setv_3d( 0,prd->size(), nv, nx,ZERO,dqdxprd, "d" ); 


/*    for( ig=0;ig<ng;ig++ )
     {
         bdf[ig]->exchange( sqb[ig] );
         bdf[ig]->exchange( sxqb[ig] );
     }*/

      while( dev->transit( ) )
     {

         prd->range( dev->avail(), &ics,&ice );
         //coo->coffset( ics,ice, 1., iprq[0], xq, NULL, xqprd );
         coo->coffset( ics,ice, 1., iprq0, xq, NULL_iview, xqprd );
         //fld->voffset( ics,ice, 1., iprq[0], v, NULL, qprd );
         fld->voffset( ics,ice, 1., iprq0, q, NULL_iview, qprd );
         //grad0( ics,ice,  NULL, xqprd,qprd, dxdxprd,dqdxprd, iprq[1], xq,v,dxdx,dv );
         grad0( ics,ice,  NULL_iview, xqprd,qprd, dxdxprd,dqdxprd, iprq1, xq,q,dxdx,dqdx );
         //fld->goffset( ics,ice,  -1., ijdx, NULL, dxdxprd, dqdxprd, NULL, dxdxprd, dqdxprd );
         fld->goffset( ics,ice, -1., dxdxprd, dqdxprd );
        #pragma acc parallel loop gang vector\
         present(siprq0[0:nprq],sdxdx[0:nx*nx*nq],sdqdx[0:nx*nv*nq],sdxdxprd[0:nx*nx*nprq],sdqdxprd[0:nx*nv*nprq],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            for( ix=0;ix<nx;ix++ )
           {
               for( jx=0;jx<nx;jx++ )
              {
                  iq= siprq0[ic];
                  //dxdx[ijdx[ix][jx]][iq]+= dxdxprd[ijdx[ix][jx]][ic];
                  sdxdx[ADDR(ix,jx,iq,nq)]+= sdxdxprd[ADDR(ix,jx,ic,nprq)];
              }
               for( iv=0;iv<nv;iv++ )
              {
                  iq= siprq0[ic];
                  //dv[iv][ix][iq]+= dqdxprd[iv][ix][ic];
                  sdqdx[ADDR(iv,ix,iq,nq)]+= sdqdxprd[ADDR(iv,ix,ic,nprq)];
              }
           }
        }

         cnf->range( dev->avail(), &ics,&ice );
         //grad0( ics, ice, ifq[0], xq,v,dxdx,dv, ifq[1], xq,v,dxdx,dv );
         grad0( ics,ice,ifq,xq,q,dxdx,dqdx );

     }
//     #pragma acc exit data copyout ( iprq0[0:nprq],iprq1[0:nprq])
//     #pragma acc exit data copyout ( sxqprd[0:nx*nprq],sqprd[0:nv*nprq],sdxdxprd[0:nx*nx*nprq],sdqdxprd[0:nx*nv*nprq])
//     #pragma acc exit data copyout ( sxq[0:nx*nq], sq[0:nv*nq], sdxdx[0:nx*nx*nq], sdqdx[0:nx*nv*nq])
//     #pragma acc exit data copyout ( sifq[0:2*nfc])
//     #pragma acc exit data copyout ( this)
//      exit_acc_device();

  }

   //void cFdDomain::gradb( Real *v[], Real **dv[] )
   void cFdDomain::gradb()
  {
      Int iqs,iqe, ibs,ibe, ics,ice;
      Int ix,jx,ig,ic,iv,iq,iql,iqr,ib,id;
      Int i;
      Real eps=1.e-9;
      Real d;
      Real w;

      for( ig=0;ig<ng;ig++ )
     {
         bdf[ig]->exchange( sqb[ig] );
         bdf[ig]->exchange( sxqb[ig] );
     }

      dof->exchange( sdxdx );
      for( iv=0;iv<nv;iv++ )
     {
         dof->exchange( sdqdx+iv*nx*nq );
     }

//      #pragma acc enter data copyin( sxq[0:nx*nq],sq[0:nv*nq],sdxdx[0:nx*nx*nq],sdqdx[0:nx*nv*nq] )
//                                   
//      for( ig=0;ig<ng;ig++ )
//     {
//         #pragma acc enter data copyin(sxqb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],siqb[ig][0:nbb[ig]], \
//                                       swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]])
//     }

      while( dev->transit() )
     { 
         for( ig=0;ig<ng;ig++ ) 
        {
            bdf[ig]->range( dev->avail(), &ibs,&ibe );
            //bbj[ig]->grad( ibs, ibe, xqb[ig], qb[ig], iqb[ig], xq, v, ijdx, dxdx, dv, wnb[ig], wxdb[ig] );
            bbj[ig]->grad( ibs, ibe, sxqb[ig], sqb[ig], siqb[ig], sxq, sq, sdxdx, sdqdx, swnb[ig], swxdb[ig], nq, nbb[ig] );
        }
     }

//      #pragma acc exit data copyout( sxq[0:nx*nq],sq[0:nv*nq],sdxdx[0:nx*nx*nq],sdqdx[0:nx*nv*nq] )
//                                   
//      for( ig=0;ig<ng;ig++ )
//     {
//         #pragma acc exit data copyout(sxqb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],siqb[ig][0:nbb[ig]], \
//                                       swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]])
//     }

  }

   void cFdDomain::grad0( Int ics, Int ice, Int *icql, Real *xql[], Real *ql[], Real *dxdxl[], Real **dqdxl[],
                                            Int *icqr, Real *xqr[], Real *qr[], Real *dxdxr[], Real **dqdxr[] )
  {
      Real      *wrk[1];
      Int        ix,jx,iv,ic,iql,iqr;
      Real       d,w;

      if( ice > ics )
     {

         wrk[0]= new Real[ice];
         setv( ics,ice, 1,ZERO, wrk );

         for( ix=0;ix<nx;ix++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iql= ic;
               iqr= ic;
               if( icql ){ iql= icql[ic]; }
               if( icqr ){ iqr= icqr[ic]; }
               d= xqr[ix][iqr]- xql[ix][iql];
               d*= d;
               wrk[0][ic]+= d;
           } 
        }
      
         for( ic=ics;ic<ice;ic++ )
        {
            wrk[0][ic]= 1./wrk[0][ic];
        }
      
         for( ix=0;ix<nx;ix++ )
        {
            for( iv=0;iv<nv;iv++ )
           {
               for( ic=ics;ic<ice;ic++ )
              {
                  iql= ic;
                  iqr= ic;
                  if( icql ){ iql= icql[ic]; }
                  if( icqr ){ iqr= icqr[ic]; }
                  w= wrk[0][ic];
                  d= w*(xqr[ix][iqr]-xql[ix][iql])*( qr[iv][iqr]-ql[iv][iql] );
                  dqdxl[iv][ix][iql]+= d;
                  dqdxr[iv][ix][iqr]+= d;
              }
           }
            for( jx=0;jx<nx;jx++ )
           {
               for( ic=ics;ic<ice;ic++ )
              {
                  iql= ic;
                  iqr= ic;
                  if( icql ){ iql= icql[ic]; }
                  if( icqr ){ iqr= icqr[ic]; }
                  w= wrk[0][ic];
                  d= w*(xqr[ix][iqr]-xql[ix][iql])*( xqr[jx][iqr]-xql[jx][iql] );
                  dxdxl[ijdx[ix][jx]][iql]+= d;
                  dxdxr[ijdx[ix][jx]][iqr]+= d;
              }
           }
        }
         delete[] wrk[0];
     }
  }

//   void cFdDomain::grad0( Int ics, Int ice, Int *icql, Real *xql, Real *ql, Real *dxdxl, Real *dqdxl,
//                                            Int *icqr, Real *xqr, Real *qr, Real *dxdxr, Real *dqdxr,
//                                            Int nql, Int nqr )
   void cFdDomain::grad0( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xql_view, cAu3xView<Real>& ql_view, cAu3xView<Real>& dxdxl_view, cAu3xView<Real>& dqdxl_view,
                                            cAu3xView<Int>& icqr_view, cAu3xView<Real>& xqr_view, cAu3xView<Real>& qr_view, cAu3xView<Real>& dxdxr_view, cAu3xView<Real>& dqdxr_view )
  {
      Real       wrk;
      Int        ix,jx,iv,ic,iql,iqr;
      Real       d,w;

      Int nql, nqr;
      Int *icql;
      Real *xql, *ql, *dxdxl, *dqdxl;
      Int *icqr;
      Real *xqr, *qr, *dxdxr, *dqdxr;

      nql = ql_view.get_dim1();
      nqr = qr_view.get_dim1();

      icql   = icql_view.get_data();
      xql   = xql_view.get_data();
      ql    = ql_view.get_data();
      dxdxl = dxdxl_view.get_data();
      dqdxl = dqdxl_view.get_data();;
      icqr   = icqr_view.get_data();
      xqr   = xqr_view.get_data();
      qr    = qr_view.get_data();
      dxdxr = dxdxr_view.get_data();
      dqdxr = dqdxr_view.get_data();;

      if( ice > ics )
     {
        //nql:nprq, nqr:nq
        #pragma acc parallel loop gang vector\
         present(            xql[0:nx*nql],ql[0:nv*nql],dxdxl[0:nx*nx*nql],dqdxl[0:nx*nv*nql], \
                 icqr[0:nql],xqr[0:nx*nqr],qr[0:nv*nqr],dxdxr[0:nx*nx*nqr],dqdxr[0:nx*nv*nqr],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            wrk = 0;
            for( ix=0;ix<nx;ix++ )
           {
               iql= ic;
               iqr= icqr[ic]; 
               //d= xqr[ix][iqr]- xql[ix][iql];
               d= xqr[ADDR(ix,iqr,nqr)]- xql[ADDR(ix,iql,nql)];
               d*= d;
               wrk+= d;
           } 
      
            wrk = 1./wrk;
      
            for( ix=0;ix<nx;ix++ )
           {
               for( iv=0;iv<nv;iv++ )
              {
                  iql= ic;
                  iqr= icqr[ic];;
                  w= wrk;
                  //d= w*(xqr[ix][iqr]-xql[ix][iql])*( qr[iv][iqr]-ql[iv][iql] );
                  d= w*(xqr[ADDR(ix,iqr,nqr)]-xql[ADDR(ix,iql,nql)])*( qr[ADDR(iv,iqr,nqr)]-ql[ADDR(iv,iql,nql)] );
                  //dqdxl[iv][ix][iql]+= d;
                  //dqdxr[iv][ix][iqr]+= d;
                  dqdxl[ADDR(iv,ix,iql,nql)]+= d;
                  dqdxr[ADDR(iv,ix,iqr,nqr)]+= d;
              }
               for( jx=0;jx<nx;jx++ )
              {
                  iql= ic;
                  iqr= icqr[ic]; 
                  w= wrk;
                  //d= w*(xqr[ix][iqr]-xql[ix][iql])*( xqr[jx][iqr]-xql[jx][iql] );
                  d= w*(xqr[ADDR(ix,iqr,nqr)]-xql[ADDR(ix,iql,nql)])*( xqr[ADDR(jx,iqr,nqr)]-xql[ADDR(jx,iql,nql)] );
                  //dxdxl[ijdx[ix][jx]][iql]+= d;
                  //dxdxr[ijdx[ix][jx]][iqr]+= d;
                  dxdxl[ADDR(ix,jx,iql,nql)]+= d;
                  dxdxr[ADDR(ix,jx,iqr,nqr)]+= d;
              }
           }
        }
     }
  }

//   void cFdDomain::grad0( Int ics, Int ice, Int *icq, Real *xq0, Real *q0, Real *dxdx0, Real *dqdx0 )
   void cFdDomain::grad0( Int ics, Int ice, cAu3xView<Int>& icq_view, cAu3xView<Real>& xq0_view, cAu3xView<Real>& q0_view, cAu3xView<Real>& dxdx0_view, cAu3xView<Real>& dqdx0_view )
  {
      Real       wrk;
      Int        ix,jx,iv,ic,iql,iqr;
      Real       d,w;

      Int nfc, nq;
      Int *icq;
      Real *xq0, *q0, *dxdx0, *dqdx0;

      nfc = icq_view.get_dim1();
      nq  = q0_view.get_dim1();

      icq   = icq_view.get_data();
      xq0   = xq0_view.get_data();
      q0    = q0_view.get_data();
      dxdx0 = dxdx0_view.get_data();
      dqdx0 = dqdx0_view.get_data();

      if( ice > ics )
     {
        #pragma acc parallel loop gang vector\
         present(icq[0:2*nfc],xq0[0:nx*nq],q0[0:nv*nq],dxdx0[0:nx*nx*nq],dqdx0[0:nx*nv*nq],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            wrk = 0;
            for( ix=0;ix<nx;ix++ )
           {
               iql= icq[ADDR(0,ic,nfc)]; 
               iqr= icq[ADDR(1,ic,nfc)]; 
               //d= xqr[ix][iqr]- xql[ix][iql];
               d= xq0[ADDR(ix,iqr,nq)]- xq0[ADDR(ix,iql,nq)];
               d*= d;
               wrk+= d;
           } 

            wrk= 1./wrk;
      
            for( ix=0;ix<nx;ix++ )
           {
               for( iv=0;iv<nv;iv++ )
              {
                  iql= icq[ADDR(0,ic,nfc)];
                  iqr= icq[ADDR(1,ic,nfc)];
                  w= wrk;
                  //d= w*(xqr[ix][iqr]-xql[ix][iql])*( qr[iv][iqr]-ql[iv][iql] );
                  d= w*(xq0[ADDR(ix,iqr,nq)]-xq0[ADDR(ix,iql,nq)])*( q0[ADDR(iv,iqr,nq)]-q0[ADDR(iv,iql,nq)] );
                  //dqdxl[iv][ix][iql]+= d;
                  //dqdxr[iv][ix][iqr]+= d;
                  #pragma acc atomic
                  dqdx0[ADDRG(iv,ix,iql,nq)]+= d;
                  #pragma acc atomic
                  dqdx0[ADDRG(iv,ix,iqr,nq)]+= d;
              }
               for( jx=0;jx<nx;jx++ )
              {
                  iql= icq[ADDR(0,ic,nfc)]; 
                  iqr= icq[ADDR(1,ic,nfc)]; 
                  w= wrk;
                  //d= w*(xqr[ix][iqr]-xql[ix][iql])*( xqr[jx][iqr]-xql[jx][iql] );
                  d= w*(xq0[ADDR(ix,iqr,nq)]-xq0[ADDR(ix,iql,nq)])*( xq0[ADDR(jx,iqr,nq)]-xq0[ADDR(jx,iql,nq)] );
                  //dxdxl[ijdx[ix][jx]][iql]+= d;
                  //dxdxr[ijdx[ix][jx]][iqr]+= d;
                  #pragma acc atomic
                  dxdx0[ADDRG(ix,jx,iql,nq)]+= d;
                  #pragma acc atomic
                  dxdx0[ADDRG(ix,jx,iqr,nq)]+= d;
              }
           }
        }
     }
  }

   //void cFdDomain::grads( Real **dv[] )
   void cFdDomain::grads()
  {
      Int id,iqs,iqe,iv; 
      for( id=0;id<dev->getncpu()+1;id++ )
     {
         dof->range( id, &iqs,&iqe );
         //getrf( iqs,iqe, nx, ijdx,dxdx );
         //#pragma acc enter data copyin(sdxdx[0:nx*nx*nq], sdqdx[0:nv*nx*nq])
//         start_acc_device();
         getrf( iqs,iqe, nx, sdxdx,nq );
         getrs( iqs,iqe, nx, sdxdx, sdqdx, nv,nq );
         //#pragma acc exit data copyout(sdxdx[0:nx*nx*nq], sdqdx[0:nv*nx*nq])
         //#pragma acc exit data delete(sdxdx[0:nx*nx*nq], sdqdx[0:nv*nx*nq])
//         exit_acc_device();

        // for( iv=0;iv<nv;iv++ )
        //{
        //    getrs( iqs,iqe, nx, ijdx,dxdx, dv[iv] );
        //}

     }
  }

   void cFdDomain::deltq( Int iql,Int idl, Real *xql[], Real *ql[], Real *dxdxl[], Real **dqdxl[], 
                        Int iqr,Int idr, Real *xqr[], Real *qr[], Real *dxdxr[], Real **dqdxr[], 
                        Real *xn, Real *wn,  Real *dql0, Real *dqr0, Real *dql, Real *dqr )
  {
      Int ix,jx,iv;
      Real w,dx,dxi,dxj,wl,wr;
      Real dqi[MxNVs];
      Real eps[MxNVs];

      Real dxdxl1[9];
      Real dxdxr1[9];
      Real dqdxl1[MxNVs][3];
      Real dqdxr1[MxNVs][3];

      for( ix=0;ix<nx;ix++ )
     {
         for( jx=0;jx<nx;jx++ )
        {
            dxdxl1[ijdx[ix][jx]]= dxdxl[ijdx[ix][jx]][iql];
        }
         for( iv=0;iv<nv;iv++ )
        {
            dqdxl1[iv][ix]= dqdxl[iv][ix][iql];
        }
     }
      for( ix=0;ix<nx;ix++ )
     {
         for( jx=0;jx<nx;jx++ )
        {
            dxdxr1[ijdx[ix][jx]]= dxdxr[ijdx[ix][jx]][iqr];
        }
         for( iv=0;iv<nv;iv++ )
        {
            dqdxr1[iv][ix]= dqdxr[iv][ix][iqr];
        }
     }
      w= 0;
      Real wi=0;
      for( ix=0;ix<nx;ix++ )
     {
         dx= xqr[ix][iqr]- xql[ix][iql];
         w+= ( dx*dx );
         wi+= dx*wn[ix];
   
     } 
      w= 1./w;

      for( iv=0;iv<nv;iv++ )
     {
         dqi[iv]= ( qr[iv][iqr]- ql[iv][iql] );
     }

      for( ix=0;ix<nx;ix++ )
     {
         dxi= xqr[ix][iqr]- xql[ix][iql];
         for( jx=0;jx<nx;jx++ )
        {
            dxj= xqr[jx][iqr]- xql[jx][iql];
            dxdxl1[ijdx[ix][jx]]-= w*dxi*dxj;
            dxdxr1[ijdx[ix][jx]]-= w*dxi*dxj;
        }
         for( iv=0;iv<nv;iv++ )
        {
            dqdxl1[iv][ix]-= w*dxi*dqi[iv];
            dqdxr1[iv][ix]-= w*dxi*dqi[iv];
        }
     }


// exact
  //    getrf( nx,ijdx, dxdxl1 );
  //    getrf( nx,ijdx, dxdxr1 );
  //    for( iv=0;iv<nv;iv++ )
  //   {
  //       getrs( nx,ijdx, dxdxl1, dqdxl1[iv] );
  //       getrs( nx,ijdx, dxdxr1, dqdxr1[iv] );
  //   }
  //    for( iv=0;iv<nv;iv++ )
  //   {
  //       dql[iv]=0;
  //       dqr[iv]=0;
  //       for( ix=0;ix<nx;ix++ )
  //      {
  //          dql[iv]+= dqdxl1[iv][ix]* wn[ix];
  //          dqr[iv]+= dqdxr1[iv][ix]* wn[ix];
  //      }
  //       dqi[iv]/= wi;
  //       dql[iv]= valbada( dql[iv],dqi[iv] );
  //       dqr[iv]= valbada( dqr[iv],dqi[iv] );
  //   }

// mininum norm
      wl=0;
      wr=0;
      for( ix=0;ix<nx;ix++ )
     {
         for( jx=0;jx<nx;jx++ )
        {
            wl+= dxdxl1[ijdx[ix][jx]]*wn[ix]*wn[jx];
            wr+= dxdxr1[ijdx[ix][jx]]*wn[ix]*wn[jx];
        }
     }
      for( iv=0;iv<nv;iv++ )
     {
         dql[iv]=0;
         dqr[iv]=0;
         for( ix=0;ix<nx;ix++ )
        {
            dql[iv]+= dqdxl1[iv][ix]*wn[ix];       
            dqr[iv]+= dqdxr1[iv][ix]*wn[ix];       
        }
         dql[iv]/= wl;
         dqr[iv]/= wr;
         dqi[iv]/= wi;
         eps[iv]= small;
     }

      valbada( nv,dql,dqi,eps,dql ); // failed
      valbada( nv,dqr,dqi,eps,dqr );

      wl= 0;
      wr= 0;
      for( ix=0;ix<nx;ix++ )
     {
         wl+= wn[ix]* ( xn[ix]- xql[ix][iql] );
         wr+= wn[ix]* ( xqr[ix][iqr] -xn[ix] );
     }
      for( iv=0;iv<nv;iv++ )
     {
         dql[iv]*=( wl);
         dqr[iv]*=(-wr);
     }
      if( gdebug )
     {
         cout << "\n";
         cout << "\n";
         cout << "LEFT "<<iql<<"\n";
         cout << wl<<"\n";
         for( iv=0;iv<nv;iv++ )
        {
             cout << ql[iv][iql]<<" "<<dql[iv]<<" "<<dql0[iv]<<"\n";
        }
         cout << "RIGHT "<<iqr<<"\n";
         cout << wr<<"\n";
         for( iv=0;iv<nv;iv++ )
        {
             cout << qr[iv][iqr]<<" "<<dqr[iv]<<" "<<dqr0[iv]<<"\n";
        }
     }
  }

   void cFdDomain::deltq_exact( Int iql,Int idl, Real *xql[], Real *ql[], Real *dxdxl[], Real **dqdxl[], 
                                Int iqr,Int idr, Real *xqr[], Real *qr[], Real *dxdxr[], Real **dqdxr[], 
                                Real *xn, Real *wn,  Real *dql0, Real *dqr0, Real *dql, Real *dqr )
  {
      Int ix,jx,iv;
      Real w,dx,dxi,dxj,wl,wr;
      Real dqi[MxNVs];
      Real eps[MxNVs];

      Real dxdxl1[9];
      Real dxdxr1[9];
      Real dqdxl1[MxNVs][3];
      Real dqdxr1[MxNVs][3];

      for( ix=0;ix<nx;ix++ )
     {
         for( jx=0;jx<nx;jx++ )
        {
            dxdxl1[ijdx[ix][jx]]= dxdxl[ijdx[ix][jx]][iql];
        }
         for( iv=0;iv<nv;iv++ )
        {
            dqdxl1[iv][ix]= dqdxl[iv][ix][iql];
        }
     }
      for( ix=0;ix<nx;ix++ )
     {
         for( jx=0;jx<nx;jx++ )
        {
            dxdxr1[ijdx[ix][jx]]= dxdxr[ijdx[ix][jx]][iqr];
        }
         for( iv=0;iv<nv;iv++ )
        {
            dqdxr1[iv][ix]= dqdxr[iv][ix][iqr];
        }
     }
      w= 0;
      Real wi=0;
      for( ix=0;ix<nx;ix++ )
     {
         dx= xqr[ix][iqr]- xql[ix][iql];
         w+= ( dx*dx );
         wi+= dx*wn[ix];
      
     } 
      w= 1./w;

      for( iv=0;iv<nv;iv++ )
     {
         dqi[iv]= ( qr[iv][iqr]- ql[iv][iql] );
     }

      //remove downstream contribution
      for( ix=0;ix<nx;ix++ )
     {
         dxi= xqr[ix][iqr]- xql[ix][iql];
         for( jx=0;jx<nx;jx++ )
        {
            dxj= xqr[jx][iqr]- xql[jx][iql];
            dxdxl1[ijdx[ix][jx]]-= w*dxi*dxj;
            dxdxr1[ijdx[ix][jx]]-= w*dxi*dxj;
        }
         for( iv=0;iv<nv;iv++ )
        {
            dqdxl1[iv][ix]-= w*dxi*dqi[iv];
            dqdxr1[iv][ix]-= w*dxi*dqi[iv];
        }
     }

      
      getrf( nx, ijdx, dxdxl1 );
      getrf( nx, ijdx, dxdxr1 );
      for(iv=0; iv<nv; iv++)
     {
        getrs( nx, ijdx,  dxdxl1, dqdxl1[iv] );
        getrs( nx, ijdx,  dxdxr1, dqdxr1[iv] );
     }

      Real tmpdql[10], tmpdqr[10], limql[10], limqr[10], di[3], dl[3], dr[3];

      /*wl=0;
      wr=0;
      for( ix=0;ix<nx;ix++ )
     {
         dl[ix] = (xn[ix] - xql[ix][iql])*wn[ix];
         dr[ix] = (xn[ix] - xqr[ix][iqr])*wn[ix];
         wl += dl[ix]*dl[ix];
         wr += dr[ix]*dr[ix];
     }
      wl = sqrt(wl);
      wr = sqrt(wr);
      wl= wl/( wl+wr );
      wr= 1.-wl;


      for(iv=0; iv<nv; iv++)
     {
         tmpdql[iv] = 0;
         tmpdqr[iv] = 0;
         for(ix=0; ix<nx; ix++)
        {
           di[ix] = (xqr[ix][iqr] - xql[ix][iql])*wn[ix];   
        }
         for(ix=0; ix<nx; ix++)
        {
           tmpdql[iv] += dqdxl1[iv][ix]*di[ix];
           tmpdqr[iv] += dqdxr1[iv][ix]*di[ix];
        }
     }*/
     dl[0]= xn[0] - xql[0][iql];
     dl[1]= xn[1] - xql[1][iql];
     dr[0]= xn[0] - xqr[0][iqr];
     dr[1]= xn[1] - xqr[1][iqr];
     di[0]= xqr[0][iqr] - xql[0][iql];
     di[1]= xqr[1][iqr] - xql[1][iql];
     wl = sqrt(dl[0]*dl[0] + dl[1]*dl[1]);
     wr = sqrt(dr[0]*dr[0] + dr[1]*dr[1]);
     wl= wl/( wl+wr );
     wr= 1.-wl;

     for(iv=0; iv<nv; iv++)
    {
       tmpdql[iv] = dqdxl1[iv][0]*di[0] + dqdxl1[iv][1]*di[1];
       tmpdqr[iv] = dqdxr1[iv][0]*di[0] + dqdxr1[iv][1]*di[1];
    }


 
      limiter( nv, dqi,    tmpdql,    limql );
      limiter( nv, dqi,    tmpdqr,    limqr );
      for(iv=0; iv<nv; iv++)
     {
        dql[iv] = limql[iv]*wl;
        dqr[iv] =-limqr[iv]*wr;

//        cout << dqi[iv] << " " << tmpdql[iv] << " " << tmpdqr[iv] << " " << limql[iv] << " " << limqr[iv] << "\n";
     }
  }

