   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::probesol()
  {
      ifstream fle;
      Int icpu, tmpnp, ip, idum, ic[100], ilq[100], iq, ix, iv;
      string fnme;

      fnme= dev->getcpath();
      icpu= dev->getrank();
      fnme= fnme+"/probe" + "." + strc(icpu) + ".part";
        
      fle.open(fnme.c_str());

      if(!fle.good()) return;

      fle >> tmpnp; 
      for(ip=0; ip<tmpnp; ip++)
     {
         fle >> idum >> ic[ip] >> ilq[ip];
     }

      ofstream ofle;
      for(ip=0; ip<tmpnp; ip++)
     {
         if(icpu == ic[ip])
        {
            fnme= dev->getcpath();
            fnme = fnme + "/probe." + strc(icpu) + "." + strc(ip) + ".q";
            ofle.open(fnme.c_str(), std::ofstream::app);

            iq = ilq[ip];
            if(iq<0)
           {
               ofle << "Error: can not probe for this point\n";
           }
            else
           {
              ofle << tm/dtm << " ";
              for(ix=0; ix<nx; ix++)
             {
                ofle << xq(ix,iq) << " ";
             }
              for(iv=0; iv<nv; iv++)
             {
                ofle << q(iv,iq) << " " ;
             }
              ofle << "\n";
           }
            ofle.close();
        }
     }
  }

