using namespace std;

#include <iomanip>      // std::setprecision
#  include <domain/cfd/domain.h>

   void jacphase(Real ibpa, cJacBlkZ *blkjac_z, Real sign)
  {
      complex<Real> dz, img(0,1);
      Int iv, jv;

      ibpa = sign*pi2*ibpa;
      dz = cos(ibpa) + sin(ibpa)*img;
      for(iv=0; iv<7; iv++)
     {
         for(jv=0; jv<7; jv++)
        {
            blkjac_z->jac[jv][iv] *= dz;
        }
     }
  }

#ifdef PETSC
   PetscErrorCode cFdDomain::petsc_nk_init_lin()
  {
      cout << "initialize petsc for linearized solver\n";
      PetscErrorCode ierr;
      PetscInt blocksize;
      MPI_Comm* A_COMM_WORLD;
      KSPType ksp_type;
      PetscInt gmres_restart, asm_ovlap;
      PCType pc_type;
      PetscInt maxneig=8;

      A_COMM_WORLD = dev->getcomm();

      blocksize = nv;

      //create vectors to store right hand sides and flow variables
      ierr = VecCreate(*A_COMM_WORLD, &petsc_dcsv_z);CHKERRQ(ierr);
      ierr = VecSetSizes(petsc_dcsv_z, nlocal*blocksize, PETSC_DECIDE);CHKERRQ(ierr);
      ierr = VecSetBlockSize(petsc_dcsv_z, blocksize);CHKERRQ(ierr);
      ierr = VecSetType(petsc_dcsv_z, VECMPI);CHKERRQ(ierr);
      ierr = VecSetFromOptions(petsc_dcsv_z);CHKERRQ(ierr);

      //duplicate 
      ierr = VecDuplicate(petsc_dcsv_z, &petsc_rhs_z);CHKERRQ(ierr);

      //create matrix for the left hand side
      //ierr = MatDuplicate(petsc_A_pre, MAT_SHARE_NONZERO_PATTERN, &petsc_A_pre_copy); CHKERRQ(ierr);
      //ierr = MatConvert(petsc_A_pre, MATSAME,  MAT_INITIAL_MATRIX, &petsc_A_pre_copy); CHKERRQ(ierr);
      ierr = MatCreateBAIJ(*A_COMM_WORLD, blocksize, nlocal*blocksize, nlocal*blocksize, PETSC_DETERMINE, PETSC_DETERMINE,
                            maxneig, NULL, maxneig, NULL, &petsc_A_pre_copy); CHKERRQ(ierr);
      ierr = MatSetOption(petsc_A_pre_copy, MAT_STRUCTURALLY_SYMMETRIC, PETSC_TRUE); CHKERRQ(ierr);

      //create linear solver
      ierr = KSPCreate(*A_COMM_WORLD, &petsc_ksp_z); CHKERRQ(ierr);

      // set operators for ksp
      ierr = KSPSetOperators(petsc_ksp_z, petsc_A_pre, petsc_A_pre); CHKERRQ(ierr);

      ksp_type = "gmres";
      gmres_restart = 80;
      asm_ovlap = 1;
      pc_type = "asm";

      ierr = KSPSetType(petsc_ksp_z, ksp_type); CHKERRQ(ierr);
      ierr = KSPGMRESSetRestart(petsc_ksp_z, gmres_restart); CHKERRQ(ierr);
      ierr = KSPGMRESSetCGSRefinementType(petsc_ksp_z, KSP_GMRES_CGS_REFINE_NEVER); CHKERRQ(ierr);
      ierr = KSPGetPC(petsc_ksp_z,&petsc_pc);CHKERRQ(ierr); CHKERRQ(ierr);
      ierr = PCSetType(petsc_pc,pc_type); CHKERRQ(ierr);
      ierr = PCASMSetOverlap(petsc_pc,asm_ovlap); CHKERRQ(ierr);
      ierr = KSPSetPCSide(petsc_ksp_z,PC_RIGHT); CHKERRQ(ierr);
      ierr = KSPSetTolerances(petsc_ksp_z,0.1e-1,PETSC_DEFAULT,PETSC_DEFAULT,PETSC_DEFAULT);CHKERRQ(ierr);

      ierr = KSPSetFromOptions(petsc_ksp_z); CHKERRQ(ierr);

      cout << "done with initialize petsc for linearized solver\n";
      ierr = 0;

      return ierr;
  }

   PetscErrorCode cFdDomain::petsc_nk_comp_lin()
  {
      Int iv, ifre, iq, iqs, iqe, nnz, jv;
      Real r1_re[10], r1_im[10], r1_re_norm, r1_im_norm, r1_norm;
      PetscErrorCode ierr;
      PetscInt niter_ksp;
      KSPConvergedReason ksp_reason;
      PetscScalar a;
      PetscInt idxm[10], idxn[10];
      PetscScalar values[100];

      //cout << scientific << setprecision(10) << "\n";

      for(ifre=0; ifre<nfre; ifre++)
     {
         //if(nexc<500)
         if(gmres_glb_z[ifre])
        {
            cfl_z[ifre]= max( 5.,cfl_z[ifre]*dcfl );
            cfl_z[ifre]= min( 50.,cfl_z[ifre]*dcfl );
        }
         else
        {
            cfl_z[ifre]= min( 100.,cfl_z[ifre]*dcfl );
        }

         //cout << "solve for " << ifre << "th harmonic\n";
         bcs_z(ifre);
   
         //gradient
         grad_z(  ifre, z_re[ifre], dzdx_re, z_im[ifre], dzdx_im );
         gradb_z( ifre, z_re[ifre], dzdx_re, z_im[ifre], dzdx_im );
   
         gtrhs_z( ifre );
         ierr = petsc_setrhs_z(petsc_rhs_z); CHKERRQ(ierr);

         //for(iv=0; iv<nv; iv++) r1_re[iv]=0;
         //for(iv=0; iv<nv; iv++) r1_im[iv]=0;
         //resd( rhsz_re, r1_re );
         //resd( rhsz_im, r1_im );
         r1_re_norm=0;
         r1_im_norm=0;
         resd2( rhsz_re, &r1_re_norm );
         resd2( rhsz_im, &r1_im_norm );
         r1_norm = r1_re_norm + r1_im_norm;
         r1_norm = sqrt(r1_norm);
         if(nexc==0) init_residual_lin[ifre] = r1_norm;
         if(r1_norm < (init_residual_lin[ifre]*0.01)) gmres_glb_z[ifre] = false;
         if(dev->getrank()==0 && ilev==0)
        {
          // *flg << cfl<<" ";
          // for( iv=0;iv<1;iv++ )
          //{
          //    *flg << r1_re[iv]<<" ";
          //}
          // for( iv=0;iv<1;iv++ )
          //{
          //    *flg << r1_im[iv]<<" ";
          //}
           //*flg << cfl_z[ifre] << " "<< r1_norm/init_residual_lin[ifre] <<" ";
           *flg << cfl_z[ifre] << " "<< r1_norm <<" ";
           //*flg << "\n";
           //flg->flush();
        }

         //copy the LHS of the real system and add spectral term in the diagonal 
         //ierr = MatZeroEntries(petsc_A_pre); CHKERRQ(ierr);
         //ierr = MatAXPY(petsc_A_pre, 1, petsc_A_pre_copy, SAME_NONZERO_PATTERN);
         ////ierr = MatCopy(petsc_A_pre_copy, petsc_A_pre, SAME_NONZERO_PATTERN);
         dof->range( dev->getrank(), &iqs,&iqe );
         //ierr = MatShift(petsc_A_pre, a); CHKERRQ(ierr);
         if(ifre>0)
        {
            //undo the diagonal contribution for the previous harmonic
            a = -1*PETSC_i * freq0[ifre-1]; 
            for(iq=iqs; iq<iqe; iq++)
           {
               nnz=0;
               for(iv=0; iv<nv; iv++)
              {   
                  for(jv=0; jv<nv; jv++)
                 {   
                     if(iv==jv) values[nnz++] =-a*wq[0][iq];
                     else       values[nnz++] = 0;
                 }
              }
               idxm[0] = ig_mat[iq];
               idxn[0] = ig_mat[iq];
               ierr = MatSetValuesBlocked(petsc_A_pre, 1, idxm, 1, idxn, values, ADD_VALUES);
               CHKERRQ(ierr);
           }

            //undo pseudo time stepping contribution of the previous harmonic
            add_pseudo_time_to_petsc_matrix( petsc_A_pre, iqs, iqe, lhsa[0], cfl_z[ifre-1], -1  );

            //undo phase change on the jacobi of periodic boundary by the previous harmonic
            add_phase_peri_jac( ifre-1, -1 );
        } 
         a = -1*PETSC_i * freq0[ifre]; 
         for(iq=iqs; iq<iqe; iq++)
        {
            nnz=0;
            for(iv=0; iv<nv; iv++)
           {   
               for(jv=0; jv<nv; jv++)
              {   
                  if(iv==jv) values[nnz++] = a*wq[0][iq];
                  else       values[nnz++] = 0;
              }
           }
            idxm[0] = ig_mat[iq];
            idxn[0] = ig_mat[iq];
            ierr = MatSetValuesBlocked(petsc_A_pre, 1, idxm, 1, idxn, values, ADD_VALUES);
            CHKERRQ(ierr);
        }
         add_pseudo_time_to_petsc_matrix( petsc_A_pre, iqs, iqe, lhsa[0], cfl_z[ifre], 1  );
         add_phase_peri_jac( ifre, 1 );


         ierr = MatAssemblyBegin(petsc_A_pre,MAT_FINAL_ASSEMBLY);CHKERRQ(ierr);
         ierr = MatAssemblyEnd(petsc_A_pre,MAT_FINAL_ASSEMBLY);CHKERRQ(ierr);
   
         ierr = VecAssemblyBegin(petsc_dcsv_z);CHKERRQ(ierr);
         ierr = VecAssemblyEnd(petsc_dcsv_z);CHKERRQ(ierr);
   
         ierr = VecAssemblyBegin(petsc_rhs_z);CHKERRQ(ierr);
         ierr = VecAssemblyEnd(petsc_rhs_z);CHKERRQ(ierr);
   
//solve
         ierr = KSPSolve(petsc_ksp_z, petsc_rhs_z, petsc_dcsv_z);   
         ierr = KSPGetIterationNumber(petsc_ksp_z, &niter_ksp); CHKERRQ(ierr);
         ierr = KSPGetConvergedReason(petsc_ksp_z, &ksp_reason); CHKERRQ(ierr);

//update the solution
         ierr = petsc_qupdt_z(ifre,petsc_dcsv_z); CHKERRQ(ierr);

     }

       if(dev->getrank()==0 && ilev==0)
      {
         *flg << "\n";
         flg->flush();
      }

      save_z();


      ierr = 0;
      return ierr;
  }

   PetscErrorCode cFdDomain::petsc_qupdt_z( Int ifre, Vec delta )
  {
      Int iv, iq, iqs, iqe, jq;
      PetscErrorCode ierr;
      PetscScalar *array;

      ierr = VecGetArray(delta,&array); CHKERRQ(ierr);

      dof->range( dev->getrank(), &iqs,&iqe );

      for(iv=0; iv<nv; iv++)
     {
         for(iq=0; iq<nlocal; iq++)
        {
            jq = ilocal[iq];
            dq[iv][jq] = array[iv + iq*nv].real();
        }
     }
      fld->dvar( iqs,iqe, q, aux, dq, daux );
      for(iv=0; iv<nv; iv++)
     {
         for(iq=iqs; iq<iqe; iq++)
        {
            z_re[ifre][iv][iq] += 0.85*daux[iv][iq];
        }
     } 

      for(iv=0; iv<nv; iv++)
     {
         for(iq=0; iq<nlocal; iq++)
        {
            jq = ilocal[iq];
            dq[iv][jq] = array[iv + iq*nv].imag();
        }
     }
      fld->dvar( iqs,iqe, q, aux, dq, daux );
      for(iv=0; iv<nv; iv++)
     {
         for(iq=iqs; iq<iqe; iq++)
        {
            z_im[ifre][iv][iq] += 0.85*daux[iv][iq];
        }
     } 

      ierr = VecRestoreArray(delta,&array); CHKERRQ(ierr);

      ierr = 0;
      return ierr;
  }

   PetscErrorCode cFdDomain::petsc_setrhs_z( Vec var )
  {
      Int iv, iq, jq;
      PetscErrorCode ierr;
      PetscScalar *array;

      ierr = VecGetArray(var,&array); CHKERRQ(ierr);

      for(iv=0; iv<nv; iv++)
     {   
         for(iq=0; iq<nlocal; iq++)
        {   
            jq = ilocal[iq];
            array[iv + nv*iq] = rhsz_re[iv][jq] + PETSC_i*rhsz_im[iv][jq];
        }
     }

      ierr = VecRestoreArray(var,&array); CHKERRQ(ierr);

      ierr = 0;
      return ierr;
  }

   PetscErrorCode cFdDomain::check_lin_lhs()
  {
      Int iv, iq, iqs, iqe, jv, nnz;
      Real *r1;
      PetscErrorCode ierr;
      PetscInt niter_ksp;
      KSPConvergedReason ksp_reason;
      cJacBlk blk;
      PetscInt idxm[10], idxn[10];
      PetscScalar values[100];
      PetscScalar a;
      Real delta = 1e-6;

      movegrid();
      frame();
      weights();

      freq0[0] = 1000./100.;
      //freq0[0] = 0;

      ibpa[0] = 0.2;
      //cout << scientific << setprecision(10) << "\n";

      dof->range( dev->getrank(), &iqs,&iqe );

//assemble right hand side
      setv( 0,dof->size(), nv, 0., rhs );
      gtrhs2(rhs);

//assemble the pre-conditioning matrix here
      ierr = MatZeroEntries(petsc_A_pre); CHKERRQ(ierr);
      petsc_assemble_jac_invi2();
      petsc_assemble_jac_visc2();
      a = -1*PETSC_i * freq0[0];
      for(iq=iqs; iq<iqe; iq++)
     {
         nnz=0;
         for(iv=0; iv<nv; iv++)
        {
            for(jv=0; jv<nv; jv++)
           {
               if(iv==jv) values[nnz++] = a*wq[0][iq];
               else       values[nnz++] = 0;
           }
        }
         idxm[0] = ig_mat[iq];
         idxn[0] = ig_mat[iq];
         ierr = MatSetValuesBlocked(petsc_A_pre, 1, idxm, 1, idxn, values, ADD_VALUES);
         CHKERRQ(ierr);
     }
      add_phase_peri_jac( 0, 1 );


      ierr = MatAssemblyBegin(petsc_A_pre,MAT_FINAL_ASSEMBLY);CHKERRQ(ierr);
      ierr = MatAssemblyEnd(petsc_A_pre,MAT_FINAL_ASSEMBLY);CHKERRQ(ierr);

      ierr = VecAssemblyBegin(petsc_dcsv);CHKERRQ(ierr);
      ierr = VecAssemblyEnd(petsc_dcsv);CHKERRQ(ierr);

      ierr = VecAssemblyBegin(petsc_rhs);CHKERRQ(ierr);
      ierr = VecAssemblyEnd(petsc_rhs);CHKERRQ(ierr);

      //matrix-vector multiplication
      ierr = VecSet(petsc_dcsv, delta); CHKERRQ(ierr);

      //pre-conditioning matrix
      ierr = VecSet(petsc_rhs, 0); CHKERRQ(ierr);
      ierr = MatMult(petsc_A_pre, petsc_dcsv,petsc_rhs);
      //ierr = VecView(petsc_rhs, PETSC_VIEWER_STDOUT_WORLD); CHKERRQ(ierr);


      for(iq=0; iq<nlocal; iq++)
     {
         for(iv=0; iv<nv; iv++)
        {
            zc_re[0][iv][iq] = delta;
            zc_im[0][iv][iq] = 0.;
        }
     }
      fld->dvar( iqs, iqe, q, aux, zc_re[0], z_re[0] );
      fld->dvar( iqs, iqe, q, aux, zc_im[0], z_im[0] );
      gtrhs_z( 0 );

      setv( 0,dof->size(), nv, 0., wrkq );
      for(iq=0; iq<nq; iq++)
     {
         for(iv=0; iv<nv; iv++)
        {
            q[iv][iq] += z_re[0][iv][iq];
        }
     }
      gtrhs2(wrkq);

      Int jq;
      const PetscScalar *array;

      ierr = VecGetArrayRead(petsc_rhs,&array); CHKERRQ(ierr);

      cout << nlocal << "\n";

      for(iq=0; iq<nlocal; iq++)
     {
         cout << "cell " << iq << "\n";
         for(iv=0; iv<nv; iv++)
        {
            jq = ilocal[iq];
            cout << array[iv + nv*iq].real() << " "
                 << array[iv + nv*iq].imag() << " "
                 << rhsz_re[iv][iq] << " "
                 << rhsz_im[iv][iq] << "\n";
//                 << wrkq[iv][iq]-rhs[iv][iq] << "\n";
        }
     }
      ierr = VecRestoreArrayRead(petsc_rhs,&array); CHKERRQ(ierr);


      ierr = 0;
      return ierr;
  }

   PetscErrorCode cFdDomain::add_phase_peri_jac( Int ifre, Real sign )
  {
      Int iqs,iqe, ibs,ibe, ics,ice;
      Int ic,iv,il,ig,iq, iql, iqr, jv, ib;
      cJacBlk blkjac;
      cJacBlkZ blkjac_z;
      PetscErrorCode ierr;

      cTabData data;
      Int asct;
      coo->get( &data );
      data.get( "assembly-sectors", &asct );

      while( dev->transit() )
     {

         prd->range( dev->avail(), &ics,&ice );

         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];

            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac.jac[jv][iv]            = jac_inv_prd[0][ic].jac[jv][iv];
                  //blkjac_z.jac[jv][iv]          = jac_inv_prd[0][ic].jac[jv][iv];
                  jac_df_prd[1][ic].jac[jv][iv] = jac_inv_prd[1][ic].jac[jv][iv];
              }
           }
            jactimerot(asct, nv, &blkjac, -1.);
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac_z.jac[jv][iv] = blkjac.jac[jv][iv];
              }
           }

            //undo the mean flow contribution
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iql, blkjac, -1*sign);

            //add phase change
            jacphase(ibpa[ifre], &blkjac_z, -1.);
            add_blk_to_petsc_matrix_z(petsc_A_pre, iqr, iql, blkjac_z, 1*sign);
        }

         coo->jacoffset(ics, ice, 1., nv, jac_df_prd[1]);
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac.jac[jv][iv]   = jac_df_prd[1][ic].jac[jv][iv];
                  //blkjac_z.jac[jv][iv] = jac_df_prd[1][ic].jac[jv][iv];
              }
           }
            jactimerot(asct, nv, &blkjac, 1.);
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac_z.jac[jv][iv] = blkjac.jac[jv][iv];
              }
           }

            //undo the mean flow contribution  
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iqr, blkjac, 1*sign);

            //add phase change
            jacphase(ibpa[ifre], &blkjac_z, 1.);
            add_blk_to_petsc_matrix_z(petsc_A_pre, iql, iqr, blkjac_z, -1*sign);
        }

         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];

            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac.jac[jv][iv]            = jac_vis_prd[0][ic].jac[jv][iv];
                  //blkjac_z.jac[jv][iv]          = jac_vis_prd[0][ic].jac[jv][iv];
                  jac_df_prd[1][ic].jac[jv][iv] = jac_vis_prd[1][ic].jac[jv][iv];
              }
           }
            jactimerot(asct, nv, &blkjac, -1.);
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac_z.jac[jv][iv] = blkjac.jac[jv][iv];
              }
           }

            //undo the mean flow contribution
            add_blk_to_petsc_matrix(petsc_A_pre, iqr, iql, blkjac, -1*sign);

            //add phase change
            jacphase(ibpa[ifre], &blkjac_z, -1.);
            add_blk_to_petsc_matrix_z(petsc_A_pre, iqr, iql, blkjac_z, 1*sign);
        }

         coo->jacoffset(ics, ice, 1., nv, jac_df_prd[1]);
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac.jac[jv][iv]   = jac_df_prd[1][ic].jac[jv][iv];
                  //blkjac_z.jac[jv][iv] = jac_df_prd[1][ic].jac[jv][iv];
              }
           }
            jactimerot(asct, nv, &blkjac, 1.);
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac_z.jac[jv][iv] = blkjac.jac[jv][iv];
              }
           }

            //undo the mean flow contribution  
            add_blk_to_petsc_matrix(petsc_A_pre, iql, iqr, blkjac, 1*sign);

            //add phase change
            jacphase(ibpa[ifre], &blkjac_z, 1.);
            add_blk_to_petsc_matrix_z(petsc_A_pre, iql, iqr, blkjac_z, -1*sign);
        }
     }

      ierr = 0;
      return ierr;
  }

#endif
