   using namespace std;

#  include <domain/cfd/domain.h>

  /* void cFdDomain::bcs_z( Int ifre )
  {
      Int ig,ib,iv;
      Int ibs,ibe;

      dof->exchange( sxq );
      dof->exchange( sz_re[ifre] );
      dof->exchange( sz_im[ifre] );
  //  cout << "===================> cFdDomain::bcs_z()\n";
    
      while( dev->transit() )
     {
         for( ig=0;ig<ng;ig++ )
        {
           // if( bgnm[ig]=="inlet" ||
           //     bgnm[ig]=="INLET" ||
           //     bgnm[ig]=="MIN" ||
           //     bgnm[ig]=="FREE.inlet" ||
           //     bgnm[ig]=="FREE.INLET"  )
            if( bgnm[ig]=="exit")
           {
              bdf[ig]->range( dev->avail(), &ibs,&ibe );
              //bbj[ig]->bcs_z( ibs,ibe, xb[ig], qb0[ig], zb0_re[ifre][ig], zb0_im[ifre][ig], xqb[ig],zb_re[ig], 
              //                zb_im[ig], iqb[ig], xq, wnb[ig], ifre, (ifre+1)*ibpa );
              bbj[ig]->bcs_z( ibs,ibe, xb[ig], qb0[ig], zb0_re[ifre][ig], zb0_im[ifre][ig], xqb[ig],zb_re[ig], 
                              zb_im[ig], iqb[ig], xq, wnb[ig], ifre, ibpa[ifre] );

           }
           // else if( bgnm[ig]=="inlet" )
           //{
           //   bdf[ig]->range( dev->avail(), &ibs,&ibe );
           //   //bbj[ig]->bcs_z( ibs,ibe, xb[ig], qb0[ig], zb0_re[ifre][ig], zb0_im[ifre][ig], xqb[ig],zb_re[ig], 
           //   //                zb_im[ig], iqb[ig], xq, wnb[ig], ifre, (ifre+1)*ibpa );
           //   //bbj[ig]->bcs_z( ibs,ibe, xb[ig], qb0[ig], NULL, NULL, xqb[ig],zb_re[ig], 
           //   //                zb_im[ig], iqb[ig], xq, wnb[ig] );
           //   cout << "here, inlet boundary\n";
           //   for(ib=ibs; ib<ibe; ib++)
           //  {
           //      int iq = iqb[ig][0][ib];
           //      for(iv=0; iv<nv; iv++)
           //     {
           //         zb0_re[ifre][ig][iv][ib] = z_re[ifre][iv][ib];
           //         zb0_im[ifre][ig][iv][ib] = z_im[ifre][iv][ib];
           //     }
           //  }
           //}
        }
     }

//      for( ig=0;ig<ng;ig++ )
//     {
//         cout << "\n";
//         cout << "\n";
//         cout << "boundary conditions group "<<ig<<" "<<bgnm[ig]<<"\n";
//         for( ib=0;ib<bdf[ig]->size();ib++ )
//        {
//            for( iv=0;iv<nv;iv++ )
//           {
//               cout << qb[ig][iv][ib]<<" ";
//           }
//            cout << "\n";
//        }
//     }

  }*/

//   void cFdDomain::bcs_z( Int ifre )
//  {
//      Int ig,jfre;
//      Int ibs,ibe;
//
//      dof->exchange( sxq );
//      dof->exchange( sz_re[ifre] );
//      dof->exchange( sz_im[ifre] );
//  //  cout << "===================> cFdDomain::bcs_z()\n";
//   
////      cout << "I am here bcs_z ====================\n"; 
//      while( dev->transit() )
//     {
//         for( ig=0;ig<ng;ig++ )
//        {
//            if(ig==bharm[ifre][0])
//           {
//              jfre = bharm[ifre][1];
////              cout << dev->getname() << " " << bgnm[ig] << " " << jfre << "\n";
//              bdf[ig]->range( dev->avail(), &ibs,&ibe );
//              bbj[ig]->bcs_z( ibs,ibe, xb[ig], qb0[ig], zb0_re[jfre][ig], zb0_im[jfre][ig], xqb[ig],zb_re[ig], 
//                              zb_im[ig], iqb[ig], xq, wnb[ig] );
//           }
//            else
//           {
////              cout << bgnm[ig] << " doing nothing\n";
//              bdf[ig]->range( dev->avail(), &ibs,&ibe );
//              bbj[ig]->bcs_z( ibs,ibe, xb[ig], qb0[ig], NULL, NULL, xqb[ig],zb_re[ig], 
//                              zb_im[ig], iqb[ig], xq, wnb[ig] );
//           }
//        }
//     }
//  }

   void cFdDomain::bcs_z( Int ifre )
  {
//      Int ig,jfre;
//      Int ibs,ibe;
//
//      dof->exchange( sxq );
//      dof->exchange( sz_re[ifre] );
//      dof->exchange( sz_im[ifre] );
//  //  cout << "===================> cFdDomain::bcs_z()\n";
//
////      cout << "I am here bcs_z ====================\n"; 
//      while( dev->transit() )
//     {
//         for( ig=0;ig<ng;ig++ )
//        {
//            if(ig==bharm[ifre][0])
//           {  
//              jfre = bharm[ifre][1];
////              cout << dev->getname() << " " << bgnm[ig] << " " << jfre << "\n";
//              bdf[ig]->range( dev->avail(), &ibs,&ibe );
//            //  if( bgnm[ig]=="EXIT"||
//            //      bgnm[ig]=="NREX"||
//            //      bgnm[ig]=="MEX" )
//            // {
//            //     bbj[ig]->bcs_z( ibs,ibe, xb[ig], qb0[ig], NULL, NULL, xqb[ig],zb_re[ig],
//            //                     zb_im[ig], iqb[ig], xq, wnb[ig] );
//            // }
//            //  else
//             {   
//                 bbj[ig]->bcs_z( ibs,ibe, xb[ig], qb0[ig], zb0_re[jfre][ig], zb0_im[jfre][ig], xqb[ig],zb_re[ig],
//                                 zb_im[ig], iqb[ig], xq, wnb[ig] );
//
//                
//                 if(bgnm[ig]=="NRMI" ||
//                    bgnm[ig]=="NRIN" ||
//                    bgnm[ig]=="NRME" ||
//                    bgnm[ig]=="NREX" )
//                {
//                    //with enforced gust reponse
//                    bbj[ig]->nrbc_z( ibs, ibe, z_re[ifre], z_im[ifre], iqb[ig], xb[ig],
//                                     zb0_re[jfre][ig], zb0_im[jfre][ig], zb_re[ig], zb_im[ig], wnb[ig],
//                                     ibql[ig], bgnm[ig], omega, freq0[ifre], ibpa[ifre], dev );
//                }
//             }
//           }
//            else
//           {
//              bdf[ig]->range( dev->avail(), &ibs,&ibe );
//              bbj[ig]->bcs_z( ibs,ibe, xb[ig], qb0[ig], NULL, NULL, xqb[ig],zb_re[ig],
//                              zb_im[ig], iqb[ig], xq, wnb[ig] );
//       
//                 if(bgnm[ig]=="NRMI" ||
//                    bgnm[ig]=="NRIN" ||
//                    bgnm[ig]=="NRME" ||
//                    bgnm[ig]=="NREX" )
//                {
//                    //nothing enforced
//                    bbj[ig]->nrbc_z( ibs, ibe, z_re[ifre], z_im[ifre], iqb[ig], xb[ig],
//                                     NULL, NULL, zb_re[ig], zb_im[ig], wnb[ig],
//                                     ibql[ig], bgnm[ig], omega, freq0[ifre], ibpa[ifre], dev );
//                }
//           }
//        }
//     }
  }

