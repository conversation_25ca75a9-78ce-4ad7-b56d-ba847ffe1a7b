   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::tecplot_z( Int ifre, Int ishift )
  {
//     Int iv, iek, jp, ie,ip, iq, ix;
//     Int ig, ick, ib;
//     Int ip0, ip1, ip2, ip3;
//     Real *tmpxp[3], *v_re[10], *v_im[10];
//
////output
//    string sdum;
//    sdum = strc(ifre) + "-" + strc(ishift);
//    
//    string fname;
//    //fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+".cell.tec";
//    fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+"." + sdum +".cell.z.tec";
//    cout << "save tecplot files to " << fname << "\n";
//
//    for(ix=0; ix<nx; ix++)
//   {
//      tmpxp[ix] = new Real [np];
//   }
//    for(ix=0; ix<nx; ix++)
//   {
//      for(ip=0; ip<np; ip++)
//     {
//        tmpxp[ix][ip] = xp[ix][ip];
//     }
//   }
//
//     for(iv=0; iv<nv; iv++)
//    {
//        v_re[iv] = new Real [nq];
//        v_im[iv] = new Real [nq];
//        for(iq=0; iq<nq; iq++)
//       {
//           v_re[iv][iq] = z_re[ifre][iv][iq];
//           v_im[iv][iq] = z_im[ifre][iv][iq];
//       }
//    }
//     coo->coffset( 0,np, ishift, NULL, tmpxp, NULL, tmpxp );
//     //fld->offset_z( 0,nq, ishift, (ifre+1)*ibpa, NULL, v_re, v_im, NULL, v_re, v_im );
//     fld->offset_z( 0,nq, ishift, ibpa[ifre], NULL, v_re, v_im, NULL, v_re, v_im );
//
//
//    ofstream fle( fname.c_str() );
//    fle.setf(ios_base::scientific);
//    fle.precision(15);
//
//    Int *itmp, *itmp1, *itmp2;
//    itmp= new Int[np];
//    itmp1= new Int[np];
//    itmp2= new Int[np];
//    if( nx == 3 )
//   {
//       fle << "VARIABLES = ";
//       for( ix=0;ix<nx;ix++ )
//      {
//          fle << "\"X"<<strc(ix)<<"\" ";
//      } 
//       for( iv=0;iv<nv;iv++ )
//      {
//          fle << "\"RE"<<strc(iv)<<"\" ";
//      }
//       for( iv=0;iv<nv;iv++ )
//      {
//          fle << "\"IM"<<strc(iv)<<"\" ";
//      }
//       fle << "\n";
//       fle << "ZONE" << " " << "NODES=" << np << " " << "ELEMENTS=" << nq << " " << "DATAPACKING=BLOCK" << " " << "ZONETYPE=FEBRICK";
//       fle << " VARLOCATION=([";
//       for( iv=0;iv<nv;iv++ )
//      {
//          fle << nx+iv+1<<",";
//      } 
//       for( iv=0;iv<nv-1;iv++ )
//      {
//          fle << nx+nv+iv+1<<",";
//      }
//       fle << nx+nv+nv<<"]=CELLCENTERED)\n";
//       //Coordinates
//       for( ix=0;ix<nx;ix++ )
//      {
//          for(ip=0; ip<np; ip++)
//         {
//             //fle << xp[ix][ip] << "\n";
//             fle << tmpxp[ix][ip] << "\n";
//         }
//      }
//       //Real part
//       for( iv=0;iv<nv;iv++ )
//      {
//          for(iek=0; iek<nek; iek++)
//         {
//            if(ne[iek]>0)
//           {
//               for(ie=0; ie<ne[iek]; ie++)
//              {
//                  iq = ieq[iek][0][ie];
//                  fle << v_re[iv][iq] << "\n";
//              }
//           }
//         }
//      }
//       //Imaginary part
//       for( iv=0;iv<nv;iv++ )
//      {
//          for(iek=0; iek<nek; iek++)
//         {
//            if(ne[iek]>0)
//           {
//               for(ie=0; ie<ne[iek]; ie++)
//              {
//                  iq = ieq[iek][0][ie];
//                  fle << v_im[iv][iq] << "\n";
//              }
//           }
//         }
//      }
//       //Connectivity
//       for(iek=0; iek<nek; iek++)
//      {
//          if(ne[iek]>0)
//         {
//             for(ie=0; ie<ne[iek]; ie++)
//            {
//                if(nep[iek]==8)
//               {
//                   for(jp=0; jp<8; jp++)
//                  {
//                      ip = iep[iek][jp][ie];
//                      ip=ip+1;
//                      fle << ip << " ";
//                  }
//                   fle << "\n";
//               }
//                else if(nep[iek]==4)
//               {
//                   for(jp=0; jp<3; jp++)
//                  {
//                      ip = iep[iek][jp][ie];
//                      ip=ip+1;
//                      fle << ip << " ";
//                  }
//                   fle << ip << " ";
//                   ip = iep[iek][3][ie];
//                   ip=ip+1;
//                   for(jp=4; jp<8; jp++)
//                  {
//                      fle << ip << " ";
//                  }
//                   fle << "\n";
//               }
//                else if(nep[iek]==5)
//               {
//                   for(jp=0; jp<4; jp++)
//                  {
//                      ip = iep[iek][jp][ie];
//                      ip=ip+1;
//                      fle << ip << " ";
//                  }
//                   fle << ip << " ";
//                   ip = iep[iek][4][ie];
//                   ip=ip+1;
//                   for(jp=5; jp<8; jp++)
//                  {
//                      fle << ip << " ";
//                  }
//                   fle << "\n";
//               }
//                else if(nep[iek]==6)
//               {
//                   for(jp=0; jp<3; jp++)
//                  {
//                      ip = iep[iek][jp][ie];
//                      ip=ip+1;
//                      fle << ip << " ";
//                  }
//                   fle << ip << " ";
//                   for(jp=3; jp<6; jp++)
//                  {
//                      ip = iep[iek][jp][ie];
//                      ip=ip+1;
//                      fle << ip << " ";
//                  }
//                   fle << ip << " ";
//                   fle << "\n";
//               }
//                else
//               {
//                   cout<<"Error in cell2tec - no treatment yet for element type "<<iek<<" nep[iek]="<<nep[iek]<<endl;
//                   exit(0);
//               }
//            }
//         }
//      }
//   }
//    else
//   {
//       fle << "VARIABLES = ";
//       for( ix=0;ix<nx;ix++ )
//      {
//          fle << "\"X"<<strc(ix)<<"\" ";
//      } 
//       for( iv=0;iv<nv;iv++ )
//      {
//          fle << "\"RE"<<strc(iv)<<"\" ";
//      }
//       for( iv=0;iv<nv;iv++ )
//      {
//          fle << "\"IM"<<strc(iv)<<"\" ";
//      }
//       fle << "\n";
//       fle << "ZONE" << " " << "NODES=" << np << " " << "ELEMENTS=" << nq << " " << "DATAPACKING=BLOCK" << " " << "ZONETYPE=FEQUADRILATERAL";
//       fle << " VARLOCATION=([";
//       for( iv=0;iv<nv;iv++ )
//      {
//          fle << nx+iv+1<<",";
//      } 
//       for( iv=0;iv<nv-1;iv++ )
//      {
//          fle << nx+nv+iv+1<<",";
//      }
//       fle << nx+nv+nv<<"]=CELLCENTERED)\n";
//       //Coordinates
//       for( ix=0;ix<nx;ix++ )
//      {
//          for(ip=0; ip<np; ip++)
//         {
//             //fle << xp[ix][ip] << "\n";
//             fle << tmpxp[ix][ip] << "\n";
//         }
//      }
//       //Real part
//       for( iv=0;iv<nv;iv++ )
//      {
//          for(iek=0; iek<nek; iek++)
//         {
//            if(ne[iek]>0)
//           {
//               for(ie=0; ie<ne[iek]; ie++)
//              {
//                  iq = ieq[iek][0][ie];
//                  fle << v_re[iv][iq] << "\n";
//              }
//           }
//         }
//      }
//       //Imaginary part
//       for( iv=0;iv<nv;iv++ )
//      {
//          for(iek=0; iek<nek; iek++)
//         {
//            if(ne[iek]>0)
//           {
//               for(ie=0; ie<ne[iek]; ie++)
//              {
//                  iq = ieq[iek][0][ie];
//                  fle << v_im[iv][iq] << "\n";
//              }
//           }
//         }
//      }
//       //Connectivity
//       for(iek=0; iek<nek; iek++)
//      {
//          if(ne[iek]>0)
//         {
//             for(ie=0; ie<ne[iek]; ie++)
//            {
//                for(jp=0; jp<nep[iek]; jp++)
//               {
//                   ip = iep[iek][jp][ie];
//                   ip=ip+1;
//                   fle << ip << " ";
//               }
//                if(nep[iek]==3){fle << ip;}
//                fle << "\n";
//            }
//         }
//      }
//  }
//     fle.close();
//
//    delete[] itmp; itmp=NULL;
//    delete[] itmp1; itmp1=NULL;
//    delete[] itmp2; itmp2=NULL;
//
//    for(ix=0; ix<nx; ix++)
//   {
//      delete[] tmpxp[ix];
//      tmpxp[ix]=NULL;
//   }
//
//     for(iv=0; iv<nv; iv++)
//    {
//        delete[] v_re[iv]; v_re[iv]=NULL;
//        delete[] v_im[iv]; v_im[iv]=NULL;
//    }
  }
