
   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::comp_z()
  {
//      Int ifre, ik, iqs, iqe, iq, iv;
//      Int nk;
//      Real dt;
//      Real alpha[10];
//      Real cflmax;
//      string fnme;
//      ofstream fle;
//
//      cflmax = 1;
//
//      movegrid();
//      frame();
//      weights();
//
//      if(dev->getrank()==0)
//     {
//        cout << "=============================================================\n";
//        cout << "===================Frequency domain==========================\n";
//        cout << "=============================================================\n";
//     }
//
//      //nfre = 1;
//      //freq0 = 12566;
//      //freq0 = 12566*4;
//      //freq0 = 12566;
//      //nstep_z = 20000;
//      //nstep_z = 20000;
//      //ibpa = 1;
//
//      //cout << "nfre " << nfre << "\n";
//      //cout << "freq0 " << freq0 << "\n";
//      //cout << "nstep_z " << nstep_z << "\n";
//      //cout << "ibpa "    << ibpa << "\n";
//
//      //freq0 /= 100;
//
//      //cout << nfre << " " << freq0 << " " << nstep_z << "\n";
//
//      cout << scientific;
//
//      cout << "nfre " << nfre << " nstep_z " << nstep_z << "\n";
//
//      //need to recompute the spectral radius
//      setv( 0,dof->size(), nlhs, ZERO,lhsa );
//      gtlhs( lhsa);
//
////      nk=1;
////      alpha[0] = 1;
//      nk = 5;
//      alpha[0] = 0.0695;
//      alpha[1] = 0.1602;
//      alpha[2] = 0.2898;
//      alpha[3] = 0.5060;
//      alpha[4] = 1;
//      dof->range( dev->getrank(), &iqs,&iqe );
//      for(ifre=0; ifre<nfre; ifre++)
//     {
//         cout << "solve for " << ifre << "th harmonic\n";
//         if(dev->getrank()==0)
//        {
//           fnme= dev->getcpath();
//           fnme= fnme+"/"+"hist_z_" + strc(ifre) + ".dat";
//           fle.open(fnme.c_str(), std::ofstream::app);
//           fle << scientific;
//           fle << "#\n";
//        }
//         //cfl = cfl0;
//         bcs_z(ifre);
//         for( Int it=0;it<nstep_z;it++ )
//        {
//
//            //cfl *= 1.1;
//            //cfl = fmin(cflmax, cfl);
//            cfl_z[ifre] *= 1.1;
//            cfl_z[ifre] = fmin(cflmax, cfl_z[ifre]);
//
//            //save old conservative var
//            fld->cnsv_z( iqs,iqe, q, z_re[ifre], zc_re[ifre] );
//            fld->cnsv_z( iqs,iqe, q, z_im[ifre], zc_im[ifre] );
//            for(iv=0; iv<nv; iv++)
//           {
//               for(iq=iqs; iq<iqe; iq++)
//              {
//                  zcold_re[iv][iq] = zc_re[ifre][iv][iq]; 
//                  zcold_im[iv][iq] = zc_im[ifre][iv][iq]; 
//              } 
//           }
//
//            //gradient
//            grad_z(  ifre, z_re[ifre], dzdx_re, z_im[ifre], dzdx_im );
//            gradb_z( ifre, z_re[ifre], dzdx_re, z_im[ifre], dzdx_im );
//
//            //runge-kutta
//            for(ik=0; ik<nk; ik++)
//           {
//               //fluxes
//               gtrhs_z( ifre );
//    
//               //update conservative var
//               for(iv=0; iv<nv; iv++)
//              {
//                  for(iq=iqs; iq<iqe; iq++)
//                 {
//                     //dt = cfl_z[ifre]*wq[0][iq]/(lhsa[0][iq] + wq[0][iq]*(ifre+1)*freq0);
//                     //dt = cfl_z[ifre]*wq[0][iq]/(lhsa[0][iq] + wq[0][iq]*freq0[ifre]);
//                     dt = cfl_z[ifre]*wq[0][iq]/(lhsa[0][iq] + wq[0][iq]*fabs(freq0[ifre]));
//                     zc_re[ifre][iv][iq] = zcold_re[iv][iq] + dt*alpha[ik]*rhsz_re[iv][iq]/wq[0][iq];
//                     zc_im[ifre][iv][iq] = zcold_im[iv][iq] + dt*alpha[ik]*rhsz_im[iv][iq]/wq[0][iq];
//                 }
//              }
//
//               //update primitive var
//               fld->dvar( iqs, iqe, q, aux, zc_re[ifre], z_re[ifre] );
//               fld->dvar( iqs, iqe, q, aux, zc_im[ifre], z_im[ifre] );
//
//           }
//
//            Real err_re[10], err_im[10];
//            for(iv=0; iv<nv; iv++)
//           {
//              err_re[iv] = -9999;
//              err_im[iv] = -9999;
//           }
//            for(iq=iqs; iq<iqe; iq++)
//           {
//              for(iv=0; iv<nv; iv++)
//             {
//                err_re[iv] = fmax(err_re[iv], fabs(zc_re[ifre][iv][iq]-zcold_re[iv][iq]));
//                err_im[iv] = fmax(err_im[iv], fabs(zc_im[ifre][iv][iq]-zcold_im[iv][iq]));
//             } 
//           }
//            dev->gmax( nv,err_re );
//            dev->gmax( nv,err_im );
//
//            if(dev->getrank()==0)
//           {
//             //  cout << it << " " << cfl_z[ifre] << " ";
//             //  for(iv=0; iv<nv; iv++)
//             // {
//             //    cout << err_re[iv] << " " << err_im[iv] << " ";
//             // }
//             //  cout << "\n";
//   
//               fle <<  cfl_z[ifre] << " ";
//               for(iv=0; iv<nv; iv++)
//              {
//                 fle << err_re[iv] << " " << err_im[iv]<< " ";
//              }
//               fle << "\n";
//           }
//        }
//         if(dev->getrank()==0) fle.close();
//     }
//
///*      fnme= dev->getcpath();
//      fnme= fnme+"/"+"z.dat";
//      fle.open(fnme.c_str());
//      for(iq=0; iq<nq; iq++)
//     {
//        fle << xq[0][iq] << " " << xq[1][iq] << " " << z_re[0][0][iq] << " " << z_re[0][1][iq] << " " << z_re[0][2][iq] << " " << z_re[0][3][iq] << "  "
//                                                    << z_im[0][0][iq] << " " << z_im[0][1][iq] << " " << z_im[0][2][iq] << " " << z_im[0][3][iq] << "\n";
//     }
//      fle.close();*/
//
//      //tecplot_z(0);
//      //vtk_z(0);
//      save_z();
  }

   void cFdDomain::gtrhs_z( Int ifre )
  {
//
//      if(limtype==1)
//     {
//         gtrhfm_venk_z( ifre, rhsz_re, rhsz_im );
//     }
//      else
//     {
//         //inviscid flux
//         gtrhf_z( ifre, rhsz_re, rhsz_im );
//
//         //viscous flux
//         gtrhm_z( ifre, rhsz_re, rhsz_im );
//     }
//
//
//      //source term
//      couple_z(ifre);
  }

   void cFdDomain::couple_z( Int ifre )
  {
//      Int iqs,iqe, iv, iq;
//
//      dof->range( dev->getrank(), &iqs,&iqe );
//      for(iv=0; iv<nv; iv++)
//     {  
//         for(iq=iqs; iq<iqe; iq++)
//        { 
//            //rhsz_re[iv][iq] +=  -(ifre+1)*freq0*zc_im[ifre][iv][iq]*wq[0][iq];
//            //rhsz_im[iv][iq] +=  +(ifre+1)*freq0*zc_re[ifre][iv][iq]*wq[0][iq]; 
//            rhsz_re[iv][iq] +=  -freq0[ifre]*zc_im[ifre][iv][iq]*wq[0][iq];
//            rhsz_im[iv][iq] +=  +freq0[ifre]*zc_re[ifre][iv][iq]*wq[0][iq]; 
//        }
//     }
  }

   void cFdDomain::comp_z( Int ifre )
  {
//      Int nk;
//      Int ik, iv, iq, iqs, iqe, it;
//      Real alpha[5];
//      Real cflmax, dt;
////      ofstream fle;
//      string fnme;
//
//      cflmax = 0.8;
//
//      nk = 5;
//      alpha[0] = 0.0695;
//      alpha[1] = 0.1602;
//      alpha[2] = 0.2898;
//      alpha[3] = 0.5060;
//      alpha[4] = 1;
//      dof->range( dev->getrank(), &iqs,&iqe );
//
//      //cout << "solve for " << ifre << "th harmonic\n";
//      bcs_z(ifre);
//
//      //gradient
//      grad_z(  ifre, z_re[ifre], dzdx_re, z_im[ifre], dzdx_im );
//      gradb_z( ifre, z_re[ifre], dzdx_re, z_im[ifre], dzdx_im );
//      for( it=0;it<nstep_z;it++ )
//     {
//
//         //cfl *= 1.1;
//         //cfl = fmin(cflmax, cfl);
//         cfl_z[ifre] *= 1.1;
//         cfl_z[ifre] = fmin(cflmax, cfl_z[ifre]);
//
//         //save old conservative var
//         fld->cnsv_z( iqs,iqe, q, z_re[ifre], zc_re[ifre] );
//         fld->cnsv_z( iqs,iqe, q, z_im[ifre], zc_im[ifre] );
//         for(iv=0; iv<nv; iv++)
//        {
//            for(iq=iqs; iq<iqe; iq++)
//           {
//               zcold_re[iv][iq] = zc_re[ifre][iv][iq]; 
//               zcold_im[iv][iq] = zc_im[ifre][iv][iq]; 
//           } 
//        }
//
//
//         //runge-kutta
//         for(ik=0; ik<nk; ik++)
//        {
//            //fluxes
//            gtrhs_z( ifre );
//    
//            //update conservative var
//            for(iv=0; iv<nv; iv++)
//           {
//               for(iq=iqs; iq<iqe; iq++)
//              {
//                  dt = cfl_z[ifre]*wq[0][iq]/(lhsa[0][iq] + wq[0][iq]*fabs(freq0[ifre]));
//                  zc_re[ifre][iv][iq] = zcold_re[iv][iq] + dt*alpha[ik]*rhsz_re[iv][iq]/wq[0][iq];
//                  zc_im[ifre][iv][iq] = zcold_im[iv][iq] + dt*alpha[ik]*rhsz_im[iv][iq]/wq[0][iq];
//              }
//           }
//
//            //update primitive var
//            fld->dvar( iqs, iqe, q, aux, zc_re[ifre], z_re[ifre] );
//            fld->dvar( iqs, iqe, q, aux, zc_im[ifre], z_im[ifre] );
//
//        }
//
////         Real err_re[10], err_im[10];
////         for(iv=0; iv<nv; iv++)
////        {
////           err_re[iv] = -9999;
////           err_im[iv] = -9999;
////        }
////         for(iq=iqs; iq<iqe; iq++)
////        {
////           for(iv=0; iv<nv; iv++)
////          {
////             err_re[iv] = fmax(err_re[iv], fabs(zc_re[ifre][iv][iq]-zcold_re[iv][iq]));
////             err_im[iv] = fmax(err_im[iv], fabs(zc_im[ifre][iv][iq]-zcold_im[iv][iq]));
////          } 
////        }
////         dev->gmax( nv,err_re );
////         dev->gmax( nv,err_im );
//         Real r1_re_norm=0;
//         Real r1_im_norm=0;
//         Real r1_norm = 0;
//         resd2( rhsz_re, &r1_re_norm );
//         resd2( rhsz_im, &r1_im_norm );
//         r1_norm = r1_re_norm + r1_im_norm;
//         r1_norm = sqrt(r1_norm);
//
//         if(dev->getrank()==0)
//        {
//            flehist_z[ifre] <<  cfl_z[ifre] << " ";
//           // for(iv=0; iv<nv; iv++)
//           //{
//           //   flehist_z[ifre] << err_re[iv] << " " << err_im[iv]<< " ";
//           //}
//            flehist_z[ifre] << r1_norm;
//            flehist_z[ifre] << "\n";
//            flehist_z[ifre].flush();
//        }
//     }
//     // if(dev->getrank()==0) flehist_z[ifre].close();
  }
