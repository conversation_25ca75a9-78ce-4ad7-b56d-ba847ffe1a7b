   using namespace std;

#  include <domain/cfd/domain.h>
#  include <assert.h>

   void cFdDomain::rebuild_z( Int id )
  {

//      cPdata      *dofl,*ptsl;
//      pickle_t     buf;
//      size_t       len,l;
//      string       fnme;
//      Int          i,j, ig, ib, ix, ip, iq, iv;
//      FILE        *f;
//      Real        *szl_re[20], *szl_im[20];
//      Real        **zl_re[20], **zl_im[20];
//
//      for(Int i=0; i<20; i++)
//     {
//         szl_re[i] = NULL;
//         szl_im[i] = NULL;
//         zl_re[i] = NULL;
//         zl_im[i] = NULL;
//     }
//
//      //cout << "the gas is "<<fld<<"\n";
//      //cout << "the coordinate system is "<<coo<<"\n";
//
//      fnme= dev->getcpath();
//      fnme= fnme+"/"+dev->getname()+".restart.z."+strc(level())+"."+strc(id);
//      //fnme= fnme+"/"+dev->getname()+".restart.q."+strc(level())+"."+strc(id) + ".bck";
//      f= fopen(fnme.c_str(),"r");
//      cout << "open restart file " << fnme << "\n";
//
//      l= fread( &len,1,sizeof(len),f );
//      buf= new pickle_v[len];
//      l= fread(  buf,1,        len,f );
//
//      //cout << "the physical time is " << tm << "\n";
//
//      len=0;
//      dofl= new cPdata( dev );
//      ptsl= new cPdata( dev );
//
//      //cout << nfre << " ==================\n";
//      dofl->unpickle( &len,buf );
//      ptsl->unpickle( &len,buf );
//      delete[] buf;buf= NULL; len=0;
//
//      //cout << "reading ...\n";
//      for(Int i=0; i<nfre; i++)
//     {
//         dofl->read( nv,    &(szl_re[i]),f );
//         dofl->read( nv,    &(szl_im[i]),f );
//     }
//
//      fclose(f);
//
//      //rotate the nodes of the mesh
//      //coo->toffset( 0,np, 1, tm*omega, xp );
//      
//
//      for(Int i=0; i<nfre; i++)
//     {
//         if( !sz_re[i] )
//        {
//            nq= dofl->gsize();
//            sz_re[i]=  new Real[  nv*nq];   z_re[i]= new Real*[  nv]; subv(   nv,nq,   sz_re[i],  z_re[i] );
//            sz_im[i]=  new Real[  nv*nq];   z_im[i]= new Real*[  nv]; subv(   nv,nq,   sz_im[i],  z_im[i] );
//        }
//         else
//        {
//            assert( nq == dofl->gsize() );
//        }
//   
//         //cout << "sxpl  "<<sxpl<<"\n";
//         //cout << "sxql  "<<sxql<<"\n";
//         //cout << "sql   "<<sql<<"\n";
//         //cout << "sauxl "<<sauxl<<"\n";
//  
//         zl_re[i] =   new Real*[nv];
//         zl_im[i] =   new Real*[nv];
//
// 
//         subv(   nv,dofl->size(),   szl_re[i],  zl_re[i] );
//         subv(   nv,dofl->size(),   szl_im[i],  zl_im[i] );
//   
//         dofl->makeglobal(  nv,z_re[i],zl_re[i] );
//         dofl->makeglobal(  nv,z_im[i],zl_im[i] );
//   
//         dofl->destroy(    &szl_re[i] );
//         dofl->destroy(    &szl_im[i] );
//   
//   
//         //cout << "assembling\n";
//         delete[] zl_re[i]; zl_re[i]=NULL;
//         delete[] zl_im[i]; zl_im[i]=NULL;
//     }
//      delete dofl; dofl=NULL;
//
//      Int ncpu;
//      ncpu=dev->getncpu();
//      if(id==(ncpu-1))
//     {
//         fnme= dev->getcpath();
//         fnme= fnme+"/"+dev->getname()+"."+strc(level())+".zsolution.bin";
//         cout << "output z-solutions to " << fnme << "\n";
//
//
//         FILE *f;
//         Int idum;
//         Real rdum;
//         string sdum;
//         size_t i, ifre;
//
//         f= fopen(fnme.c_str(),"w");
//         i=fwrite( &nfre,1,  sizeof(idum),f );
//         for(ifre=0; ifre<nfre; ifre++)
//        {
//           i=fwrite( sz_re[ifre],  nv*nq,   sizeof(rdum),f );
//           i=fwrite( sz_im[ifre],  nv*nq,   sizeof(rdum),f );
//        }
//         fclose(f);
//     }
  }

