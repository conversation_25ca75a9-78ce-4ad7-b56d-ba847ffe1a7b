   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::vtk_z( Int ifre, Int ishift )
  {
//     Int iv, iek, jp, ie,ip, iq, ix, je;
//     Int ig, ick, ib;
//     Int ip0, ip1, ip2, ip3;
//     Real *tmpxp[3], *v_re[10], *v_im[10];
//     Int *hlp[2];
//
////output
//    string sdum;
//    sdum = strc(ifre) + "-" + strc(ishift);
//
//    //cout << (ifre+1)*ibpa << " " << ishift << "\n";
//    cout << ibpa[ifre] << " " << ishift << "\n";
//    string fname;
//    //fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+".cell.tec";
//    fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+"." + sdum +".cell.z.vtk";
//    cout << "save tecplot files to " << fname << "\n";
//
//    for(ix=0; ix<nx; ix++)
//   {
//      tmpxp[ix] = new Real [np];
//   }
//    for(ix=0; ix<nx; ix++)
//   {
//      for(ip=0; ip<np; ip++)
//     {
//        tmpxp[ix][ip] = xp[ix][ip];
//     }
//   }
//   //coo->toffset( 0,np, 1, (tm-dtm)*omega, tmpxp );
//   //coo->toffset( 0,nq, 1, (tm-dtm)*omega, q );
//   //if(nx==2)
//  //{
//  //    for(ip=0; ip<np; ip++)
//  //   {
//  //       tmpxp[1][ip] += (tm-dtm)*omega;
//  //   }
//  //}
//     for(iv=0; iv<nv; iv++)
//    {
//        v_re[iv] = new Real [nq];
//        v_im[iv] = new Real [nq];
//        for(iq=0; iq<nq; iq++)
//       {
//           v_re[iv][iq] = z_re[ifre][iv][iq];
//           v_im[iv][iq] = z_im[ifre][iv][iq];
//       }
//    }
//     coo->coffset( 0,np, ishift, NULL, tmpxp, NULL, tmpxp );
//     fld->voffset( 0,nq, ishift, NULL, v_re, NULL, v_re);
//     fld->voffset( 0,nq, ishift, NULL, v_im, NULL,v_im);
//     //fld->offset_z( 0,nq, ishift, (ifre+1)*ibpa, NULL, v_re, v_im, NULL, v_re, v_im );
//     fld->offset_z( 0,nq, ishift, ibpa[ifre], NULL, v_re, v_im, NULL, v_re, v_im );
//     cout << "ibpa " << ifre << " " << ibpa[ifre] << "  ============= \n";
//
//     ofstream fle( fname.c_str() );
//     fle.setf(ios_base::scientific);
//     fle.precision(15);
//
//     fle << "# vtk DataFile Version 2.0\n";
//     fle << fname << "\n";
//     fle << "ASCII\n";
//     fle << "DATASET UNSTRUCTURED_GRID\n";
//
//     fle << "POINTS " << np << " " << "float\n";
//     for(ip=0; ip<np; ip++)
//    {
//       //fle << x[0][ip] << " " << x[1][ip] << " " << x[2][ip] << "\n";
//       if(nx==2)
//      {
//         fle << tmpxp[0][ip] << " " << tmpxp[1][ip] << " " << 0 << "\n";
//      }
//       else if(nx==3)
//      {
//         fle << tmpxp[0][ip] << " " << tmpxp[1][ip] << " " << tmpxp[2][ip] << "\n";
//      }
//    }
//     fle << "\n";
//
//     Int tmpne[2];
//     tmpne[0]=0;
//     tmpne[1]=0;
//     hlp[0] = new Int [nq];
//     hlp[1] = new Int [nq];
//     for(iek=0; iek<nek; iek++)
//    {
//       if(ne[iek]>0)
//      {
//         for(ie=0; ie<ne[iek]; ie++)
//        {
//           hlp[0][tmpne[0]] = iek;
//           hlp[1][tmpne[0]] = ie;
//
//           tmpne[0]++;
//           tmpne[1] += nep[iek] + 1;
//        }
//      }
//    }
//  
//     fle << "CELLS " << tmpne[0] << " " <<  tmpne[1] << "\n";
//
//     if(nx==3 && nek==3)
//    {
//        //special treatment for 3D FDNEUT mesh
//        //0: hex
//        //1: prism
//        //2: tet
//        for(iek=0; iek<nek; iek++)
//       {
//          if(ne[iek]==0) continue;
//          for(ie=0; ie<ne[iek]; ie++)
//         {
//             if(iek!=1)
//            {
//                fle << nep[iek] << " ";
//                for(ip=0; ip<nep[iek]; ip++)
//               {
//                  fle << iep[iek][ip][ie] << " ";
//               }
//                fle << "\n";
//            }
//             else
//            {
//                //strange orientation of prism for vtk
//                fle << 6 << " " << iep[iek][0][ie] << " " << iep[iek][2][ie] << " " << iep[iek][1][ie]
//                         << " " << iep[iek][3][ie] << " " << iep[iek][5][ie] << " " << iep[iek][4][ie] << "\n";
//            }
//         }
//       }
//        fle << "\n";
//   
//        fle << "CELL_TYPES " << tmpne[0] << "\n";
//        for(ie=0; ie<tmpne[0]; ie++)
//       {
//          iek = hlp[0][ie];
//          if(iek==0)
//         {
//            //hex
//            fle << 12 << "\n";
//         }
//          else if(iek==1)
//         {
//            //quad
//            fle << 13 << "\n";
//         }
//          else if(iek==2)
//         {
//            //tet
//            fle << 10 << "\n";
//         }
//       }
//        fle << "\n"; 
//    }
//     else
//    {
//        for(iek=0; iek<6; iek++)
//       {
//          if(ne[iek]==0) continue;
//          for(ie=0; ie<ne[iek]; ie++)
//         {
//            if(iek!=4)
//           {
//              fle << nep[iek] << " ";
//              for(ip=0; ip<nep[iek]; ip++)
//             {
//                fle << iep[iek][ip][ie] << " ";
//             }
//              fle << "\n";
//           }
//            else
//           {
//              //strange orientation of prism for vtk
//              fle << 6 << " " << iep[iek][0][ie] << " " << iep[iek][2][ie] << " " << iep[iek][1][ie]
//                       << " " << iep[iek][3][ie] << " " << iep[iek][5][ie] << " " << iep[iek][4][ie] << "\n";
//           }
//         }
//       }
//        fle << "\n";
//   
//        fle << "CELL_TYPES " << tmpne[0] << "\n";
//        for(ie=0; ie<tmpne[0]; ie++)
//       {
//          iek = hlp[0][ie];
//          if(iek==0)
//         {
//            //triangle
//            fle << 5 << "\n";
//         }
//          else if(iek==1)
//         {
//            //quad
//            fle << 9 << "\n";
//         }
//          else if(iek==2)
//         {
//            //tet
//            fle << 10 << "\n";
//         }
//          else if(iek==3)
//         {
//            //pyramid
//            fle << 14 << "\n";
//         }
//          else if(iek==4)
//         {
//            //prism
//            fle << 13 << "\n";
//         }
//          else if(iek==5)
//         {
//            //hex
//            fle << 12 << "\n";
//         }
//       }
//        fle << "\n"; 
//    }
//
//     fle << "CELL_DATA " <<  tmpne[0] << "\n";
//     for(iv=0; iv<2*nv; iv++)
//    {
//       if(nx==2)
//      {
//               if(iv==0)    fle << "SCALARS Ux_re float 1\n";
//          else if(iv==1)    fle << "SCALARS Uy_re float 1\n";
//          else if(iv==2)    fle << "SCALARS T_re float 1\n";
//          else if(iv==3)    fle << "SCALARS P_re float 1\n";
//          else if(iv==0+nv)  fle << "SCALARS Ux_im float 1\n";
//          else if(iv==1+nv)  fle << "SCALARS Uy_im float 1\n";
//          else if(iv==2+nv)  fle << "SCALARS T_im float 1\n";
//          else if(iv==3+nv)  fle << "SCALARS P_im float 1\n";
//          else               fle << "SCALARS dum float 1\n";
//      }
//       else 
//      {
//               if(iv==0)    fle << "SCALARS Ux_re float 1\n";
//          else if(iv==1)    fle << "SCALARS Uy_re float 1\n";
//          else if(iv==2)    fle << "SCALARS Uz_re float 1\n";
//          else if(iv==3)    fle << "SCALARS T_re float 1\n";
//          else if(iv==4)    fle << "SCALARS P_re float 1\n";
//          else if(iv==0+nv)  fle << "SCALARS Ux_im float 1\n";
//          else if(iv==1+nv)  fle << "SCALARS Uy_im float 1\n";
//          else if(iv==2+nv)  fle << "SCALARS Uz_im float 1\n";
//          else if(iv==3+nv)  fle << "SCALARS T_im float 1\n";
//          else if(iv==4+nv)  fle << "SCALARS P_im float 1\n";
//          else               fle << "SCALARS dum float 1\n";
//      }
//
//       fle << "LOOKUP_TABLE default\n";
//       for(ie=0; ie<tmpne[0]; ie++)
//      {
//         iek = hlp[0][ie];
//         je  = hlp[1][ie];
//         iq = ieq[iek][0][je];
//         //if(iv<nv) fle << z_re[ifre][iv][iq] << "\n";
//         //else      fle << z_im[ifre][iv-nv][iq] << "\n";
//         if(iv<nv) fle << v_re[iv][iq] << "\n";
//         else      fle << v_im[iv-nv][iq] << "\n";
//      }
//    }
//     fle.close();
//
//     for(ix=0; ix<nx; ix++)
//    {
//       delete[] tmpxp[ix];
//       tmpxp[ix]=NULL;
//    }
//     delete[] hlp[0]; hlp[0]=NULL;
//     delete[] hlp[1]; hlp[1]=NULL;
//
//     for(iv=0; iv<nv; iv++)
//    {
//        delete[] v_re[iv]; v_re[iv]=NULL;
//        delete[] v_im[iv]; v_im[iv]=NULL;
//    }
  }
