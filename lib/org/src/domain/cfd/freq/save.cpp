   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::save_z()
  {
      size_t       l=0;
      size_t       len=0;
      pickle_t     buf=NULL;
      string       fnme;
      Int          icpu,ig;

      FILE        *f;   

      cPlugin     *vwp;
// typedef void (*swl_t)( cDomain *, Int , Int , Real *[], Int , Real *[] );
   typedef void (*vwl_t)( cFdDomain *, Int , Int , Int , Int , Int , Int , Int * , Int * , Int **[] , Int **[] , Real *[], Real *[], Real *[], Real *[], Int , Int , Int * , Int [MxNBG][MxNSk], Int **[MxNBG][MxNSk] );
      vwl_t        vwl_f;

      cDomain::save();


      fnme= dev->getcpath();
      icpu= dev->getrank();
      fnme= fnme+"/"+dev->getname()+".restart.z."+strc(ilev)+"."+strc(icpu);
      //cout << "save solution to " << fnme << "\n";

     // string line;
     // line= "mv -f "+fnme+" "+fnme+".bck";
     // system( line.c_str() );


      f= fopen(fnme.c_str(),"w");
      dof->pickle( &len,&buf );
      pts->pickle( &len,&buf );

      l= fwrite( &len,1,sizeof(len),f );
      l= fwrite(  buf,1,        len,f );
      delete[] buf;buf= NULL; len=0;


      for(Int i=0; i<nfre; i++)
     {
        dof->write( nv,sz_re[i],f );
        dof->write( nv,sz_im[i],f );
     }
      fclose(f);
  }

   void cFdDomain::read_z()
  {
      cPdata      *dofl,*ptsl;
      size_t       l=0;
      size_t       len=0;
      pickle_t     buf=NULL;
      string       fnme;
      Int          i, icpu,ig, iq;
      Real         *lsz_re[MXNFRE], *lsz_im[MXNFRE];

      FILE        *f;   

      fnme= dev->getcpath();
      icpu= dev->getrank();
      fnme= fnme+"/"+dev->getname()+".restart.z."+strc(ilev)+"."+strc(icpu);

     // string line;
     // line= "mv -f "+fnme+" "+fnme+".bck";
     // system( line.c_str() );

      for(i=0; i<MXNFRE; i++)
     {
         lsz_re[i] = NULL;
         lsz_im[i] = NULL;
     }


      f= fopen(fnme.c_str(),"r");
      if(f==NULL) return;

      cout << "open restart file " << fnme << "\n";

      l= fread( &len,1,sizeof(len),f );
      buf= new pickle_v[len];
      l= fread(  buf,1,        len,f );

      //cout << "the physical time is " << tm << "\n";

      len=0;
      dofl= new cPdata( dev );
      ptsl= new cPdata( dev );

      //cout << nfre << " ==================\n";
      dofl->unpickle( &len,buf );
      ptsl->unpickle( &len,buf );
      delete[] buf;buf= NULL; len=0;


      for(i=0; i<nfre; i++)
     {
        dofl->read( nv,&(lsz_re[i]),f );
        dofl->read( nv,&(lsz_im[i]),f );
     }
      fclose(f);

      for(i=0; i<nfre; i++)
     {
        for(iq=0; iq<nq*nv; iq++)
       {
           sz_re[i][iq] = lsz_re[i][iq];
           sz_im[i][iq] = lsz_im[i][iq];
       } 
     }

      for(Int i=0; i<nfre; i++)
     {
        dofl->destroy( &(lsz_re[i]) );
        dofl->destroy( &(lsz_im[i]) );
     }

      delete dofl; dofl=NULL;
      delete ptsl; ptsl=NULL;

  }

