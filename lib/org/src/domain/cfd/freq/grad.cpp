   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::grad_z( Int ifre, Real *v_re[], Real **dv_re[], Real *v_im[], Real **dv_im[] )
  {
//      Int iqs,iqe, ibs,ibe, ics,ice;
//      Int ix,jx,ig,ic,iv,iq,iql,iqr,ib,id;
//      Int i;
//      Real eps=1.e-9;
//     // Real eps=1.e-2;
//      Real d;
//      Real w;
//
//      setv( 0,nq, nx*nx,ZERO,dxdx );
//      setv( 0,prd->size(),nx*nx,ZERO,dxdxprd );
//      for( ix=0;ix<nx;ix++ )
//     {
//         setv( 0,nq, 1,eps,dxdx+ijdx[ix][ix] );
//         setv( 0,prd->size(),1,eps,dxdxprd+ijdx[ix][ix] );
//     }
//      for( iv=0;iv<nv;iv++ )
//     {
//         setv( 0,nq, nx,ZERO,dv_re[iv] ); 
//         setv( 0,nq, nx,ZERO,dv_im[iv] ); 
//         setv( 0,prd->size(), nx,ZERO,dzdxprd_re[iv] ); 
//         setv( 0,prd->size(), nx,ZERO,dzdxprd_im[iv] ); 
//     }
//
//      //dof->exchange( sq );
//      //dof->exchange( sxq );
//      dof->exchange( sz_re[ifre] );
//      dof->exchange( sz_im[ifre] );
//      dof->exchange( sxq );
//      while( dev->transit( ) )
//     {
//
//         prd->range( dev->avail(), &ics,&ice );
//
//         //rotate coordinates
//         coo->coffset( ics,ice, 1., iprq[0], xq, NULL, xqprd );
//
//         //change phase of flow variables
//         //fld->offset_z( ics,ice, 1., (ifre+1)*ibpa, iprq[0], v_re, v_im, NULL, zprd_re, zprd_im );
//         fld->offset_z( ics,ice, 1., ibpa[ifre], iprq[0], v_re, v_im, NULL, zprd_re, zprd_im );
//         //rotate flow variables
//         fld->voffset( ics,ice, 1., NULL, zprd_re, NULL,  zprd_re );
//         fld->voffset( ics,ice, 1., NULL, zprd_im, NULL,  zprd_im );
//
//
//         //get gradient
//         grad0_z( ics,ice,  NULL,    xqprd,zprd_re,zprd_im, dxdxprd,dzdxprd_re, dzdxprd_im, 
//                            iprq[1], xq,   v_re,   v_im,    dxdx,   dv_re,      dv_im );
//
//         //rotate the gradient and dxdxprd back
//         //fld->goffset_z( ics,ice,  -1., (ifre+1)*ibpa, ijdx, NULL, dxdxprd, dzdxprd_re, dzdxprd_im, 
//         //                                           NULL, dxdxprd, dzdxprd_re, dzdxprd_im ); 
//         fld->goffset_z( ics,ice,  -1., ibpa[ifre], ijdx, NULL, dxdxprd, dzdxprd_re, dzdxprd_im, 
//                                                          NULL, dxdxprd, dzdxprd_re, dzdxprd_im ); 
//         //change the phase back
//         //fld->goffset_z( ics,ice, -1., (ifre+1)*ibpa, NULL,  dzdxprd_re,  dzdxprd_im,     
//         //                                    NULL,  dzdxprd_re,  dzdxprd_im );
//         fld->goffset_z( ics,ice, -1., ibpa[ifre], NULL,  dzdxprd_re,  dzdxprd_im,     
//                                                   NULL,  dzdxprd_re,  dzdxprd_im );
//
//         for( ix=0;ix<nx;ix++ )
//        {
//            for( jx=0;jx<nx;jx++ )
//           {
//               for( ic=ics;ic<ice;ic++ )
//              {
//                  iq= iprq[0][ic];
//                  dxdx[ijdx[ix][jx]][iq]+= dxdxprd[ijdx[ix][jx]][ic];
//              }
//           }
//            for( iv=0;iv<nv;iv++ )
//           {
//               for( ic=ics;ic<ice;ic++ )
//              {
//                  iq= iprq[0][ic];
//                  dv_re[iv][ix][iq]+= dzdxprd_re[iv][ix][ic];
//                  dv_im[iv][ix][iq]+= dzdxprd_im[iv][ix][ic];
//              }
//           }
//        }
//
//         cnf->range( dev->avail(), &ics,&ice );
//         grad0_z( ics, ice, ifq[0], xq,v_re,v_im,dxdx,dv_re, dv_im, ifq[1], xq,v_re, v_im, dxdx,dv_re, dv_im );
//
//     }
  }

   void cFdDomain::gradb_z( Int ifre, Real *v_re[], Real **dv_re[], Real *v_im[], Real **dv_im[] )
  {
//      Int ig, iv, ibs, ibe;
//
//      for( ig=0;ig<ng;ig++ )
//     {
//         bdf[ig]->exchange( szb_re[ig] );
//         bdf[ig]->exchange( szb_im[ig] );
//         bdf[ig]->exchange( sxqb[ig] );
//     }
//
//      dof->exchange( sdxdx );
//      for( iv=0;iv<nv;iv++ )
//     {
//         dof->exchange( sdzdx_re[iv] );
//         dof->exchange( sdzdx_im[iv] );
//     }
//
//      while( dev->transit() )
//     { 
//         for( ig=0;ig<ng;ig++ ) 
//        {
//            bdf[ig]->range( dev->avail(), &ibs,&ibe );
//            //bbj[ig]->grad( ibs, ibe, xqb[ig], zb_re[ig], iqb[ig], xq, v_re, ijdx, dxdx, dv_re, wnb[ig], wxdb[ig] );
//            //bbj[ig]->grad( ibs, ibe, xqb[ig], zb_im[ig], iqb[ig], xq, v_im, ijdx, dxdx, dv_im, wnb[ig], wxdb[ig] );
//            bbj[ig]->grad_z( ibs, ibe, xqb[ig], iqb[ig], xq, ijdx, dxdx, dv_re, dv_im, wnb[ig], wxdb[ig] );
//
////            void cInvFbndry::grad_z( Int ibs, Int ibe, Real *xqb[], Int *iqbq[], Real *xq[], Int *ijdx[],           
////                                     Real *dxdx[], Real **dqdx_re[], Real **dqdx_im[], Real *wnb[], Real *wxdb[] )
//        }
//     }
  }

   void cFdDomain::grad0_z( Int ics, Int ice, Int *icql, Real *xql[], Real *zl_re[], Real *zl_im[], Real *dxdxl[], Real **dzdxl_re[], Real **dzdxl_im[], 
                                              Int *icqr, Real *xqr[], Real *zr_re[], Real *zr_im[], Real *dxdxr[], Real **dzdxr_re[], Real **dzdxr_im[]  )
  {
//      Real      *wrk[1];
//      Int        ix,jx,iv,ic,iql,iqr;
//      Real       d,w;
//
//      if( ice > ics )
//     {
//
//         wrk[0]= new Real[ice];
//         setv( ics,ice, 1,ZERO, wrk );
//
//         for( ix=0;ix<nx;ix++ )
//        {
//            for( ic=ics;ic<ice;ic++ )
//           {
//               iql= ic;
//               iqr= ic;
//               if( icql ){ iql= icql[ic]; }
//               if( icqr ){ iqr= icqr[ic]; }
//               d= xqr[ix][iqr]- xql[ix][iql];
//               d*= d;
//               wrk[0][ic]+= d;
//           }
//        }
//
//         for( ic=ics;ic<ice;ic++ )
//        {
//            wrk[0][ic]= 1./wrk[0][ic];
//        }
//
//         for( ix=0;ix<nx;ix++ )
//        {
//            for( iv=0;iv<nv;iv++ )
//           {
//               for( ic=ics;ic<ice;ic++ )
//              {
//                  iql= ic;
//                  iqr= ic;
//                  if( icql ){ iql= icql[ic]; }
//                  if( icqr ){ iqr= icqr[ic]; }
//                  w= wrk[0][ic];
//
//                  d= w*(xqr[ix][iqr]-xql[ix][iql])*( zr_re[iv][iqr]-zl_re[iv][iql] );
//                  dzdxl_re[iv][ix][iql]+= d;
//                  dzdxr_re[iv][ix][iqr]+= d;
//
//                  d= w*(xqr[ix][iqr]-xql[ix][iql])*( zr_im[iv][iqr]-zl_im[iv][iql] );
//                  dzdxl_im[iv][ix][iql]+= d;
//                  dzdxr_im[iv][ix][iqr]+= d;
//              }
//           }
//            for( jx=0;jx<nx;jx++ )
//           {
//               for( ic=ics;ic<ice;ic++ )
//              {
//                  iql= ic;
//                  iqr= ic;
//                  if( icql ){ iql= icql[ic]; }
//                  if( icqr ){ iqr= icqr[ic]; }
//                  w= wrk[0][ic];
//                  d= w*(xqr[ix][iqr]-xql[ix][iql])*( xqr[jx][iqr]-xql[jx][iql] );
//                  dxdxl[ijdx[ix][jx]][iql]+= d;
//                  dxdxr[ijdx[ix][jx]][iqr]+= d;
//              }
//           }
//        }
//         delete[] wrk[0];
//     }
  }

   void cFdDomain::grads_z( Real **dv_re[], Real **dv_im[] )
  {
//      Int id,iqs,iqe,iv;
//      for( id=0;id<dev->getncpu()+1;id++ )
//     {
//         dof->range( id, &iqs,&iqe );
//         getrf( iqs,iqe, nx, ijdx,dxdx );
//         for( iv=0;iv<nv;iv++ )
//        {
//            getrs( iqs,iqe, nx, ijdx,dxdx, dv_re[iv] );
//            getrs( iqs,iqe, nx, ijdx,dxdx, dv_im[iv] );
//        }
//
//     }
  }

