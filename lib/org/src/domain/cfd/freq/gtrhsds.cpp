
using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::gtrhsds( cAu3xView<Real>& r )
  {
//      Int ics, ice, iqs, iqe, ibs, ibe, ig;
//      Int i, iv, ic, iq;
//
//      if(!bfredone) return;
//
//      //cout << "DS fluxes =================\n";
//
//      dof->exchange( q[0] );
//      for(i=0; i<nfre; i++)
//     {
//         dof->exchange( z_re[i][0] );
//         dof->exchange( z_im[i][0] );
//         dof->exchange( zc_re[i][0] );
//         dof->exchange( zc_im[i][0] );
//     }
//      while( dev->transit() )
//     {
//         dof->range( dev->avail(), &iqs,&iqe );
//         fld->auxv( iqs,iqe, q,aux );
//
////         for(int iq=iqs; iq<iqe; iq++)
////        {
////           for(int ifre=0; ifre<nfre; ifre++)
////          {
////              aux[3][iq] += 1*(z_re[ifre][0][iq]*z_re[ifre][0][iq] + 
////                               z_im[ifre][0][iq]*z_im[ifre][0][iq] );
////              aux[3][iq] += 1*(z_re[ifre][1][iq]*z_re[ifre][1][iq] + 
////                               z_im[ifre][1][iq]*z_im[ifre][1][iq] );
////          }
////        }
////         for(int iq=iqs; iq<iqe; iq++)
////        {   
////            for(int ifre=0; ifre<nfre; ifre++)
////           {   
////               for(int ix=0; ix<nx; ix++)
////              {
////                  aux[3][iq] += 1*(z_re[ifre][ix][iq]*z_re[ifre][ix][iq] + 
////                                   z_im[ifre][ix][iq]*z_im[ifre][ix][iq] );
////              }
////              // aux[3][iq] += 1*(z_re[ifre][0][iq]*z_re[ifre][0][iq] + 
////              //                  z_im[ifre][0][iq]*z_im[ifre][0][iq] );
////              // aux[3][iq] += 1*(z_re[ifre][1][iq]*z_re[ifre][1][iq] + 
////              //                  z_im[ifre][1][iq]*z_im[ifre][1][iq] );
////           }
////        }
//
////boundary faces
//         for( ig=0;ig<ng;ig++ )
//        {
//            bdf[ig]->range( dev->avail(), &ibs,&ibe );
//            bbj[ig]->auxv( ibs,ibe, qb[ig],auxb[ig] );
//
////        // for(int ib=ibs; ib<ibe; ib++)
////        //{
////        //    auxb[ig][3][ib] += 1*(zb_re[ig][0][ib]*zb_re[ig][0][ib] +
////        //                          zb_im[ig][0][ib]*zb_im[ig][0][ib] );
////        //    auxb[ig][3][ib] += 1*(zb_re[ig][1][ib]*zb_re[ig][1][ib] +
////        //                          zb_im[ig][1][ib]*zb_im[ig][1][ib] );
////        //}
////            for(int ib=ibs; ib<ibe; ib++)
////           {  
////              for(int ifre=0; ifre<nfre; ifre++)
////             {   
////                 for(int ix=0; ix<nx; ix++)
////                {
////                    auxb[ig][3][ib] += 1*(zb0_re[ifre][ig][ix][ib]*zb0_re[ifre][ig][ix][ib] + 
////                                          zb0_im[ifre][ig][ix][ib]*zb0_im[ifre][ig][ix][ib] );
////                    //auxb[ig][3][ib] += 1*(zb_re[ig][0][ib]*zb_re[ig][0][ib] + 
////                    //                      zb_im[ig][0][ib]*zb_im[ig][0][ib] );
////                    //auxb[ig][3][ib] += 1*(zb_re[ig][1][ib]*zb_re[ig][1][ib] + 
////                    //                   zb_im[ig][1][ib]*zb_im[ig][1][ib] );
////                }
////              }
////           }
//
//            setv( ibs,ibe, nv, ZERO, rhsb[ig] );
//            for(i=0; i<nfre; i++)
//           {
//               fld->cnsv_z( ibs,ibe, qb[ig], zb0_re[i][ig], zcb_re[ig] );
//               fld->cnsv_z( ibs,ibe, qb[ig], zb0_im[i][ig], zcb_im[ig] );
//               bbj[ig]->dsflx( ibs,ibe, xb[ig],qb[ig],auxb[ig],zcb_re[ig], zcb_im[ig], zb0_re[i][ig], zb0_im[i][ig], rhsb[ig],
//                               iqb[ig],xq,q,aux,zc_re[i], zc_im[i], z_re[i], z_im[i], r, xb[ig], wnb[ig],wxdb[ig] );
//           }
//        }
//
//// fluxes - periodic faces
//         prd->range( dev->avail(), &ics,&ice );
//         setv( ics,ice, nv, ZERO,rhsprd );
//         for( iv=0;iv<naux;iv++ )
//        {
//            for( ic=ics;ic<ice;ic++ )
//           {
//               iq= iprq[1][ic];
//               auxprd[iv][ic]= aux[iv][iq];
//           }
//        }
//
//         setv( ics,ice, nv, ZERO, rhszprd_re );
//         setv( ics,ice, nv, ZERO, rhszprd_im );
//         setv( ics,ice, nv, ZERO, zprd_re );
//         setv( ics,ice, nv, ZERO, zprd_im );
//
//         //coordinates
//         coo->coffset( ics,ice, (Real)-1.,       iprq[1],xq,                      NULL, xqprd );
//
//         //mean flow
//         fld->voffset( ics,ice, (Real)-1.,       iprq[1], q,                      NULL,  qprd );
//
//         for(i=0; i<nfre; i++)
//        {
//           //change phase of the variables
//           //fld->offset_z(  ics,ice, -1., (i+1)*ibpa, iprq[1], z_re[i],  z_im[i],  NULL, zprd_re,     zprd_im );
//           fld->offset_z(  ics,ice, (Real)-1., ibpa[i], iprq[1], z_re[i],  z_im[i],  NULL, zprd_re,     zprd_im );
//    
//           //rotate the variables
//           fld->voffset( ics,ice, (Real)-1., NULL, zprd_re, NULL,  zprd_re );
//           fld->voffset( ics,ice, (Real)-1., NULL, zprd_im, NULL,  zprd_im );
//
//           fld->cnsv_z( ics,ice, qprd, zprd_re, zcprd_re );
//           fld->cnsv_z( ics,ice, qprd, zprd_im, zcprd_im );
//           fld->dsflx( ics,ice, NULL,    xqprd,qprd, auxprd, zcprd_re, zcprd_im, zprd_re, zprd_im, rhsprd, 
//                                iprq[0], xq,   q,    aux,    zc_re[i], zc_im[i], z_re[i], z_im[i], r, 
//                                xprd,wnprd,wxdprd, true );
//        }
//
//         fld->roffset( ics,ice,  (Real)1, NULL, rhsprd, NULL, rhsprd );
//         for( iv=0;iv<nv;iv++ )
//        {
//            for( ic=ics;ic<ice;ic++ )
//           {
//               iq= iprq[1][ic];
//               r[iv][iq]+= rhsprd[iv][ic];
//           }
//        }
//
//
//// Inviscid fluxes - inner faces
//         cnf->range( dev->avail(), &ics,&ice );
//         for(i=0; i<nfre; i++)
//        {
//           fld->dsflx( ics,ice, ifq[0], xq,q, aux, zc_re[i], zc_im[i], z_re[i], z_im[i], r, 
//                                ifq[1], xq,q, aux, zc_re[i], zc_im[i], z_re[i], z_im[i], r, 
//                                xc,wnc,wxdc, false );
//        }
//     } 
  }

