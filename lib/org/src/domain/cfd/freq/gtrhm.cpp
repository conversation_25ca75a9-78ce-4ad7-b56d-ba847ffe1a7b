
using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::gtrhm_z( Int ifre, cAu3xView<Real>& r_re, cAu3xView<Real>& r_im )
  {
//      Int iqs,iqe, ibs,ibe, ics,ice, id, ids,ide;
//      Int nb,nc;
//      Int ix,ig,ic,iv,iq,iql,iqr,ib,ia;
//      Int ick;
//      
//      Real wcfl,dt;
//
//      if( !vsc->viscous() ) return;
//
//      //grads_z( dzdx_re, dzdx_im );
//      for( ig=0;ig<ng;ig++ )
//     {
//         bdf[ig]->exchange( sauxb[ig] );
//     }
//      dof->exchange( sdst );
//      while( dev->transit() )
//     {
//
//         dof->range( dev->avail(), &iqs,&iqe );
//
//// boundary faces
//
//         for( ig=0;ig<ng;ig++ )
//        {
//            bdf[ig]->range( dev->avail(), &ibs,&ibe );
//        //    bbj[ig]->mflx( ibs,ibe, xb[ig],qb[ig],auxb[ig],rhsb[ig], iqb[ig], xq,q,aux,r, wnb[ig],wxdb[ig],auxfb[ig] );
//            if(bbj[ig]->gettype()==visc_fbndry)
//           {
//               bbj[ig]->dmflx_z( ibs,ibe, xb[ig],qb[ig],auxb[ig], zb_re[ig],  zb_im[ig], resb[ig], resb[ig],
//                                 iqb[ig], xq,    q,     aux,      z_re[ifre], z_im[ifre],r_re,     r_im,
//                                 wnb[ig], wxdb[ig], auxfb[ig] );
//           }
//            else
//           {
//               fld->cnsv_z( ibs,ibe, qb[ig], zb_re[ig], zcb_re[ig] );
//               fld->cnsv_z( ibs,ibe, qb[ig], zb_im[ig], zcb_im[ig] );
////               bbj[ig]->dmflx( ibs,ibe, xb[ig],qb[ig],auxb[ig], zcb_re[ig],  zb_re[ig], resb[ig], 
////                               iqb[ig], xq,    q,     aux,      zc_re[ifre], z_re[ifre],r_re,
////                               wnb[ig], wxdb[ig], auxfb[ig] );
////               bbj[ig]->dmflx( ibs,ibe, xb[ig],qb[ig],auxb[ig], zcb_im[ig],  zb_im[ig], resb[ig], 
////                               iqb[ig], xq,    q,     aux,      zc_im[ifre], z_im[ifre],r_im,
////                               wnb[ig], wxdb[ig], auxfb[ig] );
//           }
//        }
//
//// periodic faces
//         prd->range( dev->avail(), &ics,&ice );
//         for( iv=0;iv<naux;iv++ )
//        {
//            for( ic=ics;ic<ice;ic++ )
//           {
//               iq= iprq[1][ic];
//               auxprd[iv][ic]= aux[iv][iq];
//           }
//        }
//
//         setv( ics,ice, nv, ZERO, rhszprd_re );
//         setv( ics,ice, nv, ZERO, rhszprd_im );
//         setv( ics,ice, nv, ZERO, zprd_re );
//         setv( ics,ice, nv, ZERO, zprd_im );
//
//         //coordinates
//         coo->coffset( ics,ice, -1.,       iprq[1],xq,                      NULL, xqprd );
//
//         //mean flow
//         fld->voffset( ics,ice, -1.,   iprq[1], q,                      NULL,  qprd );
//         fld->goffset( ics,ice, -1.,  iprq[1], dqdx, NULL, dqdxprd );
//
//         //perturbation
//         //change phase of the variables
//         //fld->offset_z(  ics,ice, -1., (ifre+1)*ibpa, iprq[1], z_re[ifre],  z_im[ifre],  NULL, zprd_re,     zprd_im );
//         fld->offset_z(  ics,ice, -1., ibpa[ifre], iprq[1], z_re[ifre],  z_im[ifre],  NULL, zprd_re,     zprd_im );
//         //change phase of the gradient
//         //fld->goffset_z( ics,ice, -1., (ifre+1)*ibpa, iprq[1], dzdx_re,     dzdx_im,     NULL, dzdxprd_re,  dzdxprd_im );
//         fld->goffset_z( ics,ice, -1., ibpa[ifre], iprq[1], dzdx_re,     dzdx_im,     NULL, dzdxprd_re,  dzdxprd_im );
//         //rotate the gradient without chaning the phase
//         fld->goffset( ics,ice, -1., NULL,  dzdxprd_re, NULL,  dzdxprd_re );
//         fld->goffset( ics,ice, -1., NULL,  dzdxprd_im, NULL,  dzdxprd_im );
//
//
//         fld->cnsv_z( ics,ice, qprd, zprd_re, zcprd_re );
//         fld->cnsv_z( ics,ice, qprd, zprd_im, zcprd_im );
////         fld->dmflx( ics,ice, NULL,    xqprd,qprd,auxprd,zcprd_re,   zprd_re,   rhszprd_re, 
////                              iprq[0], xq,   q,   aux,   zc_re[ifre],z_re[ifre],r_re,
////                              xprd, wnprd, wxdprd, auxfprd );
////         fld->dmflx( ics,ice, NULL,    xqprd,qprd,auxprd,zcprd_im,   zprd_im,   rhszprd_im, 
////                              iprq[0], xq,   q,   aux,   zc_im[ifre],z_im[ifre],r_im,
////                              xprd, wnprd, wxdprd, auxfprd );
//
//         //change the phase of the fluxes
//         //fld->offset_z( ics,ice, 1., (ifre+1)*ibpa, NULL, rhszprd_re, rhszprd_im,  NULL, rhszprd_re,  rhszprd_im );
//         fld->offset_z( ics,ice, 1., ibpa[ifre], NULL, rhszprd_re, rhszprd_im,  NULL, rhszprd_re,  rhszprd_im );
//         //rotate the fluxes without chaning the phase
//         fld->roffset( ics,ice, 1., NULL,  rhszprd_re, NULL,  rhszprd_re );
//         fld->roffset( ics,ice, 1., NULL,  rhszprd_im, NULL,  rhszprd_im );
//         for( iv=0;iv<nv;iv++ )
//        {
//            for( ic=ics;ic<ice;ic++ )
//           {
//               iq= iprq[1][ic];
//               r_re[iv][iq]+= rhszprd_re[iv][ic];
//               r_im[iv][iq]+= rhszprd_im[iv][ic];
//           }
//        }
//
//
//// Inviscid fluxes - inner faces
//
//         cnf->range( dev->avail(), &ics,&ice );
////            fld->mflx( ics,ice, ifq[0], xq,q,aux,dqdx,r, ifq[1], xq,q,aux,dqdx,r, xc,wnc,wxdc,auxf );
////         fld->dmflx( ics,ice, ifq[0], xq,q,aux,zc_re[ifre],z_re[ifre],r_re, 
////                              ifq[1], xq,q,aux,zc_re[ifre],z_re[ifre],r_re, 
////                              xc, wnc, wxdc, auxf );
////         fld->dmflx( ics,ice, ifq[0], xq,q,aux,zc_im[ifre],z_im[ifre],r_im, 
////                              ifq[1], xq,q,aux,zc_im[ifre],z_im[ifre],r_im, 
////                              xc, wnc, wxdc, auxf );
//     }
  }

