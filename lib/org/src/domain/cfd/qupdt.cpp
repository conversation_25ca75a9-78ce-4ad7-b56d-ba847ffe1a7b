
   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::qupdt( cAu3xView<Real>& d )
  {
      Int iqs,iqe;
      Int iv,iq;

//      #pragma acc enter data copyin(sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq])
//      start_acc_device();

      dof->range( dev->getrank(), &iqs,&iqe );
      //fld->dvar( iqs,iqe, q, aux, d, daux );
      //fld->dvar( iqs,iqe, sq, saux, sdq, sdaux,nq );
      fld->dvar( iqs,iqe, q, aux, dq, daux );

/*    for( iq=iqs;iq<iqe;iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            cout << d[iv][iq] << " ";
        }
         cout << lhsa[nlhs-1][iq]<<"\n";
     }
      cout << "QUPD EXITS\n";
      std::exit(0);*/

//    dev->qlock();
      //fld->qupd( iqs,iqe, q,aux, d,daux );
      fld->qupd( iqs,iqe, q,aux, dq,daux );
//    dev->qunlock();

/*    cout << "=====================> QUPD \n";
      cout.setf( ios_base::scientific ); 
      cout.width( 22 ); 
      cout.precision( 16 ); 
      for( iq=iqs;iq<iqe;iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            cout << dq[iv][iq] << " ";
        }
         for( iv=0;iv<nv;iv++ )
        {
            cout << daux[iv][iq] << " ";
        }
         cout << "\n";
     }
      cout << "===================== \n";
      cout << "\n";
      cout << "\n";*/
//      for(int iq=0; iq<nq; iq++)
//     {
//         cout << "iq " << iq << " ";
//         for(iv=0; iv<nv; iv++)
//        {
//            cout << q(iv,iq) << " ";
//        }
//         cout << " q \n";
//     }
//
//    exit(0);
//      #pragma acc exit data copyout(sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq])
//      exit_acc_device();
  }
