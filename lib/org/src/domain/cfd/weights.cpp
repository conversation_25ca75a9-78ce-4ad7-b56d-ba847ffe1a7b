   using namespace std;

#  include <domain/cfd/domain.h>
#  include <assert.h>

/*   void cFdDomain::weights()
  {
      Int ig,ix,ib,iq,ic;
      Int ibk,iek;
      Int ies,iee,ibs,ibe,ics,ice,iql,iqr;

//    cout << this << "::weights()\n";

      Int me;
      Real *swrke,*wrke[12];
      setv( 0,dof->size(), nx+1,0., wq );
      me= -1;
      for( iek=0;iek<nek;iek++ )
     {
         me= max( me,ne[iek] );
     }
      for( ibk=0;ibk<nbk;ibk++ )
     {
         me= max( me,nc[ibk] );
     }
      for( ig=0;ig<ng;ig++ )
     {
         for( ibk=0;ibk<nbk;ibk++ )
        {
            me= max( me,nb[ig][ibk] );
        }
     }
      swrke= new Real[ me*nx*(nx+1) ]; subv( nx*(nx+1),me, swrke,wrke );

      pts->exchange( sxp );
      while( dev->transit() )
     {

         for( iek=0;iek<nek;iek++ )
        {
            eld[iek]->range( dev->avail(), &ies,&iee );
            elm[iek]->volume( ies, iee, iep[iek], nx, xp, ieq[iek], wq,xq, wrke, coo );
        }

         for( ibk=0;ibk<nbk;ibk++ )
        {
            cnd[ibk]->range( dev->avail(), &ics,&ice );
            blm[ibk]->area( ics, ice, icp[ibk], nx, xp,xdp, icc[ibk], wnc,wxdc,xc, wrke, coo );
        }

         for( ig=0;ig<ng;ig++ )
        {
            for( ibk=0;ibk<nbk;ibk++ )
           {
               bld[ig][ibk]->range( dev->avail(), &ibs,&ibe );
               blm[ibk]->area( ibs, ibe, ipbp[ig][ibk], nx, xbp[ig],xdbp[ig], ibb[ig][ibk], wnb[ig],wxdb[ig],xb[ig], wrke, coo );
           }
        }

         for( ibk=0;ibk<nbk;ibk++ )
        {
            pld[ibk]->range( dev->avail(), &ibs,&ibe );
            blm[ibk]->area( ibs,ibe, iprdp[ibk], nx, xp,xdp, iprc[ibk], wnprd,wxdprd,xprd, wrke, coo );
        }


     }


      for( Int id=0;id<dev->getncpu()+1;id++ )
     {
         cnf->range( id, &ics,&ice );
         for( ix=0;ix<nx;ix++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iql= ifq[0][ic];
               iqr= ifq[1][ic];
               wq[ix+1][iql]-= wnc[nx][ic]*wnc[ix][ic];
               wq[ix+1][iqr]+= wnc[nx][ic]*wnc[ix][ic];
           }  
        }
         for( ig=0;ig<ng;ig++ )
        {
            bdf[ig]->range( id, &ibs,&ibe );
            for( ix=0;ix<nx;ix++ )
           {
               for( ib=ibs;ib<ibe;ib++ )
              {
                  iqr= iqb[ig][0][ib];
                  wq[ix+1][iqr]+= wnb[ig][nx][ib]*wnb[ig][ix][ib];
              }  
           }
        }
     }


      delete[] swrke; swrke= NULL;

// checks, remove from prod version
//    cout << "THIS IS WQ\n";
      for( iq=0;iq<nq;iq++ )
     {
         for( ix=0;ix<nx+1;ix++ )
        {
//          cout << wq[ix][iq]<<" ";
        }
//       cout << "\n";
     }

//    cout << this<<" :crs is "<<crs<<"\n";
      if( crs )
     {
       ((cFdDomain*)crs)->weights();
     }

 
//    assert( false );
  }*/

//assume no mesh movement, otherwise the commented ont should be used
   void cFdDomain::weights()
  {
      cout << "0\n";
      Int ig,ix,ib,iq,ic;
      Int ibk,iek;
      Int ies,iee,ibs,ibe,ics,ice,iql,iqr;
      Real *swrk0,*swrk1,*swrk2,*swrkx;
      cAu3xView<Real> wrk0,wrk1,wrk2,wrkx;

//    cout << this << "::weights()\n";

      Int me;
      Real *swrke,*wrke[12];
      setv( 0,dof->size(), nx+1,ZERO, wq, "h" );
      me= -1;
      for( iek=0;iek<nek;iek++ )
     {
         me= max( me,ne[iek] );
     }
      for( ibk=0;ibk<nbk;ibk++ )
     {
         me= max( me,nc[ibk] );
     }
      for( ig=0;ig<ng;ig++ )
     {
         for( ibk=0;ibk<nbk;ibk++ )
        {
            me= max( me,nb[ig][ibk] );
        }
     }
      //swrke= new Real[ me*nx*(nx+1) ]; subv( nx*(nx+1),me, swrke,wrke );
      swrk0 = new Real [me*nx]; wrk0.subv(nx, me, swrk0);
      swrk1 = new Real [me*nx]; wrk1.subv(nx, me, swrk1);
      swrk2 = new Real [me*nx]; wrk2.subv(nx, me, swrk2);
      swrkx = new Real [me*nx]; wrkx.subv(nx, me, swrkx);
     
      #pragma acc enter data copyin (swrk0[0:nx*me])
      #pragma acc enter data copyin (swrk1[0:nx*me])
      #pragma acc enter data copyin (swrk2[0:nx*me])
      #pragma acc enter data copyin (swrkx[0:nx*me])

//      #pragma acc enter data copyin (sxp[0:nx*np])
//      #pragma acc enter data copyin (sxq[0:nx*nq])
//      #pragma acc enter data copyin (swq[0:(nx+1)*nq])
//      for( iek=0;iek<nek;iek++ )
//     {
//         #pragma acc enter data copyin (siep[iek][0:nep[iek]*ne[iek]])
//         #pragma acc enter data copyin (sieq[iek][0:neq[iek]*ne[iek]])
//     }
//      #pragma acc enter data copyin (sxdp[0:nvel*np])
//      #pragma acc enter data copyin (swnc[0:(nx+1)*nfc])
//      #pragma acc enter data copyin (swxdc[0:nfc])
//      #pragma acc enter data copyin (sxc[0:nx*nfc])
//      for( ibk=0;ibk<nbk;ibk++ )
//     {
//         #pragma acc enter data copyin (sicp[ibk][0:nbp[ibk]*nc[ibk]])
//         #pragma acc enter data copyin (sicc[ibk][0:1*nc[ibk]])
//     }

//      for( ig=0;ig<ng;ig++ )
//     {
//         #pragma acc enter data copyin (sxbp[ig][0:nx*npb[ig]])
//         #pragma acc enter data copyin (sxdbp[ig][0:nvel*npb[ig]])
//         #pragma acc enter data copyin (swnb[ig][0:(nx+1)*nbb[ig]])
//         #pragma acc enter data copyin (swxdb[ig][0:nbb[ig]])
//         #pragma acc enter data copyin (sxb[ig][0:nx*nbb[ig]])
//         for( ibk=0;ibk<nbk;ibk++ )
//        {
//            Int *sipbp_tmp, *sibb_tmp;
//            
//            sipbp_tmp = sipbp[ig][ibk];
//            sibb_tmp = sibb[ig][ibk];
//
//            //if I do sibp[ig][ibk][0:nbp[ibk]*nb[ig][ibk]], openACC is complaining about partial present
//            //in the device
//            #pragma acc enter data copyin (sipbp_tmp[0:nbp[ibk]*nb[ig][ibk]])
//            #pragma acc enter data copyin (sibb_tmp[0:nbd[ibk]*nb[ig][ibk]])
//        }
//     }
//
//      #pragma acc enter data copyin (swnprd[0:(nx+1)*nprq])
//      #pragma acc enter data copyin (swxdprd[0:1*nprq])
//      #pragma acc enter data copyin (sxprd[0:nx*nprq])
//      for( ibk=0;ibk<nbk;ibk++ )
//     {
//         #pragma acc enter data copyin (siprdp[ibk][0:nbp[ibk]*nprb[ibk]])
//         #pragma acc enter data copyin (siprc[ibk][0:nbq[ibk]*nprb[ibk]])
//     }
 

//      pts->exchange( sxp );
//      while( dev->transit() )
      for( Int id=0;id<dev->getncpu()+1;id++ )
     {
         for( iek=0;iek<nek;iek++ )
        {
            //eld[iek]->range( dev->avail(), &ies,&iee );
            eld[iek]->range( id, &ies,&iee );
            //elm[iek]->volume( ies, iee, iep[iek], nx, xp, ieq[iek], wq,xq, wrke, coo );
            elm[iek]->volume( ies, iee, iep_view[iek], nx, xp, ieq_view[iek], wq,xq, wrk0, wrk1, wrk2, wrkx, coo );
        }
         for( ibk=0;ibk<nbk;ibk++ )
        {
            //cnd[ibk]->range( dev->avail(), &ics,&ice );
            cnd[ibk]->range( id, &ics,&ice );
            //blm[ibk]->area( ics, ice, icp[ibk], nx, xp,xdp, icc[ibk], wnc,wxdc,xc, wrke, coo );
            blm[ibk]->area( ics, ice, icp_view[ibk], nx, xp,xdp, icc_view[ibk], wnc,wxdc,xc, wrk0, wrk1, wrk2, wrkx, coo );
        }

         for( ig=0;ig<ng;ig++ )
        {
            for( ibk=0;ibk<nbk;ibk++ )
           {
               //bld[ig][ibk]->range( dev->avail(), &ibs,&ibe );
               bld[ig][ibk]->range( id, &ibs,&ibe );
               //blm[ibk]->area( ibs, ibe, ipbp[ig][ibk], nx, xbp[ig],xdbp[ig], ibb[ig][ibk], wnb[ig],wxdb[ig],xb[ig], wrke, coo );
               blm[ibk]->area( ibs, ibe, ipbp[ig][ibk], nx, xbp[ig],xdbp[ig], ibb_view[ig][ibk], wnb[ig],wxdb[ig],xb[ig], wrk0, wrk1, wrk2, wrkx, coo );
           }
        }

         for( ibk=0;ibk<nbk;ibk++ )
        {
            //pld[ibk]->range( dev->avail(), &ibs,&ibe );
            pld[ibk]->range( id, &ibs,&ibe );
            //blm[ibk]->area( ibs,ibe, iprdp[ibk], nx, xp,xdp, iprc[ibk], wnprd,wxdprd,xprd, wrke, coo );
            blm[ibk]->area( ibs,ibe, iprdp_view[ibk], nx, xp,xdp, iprc_view[ibk], wnprd,wxdprd,xprd, wrk0, wrk1, wrk2, wrkx, coo );
        }
     }

//      for( Int id=0;id<dev->getncpu()+1;id++ )
//     {
//         cnf->range( id, &ics,&ice );
//         for( ic=ics;ic<ice;ic++ )
//        {
//            for( ix=0;ix<nx;ix++ )
//           {
//               //iql= ifq(0,ic);
//               //iqr= ifq(1,ic);
//               iql= sifq[ADDR(0,ic,nfc)];
//               iqr= sifq[ADDR(1,ic,nfc)];
//               //wq(ix+1,iql)-= wnc(nx,ic)*wnc(ix,ic);
//               //wq(ix+1,iqr)+= wnc(nx,ic)*wnc(ix,ic);
//               swq[ADDR(ix+1,iql,nq)]-= swnc[ADDR(nx,ic,nfc)]*swnc[ADDR(ix,ic,nfc)];
//               swq[ADDR(ix+1,iqr,nq)]+= swnc[ADDR(nx,ic,nfc)]*swnc[ADDR(ix,ic,nfc)];
//           }  
//        }
//         for( ig=0;ig<ng;ig++ )
//        {
//            bdf[ig]->range( id, &ibs,&ibe );
//            for( ib=ibs;ib<ibe;ib++ )
//           {
//               for( ix=0;ix<nx;ix++ )
//              {
//                  iqr= iqb[ig][0][ib];
//                  //wq(ix+1,iqr)+= wnb[ig](nx,ib)*wnb[ig](ix,ib);
//                  swq[ADDR(ix+1,iqr,nq)]+= swnb[ig][ADDR(nx,ib,nbb[ig])]*swnb[ig][ADDR(ix,ib,nbb[ig])];
//              }  
//           }
//        }
//     }
      #pragma acc exit data copyout (swrk0[0:nx*me])
      #pragma acc exit data copyout (swrk1[0:nx*me])
      #pragma acc exit data copyout (swrk2[0:nx*me])
      #pragma acc exit data copyout (swrkx[0:nx*me])

//      #pragma acc exit data copyout (sxp[0:nx*np])
//      #pragma acc exit data copyout (sxq[0:nx*nq])
//      #pragma acc exit data copyout (swq[0:(nx+1)*nq])
//      for( iek=0;iek<nek;iek++ )
//     {
//         #pragma acc exit data copyout (siep[iek][0:nep[iek]*ne[iek]])
//         #pragma acc exit data copyout (sieq[iek][0:neq[iek]*ne[iek]])
//     }
//      #pragma acc exit data copyout (sxdp[0:nvel*np])
//      #pragma acc exit data copyout (swnc[0:(nx+1)*nfc])
//      #pragma acc exit data copyout (swxdc[0:nfc])
//      #pragma acc exit data copyout (sxc[0:nx*nfc])
//      for( ibk=0;ibk<nbk;ibk++ )
//     {
//         #pragma acc exit data copyout (sicp[ibk][0:nbp[ibk]*nc[ibk]])
//         #pragma acc exit data copyout (sicc[ibk][0:1*nc[ibk]])
//     }
//
//      for( ig=0;ig<ng;ig++ )
//     {
//         #pragma acc exit data copyout (sxbp[ig][0:nx*npb[ig]])
//         #pragma acc exit data copyout (sxdbp[ig][0:nvel*npb[ig]])
//         #pragma acc exit data copyout (swnb[ig][0:(nx+1)*nbb[ig]])
//         #pragma acc exit data copyout (swxdb[ig][0:nbb[ig]])
//         #pragma acc exit data copyout (sxb[ig][0:nx*nbb[ig]])
//         for( ibk=0;ibk<nbk;ibk++ )
//        {
//            Int *sipbp_tmp, *sibb_tmp;
//            
//            sipbp_tmp = sipbp[ig][ibk];
//            sibb_tmp = sibb[ig][ibk];
//
//            #pragma acc exit data copyout (sipbp_tmp[0:nbp[ibk]*nb[ig][ibk]])
//            #pragma acc exit data copyout (sibb_tmp[0:nbd[ibk]*nb[ig][ibk]])
//        }
//     }
//
//      #pragma acc exit data copyout (swnprd[0:(nx+1)*nprq])
//      #pragma acc exit data copyout (swxdprd[0:1*nprq])
//      #pragma acc exit data copyout (sxprd[0:nx*nprq])
//      for( ibk=0;ibk<nbk;ibk++ )
//     {
//         #pragma acc exit data copyout (siprdp[ibk][0:nbp[ibk]*nprb[ibk]])
//         #pragma acc exit data copyout (siprc[ibk][0:nbq[ibk]*nprb[ibk]])
//     }

      delete[] swrk0; swrk0=NULL;
      delete[] swrk1; swrk1=NULL;
      delete[] swrk2; swrk2=NULL;
      delete[] swrkx; swrkx=NULL;

//      delete[] swrke; swrke= NULL;

//    assert( false );
  }
