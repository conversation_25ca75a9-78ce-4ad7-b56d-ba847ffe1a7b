
   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::prolong()
  {
//
//      Int          iqs,iqe;
//      Int          iq,jq,iv;
//      Real         d;
//      cFdDomain *cd= (cFdDomain*)crs;
//      dof->range( dev->getrank(), &iqs,&iqe );
//      for( iv=0;iv<nv;iv++ )
//     {
//         for( iq=0;iq<cd->nq;iq++ )
//        {
//            cd->wrkq[iv][iq]= cd->q[iv][iq]- cd->wrkq[iv][iq];
//        }
//     }
//
////    cd->grad( cd->wrkq, cd->dqdx );
////    cd->grads( cd->dqdx );
//
////    cd->debugf( nv,cd->wrkq,"multigrid.correction" );
//      for( iv=0;iv<nv;iv++ )
//     {
//         for( iq=iqs;iq<iqe;iq++ )
//        {
//            jq= iqcrs[iq];
//            Real d=0;
////          d=  cd->dqdx[iv][0][jq]*( xq[0][iq]- cd->xq[0][jq] );
////          d+= cd->dqdx[iv][1][jq]*( xq[1][iq]- cd->xq[1][jq] );
//            dq[iv][iq]= cd->wrkq[iv][jq]+ d;
//        }
//     }
//      rsmth( dq,res, 10,(Real)1.e-2 );
//      //it is found if turbulent quantties  for tw equations are not perturbed,
//      //it works, otherwise it blows up, see DEBUG_3D, mauro
//      for( iv=0;iv<fld->getnv0();iv++ )
//     {
//         for( iq=iqs;iq<iqe;iq++ )
//        {
//            q[iv][iq]+= dq[iv][iq];
//        }
//     }
//      
  } 
