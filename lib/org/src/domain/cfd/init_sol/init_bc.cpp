   using namespace std;

#  include <sstream>
#  include <domain/cfd/domain.h>
#  include <domain/cfd/init_sol/q263.h>

   void cFdDomain::init_bcs()
  {
      Int ig;

//      for( ig=0;ig<ng;ig++ )
//     {
//         if( bbj[ig]->gettype() != neut_fbndry )
//        {
////            bip= new cPlugin( bpl[ig],bpo[ig] );
////            bil_f= (bil_t)bip->Sub();
////          (*bil_f)( this, NULL, bgnm[ig], omegb[ig], nx,nv,nbb[ig],xb[ig],qb0[ig], naux, auxb[ig], idone );
////
////            delete bip; bip=NULL;
//            if(bpo[ig]=="adiabcs")
//           {
//               adiabcs(ig);
//           }
//            else if(bpo[ig]=="unibcs")
//           {
//               unibcs(ig);
//           }
//            else if(bpo[ig]=="mixbcs")
//           {
//               mixbcs(ig);
//           }
//            else if(bpo[ig]=="none")
//           {
//               //periodic boundaries, doing nothing
//           }
//            else if(bpo[ig]=="q263bc")
//           {
//               q263bcs(ig);
//           }
//            else
//           {
//               cout << "Error: unkonwn boundary types " << bpo[ig] << "\n";
//           }
//        }
//     }
  }

   void cFdDomain::unibcs(Int ig, Real *q0)
  {
      Int      iv,iq;

     #pragma acc enter data copyin(q0[0:nv], nbb[0:ng])
     #pragma acc parallel loop gang vector\
      present (sqb0[ig][0:nv*nbb[ig]],q0[0:nv],nbb[0:ng],this) \
      default(none)
      for( iq=0;iq<nbb[ig];iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            //qb0[ig](iv,iq)= q0[iv];
            sqb0[ig][ADDR(iv,iq,nbb[ig])]= q0[iv];
        }
     }
     #pragma acc exit data copyout(q0[0:nv])
     #pragma acc exit data copyout(nbb[0:ng])

      //coo->zvel( 0,nbb[ig], NULL, xb[ig], qb0[ig],qb0[ig] );
      coo->zvel( 0,nbb[ig], xb[ig], qb0[ig],qb0[ig] );

  }

   void cFdDomain::adiabcs(Int ig)
  {
      setv( 0,nbb[ig], nv,ZERO, qb0[ig], "d" );
      coo->frame( 0,nbb[ig], omegb[ig],xb[ig],qb0[ig] );
  }

   void cFdDomain::mixbcs(Int ig)
  {     
      string fnm, cpath, devnm;
      Int nlev, ib, jl, iv, il;
      Real *tmpq[10], y[3], xr[3], *levx[2], rmax, rmin, w;
      istringstream ss;
      string sdum;
      cField *fld;
              
                 
      fnm= dev->getcpath();
      devnm = dev->getname();
   
      fnm= fnm+ "/" + bgnm[ig] + ".mixbc";
 
      assert(nx==3);
      cout <<dev->getname() << "(" << bgnm[ig] << ") read mixplane output to initialize boundary \n";
      ifstream fle;
      fle.open(fnm.c_str());
      if(!fle.good())
     {           
         cout << "Error: can not open file " << fnm << "\n";
         exit(0);
     }     
      getline(fle, sdum);
      ss.clear();
      ss.str(sdum);
      ss >> sdum >> nlev;
      //cout << "number of levels in mixbc " << nlev << "\n";
      for(iv=0; iv<nv; iv++)
     {
         tmpq[iv] = new Real [nlev];
     }
      levx[0] = new Real [nlev];
      levx[1] = new Real [nlev];
      rmin = big;
      rmax =-big;
      for(il=0; il<nlev; il++)
     {
         getline(fle, sdum);
         ss.clear();
         ss.str(sdum);
         ss >> levx[0][il] >> levx[1][il];
         for(iv=0; iv<nv; iv++)
        {
            ss >> tmpq[iv][il];
        }
     }
      fle.close();
 
      rmax = levx[1][nlev-1];
      rmin = levx[1][0];
      for(ib=0; ib<nbb[ig]; ib++)
     {
         y[0] = xb[ig](0,ib);
         y[1] = xb[ig](1,ib);
         y[2] = xb[ig](2,ib);
         xr[0] = y[0];
         xr[1] = sqrt(y[1]*y[1] + y[2]*y[2]);
         for(il=0; il<nlev-1; il++)
        {
            if(xr[1]<rmin)
           {
               jl=0;
               for(iv=0; iv<nv; iv++)
              {
                  qb0[ig](iv,ib) = tmpq[iv][jl];
              }
               break;
           }
            else if(xr[1]>rmax)
           {
               jl=nlev-1;
               for(iv=0; iv<nv; iv++)
              {
                  qb0[ig](iv,ib) = tmpq[iv][jl];
              }
               break;
           }
            else if(xr[1]>=levx[1][il] && xr[1]<=levx[1][il+1])
           {
               w = xr[1] - levx[1][il];
               w/= levx[1][il+1] - levx[1][il];
               for(iv=0; iv<nv; iv++)
              {
                  qb0[ig](iv,ib) = tmpq[iv][il]*(1-w) + tmpq[iv][il+1]*w;
              }
               break;
           }
        }
     }
 
 //the velocities are in ux, ur, ut, turn them into ux, uy, uz
      coo->zvel( 0,nbb[ig], NULL, xb[ig], qb0[ig],qb0[ig] );
      fld->redim( 0,nbb[ig], qb0[ig] );
 
      //for(iv=0; iv<nv; iv++) idone[iv] = 1;
 
      for(iv=0; iv<nv; iv++)
     {
         delete[] tmpq[iv]; tmpq[iv]=NULL;
     }
      delete[] levx[0]; levx[0]=NULL;
      delete[] levx[1]; levx[1]=NULL;
  }

   void cFdDomain::q263bcs(Int ig, string prof_nm)
  {
     cQ263 *qinit;
     string fnm, cpath; 
     int tmpidone[100];

     fnm= dev->getcpath();
     fnm= fnm+"/" + prof_nm;
       
     //only velocity, T and P are used
       
     cout <<"The profile file to initilize the boundary " << bgnm[ig] << " is " << fnm << "\n";
     qinit = new cQ263();
     qinit->setnv(nv);
     qinit->read(fnm);
     qinit->buildgrid();
     qinit->interp(nx, nv, nbb[ig], xb[ig], qb0[ig], tmpidone);

     delete qinit; qinit=NULL;
  }

   void cFdDomain::subinbcs(Int ig, Real *q0)
  {
      Int      iv,iq;

      //these two are angles, pre-multiply the unit, so after nondim, their values would be what I want
      q0[0] *= 100;
      q0[1] *= 100;

     #pragma acc enter data copyin(q0[0:nv], nbb[0:ng])
     #pragma acc parallel loop gang vector\
      present (sqb0[ig][0:nv*nbb[ig]],q0[0:nv],nbb[0:ng],this) \
      default(none)
      for( iq=0;iq<nbb[ig];iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            //qb0[ig](iv,iq)= q0[iv];
            sqb0[ig][ADDR(iv,iq,nbb[ig])]= q0[iv];
        }
     }
     #pragma acc exit data copyout(q0[0:nv])
     #pragma acc exit data copyout(nbb[0:ng])

  }

   void cFdDomain::suboutbcs(Int ig, Real *q0)
  {
      Int      iv,iq;

     #pragma acc enter data copyin(q0[0:nv], nbb[0:ng])
     #pragma acc parallel loop gang vector\
      present (sqb0[ig][0:nv*nbb[ig]],q0[0:nv],nbb[0:ng],this) \
      default(none)
      for( iq=0;iq<nbb[ig];iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            //qb0[ig](iv,iq)= q0[iv];
            sqb0[ig][ADDR(iv,iq,nbb[ig])]= q0[iv];
        }
     }
     #pragma acc exit data copyout(q0[0:nv])
     #pragma acc exit data copyout(nbb[0:ng])

  }

   void cFdDomain::restartbcs(Int ig)
  {
//      cDevice *dev;
      string fnme, bname;
      Int ilev, icpu;
      size_t       len,l;
      FILE        *f;
      cPdata      *dofl;
      pickle_t     buf;
      Real *sxqbl=NULL, *sqbl=NULL, *sauxbl=NULL; 
      cAu3xView<Real> ql,auxbl;
      Real tmptm;
      Int tmpnb;

      tmpnb = nbb[ig];

//      dev= dom->device();
      ilev= 0;
      fnme= dev->getcpath();
      icpu= dev->getrank();

      bname = bgnm[ig];
      fnme= fnme+"/"+dev->getname()+".restart.b."+bname+"."+strc(ilev)+"."+strc(icpu);
      f= fopen(fnme.c_str(),"r");
      cout << "The restart file to restart boundary " << bname << " is " << fnme << "\n";

      l= ::fread( &tmptm,1,sizeof(tmptm),f );
      l= ::fread( &len,1,sizeof(len),f );
      buf= new pickle_v[len];
      l= ::fread(  buf,1,        len,f );

      len=0; 
      dofl= new cPdata( dev );

      dofl->unpickle( &len,buf );
      delete[] buf;buf= NULL; len=0;

      dofl->read( nx,&sxqbl,f );
      dofl->read( nv,&sqbl,f );
      dofl->read( naux,&sauxbl,f );    //MAURO (There was a comment here 20130617)**********

      fclose(f);

      //ql= new Real*[nv];
      ql.subv( nv,tmpnb, sqbl );
      fld->redim( 0,tmpnb, ql );    //MAURO ... REDIM


      for( int iq=0;iq<tmpnb;iq++ )
     {
         //cout << "iq :" << iq << " ";
         for( int iv=0;iv<nv;iv++ )
        {
            //qb[iv][iq]= ql[iv][iq];
            sqb0[ig][ADDR(iv,iq,tmpnb)]= sqbl[ADDR(iv,iq,tmpnb)];
            //cout << sqb0[ig][ADDR(iv,iq,tmpnb)] << " ";
        }
         //cout << " sqb0 " << bgnm[ig] << "\n";
     }

      //auxbl= new Real*[nvauxb];
      auxbl.subv( naux,tmpnb, sauxbl );
      for( int iq=0;iq<tmpnb;iq++ )
     {
         //cout << "iq " << iq << " ";
         //0: utau, 1: yplus, the rest is not needed
         for( int iv=0;iv<naux;iv++ )
        {
           //auxb[iv][iq]= auxbl[iv][iq];
           sauxb[ig][ADDR(iv,iq,tmpnb)]= sauxbl[ADDR(iv,iq,tmpnb)];
           //cout << sauxb[ig][ADDR(iv,iq,tmpnb)] << " ";
        }
         //cout << " sauxb " << bgnm[ig] << " \n";
     }


//      for(int iv=0; iv<nv; iv++)
//     {
//        idone[iv] = 1;
//     }

//      delete[] ql;    ql=NULL;
//      delete[] auxbl; auxbl=NULL;
  }
