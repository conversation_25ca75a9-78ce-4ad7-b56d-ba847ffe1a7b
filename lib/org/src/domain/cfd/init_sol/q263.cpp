using namespace std;

# include <fstream>
# include <iostream>
# include <sstream>
# include <cmath>
# include <domain/cfd/domain.h>
# include <domain/cfd/init_sol/q263.h>

//this is not ideal to put this function
   Real area( Real x0[2], Real x1[2], Real x2[2] );

   cQ263::cQ263()
  {
     Int il, iv;

     nlev=0;
     nsta=0;
     nv=5;

     for(il=0; il<MAXSLN; il++)
    {
       x[il] = NULL;
       r[il] = NULL;
       for(iv=0; iv<MAXVAR; iv++)
      {
         var[il][iv] = NULL;
      }
    }

     sijn=NULL;
     siqn=NULL;
     spx=NULL;
     spv=NULL;
  }

   cQ263::~cQ263()
  {
     Int il, iv;

     for(il=0; il<MAXSLN; il++)
    {
       delete[] x[il]; x[il]=NULL;
       delete[] r[il]; r[il]=NULL;
       for(iv=0; iv<MAXVAR; iv++)
      {
         delete[] var[il][iv]; var[il][iv]=NULL;
      }
    }

     delete[] sijn; sijn=NULL;
     delete[] siqn; siqn=NULL;
     delete[] spx; spx=NULL;
     delete[] spv; spv=NULL;

     nlev=0;
     nsta=0;
  }

   void cQ263::read(string fnm)
  {
      Int il, is, iv;
      istringstream ss;
      string dum;

      ifstream fle(fnm.c_str());

      if(!(fle.good())) { cout << "ERROR: FAIL IN READING PROFILE DATA...\n"; 
                          return;};

      ss.clear();
      getline(fle, dum);
      ss.str(dum);
      ss >> nlev;

      ss.clear();
      getline(fle, dum);
      ss.str(dum);
      ss >> nsta;

      //fle >> nlev;
      //fle >> nsta;

      for(il=0; il<nlev; il++)
     {
        x[il] = new Real [nsta];
        r[il] = new Real [nsta];
      
        for(iv=0; iv<nv; iv++)
       {
          var[il][iv] = new Real [nsta]; 
       } 
     }

     for(is=0; is<nsta; is++)
    {
        for(il=0; il<nlev; il++)
       {
          ss.clear();
          getline(fle, dum);
          ss.str(dum);
          ss >> r[il][is] >> x[il][is];
          for(iv=0; iv<nv; iv++)
         {
            ss >> var[il][iv][is];
         }

          //fle >>  r[il][is] >> x[il][is] >> ux[il][is] >> ur[il][is] 
          //    >> ut[il][is] >> t[il][is] >> p[il][is];
       }
     }
      fle.close();
  }

   void cQ263::buildgrid()
  {
     Int j, i, ip0, iv;
     Int ntmp;

     np = nlev*nsta;

     sijn = new Int [nlev*nsta];
     ijn.subv(nlev,nsta,sijn);

//     ijn = new Int* [nlev];
//     for(j=0; j<nlev; j++)
//    {
//       ijn[j] = new Int [nsta];
//    }

     siqn = new Int [4*nlev*nsta];
     iqn.subv(4,nlev*nsta,siqn);

//     iqn[0] = new Int* [nlev];
//     iqn[1] = new Int* [nlev];
//     iqn[2] = new Int* [nlev];
//     iqn[3] = new Int* [nlev];
//     for(j=0; j<nlev; j++)
//    {
//       iqn[0][j] = new Int [nsta];
//       iqn[1][j] = new Int [nsta];
//       iqn[2][j] = new Int [nsta];
//       iqn[3][j] = new Int [nsta];
//    }


     spv = new Real [nv*np];
     pv.subv(nv,np,spv);
     setv( 0,np, nv, ZERO,pv,"h" );

//     for(iv=0; iv<nv; iv++)
//    { 
//       pv[iv] = new Real [np]; 
//    }
     spx = new Real [2*np]; 
     px.subv(2,np,spx);

//     px[0] = new Real [np];
//     px[1] = new Real [np];

     ntmp=0;
     for(j=0; j<nlev; j++)
    {
       for(i=0; i<nsta; i++)
      {
          //ijn[j][i] = ntmp;
          sijn[ADDR(j,i,nsta)] = ntmp;
          ntmp++;
      }
    }

     for(j=0; j<nlev-1; j++)
    {
       for(i=0; i<nsta-1; i++)
      {
         //iqn[0][j][i] = ijn[j][i];
         //iqn[1][j][i] = ijn[j][i+1];
         //iqn[2][j][i] = ijn[j+1][i+1];
         //iqn[3][j][i] = ijn[j+1][i];
         siqn[ADDR(0,j*nsta+i,nsta*nlev)] = sijn[ADDR(j  ,i  ,nsta)];
         siqn[ADDR(1,j*nsta+i,nsta*nlev)] = sijn[ADDR(j  ,i+1,nsta)];
         siqn[ADDR(2,j*nsta+i,nsta*nlev)] = sijn[ADDR(j+1,i+1,nsta)];
         siqn[ADDR(3,j*nsta+i,nsta*nlev)] = sijn[ADDR(j+1,i  ,nsta)];
      }
    }

     for(j=0; j<nlev; j++)
    {
       for(i=0; i<nsta; i++)
      { 
         //ip0 = ijn[j][i];
         ip0 = sijn[ADDR(j,i,nsta)];
         //px[0][ip0] = x[j][i];
         //px[1][ip0] = r[j][i];
         spx[ADDR(0,ip0,np)] = x[j][i];
         spx[ADDR(1,ip0,np)] = r[j][i];
         for(iv=0; iv<nv; iv++)
        {
           //pv[iv][ip0] = var[j][iv][i];
           spv[ADDR(iv,ip0,np)] = var[j][iv][i];
        }
         //pv[1][ip0] = ur[j][i];
         //pv[2][ip0] = ut[j][i];
         //pv[3][ip0] = t[j][i];
         //pv[4][ip0] = p[j][i];
      }
    }  

/*     ofstream fle("initslo.grid");
     for(j=0; j<nlev; j++)
    {
       fle << "\n";
       for(i=0; i<nsta; i++)
      {
         ip0 = ijn[j][i];
         fle << px[0][ip0] << " " << px[1][ip0] << "\n";
      }
       fle << "\n";
    } 
 
     for(i=0; i<nsta; i++)
    {
       fle << "\n";
       for(j=0; j<nlev; j++)
      {
         ip0 = ijn[j][i];
         fle << px[0][ip0] << " " << px[1][ip0] << "\n";
      }
       fle << "\n";
    } 

     fle.close();*/

//     ofstream ofle;
//     ofle.open("tmp.dat"); 
//     for(j=0; j<nlev-1; j++)
//    {
//       for(i=0; i<nsta-1; i++)
//      {
//         Int ip0, ip1, ip2, ip3;
//         ip0 = siqn[ADDR(0,j*nsta+i,nsta*nlev)];
//         ip1 = siqn[ADDR(1,j*nsta+i,nsta*nlev)];
//         ip2 = siqn[ADDR(2,j*nsta+i,nsta*nlev)];
//         ip3 = siqn[ADDR(3,j*nsta+i,nsta*nlev)];
//         ofle << "\n";
//         ofle << spx[ADDR(0,ip0,np)] << " " << spx[ADDR(1,ip0,np)] << "\n";
//         ofle << spx[ADDR(0,ip1,np)] << " " << spx[ADDR(1,ip1,np)] << "\n";
//         ofle << spx[ADDR(0,ip2,np)] << " " << spx[ADDR(1,ip2,np)] << "\n";
//         ofle << spx[ADDR(0,ip3,np)] << " " << spx[ADDR(1,ip3,np)] << "\n";
//         ofle << spx[ADDR(0,ip0,np)] << " " << spx[ADDR(1,ip0,np)] << "\n";
//      }
//    }
//     ofle.close();
  }

   void cQ263::interp( Int nx, Int nv, Int nb, cAu3xView<Real>& xb, cAu3xView<Real>& qb,  
                       Int *idone )
  {
      Int ib, ip1, ip2, ip3, ip4, iv;
      Int ni, nj, i, j, icell, jcell;
      Real tmpy, tmpz, tmpr;
      Real *cth, *sth, ur, ut;
      bool binside, close;
      Real s0, s1;
      Real g1, g2, g3, g4;
      Real *sxr, *sqb;
      cAu3xView<Real> xr;
      bool breakout;
      Real tmpx[2], x0[2], x1[2], x2[2],x3[2], x4[2], bx[2][4], da, damin, a0, a1;
      Real *sxb;

      sxr = new Real [2*nb];
      xr.subv(2,nb,sxr);

      sqb = qb.get_data();
      sxb = xb.get_data();

      cth = new Real [nb];
      sth = new Real [nb];

      ni = nsta-1;
      nj = nlev-1;

      #pragma acc enter data copyin(cth[0:nb],sth[0:nb],sxr[0:2*nb],spx[0:2*np],spv[0:nv*np],siqn[0:4*nlev*nsta])
      #pragma acc enter data copyin(this)

      #pragma acc parallel loop \
       present(cth[0:nb],sth[0:nb], sqb[0:nv*nb],sxb[0:nx*nb],sxr[0:nb],spx[0:2*np],spv[0:nv*np],this) \
       default(none)
      for(ib=0; ib<nb; ib++)
     {
         //tmpy = xb(1,ib);
         //tmpz = xb(2,ib);
         tmpy = sxb[ADDR(1,ib,nb)];
         tmpz = sxb[ADDR(2,ib,nb)];
         tmpr = tmpy*tmpy + tmpz*tmpz;
         tmpr = sqrt(tmpr);
         cth[ib] = tmpz/tmpr;
         sth[ib] = tmpy/tmpr;

         //xr(0,ib) = xb(0,ib);
         //xr(1,ib) = tmpr;
         sxr[ADDR(0,ib,nb)] = sxb[ADDR(0,ib,nb)];
         sxr[ADDR(1,ib,nb)] = tmpr;
     }

      //find the owernship of each boundary points in the grids and 
      //interpolate the vaules
      #pragma acc parallel loop \
       private(tmpx,x0,x1,x2,x3,x4,bx,icell,jcell,binside,close,ip1,ip2,ip3,ip4,s0,s1,g1,g2,g3,g4) \
       present(cth[0:nb],sth[0:nb], sqb[0:nv*nb],sxb[0:nx*nb],sxr[0:nb],spx[0:2*np],spv[0:nv*np],siqn[0:4*nlev*nsta],this) \
       default(none)
      for(ib=0; ib<nb; ib++)
     { 
         binside=false;
         close=false;

         //inside( Int ip, cAu3xView<Real>& tx, cAu3xView<Real>& initfx, Int ni, Int nj, 
         x0[0] = sxr[ADDR(0,ib,nb)];
         x0[1] = sxr[ADDR(1,ib,nb)];
    
         icell=-1;
         jcell=-1;
         breakout=false;
         #pragma acc loop seq
         for(j=0; j<nj; j++)
        {
           #pragma acc loop seq
           for(i=0; i<ni; i++)
          {
             ip1 = siqn[ADDR(0,j*nsta+i, nsta*nlev)];
             ip2 = siqn[ADDR(1,j*nsta+i, nsta*nlev)];
             ip3 = siqn[ADDR(2,j*nsta+i, nsta*nlev)];
             ip4 = siqn[ADDR(3,j*nsta+i, nsta*nlev)];
    
             //x1[0] = initfx[0][ip1]; x1[1] = initfx[1][ip1];
             //x2[0] = initfx[0][ip2]; x2[1] = initfx[1][ip2];
             //x3[0] = initfx[0][ip3]; x3[1] = initfx[1][ip3];
             //x4[0] = initfx[0][ip4]; x4[1] = initfx[1][ip4];
             x1[0] = spx[ADDR(0,ip1,np)]; x1[1] = spx[ADDR(1,ip1,np)];
             x2[0] = spx[ADDR(0,ip2,np)]; x2[1] = spx[ADDR(1,ip2,np)];
             x3[0] = spx[ADDR(0,ip3,np)]; x3[1] = spx[ADDR(1,ip3,np)];
             x4[0] = spx[ADDR(0,ip4,np)]; x4[1] = spx[ADDR(1,ip4,np)];
    
             a0 = area( x4, x1, x2 );
             a0+= area( x2, x3, x4 );
    
             a1 = area( x0, x1, x2 );
             a1+= area( x0, x2, x3 );
             a1+= area( x0, x3, x4 );
             a1+= area( x0, x4, x1 );
    
             if(fabs(a1-a0)<1e-16)
            {
                icell=i;
                jcell=j;
                binside=true;
                breakout=true;
                //break;
            }
          }
            //if(breakout) break;
        }

         if(binside)
        {
            //parametric( &s0, &s1, icell, jcell,  iqn, px, ib, xr );
            //ip1 = iqn[0][j0][i0];
            //ip2 = iqn[1][j0][i0];
            //ip3 = iqn[2][j0][i0];
            //ip4 = iqn[3][j0][i0];
            ip1 = siqn[ADDR(0,jcell*nsta+icell, nsta*nlev)];
            ip2 = siqn[ADDR(1,jcell*nsta+icell, nsta*nlev)];
            ip3 = siqn[ADDR(2,jcell*nsta+icell, nsta*nlev)];
            ip4 = siqn[ADDR(3,jcell*nsta+icell, nsta*nlev)];
      
            bx[0][0] = spx[ADDR(0,ip1,np)]; bx[1][0] = spx[ADDR(1,ip1,np)];
            bx[0][1] = spx[ADDR(0,ip2,np)]; bx[1][1] = spx[ADDR(1,ip2,np)];
            bx[0][2] = spx[ADDR(0,ip3,np)]; bx[1][2] = spx[ADDR(1,ip3,np)];
            bx[0][3] = spx[ADDR(0,ip4,np)]; bx[1][3] = spx[ADDR(1,ip4,np)];
      
            //x[0] = tx[0][ip];
            //x[1] = tx[1][ip];
            tmpx[0] = sxr[ADDR(0,ib,nb)];
            tmpx[1] = sxr[ADDR(1,ib,nb)];
      
            newtoniter( &s0, &s1, bx, tmpx[0], tmpx[1]);
      
            if(fabs(s0)>5 || fabs(s1)>5)
           {
              s0 = 0.5;
              s1 = 0.5;
           }
        }
         else
        {
             //close=findclose(ib, xr, px, nsta-1, nlev-1, iqn, &icell, &jcell);
            icell=-1;
            jcell=-1;
            close=false;
            da=999;
            damin=999;
            #pragma acc loop seq
            for(j=0; j<nj; j++)
           {
               #pragma acc loop seq
               for(i=0; i<ni; i++)
              {
                  //ip1 = iqn[0][j][i];
                  //ip2 = iqn[1][j][i];
                  //ip3 = iqn[2][j][i];
                  //ip4 = iqn[3][j][i];
                  ip1 = siqn[ADDR(0,j*nsta+i, nsta*nlev)];
                  ip2 = siqn[ADDR(1,j*nsta+i, nsta*nlev)];
                  ip3 = siqn[ADDR(2,j*nsta+i, nsta*nlev)];
                  ip4 = siqn[ADDR(3,j*nsta+i, nsta*nlev)];
     
                  //x1[0] = initfx[0][ip1]; x1[1] = initfx[1][ip1];
                  //x2[0] = initfx[0][ip2]; x2[1] = initfx[1][ip2];
                  //x3[0] = initfx[0][ip3]; x3[1] = initfx[1][ip3];
                  //x4[0] = initfx[0][ip4]; x4[1] = initfx[1][ip4];
                  x1[0] = spx[ADDR(0,ip1,np)]; x1[1] = spx[ADDR(1,ip1,np)];
                  x2[0] = spx[ADDR(0,ip2,np)]; x2[1] = spx[ADDR(1,ip2,np)];
                  x3[0] = spx[ADDR(0,ip3,np)]; x3[1] = spx[ADDR(1,ip3,np)];
                  x4[0] = spx[ADDR(0,ip4,np)]; x4[1] = spx[ADDR(1,ip4,np)];
     
                  a0 = area( x4, x1, x2 );
                  a0+= area( x2, x3, x4 );
     
                  a1 = area( x0, x1, x2 );
                  a1+= area( x0, x2, x3 );
                  a1+= area( x0, x3, x4 );
                  a1+= area( x0, x4, x1 );
     
                  da = fabs(a1-a0);
                  da = da/(a0+small);
     
                  if(da<damin)
                 {
                     icell=i;
                     jcell=j;
                     damin=da;
                     close=true;
                 }
              }
           }

            if(close)
           {
               //parametric( &s0, &s1, icell, jcell,  iqn, px, ib, xr );
               ip1 = siqn[ADDR(0,jcell*nsta+icell, nsta*nlev)];
               ip2 = siqn[ADDR(1,jcell*nsta+icell, nsta*nlev)];
               ip3 = siqn[ADDR(2,jcell*nsta+icell, nsta*nlev)];
               ip4 = siqn[ADDR(3,jcell*nsta+icell, nsta*nlev)];
      
               bx[0][0] = spx[ADDR(0,ip1,np)]; bx[1][0] = spx[ADDR(1,ip1,np)];
               bx[0][1] = spx[ADDR(0,ip2,np)]; bx[1][1] = spx[ADDR(1,ip2,np)];
               bx[0][2] = spx[ADDR(0,ip3,np)]; bx[1][2] = spx[ADDR(1,ip3,np)];
               bx[0][3] = spx[ADDR(0,ip4,np)]; bx[1][3] = spx[ADDR(1,ip4,np)];
      
               //x[0] = tx[0][ip];
               //x[1] = tx[1][ip];
               tmpx[0] = sxr[ADDR(0,ib,nb)];
               tmpx[1] = sxr[ADDR(1,ib,nb)];
      
               newtoniter( &s0, &s1, bx, tmpx[0], tmpx[1]);
      
               if(fabs(s0)>5 || fabs(s1)>5)
              {
                 s0 = 0.5;
                 s1 = 0.5;
              }
           }
            else
           {
               //cout <<"WARNING: CAN NOT FIND VOLUME CONTAINS THE BOUNDARY POINTS\n";
               s0=0.5;
               s1=0.5;
               icell = 0;
               jcell = 0;
               //exit(0);
           }
        }

         g1= (1-s0)*(1-s1);
         g2=    s0 *(1-s1);
         g3=    s0 *   s1;
         g4= (1-s0)*   s1;

         //ip1 = iqn[0][jcell][icell];
         //ip2 = iqn[1][jcell][icell];
         //ip3 = iqn[2][jcell][icell];
         //ip4 = iqn[3][jcell][icell];
         ip1 = siqn[ADDR(0,jcell*nsta+icell,nsta*nlev)];
         ip2 = siqn[ADDR(1,jcell*nsta+icell,nsta*nlev)];
         ip3 = siqn[ADDR(2,jcell*nsta+icell,nsta*nlev)];
         ip4 = siqn[ADDR(3,jcell*nsta+icell,nsta*nlev)];

         for(iv=0; iv<nv; iv++)
        {
           //qb(iv,ib) =  g1*pv[iv][ip1] + g2*pv[iv][ip2] + 
           //             g3*pv[iv][ip3] + g4*pv[iv][ip4];
           sqb[ADDR(iv,ib,nb)] = g1*spv[ADDR(iv,ip1,np)] + g2*spv[ADDR(iv,ip2,np)] + 
                                 g3*spv[ADDR(iv,ip3,np)] + g4*spv[ADDR(iv,ip4,np)];
        }

         //ur=qb(1,ib);
         //ut=qb(2,ib);
         ur=sqb[ADDR(1,ib,nb)];
         ut=sqb[ADDR(2,ib,nb)];

         //qb(1,ib)= cth[ib]* ut+ sth[ib]* ur;
         //qb(2,ib)=-sth[ib]* ut+ cth[ib]* ur;
         sqb[ADDR(1,ib,nb)]= cth[ib]* ut+ sth[ib]* ur;
         sqb[ADDR(2,ib,nb)]=-sth[ib]* ut+ cth[ib]* ur;
     }
      //fle.close();

      for(iv=0; iv<nv; iv++) idone[iv] = 1;

      #pragma acc exit data delete(cth[0:nb],sth[0:nb],sxr[0:nb],spx[0:2*np],spv[0:nv*np],siqn[0:4*nlev*nsta])
      #pragma acc exit data delete(this)
      delete[] cth; cth=NULL;
      delete[] sth; sth=NULL;
      delete[] sxr; sxr=NULL;

  }

#pragma acc routine seq
   void cQ263::newtoniter( Real *l1, Real *l2, Real bx0[2][4], Real tmpx, 
                           Real tmpt )
  {
      Int it;
      Real x0, t0, b1, b2;
      Real x1, x2, x3, x4;
      Real t1, t2, t3, t4;
      Real g1, g2, g3, g4;
      Real g11, g21, g31, g41;
      Real g12, g22, g32, g42;
      Real a11, a12, a21, a22;
      Real det, dl1, dl2;

      x1=bx0[0][0]; x2=bx0[0][1]; x3=bx0[0][2]; x4=bx0[0][3];
      t1=bx0[1][0]; t2=bx0[1][1]; t3=bx0[1][2]; t4=bx0[1][3];

      *l1= 0.5;
      *l2= 0.5;

     #pragma acc loop seq
      for(it=0; it<100; it++)
     {
        g1= (1-(*l1))*(1-(*l2));
        g2=    (*l1) *(1-(*l2));
        g3=    (*l1) *   (*l2);
        g4= (1-(*l1))*   (*l2);
        g11=       -(1-(*l2));
        g21=        (1-(*l2));
        g31=           (*l2);
        g41=          -(*l2);
        g12=-(1-(*l1));
        g22=   -(*l1) ;
        g32=    (*l1);
        g42= (1-(*l1));
        x0= g1*x1+ g2*x2+ g3*x3+ g4*x4;
        t0= g1*t1+ g2*t2+ g3*t3+ g4*t4;
        b1= tmpx-x0;
        b2= tmpt-t0;
        a11= g11*x1+ g21*x2+ g31*x3+ g41*x4;
        a12= g12*x1+ g22*x2+ g32*x3+ g42*x4;
        a21= g11*t1+ g21*t2+ g31*t3+ g41*t4;
        a22= g12*t1+ g22*t2+ g32*t3+ g42*t4;
        det= a11*a22- a12*a21;
        dl1= ( a22*b1- a12*b2 )/det;
        dl2= (-a21*b1+ a11*b2 )/det;
        *l1= *l1+ 0.8*dl1;
        *l2= *l2+ 0.8*dl2;

     }
  }

#pragma acc routine seq
   Real area( Real x0[2], Real x1[2], Real x2[2] )
  {
     Real a;
     Real dx0[2], dx1[2];

     dx0[0] = x1[0] - x0[0];
     dx0[1] = x1[1] - x0[1];

     dx1[0] = x2[0] - x0[0];
     dx1[1] = x2[1] - x0[1];

     a = (dx0[0]*dx1[1] - dx0[1]*dx1[0]);
     a*= 0.5;
     a = fabs(a);

     return a;
  }

