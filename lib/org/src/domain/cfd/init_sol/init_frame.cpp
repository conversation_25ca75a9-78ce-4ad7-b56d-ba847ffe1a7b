
   using namespace std;
#  include <iniParser.h>
#  include <domain/cfd/domain.h>

   void cFdDomain::init_frame()
  {
      cout << "JM28 FRAME PLUGIN\n";
      Int ig;
      IniParser parser;
      string item, val, str_sec;

      parser.parseFromFile("input.au3x");

      str_sec = dev->getname();
      item = "frame-speed";
      val = parser.getValue(str_sec, item);
      omega = stod(val);
      for( ig=0;ig<ng;ig++ )
     {
         omegb[ig]=omega;
         if( bgnm[ig] == "CASING" ){ omegb[ig]=0; };
         cout << bgnm[ig]<<" "<<omegb[ig]<<"\n";
     }

     #pragma acc update device(omegb[0:ng])
  }


