using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::rsmth( cAu3xView<Real>& v, cAu3xView<Real>& r, Int nit, Real eps )
  {
      Int iqs,iqe, ibs,ibe, ics,ice;
      Int ix,jx,ig,ic,iv,iq,iql,iqr,ib,id;
      Int i;
      Real d;

//    debugf( nv, v, "before" );

      for( Int it=0;it<nit;it++ )
     {

         setv( 0,nq, nv,ZERO,sres,nq ); 
         setv( 0,nq, nlhs,ZERO,slhsa,nq ); 

         for( id=0;id<dev->getncpu()+1;id++ )
        {

            cnf->range( id, &ics,&ice );

            for( ic=ics;ic<ice;ic++ )
           {
               //iql= ifq[0][ic];
               //iqr= ifq[1][ic];
               iql= sifq[ADDR(0,ic,nfc)];
               iqr= sifq[ADDR(1,ic,nfc)];
               d=0;
               for( ix=0;ix<nx;ix++ )
              {
                  //d+= wnc[ix][ic]*( xq[ix][iqr]- xq[ix][iql] );
                  d+= swnc[ADDR(ix,ic,nfc)]*( sxq[ADDR(ix,iqr,nq)]- sxq[ADDR(ix,iql,nq)] );
              }
               d*= d;
               d= 1./d;
               //lhsa[0][iql]+= d;
               //lhsa[0][iqr]+= d;
               slhsa[ADDR(0,iql,nq)]+= d;
               slhsa[ADDR(0,iqr,nq)]+= d;
           }
        }

         Real *sv;
         sv = v.get_data();
         dof->exchange( sv );
         while( dev->transit( ) )
        {
            cnf->range( dev->avail(), &ics,&ice );
            for( ic=ics;ic<ice;ic++ )
           {
               //iql= ifq[0][ic];
               //iqr= ifq[1][ic];
               iql= sifq[ADDR(0,ic,nfc)];
               iqr= sifq[ADDR(1,ic,nfc)];
               d=0;
               for( ix=0;ix<nx;ix++ )
              {
                  //d+= wnc[ix][ic]*( xq[ix][iqr]- xq[ix][iql] );
                  d+= swnc[ADDR(ix,ic,nfc)]*( sxq[ADDR(ix,iqr,nq)]- sxq[ADDR(ix,iql,nq)] );
              }
               d*= d;
               d= 1./d;
               for( iv=0;iv<nv;iv++ )
              {
                  //r[iv][iql]+= ( v[iv][iqr]- v[iv][iql] )*d;
                  //r[iv][iqr]-= ( v[iv][iqr]- v[iv][iql] )*d;
                  sres[ADDR(iv,iql,nq)]+= ( sdq[ADDR(iv,iqr,nq)]- sdq[ADDR(iv,iql,nq)] )*d;
                  sres[ADDR(iv,iqr,nq)]-= ( sdq[ADDR(iv,iqr,nq)]- sdq[ADDR(iv,iql,nq)] )*d;
              }
           }
        }
         
         dof->range( dev->getrank(), &iqs,&iqe );
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv;iv++ )
           {
               //v[iv][iq]+= eps*r[iv][iq]/lhsa[0][iq];
               sdq[ADDR(iv,iq,nq)]+= eps*sres[ADDR(iv,iq,nq)]/slhsa[ADDR(0,iq,nq)];
           }
        }
     }

/*    debugf(  1, lhsa, "diagonal" );
      debugf( nv, r, "update" );
      debugf( nv, v, "after" );*/
  }
