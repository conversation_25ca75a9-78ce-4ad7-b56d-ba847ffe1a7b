
   using namespace std;

#  include <domain/cfd/domain.h>

   void jactimerot( Int asct, Int nv, cJacBlk *blkjac, Real f)
  {
      //Real pitch = pi2/136.;
      Real pitch = pi2/(Real)asct;
      Real rot[7][7];
      Real cth, sth;
      Int iv, jv, kv;
      cJacBlk newjac;

      cth = cos(f*pitch);
      sth = sin(f*pitch);

      rot[0][0] =1;rot[0][1] =0;rot[0][2] = 0  ;rot[0][3] = 0 ; rot[0][4] = 0; rot[0][5] = 0; rot[0][6] = 0;
      rot[1][0] =0;rot[1][1] =1;rot[1][2] = 0  ;rot[1][3] = 0 ; rot[1][4] = 0; rot[1][5] = 0; rot[1][6] = 0;
      rot[2][0] =0;rot[2][1] =0;rot[2][2] = cth;rot[2][3] =sth; rot[2][4] = 0; rot[2][5] = 0; rot[2][6] = 0;
      rot[3][0] =0;rot[3][1] =0;rot[3][2] =-sth;rot[3][3] =cth; rot[3][4] = 0; rot[3][5] = 0; rot[3][6] = 0;
      rot[4][0] =0;rot[4][1] =0;rot[4][2] = 0  ;rot[4][3] = 0 ; rot[4][4] = 1; rot[4][5] = 0; rot[4][6] = 0;
      rot[5][0] =0;rot[5][1] =0;rot[5][2] = 0  ;rot[5][3] = 0 ; rot[5][4] = 0; rot[5][5] = 1; rot[5][6] = 0;
      rot[6][0] =0;rot[6][1] =0;rot[6][2] = 0  ;rot[6][3] = 0 ; rot[6][4] = 0; rot[6][5] = 0; rot[6][6] = 1;

      for(iv=0; iv<nv; iv++)
     {
         for(jv=0; jv<nv; jv++)
        {
            newjac.jac[iv][jv] = 0.;
            for(kv=0; kv<nv; kv++)
           {
               newjac.jac[iv][jv] += (blkjac->jac[iv][kv])*rot[kv][jv];
           }
        }
     }
      for(iv=0; iv<nv; iv++)
     {
         for(jv=0; jv<nv; jv++)
        {
            blkjac->jac[iv][jv] = newjac.jac[iv][jv];
        }
     }
  }

   void cFdDomain::gtlhs( cAu3xView<Real>& a )
  {
      Int iqs,iqe, ibs,ibe, ics,ice;
      Int ic,iv,il,ig,iq;

// reset lhs
//     #pragma acc enter data copyin ( iprq0[0:nprq],iprq1[0:nprq])
//      for( ig=0;ig<ng;ig++ )
//     {
//         #pragma acc enter data copyin(sxb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],slhsb[ig][0:nlhs*nbb[ig]], siqb[ig][0:nbb[ig]], \
//                                       swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]],sauxfb[ig][0:nauxf*nbb[ig]])
//     }
//     #pragma acc enter data copyin ( sqprd[0:nv*nprq],sauxprd[0:naux*nprq],slhsprd[0:nlhs*nprq], \
//                                     swnprd[0:(nx+1)*nprq],swxdprd[0:nprq],sauxfprd[0:nauxf*nprq])
//     #pragma acc enter data copyin (sifq[0:2*nfc],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],slhsa[0:nlhs*nq],\
//                                     swnc[0:(nx+1)*nfc],swxdc[0:nfc],sauxf[0:nauxf*nfc])
//     #pragma acc enter data copyin (sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq])
//     #pragma acc enter data copyin ( this)
//      start_acc_device();

      dof->exchange( sq );
      while( dev->transit() )
     {

// boundary faces

         for( ig=0;ig<ng;ig++ )
        {
            bdf[ig]->range( dev->avail(), &ibs,&ibe );
            setv( ibs,ibe, nlhs, ZERO,lhsb[ig],"d" );
            //bbj[ig]->ilhs( ibs,ibe, xb[ig],qb[ig],auxb[ig],lhsb[ig],  
            //               iqb[ig], xq,q,aux,a,  wnb[ig],wxdb[ig],auxfb[ig] );
            bbj[ig]->ilhs( ibs,ibe,xb[ig],qb[ig],auxb[ig],lhsb[ig],  
                           iqb_view[ig], xq,q,aux,lhsa,wnb[ig],wxdb[ig],auxfb[ig] );
        }

         prd->range( dev->avail(), &ics,&ice );
         setv( ics,ice, nlhs, ZERO,lhsprd, "d" );
         //fld->ilhs( ics,ice, NULL, qprd,auxprd,lhsprd, iprq[0], q,aux,lhsa,wnprd,wxdprd,auxfprd );
         fld->ilhs( ics,ice, NULL_iview, qprd,auxprd,lhsprd, iprq0, q,aux,lhsa,wnprd,wxdprd,auxfprd );
         //fld->aoffset( ics,ice,  1, NULL, lhsprd, NULL, lhsprd ); //this line does nothing, commented out here, but dont'remove
         #pragma acc parallel loop \
         present(slhsprd[0:nlhs*nprq],slhsa[0:nlhs*nq],siprq1[0:nprq],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            for( iv=0;iv<nlhs;iv++ )
           {
               iq= siprq1[ic];
               //lhsa[iv][iq]+= lhsprd[iv][ic];
               slhsa[ADDR(iv,iq,nq)]+= slhsprd[ADDR(iv,ic,nprq)];
           }
        }
         cnf->range( dev->avail(),&ics,&ice );
         //fld->ilhs( ics,ice,ifq[0],q,aux,a,ifq[1],q,aux,a,wnc,wxdc,auxf );
         fld->ilhs( ics,ice,ifq,q,aux,lhsa,wnc,wxdc,auxf );

     }

// volume lhs and inversion only on local DOFs
      dof->range( dev->getrank(), &iqs,&iqe );
      //fld->slhs( iqs,iqe,     cfl, q,aux, dqdx, dst, wq, a );
      fld->slhs( iqs,iqe,     cfl, q,aux, dqdx, dst, wq, lhsa );
      if(unst)
     {
        if(ntlv==3)
       {
          //2nd order time discretization
          //fld->vlhs( iqs,iqe, dtm,cfl, q,aux, NULL, dst, wq, a, 1.5 );
          fld->vlhs( iqs,iqe, dtm,cfl, q,aux, NULL_rview, dst, wq, lhsa, 1.5 );
       }
        else
       {
          //1st order time discretization
          //fld->vlhs( iqs,iqe, dtm,cfl, q,aux, NULL, dst, wq, a, 1. );
          fld->vlhs( iqs,iqe, dtm,cfl, q,aux, NULL_rview, dst, wq, lhsa, 1.0 );
       }
     }
      else
     {
          //fld->vlhs( iqs,iqe, dtm,cfl, q,aux, NULL, dst, wq, a, -1. );
          fld->vlhs( iqs,iqe, dtm,cfl, q,aux, NULL_rview, dst, wq, lhsa, -1.0 );
     }

      //fld->fctdg( iqs,iqe, a );
      fld->fctdg( iqs,iqe, lhsa );

    //  #pragma acc exit data copyout ( iprq0[0:nprq],iprq1[0:nprq])
    //  for( ig=0;ig<ng;ig++ )
    // {
    //     #pragma acc exit data copyout(sxb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],slhsb[ig][0:nlhs*nbb[ig]], siqb[ig][0:nbb[ig]], \
    //                                   swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]],sauxfb[ig][0:nauxf*nbb[ig]])
    // }
    // #pragma acc exit data copyout (sqprd[0:nv*nprq],sauxprd[0:naux*nprq],slhsprd[0:nlhs*nprq], \
    //                                swnprd[0:(nx+1)*nprq],swxdprd[0:nprq],sauxfprd[0:nauxf*nprq])
    // #pragma acc exit data copyout (sifq[0:2*nfc],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],slhsa[0:nlhs*nq],\
    //                                swnc[0:(nx+1)*nfc],swxdc[0:nfc],sauxf[0:nauxf*nfc])
    // #pragma acc exit data copyout (sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq])
    // #pragma acc exit data copyout ( this)
    //  exit_acc_device();
  }
