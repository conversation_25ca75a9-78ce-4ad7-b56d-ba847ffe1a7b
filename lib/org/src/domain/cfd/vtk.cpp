   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::vtk()
  {
     Int iv, iek, jp, ie,ip, iq, ix, je;
     Int ig, ick, ib;
     Int ip0, ip1, ip2, ip3;
     Real *tmpxp[3];
     Int *hlp[2];
     Real *qr[20];
     Real p, a, umag, mach, r, rho, ht, cp, tt, pt, s;
     Int tmpnv, *ipq[1];

//output
     string sdum;
     if(unst)
    {
        Int tci;
        tci = tm/dtm;
        if(tci<10)                                sdum = "00000" + strc(tci);
        else if(tci<100     &&  tci>=10)          sdum = "0000" + strc(tci);
        else if(tci<1000    &&  tci>=100)         sdum = "000" + strc(tci);
        else if(tci<10000   &&  tci>=1000)        sdum = "00" + strc(tci);
        else if(tci<100000  &&  tci>=10000)       sdum = "0" + strc(tci);
        else if(tci<1000000 &&  tci>=100000)      sdum = strc(tci);
    }
     else
    {
        sdum = "0";
    }
 
     string fname;
     //fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+".cell.tec";
     fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+"." + sdum +".cell.vtk";
     cout << "save tecplot files to " << fname << "\n";
 
     for(ix=0; ix<nx; ix++)
    {
       tmpxp[ix] = new Real [np];
    }
     for(ix=0; ix<nx; ix++)
    {
       for(ip=0; ip<np; ip++)
      {
         tmpxp[ix][ip] = xp(ix,ip);
      }
    }
     cout << "tm " << tm << " omega " << omega << " " << 2*tm*omega*180/pi2 <<"\n";
   //coo->toffset( 0,np, 1, (tm-dtm)*omega, tmpxp );
   //coo->toffset( 0,nq, 1, (tm-dtm)*omega, q );
   //if(nx==2)
  //{
  //    for(ip=0; ip<np; ip++)
  //   {
  //       tmpxp[1][ip] += (tm-dtm)*omega;
  //   }
  //}

     for(iv=0; iv<20; iv++)
    {
        qr[iv] = new Real [nq];
    }
     for(iv=0; iv<nv; iv++)
    {
        for(iq=0; iq<nq; iq++)
       {
           qr[iv][iq] = q(iv,iq);
       }
    }
     ipq[0] = new Int [nq];
     for(iq=0; iq<nq; iq++)
    {
        ipq[0][iq] = iq;
    }

     /*if(!unst)
    {
        //work out quantities for postprocessing

        fld->auxv( 0, nq, q, aux );
        coo->bvel( 0, nq, ipq, xq, q, qr );

        for(iq=0; iq<nq; iq++)
       {
           rho = aux[0][iq];
           a   = aux[2][iq];
           ht  = aux[3][iq];
           cp  = aux[4][iq];
           if(nx==2)
          {
              p   = q[3][iq];

              umag = q[0][iq]*q[0][iq] + q[1][iq]*q[1][iq]; 
              umag = sqrt(umag);
              mach = umag/a;             
 
              tt = ht/cp;
              pt = p*pow((1+0.2*mach*mach), 3.5);
              s  = log(p*1.4) - 1.4*log(rho);
          }
           else if(nx==3)
          {
              p   = q[4][iq];

              r = xq[1][iq]*xq[1][iq] + xq[2][iq]*xq[2][iq];
              r = sqrt(r);

              qr[2][iq] -= omega*r;
              umag = qr[0][iq]*qr[0][iq] + qr[1][iq]*qr[1][iq] + qr[2][iq]*qr[2][iq]; 
              umag = sqrt(umag);
              mach = umag/a;             

              tt = ht/cp;
              pt = p*pow((1+0.2*mach*mach), 3.5);
              s  = log(10000*p*1.4) - 1.4*log(rho);
          }

           tmpnv = nv;
           qr[tmpnv++][iq] = tt;   
           qr[tmpnv++][iq] = pt;   
           qr[tmpnv++][iq] = mach;
           qr[tmpnv++][iq] = s;
       }
    }
     else*/
    {
       tmpnv = nv;
    }

     ofstream fle( fname.c_str() );
     fle.setf(ios_base::scientific);
     fle.precision(15);

     fle << "# vtk DataFile Version 2.0\n";
     fle << fname << "\n";
     fle << "ASCII\n";
     fle << "DATASET UNSTRUCTURED_GRID\n";

     fle << "POINTS " << np << " " << "float\n";
     for(ip=0; ip<np; ip++)
    {
       //fle << x[0][ip] << " " << x[1][ip] << " " << x[2][ip] << "\n";
       if(nx==2)
      {
         fle << tmpxp[0][ip] << " " << tmpxp[1][ip] << " " << 0 << "\n";
      }
       else if(nx==3)
      {
         fle << tmpxp[0][ip] << " " << tmpxp[1][ip] << " " << tmpxp[2][ip] << "\n";
      }
    }
     fle << "\n";
  
     Int tmpne[2];
     tmpne[0]=0;
     tmpne[1]=0;
     hlp[0] = new Int [nq];
     hlp[1] = new Int [nq];
     for(iek=0; iek<nek; iek++)
    {
       if(ne[iek]>0)
      {
         for(ie=0; ie<ne[iek]; ie++)
        {
           hlp[0][tmpne[0]] = iek;
           hlp[1][tmpne[0]] = ie;

           tmpne[0]++;
           tmpne[1] += nep[iek] + 1;
        }
      }
    }
  
     fle << "CELLS " << tmpne[0] << " " <<  tmpne[1] << "\n";

     if(nx==3 && nek==4)
    {
        //special treatment for 3D FDNEUT mesh
        //0: hex
        //1: prism
        //2: tet
        //3: pyramid
        for(iek=0; iek<nek; iek++)
       {
          if(ne[iek]==0) continue;
          for(ie=0; ie<ne[iek]; ie++)
         {
             if(iek!=1)
            {
                fle << nep[iek] << " ";
                for(ip=0; ip<nep[iek]; ip++)
               {
                  fle << iep[iek][ip][ie] << " ";
               }
                fle << "\n";
            }
             else
            {
                //strange orientation of prism for vtk
                fle << 6 << " " << iep[iek][0][ie] << " " << iep[iek][2][ie] << " " << iep[iek][1][ie]
                         << " " << iep[iek][3][ie] << " " << iep[iek][5][ie] << " " << iep[iek][4][ie] << "\n";
            }
         }
       }
        fle << "\n";
   
        fle << "CELL_TYPES " << tmpne[0] << "\n";
        for(ie=0; ie<tmpne[0]; ie++)
       {
          iek = hlp[0][ie];
          if(iek==0)
         {
            //hex
            fle << 12 << "\n";
         }
          else if(iek==1)
         {
            //quad
            fle << 13 << "\n";
         }
          else if(iek==2)
         {
            //tet
            fle << 10 << "\n";
         }
          else if(iek==3)
         {
            //pyramid
            fle << 14 << "\n";
         }
       }
        fle << "\n"; 
    }
     else
    {
        for(iek=0; iek<6; iek++)
       {
          if(ne[iek]==0) continue;
          for(ie=0; ie<ne[iek]; ie++)
         {
            if(iek!=4)
           {
              fle << nep[iek] << " ";
              for(ip=0; ip<nep[iek]; ip++)
             {
                fle << iep[iek][ip][ie] << " ";
             }
              fle << "\n";
           }
            else
           {
              //strange orientation of prism for vtk
              fle << 6 << " " << iep[iek][0][ie] << " " << iep[iek][2][ie] << " " << iep[iek][1][ie]
                       << " " << iep[iek][3][ie] << " " << iep[iek][5][ie] << " " << iep[iek][4][ie] << "\n";
           }
         }
       }
        fle << "\n";
   
        fle << "CELL_TYPES " << tmpne[0] << "\n";
        for(ie=0; ie<tmpne[0]; ie++)
       {
          iek = hlp[0][ie];
          if(iek==0)
         {
            //triangle
            fle << 5 << "\n";
         }
          else if(iek==1)
         {
            //quad
            fle << 9 << "\n";
         }
          else if(iek==2)
         {
            //tet
            fle << 10 << "\n";
         }
          else if(iek==3)
         {
            //pyramid
            fle << 14 << "\n";
         }
          else if(iek==4)
         {
            //prism
            fle << 13 << "\n";
         }
          else if(iek==5)
         {
            //hex
            fle << 12 << "\n";
         }
       }
        fle << "\n"; 
    }

     fle << "CELL_DATA " <<  tmpne[0] << "\n";
     for(iv=0; iv<tmpnv; iv++)
    {
       fle << "SCALARS V" << str(iv) << " float 1\n";
       fle << "LOOKUP_TABLE default\n";
       for(ie=0; ie<tmpne[0]; ie++)
      {
         iek = hlp[0][ie];
         je  = hlp[1][ie];
         iq = ieq[iek][0][je];
         qr[iv][iq] = lower_bound_by_mag(qr[iv][iq], small);
         fle << qr[iv][iq] << "\n";
      }
    }
     fle.close();

     for(ix=0; ix<nx; ix++)
    {
       delete[] tmpxp[ix];
       tmpxp[ix]=NULL;
    }
     delete[] hlp[0]; hlp[0]=NULL;
     delete[] hlp[1]; hlp[1]=NULL;

     for(iv=0; iv<20; iv++)
    {
        delete[] qr[iv]; qr[iv]=NULL;
    }
     delete[] ipq[0]; ipq[0]=NULL;
  }

   void cFdDomain::vtk(Int nvar, Real *var[], string fnm)
  {
     Int iv, iek, jp, ie,ip, iq, ix, je;
     Int ig, ick, ib;
     Int ip0, ip1, ip2, ip3;
     Real *tmpxp[3];
     Int *hlp[2];
     Int tmpnv, *ipq[1];

//output
     string sdum;
     sdum = "0";
 
     string fname;
     //fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+".cell.tec";
     fname= dev->getcpath()+"/"+fnm;
     cout << "save tecplot files to " << fname << "\n";
 
     for(ix=0; ix<nx; ix++)
    {
       tmpxp[ix] = new Real [np];
    }
     for(ix=0; ix<nx; ix++)
    {
       for(ip=0; ip<np; ip++)
      {
         tmpxp[ix][ip] = xp(ix,ip);
      }
    }
     cout << "tm " << tm << " omega " << omega << " " << 2*tm*omega*180/pi2 <<"\n";
   //coo->toffset( 0,np, 1, (tm-dtm)*omega, tmpxp );
   //coo->toffset( 0,nq, 1, (tm-dtm)*omega, q );
   //if(nx==2)
  //{
  //    for(ip=0; ip<np; ip++)
  //   {
  //       tmpxp[1][ip] += (tm-dtm)*omega;
  //   }
  //}

     ofstream fle( fname.c_str() );
     fle.setf(ios_base::scientific);
     fle.precision(15);

     fle << "# vtk DataFile Version 2.0\n";
     fle << fname << "\n";
     fle << "ASCII\n";
     fle << "DATASET UNSTRUCTURED_GRID\n";

     fle << "POINTS " << np << " " << "float\n";
     for(ip=0; ip<np; ip++)
    {
       //fle << x[0][ip] << " " << x[1][ip] << " " << x[2][ip] << "\n";
       if(nx==2)
      {
         fle << tmpxp[0][ip] << " " << tmpxp[1][ip] << " " << 0 << "\n";
      }
       else if(nx==3)
      {
         fle << tmpxp[0][ip] << " " << tmpxp[1][ip] << " " << tmpxp[2][ip] << "\n";
      }
    }
     fle << "\n";
  
     Int tmpne[2];
     tmpne[0]=0;
     tmpne[1]=0;
     hlp[0] = new Int [nq];
     hlp[1] = new Int [nq];
     for(iek=0; iek<nek; iek++)
    {
       if(ne[iek]>0)
      {
         for(ie=0; ie<ne[iek]; ie++)
        {
           hlp[0][tmpne[0]] = iek;
           hlp[1][tmpne[0]] = ie;

           tmpne[0]++;
           tmpne[1] += nep[iek] + 1;
        }
      }
    }
  
     fle << "CELLS " << tmpne[0] << " " <<  tmpne[1] << "\n";

     if(nx==3 && nek==3)
    {
        //special treatment for 3D FDNEUT mesh
        //0: hex
        //1: prism
        //2: tet
        for(iek=0; iek<nek; iek++)
       {
          if(ne[iek]==0) continue;
          for(ie=0; ie<ne[iek]; ie++)
         {
             if(iek!=1)
            {
                fle << nep[iek] << " ";
                for(ip=0; ip<nep[iek]; ip++)
               {
                  fle << iep[iek][ip][ie] << " ";
               }
                fle << "\n";
            }
             else
            {
                //strange orientation of prism for vtk
                fle << 6 << " " << iep[iek][0][ie] << " " << iep[iek][2][ie] << " " << iep[iek][1][ie]
                         << " " << iep[iek][3][ie] << " " << iep[iek][5][ie] << " " << iep[iek][4][ie] << "\n";
            }
         }
       }
        fle << "\n";
   
        fle << "CELL_TYPES " << tmpne[0] << "\n";
        for(ie=0; ie<tmpne[0]; ie++)
       {
          iek = hlp[0][ie];
          if(iek==0)
         {
            //hex
            fle << 12 << "\n";
         }
          else if(iek==1)
         {
            //quad
            fle << 13 << "\n";
         }
          else if(iek==2)
         {
            //tet
            fle << 10 << "\n";
         }
       }
        fle << "\n"; 
    }
     else
    {
        for(iek=0; iek<6; iek++)
       {
          if(ne[iek]==0) continue;
          for(ie=0; ie<ne[iek]; ie++)
         {
            if(iek!=4)
           {
              fle << nep[iek] << " ";
              for(ip=0; ip<nep[iek]; ip++)
             {
                fle << iep[iek][ip][ie] << " ";
             }
              fle << "\n";
           }
            else
           {
              //strange orientation of prism for vtk
              fle << 6 << " " << iep[iek][0][ie] << " " << iep[iek][2][ie] << " " << iep[iek][1][ie]
                       << " " << iep[iek][3][ie] << " " << iep[iek][5][ie] << " " << iep[iek][4][ie] << "\n";
           }
         }
       }
        fle << "\n";
   
        fle << "CELL_TYPES " << tmpne[0] << "\n";
        for(ie=0; ie<tmpne[0]; ie++)
       {
          iek = hlp[0][ie];
          if(iek==0)
         {
            //triangle
            fle << 5 << "\n";
         }
          else if(iek==1)
         {
            //quad
            fle << 9 << "\n";
         }
          else if(iek==2)
         {
            //tet
            fle << 10 << "\n";
         }
          else if(iek==3)
         {
            //pyramid
            fle << 14 << "\n";
         }
          else if(iek==4)
         {
            //prism
            fle << 13 << "\n";
         }
          else if(iek==5)
         {
            //hex
            fle << 12 << "\n";
         }
       }
        fle << "\n"; 
    }

     fle << "CELL_DATA " <<  tmpne[0] << "\n";
     for(iv=0; iv<nvar; iv++)
    {
       fle << "SCALARS V" << str(iv) << " float 1\n";
       fle << "LOOKUP_TABLE default\n";
       for(ie=0; ie<tmpne[0]; ie++)
      {
         iek = hlp[0][ie];
         je  = hlp[1][ie];
         iq = ieq[iek][0][je];
         fle << var[iv][iq] << "\n";
      }
    }
     fle.close();

     for(ix=0; ix<nx; ix++)
    {
       delete[] tmpxp[ix];
       tmpxp[ix]=NULL;
    }
     delete[] hlp[0]; hlp[0]=NULL;
     delete[] hlp[1]; hlp[1]=NULL;

  }
