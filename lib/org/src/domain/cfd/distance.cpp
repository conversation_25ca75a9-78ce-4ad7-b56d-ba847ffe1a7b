   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::distance()
  {
      Int ig,ix,ib,iq,jb;

      Int nw;
      Real *sxw,*xw[3];
      Int *ixw[2];
      Int *jdst;
      cKdTree *kdt;
      Real x[3];
      Int  imin;
      Real d;

      if( vsc->viscous() )
     {

// collect the boundary coordinates
         if( sdst )
        {
            cout <<" CHECK WHERE DST IS ALLOCATED\n";
            exit(0);
        }
         else
        {
            sdst= new Real[2*nq];
            dst.subv( 2,nq, sdst );
            //setv( (Int)0,nq, ZERO,dst[0] );
            //setv( (Int)0,nq, ZERO,dst[1] );
            for(iq=0; iq<nq; iq++)
           {
               dst(0,iq) = 0;
               dst(1,iq) = 0;
           }
        } 

         cout << "THIS IS A VISCOUS CASE\n";
         jdst= new Int[nq];
         nw=0; 
         for( ig=0;ig<ng;ig++ )
        {
            if( bbj[ig]->gettype() == visc_fbndry )
           {
               nw+= nbb[ig]; 
           }
        }

         if( nw > 0 )
        {
            sxw= new Real[nx*nw];
            ixw[0]= new Int[nw];
            ixw[1]= new Int[nw];
            subv( nx,nw, sxw,xw );
            ib=0;
            for( ig=0;ig<ng;ig++ )
           {
               if( bbj[ig]->gettype() == visc_fbndry )
              {
                  for( jb=0;jb<nbb[ig];jb++ )
                 {
                     for( ix=0;ix<nx;ix++ )
                    {
                        xw[ix][ib]= xb[ig](ix,jb);
                    } 
                     ixw[0][ib]= jb;
                     ixw[1][ib]= ig;
                     ib++;
                 }
              }
           }
 

            if( nw > 1 )
           {
               kdt= new cKdTree();
               kdt->build( nx,nw,xw );

               for( iq=0;iq<nq;iq++ )
              {
                  line( iq, nx,xq, x );
                  kdt->nearest( x,&imin,&d );
                  dst(0,iq)= d;
                  dst(1,iq)= 0.;
                  jdst[iq]= imin;
              }
               delete   kdt;
           }
            else
           {
               for( iq=0;iq<nq;iq++ )
              {
                  line( iq, nx,xq, x );
                  jdst[iq]= 0;
                  cout << "CRAP NUMBERS IN DST\n";
                  exit(0);
              }

           }
   
            for( iq=0;iq<nq;iq++ )
           {
               ib= jdst[iq];
               ig= ixw[1][ib]; 
               ndst[ig]++;
           }

            for( ig=0;ig<ng;ig++ )
           {
               sibdst[ig]= new Int[ndst[ig]]; ibdst[ig].subv( 1,ndst[ig], sibdst[ig] );
               siqdst[ig]= new Int[2*ndst[ig]]; iqdst[ig].subv( 2,ndst[ig], siqdst[ig] );
               ndst[ig]=0;
           }

            for( iq=0;iq<nq;iq++ )
           {
               ib= jdst[iq];
               jb= ixw[0][ib]; 
               ig= ixw[1][ib]; 
               ib= ndst[ig];
               ibdst[ig](0,ib)= jb;
               iqdst[ig](0,ib)= iq;
               iqdst[ig](1,ib)= iqb[ig][0][jb];

               ndst[ig]++;
           }


            delete[] ixw[0];
            delete[] ixw[1];
            delete[] sxw;

        }
         delete[] jdst;

     }
  }
