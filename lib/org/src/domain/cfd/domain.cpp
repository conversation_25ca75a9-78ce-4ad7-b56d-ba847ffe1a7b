   using namespace std;

#  include <domain/cfd/domain.h>
#  include <fstream>

   cFdDomain::cFdDomain()
  {
      Int   ig,iek,ibk;

      cfl=     0.;
      niter=   5;
      npre=    2;
      npost=   2;
      nstep=  10;
      nout=nstep;
      cfl0=  0.5; 
      cfl1=  10.; 
      dcfl= 1.02;
      lmixmax = 0.005;
      nexc = 0;

      ntlv=    0;
      unst=false;
      tm=      0;
      dtm=   big;

      spatial_order = 1;

      naux= 0;
      nlhs= 0;
      nauxf=0;

      fld= NULL;
      vsc= NULL;
      bbj= NULL;

      setv( (Int)0,(Int)MxNSk,(Int*)NULL,nce );
      setv( (Int)0,(Int)MxNSk,(Int***)NULL,icep );
      setv( (Int)0,(Int)MxNSk,(Int***)NULL,iceq );

      dof=  NULL;
      pts=  NULL;
      prp=  NULL;
      prd=  NULL;
      setv( (Int)0,(Int)MxNSk,(cPdata*)NULL,pld );

      nfre = 0;
      nstep_z=0;
      for(Int ifre=0; ifre<MXNFRE; ifre++)
     {
         freq0[ifre] = 0;
         ibpa[ifre] = 0;
     }

      for( iek=0;iek<MxNSk;iek++ )
     {
         eld[iek]= NULL;
     }
      for( ig=0;ig<MxNBG;ig++ )
     {
         bdf[ig]= NULL;
         dsd[ig]= NULL;
         for( ibk=0;ibk<MxNSk;ibk++ )
        {
            bld[ig][ibk]= NULL;
        }
     }
      cnf= NULL;
      for( ibk=0;ibk<MxNSk;ibk++ )
     {
         cnd[ibk]= NULL;
     }

      elm= NULL;
      blm= NULL;

      setv( (Int)0,(Int)MxNBG, (Int*)NULL,siqb );
//      setv( (Int)0,(Int)MxNBG, (Int**)NULL,iqb );

      setv( (Int)0,(Int)MxNBG, (Real*)NULL,sqb );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,sdqb );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,sqb0 );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,srhsb );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,sresb );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,slhsb );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,sauxb );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,sdauxb );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,sauxb0 );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,sauxfb );

      setv( (Int)0,(Int)MxNBG, (Real*)NULL,sxqb );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,swnb );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,swxdb );

//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,qb );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,dqb );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,qb0);
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,rhsb );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,resb );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,lhsb );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,auxb );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,dauxb );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,auxb0);
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,auxfb );

      setv( (Int)0,(Int)MxNBG, (Real*)NULL,szb_re );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,szb_im );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,szcb_re );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,szcb_im );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,srhsbz_re );
      setv( (Int)0,(Int)MxNBG, (Real*)NULL,srhsbz_im );
      for(Int i=0; i<MXNFRE; i++)
     {
         setv( (Int)0,(Int)MxNBG, (Real*)NULL,szb0_re[i] );
         setv( (Int)0,(Int)MxNBG, (Real*)NULL,szb0_im[i] );
     }

//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,zb_re );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,zb_im );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,zcb_re );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,zcb_im );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,rhsbz_re );
//      setv( (Int)0,(Int)MxNBG, (Real**)NULL,rhsbz_im );
//      for(Int i=0; i<MXNFRE; i++)
//     {
//         setv( (Int)0,(Int)MxNBG, (Real**)NULL,zb0_re[i] );
//         setv( (Int)0,(Int)MxNBG, (Real**)NULL,zb0_im[i] );
//     }

// internal faces
      setv( (Int)0,(Int)MxNSk, (Int)0, nc );
      setv( (Int)0,(Int)MxNSk, (Int*)NULL, sicp );
      setv( (Int)0,(Int)MxNSk, (Int*)NULL, sicc );
      setv( (Int)0,(Int)MxNSk, (Int*)NULL, sicq );
      setv( (Int)0,(Int)MxNSk, (Int**)NULL, icp );
      setv( (Int)0,(Int)MxNSk, (Int**)NULL, icc );
      setv( (Int)0,(Int)MxNSk, (Int**)NULL, icq );
      nfc=0;
      sifq=NULL; //ifq[0]=NULL; ifq[1]=NULL;

// periodic faces
      setv( (Int)0,(Int)MxNSk, (Int*)NULL, siprc );
      setv( (Int)0,(Int)MxNSk, (Int**)NULL, iprc );
      setv( (Int)0,(Int)MxNSk, (Int*)NULL, siprdp );
      setv( (Int)0,(Int)MxNSk, (Int**)NULL, iprdp );
      swnprd=   NULL; //wnprd=   NULL;
      swxdprd=  NULL; //wxdprd=  NULL;
      sauxfprd= NULL; //auxfprd= NULL;
      sxprd=    NULL; //xprd=    NULL;
      sxqprd=   NULL; //xqprd=   NULL;
      sqprd=    NULL; //qprd=    NULL;
      sauxprd=  NULL; //auxprd=  NULL;
      sdqprd=   NULL; //dqprd=   NULL;
      srhsprd=  NULL; //rhsprd=  NULL;
      sresprd=  NULL; //resprd=  NULL;
      slhsprd=  NULL; //lhsprd=  NULL;
      sdauxprd=  NULL; //dauxprd=  NULL;
      szprd_re=NULL; //zprd_re=NULL;
      szprd_im=NULL; //zprd_im=NULL;
      szcprd_re=NULL; //zcprd_re=NULL;
      szcprd_im=NULL; //zcprd_im=NULL;
      srhszprd_re=NULL; //rhszprd_re=NULL;
      srhszprd_im=NULL; //rhszprd_im=NULL;

// point arrays

      sxdp=NULL;
      for(ig=0; ig<MxNBG; ig++)
     {
        sxbp[ig]=NULL;
        sxdbp[ig]=NULL;
        //xbp[ig][0] = NULL;
        //xbp[ig][1] = NULL;
        //xbp[ig][2] = NULL;
        //xdbp[ig][0] = NULL;
        //xdbp[ig][1] = NULL;
        //xdbp[ig][2] = NULL;
     }

// solution arrays

      sq=NULL;//q=NULL;
      sdq=NULL;//dq=NULL;
      setv( 0,NTLEV, (Real*)NULL, su );
      //setv( 0,NTLEV, (Real**)NULL, u );
      saux=NULL;//aux=NULL;
      sdaux=NULL;//daux=NULL;
      srhs=NULL;//rhs=NULL;
      sres=NULL;//res=NULL;
      slhsa=NULL;//lhsa=NULL;
      smgrhs=NULL; //mgrhs=NULL;
      swrkq=NULL; //wrkq=NULL;
      swq=NULL;
      sxdq=NULL;
      sdxdx= NULL; //dxdx= NULL;
      sdqdx= NULL; //dqdx= NULL;
      sdxdxprd= NULL; //dxdxprd= NULL;
      sdqdxprd= NULL; //dqdxprd= NULL;

      setv( 0,MXNFRE, (Real*)NULL, sz_re );
      setv( 0,MXNFRE, (Real*)NULL, sz_im );
      //setv( 0,MXNFRE, (Real**)NULL, z_re );
      //setv( 0,MXNFRE, (Real**)NULL, z_im );
      setv( 0,MXNFRE, (Real*)NULL, szc_re );
      setv( 0,MXNFRE, (Real*)NULL, szc_im );
      //setv( 0,MXNFRE, (Real**)NULL, zc_re );
      //setv( 0,MXNFRE, (Real**)NULL, zc_im );

      srhsz_re=NULL; //rhsz_re=NULL;
      srhsz_im=NULL; //rhsz_im=NULL;
      sdzdx_re=NULL; //dzdx_re=NULL;
      sdzdx_im=NULL; //dzdx_im=NULL;
      sdzdxprd_re=NULL; //dzdxprd_re=NULL;
      sdzdxprd_im=NULL; //dzdxprd_im=NULL;
      sauxz_re=NULL; //auxz_re=NULL;
      sauxz_im=NULL; //auxz_im=NULL;
      szcold_re=NULL; //zcold_re=NULL;
      szcold_im=NULL; //zcold_im=NULL;

      ijdx[0]= NULL;
      ijdx[1]= NULL;
      ijdx[2]= NULL;

// connection arrays

      sauxf= NULL;
      //auxf= NULL;

      swnc= NULL;
      swxdc= NULL;
      sxc= NULL;

      sdst= NULL;
      setv( (Int)0,(Int)MxNBG,(Int)0, ndst );
      setv( (Int)0,(Int)MxNBG,(Int*)NULL, sibdst );
      setv( (Int)0,(Int)MxNBG,(Int*)NULL, siqdst );

      setv( 0,MxNBG,0,nbl);
      setv( 0,MxNBG,(Int*)NULL,ibpl);
      setv( 0,MxNBG,(Int*)NULL,ibql);

/*    setv( 0,MxNBG,(Real*)NULL,sxbvg );
      setv( 0,MxNBG,(Real*)NULL,sqbvg );
      setv( 0,MxNBG,(Real*)NULL,swbvg );
      setv( 0,MxNBG,(Real**)NULL,xbvg );
      setv( 0,MxNBG,(Real**)NULL,qbvg );
      setv( 0,MxNBG,(Real**)NULL,wbvg );*/

      bprobed = false;
      nprobe=0;
      for(Int i=0; i<20; i++)
     {
         nprobep[i] = 0;
         probep[i][0] = NULL;
         probep[i][1] = NULL;
         probep[i][2] = NULL;
         probep[i][3] = NULL;
         probeq[i][0]=NULL;
         probeq[i][1]=NULL;
         probeq[i][2]=NULL;
         bslice[i] = false;
     }

      sqmin = NULL;
      sqmax = NULL;
      svklim = NULL;
      //qmin = NULL;
      //qmax = NULL;
      //vklim = NULL;

      sqface[0] = NULL;
      sqface[1] = NULL;
      //qface[0] = NULL;
      //qface[1] = NULL;
      sqprdface[0] = NULL;
      sqprdface[1] = NULL;
      //qprdface[0] = NULL;
      //qprdface[1] = NULL;

      limtype = 0;
      limfac = 10;

      lhs_jac = NULL;
      jac_df[0] = NULL;
      jac_df[1] = NULL;
      jac_df_prd[0] = NULL;
      jac_df_prd[1] = NULL;
      jac_inv_prd[0] = NULL;
      jac_inv_prd[1] = NULL;
      jac_vis_prd[0] = NULL;
      jac_vis_prd[1] = NULL;
      scsv=NULL;//csv=NULL;

# ifdef PETSC
      ilocal=NULL;
      ighost_local=NULL;
      ighost=NULL;

      petsc_csv=NULL;
      petsc_dcsv=NULL;
      petsc_rhs=NULL;
      petsc_baserhs=NULL;
      petsc_A_mf=NULL;
      petsc_A_pre=NULL;
      petsc_ksp=NULL;

      petsc_dcsv_z=NULL;
      petsc_rhs_z=NULL;
      petsc_ksp_z=NULL;
      petsc_A_pre_copy=NULL;

      init_residual = big;
      for(Int ifre=0; ifre<MXNFRE; ifre++)
     {
         init_residual_lin[ifre] = big; 
         gmres_glb_z[ifre] = true;
     }
      gmres_glb = true;
# endif

      acc_started = false;
  }

   cFdDomain::~cFdDomain()
  {
      Int   iv,is,ig,iq,iek,ibk,ip,il;

      if(acc_started) exit_acc_device();

      delete fld; fld= NULL;
      delete vsc; vsc= NULL;
      if(bbj)
     {
         for( ig=0;ig<ng;ig++ )
        {
            delete bbj[ig];
        }
         delete[] bbj; bbj=NULL;
     }

// internal faces
      for( ibk=0;ibk<nbk;ibk++ )
     {
         delete[] sicp[ibk]; sicp[ibk]= NULL; 
         delete[] sicc[ibk]; sicc[ibk]= NULL; 
         delete[] sicq[ibk]; sicq[ibk]= NULL; 
         delete[]  icp[ibk];  icp[ibk]= NULL;
         delete[]  icc[ibk];  icc[ibk]= NULL;
         delete[]  icq[ibk];  icq[ibk]= NULL;
         nc[ibk]= 0;
     }
      delete[] sifq; sifq= NULL; //ifq[0]=NULL; ifq[1]=NULL;
      nfc=0;

// periodic faces
      for( ibk=0;ibk<nbk;ibk++ )
     {
         delete[] siprc[ibk]; siprc[ibk]= NULL; 
         delete[]  iprc[ibk];  iprc[ibk]= NULL; 
     }
      for( ibk=0;ibk<nbk;ibk++ )
     {
         delete[] siprdp[ibk]; siprdp[ibk]= NULL; 
         delete[]  iprdp[ibk];  iprdp[ibk]= NULL; 
     }
      delete[] swnprd;  swnprd=   NULL; //delete[] wnprd;   wnprd=   NULL;
      delete[] swxdprd; swxdprd=  NULL; //delete[] wxdprd;  wxdprd=  NULL;
      delete[] sauxfprd;sauxfprd= NULL; //delete[] auxfprd; auxfprd= NULL;
      delete[] sxprd;   sxprd=    NULL; //delete[] xprd;    xprd=    NULL;
      delete[] sxqprd;  sxqprd=   NULL; //delete[] xqprd;   NULL;xqprd=   NULL;
      delete[] sqprd;   sqprd=    NULL; //delete[] qprd;    NULL;qprd=    NULL;
      delete[] sauxprd; sauxprd=  NULL; //delete[] auxprd;  NULL;auxprd=  NULL;
      delete[] sdqprd;  sdqprd=   NULL; //delete[] dqprd;   NULL;dqprd=   NULL;
      delete[] srhsprd; srhsprd=  NULL; //delete[] rhsprd;  NULL;rhsprd=  NULL;
      delete[] sresprd; sresprd=  NULL; //delete[] resprd;  NULL;resprd=  NULL;
      delete[] slhsprd; slhsprd=  NULL; //delete[] lhsprd;  NULL;lhsprd=  NULL;
      delete[] sdauxprd; sdauxprd=  NULL; //delete[] dauxprd;  NULL;dauxprd=  NULL;
      delete[] szprd_re; szprd_re= NULL; //delete[] zprd_re; zprd_re=NULL;
      delete[] szprd_im; szprd_im= NULL; //delete[] zprd_im; zprd_im=NULL;
      delete[] szcprd_re; szcprd_re= NULL; //delete[] zcprd_re; zcprd_re=NULL;
      delete[] szcprd_im; szcprd_im= NULL; //delete[] zcprd_im; zcprd_im=NULL;
      delete[] srhszprd_re; srhszprd_re= NULL; //delete[] rhszprd_re; rhszprd_re=NULL;
      delete[] srhszprd_im; srhszprd_im= NULL; //delete[] rhszprd_im; rhszprd_im=NULL;
 
      for( ibk=0;ibk<nbk;ibk++ )
     {
         delete[] nce[ibk];
         delete[] icep[ibk];
         delete[] iceq[ibk];
     }
      dev=  NULL;


      delete dof; dof=  NULL;
      delete pts; pts=  NULL;
      delete prp; prp=  NULL;
      delete prd; prd=  NULL;
      for( ibk=0;ibk<nbk;ibk++ )
     {
         delete pld[ibk]; pld[ibk]= NULL;
     }

      for( iek=0;iek<nek;iek++ )
     {
         delete eld[iek]; eld[iek]= NULL;
     }

      for( ig=0;ig<ng;ig++ )
     {
         delete bdf[ig]; bdf[ig]= NULL;
         delete dsd[ig]; bdf[ig]= NULL;

         delete[] sxqb[ig];  sxqb[ig]=  NULL;
         delete[] swnb[ig];  swnb[ig]=  NULL;
         delete[] swxdb[ig]; swxdb[ig]= NULL;

         delete[] siqb[ig]; siqb[ig]= NULL; //delete[] iqb[ig]; iqb[ig]= NULL;

         delete[] sqb[ig]; sqb[ig]= NULL; //delete[] qb[ig]; qb[ig]=NULL;
         delete[] sdqb[ig]; sdqb[ig]= NULL; //delete[] dqb[ig]; dqb[ig]=NULL;
         delete[] sqb0[ig]; sqb0[ig]= NULL; //delete[] qb0[ig]; qb0[ig]=NULL;

         delete[] sauxb[ig]; sauxb[ig]= NULL; //delete[] auxb[ig]; auxb[ig]=NULL;
         delete[] sdauxb[ig]; sdauxb[ig]= NULL; //delete[] dauxb[ig]; dauxb[ig]=NULL;
         delete[] sauxb0[ig]; sauxb0[ig]= NULL; //delete[] auxb0[ig]; auxb0[ig]=NULL;

         delete[] sauxfb[ig]; sauxfb[ig]= NULL; //delete[] auxfb[ig]; auxfb[ig]=NULL;

         delete[] srhsb[ig]; srhsb[ig]= NULL; //delete[] rhsb[ig]; rhsb[ig]=NULL;
         delete[] sresb[ig]; sresb[ig]= NULL; //delete[] resb[ig]; resb[ig]=NULL;
         delete[] slhsb[ig]; slhsb[ig]= NULL; //delete[] lhsb[ig]; lhsb[ig]=NULL;

         for( ibk=0;ibk<nbk;ibk++ )
        {
            delete bld[ig][ibk]; bld[ig][ibk]= NULL;
        }

         delete[] ibpl[ig]; ibpl[ig]=NULL;
         delete[] ibql[ig]; ibpl[ig]=NULL;
/*       delete[] sxbvg[ig]; sxbvg[ig]=NULL; 
         delete[] sqbvg[ig]; sqbvg[ig]=NULL; 
         delete[] swbvg[ig]; swbvg[ig]=NULL; 

         delete[] xbvg[ig]; xbvg[ig]=NULL; 
         delete[] qbvg[ig]; qbvg[ig]=NULL; 
         delete[] wbvg[ig]; wbvg[ig]=NULL; */
     }
      setv( 0,MxNBG,0,nbl );

      for(ig=0; ig<ng; ig++)
     {
         for( Int i=0; i<nfre; i++ )
        {
            delete[] szb0_re[i][ig]; szb0_re[i][ig]= NULL; //delete[] zb0_re[i][ig]; zb0_re[i][ig]=NULL;
            delete[] szb0_im[i][ig]; szb0_im[i][ig]= NULL; //delete[] zb0_im[i][ig]; zb0_im[i][ig]=NULL;
        }
         delete[] szb_re[ig];    szb_re[ig]= NULL;    //delete[] zb_re[ig];   zb_re[ig]=NULL;
         delete[] szcb_re[ig];   szcb_re[ig]= NULL;   //delete[] zcb_re[ig];  zcb_re[ig]=NULL;
         delete[] srhsbz_re[ig]; srhsbz_re[ig]= NULL; //delete[] rhsbz_re[ig]; rhsbz_re[ig]=NULL;
   
         delete[] szb_im[ig];    szb_im[ig]= NULL;    //delete[] zb_im[ig];   zb_im[ig]=NULL;
         delete[] szcb_im[ig];   szcb_im[ig]= NULL;   //delete[] zcb_im[ig];  zcb_im[ig]=NULL;
         delete[] srhsbz_im[ig]; srhsbz_im[ig]= NULL; //delete[] rhsbz_im[ig]; rhsbz_im[ig]=NULL;
     }

      delete[] sauxf; sauxf= NULL; //delete[] auxf; auxf=NULL;
      delete cnf; cnf= NULL;
      for( ibk=0;ibk<MxNSk;ibk++ )
     {
         delete cnd[ibk]; cnd[ibk]= NULL;
     }


      for( iek=0;iek<nek;iek++ )
     {
         delete elm[iek]; elm[iek]=NULL;
     }
      for( ibk=0;ibk<nbk;ibk++ )
     {
         delete blm[ibk]; blm[ibk]=NULL;
         for( ig=0;ig<ng;ig++ )
        {
            nb[ig][ibk]=0;
        }
     }
      delete[] elm; elm=NULL;
      delete[] blm; blm=NULL;


      delete[] sxdp; sxdp= NULL; 

      for(ig=0; ig<ng; ig++)
     {
        delete[] sxbp[ig];    sxbp[ig]=NULL;
        delete[] sxdbp[ig];   sxdbp[ig]=NULL;
     }

      delete[] sq; sq= NULL; //delete[] q; q=NULL;
      for( Int i=0;i<ntlv;i++ )
     {
         delete[] su[i]; su[i]= NULL; 
         //delete[]  u[i];  u[i]= NULL;
     }
      delete[] sdq; sdq= NULL; //delete[] dq; dq=NULL;
      delete[] saux; saux= NULL; //delete[] aux; aux=NULL;
      delete[] sdaux; sdaux= NULL; //delete[] daux; daux=NULL;
      delete[] srhs; srhs= NULL; //delete[] rhs; rhs=NULL;
      delete[] sres; sres= NULL; //delete[] res; res=NULL;
      delete[] slhsa; slhsa= NULL; //delete[] lhsa; lhsa=NULL;
      delete[] swq; swq=NULL;
      delete[] sxdq; sxdq=NULL;
      delete[] swrkq; swrkq=NULL; //delete[] wrkq; wrkq=NULL;
      delete[] smgrhs; smgrhs=NULL; //delete[] mgrhs; mgrhs=NULL;
      delete[] sdxdx; sdxdx=NULL; //delete[] dxdx; dxdx=NULL;
      delete[] sdxdxprd; sdxdxprd=NULL; //delete[] dxdxprd; dxdxprd=NULL;

      for( Int i=0; i<nfre; i++)
     {
         delete[] sz_re[i]; sz_re[i]= NULL; //delete[] z_re[i]; z_re[i]=NULL;
         delete[] sz_im[i]; sz_im[i]= NULL; //delete[] z_im[i]; z_im[i]=NULL;
         delete[] szc_re[i]; szc_re[i]= NULL; //delete[] zc_re[i]; zc_re[i]=NULL;
         delete[] szc_im[i]; szc_im[i]= NULL; //delete[] zc_im[i]; zc_im[i]=NULL;
     }
      delete[] srhsz_re; srhsz_re= NULL; //delete[] rhsz_re; rhsz_re=NULL;
      delete[] srhsz_im; srhsz_im= NULL; //delete[] rhsz_im; rhsz_im=NULL;
      delete[] sauxz_re; sauxz_re= NULL; //delete[] auxz_re; auxz_re=NULL;
      delete[] sauxz_im; sauxz_im= NULL; //delete[] auxz_im; auxz_im=NULL;
      delete[] szcold_re; szcold_re= NULL; //delete[] zcold_re; zcold_re=NULL;
      delete[] szcold_im; szcold_im= NULL; //delete[] zcold_im; zcold_im=NULL;

      if( sdqdx )
     {
         for( iv=0;iv<nv;iv++ )
        {
            //delete[] sdqdx[iv]; sdqdx[iv]=NULL; delete[] dqdx[iv]; dqdx[iv]=NULL;
            //delete[] dqdx[iv]; dqdx[iv]=NULL;

            delete[] sdzdx_re[iv]; sdzdx_re[iv]=NULL; //delete[] dzdx_re[iv]; dzdx_re[iv]=NULL;
            delete[] sdzdx_im[iv]; sdzdx_im[iv]=NULL; //delete[] dzdx_im[iv]; dzdx_im[iv]=NULL;

//          if( ipr0 != -1 )
           {
               //delete[] sdqdxprd[iv]; sdqdxprd[iv]=NULL; delete[] dqdxprd[iv]; dqdxprd[iv]=NULL;
               //delete[] dqdxprd[iv]; dqdxprd[iv]=NULL;
           }

            delete[] sdzdxprd_re[iv]; sdzdxprd_re[iv]=NULL; //delete[] dzdxprd_re[iv]; dzdxprd_re[iv]=NULL;
            delete[] sdzdxprd_im[iv]; sdzdxprd_im[iv]=NULL; //delete[] dzdxprd_im[iv]; dzdxprd_im[iv]=NULL;
        } 
     }
      delete[] sdqdx; sdqdx= NULL; //delete[] dqdx; dqdx= NULL;
      delete[] sdqdxprd; sdqdxprd= NULL; //delete[] dqdxprd; dqdxprd= NULL;
      delete[] sdzdx_re; sdzdx_re= NULL; //delete[] dzdx_re; dzdx_re= NULL;
      delete[] sdzdx_im; sdzdx_im= NULL; //delete[] dzdx_im; dzdx_im= NULL; 
      delete[] sdzdxprd_re; sdzdxprd_re= NULL; //delete[] dzdxprd_re; dzdxprd_re= NULL;
      delete[] sdzdxprd_im; sdzdxprd_im= NULL; //delete[] dzdxprd_im; dzdxprd_im= NULL; 
      
      delete[] ijdx[0]; ijdx[0]= NULL;
      delete[] ijdx[1]; ijdx[1]= NULL;
      delete[] ijdx[2]; ijdx[2]= NULL;

      delete[] swnc;  swnc=  NULL;
      delete[] swxdc; swxdc= NULL;
      delete[] sxc;   sxc=   NULL;


      delete[] sdst; sdst= NULL;
      setv( (Int)0,(Int)MxNBG,(Int)0, ndst );
      for( ig=0;ig<ng;ig++ )
     {
         delete[] sibdst[ig]; sibdst[ig]= NULL;
         delete[] siqdst[ig]; siqdst[ig]= NULL;
     }

      naux= 0;
      nlhs= 0;
      nauxf=0;

      bprobed = false;
      nprobe = 0;
      for(Int i=0; i<20; i++)
     {
         nprobep[i] = 0;
         delete[] probep[i][0]; probep[i][0]=NULL;
         delete[] probep[i][1]; probep[i][1]=NULL;
         delete[] probep[i][2]; probep[i][2]=NULL;
         delete[] probep[i][3]; probep[i][3]=NULL;
         delete[] probeq[i][0]; probeq[i][0]=NULL;
         delete[] probeq[i][1]; probeq[i][1]=NULL;
         delete[] probeq[i][2]; probeq[i][2]=NULL;
         bslice[i] = false;
     }

      delete[] sqmin; sqmin = NULL;
      delete[] sqmax; sqmax = NULL;
      delete[] svklim; svklim = NULL;
      //delete[] qmin; qmin = NULL;
      //delete[] qmax; qmax = NULL;
      //delete[] vklim; vklim = NULL;

      delete[] sqface[0];    sqface[0] = NULL;
      delete[] sqface[1];    sqface[1] = NULL;
      //delete[] qface[0];     qface[0] = NULL;
      //delete[] qface[1];     qface[1] = NULL;
      delete[] sqprdface[0]; sqprdface[0] = NULL;
      delete[] sqprdface[1]; sqprdface[1] = NULL;
      //delete[] qprdface[0];  qprdface[0] = NULL;
      //delete[] qprdface[1];  qprdface[1] = NULL;

      if(lhs_jac)
     {
         for(iq=0; iq<nq; iq++)
        {
            delete[] lhs_jac[iq]; lhs_jac[iq] = NULL;
        }
         delete[] lhs_jac;
     }
      delete[] jac_df[0]; jac_df[0]=NULL;
      delete[] jac_df[1]; jac_df[1]=NULL;
      delete[] jac_df_prd[0]; jac_df_prd[0]=NULL;
      delete[] jac_df_prd[1]; jac_df_prd[1]=NULL;
      delete[] jac_inv_prd[0]; jac_inv_prd[0]=NULL;
      delete[] jac_inv_prd[1]; jac_inv_prd[1]=NULL;
      delete[] jac_vis_prd[0]; jac_vis_prd[0]=NULL;
      delete[] jac_vis_prd[1]; jac_vis_prd[1]=NULL;
      delete[] scsv; scsv= NULL; //delete[] csv; csv=NULL;

//petsc
#ifdef PETSC
//      PetscErrorCode ierr;
//      delete[] ilocal;       ilocal=NULL;
//      delete[] ighost_local; ighost_local=NULL;
//      delete[] ighost;       ighost=NULL;
//      //must destroy these stuff before calling PetscFinalize
//      VecDestroy(&petsc_csv);
//      VecDestroy(&petsc_dcsv);
//      VecDestroy(&petsc_rhs);
//      VecDestroy(&petsc_baserhs);
//      MatDestroy(&petsc_A_mf);
//      MatDestroy(&petsc_A_pre);
//      KSPDestroy(&petsc_ksp);
//
//      VecDestroy(&petsc_dcsv_z);
//      VecDestroy(&petsc_rhs_z);
//      KSPDestroy(&petsc_ksp_z);
//      MatDestroy(&petsc_A_pre_copy);
//      ierr = PetscFinalize();
      petsc_cleanup();
#endif
  }

   void cFdDomain::elements( Int mbk, cFElement **bl, Int mek, cFElement **el )
  {
      size_t len;
      size_t dlen=1;
      Int    iek,ibk;
      Int    nc;
      Int  **ic;
      if( !elm && !blm )
     {

         nbk= mbk;
         blm= new cFElement*[nbk];
         for( ibk=0;ibk<nbk;ibk++ )
        {
            nbp[ibk]= bl[ibk]->getnp();
            nbq[ibk]= bl[ibk]->getnq();
            nbd[ibk]= bl[ibk]->getnd();
            blm[ibk]= bl[ibk];
        }

         nek= mek;
         elm= new cFElement*[nek];
         for( iek=0;iek<nek;iek++ )
        {
            nep[iek]= el[iek]->getnp();
            neq[iek]= el[iek]->getnq();
            elm[iek]= el[iek];
        }
         for( ibk=0;ibk<nbk;ibk++ )
        {
            nce[ibk]= new Int[nek];
            icep[ibk]= new Int**[nek];
            iceq[ibk]= new Int**[nek];
            setv( (Int)0,nek, (Int)0, nce[ibk] );
            setv( (Int)0,nek, (Int**)NULL, icep[ibk] );
            setv( (Int)0,nek, (Int**)NULL, iceq[ibk] );
            for( iek=0;iek<nek;iek++ )
           {
               elm[iek]->cmsk( ibk,nce[ibk]+iek, icep[ibk]+iek,iceq[ibk]+iek );
           }
        }
         for( ibk=0;ibk<nbk;ibk++ )
        {
            if( nbd[ibk] != 1 || nbq[ibk] != 1 )
           {
               cout << "invalid element family\n";
               exit(0);
           }
        }
     }
  }

   void cFdDomain::getcost0( Real *cost )
  {
      Real val;
      Int  iek,iq;

      cout<< "cFdDomain getcost\n";

      delete[] qcst;
      qcst= new Real[nq];
      delete[] qcpu;
      qcpu= new Int[nq];

      setv( 0,nq, ZERO, qcst );

      for( iek=0;iek<nek;iek++ )
     {
         elm[iek]->getcost( 0,ne[iek], ieq[iek], qcst );
     }

      val=0;
      for( iq=0;iq<nq;iq++ )
     {
         val+= qcst[iq];
     }
      cout << "cost extimated for this domain is "<<val << "\n";
     *cost+= val;
  }


   cFElement *cFdDomain::newelement( Int ityp )
  {
      cFElement *val=NULL;
      switch( ityp )
     {
         case( felement_q43 ):
        {
            val= new cfq43();
            break;
        }
         case( felement_t33 ):
        {
            val= new cft33();
            break;
        }
         case( felement_q83 ):
        {
            val= new cfq83();
            break;
        }
         case( felement_p63 ):
        {
            val= new cfp63();
            break;
        }
         case( felement_p53 ):
        {
            val= new cfp53();
            break;
        }
         case( felement_t43 ):
        {
            val= new cft43();
            break;
        }
         case( felement_e23 ):
        {
            val= new cfe23();
            break;
        }
         case( felement_q42 ):
        {
            val= new cfq42();
            break;
        }
         case( felement_e22 ):
        {
            val= new cfe22();
            break;
        }
         case( felement_t32 ):
        {
            val= new cft32();
            break;
        }

     }
      return val;
  }

   void cFdDomain::assgngas( cGas *var )
  {
      fld= var;
      vsc= fld->visc();
      nv= fld->getnv();
      naux= fld->getnaux();
      nauxf= fld->getnauxf();
      nlhs= fld->getnlhs();
  }


   void cFdDomain::bndries( cFbndry **var )
  {
      Int ig;
      if( !bbj )
     {
         bbj= new cFbndry*[ng];
         for( ig=0;ig<ng;ig++ )
        {
            bbj[ig]= var[ig];
        } 
     }
  }

   void cFdDomain::request( Int ig, Int *n, Int *m, Int *l, Real **x )
  {
      Int ibs,ibe;

     *n=0;
     *m=0;
     *x=NULL;

      ibs= 0;
      ibe= bdf[ig]->size();

      //bbj[ig]->request( ibs,ibe, omega*tm,xb[ig], n,m,l,x );
      bbj[ig]->request( ibs,ibe, omega*tm,sxb[ig],nbb[ig], n,m,l,x );
  }

   void cFdDomain::service( Int ig, Int nxr, Int nvr, Int nqr, Real *xr[], Real **sqr, bool bposix )
  {
//      Int ibs,ibe,iqs,iqe;
//
//      ibs= 0;
//      ibe= bdf[ig]->size();
//
//      dof->range( dev->getrank(),&iqs,&iqe );
///*    iqs=0;
//      iqe= dof->size();*/
//
//     // Int iq;
//     // for(iq=iqs; iq<iqe; iq++)
//     //{
//     //   cout << "domainx " << xq[0][iq] << " " << xq[1][iq] << " " << iq << " " << this << "\n";
//     //}
//
//      bbj[ig]->service( nxr,nvr,nqr, omega*tm, xr,sqr, iqs,iqe, xq,q,aux,dxdx,dqdx, ibs,ibe, iqb[ig], xqb[ig],qb[ig],
//                        auxb[ig], bposix );
      assert(0);
  }

   void cFdDomain::service( Int ig, Int nxr, Int nvr, Int nqr, Real *sxr, Real **sqr )
  {
      Int ibs,ibe,iqs,iqe;

      ibs= 0;
      ibe= bdf[ig]->size();

      dof->range( dev->getrank(),&iqs,&iqe );
      bbj[ig]->service( nxr,nvr,nqr, omega*tm, sxr,sqr, iqs,iqe, xq,q,aux,dxdx,dqdx, ibs,ibe, iqb_view[ig], xqb[ig],qb[ig],
                        auxb[ig] );
  }

   void cFdDomain::accept( Int ig, Int nvr, Int nqr, Real *sqr, Real *w )
  {
      //cout << "FDDOMAIN "<<this<<" accept ig "<<ig<<" nv "<<nv<<" nq "<<nqr<<"\n";
      Int ibs,ibe, nfrer, ifre;

      ibs= 0;
      ibe= bdf[ig]->size();

      //bbj[ig]->accept( ibs, ibe, nvr,nqr, omega*tm, qr,w, qb0[ig] );
      bbj[ig]->accept( ibs, ibe, nvr,nqr, omega*tm, sqr,w, qb0[ig] );

//      nfrer = bbj[ig]->getnfre_reci();
//      if(nfrer>0)
//     {
//         for(ifre=0; ifre<nfrer; ifre++)
//        {
//            bbj[ig]->accept_z( ibs, ibe, nvr,nqr, qr,w, qb0[ig], ifre, zb0_re[ifre][ig], zb0_im[ifre][ig] );
//            for(int ib=ibs; ib<ibe; ib++)
//           {
//               Real y, z, t;
//               y = xb[ig][1][ib];
//               z = xb[ig][2][ib];
//               t = atan2(y,z);
//               if(dev->getname()=="block2" && bgnm[ig]=="FREE.inlet")
//              {
//               cout << t << " " <<sqrt(y*y + z*z) << " ";
//               //for(int iv=0; iv<nv; iv++)
//               Int iv = 3;
//              {
//                  cout << zb0_re[ifre][ig][iv][ib] << " ";
//                  cout << zb0_im[ifre][ig][iv][ib] << " ";
//              }
//               cout << " block2 INLET accept harmonic " << ifre << " \n";
//              }
//           }
//        }
//     }
  }

   string cFdDomain::get_resd_info()
  {
      string tmp="";

      if(fld->gettype()==mfroe_gas)
     {
         if(vsc->gettype()==laminar_visc)
        {
            tmp = "5    Mass    X-mom    Y-mom    Z-mom    Energy";
        }
         else if(vsc->gettype()==komega_visc || 
                 vsc->gettype()==komegalowre_visc || 
                 vsc->gettype()==sbslearsm_visc)
        {
            tmp = "7    Mass    X-mom    Y-mom    Z-mom    Energy    K    Omega";
        }
     }
      else if(fld->gettype()==mfroe_gas_cht)
     {
         if(vsc->gettype()==laminar_visc)
        {
            tmp = "6    Mass    X-mom    Y-mom    Z-mom    Energy    Medium";
        }
         else if(vsc->gettype()==komega_visc || 
                 vsc->gettype()==komegalowre_visc || 
                 vsc->gettype()==sbslearsm_visc)
        {
            tmp = "8    Mass    X-mom    Y-mom    Z-mom    Energy    Medium    K    Omega";
        }
     }

//      if(nv==5)
//     {
//         //inviscid, laminar, zero-equation turb
//         tmp = "5 Mass X-mom Y-mom Z-mom Energy";
//     }    
//      else if(nv==6)
//     {
//         //SA
//         if(fld->gettype()==mfroe_gas_cht) tmp = "6 Mass X-mom Y-mom Z-mom Energy Medium";
//     }
//      else if(nv==7)
//     {
//         //k-omega
//         tmp = "7 Mass X-mom Y-mom Z-mom Energy K Omega";
//     }
      return tmp;
  }

