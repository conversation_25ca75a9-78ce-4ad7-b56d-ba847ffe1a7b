c
c********************************
c output  unk file
c********************************
c
      subroutine outunk(lfle,cfle,unkno,vrt,dis,npoin,itime,dtstr,
     &                gam,roref, uref,vrtref,disref,nstag,omega,ivisc,
     &                iturbm,ivfl,iret)
c
      real   unkno(5,*), vrt(*), dis(*), omega(*)
c
c    feng
      integer             lfle
      character(len=lfle) cfle
      integer   in
      character*500       fnm

      in = 20
      fnm= trim(cfle)
      open(in,
     &     file  =trim(fnm),
     &     form  ='unformatted',
     &     iostat = ierr)

      write(*,'(/,5x,  a)')'New unknowns contain ==================='
      write(in,iostat=ioerror)npoin,itime,dtstr,gam,roref,
     &        uref,vrtref,disref,
     &        nstag,(omega(ii),ii=1,nstag),ivfl
      if(ioerror/=0)goto 10
      write(in,iostat=ioerror) ((unkno(j,i), i=1,npoin),j=1,5)
c     do i = 1,npoin
c       write(1,*) (unkno(j,i),j=1,5)
c     enddo
      if(ioerror/=0)goto 10
      write(*,'(5x,a,i10)')'Unk                          :', npoin
c     write(*,'(/,a)') ' ... unk  written'
      if(ivisc.ne.0)then
        write(in,iostat=ioerror) (vrt(i), i=1,npoin)
        if(ioerror/=0)goto 10
c       write(*,'(/,a)') ' ... vrt  written'
        write(*,'(5x,a,i10)')'Vrt                          :', npoin
      endif
      if(iturbm.eq.3)then
        write(in,iostat=ioerror) (dis(i), i=1,npoin)
        if(ioerror/=0)goto 10
c       write(*,'(/,a)') ' ... dis  written'
        write(*,'(5x,a,i10)')'Dsp                          :', npoin
      endif
c
      write(*,'(5x,    a)')'========================================'
      return
c
 10   write(*,'(a)')' !! ERRROR : in writing unk file'
      iret = 999
c
      return
      end
