   using namespace std;

#  include <domain/cfd/domain.h>

/*   void jactimerot( cJacBlk *blkjac, Real f)
  {
      Real pitch = pi2/136.;
      Real rot[7][7];
      Real cth, sth;
      Int iv, jv, kv;
      cJacBlk newjac;

      cth = cos(f*pitch);
      sth = sin(f*pitch);

      rot[0][0] =1;rot[0][1] =0;rot[0][2] = 0  ;rot[0][3] = 0 ; rot[0][4] = 0; rot[0][5] = 0; rot[0][6] = 0;
      rot[1][0] =0;rot[1][1] =1;rot[1][2] = 0  ;rot[1][3] = 0 ; rot[1][4] = 0; rot[1][5] = 0; rot[1][6] = 0;
      rot[2][0] =0;rot[2][1] =0;rot[2][2] = cth;rot[2][3] =sth; rot[2][4] = 0; rot[2][5] = 0; rot[2][6] = 0;
      rot[3][0] =0;rot[3][1] =0;rot[3][2] =-sth;rot[3][3] =cth; rot[3][4] = 0; rot[3][5] = 0; rot[3][6] = 0;
      rot[4][0] =0;rot[4][1] =0;rot[4][2] = 0  ;rot[4][3] = 0 ; rot[4][4] = 1; rot[4][5] = 0; rot[4][6] = 0;
      rot[5][0] =0;rot[5][1] =0;rot[5][2] = 0  ;rot[5][3] = 0 ; rot[5][4] = 0; rot[5][5] = 1; rot[5][6] = 0;
      rot[6][0] =0;rot[6][1] =0;rot[6][2] = 0  ;rot[6][3] = 0 ; rot[6][4] = 0; rot[6][5] = 0; rot[6][6] = 1;

      for(iv=0; iv<7; iv++)
     {
         for(jv=0; jv<7; jv++)
        {
            newjac.jac[iv][jv] = 0.;
            for(kv=0; kv<7; kv++)
           {
               newjac.jac[iv][jv] += (blkjac->jac[iv][kv])*rot[kv][jv];
           }
        }
     }
      for(iv=0; iv<7; iv++)
     {
         for(jv=0; jv<7; jv++)
        {
            blkjac->jac[iv][jv] = newjac.jac[iv][jv];
        }
     }
  }*/

   void cFdDomain::debugf( Int n, cAu3xView<Real>& v, string fnm )
  {
      Int    ist,ien;
      Int    id,i,j;
      string fnme,ext;
      ofstream fle;

      id=  dev->getrank();
      dof->range( id, &ist,&ien );
      ext= strc( ilev );
      fnme= fnm+"."+ext;
      ext= strc( id );
      fnme= fnme+"."+ext;
      fle.open( fnme.c_str() ); 
      fle.setf( ios_base::scientific );
      fle.width( 22 );
      fle.precision( 16 );

      for( i=ist;i<ien;i++ )
     {
         for( j=0;j<nx;j++ )
        {
            fle << xq(j,i)<<" ";
        }
         for( j=0;j<n;j++ )
        {
            fle << v(j,i)<<" ";
        } 
         fle << "\n";
     }
      fle.close();
      
       
  }

   /*void cFdDomain::checkjac()
  {
      Real *dcsv[10], *rhs_new[10];
      Int iv, iq, ig, ic, ics, ice, ibs, ibe, jq, jv, iql, iqr;
      Real tmp[20];
      cJacBlk blkjac;

      for(iv=0; iv<nv; iv++)
     {
         dcsv[iv] = new Real [nq];
         rhs_new[iv] = new Real [nq];
     }

      for(iq=0; iq<nq; iq++)
     {
         for(jq=0; jq<nq; jq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               for(jv=0; jv<nv; jv++)
              {
                  lhs_jac[iq][jq].jac[iv][jv] = 0; 
              }
           }
        }
     }


      bcs();

      grad( q,dqdx );
      gradb( q,dqdx );

      setv( 0,dof->size(), nv, 0., rhs );
      gtrhf( rhs );
      //gtrhm( rhs );

      while( dev->transit( ) )
     {
//boundary
         for( ig=0;ig<ng;ig++ )
        {
            bdf[ig]->range( dev->avail(), &ibs,&ibe );
            setv( ibs,ibe, nlhs, 0.,lhsb[ig] );
            bbj[ig]->ilhs( ibs,ibe, xb[ig],qb[ig],auxb[ig],lhsb[ig], NULL,
                           iqb[ig], xq,q,aux,lhsa, lhs_jac, wnb[ig],wxdb[ig],auxfb[ig], jac_df );
        }

//periodic, shift upper to lower
         prd->range( dev->avail(), &ics,&ice );
         fld->voffset( ics,ice, -1.,iprq[1],q,NULL,qprd );
         for( iv=0;iv<naux;iv++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iq= iprq[1][ic];
               auxprd[iv][ic]= aux[iv][iq];
           }
        }
         fld->ilhs( ics,ice, NULL   , qprd,auxprd,lhsprd, NULL, 
                             iprq[0], q,   aux,   lhsa,   NULL, 
                             wnprd,wxdprd,auxfprd, jac_df_prd );
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac.jac[jv][iv] = jac_df_prd[0][ic].jac[jv][iv];
                  lhs_jac[iqr][iqr].jac[jv][iv]+= jac_df_prd[1][ic].jac[jv][iv];
              }
           }
            jactimerot(&blkjac, -1.);   
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  lhs_jac[iqr][iql].jac[jv][iv]+= blkjac.jac[jv][iv];
              }
           }
        }
//periodic, shift lower to upper
         coo->coffset( ics,ice, 1.,NULL,wnprd,NULL,wnprd ); //rotate it to the LEFT
         fld->voffset( ics,ice, 1.,iprq[0],q,NULL,qprd );
         for( iv=0;iv<naux;iv++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iq= iprq[0][ic];
               auxprd[iv][ic]= aux[iv][iq];
           }
        }
         fld->ilhs( ics,ice, iprq[1], q,   aux,   lhsa,   NULL, 
                             NULL   , qprd,auxprd,lhsprd, NULL, 
                             wnprd,wxdprd,auxfprd, jac_df_prd );
         for(ic=ics; ic<ice; ic++)
        {
            iql = iprq[1][ic];
            iqr = iprq[0][ic];
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  blkjac.jac[jv][iv] = jac_df_prd[1][ic].jac[jv][iv];
                  lhs_jac[iql][iql].jac[jv][iv]-= jac_df_prd[0][ic].jac[jv][iv];
              }
           }
            jactimerot(&blkjac, 1.);   
            for(jv=0; jv<nv; jv++)
           {
               for(iv=0; iv<nv; iv++)
              {
                  lhs_jac[iql][iqr].jac[jv][iv]-= blkjac.jac[jv][iv];
              }
           }
        }
         coo->coffset( ics,ice, -1.,NULL,wnprd,NULL,wnprd );//roate it back to RIGHT

//internal faces
         cnf->range( dev->avail(),&ics,&ice );
         fld->ilhs( ics,ice, ifq[0], q,aux,lhsa, lhs_jac, ifq[1], q,aux,lhsa, lhs_jac, wnc,wxdc,auxf, jac_df );
     }

//compare with finite difference Jacobi*dU
      for(iv=0; iv<nv; iv++)
     {
         for(iq=0; iq<nq; iq++)
        {
            dcsv[iv][iq] = 1e-6;
        }
     }
      fld->dvar( 0,dof->size(), q, aux, dcsv, dq );
      for(iv=0; iv<nv; iv++)
     {
         for(iq=0; iq<nq; iq++)
        {
            q[iv][iq] += dq[iv][iq];
        }
     }
      grad( q,dqdx );
      gradb( q,dqdx );
      setv( 0,dof->size(), nv, 0., rhs_new );
      gtrhf( rhs_new );



      for(iq=0; iq<nq; iq++)
     {
         for(iv=0; iv<nv; iv++)
        {
            tmp[iv] = 0;
            for(jq=0; jq<nq; jq++)
           {
               for(jv=0; jv<nv; jv++)
              {
                  tmp[iv] += lhs_jac[iq][jq].jac[iv][jv]*dcsv[jv][jq];
              }
           }
            cout << iq << " " << tmp[iv] << " " << rhs_new[iv][iq] - rhs[iv][iq] << "\n";
        }
     }

      for(iv=0; iv<nv; iv++)
     {
         delete[] dcsv[iv];
         delete[] rhs_new[iv];
     }
      exit(0);
  }*/

   void cFdDomain::check_diflx()
  {
/*      Real *dcsv[10], *rhs_new[10], *drhs[10], *rhs_old[10];
      Int iv, iq, ig, ic, ics, ice, ibs, ibe, jq, jv, iql, iqr, iqs, iqe;
      Real tmp[20];

      for(iv=0; iv<nv; iv++)
     {
         dcsv[iv] = new Real [nq];
         rhs_new[iv] = new Real [nq];
         rhs_old[iv] = new Real [nq];
         drhs[iv] = new Real [nq];
     }

      bcs();

//assble RHS
//      grad( q,dqdx );
//      gradb( q,dqdx );

      setv( 0,dof->size(), nv, 0., rhs_old );
      gtrhf( rhs_old );

      for(iq=0; iq<nq; iq++)
     {
         cout << rhs_old[0][iq] << " " << rhs_old[1][iq] << " " << rhs_old[2][iq] << " " << rhs_old[3][iq] << " rhs_old\n";
     }

      cout << "\nperturb flow variable \n";
//direct lineariztion
      //set a gradient of flow perturbations
      for(iv=0; iv<nv; iv++)
     {
        for(iq=0; iq<nq; iq++)
       {
           if(iv==0)
          {
              dcsv[iv][iq] = 0.001;
          }
           else
          {
              dcsv[iv][iq] = 0.0;
          }
       }
     }
      fld->dvar( 0,dof->size(), q, aux, dcsv, dq );

      for(iq=0; iq<nq; iq++)
     {
         cout << dcsv[0][iq] << " " << dcsv[1][iq] << " " << dcsv[2][iq] << " " << dcsv[3][iq] << " dcsv\n";
     }
      for(iq=0; iq<nq; iq++)
     {
         cout << dq[0][iq] << " " << dq[1][iq] << " " << dq[2][iq] << " " << dq[3][iq] << " dq\n";
     }

      setv( 0,dof->size(), nv, 0., drhs );
// fluxes - boundary faces

      for( ig=0;ig<ng;ig++ )
     {
         bdf[ig]->range( dev->avail(), &ibs,&ibe );
         setv( ibs,ibe, nv, 0., dqb[ig] );
         setv( ibs,ibe, nv, 0., dauxb[ig] );
         setv( ibs,ibe, nv, 0., resb[ig] );
         cout << "boundary " << ig << " " << bgnm[ig] << " --------- \n";
         bbj[ig]->diflx( ibs,ibe, xb[ig], qb[ig],auxb[ig], dqb[ig],dauxb[ig], resb[ig], iqb[ig], xq, q,aux, dcsv,dq,drhs,
                        wnb[ig], wxdb[ig], auxfb[ig] );
     }


// fluxes - periodic faces
      prd->range( dev->avail(), &ics,&ice );
      setv( ics,ice, nv, 0., rhsprd );
      setv( ics,ice, nv, 0., dqprd );
      setv( ics,ice, nv, 0., dauxprd );
      coo->coffset( ics,ice, -1.,       iprq[1],xq,                      NULL, xqprd );
      fld->voffset( ics,ice, -1.,       iprq[1], q,                      NULL,  qprd );
      fld->roffset( ics,ice, -1., iprq[1], dcsv, NULL, dqprd );
      fld->voffset( ics,ice, -1., iprq[1], dq, NULL, dauxprd );
      //fld->diflx( ics,ice, NULL, qprd,auxprd,dqprd,dauxprd,rhsprd, iprq[0], q,aux,dcsv,dq,drhs, wnprd, wxdprd, auxfprd );
      fld->roffset( ics,ice,  1, NULL, rhsprd, NULL, rhsprd );
      for( iv=0;iv<nv;iv++ )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            iq= iprq[1][ic];
            drhs[iv][iq]+= rhsprd[iv][ic];
        }
     }

// fluxes - inner faces
      cout << "internal faces -----------\n";
      cnf->range( dev->avail(), &ics,&ice );
      //fld->diflx( ics,ice, ifq[0], q,aux,dcsv,dq,drhs, ifq[1], q,aux,dcsv,dq,drhs, wnc, wxdc, auxf );


//compare with finite difference Jacobi*dU
//      for(iq=0; iq<nq; iq++)
//     {
//         cout << dq[0][iq] << " " << dq[1][iq] << " " << dq[2][iq] << " " << dq[3][iq] << " " << dq[4][iq] << " dq\n";
//     }
      for(iv=0; iv<nv; iv++)
     {
         for(iq=0; iq<nq; iq++)
        {
            q[iv][iq] += dq[iv][iq];
        }
     }
//      grad( q,dqdx );
//      gradb( q,dqdx );
      setv( 0,dof->size(), nv, 0., rhs_new );
      gtrhf( rhs_new );

      for(iq=0; iq<nq; iq++)
     {
         cout << rhs_new[0][iq] << " " << rhs_new[1][iq] << " " << rhs_new[2][iq] << " " << rhs_new[3][iq] << " rhs_new\n";
     }


      cout << "\n\n";
      for(iq=0; iq<nq; iq++)
     {
         for(iv=0; iv<nv; iv++)
        {
            cout << iq << " " << rhs_new[iv][iq] - rhs_old[iv][iq] << " " << drhs[iv][iq] << "\n";
        }
         cout << "\n";
     }

      for(iv=0; iv<nv; iv++)
     {
         delete[] dcsv[iv];
         delete[] rhs_new[iv];
         delete[] drhs[iv]; drhs[iv]=NULL;
     }
      exit(0);*/
  }

   void cFdDomain::check_dmflx()
  {
/*      Real *dcsv[10], *rhs_new[10], *drhs[10], *rhs_old[10];
      Int iv, iq, ig, ic, ics, ice, ibs, ibe, jq, jv, iql, iqr, iqs, iqe;
      Real tmp[20];

      for(iv=0; iv<nv; iv++)
     {
         dcsv[iv] = new Real [nq];
         rhs_new[iv] = new Real [nq];
         rhs_old[iv] = new Real [nq];
         drhs[iv] = new Real [nq];
     }

      bcs();

//assble RHS
      grad( q,dqdx );
      gradb( q,dqdx );

      setv( 0,dof->size(), nv, 0., drhs );
      setv( 0,dof->size(), nv, 0., rhs_old );
      gtrhm( rhs_old );

      for(iq=0; iq<nq; iq++)
     {
         cout << rhs_old[0][iq] << " " << rhs_old[1][iq] << " " << rhs_old[2][iq] << " " << rhs_old[3][iq] << " rhs_old\n";
     }

      cout << "\nperturb flow variable \n";
//direct lineariztion
      //set non-uniform flow perturbations
      for(iv=0; iv<nv; iv++)
     {
        for(iq=0; iq<nq; iq++)
       {
           if(iv==1 )
          {
              dcsv[iv][iq] = 0.01 + iq*0.01;
          }
           if(iv==2 )
          {
              dcsv[iv][iq] = 0.005 + iq*0.002;
          }
           if(iv==3 )
          {
              dcsv[iv][iq] = 0.003 + iq*0.003;
          }
           else
          {
              dcsv[iv][iq] = 0.0;
          }
       }
     }
      fld->dvar( 0,dof->size(), q, aux, dcsv, dq );

      for(iq=0; iq<nq; iq++)
     {
         cout << dcsv[0][iq] << " " << dcsv[1][iq] << " " << dcsv[2][iq] << " " << dcsv[3][iq] << " dcsv\n";
     }
      for(iq=0; iq<nq; iq++)
     {
         cout << dq[0][iq] << " " << dq[1][iq] << " " << dq[2][iq] << " " << dq[3][iq] << " dq\n";
     }

      setv( 0,dof->size(), nv, 0., drhs );
// fluxes - boundary faces

      for( ig=0;ig<ng;ig++ )
     {
         bdf[ig]->range( dev->avail(), &ibs,&ibe );
         setv( ibs,ibe, nv, 0., dqb[ig] );
         setv( ibs,ibe, nv, 0., dauxb[ig] );
         setv( ibs,ibe, nv, 0., resb[ig] );
         cout << "boundary " << ig << " " << bgnm[ig] << " --------- \n";
         bbj[ig]->dmflx( ibs,ibe, xb[ig],qb[ig],auxb[ig], dqb[ig],dauxb[ig], resb[ig], iqb[ig], xq,q,aux, dcsv,dq,drhs,
                        wnb[ig], wxdb[ig], auxfb[ig] );
     }

      cout << "\n\n";
      iq=0;
     {
         for(iv=0; iv<nv; iv++)
        {
            cout << iq << " " << drhs[iv][iq] << "==========\n";
        }
         cout << "\n";
     }

// fluxes - periodic faces
      prd->range( dev->avail(), &ics,&ice );
      setv( ics,ice, nv, 0., rhsprd );
      setv( ics,ice, nv, 0., dqprd );
      setv( ics,ice, nv, 0., dauxprd );
      coo->coffset( ics,ice, -1.,       iprq[1],xq,                      NULL, xqprd );
      fld->voffset( ics,ice, -1.,       iprq[1], q,                      NULL,  qprd );
      fld->roffset( ics,ice, -1., iprq[1], dcsv, NULL, dqprd );
      fld->voffset( ics,ice, -1., iprq[1], dq, NULL, dauxprd );
      //fld->dmflx( ics,ice, NULL, xqprd,qprd,auxprd,dqprd,dauxprd,rhsprd, iprq[0], xq,q,aux,dcsv,dq,drhs, xprd, wnprd, wxdprd, auxfprd );
      fld->roffset( ics,ice,  1, NULL, rhsprd, NULL, rhsprd );
      for( iv=0;iv<nv;iv++ )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            iq= iprq[1][ic];
            drhs[iv][iq]+= rhsprd[iv][ic];
        }
     }

// fluxes - inner faces
      cout << "internal faces -----------\n";
      cnf->range( dev->avail(), &ics,&ice );
      //fld->dmflx( ics,ice, ifq[0], xq,q,aux,dcsv,dq,drhs, ifq[1], xq,q,aux,dcsv,dq,drhs, xc, wnc, wxdc, auxf );

      iq=0;
     {
         for(iv=0; iv<nv; iv++)
        {
            cout << iq << " " << drhs[iv][iq] << "==========\n";
        }
         cout << "\n";
     }

//compare with finite difference Jacobi*dU
//      for(iq=0; iq<nq; iq++)
//     {
//         cout << dq[0][iq] << " " << dq[1][iq] << " " << dq[2][iq] << " " << dq[3][iq] << " " << dq[4][iq] << " dq\n";
//     }
      for(iv=0; iv<nv; iv++)
     {
         for(iq=0; iq<nq; iq++)
        {
            q[iv][iq] += dq[iv][iq];
        }
     }
      grad( q,dqdx );
      gradb( q,dqdx );
      setv( 0,dof->size(), nv, 0., rhs_new );
      gtrhm( rhs_new );

      for(iq=0; iq<nq; iq++)
     {
         cout << rhs_new[0][iq] << " " << rhs_new[1][iq] << " " << rhs_new[2][iq] << " " << rhs_new[3][iq] << " rhs_new\n";
     }


      cout << "\n\n";
      for(iq=0; iq<nq; iq++)
     {
         for(iv=0; iv<nv; iv++)
        {
            cout << iq << " " << rhs_new[iv][iq] - rhs_old[iv][iq] << " " << drhs[iv][iq] << "\n";
        }
         cout << "\n";
     }

      for(iv=0; iv<nv; iv++)
     {
         delete[] dcsv[iv];
         delete[] rhs_new[iv];
         delete[] drhs[iv]; drhs[iv]=NULL;
     }
      exit(0);*/
  }

   void cFdDomain::check_diflxmuscl()
  {
/*      cout << "===================cFdDomain::check_diflxmuscl()===================\n";
      Real *dcsv[10], *rhs_new[10], *drhs[10], *rhs_old[10];
      Int iv, iq, ig, ic, ics, ice, ibs, ibe, jq, jv, iql, iqr, iqs, iqe;
      Real tmp[20];

      for(iv=0; iv<nv; iv++)
     {
         dcsv[iv] = new Real [nq];
         rhs_new[iv] = new Real [nq];
         rhs_old[iv] = new Real [nq];
         drhs[iv] = new Real [nq];

     }

      bcs();

//assble RHS
      grad( q,dqdx );
      gradb( q,dqdx );

      setv( 0,dof->size(), nv, 0., drhs );
      setv( 0,dof->size(), nv, 0., rhs_old );
      gtrhf( rhs_old );

      for(iq=0; iq<nq; iq++)
     {
         cout << rhs_old[0][iq] << " " << rhs_old[1][iq] << " " << rhs_old[2][iq] << " " << rhs_old[3][iq] << " rhs_old\n";
     }

      cout << "\nperturb flow variable \n";
//direct lineariztion
      //set a gradient of flow perturbations
//      for(iv=0; iv<nv; iv++)
//     {
//        for(iq=0; iq<nq; iq++)
//       {
//           if(iv==0)
//          {
//              dcsv[iv][iq] = 0.001;
//          }
//           else
//          {
//              dcsv[iv][iq] = 0.0;
//          }
//       }
//     }
      for(iv=0; iv<nv; iv++)
     {
        for(iq=0; iq<nq; iq++)
       {
           if(iv==1 )
          {
              dcsv[iv][iq] = 0.01 + iq*0.01;
          }
           if(iv==2 )
          {
              dcsv[iv][iq] = 0.005 + iq*0.002;
          }
           if(iv==3 )
          {
              dcsv[iv][iq] = 0.003 + iq*0.003;
          }
           else
          {
              dcsv[iv][iq] = 0.0;
          }
       }
     }
      fld->dvar( 0,dof->size(), q, aux, dcsv, dq );
      nfre = 1;
      sz_re[0] = new Real [nv*nq];
      sz_im[0] = new Real [nv*nq];
      z_re[0] = new Real* [nv];
      z_im[0] = new Real* [nv];
      subv(nv, nq, sz_re[0], z_re[0]);
      subv(nv, nq, sz_im[0], z_im[0]);
      for(iv=0; iv<nv; iv++)
     {
         for(iq=0; iq<nq; iq++)
        {
            z_re[0][iv][iq] = dq[iv][iq];
            z_im[0][iv][iq] = dq[iv][iq];
        }
     }
      for(iv=0; iv<nv; iv++)
     {
         dzdx_re[iv] = new Real* [nx];
         dzdx_im[iv] = new Real* [nx];
         for(int ix=0; ix<nx; ix++)
        {
            dzdx_re[iv][ix] = new Real [nq];
            dzdx_im[iv][ix] = new Real [nq];
        }
     }

      grad_z(  0, z_re[0], dzdx_re, z_im[0], dzdx_im );
      gradb_z( 0, z_re[0], dzdx_re, z_im[0], dzdx_im );

      for(iq=0; iq<nq; iq++)
     {
         cout << dcsv[0][iq] << " " << dcsv[1][iq] << " " << dcsv[2][iq] << " " << dcsv[3][iq] << " dcsv\n";
     }
      for(iq=0; iq<nq; iq++)
     {
         cout << dq[0][iq] << " " << dq[1][iq] << " " << dq[2][iq] << " " << dq[3][iq] << " dq\n";
     }

      setv( 0,dof->size(), nv, 0., drhs );
// fluxes - boundary faces

      for( ig=0;ig<ng;ig++ )
     {
         bdf[ig]->range( dev->avail(), &ibs,&ibe );
         setv( ibs,ibe, nv, 0., dqb[ig] );
         setv( ibs,ibe, nv, 0., dauxb[ig] );
         setv( ibs,ibe, nv, 0., resb[ig] );
         cout << "boundary " << ig << " " << bgnm[ig] << " --------- \n";
         bbj[ig]->diflx( ibs,ibe, xb[ig], qb[ig],auxb[ig], dqb[ig],dauxb[ig], resb[ig], iqb[ig], xq, q,aux, dcsv,dq,drhs,
                        wnb[ig], wxdb[ig], auxfb[ig] );
     }


// fluxes - periodic faces
      prd->range( dev->avail(), &ics,&ice );
      setv( ics,ice, nv, 0., rhsprd );
      setv( ics,ice, nv, 0., dqprd );
      setv( ics,ice, nv, 0., dauxprd );
      coo->coffset( ics,ice, -1.,       iprq[1],xq,                      NULL, xqprd );
      fld->voffset( ics,ice, -1.,       iprq[1], q,                      NULL,  qprd );
      fld->roffset( ics,ice, -1., iprq[1], dcsv, NULL, dqprd );
      fld->voffset( ics,ice, -1., iprq[1], dq, NULL, dauxprd );
      //fld->diflx( ics,ice, NULL, qprd,auxprd,dqprd,dauxprd,rhsprd, iprq[0], q,aux,dcsv,dq,drhs, wnprd, wxdprd, auxfprd );
      fld->roffset( ics,ice,  1, NULL, rhsprd, NULL, rhsprd );
      for( iv=0;iv<nv;iv++ )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            iq= iprq[1][ic];
            drhs[iv][iq]+= rhsprd[iv][ic];
        }
     }

// fluxes - inner faces
      cout << "internal faces -----------\n";
      cnf->range( dev->avail(), &ics,&ice );
//      fld->diflx( ics,ice, ifq[0], q,aux,dcsv,dq,drhs, ifq[1], q,aux,dcsv,dq,drhs, wnc, wxdc, auxf );
      //fld->diflxmuscl( ics, ice, -1, ifq[0], xq, q, z_re[0], dxdx, dqdx, dzdx_re, drhs,
      //                           -1, ifq[1], xq, q, z_re[0], dxdx, dqdx, dzdx_re, drhs,
      //                            xc, wnc, wxdc, auxf, this );


//compare with finite difference Jacobi*dU
//      for(iq=0; iq<nq; iq++)
//     {
//         cout << dq[0][iq] << " " << dq[1][iq] << " " << dq[2][iq] << " " << dq[3][iq] << " " << dq[4][iq] << " dq\n";
//     }
      for(iv=0; iv<nv; iv++)
     {
         for(iq=0; iq<nq; iq++)
        {
            q[iv][iq] += dq[iv][iq];
        }
     }
      grad( q,dqdx );
      gradb( q,dqdx );
      setv( 0,dof->size(), nv, 0., rhs_new );
      gtrhf( rhs_new );

      for(iq=0; iq<nq; iq++)
     {
         cout << rhs_new[0][iq] << " " << rhs_new[1][iq] << " " << rhs_new[2][iq] << " " << rhs_new[3][iq] << " rhs_new\n";
     }


      cout << "\n\n";
      for(iq=0; iq<nq; iq++)
     {
         for(iv=0; iv<nv; iv++)
        {
            cout << iq << " " << rhs_new[iv][iq] - rhs_old[iv][iq] << " " << drhs[iv][iq] << "\n";
        }
         cout << "\n";
     }

      for(iv=0; iv<nv; iv++)
     {
         delete[] dcsv[iv];
         delete[] rhs_new[iv];
         delete[] drhs[iv]; drhs[iv]=NULL;
     }
      exit(0);*/
  }
