   using namespace std;

#  include <domain/cfd/domain.h>

   Real Venkat( Real d2, Real d1min, Real d1max, Real eps2 )
  {
      if (d2 > small)
     {
         Real num = (d1max*d1max+eps2)*d2 + 2.0*d2*d2*d1max;
         Real den = d2*(d1max*d1max+2.0*d2*d2+d1max*d2+eps2);
         return (num/den);
     }
      else if (d2 < -small)
     {
         Real num = (d1min*d1min+eps2)*d2 + 2.0*d2*d2*d1min;
         Real den = d2*(d1min*d1min+2.0*d2*d2+d1min*d2+eps2);
         return (num/den);
     }
      return 1.0;
  }

   void cFdDomain::setvenkref()
  {
//      visc_t turb_type = vsc->gettype();
//
//      volref = 1;
//      if(nx==2)
//     {
//         venkqref[0] = 100./100;
//         venkqref[1] = 100./100;
//         venkqref[2] = 288.;
//         venkqref[3] = 101325./10000;
//         if( turb_type==laminar_visc || 
//             turb_type==cebeci_visc  ||
//             turb_type==spalart_visc )
//        {
//            //zero equaiton
//            if(dev->getrank()==0) cout << "setvenkref, zero equation\n";
//        }
//         else if(turb_type==spalart_visc)
//        {
//            //one equaiton
//            if(dev->getrank()==0) cout << "setvenkref, one equation\n";
//            venkqref[4] = 1e-6;
//        }
//         else if(turb_type==komega_visc        ||
//                 turb_type==kepsilon_visc      ||
//                 turb_type==kepsilonlowre_visc ||
//                 turb_type==komegasst_visc     ||
//                 turb_type==komegalowre_visc   ||
//                 turb_type==komegasst2003_visc ||
//                 turb_type==sbslearsm_visc     )
//        {
//            //two equaiton
//            if(dev->getrank()==0) cout << "setvenkref, two equation\n";
//            venkqref[4] = 1e-4;
//            venkqref[5] = 0.01;
//        }
//     }
//      else
//     {
//         venkqref[0] = 200./100;
//         venkqref[1] = 200./100;
//         venkqref[2] = 200./100;
//         venkqref[3] = 488.;
//         venkqref[4] = 201325./10000;
//         if( turb_type==laminar_visc || 
//             turb_type==cebeci_visc  ||
//             turb_type==spalart_visc )
//        {
//            //zero equaiton
//            if(dev->getrank()==0) cout << "setvenkref, zero equation\n";
//        }
//         else if(turb_type==spalart_visc)
//        {
//            //one equaiton
//            if(dev->getrank()==0) cout << "setvenkref, one equation\n";
//            venkqref[5] = 1e-6;
//        }
//         else if(turb_type==komega_visc        ||
//                 turb_type==kepsilon_visc      ||
//                 turb_type==kepsilonlowre_visc ||
//                 turb_type==komegasst_visc     ||
//                 turb_type==komegalowre_visc   ||
//                 turb_type==komegasst2003_visc ||
//                 turb_type==sbslearsm_visc     )
//        {
//            //two equaiton
//            if(dev->getrank()==0) cout << "setvenkref, two equation\n";
//            venkqref[5] = 1e-4;
//            venkqref[6] = 0.01;
//        }
//     }
  }

   void cFdDomain::initvenklim()
  {
//      Int ig, iv;
//      Int ibs, ibe, ib;
//      Int ics, ice, ic;
//      Int iqs, iqe, iq, iql, iqr;
//
//      setv( 0,nq, nv, (Real)1, vklim );
//      for(iv=0; iv<nv; iv++)
//     {
//         for(iq=0; iq<nq; iq++)
//        {
//            qmin[iv][iq] = q[iv][iq]; 
//            qmax[iv][iq] = q[iv][iq]; 
//        }
//     }
//      while( dev->transit( ) )
//     {
//         // boundary faces
//         for( ig=0;ig<ng;ig++ )
//        {
//            bdf[ig]->range( dev->avail(), &ibs,&ibe );
//            if(bbj[ig]->gettype()!=inv_fbndry  && 
//               bbj[ig]->gettype()!=visc_fbndry && 
//               bbj[ig]->gettype()!=neut_fbndry )
//           {
//               for(ib=ibs; ib<ibe; ib++)
//              {
//                  iq = iqb[ig][0][ib];
//                  for(iv=0; iv<nv; iv++)
//                 {
//                     qmin[iv][iq] = fmin(qb[ig][iv][ib], qmin[iv][iq]);
//                     qmax[iv][iq] = fmax(qb[ig][iv][ib], qmax[iv][iq]);
//                 }
//              }
//           }
//        }
//
//         // periodic faces
//         prd->range( dev->avail(), &ics,&ice );
//         fld->voffset( ics,ice, -1.,       iprq[1], q,                      NULL,  qprd );
//         for(ic=ics; ic<ice; ic++)
//        {
//            iq = iprq[0][ic];
//            for(iv=0; iv<nv; iv++)
//           {
//               qmin[iv][iq] = fmin(qprd[iv][ic], qmin[iv][iq]);
//               qmax[iv][iq] = fmax(qprd[iv][ic], qmax[iv][iq]);
//           }
//        }
//         fld->voffset( ics,ice,  1.,       iprq[0], q,                      NULL,  qprd );
//         for(ic=ics; ic<ice; ic++)
//        {
//            iq = iprq[1][ic];
//            for(iv=0; iv<nv; iv++)
//           {
//               qmin[iv][iq] = fmin(qprd[iv][ic], qmin[iv][iq]);
//               qmax[iv][iq] = fmax(qprd[iv][ic], qmax[iv][iq]);
//           }
//        }
//
//
//
//         //internal faces
//         cnf->range( dev->avail(), &ics,&ice );
//         for(ic=ics; ic<ice; ic++)
//        {
//            iql = ifq[0][ic];
//            iqr = ifq[1][ic];
//            for(iv=0; iv<nv; iv++)
//           {
//               qmin[iv][iql] = fmin(q[iv][iqr], qmin[iv][iql]);
//               qmax[iv][iql] = fmax(q[iv][iqr], qmax[iv][iql]);
//
//
//               qmin[iv][iqr] = fmin(q[iv][iql], qmin[iv][iqr]);
//               qmax[iv][iqr] = fmax(q[iv][iql], qmax[iv][iqr]);
//           }
//        }
//     }
  }

   void cFdDomain::compvenklim()
  {
//      Int ig;
//      Int ibs, ibe, ib;
//      Int ics, ice, ic;
//      Int iqs, iqe, iq, iql, iqr;
//
//      while( dev->transit( ) )
//     {
//         //boundary face
//         for( ig=0;ig<ng;ig++ )
//        {
//            bdf[ig]->range( dev->avail(), &ibs,&ibe );
//            for(ib=ibs; ib<ibe; ib++)
//           {
//               iqr = iqb[ig][0][ib];
//               compvenklim(iqr, ib, xb[ig], wnb[ig]);
//           }
//        }
//
//         // periodic faces
//         prd->range( dev->avail(), &ics,&ice );
//         for(ic=ics; ic<ice; ic++)
//        {
//            iq = iprq[0][ic];
//            compvenklim(iq, ic, xprd, wnprd);
//        }
//         coo->coffset( ics,ice, 1., NULL,xprd,NULL,xprd );
//         coo->coffset( ics,ice, 1., NULL,wnprd,NULL,wnprd );
//         for(ic=ics; ic<ice; ic++)
//        {
//            iq = iprq[1][ic];
//            compvenklim(iq, ic, xprd, wnprd);
//        }
//         coo->coffset( ics,ice, -1., NULL,xprd,NULL,xprd );
//         coo->coffset( ics,ice, -1., NULL,wnprd,NULL,wnprd );
//
//         //internal faces
//         cnf->range( dev->avail(), &ics,&ice );
//         for(ic=ics; ic<ice; ic++)
//        {
//            iql = ifq[0][ic];
//            iqr = ifq[1][ic];
//            compvenklim(iql, ic, xc, wnc);
//            compvenklim(iqr, ic, xc, wnc);
//        }
//     }
  }


   void cFdDomain::compvenklim(Int iq, Int ic, cAu3xView<Real>& xf, cAu3xView<Real>& wnf)
  {
//      Int iv, ix;
//      Real dl[3], d2, epsn, d1min, limval, d1max;
//      Real eps2[10], limfac3;
//
//      if(nx==3) limfac3 = limfac*limfac*limfac/volref;
//      else      limfac3 = limfac*limfac*limfac/pow(volref,1.5);
//      for(iv=0; iv<nv; iv++)
//     {
//         eps2[iv] = limfac3*venkqref[iv]*venkqref[iv];
//     }
//  
//      for(iv=0; iv<nv; iv++)
//     {
//         for(ix=0; ix<nx; ix++)
//        {
//            dl[ix] = xf[ix][ic] - xq[ix][iq];
//        }
//         d2 = 0;
//         for(ix=0; ix<nx; ix++)
//        {
//            d2 += dqdx[iv][ix][iq]*dl[ix]; 
//        } 
//
//         if(nx==3) epsn = eps2[iv]*wq[0][iq];
//         else      epsn = eps2[iv]*pow(wq[0][iq],1.5);
//
//         d1min = qmin[iv][iq] - q[iv][iq];
//         d1max = qmax[iv][iq] - q[iv][iq];
//         limval = Venkat( d2,d1min,d1max,epsn );
//         vklim[iv][iq] = fmin(limval,vklim[iv][iq]);
//     }
  }

   void cFdDomain::gtrhfm_venk( cAu3xView<Real>& r )
  {
//      Int iqs,iqe,ibs,ibe,ics,ice,ids,ide;
//      Int ix,ig,iv,iq,ic;
//      Real dl[3], tmpdq;
//
//      if( vsc->viscous() )
//     {
//         for( ig=0;ig<ng;ig++ )
//        {
//            bdf[ig]->exchange( sauxb[ig] );
//        }
//
//         while( dev->transit() )
//        {
//// auxiliary variables for the degrees of freedom
//            for( ig=0;ig<ng;ig++ )
//           {
//               dsd[ig]->range( dev->avail(), &ids,&ide );
////               fld->yplus( ids,ide, iqdst[ig][0], xq, q, aux, ibdst[ig][0], qb[ig],auxb[ig], dst );
//
//           }
//        }
//         dof->exchange( sdst );
//     }
//
//      for( iv=0;iv<nv;iv++ )
//     {
//         dof->exchange(sdqdx+iv*nx*nq);
//     }
//      dof->exchange( sdxdx );
//      dof->exchange( swq );
//      dof->exchange( svklim );
//      while( dev->transit( ) )
//     {
//
//// auxiliary variables for the degrees of freedom
//
//         dof->range( dev->avail(), &iqs,&iqe );
//         fld->auxv( iqs,iqe, q,aux ); 
//
//         fld->accel( iqs,iqe, omega, wq, xq,q,aux, xdq,r );
//
//// save conserved variables
//         if( unst ){ fld->cnsv( iqs,iqe, q, aux, u[0] ); };
//
////----------------------------------inviscid contributions------------------------------------------------------------
//
//// boundary faces
//
//         for( ig=0;ig<ng;ig++ )
//        {
//            bdf[ig]->range( dev->avail(), &ibs,&ibe );
//            bbj[ig]->auxv( ibs,ibe, qb[ig],auxb[ig] );
//
//            setv( ibs,ibe, nv, ZERO, rhsb[ig] );
//            //bbj[ig]->iflx( ibs,ibe, xb[ig],qb[ig],auxb[ig],rhsb[ig], iqb[ig],xq,q,aux,r, wnb[ig],wxdb[ig],auxfb[ig] );
//        }
//
//// Inviscid fluxes - periodic faces
//
//         prd->range( dev->avail(), &ics,&ice );
//         setv( ics,ice, nv, ZERO,rhsprd );
//         for( iv=0;iv<naux;iv++ )
//        {
//            for( ic=ics;ic<ice;ic++ )
//           {
//               iq= iprq[1][ic];
//               auxprd[iv][ic]= aux[iv][iq];
//           }
//        }
//         //1.save the values on the periodic face
//         //2.use the limiter to extrapolate it first and then rotate it
//         //then there is no need to use the limiter after the variables are
//         //rotated to the other periodic boundary
//         for( ic=ics;ic<ice;ic++ )
//        {
//            iq= iprq[0][ic];
//            for( iv=0;iv<nv;iv++ )
//           {
//               qprdface[0][iv][ic]= q[iv][iq];
//               for(ix=0; ix<nx; ix++)
//              {
//                  dl[ix] = xprd[ix][ic] - xq[ix][iq];
//              }
//               tmpdq=0;
//               for(ix=0; ix<nx; ix++)
//              {
//                  tmpdq += dqdx[iv][ix][iq]*dl[ix];
//              }
//               tmpdq *= vklim[iv][iq];
//               qprdface[0][iv][ic] += tmpdq;
//           }
//        }
//         coo->coffset( ics,ice, 1., NULL,xprd,NULL,xprd );
//         for( ic=ics;ic<ice;ic++ )
//        {
//            iq= iprq[1][ic];
//            for( iv=0;iv<nv;iv++ )
//           {
//               qprd[iv][ic]= q[iv][iq];
//               for(ix=0; ix<nx; ix++)
//              {
//                  dl[ix] = xprd[ix][ic] - xq[ix][iq];
//              }
//               tmpdq=0;
//               for(ix=0; ix<nx; ix++)
//              {
//                  tmpdq += dqdx[iv][ix][iq]*dl[ix];
//              }
//               tmpdq *= vklim[iv][iq];
//               qprd[iv][ic] += tmpdq;
//               qprdface[1][iv][ic] = qprd[iv][ic];
//           }
//        }
//         coo->coffset( ics,ice, -1., NULL,xprd,NULL,xprd );
//
//         coo->coffset( ics,ice, -1.,       iprq[1],xq,                      NULL, xqprd );
//         fld->voffset( ics,ice, -1.,       NULL,   qprd,                    NULL,  qprd );
//         fld->goffset( ics,ice, -1., ijdx, iprq[1], dxdx,dqdx, NULL, dxdxprd, dqdxprd );
//         if( ilev == 0 )
//        {
////            fld->iflx( ics,ice, NULL, qprd,auxprd,rhsprd, iprq[0], q,aux,r, wnprd,wxdprd,auxfprd );
////            fld->iflxmuscl( ics,ice, NULL,    -1, xqprd,qprd,NULL,    dqdxprd,auxprd,rhsprd, 
////                                     iprq[0], -1, xq,   q,   vklim,   dqdx,   aux,   r,  xprd,wnprd,wxdprd,auxfprd, this );
//        }
//         else
//        {
//            fld->iflx( ics,ice, NULL, qprd,auxprd,rhsprd, iprq[0], q,aux,r, wnprd,wxdprd,auxfprd );
//        }
//         fld->roffset( ics,ice,  1, NULL, rhsprd, NULL, rhsprd );
//         for( iv=0;iv<nv;iv++ )
//        {
//            for( ic=ics;ic<ice;ic++ )
//           {
//               iq= iprq[1][ic];
//               r[iv][iq]+= rhsprd[iv][ic];
//           }
//        }
//
//// Inviscid fluxes - inner faces
//
//         cnf->range( dev->avail(), &ics,&ice );
//         if( ilev == 0 )
//        {
////              fld->iflx(  ics,ice, ifq[0], q,aux,r, ifq[1], q,aux,r, wnc,wxdc, auxf );
////            fld->iflxmuscl( ics,ice, ifq[0],-1, xq,q,vklim,dqdx,qface[0],r, 
////                                     ifq[1],-1, xq,q,vklim,dqdx,qface[1],r, xc,wnc,wxdc,auxf, this );
//        }
//         else
//        {
//              fld->iflx(  ics,ice, ifq[0], q,aux,r, ifq[1], q,aux,r, wnc,wxdc, auxf );
//        }
//
//
////----------------------------------viscous contributions------------------------------------------------------------
//
//         dof->range( dev->avail(), &iqs,&iqe );
//         fld->maux( iqs,iqe, xq,q,dst,dqdx, aux, lmixmax );//;, ng,NULL,NULL,NULL,NULL );  // agruments after ng to be removed
//
//// boundary faces
//
//         for( ig=0;ig<ng;ig++ )
//        {
//            bdf[ig]->range( dev->avail(), &ibs,&ibe );
////            bbj[ig]->mflx( ibs,ibe, xb[ig],qb[ig],auxb[ig],rhsb[ig], iqb[ig], xq,q,aux,r, wnb[ig],wxdb[ig],auxfb[ig] );
//        }
//
//// periodic faces
//
//         prd->range( dev->avail(), &ics,&ice );
//         setv( ics,ice, nv, ZERO,rhsprd );
//         for( iv=0;iv<naux;iv++ )
//        {
//            for( ic=ics;ic<ice;ic++ )
//           {
//               iq= iprq[1][ic];
//               auxprd[iv][ic]= aux[iv][iq];
//           }
//        }
//         coo->coffset( ics,ice, -1.,       iprq[1],xq,                      NULL, xqprd );
////       fld->voffset( ics,ice, -1, iprq[1], xq, NULL, xqprd );
//         fld->voffset( ics,ice, -1, iprq[1],  q, NULL,  qprd );
//         fld->goffset( ics,ice, -1., iprq[1], dqdx, NULL, dqdxprd );
//         //fld->mflx( ics,ice, NULL,     xqprd,qprd,auxprd,dqdxprd,rhsprd,
//         //                    iprq[0], xq,q,aux,dqdx,rhs, xprd,wnprd,wxdprd,auxfprd );
//         fld->roffset( ics,ice,  1, NULL, rhsprd, NULL, rhsprd );
//         for( iv=0;iv<nv;iv++ )
//        {
//            for( ic=ics;ic<ice;ic++ )
//           {
//               iq= iprq[1][ic];
//               rhs[iv][iq]+= rhsprd[iv][ic];
//           }
//        }
//
//// Inviscid fluxes - inner faces
//
//            cnf->range( dev->avail(), &ics,&ice );
//            //fld->mflx( ics,ice, ifq[0], xq,q,aux,dqdx,r, ifq[1], xq,q,aux,dqdx,r, xc,wnc,wxdc,auxf );
//     } 
  }

