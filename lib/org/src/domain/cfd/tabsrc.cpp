
   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::get( cTabData *rfl )
  {
      cTabItem *tmp;

      cDomain::get( rfl );

      tmp= new cTabItem( niter ); rfl->append( "smoothing-iterations",tmp );
      tmp= new cTabItem( npre  ); rfl->append( "pre-smoothing-iterations",tmp );
      tmp= new cTabItem( npost ); rfl->append( "post-smoothing-iterations",tmp );
      tmp= new cTabItem( nstep ); rfl->append( "outer-iterations",    tmp );
      tmp= new cTabItem( nstep_z ); rfl->append( "z-iterations",    tmp );
//      tmp= new cTabItem( freq0 ); rfl->append( "z-frequency",    tmp );
      tmp= new cTabItem( nfre ); rfl->append( "z-nfre",    tmp );
//      tmp= new cTabItem( ibpa ); rfl->append( "z-ibpa",    tmp );
      tmp= new cTabItem( nout  ); rfl->append( "output-frequency",    tmp );
      tmp= new cTabItem( cfl0  ); rfl->append( "cfl-initial",         tmp );
      tmp= new cTabItem( cfl1  ); rfl->append( "cfl-final",           tmp );
      tmp= new cTabItem( dcfl  ); rfl->append( "cfl-increment",       tmp );
      tmp= new cTabItem( unst  ); rfl->append( "unst",                tmp );
      tmp= new cTabItem( dtm   ); rfl->append( "dtm",                 tmp );
      tmp= new cTabItem( ntlv  ); rfl->append( "ntlv",                tmp );
      tmp= new cTabItem( spatial_order  ); rfl->append( "spatial-order",    tmp );
      tmp= new cTabItem( limtype  ); rfl->append( "limiter",                tmp );
      tmp= new cTabItem( limfac  ); rfl->append( "venk-limiter-k",                tmp );
      tmp= new cTabItem( lmixmax  ); rfl->append( "max-mixing-length",                tmp );

      rfl->set( "smoothing-iterations",niter );
      rfl->set( "pre-smoothing-iterations",npre );
      rfl->set( "post-smoothing-iterations",npost );
      rfl->set( "outer-iterations",    nstep );
      rfl->set( "z-iterations",    nstep_z );
//      rfl->set( "z-frequency",     freq0 );
      rfl->set( "z-nfre",          nfre );
//      rfl->set( "z-ibpa",          ibpa );
      rfl->set( "output-frequency",    nout  );
      rfl->set( "cfl-initial",         cfl0  );
      rfl->set( "cfl-final",           cfl1  );
      rfl->set( "cfl-increment",       dcfl  );
      rfl->set( "unst",                unst  );
      rfl->set( "dtm",                  dtm  );
      rfl->set( "ntlv",                ntlv  );
      rfl->set( "spatial-order",       spatial_order  );
      rfl->set( "limiter",             limtype  );
      rfl->set( "venk-limiter-k",   limfac  );
      rfl->set( "max-mixing-length",   lmixmax  );
  }

   void cFdDomain::set( cTabData *rfl )
  {

      cDomain::set( rfl );

      rfl->get( "smoothing-iterations",&niter );
      rfl->get( "pre-smoothing-iterations",&npre );
      rfl->get( "post-smoothing-iterations",&npost );
      rfl->get( "outer-iterations",    &nstep );
      rfl->get( "z-iterations",    &nstep_z );
//      rfl->get( "z-frequency",     &freq0 );
      rfl->get( "z-nfre",     &nfre );
//      rfl->get( "z-ibpa",     &ibpa );
      rfl->get( "output-frequency",    &nout  );
      rfl->get( "cfl-initial",         &cfl0  );
      rfl->get( "cfl-final",           &cfl1  );
      rfl->get( "cfl-increment",       &dcfl  );
      rfl->get( "unst",               &unst  );
      rfl->get( "dtm",                 &dtm  );
      rfl->get( "ntlv",               &ntlv  );
      rfl->get( "spatial-order",         &spatial_order  );
      rfl->get( "limiter",               &limtype  );
      rfl->get( "venk-limiter-k",        &limfac  );
      rfl->get( "max-mixing-length",  &lmixmax  );
  }
