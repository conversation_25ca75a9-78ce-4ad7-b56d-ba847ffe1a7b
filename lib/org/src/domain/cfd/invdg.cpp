
   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::invdg( cAu3xView<Real>& r, cAu3xView<Real>& d )
  {
      Int iqs,iqe;
      Int iv,iq;

//     #pragma acc enter data copyin ( sdq[0:nv*nq],slhsa[0:nlhs*nq], sres[0:nv*nq] )
//     #pragma acc enter data copyin ( this)
//      start_acc_device();

// inversion
      dof->range( dev->getrank(), &iqs,&iqe );
      fld->invdg( iqs,iqe, lhsa,res );

     #pragma acc parallel loop \
      present(sres[0:nv*nq],sdq[0:nv*nq],this) \
      default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            //d[iv][iq]+= 0.8*r[iv][iq];
            sdq[ADDR(iv,iq,nq)]+= 0.8*sres[ADDR(iv,iq,nq)];
        }
     }
 
//     #pragma acc exit data copyout ( sdq[0:nv*nq],slhsa[0:nlhs*nq], sres[0:nv*nq] )
//     #pragma acc exit data copyout ( this)
//      exit_acc_device();
  }
