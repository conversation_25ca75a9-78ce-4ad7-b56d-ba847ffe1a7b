
   using namespace std;

#  include <domain/cfd/domain.h>
#  include <domain/cfd/meshconv.h>

   void cFdDomain::postprocess()
  {
      Int          i,j;
/*    cout << "INSIDE POSTPROCESS\n";
      for( i=0;i<nq;i++ )
     {
         for( j=0;j<nv;j++ )
        {
            cout << q[j][i]<<" ";
        }
         cout << "\n";
     }*/
      //tecplot();
//      vtk();
//      tecplot();
      fld->redim_cpu(0,nq,q);
#ifdef CGNS
      output_cgns();
#else
      vtk();
#endif
      if(nfre>0)
     {
         readzsetup();
         for(Int i=0; i<nfre; i++)
        {
//           if(dev->getname()=="I07R"&&i==3) for(Int ip=0; ip<5; ip++) vtk_z(i,ip);
//           if(dev->getname()=="I06S"&&i==2) for(Int ip=0; ip<10; ip++) vtk_z(i,ip);
//           if(dev->getname()=="block1")for(Int ip=0; ip<60; ip++) vtk_z(i,ip);
//           if(dev->getname()=="block2")for(Int ip=0; ip<90; ip++) vtk_z(i,ip);
           vtk_z(i, 0);
//           vtk_z(i, 1);
//           vtk_z(i, 2);
//           vtk_z(i, 3);
//           vtk_z(i, 4);
//        vtk_z(i, 2);
           tecplot_z(i, 0);
//           tecplot_z(i, 1);
        }
     }      
  }

   void cFdDomain::postprocess( Int it )
  {
      cout << "nothing is here\n";
  }

   void cFdDomain::ppostprocess()
  {
      Int          i,j;
/*    cout << "INSIDE POSTPROCESS\n";
      for( i=0;i<nq;i++ )
     {
         for( j=0;j<nv;j++ )
        {
            cout << q[j][i]<<" ";
        }
         cout << "\n";
     }*/

//    loadpre();
      cell2node();
  }

   void cFdDomain::outputjl09()
  {
//      /*cout << "output jl09files in cFdDomain=======================\n";
//      cout << "nx " << nx << "\n";
//      cout << "np " << np << "\n";
//      cout << "sxp " << sxp << "\n";
//      for(Int iek=0; iek<MxNEk; iek++) 
//     {
//         cout << "ne " << iek << " " << ne[iek] << "\n";
//     }
//      cout << "ng " << ng << "\n";
//      for(Int ig=0; ig<ng; ig++)
//     {
//         cout << "bgnm " << ig << " " << bgnm[ig] << "\n";
//         for(Int ick=0; ick<MxNCk; ick++)
//        {
//            cout << nb[ig][ick] << "\n";
//        }   
//     }
//      cout << "siep " << siep << "\n";
//      cout << "sibp " << sibp << "\n";
//      cout << "omega " << omega << "\n";*/
//
//      string fnm;
//      cMeshconv conv;
//      conv.setgrid( nx, np, sxp, ne, ng, bgnm, nb, siep, sibp );
//      fnm= dev->getcpath()+"/"+dev->getname()+".plt";
//      cout << "output plt file to " << fnm << "\n";
//      conv.writeplt( fnm, ((cAnnCosystem*)coo)->getasct(), 1);
//
//      saveunk();
  }

   void cFdDomain::saveunk()
  {
//      Int ntmp, ie, je, iek, me, ip, jp, iv, ibk, ig;
//      Real *swrke,*wrke[12];
//      Real *qp[MxNSk], *auxp[100], *wp[1], m;
//
//      for(iv=0; iv<MxNSk; iv++)  qp[iv] = NULL;
//      for(iv=0; iv<100; iv++)    auxp[iv] = NULL;
//
//      wp[0] = new Real [np];
//      for(iv=0; iv<nv; iv++)
//     {
//         qp[iv] = new Real [np];
//     }
//      for(iv=0; iv<naux; iv++)
//     {
//         auxp[iv] = new Real [np];
//     }
//
//      for(iv=0; iv<nv; iv++)
//     {
//         setv( 0,np, nv, ZERO, qp );
//         setv( 0,np,naux, ZERO, auxp );
//     }
//      setv( 0,np,  1, ZERO, wp );
//
//
//      ntmp=0;
//      for(iek=0; iek<nek; iek++)
//     {
//         if(ne[iek]>0)
//        {
//            for(ie=0; ie<ne[iek]; ie++) 
//           {
//               ntmp++; 
//           }
//        }
//     }
//
//      wq[0] = new Real [ntmp];
//      for(ie=0; ie<ntmp; ie++) wq[0][ie] = ZERO;
//
//      me = -1;
//      for( iek=0;iek<nek;iek++ )
//     {
//         me= max( me,ne[iek] );
//     }
//      for( ibk=0;ibk<nbk;ibk++ )
//     {
//         me= max( me,nc[ibk] );
//     }
//      for( ig=0;ig<ng;ig++ )
//     {
//         for( ibk=0;ibk<nbk;ibk++ )
//        {
//            me= max( me,nb[ig][ibk] );
//        }
//     }
//      //cout << me << "\n";
//      swrke= new Real[ me*nx*(nx+1) ]; subv( nx*(nx+1),me, swrke,wrke );
//      for( iek=0;iek<nek;iek++ )
//     {
//         elm[iek]->volume( 0, ne[iek], iep[iek], nx, xp, 
//                           ieq[iek], wq,xq, wrke, coo );
//     }
//
//      for(iek=0; iek<nek; iek++)
//     {
//         if(ne[iek]>0)
//        {
//            for(ip=0; ip<nep[iek]; ip++)
//           {
//               for(ie=0; ie<ne[iek]; ie++)
//              {
//                  jp = iep[iek][ip][ie];
//                  je = ieq[iek][0][ie];
//
//                  m = aux[0][je]*wq[0][je];          
//
//                  //density
//                  qp[0][jp]+= aux[0][je]*wq[0][je];
//
//                  //momentum, rho*u, rho*v, rho*w
//                  qp[1][jp]+= q[0][je]*m;          
//                  qp[2][jp]+= q[1][je]*m;          
//                  qp[3][jp]+= q[2][je]*m;          
//
//                  //total energy 
//                  qp[4][jp]+= (aux[3][je] - q[4][je]/aux[0][je] ) * m;
//
//                  wp[0][jp]+= wq[0][je];
//              }
//           }    
//        }
//     }
//
//      for(iv=0; iv<nv; iv++)
//     {
//         for(ip=0; ip<np; ip++)
//        {
//            qp[iv][ip]/=wp[0][ip];
//        }
//     }
//
//     /*for(iv=0; iv<naux; iv++)
//     {
//         for(ip=0; ip<np; ip++)
//        {
//            auxp[iv][ip]/=wp[0][ip];
//        }
//     }*/
//
//      //read rotational speed from the "setups" file
//      cTabData *tab;
//      string syn=";()";
//      Int    iarg=0,argc=0;
//      string *argv=NULL;
//
//      tab= new cTabData();
//      cDevice *tmpdev= this->device();
//      tmpdev->get( tab );
//      string misc;
//      tab->get( "misc",&misc );
//      parse( misc,&argc,&argv,syn );
//      iarg=inlst( string("frame-speed"),argc,argv );
//      omega=0;
//      if( iarg != -1 )
//     {
//         conv( argv[iarg+1],&omega );
//     }
//      delete tab; tab=NULL;
//
//
//      float *unkno, *vrt, *dis, roref, uref, vrtref, disref;
//      float tmpgam;
//      Real tmprg;
//      int npoin, itime, dtstr, nstag, ivfl, iturbm, iret, ivisc;
//      const char *cname;
//      int lname;
//      float tmpomega;
//      string fnm;
//    
// 
//      tmpgam = 1.4;
//      tmprg = 287;
// 
//      fnm= dev->getcpath()+"/"+dev->getname()+".unk";
//      cout <<"output unk file to " << fnm << "\n";
//      if(unst)
//     {
//         cout << "The time level is " << tm << "\n";
//     }
//      cname= fnm.c_str();
//      lname= fnm.length();
//      cout << "The frame speed of " << dev->getname() << " is " << omega << "\n";
// 
//      unkno = new float [5*np];
//      vrt = new float [np];
//      dis = new float [np];
// 
//      ntmp=0;
//      for(ip=0; ip<np; ip++)
//     {
//        unkno[ntmp++] = qp[0][ip];
//        unkno[ntmp++] = qp[1][ip]/qp[0][ip] * fld->units(0)/100;
//        unkno[ntmp++] = qp[2][ip]/qp[0][ip] * fld->units(0)/100;
//        unkno[ntmp++] = qp[3][ip]/qp[0][ip] * fld->units(0)/100;
//        unkno[ntmp++] = qp[4][ip]/qp[0][ip];
//        vrt[ip] = 0;
//        dis[ip] = 0;
//     }
//
//      npoin    = np;
//      itime    = 1;
//      dtstr    = 0;
//      roref    = 1;
//      uref     = 100;
//      vrtref   = 1;
//      disref   = 1;
//      nstag    = 1;
//      ivisc    = -1;
//      iturbm   = 0;
//      ivfl     = 0;
//      iret     = 0;
//      tmpomega = omega/100;
////      outunk(&lname, cname, unkno, vrt,dis, &npoin, &itime, &dtstr,
////            &tmpgam, &roref, &uref, &vrtref, &disref, &nstag, &(tmpomega),
////            &ivisc, &iturbm, &ivfl, &iret);
//
//
//      delete[] swrke; swrke= NULL;
//      for(iv=0; iv<nv; iv++)
//     {
//         delete[] qp[iv]; qp[iv]=NULL;
//     }
//      for(iv=0; iv<naux; iv++)
//     {
//         delete[] auxp[iv]; auxp[iv]=NULL;
//     }
//      delete[] wp[0]; wp[0]=NULL;
//      delete[] wq[0]; wq[0]=NULL;
//
//      delete[] unkno; unkno=NULL;
//      delete[] vrt; vrt=NULL;
//      delete[] dis; dis=NULL;
//
  }

