   using namespace std;

#  include <domain/cfd/domain.h>
#  include <assert.h>

   void cFdDomain::rebuild( Int id )
  {

      cPdata      *dofl,*ptsl;
      pickle_t     buf;
      size_t       len,l;
      string       fnme;
      Int          i,j, ig, ib, ix, ip, iq, iv;
      FILE        *f;
      Real        *sxpl=NULL,*sxql=NULL,*sql=NULL,*sauxl=NULL;
      //Real       **xpl=NULL,**xql=NULL,**ql=NULL,**auxl=NULL;
      cAu3xView<Real>  xpl, xql,ql,auxl;

      //cout << "the gas is "<<fld<<"\n";
      //cout << "the coordinate system is "<<coo<<"\n";

      cTabData *tab;
      string syn=";()";
      Int    iarg=0,argc=0;
      string *argv=NULL;

      tab= new cTabData();
      cDevice *tmpdev= this->device();
      tmpdev->get( tab );
      string misc;
      tab->get( "misc",&misc );
      parse( misc,&argc,&argv,syn );
      iarg=inlst( string("frame-speed"),argc,argv );
      omega=0;
      if( iarg != -1 )
     {
         conv( argv[iarg+1],&omega );
     }
      iarg=inlst( string("dtm"),argc,argv );
      dtm=0;
      if( iarg != -1 )
     {
         conv( argv[iarg+1],&dtm );
     }
      delete tab; tab=NULL;
      //omega = omega/100;
      //fld->nondim_time(&dtm);
      cout << "The rotational speed is " << omega << " the time step is " << dtm << "\n";

      fnme= dev->getcpath();
      fnme= fnme+"/"+dev->getname()+".restart.q."+strc(level())+"."+strc(id);
      //fnme= fnme+"/"+dev->getname()+".restart.q."+strc(level())+"."+strc(id) + ".bck";
      f= fopen(fnme.c_str(),"r");
      cout << "open restart file " << fnme << "\n";

      l= fread( &tm,1,sizeof(tm),f );
      l= fread( &len,1,sizeof(len),f );
      buf= new pickle_v[len];
      l= fread(  buf,1,        len,f );

      //cout << "the physical time is " << tm << "\n";

      len=0;
      dofl= new cPdata( dev );
      ptsl= new cPdata( dev );

      dofl->unpickle( &len,buf );
      ptsl->unpickle( &len,buf );
      delete[] buf;buf= NULL; len=0;

      //cout << "NX   "<<  nx << " \n";
      //cout << "NV   "<<  nv << " \n";
      //cout << "NAUX "<<naux << " \n";

      //cout << "reading ...\n";
      ptsl->read( nx,   &sxpl,f ); //cout << "sxpl  "<<sxpl<<"\n";
      dofl->read( nx,   &sxql,f ); //cout << "sxql  "<<sxql<<"\n";
      dofl->read( nv,    &sql,f ); //cout << "sql   "<<sql<<"\n";
      dofl->read( naux,&sauxl,f ); //cout << "sauxl "<<sauxl<<"\n";
      //cout << "done reading ...\n";

      fclose(f);

      //rotate the nodes of the mesh
      //coo->toffset( 0,np, 1, tm*omega, xp );
      


      if( !sq )
     {
         nq= dofl->gsize();
         sxq= new Real[  nx*nq]; xq.subv(   nx,  nq,   sxq   );
         sq=  new Real[  nv*nq]; q.subv(    nv,  nq,   sq    );
         saux=new Real[naux*nq]; aux.subv(  naux,nq,    saux );
     }
      else
     {
         assert( nq == dofl->gsize() );
     }

      //cout << "sxpl  "<<sxpl<<"\n";
      //cout << "sxql  "<<sxql<<"\n";
      //cout << "sql   "<<sql<<"\n";
      //cout << "sauxl "<<sauxl<<"\n";

      //xql = new Real *[nx];
      //ql=   new Real*[nv];
      //auxl= new Real*[naux];
      xql.subv(   nx,dofl->size(),   sxql );
      ql.subv(    nv,dofl->size(),   sql  );
      auxl.subv( naux,dofl->size(),  sauxl );

      dofl->makeglobal(  nx,xq, xql );
      dofl->makeglobal(  nv,q,ql );
      dofl->makeglobal(naux,aux,auxl );

//    fld->redim( 0,nq, ql );

      //cout << "destroy ...\n";
      ptsl->destroy(   &sxpl );
      dofl->destroy(   &sxql );
      dofl->destroy(    &sql );
      dofl->destroy(  &sauxl );


      //cout << "assembling\n";
      delete ptsl; ptsl=NULL;
      delete dofl; dofl=NULL;
//      delete[] ql; ql=NULL;
//      delete[] auxl; auxl=NULL;
//      delete[] xql; xql=NULL;

      for(ig=0; ig<ng; ig++)
     {
         fnme= dev->getcpath();
         fnme= fnme+"/"+dev->getname()+".restart.b."+bgnm[ig]+"."+strc(level())+"."+strc(id);
         //fnme= fnme+"/"+dev->getname()+".restart.b."+bgnm[ig]+"."+strc(level())+"."+strc(id) + ".bck";
         cout << "open restart file " << fnme << "\n";
         rebuildb(fnme.c_str(), id, ig, ng);
     }

      Int ncpu;
      ncpu=dev->getncpu();
      if(id==(ncpu-1))
     {
         Real y0[3], y1[3], d, dmin, ypmin, ypmax, yp, ypmm;
         cKdTree *kdt;

         fnme= dev->getcpath();
         fnme= fnme+"/"+dev->getname()+"."+strc(level())+".solution.bin";
         cout << "output solutions to " << fnme << "\n";

        
         FILE *f;
         Int idum;
         Real rdum;
         string sdum;
         size_t i;

         f= fopen(fnme.c_str(),"w");
         i=fwrite( &nx,1,  sizeof(idum),f );
         i=fwrite( &nv,1,  sizeof(idum),f );
         i=fwrite( &nq,1,  sizeof(idum),f );
         i=fwrite( &naux,1,sizeof(idum),f );

         i=fwrite( sxq, nx*nq,   sizeof(rdum),f );
         i=fwrite( sq,  nv*nq,   sizeof(rdum),f );
         i=fwrite( saux,naux*nq, sizeof(rdum),f );

         i=fwrite( &ng,   1, sizeof(idum),f );
         //i=fwrite( bgnm, ng, sizeof(sdum), f);
         i=fwrite( nbb,  ng, sizeof(idum), f);
         for(ig=0; ig<ng; ig++)
        {
            i=fwrite( sxb[ig],   nx*nbb[ig], sizeof(rdum), f);
            i=fwrite( sqb[ig],   nv*nbb[ig], sizeof(rdum), f);
            i=fwrite( sauxb[ig],  2*nbb[ig], sizeof(rdum), f);
        } 
         fclose(f);


        /*ofstream fle;
         fle.setf( ios_base::scientific );
         fle.width( 16 );
         fle.precision( 12 );
         fle.open(fnme.c_str()); 
         fle << nx << " " << nv << " " << nq << " " << naux << "\n";
         for(iq=0; iq<nq; iq++)
        {
            for(ix=0; ix<nx; ix++)
           {
               fle << xq[ix][iq] << " ";       
           }
            for(iv=0; iv<nv; iv++)
           {
               fle << q[iv][iq] << " ";
           }
            for(iv=0; iv<naux; iv++)
           {
               fle << aux[iv][iq] << " ";
           }
            fle << "\n";
        }

         fle << ng << "\n";
         for(ig=0; ig<ng; ig++)
        {
            fle << bgnm[ig] << "\n";
            fle << nbb[ig] << "\n";
            for(ib=0; ib<nbb[ig]; ib++)
           {
               for(ix=0; ix<nx; ix++)
              {
                  fle << xb[ig][ix][ib] << " ";
              }
               for(iv=0; iv<nv; iv++)
              {
                  fle << qb[ig][iv][ib] << " "; 
              }
               for(iv=0; iv<2; iv++)
              {
                  fle << auxb[ig][iv][ib] << " ";
              }
               fle << "\n";
           }
        }
         fle.close();*/

     }

//rebuild frequency part
      if(nfre>0) rebuild_z(id);
  }

   void cFdDomain::rebuildb( string fnm , Int id, Int ig, Int ng )
  {
      Int          ncpu;
      Int           i,j;
      cPdata       *dofb,*ptsl;
      pickle_t     buf;
      FILE           *f;
      size_t        len,l;  
      Int nqlb;  
      Real *gsxq=NULL;
      Real *gsq=NULL;
      Real *gsq0=NULL;
      Real *gsaux=NULL;
      Real ql;
      Real        *sxplb=NULL;
      Real       *sauxlb=NULL;
      Real       *sqlb=NULL;
      Real       *sxqlb=NULL;
      cAu3xView<Real> xbl,auxlb,qlb;

      f=  fopen(fnm.c_str(),"r");

      if(!f) 
     {
        cout << "Errors: can not read files " << fnm << "\n";
        return;
     }

      l= fread( &tm,1,sizeof(tm),f );
      l= fread( &len,1,sizeof(len),f );
      buf= new pickle_v[len];
      l= fread(  buf,1,        len,f );

      len=0;
      dofb= new cPdata( dev );
      dofb->unpickle( &len,buf );
      delete[] buf;buf= NULL; len=0;
      dofb->read( nx,   &sxqlb,f );
      dofb->read( nv,    &sqlb,f );
      dofb->read( naux,&sauxlb,f );
      fclose(f);

      if( id==0)      // Questo controllo non lo riesco a gestire
     {
         sxb[ig]=new Real[nx*nbb[ig]];     xb[ig].subv( nx,nbb[ig], sxb[ig] );
         sqb[ig]= new Real[nv*nbb[ig]];    qb[ig].subv( nv,nbb[ig], sqb[ig] );
         sauxb[ig]=new Real[naux*nbb[ig]]; auxb[ig].subv( naux,nbb[ig], sauxb[ig] );
     }

      nqlb= dofb->size();
//      qlb= new Real*[nv];
//      auxlb= new Real*[naux];
//      xbl= new Real*[nx];    
      xbl.subv( nx,nqlb, sxqlb );
      qlb.subv( nv,nqlb, sqlb );    // nql is local on Vector and not on global like nq was.
      auxlb.subv( naux,nqlb, sauxlb  );
      dofb->makeglobal(  nx,xb[ig],xbl);
      dofb->makeglobal(  nv,qb[ig],qlb);
      dofb->makeglobal(  naux,auxb[ig],auxlb );

      dofb->destroy(   &sxqlb );
      dofb->destroy(    &sqlb );
      dofb->destroy(  &sauxlb );


      delete dofb; dofb=NULL;
//      delete[] qlb; qlb=NULL;
//      delete[] auxlb; auxlb=NULL; 
//      delete[] xbl; xbl=NULL;

  }

  /*void cFdDomain::rebuildunst( Int id, Int it0 )
  {
      Int ig, nbnd, jg;
      string fnme, bndnm[100];
      ifstream fle;
      bool bsaveq;

      fnme= dev->getcpath();
      fnme = fnme + "/" + dev->getname() + ".ubnd";      
      fle.open(fnme.c_str());

      if(!fle.good())
     {
         cout << "Error: can not open file " << fnme << "\n";
         return;
     }

      nbnd=0;
      fle >> bsaveq;
      cou << "rebuild the whole solution? " << bsaveq << "\n";
      fle >> nbnd;
      cout << "number of boundaries to rebuild " << nbnd << "\n";
      for(ig=0; ig<nbnd; ig++)
     {
         fle >> bndnm[ig];   
         cout << "boundary " << ig << " " << bndnm[ig] << "\n";
     }
      fle.close();

      for(ig=0; ig<ng; ig++)
     {
         bool btmp;
         btmp = false;
         for(jg=0; jg<nbnd; jg++)
        {
            if(bgnm[ig] == bndnm[jg])
           {
               btmp = true;
           }
        }
         if(!btmp) continue;

         fnme= dev->getcpath();
         fnme= fnme+"/ubnd/"+dev->getname()+".restart.b."+bgnm[ig]+"."+strc(level())+"."+strc(id) + "." + strc(it0);
         //fnme= fnme+"/"+dev->getname()+".restart.b."+bgnm[ig]+"."+strc(level())+"."+strc(id) + ".bck";
         cout << "open restart file " << fnme << "\n";
         rebuildb(fnme.c_str(), id, ig, ng);
     }

      Int ncpu;
      ncpu=dev->getncpu();
      if(id==(ncpu-1))
     {
         Real y0[3], y1[3], d, dmin, ypmin, ypmax, yp, ypmm;

         fnme= dev->getcpath();
         fnme= fnme+"/ubnd/"+dev->getname()+"."+strc(level())+ "." + strc(it0) + ".bndsolution.bin";
         cout << "output solutions to " << fnme << "\n";

        
         FILE *f;
         Int idum, *tmpnbb, *hlp, jg;
         Real rdum;
         string sdum;
         size_t i;

         tmpnbb = new Int [ng];
         hlp = new Int [ng];

         for(jg=0; jg<nbnd; jg++)
        {
            for(ig=0; ig<ng; ig++)
           {
               if(bndnm[jg] == bgnm[ig])
              {
                  hlp[jg] = ig;
                  tmpnbb[jg] = nbb[ig];
              }
           }
        } 

         f= fopen(fnme.c_str(),"w");

         i=fwrite( &nbnd,   1, sizeof(idum),f );
         //i=fwrite( bgnm, ng, sizeof(sdum), f);
         i=fwrite( tmpnbb,  nbnd, sizeof(idum), f);
         for(jg=0; jg<nbnd; jg++)
        {
            ig = hlp[jg]; 
            i=fwrite( sxb[ig],   nx*nbb[ig], sizeof(rdum), f);
            i=fwrite( sqb[ig],   nv*nbb[ig], sizeof(rdum), f);
            i=fwrite( sauxb[ig],  2*nbb[ig], sizeof(rdum), f);
        } 
         delete[] tmpnbb; tmpnbb=NULL;
         delete[] hlp;    hlp=NULL;


         fclose(f);
     }
  }*/

   void cFdDomain::rebuildunst( Int id, Int it0 )
  {
      cPdata      *dofl,*ptsl;
      pickle_t     buf;
      size_t       len,l;
      string       fnme;
      Int          i,j, ig, ib, ix, ip, iq, iv, jg;
      FILE        *f;
      Real        *sxpl=NULL,*sxql=NULL,*sql=NULL,*sauxl=NULL;
      cAu3xView<Real> xpl,xql,ql,auxl;
      bool         bsaveq;
      string       bndnm[100];
      Int          nbnd;
      ifstream     fle;

      fnme= dev->getcpath();
      fnme = fnme + "/" + dev->getname() + ".usave";      
      fle.open(fnme.c_str());

      if(!fle.good())
     {
         cout << "Error: can not open file " << fnme << "\n";
         return;
     }

      nbnd=0;
      bsaveq = false;

      fle >> bsaveq;
      cout << "rebuild the whole solution? " << bsaveq << "\n";
      fle >> nbnd;
      cout << "number of boundaries to rebuild " << nbnd << "\n";
      for(ig=0; ig<nbnd; ig++)
     {
         fle >> bndnm[ig];   
         cout << "boundary " << ig << " " << bndnm[ig] << "\n";
     }
      fle.close();

      if(bsaveq)
     {
         fnme= dev->getcpath();
         fnme= fnme+"/unstsol/"+dev->getname()+".restart.q."+strc(level())+"."+strc(id) + "." + strc(it0);
     //fnme= fnme+"/"+dev->getname()+".restart.q."+strc(level())+"."+strc(id) + ".bck";
         f= fopen(fnme.c_str(),"r");

         if(!f)
        {
            cout << "Error: can not open file " << fnme 
                 << ", Do you read an unsteady restart file that does not exists?\n";
            exit(0);
        }
         cout << "open restart file " << fnme << "\n";

         l= fread( &tm,1,sizeof(tm),f );
         l= fread( &len,1,sizeof(len),f );
         buf= new pickle_v[len];
         l= fread(  buf,1,        len,f );

         //cout << "the physical time is " << tm << "\n";
   
         len=0;
         dofl= new cPdata( dev );
         ptsl= new cPdata( dev );

         dofl->unpickle( &len,buf );
         ptsl->unpickle( &len,buf );
         delete[] buf;buf= NULL; len=0;

         ptsl->read( nx,   &sxpl,f ); //cout << "sxpl  "<<sxpl<<"\n";
         dofl->read( nx,   &sxql,f ); //cout << "sxql  "<<sxql<<"\n";
         dofl->read( nv,    &sql,f ); //cout << "sql   "<<sql<<"\n";
//         dofl->read( naux,&sauxl,f ); //cout << "sauxl "<<sauxl<<"\n";

         fclose(f);

         if( !sq )
        {
            nq= dofl->gsize();
            sxq= new Real[  nx*nq];   xq.subv(   nx,nq,   sxq );
            sq=  new Real[  nv*nq];   q.subv(    nv,nq,   sq  );
//            saux=new Real[naux*nq]; aux= new Real*[naux]; subv( naux,nq, saux,aux );
        }
         else
        {
            assert( nq == dofl->gsize() );
        }


//         xql = new Real *[nx];
//         ql=   new Real*[nv];
//         auxl= new Real*[naux];
         xql.subv(   nx,dofl->size(),   sxql );
         ql.subv (   nv,dofl->size(),   sql  );
//         subv( naux,dofl->size(), sauxl,auxl );

         dofl->makeglobal(  nx,xq, xql );
         dofl->makeglobal(  nv,q,ql );
//         dofl->makeglobal(naux,aux,auxl );

   //    fld->redim( 0,nq, ql );

         //cout << "destroy ...\n";
         ptsl->destroy(   &sxpl );
         dofl->destroy(   &sxql );
         dofl->destroy(    &sql );
//         dofl->destroy(  &sauxl );


         //cout << "assembling\n";
         delete ptsl; ptsl=NULL;
         delete dofl; dofl=NULL;
//         delete[] ql; ql=NULL;
//         delete[] auxl; auxl=NULL;
//         delete[] xql; xql=NULL;
     }

      if(nbnd>0)
     {
         for(ig=0; ig<ng; ig++)
        {
            bool btmp;
            btmp = false;
            for(jg=0; jg<nbnd; jg++)
           {
               if(bgnm[ig] == bndnm[jg])
              {
                  btmp = true;
              }
           }
            if(!btmp) continue;
   
            fnme= dev->getcpath();
            fnme= fnme+"/unstsol/"+dev->getname()+".restart.b."+bgnm[ig]+"."+strc(level())+"."+strc(id) + "." + 
                  strc(it0);
            //fnme= fnme+"/"+dev->getname()+".restart.b."+bgnm[ig]+"."+strc(level())+"."+strc(id) + ".bck";
            cout << "open restart file " << fnme << "\n";
            rebuildb(fnme.c_str(), id, ig, ng);
        }
     }

      Int ncpu;
      ncpu=dev->getncpu();
      if(id==(ncpu-1))
     {
         FILE *f;
         Int idum;
         Real rdum;
         string sdum;
         size_t i;

         if(bsaveq)
        {

            fnme= dev->getcpath();
            fnme= fnme+"/unstsol/"+dev->getname()+"."+strc(level())+".q.bin" + "." + strc(it0);

            cout << "output solutions to " << fnme << "\n";
   
            f= fopen(fnme.c_str(),"w");

            if(!f)
           {
               cout << "Error: can not open file " << fnme 
                    << ", Do you read an unsteady restart file that does not exists?\n";
           }

            i=fwrite( &nx,1,  sizeof(idum),f );
            i=fwrite( &nv,1,  sizeof(idum),f );
            i=fwrite( &nq,1,  sizeof(idum),f );
//            i=fwrite( &naux,1,sizeof(idum),f );
   
            i=fwrite( sxq, nx*nq,   sizeof(rdum),f );
            i=fwrite( sq,  nv*nq,   sizeof(rdum),f );
//            i=fwrite( saux,naux*nq, sizeof(rdum),f );

            fclose(f);

            //output iek to iq coorespondence
            fnme= dev->getcpath();
            fnme= fnme+"/unstsol/"+dev->getname()+"."+strc(level())+".e2q";
            ofstream ofle;
            ofle.open(fnme.c_str());
            for(Int iek=0; iek<6; iek++)
           {
              if(ne[iek]>0)
             {
                 for(Int ie=0; ie<ne[iek]; ie++)
                {
                    iq = ieq[iek][0][ie];
                //    ofle << iek << " " << ie << " " << iq << " " << wq[0][iq] << "\n";
                    ofle << iek << " " << ie << " " << iq << "\n";
                }
             }
           }
            ofle.close();
        }

         if(nbnd>0)
        {
            Int *tmpnbb, *hlp, jg;

            tmpnbb = new Int [ng];
            hlp = new Int [ng];
   
            for(jg=0; jg<nbnd; jg++)
           {
               for(ig=0; ig<ng; ig++)
              {
                  if(bndnm[jg] == bgnm[ig])
                 {
                     hlp[jg] = ig;
                     tmpnbb[jg] = nbb[ig];
                 }
              }
           } 
   
            fnme= dev->getcpath();
            fnme= fnme+"/unstsol/"+dev->getname()+"."+strc(level())+ ".qb.bin" + "." + strc(it0);
            cout << "output solutions to " << fnme << "\n";

            f= fopen(fnme.c_str(),"w");
            if(!f)
           {
               cout << "Error: can not open file " << fnme 
                    << ", Do you read an unsteady restart file that does not exists?\n";
           }

            i=fwrite( &nbnd,   1, sizeof(idum),f );
            //i=fwrite( bgnm, ng, sizeof(sdum), f);
            i=fwrite( tmpnbb,  nbnd, sizeof(idum), f);
            for(jg=0; jg<nbnd; jg++)
           {
               ig = hlp[jg]; 
               i=fwrite( sxb[ig],   nx*nbb[ig], sizeof(rdum), f);
               i=fwrite( sqb[ig],   nv*nbb[ig], sizeof(rdum), f);
               i=fwrite( sauxb[ig],  2*nbb[ig], sizeof(rdum), f);
           } 
            delete[] tmpnbb; tmpnbb=NULL;
            delete[] hlp;    hlp=NULL;
   
            fclose(f);
        }
     }
  }
