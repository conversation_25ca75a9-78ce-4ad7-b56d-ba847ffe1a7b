
   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::maverage( bool bposix )
  {
      Int ig,ip,ick,jp,il,ix,iv,ib;
      Int ibs,ibe, irnk;
      Real w;
      string path, bnm;
      bool bwrite;

      //only do massa averaging on the boundary if it is 3D in cylindrical system
      if(nx!=3 || coo->gettype()!=cosystem_ann) return;

      path= dev->getcpath();
      path= path+"/"+dev->getname();
      irnk= dev->getrank();

      bwrite = (irnk==0);
      for( ig=0;ig<ng;ig++ )
     {
         if( bbj[ig]->layers() )
        {
            bdf[ig]->range( dev->getrank(), &ibs,&ibe );
                 if(bgnm[ig]=="INLET"          || bgnm[ig]=="EXIT")      bnm = bgnm[ig];
            else if(bgnm[ig]=="FREE.INLET"     || bgnm[ig]=="FREE.EXIT") bnm = bgnm[ig];
            else if(bgnm[ig]=="WEX"            || bgnm[ig]=="WIN")       bnm = bgnm[ig];
            else if(bgnm[ig]=="SPEX"           || bgnm[ig]=="SPIN")      bnm = bgnm[ig];
            else if(bgnm[ig]=="MEX"            || bgnm[ig]=="MIN")       bnm = bgnm[ig];
            else if(bgnm[ig]=="NREX"           || bgnm[ig]=="NRIN")      bnm = bgnm[ig];
            else if(bgnm[ig]=="nr_inlet"       || bgnm[ig]=="nr_exit")   bnm = bgnm[ig];
            else if(bgnm[ig].substr(0,4)=="FREX")                    bnm = bgnm[ig];
            else                                                     bnm = "";
           /* if(nfre>0)
           {
               Int iqs, iqe;
               dof->range( dev->getrank(), &iqs,&iqe );
               for(Int ifre=0; ifre<nfre; ifre++)
              {
                  fld->cnsv_z( iqs,iqe, q, z_re[ifre], zc_re[ifre] );
                  fld->cnsv_z( iqs,iqe, q, z_im[ifre], zc_im[ifre] );
              }
               bbj[ig]->maverage( ibs,ibe, iqb[ig], ibql[ig], wnb[ig], xb[ig], q,aux, nfre, zc_re, zc_im, z_re, z_im,
                                  nbl[ig], dev, bposix, path, bnm, bwrite );
               //bbj[ig]->maverage( ibs,ibe, iqb[ig],ibql[ig], wnb[ig],xb[ig],q,aux, nbl[ig],dev, bposix, path, bnm, bwrite);
           }
            else
           {
               bbj[ig]->maverage( ibs,ibe, iqb[ig],ibql[ig], wnb[ig],xb[ig],q,aux, nbl[ig],dev, bposix, path, bnm, bwrite);
           }*/

//            if(nfre>0)
//           {
//               bbj[ig]->bdflux( ibs,ibe, iqb[ig], ibql[ig], wnb[ig], xb[ig], q,aux, nfre, z_re, z_im, nbl[ig], dev );
//           }
            bbj[ig]->maverage( ibs,ibe, iqb_view[ig],ibql_view[ig], wnb[ig],xb[ig],q,aux, nbl[ig],dev, bposix, path, bnm, bwrite);
        }
     }


      if(nfre>0 && nx==3)
     { 
         fftbnd();
     }  
      else
     {
//         for(ig=0; ig<ng; ig++)
//        {
//            if(bbj[ig]->layers())
//           {
//               if(bgnm[ig]=="NRIN"||bgnm[ig]=="NREX")
//              {
//                  bdf[ig]->range( dev->getrank(), &ibs,&ibe );
//                  bbj[ig]->fft( ibs, ibe, iqb[ig], ibql[ig], wnb[ig], xb[ig], q, aux, dev, 5);
//              }
//           }
//        }
     }   
  }


   void cFdDomain::fftbnd()
  {
//      Int ig, ibs, ibe;
//      Int tmpsend, tmpnrbc;
//
//      for( ig=0;ig<ng;ig++ )
//     {
//         if( bbj[ig]->layers() )
//        {
//             
//            bdf[ig]->range( dev->getrank(), &ibs,&ibe );
//            if(bgnm[ig]=="NRIN" || bgnm[ig]=="NREX"||
//               bgnm[ig]=="NRMI" || bgnm[ig]=="NRME"||
//               bgnm[ig]=="FREE.OUTFAN"||
//               bgnm[ig]=="nr_inlet" || bgnm[ig]=="nr_exit")
//           {
//               //if NRBC is used, 10 harmonics of used for the FFT
//               bbj[ig]->fft( ibs, ibe, iqb[ig], ibql[ig], wnb[ig], xb[ig], q, aux, dev, 20 );
//           }
//            else
//           {
//               //cout << dev->getname() << " " << ig << " "<<bgnm[ig]<< " FFT " << bbj[ig] << " ===============\n";
// 
//               tmpsend = bbj[ig]->getnfre_send();
//               tmpnrbc = max(tmpsend, 5);
//               bbj[ig]->fft( ibs, ibe, iqb[ig], ibql[ig], wnb[ig], xb[ig], q, aux, dev, tmpnrbc );
//           }
//            //cout << "entering fftlin for boundary " << bgnm[ig] << "\n";
//            bbj[ig]->fftlin( ibs, ibe, iqb[ig], ibql[ig], wnb[ig], xb[ig], z_re, z_im, ibpa, dev );
//        }
//     }
  }

