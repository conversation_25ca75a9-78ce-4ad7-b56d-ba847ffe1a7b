
   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::newton()
  {
      Int ifre;

//      start_acc_device();

      gtrhs( rhs );
//    debugf( nv,rhs, "RHS" );

//      cout << "mutex free newton\n";


      //setv( 0,dof->size(), nv, 0., res );
      //setv( 0,dof->size(), nv, 0., dq );
      setv( 0,dof->size(), nv, ZERO, res, "d" );
      setv( 0,dof->size(), nv, ZERO, dq, "d" );
      gtresd( rhs,res );
      invdg( res,dq);

      for( Int jt=0;jt<niter;jt++ )
     {
         //setv( 0,dof->size(), nv, 0., res );
         setv( 0,dof->size(), nv, ZERO, res,"d" );
         gtres( dq,res );
         gtresd( rhs,res );
         invdg( res,dq );
     }
      //rsmth( dq,res, 5,1.e-2 );

      if(nfre>0)
     {
         bfredone = true;

         setv( 0,dof->size(), nlhs, ZERO,lhsa, "d" );
         gtlhs( lhsa);

         for(ifre=0; ifre<nfre; ifre++)
        {
            comp_z(ifre);
        }
     }

      qupdt( dq );

//      exit_acc_device();
  }
