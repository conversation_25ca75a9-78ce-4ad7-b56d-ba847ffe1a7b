
   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::comp()
  {
      if(unst) 
     {
        comp2();
     }
      else     
     {
#ifdef PETSC
        petsc_nk_comp();
#else
        comp3();
#endif
     }
  }

//   void cFdDomain::comp2()
//  {
////      cout << "nexec " << nexc << " ============================\n";
////      if(nexc<300)
////     {
////         hypre_comp_ilu();
////      //   comp3();
////     }
////      else
////     {
////         cout << "switch to GMRES-------------------------------------\n";
////         hypre_comp_gmres();
////     }
////         hypre_comp_ilu();
////      hypre_comp_amg();
//      comp3();
//        //check_diflx();
//        //check_diflxmuscl();
//        //check_dmflx();
//
////      petsc_nk_comp();
//      //check_mat_multi_vec();
//      //check_lin_lhs();
//
//  }

   void cFdDomain::comp2()
  {
      Int iq,iv;
      Real *r1;
      string   fnme;

      //cout << "mutex free comp\n";

//      bfredone = false;

//      movegrid();
//      frame();
//      weights();

      r1= new Real[nv];

//      setv( 0,nq, nv,ZERO, mgrhs );

      for( Int it=0;it<nstep;it++ )
     {

         //cout << "step " << it << " out of " << nstep << "\n";
         smooth2();
     }
      if(dev->getrank()==0 && ilev==0) *flg << "#\n";

      //nexc++;

      if(unst)
     {
         Int i;
         i = tm/dtm;

         ifstream fle;
         string fnm;
         Int its, ite, idt;
         fnm = "time.upost";
         fle.open(fnm.c_str());
         if(fle.good())
        {
            its = 0;
            ite = 0;
            idt = 0;

            fle >> its;
            fle >> ite;
            fle >> idt;
            if((i-its)%idt ==0 && i<=ite && i>=its)
           { 
              saveunst(i);
           }
        }
         fle.close();

         probesol();

         if(nexc%nout==0)
        {
           if(dev->getrank()==0 && ilev==0)  cout << "save solution at " << nexc << "\n";
           save();
        }
     }
      else
     {
         if(nexc%nout==0)
        {
           cout << "save solution at " << nexc << "===================\n";
           save();
        }
     }
      delete[] r1;
  }

   void cFdDomain::comp3()
  {
      Int iq,iv, ifre;
      Real *r1;
      string   fnme;

      //cout << "mutex free comp\n";

//      bfredone = false;

//      movegrid();
//      frame();
//      weights();

      r1= new Real[nv];

      //setv( 0,nq, nv,0., mgrhs );

      if(dev->getrank()==0 && ilev==0)
     {
        for(ifre=0; ifre<nfre; ifre++)
       {
           fnme= dev->getcpath();
           fnme= fnme+"/"+"hist_z_" + strc(ifre) + ".dat";
           flehist_z[ifre].open(fnme.c_str(), std::ofstream::app);
           flehist_z[ifre] << scientific;
           flehist_z[ifre] << "#\n";
       }
     }

//      start_acc_device();
      for( Int it=0;it<nstep;it++ )
     {
         //cout << "step " << it << " out of " << nstep << "\n";
         smooth3();
     }
//      exit_acc_device();
      if(dev->getrank()==0 && ilev==0) *flg << "#\n";

//      nexc++;
//      if(dev->getrank()==0 && ilev==0)  cout << "iteration " << nexc << "\n";

      if(unst)
     {
        cout << "Error: calling comp3(), this function should not be used for URANS\n";
        assert(0);
     }
      else
     {
        if(nexc%nout==0)
       {
          if(dev->getrank()==0 && ilev==0)  cout << "save solution at " << nexc << "\n";
          save();
//          save_z();
       }
     }
      //save();
      //save_z();

      if(dev->getrank()==0 && ilev==0)
     {
        for(ifre=0; ifre<nfre; ifre++)
       {
           flehist_z[ifre].close();
       }
     }

//      if(nexc%nout==0) vtk(nv, vklim, "vklim.vtk");

//      if(nexc%nout==0) {
//      for(Int ig=0; ig<ng; ig++)
//     {
//         if(bgnm[ig]=="BLADE")
//        {
//            ofstream fle;
//            fle.open("wall.dat");
//            for(Int ib=0; ib<nbb[ig]; ib++)
//           {
//               Int iq = iqb[ig][0][ib];
//               fle << xq[0][iq] << " " << q[3][iq] << "\n";
//           }
//            fle.close();
//        }
//     }
//     }

      delete[] r1;
  }

