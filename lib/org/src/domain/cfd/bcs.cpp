   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::bcs()
  {
      Int ig,ib,iv;
      Int ibs,ibe;
      dof->exchange( sxq );
      dof->exchange( sq );

//     #pragma acc enter data copyin (sxq[0:nx*nq], sq[0:nv*nq], saux[0:naux*nq])
//      for( ig=0;ig<ng;ig++ )
//     {
//         #pragma acc enter data copyin(sxb[ig][0:nx*nbb[ig]],sqb0[ig][0:nv*nbb[ig]],sauxb0[ig][0:naux*nbb[ig]],sxqb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],\
//                                       sauxb[ig][0:naux*nbb[ig]],siqb[ig][0:nbb[ig]],swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]])
//     }
//      start_acc_device();

//    cout << "===================> cFdDomain::bcs()\n";
      while( dev->transit() )
     {
         for( ig=0;ig<ng;ig++ )
        {
            bdf[ig]->range( dev->avail(), &ibs,&ibe );
            //bbj[ig]->bcs( ibs,ibe, tm, xb[ig],qb0[ig],auxb0[ig], xqb[ig],qb[ig],auxb[ig], iqb[ig], xq,q,aux, NULL, wnb[ig],wxdb[ig], NULL );
            bbj[ig]->bcs( ibs,ibe, tm, xb[ig],qb0[ig],auxb0[ig], xqb[ig],qb[ig],auxb[ig], iqb_view[ig], xq,q,aux, NULL_rview, wnb[ig],wxdb[ig], NULL_rview );
        }
     }
//     #pragma acc exit data copyout (sxq[0:nx*nq], sq[0:nv*nq], saux[0:naux*nq])
//      for( ig=0;ig<ng;ig++ )
//     {
//         #pragma acc exit data copyout(sxb[ig][0:nx*nbb[ig]],sqb0[ig][0:nv*nbb[ig]],sauxb0[ig][0:naux*nbb[ig]],sxqb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],\
//                                       sauxb[ig][0:naux*nbb[ig]],siqb[ig][0:nbb[ig]],swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]])
//     }
//      exit_acc_device();
  }
