   using namespace std;

#  include <complex>
#  include <cmath>
#  include <domain/cfd/bndry/bndry.h>

const Real gam = 1.4;
const Real rg = 287./10000.;

   extern "C" void zgetrf_( int *, int *, complex<Real> *, int *, int *, int * );
   extern "C" void zgetrs_( char *, int *, int *, complex<Real> *, int *, int *, complex<Real> *, int *, int * );


   Int sgn(Real v)
  {
     if (v < 0) return -1;
     if (v > 0) return 1;
     return 0;
  }

    void solvchar(Real u, Real v, Real a, Real omega, Real beta, Real rho, Real s, 
                  Real c1, Real c2, Real c3, Real c4, Real *newc1, Real *newc2, Real *newc3);

   void bvel(Int ips, Int ipe, Real *x[], Real *q[], Real *bq[] )
  {
      Int jp;
      Real x0, x1, x2, r, cth, sth;

      for( jp=ips;jp<ipe;jp++ )
     {
         x0= x[0][jp];
         x1= x[1][jp];
         x2= x[2][jp];
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r; 
         sth= x1/r; 
         bq[0][jp]= q[0][jp];
         Real tmp= q[1][jp]; 
         bq[1][jp]= sth* tmp +cth*q[2][jp];
         bq[2][jp]= cth* tmp -sth*q[2][jp];
     }
  }

   void bvel(Real *x, Real *qsrc, Real *qdst )
  {
      Real x0, x1, x2, r, cth, sth;

      x0= x[0];
      x1= x[1];
      x2= x[2];
      r= x1*x1+ x2*x2;
      r= sqrt(r)+small;
      cth= x2/r;
      sth= x1/r;
      qdst[0]= qsrc[0];
      Real tmp= qsrc[1];
      qdst[1]= sth* tmp +cth*qsrc[2];
      qdst[2]= cth* tmp -sth*qsrc[2];
  }

   template <typename T> 
   void dq2char(Real rg, Real rho, Real a, Int nx, T *dq, T *c)
  {
      T dux, dur, dut, dt, dp, drho;
      Real p, t;

      p = rho*a*a/gam;
      t = p/(rg*rho);

      dux = dq[0];
      dur = dq[1];
      dut = dq[2];
      dt  = dq[3];
      dp  = dq[4];
      drho = (1/rg)*( -dt*p/(t*t) + dp/t );

      c[0] =-drho*a*a + dp;
      c[1] = rho*a*dut;
      c[2] = rho*a*dur;
      c[3] = rho*a*dux + dp;
      c[4] =-rho*a*dux + dp;
  }

   template <typename T> 
   void char2dq(Real rg, Real rho, Real a, Int nx, T *c, T *dq)
  {
      T dux, dur, dut, dt, dp, drho;
      T c1, c2, c3, c4, c5;
      Real p, t;

      p = rho*a*a/gam;
      t = p/(rg*rho);

      c1 = c[0];
      c2 = c[1];
      c3 = c[2];
      c4 = c[3];
      c5 = c[4];

      drho = c1/(-a*a) + c4/(2*a*a) + c5/(2*a*a);
      dux = c4/(2*rho*a) + c5/(-2*rho*a);
      dut = c2/(rho*a);
      dur = c3/(rho*a);
      dp = (Real)0.5*(c4 + c5);
      dt = dp/(rho*rg) - t*drho/rho;

      dq[0] = dux;
      dq[1] = dur;
      dq[2] = dut;
      dq[3] = dt;
      dq[4] = dp;
  }

   /*void cFreeFbndry::nrbc( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb[], Int *iqbq[], Real *q[], Real *wnb[],
                           Real *wxdb[], Int *ibql, Int mfre_nrbc, string bnm, Real omega )
  {
      Int iv, ix, il, ib, i;
      Int nv, nx, nb;
      Real ux, ur, ut, t, p, a, umag, rho;
      Real ux0, ur0, ut0, t0, p0;
      Real dux0, dur0, dut0, dt0, dp0, drho0;
      complex<Real> *dc1[20], *dc2[20], *dc3[20], *dc4[20], *dc5[20], img(0,1), beta;
      complex<Real> dux, dur, dut, dp, dt, drho;
      Real tmpdc1, tmpdc2, tmpdc3, tmpdc4, tmpdc5, w;
      Real rlx=0.85;
      Real **qwrk, *sqwrk;
      Real theta; 

      //if(bnm!="NRIN"&& bnm!="NREX") return;

      //cout << "Here" << bnm << "\n";

//assume fft() is already called

      nv= fld->getnv();
      nx= coo->getnx();
      if( ibe > ibs )
     {
         bvel( ibs,ibe, xb, qb0, qb0 );
         bvel( ibs,ibe, xb, qb, qb );

         //for(i=0; i<nfre_send; i++)
         for(i=0; i<mfre_nrbc; i++)
        {
            dc1[i] = new complex<Real> [nbl];
            dc2[i] = new complex<Real> [nbl];
            dc3[i] = new complex<Real> [nbl];
            dc4[i] = new complex<Real> [nbl];
            dc5[i] = new complex<Real> [nbl];
        }

         //convert velocity to the relative frame, THIS IS IMPORTANT TO GET THE NRBC WORKING FOR THE ROTOR
         for(il=0; il<nbl-1; il++)
        {
            zfft_re[mfre_nrbc][2][il] -= omega*xbvg[1][il];
        } 

//Giles nrbc
         for(il=0; il<nbl-1; il++)
        {
            ux = zfft_re[mfre_nrbc][0][il];
            ur = zfft_re[mfre_nrbc][1][il];
            ut = zfft_re[mfre_nrbc][2][il];
            t =  zfft_re[mfre_nrbc][3][il];
            p =  zfft_re[mfre_nrbc][4][il];
            rho = p/(rg*t);
            a = sqrt(gam*rg*t);
            umag = sqrt(ux*ux + ut*ut);

            //cout << a << " " << umag << " ==== \n";
//            if(umag>=a) cout << "supersonic!!!!!!!!!!\n";
            //for(i=0; i<nfre_send; i++)
            for(i=0; i<mfre_nrbc; i++)
           {
               dux = zfft_re[i][0][il] + zfft_im[i][0][il]*img;
               dur = zfft_re[i][1][il] + zfft_im[i][1][il]*img;
               dut = zfft_re[i][2][il] + zfft_im[i][2][il]*img;
               dt  = zfft_re[i][3][il] + zfft_im[i][3][il]*img;
               dp  = zfft_re[i][4][il] + zfft_im[i][4][il]*img;
 
               if(umag*umag<a*a)
              {
                  beta = img*sqrt(a*a-umag*umag);
              }
               else
              {
                  beta = -sgn(ut)*sqrt(umag*umag-a*a);
              }
 
               if(bnm=="NRIN"||bnm=="NRMI")
              {
                  dc5[i][il] = -rho*a*dux + dp;
                  dc1[i][il] = 0;
                  dc2[i][il] = (-(beta+ut)/(a+ux))*dc5[i][il];
                  dc3[i][il] = 0;
                  //dc4[i][il] = ((beta+ut)/(a+ux))*dc5[i][il];
                  dc4[i][il] = ((beta+ut)/(a+ux))*((beta+ut)/(a+ux))*dc5[i][il];
              }
               else if(bnm=="NREX"||bnm=="NRME")
              {
                  drho = (1/rg)*( -dt*p/(t*t) + dp/t );
     
                  dc1[i][il] =-drho*a*a + dp;
                  dc2[i][il] = rho*a*dut;
                  dc3[i][il] = rho*a*dur;
                  dc4[i][il] = rho*a*dux + dp;
                  dc5[i][il] = (2*ux/(beta-ut))*dc2[i][il] - ((beta+ut)/(beta-ut))*dc4[i][il];
                  //dc5[i][il] =-rho*a*dux + dp;
 
                 //cout << dc1[i] << " " << dc2[i] << " " << dc3[i] << " " << dc4[i] << " " << dc5[i] << "\n";
              }
           }
        }

         cTabData data;
         Int asct;
         Real pitch;
         coo->get( &data );
         data.get( "assembly-sectors", &asct );
         pitch= pi2/(Real)asct;


         for(ib=ibs; ib<ibe; ib++)
        {
            il = ibql[ib];

            //area average
            ux = zfft_re[mfre_nrbc][0][il];
            ur = zfft_re[mfre_nrbc][1][il];
            ut = zfft_re[mfre_nrbc][2][il];
            t =  zfft_re[mfre_nrbc][3][il];
            p =  zfft_re[mfre_nrbc][4][il];
            rho = p/(rg*t);
            a = sqrt(gam*rg*t);
            umag = sqrt(ux*ux + ut*ut);

//            cout << ux << " " << ur << " " << ut << " " << t << " " << p << " --- " 
//                 << qbvg[0][il] << " " << qbvg[1][il] << " " << qbvg[2][il] << " " << qbvg[3][il] << " " << qbvg[4][il] << "\n";
//1D nrbc
            ux0 = qb0[0][ib];
            ur0 = qb0[1][ib];
            ut0 = qb0[2][ib];
            t0 =  qb0[3][ib];
            p0 =  qb0[4][ib];

            //dux0 = qb0[0][ib] - qbvg[0][il];
            //dur0 = qb0[1][ib] - qbvg[1][il];
            //dut0 = qb0[2][ib] - qbvg[2][il];
            //dt0 =  qb0[3][ib] - qbvg[3][il];
            //dp0 =  qb0[4][ib] - qbvg[4][il];
            dux0 = qb0[0][ib] - ux;
            dur0 = qb0[1][ib] - ur;
            dut0 = qb0[2][ib] - ut;
            dt0 =  qb0[3][ib] - t;
            dp0 =  qb0[4][ib] - p;
//            drho0 = qb0[4][ib]/(rg*qb0[3][ib]) - 
//                    qbvg[4][il]/(rg*qbvg[3][il]);
//            dux0 = qb0[0][ib] - ux;
//            dur0 = qb0[1][ib] - ur;
//            dut0 = qb0[2][ib] - ut;
//            dt0 =  qb0[3][ib] - t;
//            dp0 =  qb0[4][ib] - p;
//            drho0 = qb0[4][ib]/(rg*qb0[3][ib]) - 
//                    p/(rg*t);

            //
            tmpdc1=0;
            tmpdc2=0;
            tmpdc3=0;
            tmpdc4=0;
            tmpdc5=0;
//            if(bnm=="NRIN")
//           {
//               tmpdc1 =-drho0*a*a + dp0;
//               tmpdc2 = rho*a*dut0;
//               tmpdc3 = rho*a*dur0;
//               tmpdc4 = rho*a*dux0 + dp0;
//               tmpdc5 =-rho*a*dux0 + dp0;
//           }
//            else if(bnm=="NREX")
//           {
//               //tmpdc1 =-drho*a*a + dp;
//               //tmpdc2 = rho*a*dut;
//               //tmpdc3 = rho*a*dur;
//               //tmpdc4 = rho*a*dux + dp;
//               tmpdc5 =-rho*a*dux0 + dp0;
//           }
//            else 
//           {
//               cout << "Error: unknow nrbc boundary type\n";
//           }      

            theta = atan2(xb[1][ib], xb[2][ib]);


//contribution from spatial perturbation
            for(i=0; i<mfre_nrbc; i++)
           {
               complex<Real> tmpz;
               tmpz = dc1[i][il]*exp((Real)asct*((Real)i+1.)*theta*img); tmpdc1 += 2*tmpz.real();
               tmpz = dc2[i][il]*exp((Real)asct*((Real)i+1.)*theta*img); tmpdc2 += 2*tmpz.real();
               tmpz = dc3[i][il]*exp((Real)asct*((Real)i+1.)*theta*img); tmpdc3 += 2*tmpz.real();
               tmpz = dc4[i][il]*exp((Real)asct*((Real)i+1.)*theta*img); tmpdc4 += 2*tmpz.real();
               tmpz = dc5[i][il]*exp((Real)asct*((Real)i+1.)*theta*img); tmpdc5 += 2*tmpz.real();
           }

            //cout << tmpdc1 << " " << tmpdc2 << " " << tmpdc3 << " " << tmpdc4 << " " << tmpdc5 << "\n";
            Real tmpdrho, tmpdux, tmpdur, tmpdut, tmpdt, tmpdp;

            tmpdrho = tmpdc1/(-a*a) + tmpdc4/(2*a*a) + tmpdc5/(2*a*a);
            tmpdux = tmpdc4/(2*rho*a) + tmpdc5/(-2*rho*a);
            tmpdut = tmpdc2/(rho*a);
            tmpdur = tmpdc3/(rho*a);
            tmpdp = 0.5*(tmpdc4 + tmpdc5);
            tmpdt = tmpdp/(rho*rg) - t*tmpdrho/rho;

//add this or not does not make a difference if the solution converges
            //tmpdux += dux0;
            //tmpdur += dur0;
            //tmpdut += dut0;
            //tmpdt += dt0;
            //tmpdp += dp0;

            qb[0][ib] = qb0[0][ib] + 1.*tmpdux;
            qb[1][ib] = qb0[1][ib] + 1.*tmpdur;
            qb[2][ib] = qb0[2][ib] + 1.*tmpdut;
            qb[3][ib] = qb0[3][ib] + 1.*tmpdt;
            qb[4][ib] = qb0[4][ib] + 1.*tmpdp;
//            qb[0][ib] = ux + 1.* tmpdux;
//            qb[1][ib] = ur + 1.* tmpdur;
//            qb[2][ib] = ut + 1.* tmpdut;
//            qb[3][ib] = t  + 1.* tmpdt;
//            qb[4][ib] = p  + 1.* tmpdp;
//            qb[0][ib] = qbvg[0][il] + rlx*tmpdux;
//            qb[1][ib] = qbvg[1][il] + rlx*tmpdur;
//            qb[2][ib] = qbvg[2][il] + rlx*tmpdut;
//            qb[3][ib] = qbvg[3][il] + rlx*tmpdt;
//            qb[4][ib] = qbvg[4][il] + rlx*tmpdp;
        }

//         delete[] qwrk; qwrk=NULL;
//         delete[] sqwrk; sqwrk=NULL;
         //coo->zvel( ibs,ibe, NULL, xb, qb, qb );
         coo->zvel( ibs,ibe, NULL, xb, qb0, qb0 );
         coo->zvel( ibs,ibe, NULL, xb, qb, qb );

        // ofstream tmpfle;
        // tmpfle.open("after.dat");
        // for(ib=ibs; ib<ibe; ib++)
        //{
        //   il = ibql[ib];
        //   if(il==0) continue;
        //   for(iv=0; iv<nv; iv++)
        //  {
        //     tmpfle << qb[iv][ib] << " "; 
        //  }
        //   tmpfle << "\n";
        //}
        // tmpfle.close();

         for(i=0; i<mfre_nrbc; i++)
        {
            delete[] dc1[i]; dc1[i]=NULL;
            delete[] dc2[i]; dc2[i]=NULL;
            delete[] dc3[i]; dc3[i]=NULL;
            delete[] dc4[i]; dc4[i]=NULL;
            delete[] dc5[i]; dc5[i]=NULL;
        }
        // exit(0);
     }
  }*/

   void cFreeFbndry::nrbc( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb[], Int *iqbq[], Real *q[], Real *wnb[],
                           Real *wxdb[], Int *ibql, Int mfre_nrbc, string bnm, Real omega )
  {
      Int iv, ix, il, ib, i;
      Int nv, nx, nb;
      Real ux, ur, ut, t, p, a, umag, rho;
      Real ux0, ur0, ut0, t0, p0;
      Real dux0, dur0, dut0, dt0, dp0, drho0;
      complex<Real> img(0,1), beta;
      complex<Real> dux, dur, dut, dp, dt, drho;
      Real tmpdc1, tmpdc2, tmpdc3, tmpdc4, tmpdc5, w;
      Real rlx=0.85;
      Real **qwrk, *sqwrk;
      Real theta; 
      Real cs[10], cav[10], dc[10], dq[10];
      cTabData data;
      Int asct;
      Real pitch;

      coo->get( &data );
      data.get( "assembly-sectors", &asct );

//assume fft() is already called

      nv= fld->getnv();
      nx= coo->getnx();
      if( ibe > ibs )
     {
         bvel( ibs,ibe, xb, qb0, qb0 );
         bvel( ibs,ibe, xb, qb, qb );

//Fourier modes of characteristic variables for each level
         for(il=0; il<nbl-1; il++)
        {
            ux = qbav[0][il];
            ur = qbav[1][il];
            ut = qbav[2][il] - omega*xbvg[1][il];
            t =  qbav[3][il];
            p =  qbav[4][il];
            rho = p/(rg*t);
            a = sqrt(gam*rg*t);
            umag = sqrt(ux*ux + ut*ut);

            for(i=0; i<nsl[0]; i++)
           {
               dux = zfft_re[i][0][il] + zfft_im[i][0][il]*img;
               dur = zfft_re[i][1][il] + zfft_im[i][1][il]*img;
               dut = zfft_re[i][2][il] + zfft_im[i][2][il]*img;
               dt  = zfft_re[i][3][il] + zfft_im[i][3][il]*img;
               dp  = zfft_re[i][4][il] + zfft_im[i][4][il]*img;
 
               if(umag*umag<a*a)
              {
                  beta = img*sqrt(a*a-umag*umag);
              }
               else
              {
                  beta = -sgn(ut)*sqrt(umag*umag-a*a);
              }
 
               if(bnm=="NRIN"||bnm=="NRMI")
              {
                  c5s[i][il] = -rho*a*dux + dp;
                  c1s[i][il] = 0;
                  c2s[i][il] = (-(beta+ut)/(a+ux))*c5s[i][il];
                  c3s[i][il] = 0;
                  c4s[i][il] = ((beta+ut)/(a+ux))*((beta+ut)/(a+ux))*c5s[i][il];
              }
               else if(bnm=="NREX"||bnm=="NRME")
              {
                  drho = (1/rg)*( -dt*p/(t*t) + dp/t );
     
                  c1s[i][il] =-drho*a*a + dp;
                  c2s[i][il] = rho*a*dut;
                  c3s[i][il] = rho*a*dur;
                  c4s[i][il] = rho*a*dux + dp;
                  c5s[i][il] = (2*ux/(beta-ut))*c2s[i][il] - ((beta+ut)/(beta-ut))*c4s[i][il];
              }
           }
        }


//reconstruct for each face
         for(ib=ibs; ib<ibe; ib++)
        {
            il = ibql[ib];

            theta = atan2(xb[1][ib], xb[2][ib]);

            //mean state
            ux = qbav[0][il];
            ur = qbav[1][il];
            ut = qbav[2][il];
            t =  qbav[3][il];
            p =  qbav[4][il];
            rho = p/(rg*t);
            a = sqrt(gam*rg*t);
            umag = sqrt(ux*ux + ut*ut);

//1D nrbc
            dq[0] = qb0[0][ib] - qbvg[0][il];
            dq[1] = qb0[1][ib] - qbvg[1][il];
            dq[2] = qb0[2][ib] - qbvg[2][il];
            dq[3] = qb0[3][ib] - qbvg[3][il];
            dq[4] = qb0[4][ib] - qbvg[4][il];
            dq2char(rg, rho, a, nx, dq, cav);

//contribution from q3d nrbc
            cs[0]=0;
            cs[1]=0;
            cs[2]=0;
            cs[3]=0;
            cs[4]=0;
            for(i=0; i<nsl[0]/2-1; i++)
           {
               complex<Real> tmpz;
               tmpz = c1s[i][il]*exp((Real)asct*((Real)i+(Real)1.)*theta*img); cs[0] += (Real)2*tmpz.real();
               tmpz = c2s[i][il]*exp((Real)asct*((Real)i+(Real)1.)*theta*img); cs[1] += (Real)2*tmpz.real();
               tmpz = c3s[i][il]*exp((Real)asct*((Real)i+(Real)1.)*theta*img); cs[2] += (Real)2*tmpz.real();
               tmpz = c4s[i][il]*exp((Real)asct*((Real)i+(Real)1.)*theta*img); cs[3] += (Real)2*tmpz.real();
               tmpz = c5s[i][il]*exp((Real)asct*((Real)i+(Real)1.)*theta*img); cs[4] += (Real)2*tmpz.real();
           }

//combine 1D and q3d contribution
            if(bnm=="NRIN"||bnm=="NRMI")
           {
               dc[0] = cs[0] + rlx*cav[0];             
               dc[1] = cs[1] + rlx*cav[1];             
               dc[2] = cs[2] + rlx*cav[2];             
               dc[3] = cs[3] + rlx*cav[3];             
               dc[4] = cs[4] + 0;
           }
            else if(bnm=="NREX"||bnm=="NRME")
           {
               dc[0] = cs[0] + 0;
               dc[1] = cs[1] + 0;
               dc[2] = cs[2] + 0;
               dc[3] = cs[3] + 0;
               dc[4] = cs[4] + rlx*cav[4];             
           }
            char2dq(rg, rho, a, nx, dc, dq);

            qb[0][ib] = qbvg[0][il] + dq[0];
            qb[1][ib] = qbvg[1][il] + dq[1];
            qb[2][ib] = qbvg[2][il] + dq[2];
            qb[3][ib] = qbvg[3][il] + dq[3];
            qb[4][ib] = qbvg[4][il] + dq[4];
        }
         coo->zvel( ibs,ibe, NULL, xb, qb0, qb0 );
         coo->zvel( ibs,ibe, NULL, xb, qb, qb );
     }
  }

   /*void cFreeFbndry::nrbc_z( Int ibs, Int ibe, Real *z_re[], Real *z_im[], Int *iqbq[], Real *xb[], 
                             Real *qb0_re[], Real *qb0_im[], Real *qb_re[], Real *qb_im[],Real *wnb[], 
                             Int *ibql, string bnm, Real omega, Real freq, Real ibpa, cParal *par )
  {
      Int il, ib, iq, i, nx;
      Real ux, ut, t, p, rho, a2, a;
      Real beta, theta;
      cTabData data;
      Int asct;
      Real pitch;
      complex<Real> img(0,1);
      complex<Real> dux, dur, dut, dp, dt, drho;
      complex<Real> cs[10], dq[10], s;
      Real lambda, lambda2, tmps;

      //cout << bnm << "===================\n";

      freq *= -1;

      nx= coo->getnx();

      cout << "nrbc lin\n";
      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      pitch= pi2/(Real)asct;

      ibpa *= pi2;

      //cart -> annular
      if(qb0_re!=NULL)
     {
         bvel( ibs,ibe, xb, qb0_re, qb0_re );
         bvel( ibs,ibe, xb, qb0_im, qb0_im );
     }

      extract_lin_nrbc_mode( ibs, ibe, qb0_re, qb0_im, z_re, z_im, iqbq, xb, wnb, ibql, bnm, ibpa, par );

//      cout << "qbvg " << qbvg[0][0] << " " << qbvg[2][il] << " " << qbvg[3][il] << " " << qbvg[4][il] << "\n";
//      cout << "freq " << freq << "\n";

      for(il=0; il<nbl-1; il++)
     {
         //1. area averaged flow
         ux = qbav[0][il];
         ut = qbav[2][il] - omega*xbvg[1][il];
         t =  qbav[3][il];
         p =  qbav[4][il];
         rho = p/(rg*t);   
         a2 = gam*rg*t;
         a = sqrt(a2); 

         for(i=0; i<nsl[0]; i++)
        {
            beta = i*asct + round(ibpa/pitch);

            //cout << bnm << " " << i << " " << beta << "\n";

            dux = zfftlin_nrbc_re[i][0][il] + zfftlin_nrbc_im[i][0][il]*img;
            dur = zfftlin_nrbc_re[i][1][il] + zfftlin_nrbc_im[i][1][il]*img;
            dut = zfftlin_nrbc_re[i][2][il] + zfftlin_nrbc_im[i][2][il]*img;
            dt  = zfftlin_nrbc_re[i][3][il] + zfftlin_nrbc_im[i][3][il]*img;
            dp  = zfftlin_nrbc_re[i][4][il] + zfftlin_nrbc_im[i][4][il]*img;

            lambda = a*beta/freq;
            lambda2 = lambda*lambda;

            tmps = (a*a - ux*ux)*lambda2/((a-ut*lambda)*(a-ut*lambda));
            tmps = 1.-tmps;
            if(tmps>0)
           {
               s = sqrt(tmps);
           }
            else if(tmps<0)
           {
               s = 1.*img*(Real)sgn(freq-beta*ut)*sqrt(-tmps);
           }
            else 
           {
               cout << "acoustic resonance??" << a << " " << ux << " " << lambda << "\n";
           }

            if(bnm=="NRIN" || bnm=="MIN" || bnm=="NRMI")
           {
               //lin, inlet NRBC
               cout << "NRBC, lin, inlet\n";
               c5s[i][il] = -rho*a*dux + dp;

               c1s[i][il] = 0;
               c2s[i][il] = -c5s[i][il]*(a-ux)*lambda/((1.+s)*(a-ut*lambda));
               c3s[i][il] = 0;
               c4s[i][il] = c5s[i][il]*(a-ux)*(a-ux)*lambda2/((1.+s)*(1.+s)*(a-ut*lambda)*(a-ut*lambda));

           }
            else if(bnm=="NREX" || bnm=="MEX" || bnm =="NRME")
           {
               //lin, exit NRBC
               cout << "NRBC, lin, exit\n";
               drho = (1/rg)*( -dt*p/(t*t) + dp/t );
     
               c1s[i][il] =-drho*a*a + dp;
               c2s[i][il] = rho*a*dut;
               c3s[i][il] = rho*a*dur;
               c4s[i][il] = rho*a*dux + dp;

               c5s[i][il] = -c2s[i][il]*2.*ux*lambda/((a-ut*lambda)*(1.+s));
               c5s[i][il]+= c4s[i][il]*(1.-s)/(1.+s);

               //cout << i << " " << il << " " << c1s[i][il] << " " << c2s[i][il] << " " << c3s[i][il] << " " 
               //     << c4s[i][il] << " " << c5s[i][il] << "\n";
               //cout << i << " " << il << " " << dux << " " << dut << " " << dt << " " << dp << "\n";
           }
        }
     }

      for(ib=ibs; ib<ibe; ib++)
     {
         il = ibql[ib];

         theta = atan2(xb[1][ib], xb[2][ib]);

         //mean state
         t =  qbav[3][il];
         p =  qbav[4][il];
         rho = p/(rg*t);
         a = sqrt(gam*rg*t);

         cs[0]=0;
         cs[1]=0;
         cs[2]=0;
         cs[3]=0;
         cs[4]=0;
         for(i=0; i<nsl[0]; i++)
        {
            beta = i*asct + round(ibpa/pitch);
            complex<Real> tmpz;
            tmpz = c1s[i][il]*exp(beta*theta*img); cs[0] += tmpz;
            tmpz = c2s[i][il]*exp(beta*theta*img); cs[1] += tmpz;
            tmpz = c3s[i][il]*exp(beta*theta*img); cs[2] += tmpz;
            tmpz = c4s[i][il]*exp(beta*theta*img); cs[3] += tmpz;
            tmpz = c5s[i][il]*exp(beta*theta*img); cs[4] += tmpz;
        }
         char2dq(rg, rho, a, nx, cs, dq);

         qb_re[0][ib] = dq[0].real();
         qb_re[1][ib] = dq[1].real();
         qb_re[2][ib] = dq[2].real();
         qb_re[3][ib] = dq[3].real();
         qb_re[4][ib] = dq[4].real();

         qb_im[0][ib] = dq[0].imag();
         qb_im[1][ib] = dq[1].imag();
         qb_im[2][ib] = dq[2].imag();
         qb_im[3][ib] = dq[3].imag();
         qb_im[4][ib] = dq[4].imag();

         if(qb0_re!=NULL)
        {
            //add back enfored BC
            qb_re[0][ib] += qb0_re[0][ib];
            qb_re[1][ib] += qb0_re[1][ib];
            qb_re[2][ib] += qb0_re[2][ib];
            qb_re[3][ib] += qb0_re[3][ib];
            qb_re[4][ib] += qb0_re[4][ib];

            qb_im[0][ib] += qb0_im[0][ib];
            qb_im[1][ib] += qb0_im[1][ib];
            qb_im[2][ib] += qb0_im[2][ib];
            qb_im[3][ib] += qb0_im[3][ib];
            qb_im[4][ib] += qb0_im[4][ib];
        }
//         cout<<qb0_re[0][ib]<<" "<<qb0_re[1][ib]<<" "<<qb0_re[2][ib]<<" "<<qb0_re[3][ib]<<" "<<qb0_re[4][ib]<<" "
//             <<qb0_im[0][ib]<<" "<<qb0_im[1][ib]<<" "<<qb0_im[2][ib]<<" "<<qb0_im[3][ib]<<" "<<qb0_im[4][ib]<<"\n";
//         cout<<qb_re[0][ib]<<" "<<qb_re[1][ib]<<" "<<qb_re[2][ib]<<" "<<qb_re[3][ib]<<" "<<qb_re[4][ib]<<" "
//             <<qb_im[0][ib]<<" "<<qb_im[1][ib]<<" "<<qb_im[2][ib]<<" "<<qb_im[3][ib]<<" "<<qb_im[4][ib]<<"\n";
     }

      //annular -> cart
      coo->zvel( ibs,ibe, NULL, xb, qb_re, qb_re );
      coo->zvel( ibs,ibe, NULL, xb, qb_im, qb_im );
      if(qb0_re!=NULL)
     {
         coo->zvel( ibs,ibe, NULL, xb, qb0_re, qb0_re );
         coo->zvel( ibs,ibe, NULL, xb, qb0_im, qb0_im );
     }
  }*/

   void cFreeFbndry::nrbc_z( Int ibs, Int ibe, Real *z_re[], Real *z_im[], Int *iqbq[], Real *xb[], 
                             Real *qb0_re[], Real *qb0_im[], Real *qb_re[], Real *qb_im[],Real *wnb[], 
                             Int *ibql, string bnm, Real omega, Real freq, Real ibpa, cParal *par )
  {
      Int il, ib, iq, i, nx, m, j, k, iv;
      Real ux, ut, t, p, rho, a2, a, rhoa;
      Real beta, theta;
      cTabData data;
      Int asct;
      Real pitch;
      complex<Real> img(0,1), tmp;
      complex<Real> dux, dur, dut, dp, dt, drho;
      complex<Real> cs[10], dq[10], tmpz, z;
      complex<Real> leig[4][4], psi, left[4][4];
      Real char_2_prim[4][4];
      complex<Real> omegaprime_psi, omegaprime, delta;
      Real epsilon = 1.e-3;

      //cout << bnm << "===================\n";

      nx= coo->getnx();

      //cout << "nrbc lin\n";
      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      pitch= pi2/(Real)asct;

      ibpa *= pi2;

      //cart -> annular
      if(qb0_re!=NULL)
     {
         bvel( ibs,ibe, xb, qb0_re, qb0_re );
         bvel( ibs,ibe, xb, qb0_im, qb0_im );
     }

      extract_lin_nrbc_mode( ibs, ibe, qb0_re, qb0_im, z_re, z_im, iqbq, xb, wnb, ibql, bnm, ibpa, par );

//      cout << "qbvg " << qbvg[0][0] << " " << qbvg[2][il] << " " << qbvg[3][il] << " " << qbvg[4][il] << "\n";
//      cout << "freq " << freq << "\n";

      if(ibe>ibs)
     {
         for(il=0; il<nbl-1; il++)
        {
            //1. area averaged flow
            ux = qbav[0][il];
            ut = qbav[2][il] - omega*xbvg[1][il];
            t =  qbav[3][il];
            p =  qbav[4][il];
            rho = p/(rg*t);   
            a2 = gam*rg*t;
            a = sqrt(a2); 
            rhoa = rho*a;
   
            char_2_prim[0][0] = -1./a2;
            char_2_prim[0][1] = 0;
            char_2_prim[0][2] = 1./(2*a2);
            char_2_prim[0][3] = 1./(2*a2);
      
            char_2_prim[1][0] = 0;
            char_2_prim[1][1] = 0;
            char_2_prim[1][2] = 1./(2*rhoa);
            char_2_prim[1][3] = -1./(2*rhoa);
      
            char_2_prim[2][0] = 0;
            char_2_prim[2][1] = 1/(rhoa);
            char_2_prim[2][2] = 0;
            char_2_prim[2][3] = 0;
      
            char_2_prim[3][0] = 0;
            char_2_prim[3][1] = 0;
            char_2_prim[3][2] = 0.5;
            char_2_prim[3][3] = 0.5;
   
   
            for(i=0; i<nsl[0]; i++)
           {
               //m = (nsl[0]/2-i)*asct + round(ibpa/pitch);
               m = linmode[i];
  
               //Equation 27 of Frey's paper  GT2014-25499 
               omegaprime = freq-img*epsilon + (Real)m*ut;
  
              /* if(fabs(omegaprime)<small)
              { 
                  //omegaprim can be zero in clocking effect, i.e.: freq=0, and m can be zero
                  omegaprime_psi = -1.*img*sqrt((a2 - ux*ux)*m*m);
              } 
               else
              {
                  delta = 1 - (a2 - ux*ux)*m*m/(omegaprime*omegaprime);
   
                  if(delta>=0)
                 {
                     psi = sqrt(delta);
                 }
                  else
                 {
                     psi =-img*(Real)sgn(omegaprime)*sqrt(-delta);
                 }
                  omegaprime_psi = omegaprime*psi;
              }*/

                delta = (Real)1. - (a2 - ux*ux)*m*m/(omegaprime*omegaprime);
 
                if(delta.real()>=ZERO)
               {
                   psi = sqrt(delta);
               }
                else
               {
                   psi =-img*(Real)sgn(omegaprime.real())*sqrt(-delta);
               }
                omegaprime_psi = omegaprime*psi;
   
               //entropy wave, going downstream
               leig[0][0] = 1.;
               leig[0][1] = 0.;
               leig[0][2] = 0.;
               leig[0][3] = -1./(a2);
         
               //vortical wave, going downstream
               leig[1][0] = 0.;
               leig[1][1] = m*ux;
               leig[1][2] = m*ut+freq;
               leig[1][3] = m/rho;
         
               //pressure wave, going downstream
               leig[2][0] = 0.;
               leig[2][1] = m*ut+freq;
               leig[2][2] = -m*ux;
               //leig[2][3] = omegaprime*psi/(rhoa);
               leig[2][3] = omegaprime_psi/(rhoa);
         
               //pressure wave, going upstream
               leig[3][0] = 0.;
               leig[3][1] = m*ut+freq;
               leig[3][2] = -m*ux;
               //leig[3][3] = -omegaprime*psi/(rhoa);
               leig[3][3] = -omegaprime_psi/(rhoa);
   
               for(int ii=0; ii<4; ii++)
              {
                  for(j=0; j<4; j++)
                 {
                     tmp=0;
                     for(k=0; k<4; k++)
                    {
                        tmp += leig[ii][k]*char_2_prim[k][j];
                    }
                     left[ii][j] = tmp;
                 }
              }
   
   
               dux = zfftlin_nrbc_re[i][0][il] + zfftlin_nrbc_im[i][0][il]*img;
               dur = zfftlin_nrbc_re[i][1][il] + zfftlin_nrbc_im[i][1][il]*img;
               dut = zfftlin_nrbc_re[i][2][il] + zfftlin_nrbc_im[i][2][il]*img;
               dt  = zfftlin_nrbc_re[i][3][il] + zfftlin_nrbc_im[i][3][il]*img;
               dp  = zfftlin_nrbc_re[i][4][il] + zfftlin_nrbc_im[i][4][il]*img;
   
         //      cout << i << " " << il << " " << dux << " " << dur << " " << dut << " " << il << "\n";
   
               cs[0] = 0;
               cs[1] = 0;
               cs[2] = 0;
               cs[3] = 0;
               cs[4] = 0;
               if(bnm=="NRIN" || bnm=="MIN" || bnm=="NRMI")
              {
                  //lin, inlet NRBC
                  //cout << "NRBC, lin, inlet\n";
                  c5s[i][il] = -rho*a*dux + dp;
                  c3s[i][il] = 0;

                  complex<Real> b[4], slhs[5*5], *lhs[5], rhs[5];
                  int  lda=3,nlhs=3, nrhs=1;
                  int  info0, info1;
                  int  ipiv[3];
                  char job='n';

                  for(int ii=0; ii<3; ii++)
                 {
                     b[ii] = -left[ii][3]*c5s[i][il];
                 }

                  rhs[0] = b[0];
                  rhs[1] = b[1];
                  rhs[2] = b[2];
            
                  subv(3, 3, slhs, lhs);
                  lhs[0][0] = left[0][0];
                  lhs[1][0] = left[0][1];
                  lhs[2][0] = left[0][2];
            
                  lhs[0][1] = left[1][0];
                  lhs[1][1] = left[1][1];
                  lhs[2][1] = left[1][2];
            
                  lhs[0][2] = left[2][0];
                  lhs[1][2] = left[2][1];
                  lhs[2][2] = left[2][2];

//TMP commented out!!!            
//                  zgetrf_( &nlhs, &nlhs, slhs, &nlhs, ipiv, &info0 );
//                  zgetrs_( &job, &nlhs, &nrhs, slhs, &nlhs, ipiv, rhs, &nlhs, &info1 );

                  c1s[i][il] = rhs[0];   
                  c2s[i][il] = rhs[1];   
                  c4s[i][il] = rhs[2];   


              }
               else if(bnm=="NREX" || bnm=="MEX" || bnm =="NRME")
              {
                  //lin, exit NRBC
                  //cout << "NRBC, lin, exit\n";
                  drho = (1/rg)*( -dt*p/(t*t) + dp/t );
        
                  c1s[i][il] =-drho*a*a + dp;
                  c2s[i][il] = rho*a*dut;
                  c3s[i][il] = rho*a*dur;
                  c4s[i][il] = rho*a*dux + dp;
   
                  c5s[i][il] = left[3][1]*c2s[i][il] + left[3][2]*c4s[i][il];
                  c5s[i][il] /= -left[3][3];
   
   
                  //cout << i << " " << il << " " << c1s[i][il] << " " << c2s[i][il] << " " << c3s[i][il] << " " 
                  //     << c4s[i][il] << " " << c5s[i][il] << "\n";
                  //cout << i << " " << il << " " << dux << " " << dut << " " << dt << " " << dp << "\n";
              }
               else
              {
                  cout << "unknow boundary name of lin NRBC\n";
              }

               //convert modes to primitive variables, must do it 
               cs[0] = c1s[i][il];
               cs[1] = c2s[i][il];
               cs[2] = c3s[i][il];
               cs[3] = c4s[i][il];
               cs[4] = c5s[i][il];
               char2dq(rg, rho, a, nx, cs, dq);
               for(iv=0; iv<5; iv++)
              {
                  zfftlin_nrbc_re[i][iv][il] = dq[iv].real();
                  zfftlin_nrbc_im[i][iv][il] = dq[iv].imag();
              }
           }
        }

         for(ib=ibs; ib<ibe; ib++)
        {
            il = ibql[ib];
   
            theta = atan2(xb[1][ib], xb[2][ib]);
   
            //mean state
            t =  qbav[3][il];
            p =  qbav[4][il];
            rho = p/(rg*t);
            a = sqrt(gam*rg*t);
   
            for(Int iv=0; iv<5; iv++)
           {
               dq[iv]=0;
               for(i=0; i<nsl[0]; i++)
              {
                  //beta = (nsl[0]/2-i)*asct + round(ibpa/pitch);
                  beta = linmode[i];
                  z =   zfftlin_nrbc_re[i][iv][il] + zfftlin_nrbc_im[i][iv][il]*img;
                  tmpz = z*exp(beta*theta*img); dq[iv] += tmpz;
              }
           }

   
            qb_re[0][ib] = dq[0].real();
            qb_re[1][ib] = dq[1].real();
            qb_re[2][ib] = dq[2].real();
            qb_re[3][ib] = dq[3].real();
            qb_re[4][ib] = dq[4].real();
   
            qb_im[0][ib] = dq[0].imag();
            qb_im[1][ib] = dq[1].imag();
            qb_im[2][ib] = dq[2].imag();
            qb_im[3][ib] = dq[3].imag();
            qb_im[4][ib] = dq[4].imag();
   
            if(qb0_re!=NULL)
           {
               //add back enfored BC
               qb_re[0][ib] += qb0_re[0][ib];
               qb_re[1][ib] += qb0_re[1][ib];
               qb_re[2][ib] += qb0_re[2][ib];
               qb_re[3][ib] += qb0_re[3][ib];
               qb_re[4][ib] += qb0_re[4][ib];
   
               qb_im[0][ib] += qb0_im[0][ib];
               qb_im[1][ib] += qb0_im[1][ib];
               qb_im[2][ib] += qb0_im[2][ib];
               qb_im[3][ib] += qb0_im[3][ib];
               qb_im[4][ib] += qb0_im[4][ib];
           }
   //         cout<<qb0_re[0][ib]<<" "<<qb0_re[1][ib]<<" "<<qb0_re[2][ib]<<" "<<qb0_re[3][ib]<<" "<<qb0_re[4][ib]<<" "
   //             <<qb0_im[0][ib]<<" "<<qb0_im[1][ib]<<" "<<qb0_im[2][ib]<<" "<<qb0_im[3][ib]<<" "<<qb0_im[4][ib]<<"\n";
   //         cout<<qb_re[0][ib]<<" "<<qb_re[1][ib]<<" "<<qb_re[2][ib]<<" "<<qb_re[3][ib]<<" "<<qb_re[4][ib]<<" "
   //             <<qb_im[0][ib]<<" "<<qb_im[1][ib]<<" "<<qb_im[2][ib]<<" "<<qb_im[3][ib]<<" "<<qb_im[4][ib]<<"\n";
        }
   
         //annular -> cart
         coo->zvel( ibs,ibe, NULL, xb, qb_re, qb_re );
         coo->zvel( ibs,ibe, NULL, xb, qb_im, qb_im );
         if(qb0_re!=NULL)
        {
            coo->zvel( ibs,ibe, NULL, xb, qb0_re, qb0_re );
            coo->zvel( ibs,ibe, NULL, xb, qb0_im, qb0_im );
        }
     }
  }


   void cFreeFbndry::extract_lin_nrbc_mode( Int ibs, Int ibe, Real *qb0_re[], Real *qb0_im[], Real *z_re[], 
                                            Real *z_im[], Int *iqbq[], Real *xb[], Real *wnb[], Int *ibql, 
                                            string bnm, Real ibpa, cParal *par )
  {
      Int ib, i, iv, il, nb, ifre, iq;
      cTabData data;
      Int asct;
      Real pitch, wavenum, t;
      Real tmpz_re[10], tmpz_im[10];
      Int nvel, nx, nv;
      Real *szwrk_re,**zwrk_re;
      Real *szwrk_im,**zwrk_im;
      complex<Real> dz, img(0,1), z;

      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      pitch = pi2/(Real)asct;

      nv= fld->getnv();
      nvel= coo->getnvel();
      nx= coo->getnx();

      if(zfftlin_nrbc_re[0]==NULL)
     {
         //the first time fft is called
         cout << "allocating memory for fftlin_nrbc boundary\n";
         for(i=0; i<nsl[0]; i++)
        {
            szfftlin_nrbc_re[i]= new Real [nbl*nv]; 
            zfftlin_nrbc_re[i]= new Real* [nv]; 
            subv( nv,nbl, szfftlin_nrbc_re[i],zfftlin_nrbc_re[i] );

            szfftlin_nrbc_im[i]= new Real [nbl*nv]; 
            zfftlin_nrbc_im[i]= new Real* [nv]; 
            subv( nv,nbl, szfftlin_nrbc_im[i],zfftlin_nrbc_im[i] );
        }
     }

      for(i=0; i<nsl[0]; i++)
     {
         for(iv=0; iv<nv; iv++)
        {
            for(il=0; il<nbl; il++)
           {
               zfftlin_nrbc_re[i][iv][il] = 0;
               zfftlin_nrbc_im[i][iv][il] = 0;
           }
        }
     }

      if(ibe>ibs)
     {
         nb = ibe;
         zwrk_re= new Real*[nv];
         zwrk_im= new Real*[nv];


         szwrk_re= new Real[nv*nb]; subv( nv,nb, szwrk_re,zwrk_re );
         szwrk_im= new Real[nv*nb]; subv( nv,nb, szwrk_im,zwrk_im );

         coo->bvel( ibs,ibe, iqbq,xb, z_re, zwrk_re );
         coo->bvel( ibs,ibe, iqbq,xb, z_im, zwrk_im );
         for(ifre=0; ifre<nsl[0]; ifre++)
        {
            for(ib=ibs; ib<ibe; ib++)
           {
               il = ibql[ib];
               if(il<0) continue;
               iq= iqbq[0][ib];
               
               t = atan2(xb[1][ib], xb[2][ib]);
        
               wavenum = (nsl[0]/2-ifre)*asct + round(ibpa/pitch);

               for(iv=0; iv<nvel; iv++)
              {
                  tmpz_re[iv] = zwrk_re[iv][ib];
                  tmpz_im[iv] = zwrk_im[iv][ib];
              }
               for(iv=nvel; iv<nv; iv++)
              {
                  tmpz_re[iv] = z_re[iv][iq];
                  tmpz_im[iv] = z_im[iv][iq];
              }
    
               if(qb0_re!=NULL)
              { 
                  //substract enforced BC 
                  for(iv=0; iv<nv; iv++)
                 {
                     tmpz_re[iv] -= qb0_re[iv][ib]; 
                     tmpz_im[iv] -= qb0_im[iv][ib]; 
                 }
              }
 
               dz = exp(-1*wavenum*t*img);
               for(iv=0; iv<nv; iv++)
              {   
                  z = (tmpz_re[iv] + tmpz_im[iv]*img);
                  z *=dz;
                  
                  zfftlin_nrbc_re[ifre][iv][il] += z.real();
                  zfftlin_nrbc_im[ifre][iv][il] += z.imag();
              }
               linmode[ifre] = wavenum;
           }
        }
         delete[] szwrk_re; szwrk_re=NULL;
         delete[] szwrk_im; szwrk_im=NULL;
         delete[] zwrk_re; zwrk_re=NULL;
         delete[] zwrk_im; zwrk_im=NULL;
     }


      //sum up
      for(ifre=0; ifre<nsl[0]; ifre++)
     {
         for(iv=0; iv<nv; iv++)
        {
            par->gsum( nbl, zfftlin_nrbc_re[ifre][iv] );
            par->gsum( nbl, zfftlin_nrbc_im[ifre][iv] );
        }
     }

      //divided by total number of cells on each radial level
      for(ifre=0; ifre<nsl[0]; ifre++)
     {
         for(iv=0; iv<nv; iv++)
        {
            for(il=0; il<nbl-1; il++)
           {
               zfftlin_nrbc_re[ifre][iv][il] /= (nsl[il]);
               zfftlin_nrbc_im[ifre][iv][il] /= (nsl[il]);
           }
        }
     }
  }

////////////////////////////////////////////
/*    void cFreeFbndry::nrbc_z( Int ibs, Int ibe, Real *z_re[], Real *z_im[], Int *iqbq[], Real *xb[], 
                             Real *qb0_re[], Real *qb0_im[], Real *qb_re[], Real *qb_im[],Real *wnb[], 
                             Int *ibql, Int mfre_nrbc, string bnm, Real omega, Real freq, Real ibpa )
  {
      Int il, ib, iq;
      Real ux, ur, ut, t, p, rho, a2, a, us;
      Real beta;
      cTabData data;
      Int asct;
      Real pitch;
      Real lefteigen[3][4];
      Real c1_re_in, c2_re_in, c3_re_in, c4_re_in;
      Real c1_im_in, c2_im_in, c3_im_in, c4_im_in;
      Real c1_re, c2_re, c3_re, c4_re;
      Real c1_im, c2_im, c3_im, c4_im;
      Real zux_re, zut_re, zt_re, zp_re, zrho_re, zur_re, zus_re;
      Real zux_im, zut_im, zt_im, zp_im, zrho_im, zur_im, zus_im;
      Real s, newc1_re, newc1_im, newc2_re, newc2_im, newc3_re, newc3_im, newc4_re, newc4_im;
      Real tmpz[10], tmpy[10], dl[2], dn[2], zun_re[2], zun_im[2];

      if(!(ibe>ibs)) return;

      cout << "nrbc lin\n";
      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      pitch= pi2/(Real)asct;

      //cart -> annular
      bvel( ibs,ibe, xb, qb0_re, qb0_re );
      bvel( ibs,ibe, xb, qb0_im, qb0_im );

      for(ib=ibs; ib<ibe; ib++)
     {
         il = ibql[ib];
         if( il <0 ) continue;

         //1. area averaged flow
         ux = qbvg[0][il];
         ur = qbvg[1][il];
         ut = qbvg[2][il];
         t =  qbvg[3][il];
         p =  qbvg[4][il];
         rho = p/(rg*t);   
         a2 = gam*rg*t;
         a = sqrt(a2); 
         us = sqrt(ux*ux + ur*ur);
         dl[0] = ux/us;
         dl[1] = ur/us;
         dn[0] =-dl[1];
         dn[1] = dl[0];

         //use relative velocity 
         ut -= omega*xbvg[1][il];

         beta = pi2*ibpa/pitch;

         //2. left eigenvector
         //entropy wave
         lefteigen[0][0] = 1;
         lefteigen[0][1] = 0;
         lefteigen[0][2] = 0;
         lefteigen[0][3] = -1/a2;

         //vortical wave
         lefteigen[1][0] = 0;
         lefteigen[1][1] = beta*us;
         lefteigen[1][2] = beta*ut + freq;
         lefteigen[1][3] = beta/rho;

         //presure wave
         s = beta*beta*(us*us+ut*ut-a2) + 2*beta*freq*ut + freq*freq;
         s = sqrt(s)/(rho*a);
         lefteigen[2][0] = 0;
         lefteigen[2][1] = beta*ut + freq;
         lefteigen[2][2] = -beta*us;
         lefteigen[2][3] = s;

//         for(Int i=0; i<3; i++)
//        {
//           for(Int j=0; j<4; j++)
//          {
//              cout << lefteigen[i][j] << " ";
//          } 
//           cout << "\n";
//        }

         //3. compute the incoming wave amplitutes of the specified bc
         zux_re = qb0_re[0][ib];
         zur_re = qb0_re[1][ib];
         zut_re = qb0_re[2][ib];
         zt_re  = qb0_re[3][ib];
         zp_re  = qb0_re[4][ib];
         zrho_re = (1/rg)*( -zt_re*p/(t*t) + zp_re/t );

         zus_re = zux_re*dl[0] + zur_re*dl[1]; //streamwise mag
         zun_re[0] = zux_re - zus_re*dl[0];  //normal to streamwise
         zun_re[1] = zur_re - zus_re*dl[1];  //normal to streamwise

         zux_im = qb0_im[0][ib];
         zur_im = qb0_im[1][ib];
         zut_im = qb0_im[2][ib];
         zt_im  = qb0_im[3][ib];
         zp_im  = qb0_im[4][ib];
         zrho_im = (1/rg)*( -zt_im*p/(t*t) + zp_im/t );

         zus_im = zux_im*dl[0] + zur_im*dl[1]; //streamwise mag
         zun_im[0] = zux_im - zus_im*dl[0];  //normal to streamwise
         zun_im[1] = zur_im - zus_im*dl[1];  //normal to streamwise


         c1_re_in = -a2 * zrho_re + zp_re;
         c2_re_in = rho*a*zut_re;
         c3_re_in = rho*a*zus_re + zp_re;
         c4_re_in = -rho*a*zus_re + zp_re;

         c1_im_in = -a2 * zrho_im + zp_im;
         c2_im_in = rho*a*zut_im;
         c3_im_in = rho*a*zus_im + zp_im;
         c4_im_in = -rho*a*zus_im + zp_im;

         //components normal to the streamwise direction

         //4. outgoing wave from the interior domain
         iq = iqbq[0][ib];
         tmpy[0] = xb[0][ib];
         tmpy[1] = xb[1][ib];
         tmpy[2] = xb[2][ib];
         tmpz[0] = z_re[0][iq];
         tmpz[1] = z_re[1][iq];
         tmpz[2] = z_re[2][iq];
         bvel(tmpy, tmpz, tmpz);
         zux_re = tmpz[0];
         zur_re = tmpz[1];
         zus_re = zux_re*dl[0] + zur_re*dl[1];

         tmpz[0] = z_im[0][iq];
         tmpz[1] = z_im[1][iq];
         tmpz[2] = z_im[2][iq];
         bvel(tmpy, tmpz, tmpz);
         zux_im = tmpz[0];
         zur_im = tmpz[1];
         zus_im = zux_im*dl[0] + zur_im*dl[1];

         zp_re = z_re[4][iq];
         zp_im = z_im[4][iq];
         c4_re = -rho*a*zus_re + zp_re;
         c4_im = -rho*a*zus_im + zp_im;

         //5. work out new wave amplitute for c1, c2, c3
         solvchar(us, ut, a, freq, beta, rho, s, 
                  c1_re_in, c2_re_in, c3_re_in, c4_re_in, c4_re, &newc1_re, &newc2_re, &newc3_re);
         solvchar(us, ut, a, freq, beta, rho, s, 
                  c1_im_in, c2_im_in, c3_im_in, c4_im_in, c4_re, &newc1_im, &newc2_im, &newc3_im);

         newc1_re += c1_re_in;
         newc2_re += c2_re_in;
         newc3_re += c3_re_in;
         newc4_re  = c4_re_in + c4_re;

         newc1_im += c1_im_in;
         newc2_im += c2_im_in;
         newc3_im += c3_im_in;
         newc4_im  = c4_im_in + c4_im;

         //6. use the new c1,c2,c3 and convert them back to primitive variable
         c1_re = newc1_re;
         c2_re = newc2_re;
         c3_re = newc3_re;
         c4_re = newc4_re;
         zrho_re = c1_re/(-a*a) + c3_re/(2*a*a) + c4_re/(2*a*a);
         zus_re = c3_re/(2*rho*a) + c4_re/(-2*rho*a);
         zut_re = c2_re/(rho*a);
         zp_re = 0.5*(c3_re + c4_re);
         zt_re = zp_re/(rho*rg) - t*zrho_re/rho;

         c1_im = newc1_im;
         c2_im = newc2_im;
         c3_im = newc3_im;
         c4_im = newc4_im;
         zrho_im = c1_im/(-a*a) + c3_im/(2*a*a) + c4_im/(2*a*a);
         zus_im = c3_im/(2*rho*a) + c4_im/(-2*rho*a);
         zut_im = c2_im/(rho*a);
         zp_im = 0.5*(c3_im + c4_im);
         zt_im = zp_im/(rho*rg) - t*zrho_im/rho;

         //reconstruct perturbation using the new updated streamwise component
         //and the old normal component
         zux_re = zus_re*dl[0] + zun_re[0];
         zur_re = zus_re*dl[1] + zun_re[1];
         zux_im = zus_im*dl[0] + zun_im[0];
         zur_im = zus_im*dl[1] + zun_im[1];

         qb_re[0][ib] = zux_re;
         qb_re[1][ib] = zur_re;
         qb_re[2][ib] = zut_re;
         qb_re[3][ib] = zt_re;
         qb_re[4][ib] = zp_re;

         qb_im[0][ib] = zux_im;
         qb_im[1][ib] = zur_im;
         qb_im[2][ib] = zut_im;
         qb_im[3][ib] = zt_im;
         qb_im[4][ib] = zp_im;
     }
      //annular -> cart
      coo->zvel( ibs,ibe, NULL, xb, qb_re, qb_re );
      coo->zvel( ibs,ibe, NULL, xb, qb_im, qb_im );
      coo->zvel( ibs,ibe, NULL, xb, qb0_re, qb0_re );
      coo->zvel( ibs,ibe, NULL, xb, qb0_im, qb0_im );
  }
*/
   
