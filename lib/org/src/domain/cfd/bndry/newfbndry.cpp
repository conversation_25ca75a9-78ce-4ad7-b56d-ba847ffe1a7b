
   using namespace std;

#  include <domain/cfd/bndry/bndry.h>

   cFbndry *newfbndry(Int ityp )
  {
      cFbndry *val;
      val= NULL;

      switch( ityp )
     {
         case( neut_fbndry ):
        {
            val= new cFbndry();
            break;
        }
         case( free_fbndry ):
        {
            val= new cFreeFbndry();
            break;
        }
         case( inv_fbndry ):
        {
            val= new cInvFbndry();
            break;
        }
         case( visc_fbndry ):
        {
            val= new cViscFbndry();
            break;
        }
         case( wave_fbndry ):
        {
            val= new cWaveFbndry();
            break;
        }
         case( slide_fbndry ):
        {
            val= new cSlideFbndry();
            break;
        }
         case( free_fbndry_x ):
        {
            val= new cFreeFbndryX();
            break;
        }
         case( free_fbndry_subin ):
        {
            val= new cFreeFbndrySubin();
            break;
        }
         case( free_fbndry_subout ):
        {
            val= new cFreeFbndrySubout();
            break;
        }
         case( thermal_wall_fbndry ):
        {
            val= new cThermalWallFbndry();
            break;
        }
         case( free_fbndry_massflow ):
        {
            val= new cFreeFbndryMass();
            break;
        }
     }
      return val;
  }

