
   using namespace std;

#include <iomanip>
#  include <domain/cfd/bndry/bndry.h>

   void cFreeFbndrySubin::bcs( Int ibs, Int ibe, Real tm,
                               cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                               cAu3xView<Int>&  iqbq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                               cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      Int ix,ib,iq,iv,nx,nv,naux;
      Real w;

      Int nbb, nq;

      Real *sxb, *sqb0, *sauxb0, *sxqb, *sqb, *sauxb;
      Int *siqbq;
      Real *sxq, *sq, *saux, *sdqdx, *swnb, *swxdb, *sauxfb;

      Int n_dims;
      Real n_free_stream[3], loc[3], norm[3], v_l[3], p_l, rho_l, vn_l, c_l, R_plus;
      Real alpha, beta;
      Real cos_alpha, cos_beta, sin_alpha, sin_beta, tmp_mag;
      Real cx, ct, cr, cy, cz, r, cth, sth;
      Real p_r, p_total_temp, Mach_sq, v_sq, V_r, v_r[3], dd, cc, bb, aa, T_total_temp, c_total_sq, c_r_sq, T_r;
      Real gamma = 1.4; //TEMP!!!
      Real R = 287./10000.;

      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();

      nbb = iqbq.get_dim0();
      nq  = q.get_dim1();
  
      sxb    = xb.get_data();
      sqb0   = qb0.get_data();
      sauxb0 = auxb0.get_data();
      sxqb   = xqb.get_data();
      sqb    = qb.get_data();
      sauxb  = auxb.get_data();
      siqbq  = iqbq.get_data();
      sxq    = xq.get_data();
      sq     = q.get_data();
      saux   = aux.get_data();
      sdqdx  = dqdx.get_data();
      swnb   = wnb.get_data();
      swxdb  = wxdb.get_data();
      sauxfb = auxb.get_data();

      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();
      n_dims = nx;

      if( ibe > ibs )
     {
        #pragma acc enter data copyin(this) 
        #pragma acc parallel loop gang vector \
         private(n_free_stream, loc, norm, v_r, v_l)\
         present(sxb[0:nx*nbb],sqb0[0:nv*nbb],sauxb0[0:naux*nbb],sxqb[0:nx*nbb],sqb[0:nv*nbb],sauxb[0:naux*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
                 swnb[0:(nx+1)*nbb],swxdb[0:nbb],this ) \
         default(none)
         for( ib=ibs;ib<ibe;ib++ )
        {
            //iq= iqbq[0][ib];
            iq= siqbq[ib];

           // for( iv=0;iv<nv;iv++ )
           //{
           //    //qb[iv][ib]= qb0[iv][ib];
           //    sqb[ADDR(iv,ib,nbb)]= sqb0[ADDR(iv,ib,nbb)];
           //}

            loc[0] = sxb[ADDR(0,ib,nbb)];
            loc[1] = sxb[ADDR(1,ib,nbb)];
            loc[2] = sxb[ADDR(2,ib,nbb)];

            norm[0] = swnb[ADDR(0,ib,nbb)];
            norm[1] = swnb[ADDR(1,ib,nbb)];
            norm[2] = swnb[ADDR(2,ib,nbb)];

            v_l[0] = sq[ADDR(0,iq,nq)];
            v_l[1] = sq[ADDR(1,iq,nq)];
            v_l[2] = sq[ADDR(2,iq,nq)];

            rho_l = saux[ADDR(0,iq,nq)];
            p_l = sq[ADDR(4,iq,nq)];
            c_l = saux[ADDR(2,iq,nq)];

            T_total_temp = sqb0[ADDR(3,ib,nbb)];
            p_total_temp = sqb0[ADDR(4,ib,nbb)];

            //turn whirl and pitch angle to local directions in cartesian cordinate system

            //0:total pressure, 1: total temperature, 2: whirl angle, 3:pitch angle, 4: k, 5, omega
            alpha=sqb0[ADDR(0,ib,nbb)]*pi2/360.;
            beta=sqb0[ADDR(1,ib,nbb)]*pi2/360.;

            cos_alpha = cos(alpha);
            sin_alpha = sin(alpha);
            cos_beta = cos(beta);
            sin_beta = sin(beta);

            tmp_mag = sqrt(cos_beta*cos_beta + cos_alpha*cos_alpha*sin_beta*sin_beta) + small;

            cx = cos_alpha * cos_beta;
            ct = sin_alpha * cos_beta;
            cr = cos_alpha * sin_beta;
            cx/= tmp_mag;
            ct/= tmp_mag;
            cr/= tmp_mag;

            r = sqrt(loc[1]*loc[1] + loc[2]*loc[2]) + small;
            sth = loc[1]/r;
            cth = loc[2]/r;
            cy = cr * sth + ct * cth; 
            cz = cr * cth - ct * sth; 

            tmp_mag = sqrt(cx*cx + cy*cy + cz*cz) + small;
            cx /= tmp_mag;
            cy /= tmp_mag;
            cz /= tmp_mag;

            n_free_stream[0]=cx;
            n_free_stream[1]=cy;
            n_free_stream[2]=cz;
            //cout << setprecision(10) << " " << loc[1] << " " << loc[2] << " " << cx << " " << cy << " " << cz << "\n";

            // Compute normal velocity on left side
            vn_l = 0.;
            for (int i=0; i<n_dims; i++)
                vn_l += v_l[i]*norm[i];

            // Compute speed of sound
            //c_l = sqrt(gamma*p_l/rho_l);

            // Extrapolate Riemann invariant
            R_plus = vn_l + 2.0*c_l/(gamma-1.0);

            // Specify total enthalpy
            //h_total = gamma*R_ref/(gamma-1.0)*T_total_temp;

            // Compute total speed of sound squared
            c_total_sq = gamma*R*T_total_temp;

//            cout << rho_l << " " << p_l << " " << c_l << " " << alpha << " " << beta << " " << vn_l << " " << R_plus << " " << p_total_temp << " " << T_total_temp << " " << c_total_sq << "\n";

            // Dot product of normal flow velocity
            alpha = 0.;
            for (int i=0; i<n_dims; i++)
                alpha += norm[i]*n_free_stream[i];

            // Coefficients of quadratic equation
            aa = 1.0 + 0.5*(gamma-1.0)*alpha*alpha;
            bb = -(gamma-1.0)*alpha*R_plus;
            cc = 0.5*(gamma-1.0)*R_plus*R_plus - 2.0*c_total_sq/(gamma-1.0);

            // Solve quadratic equation for velocity on right side
            // (Note: largest value will always be the positive root)
            // (Note: Will be set to zero if NaN)
            dd = bb*bb - 4.0*aa*cc;
            dd = sqrt(max(dd, ZERO));
            V_r = (-bb + dd)/(2.0*aa);
            V_r = max(V_r, ZERO);
            v_sq = V_r*V_r;

            // Compute speed of sound
            c_r_sq = c_total_sq - 0.5*(gamma-1.0)*v_sq;

            // Compute Mach number (cutoff at Mach = 1.0)
            Mach_sq = v_sq/(c_r_sq);
#ifdef FP32
            Mach_sq = min(Mach_sq, 1.0f);
#else
            Mach_sq = min(Mach_sq, 1.0d);
#endif
            v_sq = Mach_sq*c_r_sq;
            V_r = sqrt(v_sq);
            c_r_sq = c_total_sq - 0.5*(gamma-1.0)*v_sq;

            // Compute velocity (based on free stream direction)
            for (int i=0; i<n_dims; i++)
                v_r[i] = V_r*n_free_stream[i];

            // Compute temperature
            T_r = c_r_sq/(gamma*R);

            // Compute pressure
            p_r = p_total_temp*pow(T_r/T_total_temp, gamma/(gamma-1.0));

            sqb[ADDR(0,ib,nbb)]= v_r[0];
            sqb[ADDR(1,ib,nbb)]= v_r[1];
            sqb[ADDR(2,ib,nbb)]= v_r[2];
            sqb[ADDR(3,ib,nbb)]= T_r;
            sqb[ADDR(4,ib,nbb)]= p_r;
            for(iv=5; iv<nv; iv++)
           {
               sqb[ADDR(iv,ib,nbb)]= sqb0[ADDR(iv,ib,nbb)];
           }

//            for(iv=0; iv<nv; iv++)
//           {
//               cout << sqb[ADDR(iv,ib,nbb)] << " ";
//           }
//            cout << "\n";

            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               //w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
               w+= ( sxb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)] )*swnb[ADDR(ix,ib,nbb)];
           }
            for( ix=0;ix<nx;ix++ )
           {
               //xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
               sxqb[ADDR(ix,ib,nbb)]= sxq[ADDR(ix,iq,nq)]+ 2*w*swnb[ADDR(ix,ib,nbb)];
           }
        }
        #pragma acc exit data copyout(this) 
     }
//exit(0);
  }
