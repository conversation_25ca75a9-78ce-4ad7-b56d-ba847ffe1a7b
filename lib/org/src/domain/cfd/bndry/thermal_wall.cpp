
   using namespace std;

#  include <domain/cfd/bndry/bndry.h>

   cThermalWallFbndry::cThermalWallFbndry()
  {

  }

   cThermalWallFbndry::~cThermalWallFbndry()
  {

  }

   void cThermalWallFbndry::mflx( Int ibs, Int ibe,    cAu3xView<Real>& xb,  cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& rhsb,
                                  cAu3xView<Int>& ibq, cAu3xView<Real>& xq,  cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& rhs,
                                  cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      //compute incoming heat flux
  //    fld->mflx( ibs,ibe, NULL_iview, NULL_rview,qb,auxb,NULL_rview,rhsb, 
  //                        ibq,        xq,        q, aux, NULL_rview,rhs, 
  //                        xb,wnb,wxdb, auxfb );
//   void cMfRoeGasCHT::mflx( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& rhsl,
//                                              cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& rhsr,
//                                              cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
      Real            wl,wr,w;
      Real            dqn[MxNVs];
      Real            wn[4],xn[3];
      Real            taun[MxNVs];
      Real            rho,kappa,cp;
      Int             nql, nqr;

      Int iv, iql, iqr, ic, ics, ice;
      Int nfc, nq, nv0, naux, nauxf, nv, nx;
      Real *sxl, *sql, *sauxl, *srhsl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *srhsr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = wnb.get_dim1();
      nq  = q.get_dim1();

      sxl    = xb.get_data();
      sql    = qb.get_data();
      sauxl  = auxb.get_data();
      srhsl  = rhsb.get_data();
      icqr   = ibq.get_data();
      sxr    = xq.get_data();
      sqr    = q.get_data();
      sauxr  = aux.get_data();
      srhsr  = rhs.get_data();
      sxc    = xb.get_data();
      swc    = wnb.get_data();
      swxdc  = wxdb.get_data();
      sauxc  = auxfb.get_data();

      nv    = fld->getnv();
      nv0   = fld->getnv0();
      naux  = fld->getnaux();
      nauxf = fld->getnauxf();
      nx=  coo->getnx(); 

      ics = ibs;
      ice = ibe;
      if( ibe > ibs )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr) \
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxl,dqdxr)\
         present(sql[0:nv*nfc],srhsl[0:nv*nfc],\
                 icqr[0:nfc],sxr[0:nx*nq],sqr[0:nv*nq],sauxr[0:naux*nq],srhsr[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];

// distance of DOF positions from face centre

            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

            w= wl;
            wl = 0.5;
            wr = 0.5;

// normal gradients
            iv=3;
            dqn[iv]= ( sqr[ADDR(iv,iqr,nqr)]-sql[ADDR(iv,iql,nql)] )/w;


            kappa= sauxr[ADDR(naux-1,iqr,nqr)];
            rho=   sauxr[ADDR(     0,iqr,nqr)];
            cp=    sauxr[ADDR(     4,iqr,nqr)];
 
            for(iv=0; iv<nv; iv++) taun[iv] = 0;
            taun[4]= -kappa*dqn[3];

            sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*kappa/(rho*cp*w);

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               //rhsr[iv][iqr]+= taun[iv]*wc[3][ic];
               //rhsl[iv][iql]-= taun[iv]*wc[3][ic];
               #pragma acc atomic
               srhsr[ADDR_(iv,iqr,nqr)]+= taun[iv]*wn[3];
               #pragma acc atomic
               srhsl[ADDR_(iv,iql,nql)]-= taun[iv]*wn[3];
           }
            //auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            
        }
        #pragma acc exit data delete(this)
     }
  }

   void cThermalWallFbndry::dmflx( Int ibs, Int ibe,    cAu3xView<Real>& xb, cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& dqb, cAu3xView<Real>& dauxb, cAu3xView<Real>& resb,
                                   cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& dq,  cAu3xView<Real>& daux,  cAu3xView<Real>& res,
                                   cAu3xView<Real>& wnb,cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      Real            wl,wr,w;
      Real            ddqn[MxNVs];
      Real            wn[4],xn[3];      
      Real            dtaun[MxNVs];
      Real            kappa;
      Int             nql, nqr;

      Int iv, iql, iqr, ic, ics, ice;
      Int nfc, nq, nv0, naux, nv, nx;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xb.get_dim1();
      nq  = q.get_dim1();

      sxl    = xb.get_data();
      sql    = qb.get_data();
      sauxl  = auxb.get_data();
      sdql   = dqb.get_data();
      sdauxl = dauxb.get_data();
      sresl  = resb.get_data();
      icqr   = ibq.get_data();
      sxr    = xq.get_data();
      sqr    = q.get_data();
      sauxr  = aux.get_data();
      sdqr   = dq.get_data();
      sdauxr = daux.get_data();
      sresr  = res.get_data();
      sxc    = xb.get_data();
      swc    = wnb.get_data();
      swxdc  = wxdb.get_data();
      sauxc  = auxfb.get_data();

      nv    = fld->getnv();
      nv0   = fld->getnv0();
      naux  = fld->getnaux();
      nx=  coo->getnx(); 

      ics = ibs;
      ice = ibe;
      if( ice > ics )
     { 
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr)\
         private(ddqn,dtaun,wn,xn)\
         present (sresl[0:nv*nql],\
                  icqr[0:nfc],sxr[0:nx*nqr],sauxr[0:naux*nq],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
                  sxc[0:nx*nfc],swc[0:(nx+1)*nfc],this ) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];

// distance of DOF positions from face centre

            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );
   
            w= wl;

//linearized gradients
            iv = 3;
            ddqn[iv]= ( sdauxr[ADDR(iv,iqr,nqr)]- 0. )/w; //boundary value is fixed

            kappa= sauxr[ADDR(naux-1,iqr,nqr)];

//linearized heat flux
            for(iv=0; iv<nv; iv++) dtaun[iv] = 0;
            dtaun[4]= -kappa*ddqn[3];
// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               //resr[iv][iqr]+= dtaun[iv]*wc[3][ic];
               //resl[iv][iql]-= dtaun[iv]*wc[3][ic];
               #pragma acc atomic
               sresr[ADDR_(iv,iqr,nqr)]+= dtaun[iv]*wn[3];
               #pragma acc atomic
               sresl[ADDR_(iv,iql,nql)]-= dtaun[iv]*wn[3];
           }
        }
        #pragma acc exit data delete(this)
     }
  }

