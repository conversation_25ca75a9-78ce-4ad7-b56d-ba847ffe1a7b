   using namespace std;

#  include <domain/cfd/bndry/bndry.h>

   cFbndry::cFbndry()
  {
      coo=NULL; fld=NULL; 
      pthread_mutex_init( &mtx,NULL );
      nfre_send=-1;
      nfre_reci=-1;
  }

   cFbndry::~cFbndry()
  {
      coo=NULL; fld=NULL; 
      pthread_mutex_destroy( &mtx );
  }

   void cFbndry::lock()
  {
      pthread_mutex_lock( &mtx );
  }

   void cFbndry::unlock()
  {
      pthread_mutex_unlock( &mtx );
  }

   void cFbndry::request( Int ibs, Int ibe, Real tm, Real *xb[], Int *n, Int *m, Int *l, Real **sx )
  {
      Int ix,ib,jb,nx;
      Real w;
      nx= coo->getnx();
      Real      *x[3];
     *n=nx;
     *m=ibe-ibs;
     *l=fld->getnv();
     *sx= new Real[nx*(ibe-ibs)];
      subv( nx,(ibe-ibs),*sx,x );

      for( ix=0;ix<nx;ix++ )
     {
         jb=0;
         for( ib=ibs;ib<ibe;ib++ )
        {
            x[ix][jb]= xb[ix][ib];
            jb++;
        }
     }
  }

   void cFbndry::service( Int nxr, Int nvr, Int nqr, Real tm, Real *xr[], Real **qr, 
                        Int iqs, Int iqe, Real *xq[], Real  *q[], Real  *aux[], Real *dxdx[], Real **dqdx[],
                        Int ibs, Int ibe, Int *ibq[], Real *xb[], Real *qb[], Real *auxb[], bool bposix )
  {
       if(bposix)
      {
         lock();
         unlock();
      }
  }

