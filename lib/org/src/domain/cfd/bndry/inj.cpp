
   using namespace std;

#  include <domain/cfd/bndry/bndry.h>

   cInjFbndry::cInjFbndry()
  {

  }

   cInjFbndry::~cInjFbndry()
  {

  }

   void cInjFbndry::bcs( Int ibs, Int ibe, Real tm,
                         Real *sxb, Real *sqb0, Real *sauxb0, Real *sxqb, Real *sqb, Real *sauxb,
                         Int *siqbq, Real *sxq, Real *sq, Real *saux,
                         Real *sdqdx, Real *swnb, Real *swxdb , Real *sauxfb, Int nq, Int nbb)
  {
      Int ix,ib,iq,iv,nx,nv,naux;
      Real w,un;
      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();
      if( ibe > ibs )
     {
        #pragma acc enter data copyin(this) 
        #pragma acc parallel loop gang vector \
         present(sxb[0:nx*nbb],sqb0[0:nv*nbb],sauxb0[0:naux*nbb],sxqb[0:nx*nbb],sqb[0:nv*nbb],sauxb[0:naux*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
                 swnb[0:(nx+1)*nbb],swxdb[0:nbb],this ) \
         default(none)
         for( ib=ibs;ib<ibe;ib++ )
        {
            //iq= iqbq[0][ib];
            iq= siqbq[ib];
            for( iv=0;iv<nv;iv++ )
           {
               //qb[iv][ib]= qb0[iv][ib];
               sqb[ADDR(iv,ib,nbb)]= sqb0[ADDR(iv,ib,nbb)];
           }
            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               //w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
               w+= ( sxb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)] )*swnb[ADDR(ix,ib,nbb)];
           }
            for( ix=0;ix<nx;ix++ )
           {
               //xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
               sxqb[ADDR(ix,ib,nbb)]= sxq[ADDR(ix,iq,nq)]+ 2*w*swnb[ADDR(ix,ib,nbb)];
           }
        }
        #pragma acc exit data copyout(this) 
     }
  }


   void cInjFbndry::iflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *rhsb[], 
                                Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *rhs[],
                                            Real *wnb[], Real *wxdb[], Real *auxfb[]  )
  {
      fld->jflx( ibs,ibe, NULL, qb,auxb,rhsb, ibq[0], q,aux,rhs, wnb,wxdb, auxfb );
  }


   void cInjFbndry::ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[], 
                                   Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[], 
                                   Real *wnb[], Real *wxdb[], Real *auxfb[], cJacBlk *jac_df[2]  )
  {
      fld->wlhs( ibs,ibe, NULL, qb,auxb,lhsb, ibq[0], q,aux,lhs, wnb,wxdb, auxfb, jac_df );
  }

   void cInjFbndry::ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[], 
                                   Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[], 
                                   Real *wnb[], Real *wxdb[], Real *auxfb[] )
  {
      fld->wlhs( ibs,ibe, NULL, qb,auxb,lhsb, ibq[0], q,aux,lhs, wnb,wxdb, auxfb );
  }

   void cInjFbndry::grad( Int ibs, Int ibe, Real *xqb[], Real *qb[], Int *iqbq[], Real *xq[], Real *q[], 
                          Int *ijdx[], Real *dxdx[], Real **dqdx[], Real *wnb[], Real *wxdb[] )
  {
      Real dxdx1[9];
      Real **dqdx1;
      Real dx[3],*dq;

      Int  x,ix,jx,ib,iq,iv,nx,nv,iql,iqr;
      Real w,d;
      nv= fld->getnv();
      nx= coo->getnx();


      if( ibe > ibs )
     {
         dq= new Real[nv];
         dqdx1= new Real*[nv];
         for( iv=0;iv<nv;iv++ )
        {
            dqdx1[iv]= new Real[nx]; 
        }
         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= iqbq[0][ib];

            for( ix=0;ix<nx;ix++ )
           {
               for( jx=0;jx<nx;jx++ )
              {
                  dxdx1[ijdx[ix][jx]]= dxdx[ijdx[ix][jx]][iq];
              }
               dxdx1[ijdx[ix][ix]]+= 1.e-4;
               for( iv=0;iv<nv;iv++ )
              {
                  dqdx1[iv][ix]= dqdx[iv][ix][iq];
              }
           }
            getrf( nx,ijdx, dxdx1 );
            for( iv=0;iv<nv;iv++ )
           {
               getrs( nx,ijdx, dxdx1, dqdx1[iv] );
           }

            w= 0;
            for( ix=0;ix<nx;ix++ )
           {
               dx[ix]= xqb[ix][ib]- xq[ix][iq];
               d= dx[ix];
               d*= d;
               w+= d;
           }
            w= 1./w;
            
            for( iv=0;iv<nv;iv++ )
           {
               dq[iv]= 0;
               for( ix=0;ix<nx;ix++ )
              {
                  dq[iv]+= dqdx1[iv][ix]*dx[ix];
              }
           }

            for( ix=0;ix<nx;ix++ )
           {
               for( jx=0;jx<nx;jx++ )
              {
                  dxdx[ijdx[ix][jx]][iq]+= w*dx[ix]*dx[jx];
              }
           }
            for( iv=0;iv<nx;iv++ )
           {
               for( ix=0;ix<nx;ix++ )
              {
                  dqdx[iv][ix][iq]+= w*dx[ix]*dq[iv];
              }
           }
        }
         for( iv=0;iv<nv;iv++ )
        {
            delete[] dqdx1[iv]; dqdx1[iv]= NULL;
        }
         delete[] dqdx1; dqdx1= NULL;
         delete[] dq; dq= NULL;
     }
  }
   void cInjFbndry::diflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *dqb[], Real *dauxb[], Real *resb[],
                                 Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *dq[],  Real *daux[], Real *res[],
                                            Real *wnb[], Real *wxdb[], Real *auxfb[]  )
  {
      fld->djflx( ibs,ibe, NULL, qb,auxb, dqb,dauxb, resb, ibq[0], q,aux, dq,daux, res, wnb,wxdb, auxfb );

  }
   void cInjFbndry::auxv( Int ibs, Int ibe, Real *qb[], Real *auxb[] )
  {
      fld->auxv( ibs,ibe, qb,auxb ); 
  }
