
   using namespace std;

#  include <domain/cfd/bndry/bndry.h>

   cInvFbndry::cInvFbndry()
  {

  }

   cInvFbndry::~cInvFbndry()
  {

  }

   void cInvFbndry::bcs( Int ibs, Int ibe, Real tm, 
                         Real *sxb, Real *sqb0, Real *sauxb0, Real *sxqb, Real *sqb, Real *sauxb, 
                         Int *siqbq, Real *sxq, Real *sq, Real *saux, 
                         Real *sdqdx, Real *swnb, Real *swxdb , Real *sauxfb, Int nq, Int nbb)
  {
      Int ix,ib,iq,iv,nx,nv,naux;
      Real w,un;
      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();
      if( ibe > ibs )
     {
        #pragma acc enter data copyin(this) 
        #pragma acc parallel loop gang vector \
         present(sxb[0:nx*nbb],sqb0[0:nv*nbb],sauxb0[0:naux*nbb],sxqb[0:nx*nbb],sqb[0:nv*nbb],sauxb[0:naux*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
                 swnb[0:(nx+1)*nbb],swxdb[0:nbb],this ) \
         default(none)
         for( ib=ibs;ib<ibe;ib++ )
        {
            //iq= iqbq[0][ib];
            iq= siqbq[ib];
            for( iv=0;iv<nv;iv++ )
           {
               //qb[iv][ib]= qb0[iv][ib];
               sqb[ADDR(iv,ib,nbb)]= sqb0[ADDR(iv,ib,nbb)];
           }
            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               //w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
               w+= ( sxb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)] )*swnb[ADDR(ix,ib,nbb)];
           }
            for( ix=0;ix<nx;ix++ )
           {
               //xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
               sxqb[ADDR(ix,ib,nbb)]= sxq[ADDR(ix,iq,nq)]+ 2*w*swnb[ADDR(ix,ib,nbb)];
           }
        }
        #pragma acc exit data copyout(this) 
     }
  }

   void cInvFbndry::bcs( Int ibs, Int ibe, Real tm,
                         cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                         cAu3xView<Int>&  iqbq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                         cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      Int ix,ib,iq,iv,nx,nv,naux;
      Real w,un;

      Int nbb, nq;

      Real *sxb, *sqb0, *sauxb0, *sxqb, *sqb, *sauxb;
      Int *siqbq;
      Real *sxq, *sq, *saux, *sdqdx, *swnb, *swxdb, *sauxfb;

      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();

      nbb = iqbq.get_dim0();
      nq  = q.get_dim1();

      sxb    = xb.get_data();
      sqb0   = qb0.get_data();
      sauxb0 = auxb0.get_data();
      sxqb   = xqb.get_data();
      sqb    = qb.get_data();
      sauxb  = auxb.get_data();
      siqbq  = iqbq.get_data();
      sxq    = xq.get_data();
      sq     = q.get_data();
      saux   = aux.get_data();
      sdqdx  = dqdx.get_data();
      swnb   = wnb.get_data();
      swxdb  = wxdb.get_data();
      sauxfb = auxb.get_data();

      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();
      if( ibe > ibs )
     {
        #pragma acc enter data copyin(this) 
        #pragma acc parallel loop gang vector \
         present(sxb[0:nx*nbb],sqb0[0:nv*nbb],sauxb0[0:naux*nbb],sxqb[0:nx*nbb],sqb[0:nv*nbb],sauxb[0:naux*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
                 swnb[0:(nx+1)*nbb],swxdb[0:nbb],this ) \
         default(none)
         for( ib=ibs;ib<ibe;ib++ )
        {
            //iq= iqbq[0][ib];
            iq= siqbq[ib];
            for( iv=0;iv<nv;iv++ )
           {
               //qb[iv][ib]= qb0[iv][ib];
               sqb[ADDR(iv,ib,nbb)]= sqb0[ADDR(iv,ib,nbb)];
           }
            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               //w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
               w+= ( sxb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)] )*swnb[ADDR(ix,ib,nbb)];
           }
            for( ix=0;ix<nx;ix++ )
           {
               //xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
               sxqb[ADDR(ix,ib,nbb)]= sxq[ADDR(ix,iq,nq)]+ 2*w*swnb[ADDR(ix,ib,nbb)];
           }
        }
        #pragma acc exit data copyout(this) 
     }
  }


   //void cInvFbndry::iflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *rhsb[], 
   //                             Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *rhs[],
   //                                         Real *wnb[], Real *wxdb[], Real *auxfb[]  )
   void cInvFbndry::iflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *srhsb, 
                                Int *sibq, Real *sxq, Real *sq,  Real *saux,  Real *srhs,
                                            Real *swnb, Real *swxdb, Real *sauxfb, Int nfc, Int nq  )
  {
      //Int iq,ia,ib;
      //Int nvel,nx,nv,nauxf;
      fld->wflx( ibs,ibe, NULL, sqb,sauxb,srhsb, sibq, sq,saux,srhs, swnb,swxdb, sauxfb, nfc, nq );
  }

   void cInvFbndry::iflx( Int ibs, Int ibe, cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& rhsb,
                          cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                          cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      //Int iq,ia,ib;
      //Int nvel,nx,nv,nauxf;
      fld->wflx( ibs,ibe, NULL_iview, qb,auxb,rhsb, ibq, q,aux,rhs, wnb,wxdb, auxfb );
  }


   void cInvFbndry::ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[],
                                   Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[],
                                   Real *wnb[], Real *wxdb[], Real *auxfb[], cJacBlk *jac_df[2]  )
  {
      fld->wlhs( ibs,ibe, NULL, qb,auxb,lhsb, ibq[0], q,aux,lhs, wnb,wxdb, auxfb, jac_df );
  }

   //void cInvFbndry::ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[],
   //                                Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[],
   //                                Real *wnb[], Real *wxdb[], Real *auxfb[] )
   void cInvFbndry::ilhs( Int ibs, Int ibe,    Real *sxb, Real *sqb, Real *sauxb, Real *slhsb,
                                   Int *sibq,  Real *sxq, Real  *sq,  Real *saux, Real *slhsa,
                                   Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq )
  {
      //fld->wlhs( ibs,ibe, NULL, qb,auxb,lhsb, ibq[0], q,aux,lhs, wnb,wxdb, auxfb );
      fld->wlhs( ibs,ibe, NULL, sqb,sauxb,slhsb, sibq, sq,saux,slhsa, swnb,swxdb,sauxfb,nbb,nq );
  }

   void cInvFbndry::ilhs( Int ibs, Int ibe,     cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& lhsb,
                          cAu3xView<Int>& ibq,  cAu3xView<Real>& xq, cAu3xView<Real>& q,  cAu3xView<Real>& aux,  cAu3xView<Real>& lhsa,
                          cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      //fld->wlhs( ibs,ibe, NULL, qb,auxb,lhsb, ibq[0], q,aux,lhs, wnb,wxdb, auxfb );
      fld->wlhs( ibs,ibe, NULL_iview, qb,auxb,lhsb, ibq, q,aux,lhsa, wnb,wxdb,auxfb );
  }

   void cInvFbndry::grad( Int ibs, Int ibe, Real *sxqb, Real *sqb, Int *siqbq, Real *sxq, Real *sq, 
                          Real *sdxdx, Real *sdqdx, Real *swnb, Real *swxdb, Int nq, Int nbb )
  {
      Real dxdx1[9];
      Real dqdx1[10][3];
      Real dx[3],dq[10];
      Int ijdx[3][3];

      Int  x,ix,jx,ib,iq,iv,nx,nv,iql,iqr;
      Real w,d;
      nv= fld->getnv();
      nx= coo->getnx();

      Int i=0;
      for( ix=0;ix<nx;ix++ )
     {
         for( jx=0;jx<nx;jx++ )
        {   
            ijdx[ix][jx]= i++;
        }
     }

      if( ibe > ibs )
     {
        // dq= new Real[nv];
        // dqdx1= new Real*[nv];
        // for( iv=0;iv<nv;iv++ )
        //{
        //    dqdx1[iv]= new Real[nx]; 
        //}
        #pragma acc enter data copyin(this) 
        #pragma acc parallel loop gang vector \
         firstprivate(ijdx) \
         private(dxdx1,dqdx1,dx,dq) \
         present(sxqb[0:nx*nbb],sqb[0:nv*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],sdxdx[0:nx*nx*nq],sdqdx[0:nx*nv*nq], \
                 swnb[0:(nx+1)*nbb],swxdb[0:nbb],this ) \
         default(none)
         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= siqbq[ib];

            for( ix=0;ix<nx;ix++ )
           {
               for( jx=0;jx<nx;jx++ )
              {
                  //dxdx1[ijdx[ix][jx]]= dxdx[ijdx[ix][jx]][iq];
                  dxdx1[ijdx[ix][jx]]= sdxdx[ADDR(ix,jx,iq,nq)];
              }
               dxdx1[ijdx[ix][ix]]+= 1.e-4;
               for( iv=0;iv<nv;iv++ )
              {
                  //dqdx1[iv][ix]= dqdx[iv][ix][iq];
                  dqdx1[iv][ix]= sdqdx[ADDR(iv,ix,iq,nq)];
              }
           }
            getrf( ijdx, dxdx1 );
            for( iv=0;iv<nv;iv++ )
           {
               getrs( ijdx, dxdx1, dqdx1[iv] );
           }

            w= 0;
            for( ix=0;ix<nx;ix++ )
           {
               //dx[ix]= xqb[ix][ib]- xq[ix][iq];
               dx[ix]= sxqb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)];
               d= dx[ix];
               d*= d;
               w+= d;
           }
            w= 1./w;
            
            for( iv=0;iv<nv;iv++ )
           {
               dq[iv]= 0;
               for( ix=0;ix<nx;ix++ )
              {
                  dq[iv]+= dqdx1[iv][ix]*dx[ix];
              }
           }

            for( ix=0;ix<nx;ix++ )
           {
               for( jx=0;jx<nx;jx++ )
              {
                  //dxdx[ijdx[ix][jx]][iq]+= w*dx[ix]*dx[jx];
                  #pragma acc atomic
                  sdxdx[ADDRG(ix,jx,iq,nq)]+= w*dx[ix]*dx[jx];
              }
           }
            for( iv=0;iv<nv;iv++ )
           {
               for( ix=0;ix<nx;ix++ )
              {
                  //dqdx[iv][ix][iq]+= w*dx[ix]*dq[iv];
                  #pragma acc atomic
                  sdqdx[ADDRG(iv,ix,iq,nq)]+= w*dx[ix]*dq[iv];
              }
           }
        }
        #pragma acc exit data copyout(this) 
        // for( iv=0;iv<nv;iv++ )
        //{
        //    delete[] dqdx1[iv]; dqdx1[iv]= NULL;
        //}
        // delete[] dqdx1; dqdx1= NULL;
        // delete[] dq; dq= NULL;
     }
  }

   //void cInvFbndry::diflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *dqb[], Real *dauxb[], Real *resb[],
   //                              Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *dq[],  Real *daux[], Real *res[],
   //                                         Real *wnb[], Real *wxdb[], Real *auxfb[]  )
   void cInvFbndry::diflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *sdqb, Real *sdauxb, Real *sresb,
                                 Int *sibq, Real *sxq, Real *sq,  Real *saux,  Real *sdq,  Real *sdaux, Real *sres,
                                            Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq  )
  {
      //fld->dwflx( ibs,ibe, NULL, qb,auxb, dqb,dauxb, resb, ibq[0], q,aux, dq,daux, res, wnb,wxdb, auxfb );
      fld->dwflx( ibs,ibe, NULL, sqb,sauxb, sdqb,sdauxb, sresb, sibq, sq,saux, sdq,sdaux, sres, swnb,swxdb, sauxfb,nbb,nq );

  }

   void cInvFbndry::diflx( Int ibs, Int ibe, cAu3xView<Real>& xb,  cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& dqb, cAu3xView<Real>& dauxb, cAu3xView<Real>& resb,
                        cAu3xView<Int>& ibq, cAu3xView<Real>& xq,  cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& dq,  cAu3xView<Real>& daux,  cAu3xView<Real>& res,
                                             cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      //fld->dwflx( ibs,ibe, NULL, qb,auxb, dqb,dauxb, resb, ibq[0], q,aux, dq,daux, res, wnb,wxdb, auxfb );
      fld->dwflx( ibs,ibe, NULL_iview, qb,auxb, dqb,dauxb, resb, ibq, q,aux, dq,daux, res, wnb,wxdb, auxfb );

  }

   void cInvFbndry::dsflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *zcb_re[], Real *zcb_im[], Real *zb_re[], Real *zb_im[], Real *rhsb[],
                            Int *ibq[], Real *xq[], Real *q[], Real *aux[], Real *zc_re[], Real *zc_im[], Real *z_re[], Real *z_im[], Real *rhs[], Real *xc[],
                            Real *wn[], Real *wxdc[]  )
  {
  //    fld->dsflx( ibs,ibe, NULL, xb, qb,auxb,zcb_re, zcb_im, zb_re, zb_im, rhsb,
  //                       ibq[0], xq, q,aux,  zc_re,  zc_im,  z_re,  z_im,  rhs, xc, wn, wxdc );
  }

   void cInvFbndry::grad_z( Int ibs, Int ibe, Real *xqb[], Int *iqbq[], Real *xq[], Int *ijdx[], 
                            Real *dxdx[], Real **dqdx_re[], Real **dqdx_im[], Real *wnb[], Real *wxdb[] )
  {
      Real dxdx1[9];
      Real **dqdx1_re;
      Real **dqdx1_im;
      Real dx[3],*dq_re, *dq_im;

      Int  x,ix,jx,ib,iq,iv,nx,nv,iql,iqr;
      Real w,d;
      nv= fld->getnv();
      nx= coo->getnx();


      if( ibe > ibs )
     {
         dq_re= new Real[nv];
         dq_im= new Real[nv];
         dqdx1_re= new Real*[nv];
         dqdx1_im= new Real*[nv];
         for( iv=0;iv<nv;iv++ )
        {
            dqdx1_re[iv]= new Real[nx]; 
            dqdx1_im[iv]= new Real[nx]; 
        }
         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= iqbq[0][ib];

            for( ix=0;ix<nx;ix++ )
           {
               for( jx=0;jx<nx;jx++ )
              {
                  dxdx1[ijdx[ix][jx]]= dxdx[ijdx[ix][jx]][iq];
              }
               dxdx1[ijdx[ix][ix]]+= 1.e-4;
               for( iv=0;iv<nv;iv++ )
              {
                  dqdx1_re[iv][ix]= dqdx_re[iv][ix][iq];
                  dqdx1_im[iv][ix]= dqdx_im[iv][ix][iq];
              }
           }
            getrf( nx,ijdx, dxdx1 );
            for( iv=0;iv<nv;iv++ )
           {
               getrs( nx,ijdx, dxdx1, dqdx1_re[iv] );
               getrs( nx,ijdx, dxdx1, dqdx1_im[iv] );
           }

            w= 0;
            for( ix=0;ix<nx;ix++ )
           {
               dx[ix]= xqb[ix][ib]- xq[ix][iq];
               d= dx[ix];
               d*= d;
               w+= d;
           }
            w= 1./w;
            
            for( iv=0;iv<nv;iv++ )
           {
               dq_re[iv]= 0;
               dq_im[iv]= 0;
               for( ix=0;ix<nx;ix++ )
              {
                  dq_re[iv]+= dqdx1_re[iv][ix]*dx[ix];
                  dq_im[iv]+= dqdx1_im[iv][ix]*dx[ix];
              }
           }

            for( ix=0;ix<nx;ix++ )
           {
               for( jx=0;jx<nx;jx++ )
              {
                  dxdx[ijdx[ix][jx]][iq]+= w*dx[ix]*dx[jx];
              }
           }
            for( iv=0;iv<nv;iv++ )
           {
               for( ix=0;ix<nx;ix++ )
              {
                  dqdx_re[iv][ix][iq]+= w*dx[ix]*dq_re[iv];
                  dqdx_im[iv][ix][iq]+= w*dx[ix]*dq_im[iv];
              }
           }
        }
         for( iv=0;iv<nv;iv++ )
        {
            delete[] dqdx1_re[iv]; dqdx1_re[iv]= NULL;
            delete[] dqdx1_im[iv]; dqdx1_im[iv]= NULL;
        }
         delete[] dqdx1_re; dqdx1_re= NULL;
         delete[] dqdx1_im; dqdx1_im= NULL;
         delete[] dq_re; dq_re= NULL;
         delete[] dq_im; dq_im= NULL;
     }
  }
