
   using namespace std;

#  include <domain/cfd/bndry/bndry.h>

   cViscFbndry::cViscFbndry()
  {

  }

   cViscFbndry::~cViscFbndry()
  {

  }


   //void cViscFbndry::mflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *rhsb[], 
   //                             Int *ibq[], Real *xq[],  Real *q[],  Real *aux[],  Real *rhs[],
   //                                         Real *wnb[], Real *wxdb[], Real *auxfb[]  )
   void cViscFbndry::mflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *srhsb, 
                                 Int *sibq,  Real *sxq, Real *sq,  Real *saux,  Real *srhs,
                                             Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq  )
  {
      //fld->mwflx( ibs,ibe, NULL, xb,qb,auxb,rhsb, ibq[0], xq,q,aux,rhs, wnb,wxdb, auxfb );
      fld->mwflx( ibs,ibe, NULL, sxb,sqb,sauxb,srhsb, sibq, sxq,sq,saux,srhs, swnb,swxdb, sauxfb,nbb,nq );
  }

   void cViscFbndry::mflx( Int ibs, Int ibe,    cAu3xView<Real>& xb,  cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& rhsb,
                           cAu3xView<Int>& ibq, cAu3xView<Real>& xq,  cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& rhs,
                           cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      //fld->mwflx( ibs,ibe, NULL, xb,qb,auxb,rhsb, ibq[0], xq,q,aux,rhs, wnb,wxdb, auxfb );
      fld->mwflx( ibs,ibe, NULL_iview, xb,qb,auxb,rhsb, ibq, xq,q,aux,rhs, wnb,wxdb, auxfb );
  }

   //void cViscFbndry::dmflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *dqb[], Real *dauxb[], Real *resb[],
   //                              Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *dq[],  Real *daux[], Real *res[],
   //                                         Real *wnb[], Real *wxdb[], Real *auxfb[]  )
   void cViscFbndry::dmflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *sdqb, Real *sdauxb, Real *sresb,
                                   Int *sibq, Real *sxq, Real *sq,  Real *saux,  Real *sdq,  Real *sdaux, Real *sres,
                                              Real *swnb,Real *swxdb, Real *sauxfb, Int nbb, Int ng  )
  {
      //fld->dmwflx( ibs,ibe, NULL, xb,qb,auxb, dqb,dauxb, resb, ibq[0], xq,q,aux, dq,daux, res, wnb,wxdb, auxfb );
      fld->dmwflx( ibs,ibe, NULL, sxb,sqb,sauxb, sdqb,sdauxb, sresb, sibq, sxq,sq,saux, sdq,sdaux, sres, swnb,swxdb, sauxfb,nbb,ng );

  }

   void cViscFbndry::dmflx( Int ibs, Int ibe,    cAu3xView<Real>& xb, cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& dqb, cAu3xView<Real>& dauxb, cAu3xView<Real>& resb,
                            cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& dq,  cAu3xView<Real>& daux,  cAu3xView<Real>& res,
                            cAu3xView<Real>& wnb,cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      //fld->dmwflx( ibs,ibe, NULL, xb,qb,auxb, dqb,dauxb, resb, ibq[0], xq,q,aux, dq,daux, res, wnb,wxdb, auxfb );
      fld->dmwflx( ibs,ibe, NULL_iview, xb,qb,auxb, dqb,dauxb, resb, ibq, xq,q,aux, dq,daux, res, wnb,wxdb, auxfb );

  }

   void cViscFbndry::dmflx_z( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *zb_re[], Real *zb_im[], Real *resb_re[], Real *resb_im[],
                              Int *ibq[],       Real *xq[], Real *q[],  Real *aux[],  Real *z_re[],  Real *z_im[],  Real *res_re[],  Real *res_im[],
                              Real *wnb[], Real *wxdb[], Real *auxfb[]  )
  {
      fld->dmwflx_z( ibs,ibe, NULL, xb,qb,auxb, zb_re,zb_im, resb_re, resb_im, ibq[0], xq,q,aux, z_re,z_im, res_re, res_im, wnb,wxdb, auxfb );
  }
