
   using namespace std;

#include <iomanip>
#  include <domain/cfd/bndry/bndry.h>

//   void cFreeFbndrySubout::bcs( Int ibs, Int ibe, Real tm,
//                               cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
//                               cAu3xView<Int>&  iqbq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
//                               cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
//  {
//      Int ix,ib,iq,iv,nx,nv,naux;
//      Real w;
//
//      Int nbb, nq;
//
//      Real *sxb, *sqb0, *sauxb0, *sxqb, *sqb, *sauxb;
//      Int *siqbq;
//      Real *sxq, *sq, *saux, *sdqdx, *swnb, *swxdb, *sauxfb;
//
//      Int n_dims;
//      Real c_l, c_r, v_r[3], v_l[3], norm[3], T_r, p_l, rho_l, p_r, rho_r;
//      Real R_plus, s;
//      Real vn_r, vn_l;
//      Real gamma = 1.4; //TEMP!!!
//      Real R = 287./10000.;
//
//      nv= fld->getnv();
//      nx= coo->getnx();
//      naux= fld->getnaux();
//
//      nbb = iqbq.get_dim0();
//      nq  = q.get_dim1();
//  
//      sxb    = xb.get_data();
//      sqb0   = qb0.get_data();
//      sauxb0 = auxb0.get_data();
//      sxqb   = xqb.get_data();
//      sqb    = qb.get_data();
//      sauxb  = auxb.get_data();
//      siqbq  = iqbq.get_data();
//      sxq    = xq.get_data();
//      sq     = q.get_data();
//      saux   = aux.get_data();
//      sdqdx  = dqdx.get_data();
//      swnb   = wnb.get_data();
//      swxdb  = wxdb.get_data();
//      sauxfb = auxb.get_data();
//
//      nv= fld->getnv();
//      nx= coo->getnx();
//      naux= fld->getnaux();
//      n_dims = nx;
//
//      if( ibe > ibs )
//     {
//        #pragma acc enter data copyin(this) 
//        #pragma acc parallel loop gang vector \
//         private(norm, v_r, v_l)\
//         present(sxb[0:nx*nbb],sqb0[0:nv*nbb],sauxb0[0:naux*nbb],sxqb[0:nx*nbb],sqb[0:nv*nbb],sauxb[0:naux*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
//                 swnb[0:(nx+1)*nbb],swxdb[0:nbb],this ) \
//         default(none)
//         for( ib=ibs;ib<ibe;ib++ )
//        {
//            //iq= iqbq[0][ib];
//            iq= siqbq[ib];
//
//            norm[0] = swnb[ADDR(0,ib,nbb)];
//            norm[1] = swnb[ADDR(1,ib,nbb)];
//            norm[2] = swnb[ADDR(2,ib,nbb)];
//
//            v_l[0] = sq[ADDR(0,iq,nq)];
//            v_l[1] = sq[ADDR(1,iq,nq)];
//            v_l[2] = sq[ADDR(2,iq,nq)];
//
//            rho_l = saux[ADDR(0,iq,nq)];
//            p_l = sq[ADDR(4,iq,nq)];
//            c_l = saux[ADDR(2,iq,nq)];
//
//            // Compute normal velocity on left side
//            vn_l = 0.;
//            for (int i=0; i<n_dims; i++)
//                vn_l += v_l[i]*norm[i];
//
//            // Compute speed of sound
//            //c_l = sqrt(gamma*p_l/rho_l);
//
//            // Extrapolate Riemann invariant
//            R_plus = vn_l + 2.0*c_l/(gamma-1.0);
//
//            // Extrapolate entropy
//            s = p_l/pow(rho_l,gamma);
//
//            // fix pressure on the right side
//            p_r = sqb0[ADDR(4,ib,nbb)];
//
//            // Compute density
//            rho_r = pow(p_r/s, 1.0/gamma);
//
//            // Compute temperature
//            T_r = p_r/(R*rho_r);
//
//            // Compute speed of sound
//            c_r = sqrt(gamma*p_r/rho_r);
//
//            // Compute normal velocity
//            vn_r = R_plus - 2.0*c_r/(gamma-1.0);
//
//            // Compute velocity
//            for (int i=0; i<n_dims; i++)
//            {
//                v_r[i] = v_l[i] + (vn_r - vn_l)*norm[i];
//            }
//
//
//            sqb[ADDR(0,ib,nbb)]= v_r[0];
//            sqb[ADDR(1,ib,nbb)]= v_r[1];
//            sqb[ADDR(2,ib,nbb)]= v_r[2];
//            sqb[ADDR(3,ib,nbb)]= T_r;
//            sqb[ADDR(4,ib,nbb)]= p_r;
//            for(iv=5; iv<nv; iv++)
//           {
//               sqb[ADDR(iv,ib,nbb)]= sqb0[ADDR(iv,ib,nbb)];
//           }
//
//            for(iv=0; iv<nv; iv++)
//           {
//               cout << sqb[ADDR(iv,ib,nbb)] << " ";
//           }
//            cout << "\n";
//
//            w= 0; 
//            for( ix=0;ix<nx;ix++ )
//           {
//               //w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
//               w+= ( sxb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)] )*swnb[ADDR(ix,ib,nbb)];
//           }
//            for( ix=0;ix<nx;ix++ )
//           {
//               //xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
//               sxqb[ADDR(ix,ib,nbb)]= sxq[ADDR(ix,iq,nq)]+ 2*w*swnb[ADDR(ix,ib,nbb)];
//           }
//        }
//        #pragma acc exit data copyout(this) 
//     }
//exit(0);
//  }

   void cFreeFbndrySubout::bcs( Int ibs, Int ibe, Real tm,
                               cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                               cAu3xView<Int>&  iqbq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                               cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      Int ix,ib,iq,iv,nx,nv,naux;
      Real w;

      Int nbb, nq;

      Real *sxb, *sqb0, *sauxb0, *sxqb, *sqb, *sauxb;
      Int *siqbq;
      Real *sxq, *sq, *saux, *sdqdx, *swnb, *swxdb, *sauxfb;

      Real rho, a, pex, norm[3], deltp, rrhoc, vel[3], tb, pb;
      Real gamma = 1.4; //TEMP!!!
      Real R = 287./10000.;

      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();

      nbb = iqbq.get_dim0();
      nq  = q.get_dim1();
  
      sxb    = xb.get_data();
      sqb0   = qb0.get_data();
      sauxb0 = auxb0.get_data();
      sxqb   = xqb.get_data();
      sqb    = qb.get_data();
      sauxb  = auxb.get_data();
      siqbq  = iqbq.get_data();
      sxq    = xq.get_data();
      sq     = q.get_data();
      saux   = aux.get_data();
      sdqdx  = dqdx.get_data();
      swnb   = wnb.get_data();
      swxdb  = wxdb.get_data();
      sauxfb = auxb.get_data();

      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();

      if( ibe > ibs )
     {
        #pragma acc enter data copyin(this) 
        #pragma acc parallel loop gang vector \
         private(vel, norm)\
         present(sxb[0:nx*nbb],sqb0[0:nv*nbb],sauxb0[0:naux*nbb],sxqb[0:nx*nbb],sqb[0:nv*nbb],sauxb[0:naux*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
                 swnb[0:(nx+1)*nbb],swxdb[0:nbb],this ) \
         default(none)
         for( ib=ibs;ib<ibe;ib++ )
        {
            //iq= iqbq[0][ib];
            iq= siqbq[ib];

            norm[0] = swnb[ADDR(0,ib,nbb)];
            norm[1] = swnb[ADDR(1,ib,nbb)];
            norm[2] = swnb[ADDR(2,ib,nbb)];

            //turn back pressure to primitive variables
            pex = sqb0[ADDR(4,ib,nbb)];

            rho = saux[ADDR(0,iq,nq)];
            a = saux[ADDR(2,iq,nq)];

            vel[0] = sq[ADDR(0,iq,nq)];
            vel[1] = sq[ADDR(1,iq,nq)];
            vel[2] = sq[ADDR(2,iq,nq)];
            pb = sq[ADDR(4,iq,nq)];

            rrhoc = 1/(rho*a);
            deltp = pb - pex;
            rho  += -deltp/(a*a);

            for(ix=0; ix<nx; ix++)
           {
               vel[ix] -= norm[ix]*deltp*rrhoc;
           }
            pb = pex;
            tb = pb/(R*rho);

            sqb[ADDR(0,ib,nbb)]= vel[0];
            sqb[ADDR(1,ib,nbb)]= vel[1];
            sqb[ADDR(2,ib,nbb)]= vel[2];
            sqb[ADDR(3,ib,nbb)]= tb;
            sqb[ADDR(4,ib,nbb)]= pb;
            for(iv=5; iv<nv; iv++)
           {
               sqb[ADDR(iv,ib,nbb)]= sqb0[ADDR(iv,ib,nbb)];
           }

//            for(iv=0; iv<nv; iv++)
//           {
//               cout << sqb[ADDR(iv,ib,nbb)] << " ";
//           }
//            cout << "\n";

            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               //w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
               w+= ( sxb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)] )*swnb[ADDR(ix,ib,nbb)];
           }
            for( ix=0;ix<nx;ix++ )
           {
               //xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
               sxqb[ADDR(ix,ib,nbb)]= sxq[ADDR(ix,iq,nq)]+ 2*w*swnb[ADDR(ix,ib,nbb)];
           }
        }
        #pragma acc exit data copyout(this) 
     }
  }
