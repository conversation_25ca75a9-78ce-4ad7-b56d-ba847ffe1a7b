   using namespace std;

#  include <domain/cfd/bndry/bndry.h>

//   Real getpitch(cCosystem* coo);

   /*void cFreeFbndry::fft( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
                          cParal *par, Int mfre_nrbc )
  {
      const Int TMPMXNL=300;
      const Int TMPMXNB=400;
      Int ib, il, i, iv, iq, is;
      Int nvel, nv, nb, nx;
      Real ns[TMPMXNL];
      Real *sqwrk,**qwrk;
      Real *sxwrk,**xwrk;
      Real tmpavq_re, tmpavq_im;
      complex<Real> img(0,1), tmpz, z;
      cTabData data;
      Int asct;
      Real dux, dut, dt, dp, drho, c2s, c4s, c5s, a, rho;
      Real theta;

      coo->get( &data );
      data.get( "assembly-sectors", &asct );

      nx= coo->getnx();
      nv= fld->getnv();
      nvel= coo->getnvel();

      Real gam = 1.4;
      Real rg = 287./fld->units(2);

      if(nbl<0)
     {
         cout << "Error: can not FFT freastream boundary, you need to call 'maverage' first !\n";
         assert(0);
     }

      for(il=0; il<TMPMXNL; il++)  ns[il] = 0;    
      if( ibe > ibs )
     {
         for(ib=ibs; ib<ibe; ib++)
        {
            il = ibql[ib];
            ns[il]++;
        }
     }
      par->gsum( nbl, ns );
      for(il=0; il<nbl-1; il++) nsl[il] = ns[il];

      if(c2s_re==NULL)
     {
         cout << "allocating memory for fft boundary, characteristic variable\n";
         c2s_re = new Real *[nsl[0]];
         c4s_re = new Real *[nsl[0]];
         c5s_re = new Real *[nsl[0]];
         c2s_im = new Real *[nsl[0]];
         c4s_im = new Real *[nsl[0]];
         c5s_im = new Real *[nsl[0]];
         for(i=0; i<nsl[0]; i++)
        {
            c2s_re[i] = new Real [nbl];    
            c4s_re[i] = new Real [nbl];    
            c5s_re[i] = new Real [nbl];    
            c2s_im[i] = new Real [nbl];    
            c4s_im[i] = new Real [nbl];    
            c5s_im[i] = new Real [nbl];    
        }
     }
      for(i=0; i<nsl[0]; i++)
     {
         for(il=0; il<nbl; il++)
        {
            c2s_re[i][il] = 0;
            c4s_re[i][il] = 0;
            c5s_re[i][il] = 0;
            c2s_im[i][il] = 0;
            c4s_im[i][il] = 0;
            c5s_im[i][il] = 0;
        }
     }

      if(nfre_send>0)
     {
         if(zfft_re[0]==NULL)
        {
            //the first time fft is called
            cout << "allocating memory for fft boundary, primitive variable\n";
            for(i=0; i<nfre_send+1; i++)
           { 
               szfft_re[i]= new Real [nbl*nv]; zfft_re[i]= new Real* [nv]; subv( nv,nbl, szfft_re[i],zfft_re[i] );
               szfft_im[i]= new Real [nbl*nv]; zfft_im[i]= new Real* [nv]; subv( nv,nbl, szfft_im[i],zfft_im[i] );
           }
        }
   
         for(i=0; i<nfre_send+1; i++)
        {
            for(iv=0; iv<nv; iv++)
           { 
               for(il=0; il<nbl; il++)
              {
                  zfft_re[i][iv][il] = 0;
                  zfft_im[i][iv][il] = 0;
              }
           }
        }
     }

      if( ibe > ibs )
     {
         nb= ibe;

         qwrk= new Real*[nvel];
         xwrk= new Real*[nx];

         sqwrk= new Real[nvel*nb]; subv( nvel,nb, sqwrk,qwrk );
         sxwrk= new Real[nx*nb]; subv(   nx,nb, sxwrk,xwrk );

         coo->bcoor( ibs,ibe, xb, xwrk );
         coo->bvel( ibs,ibe, ibq,xb, q,qwrk );

         for(ib=ibs; ib<ibe; ib++)
        {
            il = ibql[ib];
            if(il<0) continue;
            iq= ibq[0][ib];

            theta = atan2(xb[1][ib], xb[2][ib]);

            if(nfre_send>0)
           {
               for(i=0; i<nfre_send+1; i++)
              {
                  tmpz = exp(-1.*(Real)i*(Real)asct*theta*img);
                  for(iv=0; iv<nvel; iv++)
                 {
                     z = qwrk[iv][ib]*tmpz;
                     zfft_re[i][iv][il] += z.real();
                     zfft_im[i][iv][il] += z.imag();
                 }
                  for(iv=nvel; iv<nv; iv++)
                 {
                     z = q[iv][iq]*tmpz;
                     zfft_re[i][iv][il] += z.real();
                     zfft_im[i][iv][il] += z.imag();
                 }
              }
           }

            dux = qwrk[0][ib] - qbav[0][il];
            dut = qwrk[2][ib] - qbav[2][il];
            dt  = q[3][iq]    - qbav[3][il];
            dp  = q[4][iq]    - qbav[4][il];
            rho = qbav[4][il]/(rg*qbav[3][il]);
            a =  sqrt(gam*rg*qbav[3][il]);
            for(i=1; i<nsl[0]; i++)
           {
               tmpz = exp(-1.*(Real)i*(Real)asct*theta*img);
         
               c2s = rho*a*dut;
               c4s = rho*a*dux + dp;
               c5s =-rho*a*dux + dp;

               z = c2s*tmpz; c2s_re[i][il] += z.real(); c2s_im[i][il] += z.imag();
               z = c4s*tmpz; c4s_re[i][il] += z.real(); c4s_im[i][il] += z.imag();
               z = c5s*tmpz; c5s_re[i][il] += z.real(); c5s_im[i][il] += z.imag();
           }

        }
         delete[] sqwrk; sqwrk=NULL; delete[] qwrk; qwrk=NULL;
         delete[] sxwrk; sxwrk=NULL; delete[] xwrk; xwrk=NULL;
     }

      if(nfre_send>0)
     {
         for(i=0; i<nfre_send+1; i++)
        {
            for(iv=0; iv<nv; iv++)
           {
               par->gsum( nbl, zfft_re[i][iv] );
               par->gsum( nbl, zfft_im[i][iv] );
           }
        }
         for(i=0; i<nfre_send+1; i++)
        {
            for(iv=0; iv<nv; iv++)
           {
               for(il=0; il<nbl-1; il++)
              {
                  zfft_re[i][iv][il] /= nsl[0];
                  zfft_im[i][iv][il] /= nsl[0];
              }
           }
        }
         //move the mean value to the end of the array
         for(iv=0; iv<nv; iv++)
        {
            for(il=0; il<nbl-1; il++)
           {
               tmpavq_re = zfft_re[0][iv][il];
               tmpavq_im = zfft_im[0][iv][il];
               for(i=1; i<nfre_send+1; i++)
              {
                  zfft_re[i-1][iv][il] = zfft_re[i][iv][il];
                  zfft_im[i-1][iv][il] = zfft_im[i][iv][il];
              }
               zfft_re[nfre_send][iv][il] = tmpavq_re;
               zfft_im[nfre_send][iv][il] = tmpavq_im;
           }
        }
     }

      for(i=0; i<nsl[0]; i++)
     {
         par->gsum( nbl, c2s_re[i] );
         par->gsum( nbl, c2s_im[i] );
         par->gsum( nbl, c4s_re[i] );
         par->gsum( nbl, c4s_im[i] );
         par->gsum( nbl, c5s_re[i] );
         par->gsum( nbl, c5s_im[i] );
     }

      for(i=1; i<ns[0]; i++)
     {
         for(il=0; il<nbl-1; il++)
        {
            c2s_re[i][il] /= nsl[0];
            c2s_im[i][il] /= nsl[0];
            c4s_re[i][il] /= nsl[0];
            c4s_im[i][il] /= nsl[0];
            c5s_re[i][il] /= nsl[0];
            c5s_im[i][il] /= nsl[0];
        }
     }


//      cout << "FFT nbl " << nbl << "\n";
//      for(il=0; il<nbl-1; il++)
//     {
//         for(i=0; i<nfre_send; i++)
//        {
//            cout << this << " " << il << " " << i << " ";
//            for(iv=0; iv<nv; iv++)
//           {
//               cout << zfft_re[i][iv][il] << " ";
//           }
//            for(iv=0; iv<nv; iv++)
//           {
//               cout << zfft_im[i][iv][il] << " ";
//           }
//            cout << "\n";
//        }
//     }
//      cout << "I am here???????????????????????????????????????\n";
  }*/

   void cFreeFbndry::fft( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
                          cParal *par, Int mfre_nrbc )
  {
      const Int TMPMXNL=300;
      const Int TMPMXNB=400;
      Int ib, il, i, iv, iq, is;
      Int nvel, nv, nb, nx;
      Real ns[TMPMXNL];
      Real *sqwrk,**qwrk;
      Real *sxwrk,**xwrk;
      Real tmpavq_re, tmpavq_im;
      complex<Real> img((Real)0.,(Real)1.), tmpz, z;
      cTabData data;
      Int asct;
      Real dux, dut, dt, dp, drho, a, rho;
      Real theta;

      coo->get( &data );
      data.get( "assembly-sectors", &asct );

      nx= coo->getnx();
      nv= fld->getnv();
      nvel= coo->getnvel();

      Real gam = 1.4;
      Real rg = 287./fld->units(2);

      if(nbl<0)
     {
         cout << "Error: can not FFT freastream boundary, you need to call 'maverage' first !\n";
         assert(0);
     }

      for(il=0; il<TMPMXNL; il++)  ns[il] = 0;    
      if( ibe > ibs )
     {
         for(ib=ibs; ib<ibe; ib++)
        {
            il = ibql[ib];
            ns[il]++;
        }
     }
      par->gsum( nbl, ns );
      for(il=0; il<nbl-1; il++) nsl[il] = ns[il];

      if(zfft_re[0]==NULL)
     {
         //the first time fft is called
         cout << "allocating memory for fft boundary, primitive variable\n";
         for(i=0; i<nsl[0]; i++)
        { 
            szfft_re[i]= new Real [nbl*nv]; zfft_re[i]= new Real* [nv]; subv( nv,nbl, szfft_re[i],zfft_re[i] );
            szfft_im[i]= new Real [nbl*nv]; zfft_im[i]= new Real* [nv]; subv( nv,nbl, szfft_im[i],zfft_im[i] );

            c1s[i] = new complex<Real> [nbl];
            c2s[i] = new complex<Real> [nbl];
            c3s[i] = new complex<Real> [nbl];
            c4s[i] = new complex<Real> [nbl];
            c5s[i] = new complex<Real> [nbl];
        }
     }
   
      for(i=0; i<nsl[0]; i++)
     {
         for(iv=0; iv<nv; iv++)
        { 
            for(il=0; il<nbl; il++)
           {
               zfft_re[i][iv][il] = 0;
               zfft_im[i][iv][il] = 0;
           }
        }
     }

      if( ibe > ibs )
     {
         nb= ibe;

         qwrk= new Real*[nvel];
         xwrk= new Real*[nx];

         sqwrk= new Real[nvel*nb]; subv( nvel,nb, sqwrk,qwrk );
         sxwrk= new Real[nx*nb]; subv(   nx,nb, sxwrk,xwrk );

         coo->bcoor( ibs,ibe, xb, xwrk );
         coo->bvel( ibs,ibe, ibq,xb, q,qwrk );

         for(ib=ibs; ib<ibe; ib++)
        {
            il = ibql[ib];
            if(il<0) continue;
            iq= ibq[0][ib];

            theta = atan2(xb[1][ib], xb[2][ib]);

            for(i=0; i<nsl[0]; i++)
           {
               tmpz = exp((Real)-1.*(Real)i*(Real)asct*theta*img);
               for(iv=0; iv<nvel; iv++)
              {
                  z = qwrk[iv][ib]*tmpz;
                  zfft_re[i][iv][il] += z.real();
                  zfft_im[i][iv][il] += z.imag();
              }
               for(iv=nvel; iv<nv; iv++)
              {
                  z = q[iv][iq]*tmpz;
                  zfft_re[i][iv][il] += z.real();
                  zfft_im[i][iv][il] += z.imag();
              }
           }
        }
         delete[] sqwrk; sqwrk=NULL; delete[] qwrk; qwrk=NULL;
         delete[] sxwrk; sxwrk=NULL; delete[] xwrk; xwrk=NULL;
     }

      for(i=0; i<nsl[0]; i++)
     {
         for(iv=0; iv<nv; iv++)
        {
            par->gsum( nbl, zfft_re[i][iv] );
            par->gsum( nbl, zfft_im[i][iv] );
        }
     }
      for(i=0; i<nsl[0]; i++)
     {
         for(iv=0; iv<nv; iv++)
        {
            for(il=0; il<nbl-1; il++)
           {
               zfft_re[i][iv][il] /= nsl[0];
               zfft_im[i][iv][il] /= nsl[0];
           }
        }
     }
      //move the mean value to the end of the array
      for(iv=0; iv<nv; iv++)
     {
         for(il=0; il<nbl-1; il++)
        {
            tmpavq_re = zfft_re[0][iv][il];
            tmpavq_im = zfft_im[0][iv][il];
            for(i=1; i<nsl[0]; i++)
           {
               zfft_re[i-1][iv][il] = zfft_re[i][iv][il];
               zfft_im[i-1][iv][il] = zfft_im[i][iv][il];
           }
            zfft_re[nsl[0]-1][iv][il] = tmpavq_re;
            zfft_im[nsl[0]-1][iv][il] = tmpavq_im;
        }
     }
  }

   void cFreeFbndry::fftlin( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real **z_re[], 
                             Real **z_im[], Real *ibpa, cParal *par )
  {
      Int ifre, jfre, iv, il, ib, iq, is;
      Real t;
      Int nvel, nv, nx, nb;
      Real *szwrk_re,**zwrk_re;
      Real *szwrk_im,**zwrk_im;
      Real *sxwrk,**xwrk;
      complex<Real> z, dz, img(0,1);

//      cout << send_lin_ngrp << "\n";
      if(nfre_send_lin<=0) {return;};

      nx= coo->getnx();
      nv= fld->getnv();
      nvel= coo->getnvel();
 
      if(nbl<0)
     {
         cout << "Error: can not FFT freastream boundary, you need to call 'maverage' first !\n";
         assert(0);
     }

      cout << "FFT lin bnd\n";

      //allocate memoery, only once
      if(szfftlin_re[0]==NULL)
     {
         //the first time fft is called
         cout << "allocating memory for fft boundary\n";
         for(ifre=0; ifre<nfre_send_lin+1; ifre++)
        { 
            szfftlin_re[ifre]= new Real [nbl*nv]; 
            zfftlin_re[ifre]= new Real* [nv]; 
            subv( nv,nbl, szfftlin_re[ifre],zfftlin_re[ifre] );

            szfftlin_im[ifre]= new Real [nbl*nv]; 
            zfftlin_im[ifre]= new Real* [nv]; 
            subv( nv,nbl, szfftlin_im[ifre],zfftlin_im[ifre] );
        }
     }

      for(ifre=0; ifre<nfre_send_lin; ifre++)
     {
         for(iv=0; iv<nv; iv++)
        {
            for(il=0; il<nbl; il++)
           {
               zfftlin_re[ifre][iv][il] = 0;
               zfftlin_im[ifre][iv][il] = 0;
           }
        }
     }

      if(ibe>ibs)
     {
         nb = ibe;
         zwrk_re= new Real*[nv];
         zwrk_im= new Real*[nv];
         xwrk= new Real*[nx];


         szwrk_re= new Real[nv*nb]; subv( nv,nb, szwrk_re,zwrk_re );
         szwrk_im= new Real[nv*nb]; subv( nv,nb, szwrk_im,zwrk_im );
         sxwrk= new Real[nx*nb]; subv(   nx,nb, sxwrk,xwrk );

         coo->bcoor( ibs,ibe, xb, xwrk );

         for(ifre=0; ifre<nfre_send_lin; ifre++)
        {
            jfre = lin_dft[ifre];
            coo->bvel( ibs,ibe, ibq,xb, z_re[jfre], zwrk_re );
            coo->bvel( ibs,ibe, ibq,xb, z_im[jfre], zwrk_im );
            for(ib=ibs; ib<ibe; ib++)
           {
               il = ibql[ib];
               if(il<0) continue;
               iq= ibq[0][ib];
   
               t = atan2(xb[1][ib], xb[2][ib]);

               dz = exp(-1*send_lin_wavnum[ifre]*t*img);
               for(iv=0; iv<nvel; iv++)
              {
                  z = (zwrk_re[iv][ib] + zwrk_im[iv][ib]*img);
                  z *=dz;

                  zfftlin_re[ifre][iv][il] += z.real();
                  zfftlin_im[ifre][iv][il] += z.imag();
              }
               for(iv=nvel; iv<nv; iv++)
              {
                  z = (z_re[jfre][iv][iq] + z_im[jfre][iv][iq]*img);
                  z *=dz;
                  zfftlin_re[ifre][iv][il] += z.real();
                  zfftlin_im[ifre][iv][il] += z.imag();
              }
           } 
        }
         delete[] szwrk_re; szwrk_re=NULL;
         delete[] szwrk_im; szwrk_im=NULL;
         delete[] sxwrk; sxwrk=NULL;
         delete[] zwrk_re; zwrk_re=NULL;
         delete[] zwrk_im; zwrk_im=NULL;
         delete[] xwrk; xwrk=NULL;
     }

      //sum up
      for(ifre=0; ifre<nfre_send_lin; ifre++)
     {
         for(iv=0; iv<nv; iv++)
        {
            par->gsum( nbl, zfftlin_re[ifre][iv] );
            par->gsum( nbl, zfftlin_im[ifre][iv] );
        }
     }

      //divided by total number of cells on each radial level
      for(ifre=0; ifre<nfre_send_lin; ifre++)
     {
         for(iv=0; iv<nv; iv++)
        {
            for(il=0; il<nbl-1; il++)
           {
               zfftlin_re[ifre][iv][il] /= (nsl[il]);
               zfftlin_im[ifre][iv][il] /= (nsl[il]);
           }
        }
     }

     /*for(il=0; il<nbl-1; il++)
     {
         //for(iv=0; iv<nv; iv++)
         iv = 3;
        {
            cout << "iv " << iv << " ";
            for(ifre=0; ifre<nfre_send_lin; ifre++)
           {
               cout << zfftlin_re[ifre][iv][il]  << " ";
           }
            for(ifre=0; ifre<nfre_send_lin; ifre++)
           {
               cout << zfftlin_im[ifre][iv][il]  << " ";
           }
            cout << "--------------\n";
        }
     }*/

//      cout << "here\n"; 
//      exit(0);
  }

