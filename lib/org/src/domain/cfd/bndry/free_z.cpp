
   using namespace std;

# include <complex>
# include <domain/cfd/bndry/bndry.h>

   void zvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] );

   void cFreeFbndry::bcs_z( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb0_re[], Real *qb0_im[], Real *xqb[], Real *qb_re[],
                            Real *qb_im[], Int *iqbq[], Real *xq[], Real *wnb[], Int ifre, Real ibpa )
  {
      Int ix,ib,iq,iv,nx,nv;
      Real w;
      Int nl, n, i, il, unhlp[1000][2]; 

      nv= fld->getnv();
      nx= coo->getnx();
      if( ibe > ibs )
     {
         if(nx==2)
        {
            for( ib=ibs;ib<ibe;ib++ )
           {
               iq= iqbq[0][ib];
               Real ux, p; 

               ux = qb0[0][ib]; 
               p = qb0[3][ib]; 

               //qb_re[0][ib] = ux*0.10;
               qb_re[0][ib] = 0;
               qb_re[1][ib] = 0;
               qb_re[2][ib] = 0;
               qb_re[3][ib] = p*0.025;
               //qb_re[3][ib] = 0;

               qb_im[0][ib] = 0;
               qb_im[1][ib] = 0;
               qb_im[2][ib] = 0;
               qb_im[3][ib] = 0;

               qb0_re[0][ib] =   qb_re[0][ib];
               qb0_re[1][ib] =   qb_re[1][ib];
               qb0_re[2][ib] =   qb_re[2][ib];
               qb0_re[3][ib] =   qb_re[3][ib];
                                            
               qb0_im[0][ib] =   qb_im[0][ib];
               qb0_im[1][ib] =   qb_im[1][ib];
               qb0_im[2][ib] =   qb_im[2][ib];
               qb0_im[3][ib] =   qb_im[3][ib];
 
//               Real ux, uy, rho, p, t;
//               Real dux, duy, dt, dp, delta, drho;
//               Real gam = 1.4;
//               Real rg = 287/10000.;
//   
//               w = ((Real)ib+0.5)/(Real)(ibe-ibs);
//               //w = 1-w;
//   
//               ux  = qb0[0][ib];
//               uy  = qb0[1][ib];
//               t   = qb0[2][ib];
//               p   = qb0[3][ib];
//               rho  = p/(rg*t);
//   
//               //delta = 0.20;
//  //             delta = 0.05;
//               delta = 0.1;
//   
//               //cout << ux << " " << uy << " " << t << " " << p << " " << delta << " " << w << " " << pi2 << " " << ibpa << " ==== \n";
//   
//               dux = ux*delta*cos(w*pi2*ibpa);
//               duy = uy*delta*cos(w*pi2*ibpa + pi2/2.);
//               //dt = (1.-gam)/(gam*rg) * (ux*dux +uy*duy) * cos(w*pi2*ibpa);
//               drho = ((gam-1)/gam) * (rho*rho/p) * delta * cos(w*pi2*ibpa) * (ux*ux + uy*uy);
//               dt =-t*drho/rho;
//               dp = 0;
//   
//   
//               qb_re[0][ib] = dux;
//               qb_re[1][ib] = duy;
//               qb_re[2][ib] = dt;
//               qb_re[3][ib] = dp;
//               qb0_re[0][ib] = qb_re[0][ib];
//               qb0_re[1][ib] = qb_re[1][ib];
//               qb0_re[2][ib] = qb_re[2][ib];
//               qb0_re[3][ib] = qb_re[3][ib];
//   
//               //qb0_re[0][ib] = dux/100.;
//               //qb0_re[1][ib] = duy/100.;
//               //qb0_re[2][ib] = dt;
//               //qb0_re[3][ib] = dp;
//   
//               dux = ux*delta*sin(w*pi2*ibpa);
//               duy = uy*delta*sin(w*pi2*ibpa + pi2/2.);
//               //dt = (1.-gam)/(gam*rg) * (ux*dux + uy*duy) * sin(w*pi2*ibpa);
//               drho = ((gam-1)/gam) * (rho*rho/p) * delta * sin(w*pi2*ibpa) * (ux*ux + uy*uy);
//               dt =-t*drho/rho;
//               dp = 0;
//   
//               qb_im[0][ib] = dux;
//               qb_im[1][ib] = duy;
//               qb_im[2][ib] = dt;
//               qb_im[3][ib] = dp;
//               qb0_im[0][ib] = qb_im[0][ib];
//               qb0_im[1][ib] = qb_im[1][ib];
//               qb0_im[2][ib] = qb_im[2][ib];
//               qb0_im[3][ib] = qb_im[3][ib];
//   
               //qb0_im[0][ib] = dux/100.;
               //qb0_im[1][ib] = duy/100.;
               //qb0_im[2][ib] = dt;
               //qb0_im[3][ib] = dp;
   
   //            fle << qb0_re[0][ib] << " " << qb0_re[1][ib] << " " << qb0_re[2][ib] << " " << qb0_re[3][ib] << " "
   //                << qb0_im[0][ib] << " " << qb0_im[1][ib] << " " << qb0_im[2][ib] << " " << qb0_im[3][ib] << "\n";
               w= 0; 
               for( ix=0;ix<nx;ix++ )
              {
                  w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
              }
               for( ix=0;ix<nx;ix++ )
              {
                  xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
              }
   
           }
        }
         else if(nx==3)
        {
   //         ofstream fle;
   //         fle.open("bcs.dat");
            //cout << ibs << " " << ibe << "\n";
            Real ph[10][200];
            nl = 3;
            n = (ibe-ibs)/nl;
         
            i=0; 
            for( il=0; il<nl; il++)
           {
              for( ib=0; ib<n; ib++)
             {
                unhlp[i][0] = il; 
                unhlp[i][1] = ib; 
                i++;
   
                w = ((Real)ib+0.5)/(Real)(n);
   //             w = 1-w;
                ph[il][ib] = w;
             }
           } 
            assert(i==(ibe-ibs));
   
            for( ib=ibs;ib<ibe;ib++ )
           {
               iq= iqbq[0][ib];
   
               Real ux, ur, ut, rho, p, t;
               Real dux, dur, dut, dt, dp, delta, drho;
               Real gam = 1.4;
               Real rg = 287/10000.;
   
   //            w = ((Real)ib+0.5)/(Real)(ibe-ibs);
   //            w = 1-w;
               w = ph[unhlp[ib][0]][unhlp[ib][1]];
   
               ux  = qb0[0][ib];
               ur  = qb0[1][ib];
               ut  = qb0[2][ib];
               t   = qb0[3][ib];
               p   = qb0[4][ib];
               rho  = p/(rg*t);
   
               //delta = 0.20;
               delta = 0.1;
   
               //cout << ux << " " << uy << " " << t << " " << p << " " << delta << " " << w << " " << pi2 << " " << ibpa << " ==== \n";
   
               dux = ux*delta*cos(w*pi2*ibpa);
               dur = 0;
               dut = ut*delta*cos(w*pi2*ibpa + pi2/2.);
               //dt = (1.-gam)/(gam*rg) * (ux*dux +uy*duy) * cos(w*pi2*ibpa);
               drho = ((gam-1)/gam) * (rho*rho/p) * delta * cos(w*pi2*ibpa) * (ux*ux + ut*ut);
               dt =-t*drho/rho;
               dp = 0;
   
   
               qb_re[0][ib] = dux;
               qb_re[1][ib] = dur;
               qb_re[2][ib] = dut;
               qb_re[3][ib] = dt;
               qb_re[4][ib] = dp;
   
               dux = ux*delta*sin(w*pi2*ibpa);
               dur = 0;
               dut = ut*delta*sin(w*pi2*ibpa + pi2/2.);
               //dt = (1.-gam)/(gam*rg) * (ux*dux + uy*duy) * sin(w*pi2*ibpa);
               drho = ((gam-1)/gam) * (rho*rho/p) * delta * sin(w*pi2*ibpa) * (ux*ux + ut*ut);
               dt =-t*drho/rho;
               dp = 0;
   
               qb_im[0][ib] = dux;
               qb_im[1][ib] = dur;
               qb_im[2][ib] = dut;
               qb_im[3][ib] = dt;
               qb_im[4][ib] = dp;
   
               //qb0_im[0][ib] = dux/100.;
               //qb0_im[1][ib] = duy/100.;
               //qb0_im[2][ib] = dt;
               //qb0_im[3][ib] = dp;
   
   //            fle << qb0_re[0][ib] << " " << qb0_re[1][ib] << " " << qb0_re[2][ib] << " " << qb0_re[3][ib] << " "
   //                << qb0_im[0][ib] << " " << qb0_im[1][ib] << " " << qb0_im[2][ib] << " " << qb0_im[3][ib] << "\n";
   
               w= 0; 
               for( ix=0;ix<nx;ix++ )
              {
                  w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
              }
               for( ix=0;ix<nx;ix++ )
              {
                  xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
              }
           }
        }
     }
  }

   /*void cFreeFbndry::bcs_z( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb0_re[], Real *qb0_im[], Real *xqb[], Real *qb_re[],
                            Real *qb_im[], Int *iqbq[], Real *xq[], Real *wnb[], Int ifre, Real ibpa )
  {
      Int ix,ib,iq,iv,nx,nv;
      Real w;
      Int nl, n, i, il, *unhlp[2], jb , nfre, jl, nl1;
      complex<Real> z, z0[10], z1[10], img(0,1);
      Real zb_re[200][10][10], zb_im[200][10][10], xr[1000][2], r;
      ifstream fle;
      string fnm;

      nv= fld->getnv();
      nx= coo->getnx();
      if( ibe > ibs )
     {
         Real ph[200][200];

         unhlp[0] = new Int [ibe-ibs+1];
         unhlp[1] = new Int [ibe-ibs+1];

         //nl1 = 48;
         //nl1 = 43;
         nl1 = 2;
         for(il=0; il<nl1; il++)
        {
           //fnm = "./det/duct/0000/EXIT.zbnd." + strc(il) + ".dat";
           fnm = "./zbnd/EXIT.zbnd." + strc(il) + ".dat";
           fle.open(fnm.c_str());
           if(!fle.good()) 
          {
             cout << "Error: can not open file " << fnm << "\n";
             assert(0);
          }
           //cout << "open file " << fnm << "\n";
           fle >> nfre;
           fle >> xr[il][0] >> xr[il][1];
           for(i=0; i<nfre; i++)
          {
             for(iv=0; iv<nv;iv++)
            {
               fle >> zb_re[il][i][iv] >> zb_im[il][i][iv];
               //cout << zb_re[il][i][iv] << " " << zb_im[il][i][iv] << "\n";;
            }
          }
           fle.close();
        }
     
         //nl = 29;
         //nl = 39; //s8
         nl = 2; //s8
         //nl = 29; //s8 coarse
         n = (ibe-ibs)/nl;
         i=0;
         for( il=0; il<nl; il++)
        {
           for( ib=0; ib<n; ib++)
          {
             unhlp[0][i] = il; 
             unhlp[1][i] = ib; 
             i++;

             w = ((Real)ib+0.5)/(Real)(n);
             //w = 1-w;
             ph[il][ib] = w;
          }
        } 
         if(i!=(ibe-ibs)) cout << "Mismatch on the boundary face "<<i<< " " << ibe - ibs << "\n";
         assert(i==(ibe-ibs));
  
         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= iqbq[0][ib];
            i = ifre;

            il = unhlp[0][ib];
            jb = unhlp[1][ib];

            r = sqrt(xb[1][ib]*xb[1][ib] + xb[2][ib]*xb[2][ib]);
//            cout << r << "\n";
            if(r>xr[nl1-1][1])
           {
              for(iv=0; iv<nv; iv++)
             {
                z0[iv] = zb_re[nl1-1][i][iv] + zb_im[nl1-1][i][iv]*img;
             }
           }
            else if(r<xr[0][1])
           {
              for(iv=0; iv<nv; iv++)
             {
                z0[iv] = zb_re[0][i][iv] + zb_im[0][i][iv]*img;
             }
           }
            else
           { 
              for(jl=1; jl<nl1; jl++)
             {
                if(r>=xr[jl-1][1]&& r<=xr[jl][1])
               {
                  w = (r-xr[jl-1][1])/(xr[jl][1] - xr[jl-1][1]);
                  for(iv=0; iv<nv; iv++)
                 {
                     z0[iv] = (zb_re[jl-1][i][iv]*(1-w) + zb_re[jl][i][iv]*w) +
                              (zb_im[jl-1][i][iv]*(1-w) + zb_im[jl][i][iv]*w)*img;
                 }
                  break;
               }
             }
           }

//            for(iv=0; iv<nv; iv++)
//           {
//              z0[iv] = zb_re[il][i][iv] + zb_im[il][i][iv]*img;
//           }

            w = ph[il][jb];

            //z = cos(ibpa*pi2*w) + sin(ibpa*pi2*w)*img;
            z = cos(ibpa*pi2*w) + sin(ibpa*pi2*w)*img;
            for(iv=0; iv<nv; iv++)
           {
              z1[iv] = z0[iv]*z;
              qb_re[iv][ib] = z1[iv].real();
              qb_im[iv][ib] = z1[iv].imag();
//              cout << z0[iv] << "\n";
           }

            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
           }
            for( ix=0;ix<nx;ix++ )
           {
               xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
           }
        }
         zvel( ibs, ibe, NULL, xqb, qb_re, qb_re );
         zvel( ibs, ibe, NULL, xqb, qb_im, qb_im );
         for(ib=ibs; ib<ibe; ib++)
        {
            for(iv=0; iv<nv; iv++)
           {
               qb0_re[iv][ib] = qb_re[iv][ib];
               qb0_im[iv][ib] = qb_im[iv][ib];
           }
        }
         delete[] unhlp[0]; unhlp[0]=NULL;
         delete[] unhlp[1]; unhlp[1]=NULL;
     }
//      exit(0);
  }*/

   /*void cFreeFbndry::bcs_z( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb0_re[], Real *qb0_im[], Real *xqb[], Real *qb_re[],
                            Real *qb_im[], Int *iqbq[], Real *xq[], Real *wnb[], Int ifre, Real ibpa )
  {
      Int ix,ib,iq,iv,nx,nv;
      Real w;
      Int nl, n, i, il, *unhlp[2], jb , nfre, jl, nl1, nbld;
      complex<Real> z, z0[10], z1[10], img(0,1);
      Real zb_re[200][10][10], zb_im[200][10][10], rt[1000][2], r, t, dt;
      ifstream fle;
      string fnm, sdum;

      nv= fld->getnv();
      nx= coo->getnx();
      if( ibe > ibs )
     {
         Real ph[200][200];

         unhlp[0] = new Int [ibe-ibs+1];
         unhlp[1] = new Int [ibe-ibs+1];

         ifstream fle;
         fnm = "./zbnd/EXIT.zbnd.dat";
         fle.open(fnm.c_str());
         if(!fle.good()) cout << "Error: can not open file " << fnm << "\n";
         fle >> sdum >> nbld >> nl1 >> nfre;
         for(il=0; il<nl1; il++)
        {
           for(i=0; i<nfre; i++)
          {
             fle >> rt[il][0] >> rt[il][1];
             for(iv=0; iv<nv; iv++)
            {
               fle >> zb_re[il][i][iv];
            }
             for(iv=0; iv<nv; iv++)
            {
               fle >> zb_im[il][i][iv];
            }
          }
        }
         fle.close();

         for(il=0; il<nl1; il++)
        {
           for(iv=0; iv<nv; iv++)
          {
             cout << zb_re[il][0][iv] << " ";
          }
           for(iv=0; iv<nv; iv++)
          {
             cout << zb_im[il][0][iv] << " ";
          }
           cout << "\n";
        }

         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= iqbq[0][ib];
            i = ifre;

            t = atan2(xb[1][ib], xb[2][ib]);
            r = sqrt(xb[1][ib]*xb[1][ib] + xb[2][ib]*xb[2][ib]);
//            cout << r << "\n";
            if(r>rt[nl1-1][0])
           {
              for(iv=0; iv<nv; iv++)
             {
                z0[iv] = zb_re[nl1-1][i][iv] + zb_im[nl1-1][i][iv]*img;
             }
              dt = t - rt[nl1-1][1];
           }
            else if(r<rt[0][0])
           {
              for(iv=0; iv<nv; iv++)
             {
                z0[iv] = zb_re[0][i][iv] + zb_im[0][i][iv]*img;
             }
              dt = t - rt[0][1];
           }
            else
           { 
              for(jl=1; jl<nl1; jl++)
             {
                if(r>=rt[jl-1][0]&& r<=rt[jl][0])
               {
                  w = (r-rt[jl-1][0])/(rt[jl][0] - rt[jl-1][0]);
                  for(iv=0; iv<nv; iv++)
                 {
                     z0[iv] = (zb_re[jl-1][i][iv]*(1-w) + zb_re[jl][i][iv]*w) +
                              (zb_im[jl-1][i][iv]*(1-w) + zb_im[jl][i][iv]*w)*img;
                 }
                  dt = rt[jl-1][1]*(1-w) + rt[jl][1]*w;
                  dt = t - dt;
                  break;
               }
             }
           }

            //Real pitch = pi2/nbld;
            Real pitch = pi2/136;
            z = cos(pi2*ibpa*dt/pitch) + sin(-pi2*ibpa*dt/pitch)*img;
            for(iv=0; iv<nv; iv++)
           {
              z1[iv] = z0[iv]*z;
              //qb_re[iv][ib] = z1[iv].real();
              //qb_im[iv][ib] = z1[iv].imag();
              qb_re[iv][ib] = z1[iv].imag();
              qb_im[iv][ib] = z1[iv].real();
//              cout << z0[iv] << "\n";
           }

            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
           }
            for( ix=0;ix<nx;ix++ )
           {
               xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
           }
        }


         zvel( ibs, ibe, NULL, xqb, qb_re, qb_re );
         zvel( ibs, ibe, NULL, xqb, qb_im, qb_im );
         for(ib=ibs; ib<ibe; ib++)
        {
            for(iv=0; iv<nv; iv++)
           {
               qb0_re[iv][ib] = qb_re[iv][ib];
               qb0_im[iv][ib] = qb_im[iv][ib];
           }
        }

         delete[] unhlp[0]; unhlp[0]=NULL;
         delete[] unhlp[1]; unhlp[1]=NULL;
     }
//      exit(0);
  }*/

   void cFreeFbndry::grad_z( Int ibs, Int ibe, Real *xqb[], Int *iqbq[], Real *xq[], Int *ijdx[],           
                            Real *dxdx[], Real **dqdx_re[], Real **dqdx_im[], Real *wnb[], Real *wxdb[] )
  {
      Real dxdx1[9];
      Real **dqdx1_re;
      Real **dqdx1_im;
      Real dx[3],*dq_re, *dq_im;

      Int  x,ix,jx,ib,iq,iv,nx,nv,iql,iqr;
      Real w,d;
      nv= fld->getnv();
      nx= coo->getnx();


      if( ibe > ibs )
     {
         dq_re= new Real[nv];
         dq_im= new Real[nv];
         dqdx1_re= new Real*[nv];
         dqdx1_im= new Real*[nv];
         for( iv=0;iv<nv;iv++ )
        {
            dqdx1_re[iv]= new Real[nx]; 
            dqdx1_im[iv]= new Real[nx]; 
        }
         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= iqbq[0][ib];

            for( ix=0;ix<nx;ix++ )
           {
               for( jx=0;jx<nx;jx++ )
              {
                  dxdx1[ijdx[ix][jx]]= dxdx[ijdx[ix][jx]][iq];
              }
               dxdx1[ijdx[ix][ix]]+= 1.e-4;
               for( iv=0;iv<nv;iv++ )
              {
                  dqdx1_re[iv][ix]= dqdx_re[iv][ix][iq];
                  dqdx1_im[iv][ix]= dqdx_im[iv][ix][iq];
              }
           }
            getrf( nx,ijdx, dxdx1 );
            for( iv=0;iv<nv;iv++ )
           {
               getrs( nx,ijdx, dxdx1, dqdx1_re[iv] );
               getrs( nx,ijdx, dxdx1, dqdx1_im[iv] );
           }

            w= 0;
            for( ix=0;ix<nx;ix++ )
           {
               dx[ix]= xqb[ix][ib]- xq[ix][iq];
               d= dx[ix];
               d*= d;
               w+= d;
           }
            w= 1./w;
            
            for( iv=0;iv<nv;iv++ )
           {
               dq_re[iv]= 0;
               dq_im[iv]= 0;
               for( ix=0;ix<nx;ix++ )
              {
                  dq_re[iv]+= dqdx1_re[iv][ix]*dx[ix];
                  dq_im[iv]+= dqdx1_im[iv][ix]*dx[ix];
              }
           }

            for( ix=0;ix<nx;ix++ )
           {
               for( jx=0;jx<nx;jx++ )
              {
                  dxdx[ijdx[ix][jx]][iq]+= w*dx[ix]*dx[jx];
              }
           }
            for( iv=0;iv<nv;iv++ )
           {
               for( ix=0;ix<nx;ix++ )
              {
                  dqdx_re[iv][ix][iq]+= w*dx[ix]*dq_re[iv];
                  dqdx_im[iv][ix][iq]+= w*dx[ix]*dq_im[iv];
              }
           }
        }
         for( iv=0;iv<nv;iv++ )
        {
            delete[] dqdx1_re[iv]; dqdx1_re[iv]= NULL;
            delete[] dqdx1_im[iv]; dqdx1_im[iv]= NULL;
        }
         delete[] dqdx1_re; dqdx1_re= NULL;
         delete[] dqdx1_im; dqdx1_im= NULL;
         delete[] dq_re; dq_re= NULL;
         delete[] dq_im; dq_im= NULL;
     }
  }

   void zvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipq ) ip= ipq[0][jp];
         x0= x[0][jp];
         x1= x[1][jp];
         x2= x[2][jp];
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r;
         sth= x1/r;
         q[0][ip]= bq[0][jp];
         Real tmp= bq[1][jp];
         q[1][ip]= sth*tmp +cth*bq[2][jp];
         q[2][ip]= cth*tmp -sth*bq[2][jp];
     }
  }

   void cFreeFbndry::dsflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *zcb_re[], Real *zcb_im[], Real *zb_re[], Real *zb_im[], Real *rhsb[],
                            Int *ibq[], Real *xq[], Real *q[], Real *aux[], Real *zc_re[], Real *zc_im[], Real *z_re[], Real *z_im[], Real *rhs[], Real *xc[],
                            Real *wn[], Real *wxdc[]  )
  {
      fld->dsflx( ibs,ibe, NULL, xb, qb,auxb,zcb_re, zcb_im, zb_re, zb_im, rhsb,
                         ibq[0], xq, q,aux,  zc_re,  zc_im,  z_re,  z_im,  rhs, xc, wn, wxdc, false );
//           fld->dsflx( ics,ice, ifq[0], xq,q, aux, zc_re[i], zc_im[i], z_re[i], z_im[i], r,
//                                ifq[1], xq,q, aux, zc_re[i], zc_im[i], z_re[i], z_im[i], r,
//                                xc,wnc,wxdc );

  }

   void cFreeFbndry::bcs_z( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb0_re[], Real *qb0_im[], Real *xqb[], Real *qb_re[],
                            Real *qb_im[], Int *iqbq[], Real *xq[], Real *wnb[] )
  {
      Int ix,ib,iq,iv,nx,nv;
      Real w;
      nv= fld->getnv();
      nx= coo->getnx();
      if( ibe > ibs )
     {
         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= iqbq[0][ib];
            if(qb0_re==NULL)
           {
               for( iv=0;iv<nv;iv++ )
              {
                  qb_re[iv][ib]= 0;
                  qb_im[iv][ib]= 0;
              }
           }
            else
           {
               for( iv=0;iv<nv;iv++ )
              {
                  qb_re[iv][ib]= qb0_re[iv][ib];
                  qb_im[iv][ib]= qb0_im[iv][ib];
              }
           }
            w= 0;
            for( ix=0;ix<nx;ix++ )
           {
               w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
           }
            for( ix=0;ix<nx;ix++ )
           {
               xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
           }

        }
     }
  }

   void cFreeFbndry::report_zsetup()
  {
      Int ig, ifre;

      if(nfre_reci>0 || nfre_send>0 || nfre_send_lin>0)
     {
         cout << "==========================================================\n";
         cout << "---------------meanflow---------------\n";
         cout << "nfre_send: " << nfre_send << "\n";
         cout << "nfre_reci: " << nfre_reci << "\n";
         cout << "---------------linearized flow---------------\n";
         cout << "nfre_send_lin: " << nfre_send_lin << "\n";
         for(ifre=0; ifre<nfre_send_lin; ifre++)
        {
            cout << ifre << " :harmonic " << lin_dft[ifre] << " with wavenumber " << send_lin_wavnum[ifre] << "\n";
        }
         cout << "==========================================================\n"; 
     }
      
  }
