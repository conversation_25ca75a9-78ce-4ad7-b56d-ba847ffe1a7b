   using namespace std;

#  include <domain/cfd/bndry/bndry.h>

  #pragma acc routine seq
   void ltoffset( Int jp, Int iv, Real f, Real *sv, Int n )
  {
      Real y,z,cth,sth;
      cth= cos(f);
      sth= sin(f);
      y= sv[ADDR(iv,jp,n)];
      z= sv[ADDR(iv+1,jp,n)];
      sv[ADDR(iv,jp,n)]= y*cth+ z*sth;
      sv[ADDR(iv+1,jp,n)]=-y*sth+ z*cth;
  }

   cSlideFbndry::cSlideFbndry()
  {
      idt=NULL;
      isec=NULL;
      kdt=NULL;
      ptch=0;
     #pragma acc enter data copyin(this,ptch)
  }

   cSlideFbndry::~cSlideFbndry()
  {
     #pragma acc exit data delete(this,ptch)
     #pragma acc exit data delete(idt,isec)
     #ifdef _OPENACC
      cudaFree(kdt); kdt= NULL;
     #else
      delete   kdt; kdt= NULL;
     #endif
      delete[] idt; idt= NULL;
      delete[] isec; isec= NULL;
  }

   void cSlideFbndry::request( Int ibs, Int ibe, Real tm, Real *xb[], Int *n, Int *m, Int *l, Real **sx )
  {
      Int ix,ib,jb,nx;
      Real w;
      nx= coo->getnx();
      Real      *x[3];
     *n=nx;
     *m=ibe-ibs;
     *l=fld->getnv();
     *sx= new Real[nx*(ibe-ibs)];
      subv( nx,(ibe-ibs),*sx,x );
      //cout << "sliding plane requests "<<ibe-ibs<<" values \n";

      for( ix=0;ix<nx;ix++ )
     {
         jb=0;
         for( ib=ibs;ib<ibe;ib++ )
        {
            x[ix][jb]= xb[ix][ib];
            jb++;
        }
     }
      coo->toffset( 0,jb, 1,tm, x );


      /*cout << "# " << this << " " << tm << " inside request, after toffset\n";
      for( ib=0;ib<jb;ib++ )
     {
         cout << this << " ";
         for( ix=0;ix<nx;ix++ )
        {
           cout << x[ix][ib] << " ";
        }
         cout << "\n";
     }*/
  }

   void cSlideFbndry::request( Int ibs, Int ibe, Real tm, Real *sxb, Int nbb, Int *n, Int *m, Int *l, Real **sx )
  {
      Int ix,ib,jb,nx,tmpn;
      Real w;
      nx= coo->getnx();
      Real      *x[3],*tmpsx;
     *n=nx;
     *m=ibe-ibs;
     *l=fld->getnv();
     *sx= new Real[nx*(ibe-ibs)];
      subv( nx,(ibe-ibs),*sx,x );
      //cout << "sliding plane requests "<<ibe-ibs<<" values \n";

      tmpsx = *sx;
      tmpn = ibe-ibs;

      for( ib=ibs;ib<ibe;ib++ )
     {
         jb = ib-ibs;
         tmpsx[ADDR(0,jb,tmpn)]= sxb[ADDR(0,ib,nbb)];
         tmpsx[ADDR(1,jb,tmpn)]= sxb[ADDR(1,ib,nbb)];
         tmpsx[ADDR(2,jb,tmpn)]= sxb[ADDR(2,ib,nbb)];
     }
      jb=tmpn;
      coo->toffset( 0,jb, 1,tm, tmpsx,tmpn );


      /*cout << "# " << this << " " << tm << " inside request, after toffset\n";
      for( ib=0;ib<jb;ib++ )
     {
         cout << this << " ";
         for( ix=0;ix<nx;ix++ )
        {
           cout << x[ix][ib] << " ";
        }
         cout << "\n";
     }*/
  }

   void cSlideFbndry::service( Int nxr, Int nvr, Int nqr, Real tm, Real *xr[], Real **sqr, Int iqs, Int iqe, 
                               Real *xq[], Real  *q[], Real  *aux[], Real *dxdx[], Real **dqdx[], Int ibs, Int ibe, 
                               Int *ibq[], Real *xb[], Real *qb[], Real *auxb[], bool bposix)
  {
       //cout << "sliding plane services " << this << "\n";
       Int nx,iv,ix,ib,iq,it,ir,is0;
       Real **qr, y[3], d, x0[3];
       Int is, asct;
       //if(!bposix) cout << "mutex free sliding plane\n";

       if(bposix) lock();

      *sqr= new Real[nqr*(nvr+1)];
       qr= new Real*[nvr+1];
       subv( nvr+1,nqr,*sqr,qr );

       if( ibe > ibs )
      {
          if( !kdt )
         {
             //cout << "create kd tree............\n";
             Int n;
             nx= coo->getnx();
             Real *tmp[3], *tmp1[3];
             Real  *stmp, *stmp1;
 
             n= ibe-ibs;

             //a way to get the pitch
             Real *tmpy0[3], *tmpy1[3], tmpy[3];
             tmpy0[0] = new Real [1];  tmpy0[1] = new Real [1];  tmpy0[2] = new Real [1];
             tmpy1[0] = new Real [1];  tmpy1[1] = new Real [1];  tmpy1[2] = new Real [1];
             tmpy0[0][0] = 1;
             tmpy0[1][0] = 1;
             tmpy0[2][0] = 0;
             tmpy1[0][0] = 1;
             tmpy1[1][0] = 1;
             tmpy1[2][0] = 0;
             tmpy[0] = tmpy0[0][0];
             tmpy[1] = tmpy0[1][0];
             tmpy[2] = tmpy0[2][0];
             coo->coffset( 1., tmpy );
             tmpy1[0][0] = tmpy[0];
             tmpy1[1][0] = tmpy[1];
             tmpy1[2][0] = tmpy[2];
             coo->bcoor( 0, 1, tmpy0, tmpy0 );
             coo->bcoor( 0, 1, tmpy1, tmpy1 );
             //cout << tmpy0[0][0] << " " << tmpy0[1][0] << " " << tmpy0[2][0] << "\n";
             //cout << tmpy1[0][0] << " " << tmpy1[1][0] << " " << tmpy1[2][0] << "\n";
             ptch= fabs(tmpy1[2][0] - tmpy0[2][0]);
             if(fabs(ptch)<1e-6)
            {
                //whole annulus
                asct = 1;
                ptch = pi2;
            }
             else
            {
                //round to the closest inteter
                asct = (Int)std::floor(pi2/ptch+0.5);
            }

             delete[] tmpy0[0]; tmpy0[0] = NULL;
             delete[] tmpy0[1]; tmpy0[1] = NULL;
             delete[] tmpy0[2]; tmpy0[2] = NULL;
             delete[] tmpy1[0]; tmpy1[0] = NULL;
             delete[] tmpy1[1]; tmpy1[1] = NULL;
             delete[] tmpy1[2]; tmpy1[2] = NULL;
  
             cout << "the pitch is " << ptch << " with number of repetitive sectors " << asct << "\n";
             n = asct*n;

             if( n>0 )
            {
                kdt= new cKdTree();
                idt= new Int[n];
                isec= new Int[n];
                stmp= new Real[nx*n];
                stmp1= new Real[nx*n];
                subv( nx,n, stmp,tmp );
                subv( nx,n, stmp1,tmp1 );
              
                for( ix=0;ix<nx;ix++ )
               {
                   for( ib=ibs;ib<ibe;ib++ )
                  {
                      iq= ibq[0][ib];
                      it= ib-ibs;
                      tmp[ix][it]= xq[ix][iq];
                      tmp1[ix][it] = xq[ix][iq];
                  }
               }
                for( ib=ibs;ib<ibe;ib++ )
               {
                   iq= ibq[0][ib];
                   it= ib-ibs;
                   idt[it]= iq;
                   isec[it] = 0;
               }

                for(is=1; is<asct; is++)
               { 
                   coo->toffset( 0,ibe-ibs, 1, ptch, tmp1 );
                   for( ib=ibs; ib<ibe; ib++)
                  {
                      iq= ibq[0][ib];
                      it= (ibe-ibs)*is + ib-ibs;

                      tmp[0][it] = tmp1[0][ib-ibs];   
                      tmp[1][it] = tmp1[1][ib-ibs];   
                      tmp[2][it] = tmp1[2][ib-ibs];   
                      idt[it] = iq;
                      isec[it] = is;
                  }
               }

                kdt->build( nx,n, tmp );
                delete[] stmp; stmp=NULL;
                delete[] stmp1; stmp1=NULL;
            }
         }
         // cout << "servicing request for "<<nqr<<" values\n";

         // for(ib=ibs; ib<ibe; ib++)
         //{
         //   iq = ibq[0][ib];
         //   cout << "ibq " << iq << " " << xq[0][iq] << " " << xq[1][iq] << " " << iqs << " " << iqe << "\n";
         //}

          coo->toffset( 0,nqr, 1,-tm, xr );

          for( ir=0;ir<nqr;ir++ )
         {
             for( ix=0;ix<nxr;ix++ )
            {
                x0[ix]= xr[ix][ir]; 
            }

            //cout << "bndx " << x0[0] << " " << x0[1] << "\n";

             kdt->nearest( x0,&it,&d );
             iq = idt[it];
             is = isec[it];

             Real *tmpsq, *tmpq[50];
             tmpsq= new Real[(nvr+1)];
             subv( nvr+1,1, tmpsq, tmpq );
             for( iv=0;iv<nvr;iv++ )
            {
                tmpq[iv][0] = q[iv][iq];
            }
             coo->toffset( 0,1, 1, ptch*is, tmpq );

             for(iv=0; iv<nvr; iv++)
            {
                //qr[iv][ir]= q[iv][iq];
                qr[iv][ir]= tmpq[iv][0];
/*              qr[iv][ir]+=( dqdx[0][iv][iq]*x0[0]+
                              dqdx[1][iv][iq]*x0[1]+
                              dqdx[2][iv][iq]*x0[2] );*/
                //cout << qr[iv][ir] << " ";
            }
             qr[nvr][ir]=d;
             delete[] tmpsq; tmpsq=NULL;
           
         }
          coo->toffset( 0,nqr, 1,tm, xr );
          coo->toffset( 0,nqr, 1,tm, qr );
      }
       else
      {
      //    cout << "no points on this boundary ...\n";
          for( ir=0;ir<nqr;ir++ )
         {
            for( iv=0; iv<nvr; iv++ )
           {
               qr[iv][ir] = -9999;
           }
            qr[nvr][ir] = big;
         }
      }


//     stophere();
       if(bposix) unlock();
       delete[] qr; qr=NULL;

  }

   void cSlideFbndry::service( Int nxr, Int nvr, Int nqr, Real tm, Real *sxr, Real **sqr, Int iqs, Int iqe, 
                               Real *sxq, Real  *sq, Real  *saux, Real *sdxdx, Real *sdqdx, Int ibs, Int ibe, 
                               Int *sibq, Real *sxb, Real *sqb, Real *sauxb, Int nq, Int nbb )
  {
       //cout << "sliding plane services " << this << "\n";
       Int nx,iv,ix,ib,iq,it,ir,is0;
       Real y[3];
       Int is, asct;
       Real *lsqr;
       cAu3xView<Real> qr, xr;
       //if(!bposix) cout << "mutex free sliding plane\n";
       Int n;
       nx= coo->getnx();

      *sqr= new Real[nqr*(nvr+1)];
       //qr= new Real*[nvr+1];
       //subv( nvr+1,nqr,*sqr,qr );
       lsqr = *sqr;

       xr.subv(nxr,  nqr,sxr);
       qr.subv(nvr+1,nqr,lsqr);

       if( ibe > ibs )
      {
         #pragma acc enter data copyin(lsqr[0:(nvr+1)*nqr],sxr[0:nxr*nqr])
          n= ibe-ibs;
          cTabData data;
          coo->get( &data );
          data.get( "assembly-sectors", &asct );
          ptch = pi2/(Real)asct;
          #pragma acc update device(ptch)
//          cout << "the pitch is " << ptch << " with number of repetitive sectors " << asct << "\n";
          n = asct*n;

          if( !kdt )
         {
             //cout << "create kd tree............\n";
             Real *tmp[3];
             Real  *stmp, *stmp1;
             cAu3xView<Real> tmp1;            
 
             if( n>0 )
            {
               #ifdef _OPENACC
                cudaMallocManaged(&kdt,sizeof(cKdTree));
               #else
                kdt= new cKdTree();
               #endif
                idt= new Int[n];
                isec= new Int[n];
                stmp= new Real[nxr*n];
                stmp1= new Real[nxr*n];
                tmp1.subv(nxr,n,stmp1);
               #pragma acc enter data copyin(idt[0:n],isec[0:n],stmp[0:nxr*n],stmp1[0:nxr*n])
 
               #pragma acc parallel loop gang vector\
                present(sibq[0:nbb],stmp[0:nxr*n],stmp1[0:nxr*n],sxq[0:nxr*nq],this) \
                default(none)
                for( ib=ibs;ib<ibe;ib++ )
               {
                   iq= sibq[ib];
                   it= ib-ibs;
                   for( ix=0;ix<nx;ix++ )
                  {
                      //iq= ibq[0][ib];
                      //tmp[ix][it]= xq[ix][iq];
                      //tmp1[ix][it] = xq[ix][iq];
                      stmp[ADDR(ix,it,n)]= sxq[ADDR(ix,iq,nq)];
                      stmp1[ADDR(ix,it,n)] = sxq[ADDR(ix,iq,nq)];
                  }
                   idt[it]= iq;
                   isec[it] = 0;
               }

                for(is=1; is<asct; is++)
               { 
                   //coo->toffset( 0,ibe-ibs, 1, ptch, tmp1 );
                   //coo->toffset( 0,ibe-ibs, 1, ptch, stmp1, n );
                   coo->toffset( 0,ibe-ibs, 1, ptch, tmp1 );
                  #pragma acc parallel loop\
                   present(sibq[0:nbb],stmp[0:nx*n],stmp1[0:nx*n],idt[0:n],isec[0:n],this) \
                   default(none)
                   for( ib=ibs; ib<ibe; ib++)
                  {
                      //iq= ibq[0][ib];
                      iq= sibq[ib];
                      it= (ibe-ibs)*is + ib-ibs;

                      //tmp[0][it] = tmp1[0][ib-ibs];   
                      //tmp[1][it] = tmp1[1][ib-ibs];   
                      //tmp[2][it] = tmp1[2][ib-ibs];   
                      stmp[ADDR(0,it,n)] = stmp1[ADDR(0,ib-ibs,n)];   
                      stmp[ADDR(1,it,n)] = stmp1[ADDR(1,ib-ibs,n)];   
                      stmp[ADDR(2,it,n)] = stmp1[ADDR(2,ib-ibs,n)];   
                      idt[it] = iq;
                      isec[it] = is;
                  }
               }

               #pragma acc update host(stmp[0:n*nx])
                subv( nx,n, stmp,tmp );
               
                kdt->build( nx,n, tmp );
               #pragma acc exit data delete(stmp,stmp1)
                delete[] stmp; stmp=NULL;
                delete[] stmp1; stmp1=NULL;
            }
         }

          coo->toffset( 0,nqr, 1,-tm, xr );

         #pragma acc parallel loop gang vector\
          private(iq,is) \
          present(lsqr[0:(nvr+1)*nqr],idt[0:n],isec[0:n],kdt[:1],sxr[0:nxr*nqr],sq[0:nvr*nq],this,ptch) \
          default(none)
          for( ir=0;ir<nqr;ir++ )
         {
             Int ipmin;
             Real x0[3],d;
             Real tmpsq[20];
             for( ix=0;ix<nxr;ix++ )
            {
                x0[ix]= sxr[ADDR(ix,ir,nqr)]; 
            }

             kdt->nearest( x0,&ipmin,&d );
             iq = idt[ipmin];
             is = isec[ipmin];
             for(iv=0; iv<nvr; iv++) tmpsq[iv] = sq[ADDR(iv,iq,nq)];
             ltoffset( 0, 1, ptch*is, tmpsq, 1 );

             for(iv=0; iv<nvr; iv++)
            {
                //qr[iv][ir]= q[iv][iq];
                //qr[iv][ir]= tmpq[iv][0];
                lsqr[ADDR(iv,ir,nqr)]= tmpsq[iv];
/*              qr[iv][ir]+=( dqdx[0][iv][iq]*x0[0]+
                              dqdx[1][iv][iq]*x0[1]+
                              dqdx[2][iv][iq]*x0[2] );*/
                //cout << qr[iv][ir] << " ";
            }
             //qr[nvr][ir]=d;
             lsqr[ADDR(nvr,ir,nqr)]=d;
           
         }
          coo->toffset( 0,nqr, 1,tm, xr );
          coo->toffset( 0,nqr, 1,tm, qr );
         #pragma acc exit data copyout(lsqr[0:(nvr+1)*nqr])
         #pragma acc exit data delete(sxr[0:nx*nqr])
      }
       else
      {
      //    cout << "no points on this boundary ...\n";
         #pragma acc enter data copyin(lsqr[0:(nvr+1)*nqr])
         #pragma acc parallel loop gang vector\
          present(lsqr[0:(nvr+1)*nqr]) \
          default(none)
          for( ir=0;ir<nqr;ir++ )
         {
            for( iv=0; iv<nvr; iv++ )
           {
               lsqr[ADDR(iv,ir,nqr)] = -9999;
           }
            lsqr[ADDR(nvr,ir,nqr)] = big;
         }
         #pragma acc exit data copyout(lsqr[0:(nvr+1)*nqr])
      }
  }

   void cSlideFbndry::service( Int nxr, Int nvr, Int nqr, Real tm, Real *sxr, Real **sqr, Int iqs, Int iqe, 
                               cAu3xView<Real>& xq, cAu3xView<Real>&  q, cAu3xView<Real>& aux, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, Int ibs, Int ibe, 
                               cAu3xView<Int>& ibq, cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb )
  {
       //cout << "sliding plane services " << this << "\n";
       Int nx,iv,ix,ib,iq,it,ir,is0;
       Real y[3];
       Int is, asct;
       Real *lsqr;
       //if(!bposix) cout << "mutex free sliding plane\n";
       Int n;
 
       Int nbb, nq;
       Int *sibq;
       Real *sxq, *sq;

       nq   = q.get_dim1();
       nbb  = qb.get_dim1();
       sibq = ibq.get_data();
       sxq  = xq.get_data();
       sq   = q.get_data();

       nx= coo->getnx();

      *sqr= new Real[nqr*(nvr+1)];
       //qr= new Real*[nvr+1];
       //subv( nvr+1,nqr,*sqr,qr );
       lsqr = *sqr;

       if( ibe > ibs )
      {
         #pragma acc enter data copyin(lsqr[0:(nvr+1)*nqr],sxr[0:nxr*nqr],this,ptch)
          n= ibe-ibs;
          cTabData data;
          coo->get( &data );
          data.get( "assembly-sectors", &asct );
          ptch = pi2/(Real)asct;
          #pragma acc update device(ptch)
//          cout << "the pitch is " << ptch << " with number of repetitive sectors " << asct << "\n";
          n = asct*n;

          if( !kdt )
         {
             //cout << "create kd tree............\n";
             Real *tmp[3];
             Real  *stmp, *stmp1;
 
             if( n>0 )
            {
               #ifdef _OPENACC
                cudaMallocManaged(&kdt,sizeof(cKdTree));
               #else
                kdt= new cKdTree();
               #endif
                idt= new Int[n];
                isec= new Int[n];
                stmp= new Real[nxr*n];
                stmp1= new Real[nxr*n];
               #pragma acc enter data copyin(idt[0:n],isec[0:n],stmp[0:nxr*n],stmp1[0:nxr*n])
 
               #pragma acc parallel loop gang vector\
                present(sibq[0:nbb],stmp[0:nxr*n],stmp1[0:nxr*n],sxq[0:nxr*nq],this) \
                default(none)
                for( ib=ibs;ib<ibe;ib++ )
               {
                   iq= sibq[ib];
                   it= ib-ibs;
                   for( ix=0;ix<nx;ix++ )
                  {
                      //iq= ibq[0][ib];
                      //tmp[ix][it]= xq[ix][iq];
                      //tmp1[ix][it] = xq[ix][iq];
                      stmp[ADDR(ix,it,n)]= sxq[ADDR(ix,iq,nq)];
                      stmp1[ADDR(ix,it,n)] = sxq[ADDR(ix,iq,nq)];
                  }
                   idt[it]= iq;
                   isec[it] = 0;
               }

                for(is=1; is<asct; is++)
               { 
                   //coo->toffset( 0,ibe-ibs, 1, ptch, tmp1 );
                   //coo->toffset( 0,ibe-ibs, 1, ptch, stmp1, n );
                   coo->toffsetgpu( 0,ibe-ibs, 1, ptch, stmp1, n, nx );
                  #pragma acc parallel loop\
                   present(sibq[0:nbb],stmp[0:nx*n],stmp1[0:nx*n],idt[0:n],isec[0:n],this) \
                   default(none)
                   for( ib=ibs; ib<ibe; ib++)
                  {
                      //iq= ibq[0][ib];
                      iq= sibq[ib];
                      it= (ibe-ibs)*is + ib-ibs;

                      //tmp[0][it] = tmp1[0][ib-ibs];   
                      //tmp[1][it] = tmp1[1][ib-ibs];   
                      //tmp[2][it] = tmp1[2][ib-ibs];   
                      stmp[ADDR(0,it,n)] = stmp1[ADDR(0,ib-ibs,n)];   
                      stmp[ADDR(1,it,n)] = stmp1[ADDR(1,ib-ibs,n)];   
                      stmp[ADDR(2,it,n)] = stmp1[ADDR(2,ib-ibs,n)];   
                      idt[it] = iq;
                      isec[it] = is;
                  }
               }

               #pragma acc update host(stmp[0:n*nx])
                subv( nx,n, stmp,tmp );
               
                kdt->build( nx,n, tmp );
               #pragma acc exit data delete(stmp,stmp1)
                delete[] stmp; stmp=NULL;
                delete[] stmp1; stmp1=NULL;
            }
         }

          coo->toffsetgpu( 0,nqr, 1,-tm, sxr, nqr, nxr );

         #pragma acc parallel loop gang vector\
          private(iq,is) \
          present(lsqr[0:(nvr+1)*nqr],idt[0:n],isec[0:n],kdt[:1],sxr[0:nxr*nqr],sq[0:nvr*nq],this) \
          default(none)
          for( ir=0;ir<nqr;ir++ )
         {
             Int ipmin;
             Real x0[3],d;
             Real tmpsq[20];
             for( ix=0;ix<nxr;ix++ )
            {
                x0[ix]= sxr[ADDR(ix,ir,nqr)]; 
            }

             kdt->nearest( x0,&ipmin,&d );
             iq = idt[ipmin];
             is = isec[ipmin];
             for(iv=0; iv<nvr; iv++) tmpsq[iv] = sq[ADDR(iv,iq,nq)];
             ltoffset( 0, 1, ptch*is, tmpsq, 1 );

             for(iv=0; iv<nvr; iv++)
            {
                //qr[iv][ir]= q[iv][iq];
                //qr[iv][ir]= tmpq[iv][0];
                lsqr[ADDR(iv,ir,nqr)]= tmpsq[iv];
/*              qr[iv][ir]+=( dqdx[0][iv][iq]*x0[0]+
                              dqdx[1][iv][iq]*x0[1]+
                              dqdx[2][iv][iq]*x0[2] );*/
                //cout << qr[iv][ir] << " ";
            }
             //qr[nvr][ir]=d;
             lsqr[ADDR(nvr,ir,nqr)]=d;
           
         }
          coo->toffsetgpu( 0,nqr, 1,tm, sxr, nqr, nxr );
          coo->toffsetgpu( 0,nqr, 1,tm, lsqr, nqr, nvr+1 );
         #pragma acc exit data copyout(lsqr[0:(nvr+1)*nqr])
         #pragma acc exit data delete(sxr[0:nx*nqr])
      }
       else
      {
      //    cout << "no points on this boundary ...\n";
         #pragma acc enter data copyin(lsqr[0:(nvr+1)*nqr])
         #pragma acc parallel loop gang vector\
          present(lsqr[0:(nvr+1)*nqr]) \
          default(none)
          for( ir=0;ir<nqr;ir++ )
         {
            for( iv=0; iv<nvr; iv++ )
           {
               lsqr[ADDR(iv,ir,nqr)] = -9999;
           }
            lsqr[ADDR(nvr,ir,nqr)] = big;
         }
         #pragma acc exit data copyout(lsqr[0:(nvr+1)*nqr])
      }
  }

   void cSlideFbndry::accept( Int ibs, Int ibe, Int nv, Int nq, Real tm, Real *q[], Real *w, Real *qb[] )
  {
      Int i,j;
      Int n=0;
      for( i=ibs;i<ibe;i++ )
     {
         if( w[i] > q[nv][i] )
        {
           
            n++;
            w[i]= q[nv][i];
            for( j=0;j<nv;j++ )
           {
               qb[j][i]= q[j][i];
           }
            coo->toffset( i,i+1, 1,-tm, qb );
        }
     }
     // coo->toffset( ibs,ibe, 1,-tm, qb );
      //coo->toffset( ibs,ibe, 1,tm, qb );
      //cout << "sliding plane accepts "<<n<<" values\n";
  }

   void cSlideFbndry::accept( Int ibs, Int ibe, Int nv, Int nq, Real tm, Real *sq, Real *w, cAu3xView<Real>& qb )
  {
      Int i,j;
      Int n=0;

      Int nbb;
      Real *sqb;

      nbb = qb.get_dim1();
      sqb = qb.get_data();

     #pragma acc enter data copyin(sq[0:(nv+1)*nq],w[0:nq],this)
     #pragma acc parallel loop \
      present(sq[0:(nv+1)*nq],w[0:nq],sqb[0:nv*nbb],this) \
      default(none)
      for( i=ibs;i<ibe;i++ )
     {
         //if( w[i] > q[nv][i] )
         if( w[i] > sq[ADDR(nv,i,nq)] )
        {
           
            //w[i]= q[nv][i];
            w[i]= sq[ADDR(nv,i,nq)];
            for( j=0;j<nv;j++ )
           {
               //qb[j][i]= q[j][i];
               sqb[ADDR(j,i,nbb)]= sq[ADDR(j,i,nq)];
           }
            //coo->toffset( i,i+1, 1,-tm, qb );
            //coo->toffset( i,i+1, 1,-tm, sqb,nbb );
            //coo->toffset( i,1,-tm, sqb,nbb );
            ltoffset( i,1,-tm, sqb,nbb );
        }
     }
     #pragma acc exit data delete(sq,this)
     #pragma acc exit data copyout(w[0:nq])
     // coo->toffset( ibs,ibe, 1,-tm, qb );
      //coo->toffset( ibs,ibe, 1,tm, qb );
      //cout << "sliding plane accepts "<<n<<" values\n";
  }


   cKdBox::cKdBox()
  { 
      x0[0]= -big;
      x0[1]= -big;
      x0[2]= -big;
      x1[0]=  big;
      x1[1]=  big;
      x1[2]=  big;
      parent=-1;
      child[0]=-1;
      child[1]=-1; 
      ips=-1; 
      ipe=-1; 
  };

   cKdBox::cKdBox(Int Parent, Int Child0, Int Child1, Int Ips, Int Ipe )
  {
      x0[0]= -big;
      x0[1]= -big;
      x0[2]= -big;
      x1[0]=  big;
      x1[1]=  big;
      x1[2]=  big;
      parent= Parent;
      child[0]= Child0;
      child[1]= Child1;
      ips= Ips;
      ipe= Ipe;
  };

   cKdBox::cKdBox( Real *X0, Real *X1, Int Parent, Int Child0, Int Child1, Int Ips, Int Ipe )
  {
      x0[0]= X0[0];
      x0[1]= X0[1];
      x0[2]= X0[2];
      x1[0]= X1[0];
      x1[1]= X1[1];
      x1[2]= X1[2];
      parent= Parent;
      child[0]= Child0;
      child[1]= Child1;
      ips= Ips;
      ipe= Ipe;
  };

   void cKdBox::corners( Real *X0, Real *X1 )
  {
      X0[0]= x0[0];
      X0[1]= x0[1];
      X0[2]= x0[2];
      X1[0]= x1[0];
      X1[1]= x1[1];
      X1[2]= x1[2];
  }

   void cKdBox::plot()
  {
      cout << x0[0] << " " <<x0[1] <<"\n";
      cout << x1[0] << " " <<x0[1] <<"\n";
      cout << x1[0] << " " <<x1[1] <<"\n";
      cout << x0[0] << " " <<x1[1] <<"\n";
      cout << x0[0] << " " <<x0[1] <<"\n";
      cout << "\n";
  }

   Real cKdBox::dist( Int Nx, Real *x )
  {
      Real d;
      Real dx;
      Int ix;
      d= 0.;
      for( ix=0;ix<Nx;ix++ )
     {
         if( x[ix]<x0[ix] ){ dx= x[ix]-x0[ix]; d+= dx*dx; };
         if( x[ix]>x1[ix] ){ dx= x[ix]-x1[ix]; d+= dx*dx; };
     }
      return (sqrt(d));
  }

   Real cKdBox::dist( Int Nx, Int ip, Real *x[] )
  {
      Real d,dx;
      Int ix;
      d= 0.;
      for( ix=0;ix<Nx;ix++ )
     {
         if( x[ix][ip]<x0[ix] ){ dx= x[ix][ip]-x0[ix]; d+= dx*dx; };
         if( x[ix][ip]>x1[ix] ){ dx= x[ix][ip]-x1[ix]; d+= dx*dx; };
     }
      return (sqrt(d));
  }

   cKdTree::cKdTree()
  {
      nx=0;
      nbxs=  0;
      bxs=   NULL;
      iprm=  NULL;
      iprmi= NULL;
      xp[0]= NULL;
      xp[1]= NULL;
      xp[2]= NULL;
     #pragma acc enter data copyin(this)   
  }

   void cKdTree::clean()
  {
      nx=0;
      nbxs=  0;
     #ifdef _OPENACC
      cudaFree(bxs);
      cudaFree(iprm);
      cudaFree(iprmi);
     #else
      delete[] bxs;
      delete[] iprm;
      delete[] iprmi;
     #endif
      bxs=   NULL;
      iprm=  NULL;
      iprmi= NULL;
     #ifdef _OPENACC
      cudaFree(xp[0]);
      cudaFree(xp[1]);
      cudaFree(xp[2]);
     #else
      delete[] xp[0]; xp[0]= NULL;
      delete[] xp[1]; xp[1]= NULL;
      delete[] xp[2]; xp[2]= NULL;
     #endif
      xp[0]= NULL;
      xp[1]= NULL;
      xp[2]= NULL;
  }

   void cKdTree::build( Int Nx, Int Np, Real *x[] )
  {

      if( Np < 3 )
     {
         cout << "never use trees for less than three points\n";
         exit(0);
     }
// stacks of parent boxes to be partitioned and partitioning directions
      Int pstk[50],dstk[50];

      Int  i,j,m,ntmp,istk,jb,ix;
      Int  iprn,idim;
      Int  ips,ipe;

      Real x0[3],x1[3];
      Real y0[3],y1[3];

      np= Np;
      nx= Nx;
     #ifdef _OPENACC
      cudaMallocManaged(&iprm,np*sizeof(Int));
      cudaMallocManaged(&iprmi,np*sizeof(Int));
     #else
      iprm=  new Int[np];
      iprmi= new Int[np];
     #endif

// points coordinates
     // for( ix=0;ix<nx;ix++ )
     //{
     //    xp[ix]= x[ix];
     //}

      for( ix=0;ix<nx;ix++ )
     {
        #ifdef _OPENACC
         cudaMallocManaged(&(xp[ix]),np*sizeof(Real));
        #else
         xp[ix] = new Real [np];
        #endif
     }

      for(ix=0; ix<nx; ix++)
     {
        for(Int ip=0; ip<np; ip++)
       {
          xp[ix][ip] = x[ix][ip];
       }
     }

// initialise points list
      for( i=0;i<np;i++)iprm[i]=i;
  
// compute number of boxes
      j=1;
      for( i=np; i; i >>= 1 )
     {
         j <<= 1;
     }
      nbxs= 2*np-(j>>1);
      if( j < nbxs ) nbxs= j;
      nbxs--;

// allocate boxes
     #ifdef _OPENACC
     cudaMallocManaged(&bxs,nbxs*sizeof(cKdBox));
     #else
      bxs= new cKdBox[nbxs]; 
     #endif
      bxs[0]= cKdBox(0,0,0, 0,np-1 );
      jb= 0;
      pstk[1]= 0;
      dstk[1]= 0;
      istk=1;

// create boxes: pending task list (kept in istk)

      while( istk )
     {

         iprn= pstk[istk];                        // parent box
         idim= dstk[istk--];                      // current dimension
         bxs[iprn].range( &ips, &ipe );           // range of points in parent box
         ntmp= ipe-ips+1;                           
         i= (ntmp-1)/2;
         select( i, ntmp,iprm+ips, x[idim] );       // split in two halves

         
         bxs[iprn].corners(x0,x1);
         bxs[iprn].corners(y0,y1);
         x0[idim]= x[idim][iprm[i+ips]];
         x1[idim]= x0[idim];
         bxs[++jb]= cKdBox( y0,x1, iprn,0,0, ips,ips+i );
         bxs[++jb]= cKdBox( x0,y1, iprn,0,0, ips+i+1,ipe );
         bxs[iprn].setChildren( jb-1,jb );
         if( i > 1 )
        {
            pstk[++istk]= jb-1;
            dstk[istk]= (idim+1)%nx;
        }
         if( ntmp-i > 3 )
        {
            pstk[++istk]= jb;
            dstk[istk]= (idim+1)%nx;
        }
     }
      for( i=0;i<np;i++ ) iprmi[iprm[i]]= i;

  }

   Int cKdTree::locate( Real *x )
  {

      Real x0[3],x1[3];
      Int ib,ib0,ib1,id;
      ib= 0;
      id= 0;
      bxs[ib].getChildren( &ib0,&ib1 );
      while( ib0 )
     {
         bxs[ib0].corners(x0,x1);
         if( x[id] <= x1[id] )
        {
            ib=ib0;
        }
         else
        {
            ib=ib1;
        }
         bxs[ib].getChildren( &ib0,&ib1 );
         id= ++id % nx;
     }
      return ib;
  }

   Int cKdTree::locate( Int ip0 )
  {
      Int ip,ips,ipe;
      Int ib,ib0,ib1;
      ip= iprmi[ip0];
      ib= 0; 
      bxs[ib].getChildren( &ib0,&ib1 );
      while( ib0 )
     {
         bxs[ib0].range( &ips,&ipe );
         if( ip <= ipe )
        {
            ib= ib0;
        }
         else
        {
            ib= ib1;
        }
         bxs[ib].getChildren( &ib0,&ib1 );
     }
      return ib;
  }

   void cKdTree::print( Int ib )
  {
      bxs[ib].print();
  }

   Real dist( Int nx, Real *x, Int ip, Real *xp[] )
  {
      Real tmp,d;
      Int  ix;
      tmp= xp[0][ip]-x[0]; 
      d= tmp*tmp;
      for( ix=1;ix<nx;ix++ )
     {
         tmp= xp[ix][ip]-x[ix]; d+= tmp*tmp;
     }
      d= sqrt(d);
      return d;
  }

   Real cKdTree::dist( Real *x, Int ip )
  {
      Real tmp,d;
      Int  ix;
      tmp= xp[0][ip]-x[0]; 
      d= tmp*tmp;
      for( ix=1;ix<nx;ix++ )
     {
         tmp= xp[ix][ip]-x[ix]; d+= tmp*tmp;
     }
      d= sqrt(d);
      return d;
  }

   Real cKdTree::dist( Int ip1, Int ip2 )
  {
      Real tmp,d;
      Int  ix;
      if( ip1 == ip2 )
     {
         return big;
     }
      else
     {
         tmp= xp[0][ip1]-xp[0][ip2]; 
         d= tmp*tmp;
         for( ix=1;ix<nx;ix++ )
        {
            tmp= xp[ix][ip1]-xp[ix][ip2]; d+= tmp*tmp;
        }
         d= sqrt(d);
         return d;
     }
  }

   void cKdTree::nearest( Real *x, Int *imin, Real *dmin )
  {
      Int ib,ip,ix,nstk;
      Int ips,ipe,ib0,ib1;
      Int stk[50];
      Real d;

     *dmin= big;

// nearest neighbour in the box containing the point

      ib= locate( x );
      bxs[ib].range( &ips,&ipe );
//    cout << "locate identifies box "<<ib<<" ";
//    print( ib );
      for( ip=ips;ip<=ipe;ip++ )
     {
         d= dist( x,iprm[ip] );
         if( d < *dmin )
        {
           *dmin= d;
           *imin=iprm[ip];
        }
     }
//    cout << "max distance is "<<d<<"\n";

// traverse tree discarding all boxes at distance >= dmin

      stk[1]= 0;
      nstk=1;
      while( nstk )
     {
         ib= stk[nstk--];
         if( bxs[ib].dist(nx,x) < *dmin )
        {
            bxs[ib].getChildren( &ib0,&ib1 );
            if( ib0 )
           {
               stk[++nstk]= ib0;
               stk[++nstk]= ib1;
           }
            else
           {
// end of tree box - find nearest neighbour
               bxs[ib].range( &ips,&ipe );
               for( ip=ips;ip<=ipe;ip++ )
              {
                  d= dist( x,iprm[ip] );
                  if( d < *dmin )
                 {
                    *dmin= d;
                    *imin=iprm[ip];
                 }
              }
           }
        }
     }
  }

   void cKdTree::nearest( Int ip0, Int n, Int *imin, Real *dmin )
  {

      Int ib,ib0,ib1,ib2;
      Int ip,jp,ips,ipe;

      Int nstk;
      Int stk[50];
      Real d;
      if( n > np-1 )
     {
         cout << "too many neighbours requested\n";
         std::exit(0);
     } 
      for( ip=0;ip<n;ip++ )  
     {
         dmin[ip]= big;
     }

      ib0= locate( ip0 );
      do 
     {
         ib0= bxs[ib0].getParent();
         bxs[ib0].range( &ips,&ipe );
     }while( ipe-ips < n );
 
      for( jp=ips;jp<=ipe;jp++ )
     {
         ip= iprm[jp];
         if( ip0 == ip )continue;
         d= dist( ip0,ip );
         if( d < dmin[0] )
        {
            dmin[0]= d;
            imin[0]= ip;
            if( n>1 )sift_down( n, dmin,imin );
        } 
     }

      nstk=1;
      stk[nstk]= 0;
      while( nstk )
     {
         ib= stk[nstk--];
         if( ib == ib0 ) continue;
         if( bxs[ib].dist(nx,ip0,xp) < dmin[0] )
        {
            bxs[ib].getChildren( &ib1,&ib2 );
            if( ib1 )
           {
               stk[++nstk]= ib1;
               stk[++nstk]= ib2;
           }
            else
           {
               bxs[ib].range( &ips,&ipe );
               for( jp=ips;jp<=ipe;jp++ )
              {
                  ip= iprm[jp];
                  if( ip0 == ip )continue;
                  d= dist( ip0,ip );
                  if( d < dmin[0] )
                 {
                     dmin[0]= d;
                     imin[0]= ip;
                     if( n>1 )sift_down( n, dmin,imin );
                 } 
              }
           }
        }
     }
  }

   cKdTree::~cKdTree()
  {
/*    np=0;
      nx=0;
      nbxs=  0;
      delete[] bxs;
      delete[] iprm;
      delete[] iprmi;
      bxs=NULL;
      iprm=NULL;
      iprmi=NULL;*/
     #pragma acc exit data delete (this)   
      clean();
  }
   void cKdTree::nearest( Real *x0, Int n, Int *imin, Real *dmin )
  {
      nearest( x0, imin, dmin );
      nearest( imin[0] , n-1 , imin+1, dmin+1 );
  }

