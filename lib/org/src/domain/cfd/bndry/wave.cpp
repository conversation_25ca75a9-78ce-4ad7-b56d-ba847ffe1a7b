
   using namespace std;

#  include <domain/cfd/bndry/bndry.h>

   Real gaussian( Real w, Real tm, Real omega, Real pitch )
  {
     Real pi;
     Real y, y0, xs, xe, x, d;

     d = omega*tm;
     d = fmod(d, pitch)/pitch;
     if((d+w)>1.) d = d-1;
     w = d+w;

     xs = -5;
     xe = 5;
     x = xs*(1-w) + xe*w;


     pi = 4*atan(1);

     y = (1/sqrt(0.4*pi))*exp(-x*x/0.4);
     y0 = (1/sqrt(0.4*pi))*exp(-0*0/0.4);
     return y/y0;
  }


   cWaveFbndry::cWaveFbndry()
  {
      nbl= -1;
      sxbvg=(Real* )NULL;
      sqbvg=(Real* )NULL;
      swbvg=(Real* )NULL;
      xbvg =(Real**)NULL;
      qbvg =(Real**)NULL;
      wbvg =(Real**)NULL;
      for(Int i=0; i<20; i++) mixvar[i] = NULL;
  }

   cWaveFbndry::~cWaveFbndry()
  {
      nbl= -1;
      delete[] sxbvg; sxbvg= NULL; delete[] xbvg; xbvg= NULL;
      delete[] sqbvg; sqbvg= NULL; delete[] qbvg; qbvg= NULL;
      delete[] swbvg; swbvg= NULL; delete[] wbvg; wbvg= NULL;
      for(Int i=0; i<20; i++) { delete[] mixvar[i]; mixvar[i] = NULL; };
  }


  /*void cWaveFbndry::bcs( Int ibs, Int ibe, Real tm, 
                          Real *xb[], Real *qb0[], Real *auxb0[], Real *xqb[], Real *qb[], Real *auxb[], 
                          Int  *iqbq[], Real *xq[], Real *q[], Real *aux[], 
                          Real **dqdx[], Real *wnb[], Real *wxdb[], Real *auxfb[] )
  {
      Int ix,ib,iq,iv,nx,nv;
      Real w;
      nv= fld->getnv();
      nx= coo->getnx();
      if( ibe > ibs )
     {
         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= iqbq[0][ib];
            for( iv=0;iv<nv;iv++ )
           {
               qb[iv][ib]= qb0[iv][ib];
           }
            //cout << "physical time " << tm << "\n";
// temporary hardwire: replace with specification of qb1 for harmonic components of boundary conditions!
            //qb[3][ib]= qb[3][ib]+ 0.01*sin(12.56*tm);
            //qb[3][ib]= qb[3][ib]+ 0.01*sin(12.56*tm);

//            Real dp, pav, flag;
//            flag = (ib+0.5)*(2*3.1415926)/(Real)(ibe-ibs);
//            pav  = qb[4][ib];
//            dp = 0.005*pav* cos(2*37.68*tm+flag);
//            qb[4][ib]= pav + dp;


            Real du, dv, delta, phase, nj, theta, dumag, af, pi, pitch, omega, dp, rho, umag;
            Real cs, ss, u0, v0;
            Real ht, h, gam, gam1, cp;

            gam = 1.4;
            gam1 = gam/(gam-1.); 
            cp = gam1*287/10000;

            nj = 1;
            pi = 4*atan(1);
            //delta = 0.20;
            //delta = 0.10;
            delta = 0.05;
            pitch = 2*pi/60.;
            //pitch = 0.09;
            omega = -200/100.;
            rho = 10000*qb[3][ib]/(qb[2][ib]*287);

            //convert velocity to rotor frame of reference
            u0 = qb[0][ib];
            v0 = qb[1][ib];
            v0 -= omega;

            umag = sqrt(u0*u0 + v0*v0);

            //gaussian wake
            //w = (ib+0.5)/(Real)(ibe-ibs);
            //dumag = umag*delta*gaussian( w, tm, omega, pitch );

            //cos wave 
            phase = (ib+0.5)*(2*pi)/(Real)(ibe-ibs);
            dumag = umag*delta*cos((pi*2*omega/pitch)*tm+phase);
       
            //work out du, dv for a certain wake orientation 
            theta = 45;
            theta = theta*pi/180;
            cs = cos(theta);
            ss = sin(theta);
            du =-dumag*cs;
            dv = dumag*ss;

            //freestream total enthalpy
            ht = cp*qb[2][ib] + 0.5*( u0*u0 + v0*v0);

            //static pressure is constant and the total enthalpy is constant
            qb[0][ib] += du;
            qb[1][ib] += dv;
            qb[2][ib]  = (ht - 0.5*( (u0+du)*(u0+du) + (v0+dv)*(v0+dv) ))/cp;                   

            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
           }
            for( ix=0;ix<nx;ix++ )
           {
               xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
           }

        }
     }
  }*/


   /*void cWaveFbndry::bcs( Int ibs, Int ibe, Real tm, 
                          Real *xb[], Real *qb0[], Real *auxb0[], Real *xqb[], Real *qb[], Real *auxb[], 
                          Int  *iqbq[], Real *xq[], Real *q[], Real *aux[], 
                          Real **dqdx[], Real *wnb[], Real *wxdb[], Real *auxfb[] )
  {
      Int ix,ib,iq,iv,nx,nv;
      Real w;
      nv= fld->getnv();
      nx= coo->getnx();
      if( ibe > ibs )
     {
         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= iqbq[0][ib];
            for( iv=0;iv<nv;iv++ )
           {
               qb[iv][ib]= qb0[iv][ib];
           }

            Real ux, uy, rho, p, t;
            Real dux, duy, dt, dp, delta, drho;
            Real gam = 1.4;
            Real rg = 287/10000.;
            Real phase, omega, pitch, r;
            //Real theta_min = -0.011178;
            Real theta_min = 0.0639;
            Real theta, dtheta;           
 
            w = ((Real)ib+0.5)/(Real)(ibe-ibs);
            w = 1.-w;

            //delta = 50;
            delta = 100;
            theta = atan2(xb[1][ib],xb[2][ib]);
            dtheta = theta - theta_min;

            //pitch = pi2/15;
            pitch = pi2/8;
            dt = delta*sin(pi2*dtheta/pitch);

            qb[3][ib] = qb0[3][ib] + dt;

            //cout << dux << " " << duy << " " << dt << " " << dp << "\n";
            //cout << qb[0][ib] << " " << qb[1][ib] << " " << qb[2][ib] << " " << qb[3][ib] << "\n";

            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
           }
            for( ix=0;ix<nx;ix++ )
           {
               xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
           }

        }
     }
  }*/

   void cWaveFbndry::bcs( Int ibs, Int ibe, Real tm,
                          Real *sxb, Real *sqb0, Real *sauxb0, Real *sxqb, Real *sqb, Real *sauxb,
                          Int *siqbq, Real *sxq, Real *sq, Real *saux,
                          Real *sdqdx, Real *swnb, Real *swxdb , Real *sauxfb, Int nq, Int nbb)
  {
//      Int ix,ib,iq,iv,nx,nv;
//      Real w;
//      nv= fld->getnv();
//      nx= coo->getnx();
//      if( ibe > ibs )
//     {
//         for( ib=ibs;ib<ibe;ib++ )
//        {
//            //iq= iqbq[0][ib];
//            iq= iqbq[0][ib];
//            for( iv=0;iv<nv;iv++ )
//           {
//               qb[iv][ib]= qb0[iv][ib];
//           }
//
//            Real dt, r_mean, theta_mean, r, theta, dr, dtheta, delta_temp, r_range, theta_range;
//            Real sigmar, sigmat;
//
//            delta_temp = 200;
//
//            r_mean = 0.3193955;
//            theta_mean = 0.46532328;;
//
//            r_range = 0.0757819;
//            theta_range = 0.78536125;
//
//            r = sqrt(xb[1][ib]*xb[1][ib] + xb[2][ib]*xb[2][ib]);
//            theta = atan2(xb[1][ib], xb[2][ib]);
//
//            dr = r - r_mean;
//            dtheta = theta - theta_mean;
//
//            sigmar = r_range*0.15;
//            sigmat = theta_range*0.15;
//            dt = delta_temp*exp(-( (dtheta*dtheta/(2*sigmat*sigmat) + dr*dr/(2*sigmar*sigmar)) ) );
//
//
//            qb[3][ib] = qb0[3][ib] + dt;
//
//            //cout << dux << " " << duy << " " << dt << " " << dp << "\n";
//            //cout << qb[0][ib] << " " << qb[1][ib] << " " << qb[2][ib] << " " << qb[3][ib] << "\n";
//
//            w= 0;
//            for( ix=0;ix<nx;ix++ )
//           {
//               w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
//           }
//            for( ix=0;ix<nx;ix++ )
//           {
//               xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
//           }
//
//        }
//     }
  }

   void cWaveFbndry::bcs( Int ibs, Int ibe, Real tm,
                          cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                          cAu3xView<Int>&  iqbq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                          cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
//      Int ix,ib,iq,iv,nx,nv;
//      Real w;
//      nv= fld->getnv();
//      nx= coo->getnx();
//      if( ibe > ibs )
//     {
//         for( ib=ibs;ib<ibe;ib++ )
//        {
//            //iq= iqbq[0][ib];
//            iq= iqbq[0][ib];
//            for( iv=0;iv<nv;iv++ )
//           {
//               qb[iv][ib]= qb0[iv][ib];
//           }
//
//            Real dt, r_mean, theta_mean, r, theta, dr, dtheta, delta_temp, r_range, theta_range;
//            Real sigmar, sigmat;
//
//            delta_temp = 200;
//
//            r_mean = 0.3193955;
//            theta_mean = 0.46532328;;
//
//            r_range = 0.0757819;
//            theta_range = 0.78536125;
//
//            r = sqrt(xb[1][ib]*xb[1][ib] + xb[2][ib]*xb[2][ib]);
//            theta = atan2(xb[1][ib], xb[2][ib]);
//
//            dr = r - r_mean;
//            dtheta = theta - theta_mean;
//
//            sigmar = r_range*0.15;
//            sigmat = theta_range*0.15;
//            dt = delta_temp*exp(-( (dtheta*dtheta/(2*sigmat*sigmat) + dr*dr/(2*sigmar*sigmar)) ) );
//
//
//            qb[3][ib] = qb0[3][ib] + dt;
//
//            //cout << dux << " " << duy << " " << dt << " " << dp << "\n";
//            //cout << qb[0][ib] << " " << qb[1][ib] << " " << qb[2][ib] << " " << qb[3][ib] << "\n";
//
//            w= 0;
//            for( ix=0;ix<nx;ix++ )
//           {
//               w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
//           }
//            for( ix=0;ix<nx;ix++ )
//           {
//               xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
//           }
//
//        }
//     }
  }


   /*void cWaveFbndry::bcs( Int ibs, Int ibe, Real tm, 
                          Real *xb[], Real *qb0[], Real *auxb0[], Real *xqb[], Real *qb[], Real *auxb[], 
                          Int  *iqbq[], Real *xq[], Real *q[], Real *aux[], 
                          Real **dqdx[], Real *wnb[], Real *wxdb[], Real *auxfb[] )
  {
      Int ix,ib,iq,iv,nx,nv;
      Real w;
      Real *qwrk[5];
      nv= fld->getnv();
      nx= coo->getnx();
      if( ibe > ibs )
     {
         for(iv=0; iv<nv; iv++)
        {
            qwrk[iv] = new Real [ibe];
            for(ib=ibs; ib<ibe; ib++)
           {
               iq= iqbq[0][ib];
               qwrk[iv][ib] = q[iv][iq];
           }
        }
         coo->bvel( ibs,ibe, iqbq,xb, q,qwrk );
         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= iqbq[0][ib];
            for( iv=0;iv<nv;iv++ )
           {
               qb[iv][ib]= qb0[iv][ib];
           }

            Real ux, ur, ut, rho, p, t;
            Real dux, dur, dut, dt, dp, delta, drho;
            Real gam = 1.4;
            Real rg = 287/10000.;
            Real phase, omega, pitch, r;

            w = ((Real)ib+0.5)/(Real)(ibe-ibs);
            w = 1.-w;

            ux  = qwrk[0][ib];
            ur  = qwrk[1][ib];
            ut  = qwrk[2][ib];
            t   = qwrk[3][ib];
            p   = qwrk[4][ib];
            rho  = p/(rg*t);

            delta = 0.01;
            int nwave = 2;
            omega = 65151.83201*nwave;
            pitch = 8*atan(1)/136.;
            omega /= 100.;

            dux = 0; 
            dur = 0; 
            dut = 0; 
            dp = 0; 
            dt = 0; 

            phase = pi2*w;
            //dux = ux*delta*cos(pi2*omega*tm/pitch + phase);
            dux = ux*delta*cos(omega*tm);
            dur = 0;
            //dut = ut*delta*cos(pi2*omega*tm/pitch + phase + pi2/2.);
            //dut = ut*delta*cos(omega*tm + phase + pi2/2.);
            //dt = (1.-gam)/(gam*rg) * (ux*dux +uy*duy) * cos(w*pi2*nwave);
            //drho = ((gam-1)/gam) * (rho*rho/p) * delta * cos(pi2*omega*tm/pitch + phase) * (ux*ux + ut*ut);
            //drho = ((gam-1)/gam) * (rho*rho/p) * delta * cos(omega*tm + phase) * (ux*ux + ut*ut);
            //dt =-t*drho/rho;

            qwrk[0][ib] += dux;
            qwrk[1][ib] += dur;
            qwrk[2][ib] += dut;
            qwrk[3][ib] += dt;
            qwrk[4][ib] += dp;

            //cout << dux << " " << duy << " " << dt << " " << dp << "\n";
            //cout << qb[0][ib] << " " << qb[1][ib] << " " << qb[2][ib] << " " << qb[3][ib] << "\n";

            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
           }
            for( ix=0;ix<nx;ix++ )
           {
               xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
           }
        }
         coo->zvel( ibs,ibe, NULL, xb, qwrk,qwrk );
         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= iqbq[0][ib];
            for( iv=0;iv<nv;iv++ )
           {
               qb[iv][ib]= qwrk[iv][ib];
               qb0[iv][ib]= qwrk[iv][ib];
           }
        }

         for(iv=0; iv<nv; iv++)
        {
            delete[] qwrk[iv]; qwrk[iv]=NULL;
        }

     }
  }*/
