
   using namespace std;

#include <iomanip>
#  include <domain/cfd/bndry/bndry.h>

   void zvel( Real *x,  Real *bq, Real *q );

   cFreeFbndry::cFreeFbndry()
  {
      nbl= -1;
      sxbvg=(Real* )NULL;
      sqbvg=(Real* )NULL;
      sqbav=(Real* )NULL;
      swbvg=(Real* )NULL;
      xbvg =(Real**)NULL;
      qbvg =(Real**)NULL;
      qbav =(Real**)NULL;
      wbvg =(Real**)NULL;

      smixvar=(Real* )NULL;
      for(Int i=0; i<20; i++) mixvar[i] = NULL;

      sqwrk=NULL; //delete[] qwrk;
      sxwrk=NULL; //delete[] xwrk;

      for(Int i=0; i<MXNFRE; i++)
     {
         szfftlin_re[i]=NULL;
         szfftlin_im[i]=NULL;
         zfftlin_re[i]=NULL;
         zfftlin_im[i]=NULL;

         send_lin_wavnum[i] = 0;
         lin_dft[i] = 0;
     }
      nfre_send_lin = 0;

      for(Int i=0; i<1000; i++)
     {
         nsl[i] = 0;
         c1s[i] = NULL;
         c2s[i] = NULL;
         c3s[i] = NULL;
         c4s[i] = NULL;
         c5s[i] = NULL;
         szfft_re[i]=NULL;
         szfft_im[i]=NULL;
         zfft_re[i]=NULL;
         zfft_im[i]=NULL;
         szfftlin_nrbc_re[i]=NULL;
         szfftlin_nrbc_im[i]=NULL;
         zfftlin_nrbc_re[i]=NULL;
         zfftlin_nrbc_im[i]=NULL;
     }

      sdflux=(Real* )NULL;
      dflux=(Real** )NULL;
//      c2s_re=NULL; 
//      c4s_re=NULL; 
//      c5s_re=NULL; 
//      c2s_im=NULL;
//      c4s_im=NULL; 
//      c5s_im=NULL;
  }

   cFreeFbndry::~cFreeFbndry()
  {
     if(sxbvg)
     {
         #pragma acc exit data delete(sxbvg)
         #pragma acc exit data delete(sqbvg)
         #pragma acc exit data delete(sqbav)
         #pragma acc exit data delete(swbvg)
         #pragma acc exit data delete(smixvar)
     }

     if(sqwrk)
     {
         #pragma acc exit data delete(sqwrk)
     }

     if(sxwrk)
     {
         #pragma acc exit data delete(sxwrk)
     }

      nbl= -1;
      delete[] sxbvg; sxbvg= NULL; delete[] xbvg; xbvg= NULL;
      delete[] sqbvg; sqbvg= NULL; delete[] qbvg; qbvg= NULL;
      delete[] sqbav; sqbav= NULL; delete[] qbav; qbav= NULL;
      delete[] swbvg; swbvg= NULL; delete[] wbvg; wbvg= NULL;
      for(Int i=0; i<20; i++) { delete[] mixvar[i]; mixvar[i] = NULL; };
      delete[] sdflux; sdflux= NULL; delete[] dflux; dflux= NULL;

      for(Int i=0; i<MXNFRE; i++)
     {
         delete[] szfftlin_re[i]; szfftlin_re[i]=NULL;
         delete[] szfftlin_im[i]; szfftlin_im[i]=NULL;
         delete[] zfftlin_re[i];   zfftlin_re[i]=NULL;
         delete[] zfftlin_im[i];   zfftlin_im[i]=NULL;
     }

      for(Int i=0; i<nsl[0]; i++)
     {
//         delete[] c2s_re[i]; c2s_re[i]=NULL;
//         delete[] c4s_re[i]; c4s_re[i]=NULL;
//         delete[] c5s_re[i]; c5s_re[i]=NULL;
//         delete[] c2s_im[i]; c2s_im[i]=NULL;
//         delete[] c4s_im[i]; c4s_im[i]=NULL;
//         delete[] c5s_im[i]; c5s_im[i]=NULL;

         delete[] c1s[i]; c1s[i] = NULL;
         delete[] c2s[i]; c2s[i] = NULL;
         delete[] c3s[i]; c3s[i] = NULL;
         delete[] c4s[i]; c4s[i] = NULL;
         delete[] c5s[i]; c5s[i] = NULL;

         delete[] szfft_re[i]; szfft_re[i]=NULL;
         delete[] szfft_im[i]; szfft_im[i]=NULL;
         delete[] zfft_re[i];   zfft_re[i]=NULL;
         delete[] zfft_im[i];   zfft_im[i]=NULL;

         delete[] szfftlin_nrbc_re[i]; szfftlin_nrbc_re[i]=NULL;
         delete[] szfftlin_nrbc_im[i]; szfftlin_nrbc_im[i]=NULL;
         delete[] zfftlin_nrbc_re[i];   zfftlin_nrbc_re[i]=NULL;
         delete[] zfftlin_nrbc_im[i];   zfftlin_nrbc_im[i]=NULL;
     }
//      delete[] c2s_re; c2s_re=NULL; 
//      delete[] c4s_re; c4s_re=NULL;
//      delete[] c5s_re; c5s_re=NULL;
//      delete[] c2s_im; c2s_im=NULL;
//      delete[] c4s_im; c4s_im=NULL;
//      delete[] c5s_im; c5s_im=NULL;

        delete[] sqwrk; sqwrk=NULL; //delete[] qwrk;
        delete[] sxwrk; sxwrk=NULL; //delete[] xwrk;
  }


   void cFreeFbndry::bcs( Int ibs, Int ibe, Real tm, 
                          Real *sxb, Real *sqb0, Real *sauxb0, Real *sxqb, Real *sqb, Real *sauxb, 
                          Int  *siqbq, Real *sxq, Real *sq, Real *saux, 
                          Real *sdqdx, Real *swnb, Real *swxdb, Real *sauxfb, Int nq, Int nbb )
  {
      Int ix,ib,iq,iv,nx,nv,naux;
      Real w;

      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();

      if( ibe > ibs )
     {
        #pragma acc enter data copyin(this) 
        #pragma acc parallel loop gang vector \
         present(sxb[0:nx*nbb],sqb0[0:nv*nbb],sauxb0[0:naux*nbb],sxqb[0:nx*nbb],sqb[0:nv*nbb],sauxb[0:naux*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
                 swnb[0:(nx+1)*nbb],swxdb[0:nbb],this ) \
         default(none)
         for( ib=ibs;ib<ibe;ib++ )
        {
            //iq= iqbq[0][ib];
            iq= siqbq[ib];
            for( iv=0;iv<nv;iv++ )
           {
               //qb[iv][ib]= qb0[iv][ib];
               sqb[ADDR(iv,ib,nbb)]= sqb0[ADDR(iv,ib,nbb)];
           }

            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               //w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
               w+= ( sxb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)] )*swnb[ADDR(ix,ib,nbb)];
           }
            for( ix=0;ix<nx;ix++ )
           {
               //xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
               sxqb[ADDR(ix,ib,nbb)]= sxq[ADDR(ix,iq,nq)]+ 2*w*swnb[ADDR(ix,ib,nbb)];
           }
        }
        #pragma acc exit data copyout(this) 
     }
  }

   void cFreeFbndry::bcs( Int ibs, Int ibe, Real tm,
                          cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                          cAu3xView<Int>&  iqbq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                          cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      Int ix,ib,iq,iv,nx,nv,naux;
      Real w;

      Int nbb, nq;

      Real *sxb, *sqb0, *sauxb0, *sxqb, *sqb, *sauxb;
      Int *siqbq;
      Real *sxq, *sq, *saux, *sdqdx, *swnb, *swxdb, *sauxfb;

      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();

      nbb = iqbq.get_dim0();
      nq  = q.get_dim1();
  
      sxb    = xb.get_data();
      sqb0   = qb0.get_data();
      sauxb0 = auxb0.get_data();
      sxqb   = xqb.get_data();
      sqb    = qb.get_data();
      sauxb  = auxb.get_data();
      siqbq  = iqbq.get_data();
      sxq    = xq.get_data();
      sq     = q.get_data();
      saux   = aux.get_data();
      sdqdx  = dqdx.get_data();
      swnb   = wnb.get_data();
      swxdb  = wxdb.get_data();
      sauxfb = auxb.get_data();

      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();

      if( ibe > ibs )
     {
        #pragma acc enter data copyin(this) 
        #pragma acc parallel loop gang vector \
         present(sxb[0:nx*nbb],sqb0[0:nv*nbb],sauxb0[0:naux*nbb],sxqb[0:nx*nbb],sqb[0:nv*nbb],sauxb[0:naux*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
                 swnb[0:(nx+1)*nbb],swxdb[0:nbb],this ) \
         default(none)
         for( ib=ibs;ib<ibe;ib++ )
        {
            //iq= iqbq[0][ib];
            iq= siqbq[ib];
            for( iv=0;iv<nv;iv++ )
           {
               //qb[iv][ib]= qb0[iv][ib];
               sqb[ADDR(iv,ib,nbb)]= sqb0[ADDR(iv,ib,nbb)];
           }

            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               //w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
               w+= ( sxb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)] )*swnb[ADDR(ix,ib,nbb)];
           }
            for( ix=0;ix<nx;ix++ )
           {
               //xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
               sxqb[ADDR(ix,ib,nbb)]= sxq[ADDR(ix,iq,nq)]+ 2*w*swnb[ADDR(ix,ib,nbb)];
           }
        }
        #pragma acc exit data copyout(this) 
     }
  }


   void cFreeFbndry::auxv( Int ibs, Int ibe, Real *qb[], Real *auxb[] )
  {
      fld->auxv( ibs,ibe, qb,auxb ); 
  }

   void cFreeFbndry::auxv( Int ibs, Int ibe, Real *sqb, Real *sauxb, Int nbb )
  {
      assert(0);
      //fld->auxvgpu( ibs,ibe, sqb,sauxb,nbb ); 
  }

   void cFreeFbndry::auxv( Int ibs, Int ibe, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, string arch )
  {
      fld->auxv( ibs,ibe, qb,auxb, arch );
  }

   //void cFreeFbndry::iflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *rhsb[], Int *ibq[], Real *xq[], Real *q[], Real *aux[], Real *rhs[], Real *wnb[], Real *wxdb[], Real *auxfb[]  )
   void cFreeFbndry::iflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *srhsb, Int *sibq, Real *sxq, Real *sq, Real *saux, Real *srhs, Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq  )
  {
      fld->iflx( ibs,ibe, NULL, sqb,sauxb,srhsb, sibq, sq,saux,srhs, swnb,swxdb, sauxfb, nbb, nq);
  }

  void cFreeFbndry::iflx( Int ibs, Int ibe, cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& rhsb, cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q,
                           cAu3xView<Real>& aux, cAu3xView<Real>& rhs, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      fld->iflx( ibs,ibe, NULL_iview, qb,auxb,rhsb, ibq, q,aux,rhs, wnb,wxdb, auxfb);
  }

   void cFreeFbndry::ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[],
                                  Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[],  
                                  Real *wnb[], Real *wxdb[], Real *auxfb[], cJacBlk *jac_df[2]  )
  {
      fld->ilhs( ibs,ibe, NULL, qb,auxb,lhsb, ibq[0], q,aux,lhs, wnb,wxdb, auxfb, jac_df );
  }
   //void cFreeFbndry::ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[],
   //                               Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[],  
   //                               Real *wnb[], Real *wxdb[], Real *auxfb[] )
   void cFreeFbndry::ilhs( Int ibs, Int ibe,   Real *sxb, Real *sqb, Real *sauxb, Real *slhsb,
                                    Int *sibq, Real *sxq, Real  *sq, Real *saux,  Real *slhsa,  
                                   Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq )
  {
      //fld->ilhs( ibs,ibe, NULL, qb,auxb,lhsb, ibq[0], q,aux,lhs, wnb,wxdb, auxfb );
      fld->ilhs( ibs,ibe, NULL, sqb,sauxb,slhsb, sibq, sq,saux,slhsa, swnb,swxdb,sauxfb,nbb,nq );
  }

   void cFreeFbndry::ilhs( Int ibs, Int ibe,    cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& lhsb,
                          cAu3xView<Int>& ibq,  cAu3xView<Real>& xq, cAu3xView<Real>& q,  cAu3xView<Real>& aux,  cAu3xView<Real>& lhsa,
                          cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      //fld->ilhs( ibs,ibe, NULL, qb,auxb,lhsb, ibq[0], q,aux,lhs, wnb,wxdb, auxfb );
      fld->ilhs( ibs,ibe, NULL_iview, qb,auxb,lhsb, ibq, q,aux,lhsa, wnb,wxdb,auxfb );
  }

   void cFreeFbndry::grad( Int ibs, Int ibe, Real *sxqb, Real *sqb, Int *siqbq, Real *sxq, Real *sq, 
                           Real *sdxdx, Real *sdqdx, Real *swnb, Real *swxdb, Int nq, Int nbb )
  {
      Real dxdx1[9];
      Real dqdx1[10][3];
      Real dx[3],dq[10];
      Int ijdx[3][3];

      Int  x,ix,jx,ib,iq,iv,nx,nv,iql,iqr;
      Real w,d;
      nv= fld->getnv();
      nx= coo->getnx();

      Int i=0;
      for( ix=0;ix<nx;ix++ )
     {
         for( jx=0;jx<nx;jx++ )
        {
            ijdx[ix][jx]= i++;
        }
     }

      if( ibe > ibs )
     {
        // dq= new Real[nv];
        // dqdx1= new Real*[nv];
        // for( iv=0;iv<nv;iv++ )
        //{
        //    dqdx1[iv]= new Real[nx]; 
        //}
        #pragma acc enter data copyin(this) 
        #pragma acc parallel loop gang vector \
         firstprivate(ijdx) \
         private(dxdx1,dqdx1,dx,dq) \
         present(sxqb[0:nx*nbb],sqb[0:nv*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],sdxdx[0:nx*nx*nq],sdqdx[0:nx*nv*nq], \
                 swnb[0:(nx+1)*nbb],swxdb[0:nbb],this ) \
         default(none)
         for( ib=ibs;ib<ibe;ib++ )
        {
            iq= siqbq[ib];

            for( ix=0;ix<nx;ix++ )
           {
               for( jx=0;jx<nx;jx++ )
              {
                  //dxdx1[ijdx[ix][jx]]= dxdx[ijdx[ix][jx]][iq];
                  dxdx1[ijdx[ix][jx]]= sdxdx[ADDR(ix,jx,iq,nq)];
              }
               dxdx1[ijdx[ix][ix]]+= 1.e-4;
               for( iv=0;iv<nv;iv++ )
              {
                  //dqdx1[iv][ix]= dqdx[iv][ix][iq];
                  dqdx1[iv][ix]= sdqdx[ADDR(iv,ix,iq,nq)];
              }
           }
            getrf( ijdx, dxdx1 );
            for( iv=0;iv<nv;iv++ )
           {
               getrs( ijdx, dxdx1, dqdx1[iv] );
           }

            w= 0;
            for( ix=0;ix<nx;ix++ )
           {
               //dx[ix]= xqb[ix][ib]- xq[ix][iq];
               dx[ix]= sxqb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)];
               d= dx[ix];
               d*= d;
               w+= d;
           }
            w= 1./w;
            
            for( iv=0;iv<nv;iv++ )
           {
               dq[iv]= 0;
               for( ix=0;ix<nx;ix++ )
              {
                  dq[iv]+= dqdx1[iv][ix]*dx[ix];
              }
           }

            for( ix=0;ix<nx;ix++ )
           {
               for( jx=0;jx<nx;jx++ )
              {
                  //dxdx[ijdx[ix][jx]][iq]+= w*dx[ix]*dx[jx];
                  #pragma acc atomic
                  sdxdx[ADDRG(ix,jx,iq,nq)]+= w*dx[ix]*dx[jx];
              }
           }
            for( iv=0;iv<nx;iv++ )
           {
               for( ix=0;ix<nx;ix++ )
              {
                  //dqdx[iv][ix][iq]+= w*dx[ix]*dq[iv];
                  #pragma acc atomic
                  sdqdx[ADDRG(iv,ix,iq,nq)]+= w*dx[ix]*dq[iv];
              }
           }
        }
        #pragma acc exit data copyout(this) 
        // for( iv=0;iv<nv;iv++ )
        //{
        //    delete[] dqdx1[iv]; dqdx1[iv]= NULL;
        //}
        // delete[] dqdx1; dqdx1= NULL;
        // delete[] dq; dq= NULL;
     }
  }

   //void cFreeFbndry::diflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *dqb[], Real *dauxb[], Real *resb[],
   //                             Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *dq[],  Real *daux[],  Real *res[],
   //                                         Real *wnb[], Real *wxdb[], Real *auxfb[]  )
   void cFreeFbndry::diflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *sdqb, Real *sdauxb, Real *sresb,
                                   Int *sibq, Real *sxq, Real *sq,  Real *saux,  Real *sdq,  Real *sdaux,  Real *sres,
                                              Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq  )
  {
      //fld->diflxb( ibs,ibe, NULL, qb,auxb, dqb,dauxb, resb, ibq[0], q,aux, dq,daux, res, wnb,wxdb, auxfb );
      fld->diflxb( ibs,ibe, NULL, sqb,sauxb, sdqb,sdauxb, sresb, sibq, sq,saux, sdq,sdaux, sres, swnb,swxdb, sauxfb, nbb,nq );
  }

   void cFreeFbndry::diflx( Int ibs, Int ibe, cAu3xView<Real>& xb,  cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& dqb, cAu3xView<Real>& dauxb, cAu3xView<Real>& resb,
                         cAu3xView<Int>& ibq, cAu3xView<Real>& xq,  cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& dq,  cAu3xView<Real>& daux,  cAu3xView<Real>& res,
                                              cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      //fld->diflxb( ibs,ibe, NULL, qb,auxb, dqb,dauxb, resb, ibq[0], q,aux, dq,daux, res, wnb,wxdb, auxfb );
      fld->diflxb( ibs,ibe, NULL_iview, qb,auxb, dqb,dauxb, resb, ibq, q,aux, dq,daux, res, wnb,wxdb, auxfb );
  }

   void cFreeFbndry::request( Int ibs, Int ibe, Real tm, Real *xb[], Int *n, Int *m, Int *l, Real **sx )
  {
      cout << "void cFreeFbndry::request( Int ibs, Int ibe, Real tm, Real *xb[], Int *n, Int *m, Int *l, Real **sx ) not used anymore\n";
      exit(0);

      Int ix,ib,jb,nx;
      Real w;
      nx= coo->getnx();
      Real      *x[3];

     *n=3;
     *m=ibe-ibs;
     *l=fld->getnv();
     *sx= new Real[3*(ibe-ibs)];

      subv( 3,(ibe-ibs),*sx,x );

      for( ix=0;ix<nx;ix++ )
     {
         jb=0;
         for( ib=ibs;ib<ibe;ib++ )
        {
            x[ix][jb]= xb[ix][ib];
            jb++;
        }
     }
      coo->ccoor( 0,ibe-ibs, NULL,x,x );

      if(nfre_reci>0) (*l) += 2*nfre_reci*fld->getnv();

      cout << "nfre_reci " << nfre_reci << "\n";
      cout << "request " << *n << " " << *m << " " << *l << "\n";
  }

   void cFreeFbndry::request( Int ibs, Int ibe, Real tm, Real *sxb, Int nbb, Int *n, Int *m, Int *l, Real **sx )
  {
      Int ix,ib,jb,nx, tmpn;
      Real w;
      nx= coo->getnx();
      Real *tmpsx;

     *n=3;
     *m=ibe-ibs;
     *l=fld->getnv();
     *sx= new Real[3*(ibe-ibs)];

      tmpsx = *sx;
      tmpn = ibe-ibs;

      for( ib=ibs;ib<ibe;ib++ )
     {
         jb = ib-ibs;
         tmpsx[ADDR(0,jb,tmpn)]= sxb[ADDR(0,ib,nbb)];
         tmpsx[ADDR(1,jb,tmpn)]= sxb[ADDR(1,ib,nbb)];
         tmpsx[ADDR(2,jb,tmpn)]= sxb[ADDR(2,ib,nbb)];
     }
      //coo->ccoor( 0,ibe-ibs, NULL,x,x );
      //coo->ccoor( 0,ibe-ibs, NULL,tmpsx,tmpsx,tmpn ); // no need to call ccoor, I think, ccoor is used by x-r coordinate system

  //    cout << "request " << *n << " " << *m << " " << *l << "\n";
  }

   void cFreeFbndry::service( Int nxr, Int nvr, Int nqr, Real tm, Real *xr[], Real **sqr, 
                      Int iqs, Int iqe, Real *xq[], Real  *q[], Real  *aux[], Real *dxdx[], Real **dqdx[],
                      Int ibs, Int ibe, Int *ibq[], Real *xb[], Real *qb[], Real *auxb[], bool bposix )
  {

      Real **qr;
      Real w, rmin, rmax;
      bool btmp;

     *sqr= new Real[nqr*(nvr+1)];
      qr= new Real*[nvr+1];
      subv( nvr+1,nqr,*sqr,qr );
       
      Int i,il,iv, jl, nv, ig;
      Real dmin,d,d0,d1;
      Int  ilmin, ilmax;
      Real *y[3];
      y[0]= new Real[nqr];
      y[1]= new Real[nqr];
      y[2]= new Real[nqr];

      coo->bcoor( 0,nqr, xr,y );
      nv = fld->getnv();

     /*for( i=0;i<nqr;i++ )
     {
         dmin=big;
         ilmin=-1;
         for( il=0;il<(nbl-1);il++ )
        {
            d0= ( y[0][i]-xbvg[0][il] );
            d1= ( y[1][i]-xbvg[1][il] );
            d= sqrt( d0*d0+ d1*d1 );
            if( d < dmin )
           {
               dmin= d;
               ilmin= il;
           }
        }
         qr[nvr][i]= dmin;
         for( iv=0;iv<nvr;iv++ )
        {
            qr[iv][i]= qbvg[iv][ilmin];
        }
     }*/

      for(il=0; il<nbl-1; il++)
     {
         if(il==0)
        {
            rmin = xbvg[1][il]; ilmin = il;
            rmax = xbvg[1][il]; ilmax = il;
        }
         else
        {
            if(xbvg[1][il]<rmin)
           {
               rmin = fmin(rmin, xbvg[1][il]);
               ilmin = il;
           }

            if(xbvg[1][il]>rmax)
           {
               rmax = fmax(rmax, xbvg[1][il]);
               ilmax = il;
           }
        }
     }

      for( i=0;i<nqr;i++ )
     {
         btmp = false; 
         for( il=0;il<(nbl-2);il++ )
        {
            if(y[1][i]>=xbvg[1][il] && y[1][i]<=xbvg[1][il+1])
           {
               w = y[1][i]-xbvg[1][il];
               w/= xbvg[1][il+1] - xbvg[1][il];
               for(iv=0; iv<nv; iv++)
              {
                  qr[iv][i] = qbvg[iv][il]*(1-w) + qbvg[iv][il+1]*w;
              } 
               btmp = true;
               qr[nv][i]= 0.001;
               break;
           }
        }
 
         if(!btmp)
        {
            if(y[1][i]<=rmin)
           {
               for(iv=0; iv<nv; iv++)
              {
                  qr[iv][i] = qbvg[iv][ilmin];
              } 
               btmp = true;
               qr[nv][i]= 0.001;
           }
            else if(y[1][i]>=rmax)
           {
               for(iv=0; iv<nv; iv++)
              {
                  qr[iv][i] = qbvg[iv][ilmax];
              } 
               btmp = true;
               qr[nv][i]= 0.001;
           }
        }
     }
      coo->zvel( 0,nqr, NULL, xr, qr,qr );

//service for the frequency part
      if(nfre_send>0 || nfre_send_lin>0)
     {
         Int hlp_re[20][200], hlp_im[20][200], jv, nfrer, ifre, jv0, jv1;
         Int hlplin_re[20][200], hlplin_im[20][200], ig, ih;
         complex<Real> z, dz, img(0,1);
         Real tmpq_re[10], tmpq_im[10], dt, t1, pitch, tmpy[3];
         cTabData data;
         Int asct;


         coo->get( &data );
         data.get( "assembly-sectors", &asct );
         //pitch = getpitch(coo);
         pitch= pi2/(Real)asct;

       //  cout << "the pitch in service is " << pitch << "\n";

       //  for(il=0; il<nbl-1; il++)
       // {
       //     cout << xbvg[1][il] << " " << xbvg[2][il] << " ";
       //     for(ifre=0; ifre<nfre_send; ifre++)
       //    {
       //        for(iv=0; iv<nv; iv++)
       //       {
       //           cout << zfft_re[ifre][iv][il] << " ";
       //       }
       //        for(iv=0; iv<nv; iv++)
       //       {
       //           cout << zfft_im[ifre][iv][il] << " ";
       //       }
       //        cout << "\n";
       //    }
       // }

         cout << "nfre_send " << nfre_send << "  nfre_send_lin " << nfre_send_lin << " " 
              << "nfrer " << nfrer << " nvr " << nvr << " " << this << "\n";
         //double-check number of harmonics to service
         nfrer = (nvr-nv)/(2*nv);
         if(nfrer!=(nfre_send+nfre_send_lin))
        {
            cout << "Error: the expected harmonics is " << nfre_send << " + " << nfre_send_lin 
                 << " but " << nfrer << " is received\n";
            assert(nfrer==(nfre_send+nfre_send_lin));
        }

         i = nv+1;
         for(ifre=0; ifre<nfre_send; ifre++)
        {
            for(iv=0; iv<nv; iv++)
           {
              hlp_re[ifre][iv] = i;
              i++;
           }
        }
         for(ifre=0; ifre<nfre_send_lin; ifre++)
        {
            for(iv=0; iv<nv; iv++)
           {
               hlplin_re[ifre][iv] = i;
               i++;
           }
        }
         for(ifre=0; ifre<nfre_send; ifre++)
        {
            for(iv=0; iv<nv; iv++)
           {
               hlp_im[ifre][iv] = i;
               i++;
           }
        }
         for(ifre=0; ifre<nfre_send_lin; ifre++)
        {
            for(iv=0; iv<nv; iv++)
           {
              hlplin_im[ifre][iv] = i;
              i++;
           }
        }

         for( i=0;i<nqr;i++ )
        {
            btmp = false;
            for( il=0;il<(nbl-2);il++ )
           {
               if(y[1][i]>=xbvg[1][il] && y[1][i]<=xbvg[1][il+1])
              {
                  w = y[1][i]-xbvg[1][il];
                  w/= xbvg[1][il+1] - xbvg[1][il];

                  t1 = y[2][i];
                  for(ifre=0; ifre<nfre_send; ifre++)
                 {
                     for(iv=0; iv<nv; iv++)
                    {
                        jv0 = hlp_re[ifre][iv];
                        jv1 = hlp_im[ifre][iv];
                        tmpq_re[iv] = zfft_re[ifre][iv][il]*(1-w) + zfft_re[ifre][iv][il+1]*w;
                        tmpq_im[iv] = zfft_im[ifre][iv][il]*(1-w) + zfft_im[ifre][iv][il+1]*w;
                    }
                     tmpy[0] = xr[0][i];                    
                     tmpy[1] = xr[1][i];                    
                     tmpy[2] = xr[2][i];                    
                     zvel(tmpy, tmpq_re, tmpq_re);
                     zvel(tmpy, tmpq_im, tmpq_im);

                     for(iv=0; iv<nv; iv++)
                    {
                        jv0 = hlp_re[ifre][iv];
                        jv1 = hlp_im[ifre][iv];

                        z = tmpq_re[iv] + tmpq_im[iv] * img;
                        dz = cos((ifre+1)*asct*t1) + sin((ifre+1)*asct*t1)*img;
                        z = z*dz;

                        qr[jv0][i] = z.real();
                        qr[jv1][i] = z.imag();
                    }
                 }
                  for(ifre=0; ifre<nfre_send_lin; ifre++)
                 {
                     //cout << "ifre " << ifre << " wave number " << send_lin_wavnum[ifre] << "\n";
                     for(iv=0; iv<nv; iv++)
                    {
                        tmpq_re[iv] = zfftlin_re[ifre][iv][il]*(1-w) + zfftlin_re[ifre][iv][il+1]*w;
                        tmpq_im[iv] = zfftlin_im[ifre][iv][il]*(1-w) + zfftlin_im[ifre][iv][il+1]*w;
                    }
                     tmpy[0] = xr[0][i];                    
                     tmpy[1] = xr[1][i];                    
                     tmpy[2] = xr[2][i];                    
                     zvel(tmpy, tmpq_re, tmpq_re);
                     zvel(tmpy, tmpq_im, tmpq_im);

                     for(iv=0; iv<nv; iv++)
                    {
                        jv0 = hlplin_re[ifre][iv];
                        jv1 = hlplin_im[ifre][iv];
   
                        z = tmpq_re[iv] + tmpq_im[iv] * img;
                        dz = cos(send_lin_wavnum[ifre]*t1) + 
                             sin(send_lin_wavnum[ifre]*t1)*img;
                        z = z*dz;
   
                        qr[jv0][i] = z.real();
                        qr[jv1][i] = z.imag();
                    }
                 }
                 // cout << "\n";
                  btmp = true;
                  break;
              }
           }

            if(!btmp)
           {
               if(y[1][i]<=rmin)
              {
                  t1 = y[2][i];
                  for(ifre=0; ifre<nfre_send; ifre++)
                 {
                     for(iv=0; iv<nv; iv++)
                    {
                        jv0 = hlp_re[ifre][iv];
                        jv1 = hlp_im[ifre][iv];
                        tmpq_re[iv] = zfft_re[ifre][iv][ilmin];
                        tmpq_im[iv] = zfft_im[ifre][iv][ilmin];
                    }
                     tmpy[0] = xr[0][i];                    
                     tmpy[1] = xr[1][i];                    
                     tmpy[2] = xr[2][i];                    
                     zvel(tmpy, tmpq_re, tmpq_re);
                     zvel(tmpy, tmpq_im, tmpq_im);

                     for(iv=0; iv<nv; iv++)
                    {
                        jv0 = hlp_re[ifre][iv];
                        jv1 = hlp_im[ifre][iv];

                        z = tmpq_re[iv] + tmpq_im[iv] * img;
                        dz = cos((ifre+1)*asct*t1) + sin((ifre+1)*asct*t1)*img;
                        z = z*dz;

                        qr[jv0][i] = z.real();
                        qr[jv1][i] = z.imag();
                    }
                 }
                  for(ifre=0; ifre<nfre_send_lin; ifre++)
                 {
                     //cout << "ifre " << ifre << " wave number " << send_lin_wavnum[ifre] << "\n";
                     for(iv=0; iv<nv; iv++)
                    {
                        tmpq_re[iv] = zfftlin_re[ifre][iv][ilmin];
                        tmpq_im[iv] = zfftlin_im[ifre][iv][ilmin];
                    }
                     tmpy[0] = xr[0][i];                    
                     tmpy[1] = xr[1][i];                    
                     tmpy[2] = xr[2][i];                    
                     zvel(tmpy, tmpq_re, tmpq_re);
                     zvel(tmpy, tmpq_im, tmpq_im);

                     for(iv=0; iv<nv; iv++)
                    {
                        jv0 = hlplin_re[ifre][iv];
                        jv1 = hlplin_im[ifre][iv];
   
                        z = tmpq_re[iv] + tmpq_im[iv] * img;
                        dz = cos(send_lin_wavnum[ifre]*t1) + 
                             sin(send_lin_wavnum[ifre]*t1)*img;
                        z = z*dz;
   
                        qr[jv0][i] = z.real();
                        qr[jv1][i] = z.imag();
                    }
                 }
              }
               else if(y[1][i]>=rmax)
              {
                  t1 = y[2][i];
                  for(ifre=0; ifre<nfre_send; ifre++)
                 {
                     for(iv=0; iv<nv; iv++)
                    {
                        jv0 = hlp_re[ifre][iv];
                        jv1 = hlp_im[ifre][iv];
                        tmpq_re[iv] = zfft_re[ifre][iv][ilmax];
                        tmpq_im[iv] = zfft_im[ifre][iv][ilmax];
                    }
                     tmpy[0] = xr[0][i];                    
                     tmpy[1] = xr[1][i];                    
                     tmpy[2] = xr[2][i];                    
                     zvel(tmpy, tmpq_re, tmpq_re);
                     zvel(tmpy, tmpq_im, tmpq_im);

                     for(iv=0; iv<nv; iv++)
                    {
                        jv0 = hlp_re[ifre][iv];
                        jv1 = hlp_im[ifre][iv];

                        z = tmpq_re[iv] + tmpq_im[iv] * img;
                        dz = cos((ifre+1)*asct*t1) + sin((ifre+1)*asct*t1)*img;
                        z = z*dz;

                        qr[jv0][i] = z.real();
                        qr[jv1][i] = z.imag();
                    }
                 }
                  for(ifre=0; ifre<nfre_send_lin; ifre++)
                 {
                     //cout << "ifre " << ifre << " wave number " << send_lin_wavnum[ifre] << "\n";
                     for(iv=0; iv<nv; iv++)
                    {
                        tmpq_re[iv] = zfftlin_re[ifre][iv][ilmax];
                        tmpq_im[iv] = zfftlin_im[ifre][iv][ilmax];
                    }
                     tmpy[0] = xr[0][i];                    
                     tmpy[1] = xr[1][i];                    
                     tmpy[2] = xr[2][i];                    
                     zvel(tmpy, tmpq_re, tmpq_re);
                     zvel(tmpy, tmpq_im, tmpq_im);

                     for(iv=0; iv<nv; iv++)
                    {
                        jv0 = hlplin_re[ifre][iv];
                        jv1 = hlplin_im[ifre][iv];
   
                        z = tmpq_re[iv] + tmpq_im[iv] * img;
                        dz = cos(send_lin_wavnum[ifre]*t1) + 
                             sin(send_lin_wavnum[ifre]*t1)*img;
                        z = z*dz;
   
                        qr[jv0][i] = z.real();
                        qr[jv1][i] = z.imag();
                    }
                 }
              }
           }
        }

        // for(i=0; i<nqr; i++)
        //{
        //    iv = 3;
        //   // cout << "mean ";
        //   // for(ifre=0; ifre<nfre_send; ifre++)
        //   //{
        //   //    jv = hlp_re[ifre][iv];
        //   //    cout << qr[jv][i] << " ";
        //   //    jv = hlp_im[ifre][iv];
        //   //    cout << qr[jv][i] << " ";
        //   //}
        //    cout << "lin ";
        //    for(ifre=0;ifre<nfre_send_lin[send_lin_ngrp]; ifre++)
        //   {
        //       jv = hlplin_re[ifre][iv];
        //       cout << qr[jv][i] << " ";    
        //       jv = hlplin_im[ifre][iv];
        //       cout << qr[jv][i] << " ";    
        //   } 
        //    cout << "\n";
        //}
     }
      else 
     {
         //both nfre_send and nfre_send_lin are less equal to zero
         //this is not quite right unless it is a non-harmonic computation
         Int nfrer = (nvr-nv)/(2*nv);
         if(nfre_send<=0 && nfrer>0)
        {
            if(nfrer!=(nfre_send+nfre_send_lin))
           {
               cout << "Error: the expected harmonics is " << nfre_send << " + " << nfre_send_lin 
                    << " but " << nfrer << " is received\n";
               cout << "There is a mistake in zsetup, probably the send-mean is not set up correctly\n";
               assert(nfrer==(nfre_send+nfre_send_lin));
           }
        }
     }


      delete[] y[0];
      delete[] y[1];
      delete[] y[2];

      delete[] qr;

  }

   void cFreeFbndry::service( Int nxr, Int nvr, Int nqr, Real tm, Real *sxr, Real **sqr, Int iqs, Int iqe,
                              Real *sxq, Real  *sq, Real  *saux, Real *sdxdx, Real *sdqdx, Int ibs, Int ibe,
                              Int *sibq, Real *sxb, Real *sqb, Real *sauxb, Int nq, Int nbb )
  {
      Real **qr, **xr;
      Real w, rmin, rmax;
      bool btmp;

     *sqr= new Real[nqr*(nvr+1)];
      qr= new Real*[nvr+1];
      subv( nvr+1,nqr,*sqr,qr );
       
      Int i,il,iv, jl, nv, ig;
      Real dmin,d,d0,d1;
      Int  ilmin, ilmax;
      Real *y[3];
      y[0]= new Real[nqr];
      y[1]= new Real[nqr];
      y[2]= new Real[nqr];

      xr= new Real*[nxr];
      subv( nxr,nqr,sxr,xr );
      coo->bcoor( 0,nqr, xr,y );
      nv = fld->getnv();

      for(il=0; il<nbl-1; il++)
     {
         if(il==0)
        {
            rmin = xbvg[1][il]; ilmin = il;
            rmax = xbvg[1][il]; ilmax = il;
        }
         else
        {
            if(xbvg[1][il]<rmin)
           {
               rmin = fmin(rmin, xbvg[1][il]);
               ilmin = il;
           }

            if(xbvg[1][il]>rmax)
           {
               rmax = fmax(rmax, xbvg[1][il]);
               ilmax = il;
           }
        }
     }

      for( i=0;i<nqr;i++ )
     {
         btmp = false; 
         for( il=0;il<(nbl-2);il++ )
        {
            if(y[1][i]>=xbvg[1][il] && y[1][i]<=xbvg[1][il+1])
           {
               w = y[1][i]-xbvg[1][il];
               w/= xbvg[1][il+1] - xbvg[1][il];
               for(iv=0; iv<nv; iv++)
              {
                  qr[iv][i] = qbvg[iv][il]*(1-w) + qbvg[iv][il+1]*w;
              } 
               btmp = true;
               qr[nv][i]= 0.001;
               break;
           }
        }
 
         if(!btmp)
        {
            if(y[1][i]<=rmin)
           {
               for(iv=0; iv<nv; iv++)
              {
                  qr[iv][i] = qbvg[iv][ilmin];
              } 
               btmp = true;
               qr[nv][i]= 0.001;
           }
            else if(y[1][i]>=rmax)
           {
               for(iv=0; iv<nv; iv++)
              {
                  qr[iv][i] = qbvg[iv][ilmax];
              } 
               btmp = true;
               qr[nv][i]= 0.001;
           }
        }
     }
      coo->zvel( 0,nqr, NULL, xr, qr,qr );

      delete[] y[0];
      delete[] y[1];
      delete[] y[2];

      delete[] qr;

  }

   void cFreeFbndry::service( Int nxr, Int nvr, Int nqr, Real tm, Real *sxr, Real **sqr, Int iqs, Int iqe,
                              cAu3xView<Real>& xq, cAu3xView<Real>&  q, cAu3xView<Real>& aux, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, Int ibs, Int ibe,  
                              cAu3xView<Int>& ibq, cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb )
  {
      cAu3xView<Real> qr, xr, y;
      Real w, rmin, rmax, xmin, xmax;
      bool btmp;
      Int i,il,iv, jl, nv, ig;
      Real dmin,d,d0,d1;
      Int  ilmin, ilmax, ilmin_x, ilmax_x;
      Real *sy, *lsqr;
      
      nv = fld->getnv();
      
     *sqr= new Real[nqr*(nvr+1)];
      lsqr = *sqr;
      qr.subv( nvr+1,nqr,lsqr );

      sy = new Real [nxr*nqr];
      y.subv( nxr,nqr,sy );

      xr.subv( nxr,nqr,sxr ); 

      #pragma acc enter data copyin(lsqr[0:(nvr+1)*nqr],sxr[0:nxr*nqr],sy[0:nxr*nqr])

      coo->bcoor( 0,nqr, xr,y );

      for(il=0; il<nbl-1; il++)
     {
         if(il==0)
        {
            rmin = xbvg[1][il]; ilmin = il;
            rmax = xbvg[1][il]; ilmax = il;
            xmin = xbvg[0][il]; ilmin_x = il;
            xmax = xbvg[0][il]; ilmax_x = il;
        }
         else
        {
            if(xbvg[1][il]<rmin)
           {
               rmin = fmin(rmin, xbvg[1][il]);
               ilmin = il;
           }

            if(xbvg[1][il]>rmax)
           {
               rmax = fmax(rmax, xbvg[1][il]);
               ilmax = il;
           }

            if(xbvg[0][il]<xmin)
           {
               xmin = fmin(xmin, xbvg[0][il]);
               ilmin_x = il;
           }

            if(xbvg[0][il]>xmax)
           {
               xmax = fmax(xmax, xbvg[0][il]);
               ilmax_x = il;
           }
        }
     }

      if(fabs(rmax-rmin)>fabs(xmax-xmin))
     {
         #pragma acc enter data copyin(this) 
         #pragma acc parallel loop gang vector \
          firstprivate(rmin,rmax)\
          private(btmp,w)\
          present(sxbvg[0:nbl*nxr],sqbvg[0:nbl*nv],lsqr[0:(nvr+1)*nqr],sy[0:nxr*nqr],this) \
          default(none)
          for( i=0;i<nqr;i++ )
         {
             btmp = false;
             for( il=0;il<(nbl-2);il++ )
            {
                if(sy[ADDR(1,i,nqr)]>=sxbvg[ADDR(1,il,nbl)] && sy[ADDR(1,i,nqr)]<=sxbvg[ADDR(1,il+1,nbl)])
               {
                   w = sy[ADDR(1,i,nqr)]-sxbvg[ADDR(1,il,nbl)];
                   w/= sxbvg[ADDR(1,il+1,nbl)] - sxbvg[ADDR(1,il,nbl)];
                   for(iv=0; iv<nv; iv++)
                  {
                      lsqr[ADDR(iv,i,nqr)] = sqbvg[ADDR(iv,il,nbl)]*(1-w) + sqbvg[ADDR(iv,il+1,nbl)]*w;
                  }
                   btmp = true;
                   lsqr[ADDR(nv,i,nqr)]= 0.001;
                   break;
               }
            }

             if(!btmp)
            {
                if(sy[ADDR(1,i,nqr)]<=rmin)
               {
                   for(iv=0; iv<nv; iv++)
                  {
                      lsqr[ADDR(iv,i,nqr)] = sqbvg[ADDR(iv,ilmin,nbl)];
                  }
                   btmp = true;
                   lsqr[ADDR(nv,i,nqr)]= 0.001;
               }
                else if(sy[ADDR(1,i,nqr)]>=rmax)
               {
                   for(iv=0; iv<nv; iv++)
                  {
                      lsqr[ADDR(iv,i,nqr)] = sqbvg[ADDR(iv,ilmax,nbl)];
                  }
                   btmp = true;
                   lsqr[ADDR(nv,i,nqr)]= 0.001;
               }
            }
         }
          #pragma acc exit data delete(this) 
     }
      else
     {
         #pragma acc enter data copyin(this) 
         #pragma acc parallel loop gang vector \
          firstprivate(rmin,rmax)\
          private(btmp,w)\
          present(sxbvg[0:nbl*nxr],sqbvg[0:nbl*nv],lsqr[0:(nvr+1)*nqr],sy[0:nxr*nqr],this) \
          default(none)
          for( i=0;i<nqr;i++ )
         {
             btmp = false;
             for( il=0;il<(nbl-2);il++ )
            {
                if(sy[ADDR(0,i,nqr)]>=sxbvg[ADDR(0,il,nbl)] && sy[ADDR(0,i,nqr)]<=sxbvg[ADDR(0,il+1,nbl)])
               {
                   w = sy[ADDR(0,i,nqr)]-sxbvg[ADDR(0,il,nbl)];
                   w/= sxbvg[ADDR(0,il+1,nbl)] - sxbvg[ADDR(0,il,nbl)];
                   for(iv=0; iv<nv; iv++)
                  {
                      lsqr[ADDR(iv,i,nqr)] = sqbvg[ADDR(iv,il,nbl)]*(1-w) + sqbvg[ADDR(iv,il+1,nbl)]*w;
                  }
                   btmp = true;
                   lsqr[ADDR(nv,i,nqr)]= 0.001;
                   break;
               }
            }

             if(!btmp)
            {
                if(sy[ADDR(0,i,nqr)]<=xmin)
               {
                   for(iv=0; iv<nv; iv++)
                  {
                      lsqr[ADDR(iv,i,nqr)] = sqbvg[ADDR(iv,ilmin,nbl)];
                  }
                   btmp = true;
                   lsqr[ADDR(nv,i,nqr)]= 0.001;
               }
                else if(sy[ADDR(0,i,nqr)]>=xmax)
               {
                   for(iv=0; iv<nv; iv++)
                  {
                      lsqr[ADDR(iv,i,nqr)] = sqbvg[ADDR(iv,ilmax,nbl)];
                  }
                   btmp = true;
                   lsqr[ADDR(nv,i,nqr)]= 0.001;
               }
            }
         }
          #pragma acc exit data delete(this) 
     }

      //turn velocity from x-y-z to x-r-t
      coo->zvel( 0,nqr, xr, qr,qr );

      #pragma acc exit data copyout(lsqr[0:(nvr+1)*nqr])
      #pragma acc exit data delete(sxr[0:nxr*nqr],sy[0:nxr*nqr])


      //delete[] y[0];
      //delete[] y[1];
      //delete[] y[2];

      //delete[] qr;
      delete[] sy; sy=NULL;
  }

   void cFreeFbndry::maverage( Int ibs,Int ibe, cAu3xView<Int>& ibq, cAu3xView<Int>& ibql, cAu3xView<Real>& wnb, cAu3xView<Real>& xb, cAu3xView<Real>& q,
                               cAu3xView<Real>& aux, Int mbl, cParal *par, bool bposix, string path, string bnm, bool bwrite )
  {
      Int   nv,nx,ib,iv,il,iq,nvel,ix, naux;
      Real  w;
//      Real *sqwrk;cAu3xView<Real> qwrk;
//      Real *sxwrk;cAu3xView<Real> xwrk;
      Real un, dm, tmpwnb[3], ss, cs, alpha, gamma, rg, ca, umaga;
      Real fm, fx, fr, ft, fh, ux, ur, ut,uxa, ura, uta, ta, pa, sfct, rhoa, una, fa, f2, rho;
      Real d, dnxa, dnra, dnta;
      Int nmv;

      Int nbb, nq;
      Int *sibq, *sibql;
      Real *swnb, *sxb, *sq, *saux;

      nbb  = xb.get_dim1();
      nq   = q.get_dim1();
      sibq = ibq.get_data();
      sibql= ibql.get_data();
      swnb = wnb.get_data(); 
      sxb  = xb.get_data(); 
      sq   = q.get_data(); 
      saux = aux.get_data();;
      

      nx= coo->getnx();
      nvel= coo->getnvel();
      nv= fld->getnv();
      naux=fld->getnaux();

      gamma = 1.4;
      rg = 287./fld->units(2);

      if(bposix) lock();
      if( mbl != nbl )
     {
         nbl= mbl;
         delete[] sxbvg; sxbvg= NULL; delete[] xbvg; xbvg= NULL;
         delete[] sqbvg; sqbvg= NULL; delete[] qbvg; qbvg= NULL;
         delete[] sqbav; sqbav= NULL; delete[] qbav; qbav= NULL;
         delete[] swbvg; swbvg= NULL; delete[] wbvg; wbvg= NULL;
         delete[] smixvar; smixvar=NULL;

         sxbvg  = new Real[nbl*nx]; xbvg= new Real*[nx]; subv( nx, nbl, sxbvg,  xbvg );
         sqbvg  = new Real[nbl*nv]; qbvg= new Real*[nv]; subv( nv, nbl, sqbvg,  qbvg );
         sqbav  = new Real[nbl*nv]; qbav= new Real*[nv]; subv( nv, nbl, sqbav,  qbav );
         swbvg  = new Real[nbl   ]; wbvg= new Real*[ 1]; subv(  1, nbl, swbvg,  wbvg );
         smixvar= new Real[nbl*20];                      subv( 20, nbl, smixvar,mixvar );
//         for(iv=0; iv<20; iv++)
//        {
//            delete[] mixvar[iv]; mixvar[iv]=NULL;
//            mixvar[iv] = new Real [nbl];
//        }
//         for(iv=0; iv<20; iv++)
//        {
//            for(il=0; il<nbl; il++)
//           {
//               mixvar[iv][il] = ZERO;  
//           }
//        }

         sqwrk= new Real[nv*nbb];   qwrk.subv( nv,nbb, sqwrk );
         sxwrk= new Real[nx*nbb];   xwrk.subv( nx,nbb, sxwrk );

        #pragma acc enter data copyin(sxbvg[0:nbl*nx])
        #pragma acc enter data copyin(sqbvg[0:nbl*nv])
        #pragma acc enter data copyin(sqbav[0:nbl*nv])
        #pragma acc enter data copyin(swbvg[0:nbl   ])
        #pragma acc enter data copyin(smixvar[0:nbl*20])
        #pragma acc enter data copyin(sqwrk[0:nv*nbb])
        #pragma acc enter data copyin(sxwrk[0:nx*nbb])
     }

//      setv( 0,nbl,  1, ZERO, wbvg );
//      setv( 0,nbl, nv, ZERO, qbvg );
//      setv( 0,nbl, nv, ZERO, qbav );
//      setv( 0,nbl, nx, ZERO, xbvg );
//      for(iv=0; iv<20; iv++)
//     {
//         for(il=0; il<nbl; il++)
//        {
//            mixvar[iv][il] = 0;  
//        }
//     }
     #pragma acc enter data copyin(this) 
     #pragma acc parallel loop gang vector \
      present(sxbvg[0:nbl*nx],sqbvg[0:nbl*nv],sqbav[0:nbl*nv],swbvg[0:nbl],smixvar[0:nbl*20],this) \
      default(none)
      for(il=0; il<nbl; il++)
     {
         swbvg[il] = ZERO;
         for(iv=0; iv<nv; iv++)
        {
            sqbvg[ADDR(iv,il,nbl)] = ZERO; 
            sqbav[ADDR(iv,il,nbl)] = ZERO; 
        }
         for(ix=0; ix<nx; ix++)
        {
            sxbvg[ADDR(ix,il, nbl)] = ZERO;
        }
         for(iv=0; iv<20; iv++)
        {
            smixvar[ADDR(iv,il,nbl)] = ZERO;
        }
     }
      #pragma acc exit data delete(this) 

      if( ibe > ibs )
     {

         coo->bcoor( ibs,ibe, xb, xwrk );
         coo->bvel( ibs,ibe, ibq,xb, q,qwrk );

        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(il,iq,iv,w,ss,cs,un,dm,nmw,tmpwnb)\
         present(sxbvg[0:nbl*nx],sqbvg[0:nbl*nv],sqbav[0:nbl*nv],swbvg[0:nbl],smixvar[0:nbl*20], \
                 sqwrk[0:nv*nbb],sxwrk[0:nx*nbb], \
                 sibq[0:nbb],sibql[0:nbb],sq[0:nv*nq],sxb[0:nx*nbb],swnb[0:(nx+1)*nbb],saux[0:naux*nq],this) \
         default(none)
         for( ib=ibs;ib<ibe;ib++ )
        {
            il= sibql[ib];
            if( il > -1 )
           {
               //iq= ibq[0][ib];
               iq= sibq[ib];
               //w= wnb[nx][ib];
               w= swnb[ADDR(nx,ib,nbb)];

               #pragma acc atomic
               sxbvg[ADDR_(0,il,nbl)]+= w*sxwrk[ADDR_(0,ib,nbb)];
               #pragma acc atomic
               sxbvg[ADDR_(1,il,nbl)]+= w*sxwrk[ADDR_(1,ib,nbb)];

 
//             cout << il << " "<<ib<<" "<<iq<<" "<<xwrk[0][il]<<" "<<xwrk[1][il]<<" : "; 
//             for( ix=0;ix<nx;ix++ ){ cout << xb(ix,ib) << " "; }; cout <<" : ";
//               for( iv=0;iv<nv;iv++ ){ cout << q(iv,iq) << " "; }; cout <<" : ";
//               for( iv=0;iv<nvel;iv++ ){ cout << qwrk(iv,ib) << " "; };cout <<"\n";

               for( iv=0;iv<nvel;iv++ )
              {
                  #pragma acc atomic
                  sqbvg[ADDR_(iv,il,nbl)]+= w*sqwrk[ADDR_(iv,ib,nbb)];
              }
               for( iv=nvel;iv<nv;iv++ )
              {
                  #pragma acc atomic
                  sqbvg[ADDR_(iv,il,nbl)]+= w*sq[ADDR_(iv,iq,nq)];
              }
               #pragma acc atomic
               swbvg[ADDR_(0,il,nbl)]+= w;

               ss = sxb[ADDR(1,ib,nbb)]/sxwrk[ADDR(1,ib,nbb)];
               cs = sxb[ADDR(2,ib,nbb)]/sxwrk[ADDR(1,ib,nbb)];
               tmpwnb[0] = swnb[ADDR(0,ib,nbb)];
               tmpwnb[1] = swnb[ADDR(1,ib,nbb)]*ss + swnb[ADDR(2,ib,nbb)]*cs;
               tmpwnb[2] = swnb[ADDR(1,ib,nbb)]*cs - swnb[ADDR(2,ib,nbb)]*ss;
  
               rho = saux[ADDR(0,iq,nq)];
 
               //compute fluxes
               un=0;
               for( iv=0;iv<nvel;iv++ )
              {
                  un += sqwrk[ADDR(iv,ib,nbb)] * tmpwnb[iv];    
              }
               dm = rho*un*w;

               nmv=0;

               #pragma acc atomic
               smixvar[ADDR_(nmv,il,nbl)]+= dm; 
               nmv++;

               #pragma acc atomic
               smixvar[ADDR_(nmv,il,nbl)]+= dm*sqwrk[ADDR_(0,ib,nbb)] + sq[ADDR_(4,iq,nq)]*tmpwnb[0]*w; 
               nmv++;

               #pragma acc atomic
               smixvar[ADDR_(nmv,il,nbl)]+= dm*sqwrk[ADDR_(1,ib,nbb)] + sq[ADDR_(4,iq,nq)]*tmpwnb[1]*w; 
               nmv++;

               #pragma acc atomic
               smixvar[ADDR_(nmv,il,nbl)]+= dm*sqwrk[ADDR_(2,ib,nbb)] + sq[ADDR_(4,iq,nq)]*tmpwnb[2]*w; 
               nmv++;

               #pragma acc atomic
               smixvar[ADDR_(nmv,il,nbl)]+= dm*saux[ADDR_(3,iq,nq)];
               nmv++;

               for(iv=5; iv<nv; iv++)
              {
                  #pragma acc atomic
                  smixvar[ADDR_(nmv,il,nbl)] += dm*sq[ADDR_(iv,iq,nq)];
                  nmv++;
                  //cout << q[iv][iq] << " " << vtmax << " " << vtmin << "\n";  
              }

              #pragma acc atomic
               smixvar[ADDR_(nmv,il,nbl)]+= tmpwnb[0]*w;
               nmv++;

              #pragma acc atomic
               smixvar[ADDR_(nmv,il,nbl)]+= tmpwnb[1]*w;
               nmv++;

              #pragma acc atomic
               smixvar[ADDR_(nmv,il,nbl)]+= tmpwnb[2]*w;
               nmv++;

              #pragma acc atomic
               smixvar[ADDR_(nmv,il,nbl)]+= rho*w;
               nmv++;

               ux = sqwrk[ADDR_(0,ib,nbb)];
               ur = sqwrk[ADDR_(1,ib,nbb)];
               ut = sqwrk[ADDR_(2,ib,nbb)];
               umaga = sqrt(ux*ux + ur*ur + ut*ut);
              #pragma acc atomic
               smixvar[ADDR_(nmv,il,nbl)]+= umaga*w;
               nmv++;
           }
        }
     }
     #pragma acc exit data delete(this) 
     #pragma acc update host(sxbvg[0:nbl*nx])
     #pragma acc update host(sqbvg[0:nbl*nv])
     #pragma acc update host(swbvg[0:nbl   ])
     #pragma acc update host(smixvar[0:nbl*20])

      par->gsum( nbl, wbvg[0] );
      par->gsum( nbl, xbvg[0] );
      par->gsum( nbl, xbvg[1] );

      for( iv=0;iv<nv;iv++ )
     {
         par->gsum( nbl, qbvg[iv] );
     }


      //nmv = 7 + nv-7 + 7;
      nmv = nv+5;
      //cout << "nmv " << nmv << "\n";
      for(iv=0; iv<nmv; iv++)
     {
         par->gsum( nbl, mixvar[iv] );
     }

      if(sdflux!=NULL)
     {
         //cout << "include DF in mixing plane\n";
         //deterministic flux contribution
         for(il=0; il<nbl-1; il++)
        {
            mixvar[1][il]+= dflux[1][il];
            mixvar[2][il]+= dflux[2][il]; 
            mixvar[3][il]+= dflux[3][il];
            mixvar[4][il]+= dflux[4][il];
        }
     }

      if(bnm !="" && nx == 3 && bwrite)
     {
        ofstream fle;
        string fnm;
        fnm = path + "." + bnm + ".massflux";
        fle.open(fnm.c_str(), std::ofstream::app);
        Real m;
        m=0;
        for(il=0; il<nbl-1; il++) m += mixvar[0][il];
        fle << m << "\n";
        fle.close();
     }

      //cout << "fluxes from each level:\n";
      for( il=0;il<nbl-1;il++ )
     {
         w= wbvg[0][il];
         xbvg[0][il]/= w;
         xbvg[1][il]/= w;
         for( iv=0;iv<nv;iv++ )
        {
            qbvg[iv][il]/= w;
            qbav[iv][il] = qbvg[iv][il]; //save area average
        }
         for(iv=0; iv<nmv; iv++)
        {
            //cout << fl[iv][il] << " ";
            mixvar[iv][il]/=w;
        }
        // cout << "averaged " << qbvg[5][il] << " max " << mixvar[nmv-2][il] << " minimum " << mixvar[nmv-1][il] << " " << nmv-2 << " " << nmv-1 << "====\n";
        // qbvg[5][il] = qbvg[5][il] * (1-0.5) + mixvar[nmv-2][il]*0.5;
        // qbvg[5][il] = 0.00025;
        //qbvg[5][il] *= 2.5;
        //qbvg[5][il] += 1e-4;
//         cout << qbvg[5][il] << "===\n";

         //axi-symmetric can only do area average
         if(nx==2) continue;

         if(nmv<=0) continue;

         //update qbvg by flux conservation. if conservation is failed,
         //the original values, which are area-averaged, are used.
         //cout << "layer " << il << " ";

         uxa = qbvg[0][il]; 
         ura = qbvg[1][il]; 
         uta = qbvg[2][il]; 
         ta  = qbvg[3][il]; 
         pa  = qbvg[4][il];
         rhoa = mixvar[nv+3][il];

         fm = mixvar[0][il];
         fx = mixvar[1][il];     
         fr = mixvar[2][il];     
         ft = mixvar[3][il];     
         fh = mixvar[4][il];
         dnxa = mixvar[nv][il];
         dnra = mixvar[nv+1][il];
         dnta = mixvar[nv+2][il];
         umaga = mixvar[nv+4][il];
         d = sqrt(dnxa*dnxa + dnra*dnra + dnta*dnta);
         dnxa/=d;
         dnra/=d;
         dnta/=d;

         fa = fx*dnxa + fr*dnra + ft*dnta;
         f2 = fx*fx + fr*fr + ft*ft;         

         ca = uxa*dnxa + ura*dnra + uta*dnta;
         //umag = sqrt(uxa*uxa + ura*ura + uta*uta);
         if(fabs(ca)>0.05*umaga && umaga > 1e-6)
        {
            //conserve mass flux, momentum flux and enthalpy flux
            //cout<<"*******************************************************\n";

            sfct = fmax(pa, rhoa*umaga*umaga);

            fa/=sfct; 
            f2/=(sfct*sfct);
            fh/=(sfct*sfct);

            alpha = fa*fa + (gamma*gamma-1)*f2 - 2*(gamma*gamma-1)*fh*fm;
   
            if(alpha>0)
           {
               if((fabs(fm)/rhoa) > sqrt(gamma*pa/rhoa))
              {
                  //cout << "average supersonic flow\n";
                  pa = (fa-sqrt(alpha))/(gamma+1);
              }
               else 
              {
                  //cout << "average subsonic flow\n";
                  pa = (fa+sqrt(alpha))/(gamma+1);
              }
               pa*=sfct;
   
               uxa = fx - pa*dnxa;
               ura = fr - pa*dnra;
               uta = ft - pa*dnta;
               rhoa = fm*fm/((uxa*dnxa+ura*dnra+uta*dnta));
               uxa = uxa/fm;
               ura = ura/fm;
               uta = uta/fm;
               ta = pa/(rg*rhoa);


               qbvg[0][il] = uxa;
               qbvg[1][il] = ura;
               qbvg[2][il] = uta;
               qbvg[3][il] = ta;
               qbvg[4][il] = pa;
               for(iv=5; iv<nv; iv++)
              {
                  qbvg[iv][il] = mixvar[iv][il]/fm;
              }
           }
            else
           {
           //cout << "Warning: alpha<0, mixing plane fail to conserve fluxes\n";
           }
        }
         else
        {
            //cout << "Warning: mixing plane fail to conserve fluxes\n";
        }

//         cout << il << " "<< setprecision(10) << scientific << xbvg[0][il]<<" "<<xbvg[1][il]<<" ";
//         for( iv=0;iv<nv;iv++ )
//        {
//            cout << qbvg[iv][il]<<" ";
//        }
//         cout <<"\n";
     }

      if(bnm !="" && nx == 3 && bwrite)
     {
        ofstream fle;
        string fnm;
        fnm = path + "." + bnm + ".mix";
        fle.open(fnm.c_str());
        fle << "# " << nbl-1 << "\n";
        for(il=0; il<nbl-1; il++)
       {
          fle << xbvg[0][il] << " " << xbvg[1][il] << " ";
          for( iv=0;iv<nv;iv++ )
         {
             fle << qbvg[iv][il] << " ";
         }
          fle << "\n";
       }        
        fle.close();
     }
      if(bposix) unlock();

     #pragma acc update device(sxbvg[0:nbl*nx])
     #pragma acc update device(sqbvg[0:nbl*nv])
     #pragma acc update device(swbvg[0:nbl   ])
  }

   void cFreeFbndry::maverage( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
                               Int nfre, Real **zc_re[20], Real **zc_im[20], Real **z_re[20], Real **z_im[20],
                               Int mbl, cParal *par, bool bposix, string path, string bnm, bool bwrite )
  {
      Int   nb,nv,nx,ib,iv,il,iq,nvel,ix;
      Real  w;
      Real *sqwrk,**qwrk;
      Real *sxwrk,**xwrk;
      Real un, dm, tmpwnb[3], ss, cs, alpha, gamma, rg, ca, umaga;
      Real fm, fx, fr, ft, fh, uxa, ura, uta, ta, pa, sfct, rhoa, una, fa, f2;
      Real d, dnxa, dnra, dnta;
      Int nmv, ifre;
      Real zun_re, zun_im, zrun_re, zrun_im, h_re, h_im; 
      Real *szwrk_re, *szwrk_im, *szcwrk_re, *szcwrk_im;
      Real **zwrk_re, **zwrk_im, **zcwrk_re, **zcwrk_im;

      if(!bposix)
     {
        //cout << "mutex free maverage\n";
     }

      nx= coo->getnx();
      nvel= coo->getnvel();
      nv= fld->getnv();

      gamma = 1.4;
      rg = 287./fld->units(2);

      if(bposix) lock();
      if( mbl != nbl )
     {
         nbl= mbl;
         delete[] sxbvg; sxbvg= NULL; delete[] xbvg; xbvg= NULL;
         delete[] sqbvg; sqbvg= NULL; delete[] qbvg; qbvg= NULL;
         delete[] sqbav; sqbav= NULL; delete[] qbav; qbav= NULL;
         delete[] swbvg; swbvg= NULL; delete[] wbvg; wbvg= NULL;

         sxbvg= new Real[nbl*nx]; xbvg= new Real*[nx]; subv( nx,nbl, sxbvg,xbvg );
         sqbvg= new Real[nbl*nv]; qbvg= new Real*[nv]; subv( nv,nbl, sqbvg,qbvg );
         sqbav= new Real[nbl*nv]; qbav= new Real*[nv]; subv( nv,nbl, sqbav,qbav );
         swbvg= new Real[nbl   ]; wbvg= new Real*[ 1]; subv(  1,nbl, swbvg,wbvg );
         for(iv=0; iv<20; iv++)
        {
            delete[] mixvar[iv]; mixvar[iv]=NULL;
            mixvar[iv] = new Real [nbl];
        }
         for(iv=0; iv<20; iv++)
        {
            for(il=0; il<nbl; il++)
           {
               mixvar[iv][il] = 0;  
           }
        }
     }

      setv( 0,nbl,  1, ZERO, wbvg );
      setv( 0,nbl, nv, ZERO, qbvg );
      setv( 0,nbl, nv, ZERO, qbav );
      setv( 0,nbl, nx, ZERO, xbvg );
      for(iv=0; iv<20; iv++)
     {
         for(il=0; il<nbl; il++)
        {
            mixvar[iv][il] = 0;  
        }
     }

      if( ibe > ibs )
     {

         nb= ibe;

         qwrk= new Real*[nvel];
         xwrk= new Real*[nx];

         sqwrk= new Real[nvel*nb]; subv( nvel,nb, sqwrk,qwrk );
         sxwrk= new Real[nx*nb]; subv(   nx,nb, sxwrk,xwrk );

         coo->bcoor( ibs,ibe, xb, xwrk );
         coo->bvel( ibs,ibe, ibq,xb, q,qwrk );

         zwrk_re= new Real*[nv];
         zwrk_im= new Real*[nv];
         zcwrk_re= new Real*[nv];
         zcwrk_im= new Real*[nv];
         szwrk_re= new Real[nv*nb]; subv( nv,nb, szwrk_re,zwrk_re );
         szwrk_im= new Real[nv*nb]; subv( nv,nb, szwrk_im,zwrk_im );
         szcwrk_re= new Real[nv*nb]; subv( nv,nb, szcwrk_re,zcwrk_re );
         szcwrk_im= new Real[nv*nb]; subv( nv,nb, szcwrk_im,zcwrk_im );


         for( ib=ibs;ib<ibe;ib++ )
        {
            //no mass average for a cartisian 2D case

            il= ibql[ib];
            if( il > -1 )
           {
               iq= ibq[0][ib];
               w= wnb[nx][ib];
               xbvg[0][il]+= w*xwrk[0][ib];
               xbvg[1][il]+= w*xwrk[1][ib];

 
//             cout << il << " "<<ib<<" "<<iq<<" "<<xwrk[0][il]<<" "<<xwrk[1][il]<<" : "; 
 /*            for( ix=0;ix<nx;ix++ ){ cout << xb[ix][ib] << " "; }; cout <<" : ";
               for( iv=0;iv<nv;iv++ ){ cout << q[iv][iq] << " "; }; cout <<" : ";
               for( iv=0;iv<nvel;iv++ ){ cout << qwrk[iv][ib] << " "; };cout <<"\n";*/

               for( iv=0;iv<nvel;iv++ )
              {
                  qbvg[iv][il]+= w*qwrk[iv][ib];
              }
               for( iv=nvel;iv<nv;iv++ )
              {
                  qbvg[iv][il]+= w*q[iv][iq];
              }
               wbvg[0][il]+= w;

               if(nx==3)
              {
                 ss = xb[1][ib]/xwrk[1][ib];
                 cs = xb[2][ib]/xwrk[1][ib];
                 tmpwnb[0] = wnb[0][ib];
                 tmpwnb[1] = wnb[1][ib]*ss + wnb[2][ib]*cs;
                 tmpwnb[2] = wnb[1][ib]*cs - wnb[2][ib]*ss;
  
                 //compute fluxes
                 un=0;
                 for( iv=0;iv<nvel;iv++ )
                {
                    un += qwrk[iv][ib] * tmpwnb[iv];    
                }
                 dm = aux[0][iq]*un*w;
                 nmv=0;
                 mixvar[nmv++][il]+= dm; 
                 mixvar[nmv++][il]+= dm*qwrk[0][ib] + q[4][iq]*tmpwnb[0]*w; 
                 mixvar[nmv++][il]+= dm*qwrk[1][ib] + q[4][iq]*tmpwnb[1]*w; 
                 mixvar[nmv++][il]+= dm*qwrk[2][ib] + q[4][iq]*tmpwnb[2]*w; 
                 mixvar[nmv++][il]+= dm*aux[3][iq];
                 for(iv=5; iv<nv; iv++)
                {
                    mixvar[nmv++][il] += dm*q[iv][iq];
                    //cout << q[iv][iq] << " " << vtmax << " " << vtmin << "\n";  
                }
                 mixvar[nmv++][il]+= tmpwnb[0]*w;
                 mixvar[nmv++][il]+= tmpwnb[1]*w;
                 mixvar[nmv++][il]+= tmpwnb[2]*w;
                 mixvar[nmv++][il]+= aux[0][iq]*w;
                 mixvar[nmv++][il]+= sqrt(qwrk[0][ib]*qwrk[0][ib] + 
                                          qwrk[1][ib]*qwrk[1][ib] + 
                                          qwrk[2][ib]*qwrk[2][ib]  )*w;

              }
           }
        }

         for(ifre=0; ifre<nfre; ifre++)
        {
            for(ib=ibs; ib<ibe; ib++)
           {
               iq = ibq[0][ib];
               for(iv=0; iv<nv; iv++)
              {
                  zwrk_re[iv][ib] = z_re[ifre][iv][iq];
                  zwrk_im[iv][ib] = z_im[ifre][iv][iq];
                  zcwrk_re[iv][ib] = zc_re[ifre][iv][iq];
                  zcwrk_im[iv][ib] = zc_im[ifre][iv][iq];
              }
           }
            coo->bvel( ibs,ibe, ibq,xb, z_re[ifre],zwrk_re );
            coo->bvel( ibs,ibe, ibq,xb, z_im[ifre],zwrk_im );
            coo->bvel( ibs,ibe, ibq,xb, zc_re[ifre]+1,zcwrk_re+1 );
            coo->bvel( ibs,ibe, ibq,xb, zc_im[ifre]+1,zcwrk_im+1 );
            for( ib=ibs;ib<ibe;ib++ )
           {
               il= ibql[ib];
               if( il > -1 )
              {
                  iq= ibq[0][ib];
                  w= wnb[nx][ib];

                  if(nx==3)
                 {
                    ss = xb[1][ib]/xwrk[1][ib];
                    cs = xb[2][ib]/xwrk[1][ib];
                    tmpwnb[0] = wnb[0][ib];
                    tmpwnb[1] = wnb[1][ib]*ss + wnb[2][ib]*cs;
                    tmpwnb[2] = wnb[1][ib]*cs - wnb[2][ib]*ss;
 
                    h_re = 0;
                    h_im = 0;
                    for(ifre=0; ifre<nfre; ifre++)
                   { 
                       h_re +=  z_re[ifre][3][iq]*rg*gamma/(gamma-1) + 
                               q[0][iq]*z_re[ifre][0][iq] + q[1][iq]*z_re[ifre][1][iq] + q[2][iq]*z_re[ifre][2][iq];

                       h_im +=  z_im[ifre][3][iq]*rg*gamma/(gamma-1) + 
                               q[0][iq]*z_im[ifre][0][iq] + q[1][iq]*z_im[ifre][1][iq] + q[2][iq]*z_im[ifre][2][iq];
                   }

                    Int jx;
                    Real tau[3][3], taun[5];

                    for(ix=0; ix<nx; ix++)
                   {
                       for(jx=0; jx<nx; jx++)
                      {
                          tau[ix][jx] = 0;
                      }
                   }
                       
                    for(ifre=0; ifre<nfre; ifre++)
                   {
                      for(ix=0; ix<nx; ix++)
                     {
                         for(jx=0; jx<nx; jx++)
                        {
                            tau[ix][jx]+= aux[0][iq]*2*(z_re[ifre][ix][iq]*z_re[ifre][jx][iq] + z_im[ifre][ix][iq]*z_im[ifre][jx][iq]);
                        }
                     }
                   }
                    taun[1] = tau[0][0]*wnb[0][ib];
                    taun[1]+= tau[0][1]*wnb[1][ib];
                    taun[1]+= tau[0][2]*wnb[2][ib];
                    taun[2]=  tau[1][0]*wnb[0][ib];
                    taun[2]+= tau[1][1]*wnb[1][ib];
                    taun[2]+= tau[1][2]*wnb[2][ib];
                    taun[3] = tau[2][0]*wnb[0][ib];
                    taun[3]+= tau[2][1]*wnb[1][ib];
                    taun[3]+= tau[2][2]*wnb[2][ib];
                    taun[4] = taun[1]*q[0][iq];
                    taun[4]+= taun[2]*q[1][iq];
                    taun[4]+= taun[3]*q[2][iq];

                    //compute fluxes
                    zun_re=0;
                    zun_im=0;
                    zrun_re=0;
                    zrun_im=0;
                    for( iv=0;iv<nvel;iv++ )
                   {
                       zun_re += zwrk_re[iv][ib] * tmpwnb[iv];    
                       zun_im += zwrk_im[iv][ib] * tmpwnb[iv];    
                       zrun_re += zcwrk_re[iv+1][ib] * tmpwnb[iv];    
                       zrun_im += zcwrk_im[iv+1][ib] * tmpwnb[iv];    
                   }
                    //dm= 2*(zcwrk_re[0][ib]*zun_re + zcwrk_im[0][ib]*zun_im)*w;
                    //mixvar[0][il]+= dm; 
                    //mixvar[1][il]+= dm*qwrk[0][ib] + 2*(zwrk_re[0][ib]*zrun_re + zwrk_im[0][ib]*zrun_im)*w;
                    //mixvar[2][il]+= dm*qwrk[1][ib] + 2*(zwrk_re[1][ib]*zrun_re + zwrk_im[1][ib]*zrun_im)*w;
                    //mixvar[3][il]+= dm*qwrk[2][ib] + 2*(zwrk_re[2][ib]*zrun_re + zwrk_im[2][ib]*zrun_im)*w;
                    //mixvar[4][il]+= dm*aux[3][iq] + 2*(h_re*zrun_re + h_im*zrun_im)*w;
                    //mixvar[1][il]+= dm*qwrk[0][ib] + 2*(zwrk_re[0][ib]*zrun_re + zwrk_im[0][ib]*zrun_im)*w;
                    //mixvar[2][il]+= dm*qwrk[1][ib] + 2*(zwrk_re[1][ib]*zrun_re + zwrk_im[1][ib]*zrun_im)*w;
                    //mixvar[3][il]+= dm*qwrk[2][ib] + 2*(zwrk_re[2][ib]*zrun_re + zwrk_im[2][ib]*zrun_im)*w;
                    //mixvar[4][il]+= dm*aux[3][iq] + 2*(h_re*zrun_re + h_im*zrun_im)*w;
                    mixvar[1][il]+= aux[0][iq]*2*(zwrk_re[0][ib]*zun_re + zwrk_im[0][ib]*zun_im)*w;
                    mixvar[2][il]+= aux[0][iq]*2*(zwrk_re[1][ib]*zun_re + zwrk_im[1][ib]*zun_im)*w;
                    mixvar[3][il]+= aux[0][iq]*2*(zwrk_re[2][ib]*zun_re + zwrk_im[2][ib]*zun_im)*w;
                    mixvar[4][il]+= aux[0][iq]*2*(h_re*zun_re + h_im*zun_im)*w;
                    //mixvar[4][il]+= taun[4]*w;
                 }
              }
           }
        }

         delete[] sqwrk; sqwrk=NULL; delete[] qwrk;
         delete[] sxwrk; sxwrk=NULL; delete[] xwrk;

         delete[] zwrk_re;   zwrk_re=NULL;    
         delete[] zwrk_im;   zwrk_im=NULL;
         delete[] zcwrk_re;  zcwrk_re=NULL;
         delete[] zcwrk_im;  zcwrk_im=NULL;
         delete[] szwrk_re;  szwrk_re=NULL;
         delete[] szwrk_im;  szwrk_im=NULL;
         delete[] szcwrk_re;  szcwrk_re=NULL;
         delete[] szcwrk_im;  szcwrk_im=NULL;
     }
      par->gsum( nbl, wbvg[0] );
      par->gsum( nbl, xbvg[0] );
      par->gsum( nbl, xbvg[1] );

      for( iv=0;iv<nv;iv++ )
     {
         par->gsum( nbl, qbvg[iv] );
     }


      //nmv = 7 + nv-7 + 7;
      nmv = nv+5;
      //cout << "nmv " << nmv << "\n";
      for(iv=0; iv<nmv; iv++)
     {
         par->gsum( nbl, mixvar[iv] );
     }

      if(bnm !="" && nx == 3 && bwrite)
     {
        ofstream fle;
        string fnm;
        fnm = path + "." + bnm + ".massflux";
        fle.open(fnm.c_str(), std::ofstream::app);
        Real m;
        m=0;
        for(il=0; il<nbl-1; il++) m += mixvar[0][il];
        fle << m << "\n";
        fle.close();
     }

      //cout << "fluxes from each level:\n";
      for( il=0;il<nbl-1;il++ )
     {
         w= wbvg[0][il];
         xbvg[0][il]/= w;
         xbvg[1][il]/= w;
         for( iv=0;iv<nv;iv++ )
        {
            qbvg[iv][il]/= w;
            qbav[iv][il] = qbvg[iv][il];
        }
         for(iv=0; iv<nmv; iv++)
        {
            //cout << fl[iv][il] << " ";
            mixvar[iv][il]/=w;
        }
        // cout << "averaged " << qbvg[5][il] << " max " << mixvar[nmv-2][il] << " minimum " << mixvar[nmv-1][il] << " " << nmv-2 << " " << nmv-1 << "====\n";
        // qbvg[5][il] = qbvg[5][il] * (1-0.5) + mixvar[nmv-2][il]*0.5;
        // qbvg[5][il] = 0.00025;
        //qbvg[5][il] *= 2.5;
        //qbvg[5][il] += 1e-4;
//         cout << qbvg[5][il] << "===\n";

         //axi-symmetric can only do area average
         if(nx==2) continue;

         if(nmv<=0) continue;

         //update qbvg by flux conservation. if conservation is failed,
         //the original values, which are area-averaged, are used.
         //cout << "layer " << il << " ";

         uxa = qbvg[0][il]; 
         ura = qbvg[1][il]; 
         uta = qbvg[2][il]; 
         ta  = qbvg[3][il]; 
         pa  = qbvg[4][il];
         rhoa = mixvar[nv+3][il];


         fm = mixvar[0][il];
         fx = mixvar[1][il];     
         fr = mixvar[2][il];     
         ft = mixvar[3][il];     
         fh = mixvar[4][il];
         dnxa = mixvar[nv][il];
         dnra = mixvar[nv+1][il];
         dnta = mixvar[nv+2][il];
         umaga = mixvar[nv+4][il];
         d = sqrt(dnxa*dnxa + dnra*dnra + dnta*dnta);
         dnxa/=d;
         dnra/=d;
         dnta/=d;

         fa = fx*dnxa + fr*dnra + ft*dnta;
         f2 = fx*fx + fr*fr + ft*ft;         

         ca = uxa*dnxa + ura*dnra + uta*dnta;
         //umag = sqrt(uxa*uxa + ura*ura + uta*uta);
         if(fabs(ca)>0.05*umaga && umaga > 1e-6)
        {
            //conserve mass flux, momentum flux and enthalpy flux
            //cout<<"*******************************************************\n";

            sfct = fmax(pa, rhoa*umaga*umaga);

            fa/=sfct; 
            f2/=(sfct*sfct);
            fh/=(sfct*sfct);

            alpha = fa*fa + (gamma*gamma-1)*f2 - 2*(gamma*gamma-1)*fh*fm;
   
            if(alpha>0)
           {
               if((fabs(fm)/rhoa) > sqrt(gamma*pa/rhoa))
              {
                  //cout << "average supersonic flow\n";
                  pa = (fa-sqrt(alpha))/(gamma+1);
              }
               else 
              {
                  //cout << "average subsonic flow\n";
                  pa = (fa+sqrt(alpha))/(gamma+1);
              }
               pa*=sfct;
   
               uxa = fx - pa*dnxa;
               ura = fr - pa*dnra;
               uta = ft - pa*dnta;
               rhoa = fm*fm/((uxa*dnxa+ura*dnra+uta*dnta));
               uxa = uxa/fm;
               ura = ura/fm;
               uta = uta/fm;
               ta = pa/(rg*rhoa);


               qbvg[0][il] = uxa;
               qbvg[1][il] = ura;
               qbvg[2][il] = uta;
               qbvg[3][il] = ta;
               qbvg[4][il] = pa;
               for(iv=5; iv<nv; iv++)
              {
                  qbvg[iv][il] = mixvar[iv][il]/fm;
              }
           }
            else
           {
           //cout << "Warning: alpha<0, mixing plane fail to conserve fluxes\n";
           }
        }
         else
        {
            //cout << "Warning: mixing plane fail to conserve fluxes\n";
        }

/*       cout << il << " "<<xbvg[0][il]<<" "<<xbvg[1][il]<<" ";
         for( iv=0;iv<nv;iv++ )
        {
            cout << qbvg[iv][il]<<" ";
        }
         cout <<"\n";*/
     }

      if(bnm !="" && nx == 3 && bwrite)
     {
        ofstream fle;
        string fnm;
        fnm = path + "." + bnm + ".mix";
        fle.open(fnm.c_str());
        fle << "# " << nbl-1 << "\n";
        for(il=0; il<nbl-1; il++)
       {
          fle << xbvg[0][il] << " " << xbvg[1][il] << " ";
          for( iv=0;iv<nv;iv++ )
         {
             fle << qbvg[iv][il] << " ";
         }
          fle << "\n";
       }        
        fle.close();
     }

      if(bposix) unlock();
  }

   void cFreeFbndry::bdflux( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
                             Int nfre, Real **z_re[20], Real **z_im[20], Int mbl, cParal *par )
  {
      Int nv, nvel, nx;
      Int ifre, ib, iq, iv, il;
      Real w;
      Real zun_re, zun_im, h_re, h_im;
      Real zwrk_re[3], zwrk_im[3], tmpwnb[3], ss, cs, r;
      Real gamma, rg;

//determinisic fluxes need to be computed in the cynlindrical system
      nvel= coo->getnvel();
      nx= coo->getnx();
      nv= fld->getnv();

      gamma = 1.4;
      rg = 287./fld->units(2);

      if( sdflux == NULL )
     {
         //cout << "allocate memory for sdflux\n";
         sdflux= new Real[mbl*nv]; dflux= new Real*[nv]; subv( nv,mbl, sdflux,dflux );
     }
      setv( 0,mbl, nv, ZERO, dflux );

      if( ibe > ibs )
     {
         for( ib=ibs;ib<ibe;ib++ )
        {
            il= ibql[ib];
            if( il > -1 )
           {
               iq= ibq[0][ib];
               w= wnb[nx][ib];

               r = xb[1][ib]*xb[1][ib] + xb[2][ib]*xb[2][ib];
               r = sqrt(r);
               ss = xb[1][ib]/r;
               cs = xb[2][ib]/r;
               tmpwnb[0] = wnb[0][ib];
               tmpwnb[1] = wnb[1][ib]*ss + wnb[2][ib]*cs;
               tmpwnb[2] = wnb[1][ib]*cs - wnb[2][ib]*ss;

               for(ifre=0; ifre<nfre; ifre++)
              {
                  coo->bvel( ib,ibq,xb,z_re[ifre],zwrk_re );
                  coo->bvel( ib,ibq,xb,z_im[ifre],zwrk_im );
         
                  h_re =  z_re[ifre][3][iq]*rg*gamma/(gamma-1) + 
                          q[0][iq]*z_re[ifre][0][iq] + q[1][iq]*z_re[ifre][1][iq] + q[2][iq]*z_re[ifre][2][iq];

                  h_im =  z_im[ifre][3][iq]*rg*gamma/(gamma-1) + 
                          q[0][iq]*z_im[ifre][0][iq] + q[1][iq]*z_im[ifre][1][iq] + q[2][iq]*z_im[ifre][2][iq];

                  zun_re=0;
                  zun_im=0;
                  for( iv=0;iv<nvel;iv++ )
                 {
                     zun_re += zwrk_re[iv] * tmpwnb[iv];    
                     zun_im += zwrk_im[iv] * tmpwnb[iv];    
                 }
                  dflux[0][il] = 0;
                  dflux[1][il]+= aux[0][iq]*2*(zwrk_re[0]*zun_re + zwrk_im[0]*zun_im)*w;
                  dflux[2][il]+= aux[0][iq]*2*(zwrk_re[1]*zun_re + zwrk_im[1]*zun_im)*w;
                  dflux[3][il]+= aux[0][iq]*2*(zwrk_re[2]*zun_re + zwrk_im[2]*zun_im)*w;
                  dflux[4][il]+= aux[0][iq]*2*(h_re*zun_re + h_im*zun_im)*w;
              }
           }
        }
     }

      for(iv=0; iv<5; iv++)
     {
         par->gsum( mbl, dflux[iv] );
     }
  }

   void cFreeFbndry::accept( Int ibs, Int ibe, Int nvr, Int nq, Real tm, Real *q[], Real *w, Real *qb[] )
  {
      Int i,j, nv;
      Int n=0;
      nv = fld->getnv();
      for( i=ibs;i<ibe;i++ )
     {
         if( w[i] > q[nv][i] )
        {
            n++;
            w[i]= q[nv][i];
            for( j=0;j<nv;j++ )
           {
               qb[j][i]= q[j][i];
           }
        }
     }
      //cout << "FreeBndry "<<this<<" accepts "<<n<<" values\n";
  }

   void cFreeFbndry::accept( Int ibs, Int ibe, Int nv, Int nq, Real tm, Real *sq, Real *w, cAu3xView<Real>& qb )
  {
      Int i,j;
      Int n=0;

      Int nbb;
      Real *sqb;

      nbb = qb.get_dim1();
      sqb = qb.get_data();

     #pragma acc enter data copyin(sq[0:(nv+1)*nq],w[0:nq],this)
     #pragma acc parallel loop \
      present(sq[0:(nv+1)*nq],w[0:nq],sqb[0:nv*nbb],this) \
      default(none)
      for( i=ibs;i<ibe;i++ )
     {
         //if( w[i] > q[nv][i] )
         if( w[i] > sq[ADDR(nv,i,nq)] )
        {

            //w[i]= q[nv][i];
            w[i]= sq[ADDR(nv,i,nq)];
            for( j=0;j<nv;j++ )
           {
               //qb[j][i]= q[j][i];
               sqb[ADDR(j,i,nbb)]= sq[ADDR(j,i,nq)];
           }
        }
     }
     #pragma acc exit data delete(sq,this)
     #pragma acc exit data copyout(w[0:nq])
     // coo->toffset( ibs,ibe, 1,-tm, qb );
      //coo->toffset( ibs,ibe, 1,tm, qb );
      //cout << "sliding plane accepts "<<n<<" values\n";
  }


   void cFreeFbndry::accept_z( Int ibs, Int ibe, Int nvr, Int nq, Real *q[], Real *w, Real *qb[], Int ifre0, Real *zb_re[], Real *zb_im[] )
  {
      Int i, nv;
      Int n=0;

      Int hlp_re[20][200], hlp_im[20][200], iv, jv, ifre;

      nv = fld->getnv();

//      cout << "accept " << 2*nfre_reci*nv + nv << " " << nvr << " ==================== \n";
      assert((2*nfre_reci*nv + nv) == nvr);


      i = nv+1;
      for(ifre=0; ifre<nfre_reci; ifre++)
     {
         for(iv=0; iv<nv; iv++)
        {
           hlp_re[ifre][iv] = i;
           i++;
        }
     }
      for(ifre=0; ifre<nfre_reci; ifre++)
     {
         for(iv=0; iv<nv; iv++)
        {
           hlp_im[ifre][iv] = i;
           i++;
        }
     }

      for( i=ibs;i<ibe;i++ )
     {
//         cout << w[i] << " " << q[nv][i] << "\n";
//            cout << "here accept_z=============================\n";
         //the contents in q should be the same for all the mpi rank, so there is no need to check the distance
        /* n++;
         w[i]= q[nv][i];
         for( iv=0;iv<nv;iv++ )
        {
            jv = hlp_re[ifre0][iv];
            zb_re[iv][i]= q[jv][i];

            jv = hlp_im[ifre0][iv];
            zb_im[iv][i]= q[jv][i];
//              cout << zb_re[iv][i] << " " << zb_im[iv][i] << " ";
        }*/
         if(fabs(w[i]-q[nv][i])<1e-12)
        {
           //cout << this << " " << "accept :";
           for( iv=0;iv<nv;iv++ )
          {
              jv = hlp_re[ifre0][iv];
              zb_re[iv][i]= q[jv][i];
  
              jv = hlp_im[ifre0][iv];
              zb_im[iv][i]= q[jv][i];
          //    cout << zb_re[iv][i] << " " << zb_im[iv][i] << " ";
          }
          // cout << w[i] << " " << q[nv][i] << "\n";
        }
         else 
        {
           cout << w[i] << " " << q[nv][i] << " I am here???????????/\n";
        }
//         cout << "\n";
     }
      //cout << "FreeBndry "<<this<<" accepts "<<n<<" values\n";
  }

   cFreeFbndryX::cFreeFbndryX()
  {
      nbl= -1;
      sxbvg=(Real* )NULL;
      sqbvg=(Real* )NULL;
      swbvg=(Real* )NULL;
      xbvg =(Real**)NULL;
      qbvg =(Real**)NULL;
      wbvg =(Real**)NULL;
      for(Int i=0; i<20; i++) mixvar[i] = NULL;
  }

   cFreeFbndryX::~cFreeFbndryX()
  {
      nbl= -1;
      delete[] sxbvg; sxbvg= NULL; delete[] xbvg; xbvg= NULL;
      delete[] sqbvg; sqbvg= NULL; delete[] qbvg; qbvg= NULL;
      delete[] swbvg; swbvg= NULL; delete[] wbvg; wbvg= NULL;
      for(Int i=0; i<20; i++) { delete[] mixvar[i]; mixvar[i] = NULL; };
  }

   void cFreeFbndryX::service( Int nxr, Int nvr, Int nqr, Real tm, Real *xr[], Real **sqr, 
                               Int iqs, Int iqe, Real *xq[], Real  *q[], Real  *aux[], Real *dxdx[], Real **dqdx[],
                               Int ibs, Int ibe, Int *ibq[], Real *xb[], Real *qb[], Real *auxb[], bool bposix )
  {

      Real **qr;
      Real w, xmin, xmax;
      bool btmp;

     *sqr= new Real[nqr*(nvr+1)];
      qr= new Real*[nvr+1];
      subv( nvr+1,nqr,*sqr,qr );
       
      Int i,il,iv, jl;
      Real dmin,d,d0,d1;
      Int  ilmin, ilmax;
      Real *y[3];
      y[0]= new Real[nqr];
      y[1]= new Real[nqr];
      y[2]= new Real[nqr];

      coo->bcoor( 0,nqr, xr,y );

     /*for( i=0;i<nqr;i++ )
     {
         dmin=big;
         ilmin=-1;
         for( il=0;il<(nbl-1);il++ )
        {
            d0= ( y[0][i]-xbvg[0][il] );
            d1= ( y[1][i]-xbvg[1][il] );
            d= sqrt( d0*d0+ d1*d1 );
            if( d < dmin )
           {
               dmin= d;
               ilmin= il;
           }
        }
         qr[nvr][i]= dmin;
         for( iv=0;iv<nvr;iv++ )
        {
            qr[iv][i]= qbvg[iv][ilmin];
        }
     }*/

      for(il=0; il<nbl-1; il++)
     {
         if(il==0)
        {
            xmin = xbvg[0][il]; ilmin = il;
            xmax = xbvg[0][il]; ilmax = il;
        }
         else
        {
            if(xbvg[0][il]<xmin)
           {
               xmin = fmin(xmin, xbvg[0][il]);
               ilmin = il;
           }

            if(xbvg[0][il]>xmax)
           {
               xmax = fmax(xmax, xbvg[0][il]);
               ilmax = il;
           }
        }
     }

      for( i=0;i<nqr;i++ )
     {
         btmp = false; 
         for( il=0;il<(nbl-2);il++ )
        {
            if(y[0][i]>=xbvg[0][il] && y[0][i]<=xbvg[0][il+1])
           {
               w = y[0][i]-xbvg[0][il];
               w/= xbvg[0][il+1] - xbvg[0][il];
               for(iv=0; iv<nvr; iv++)
              {
                  qr[iv][i] = qbvg[iv][il]*(1-w) + qbvg[iv][il+1]*w;
              } 
               btmp = true;
               qr[nvr][i]= 0.001;
               break;
           }
        }
 
         if(!btmp)
        {
            if(y[0][i]<=xmin)
           {
               for(iv=0; iv<nvr; iv++)
              {
                  qr[iv][i] = qbvg[iv][ilmin];
              } 
               btmp = true;
               qr[nvr][i]= 0.001;
           }
            else if(y[0][i]>=xmax)
           {
               for(iv=0; iv<nvr; iv++)
              {
                  qr[iv][i] = qbvg[iv][ilmax];
              } 
               btmp = true;
               qr[nvr][i]= 0.001;
           }
        }
     }
      coo->zvel( 0,nqr, NULL, xr, qr,qr );
      delete[] y[0];
      delete[] y[1];
      delete[] y[2];

      delete[] qr;

  }

   void zvel( Real *x,  Real *bq, Real *q )
  {
      Real x0, x1, x2, r, cth, sth;

      x0= x[0];
      x1= x[1];
      x2= x[2];
      r= x1*x1+ x2*x2;
      r= sqrt(r)+small;
      cth= x2/r;
      sth= x1/r;
      q[0]= bq[0];
      Real tmp= bq[1];
      q[1]= sth*tmp +cth*bq[2];
      q[2]= cth*tmp -sth*bq[2];
  }
