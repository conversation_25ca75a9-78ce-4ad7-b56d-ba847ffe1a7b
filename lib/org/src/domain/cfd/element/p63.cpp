   using namespace std;

#  include <domain/cfd/element/element.h>

   cfp63::cfp63()
  {
      nsp=6;
      nsq=1;
      ng= 6;
      yg[0]= new Real[ng];
      yg[1]= new Real[ng];
      yg[2]= new Real[ng];
      wg   = new Real[ng];
      yg[0][0]= 0.500000000000e0; yg[1][0]= 0.000000000000e0; yg[2][0]= 0.211324865405e0; wg[0]= 0.083333333333e0;
      yg[0][1]= 0.000000000000e0; yg[1][1]= 0.500000000000e0; yg[2][1]= 0.211324865405e0; wg[1]= 0.083333333333e0;
      yg[0][2]= 0.500000000000e0; yg[1][2]= 0.500000000000e0; yg[2][2]= 0.211324865405e0; wg[2]= 0.083333333333e0;
      yg[0][3]= 0.500000000000e0; yg[1][3]= 0.000000000000e0; yg[2][3]= 0.788675134595e0; wg[3]= 0.083333333333e0;
      yg[0][4]= 0.000000000000e0; yg[1][4]= 0.500000000000e0; yg[2][4]= 0.788675134595e0; wg[4]= 0.083333333333e0;
      yg[0][5]= 0.500000000000e0; yg[1][5]= 0.500000000000e0; yg[2][5]= 0.788675134595e0; wg[5]= 0.083333333333e0;

/*    nce[0]= 9;
      icep[0]= new Int*[2];
      icep[0][0]= new Int[9];
      icep[0][1]= new Int[9];
      icep[0][0][ 0]= 0; icep[0][1][ 0]= 1;
      icep[0][0][ 1]= 1; icep[0][1][ 1]= 2;
      icep[0][0][ 2]= 2; icep[0][1][ 2]= 0;
      icep[0][0][ 3]= 0; icep[0][1][ 3]= 3;
      icep[0][0][ 4]= 1; icep[0][1][ 4]= 4;
      icep[0][0][ 5]= 2; icep[0][1][ 5]= 5;
      icep[0][0][ 6]= 3; icep[0][1][ 6]= 4;
      icep[0][0][ 7]= 4; icep[0][1][ 7]= 5;
      icep[0][0][ 8]= 5; icep[0][1][ 8]= 6;
      iceq[0]= new Int*[1];
      iceq[0][0]= new Int[9];
      iceq[0][0][0]= 0;
      iceq[0][0][1]= 0;
      iceq[0][0][2]= 0;
      iceq[0][0][3]= 0;
      iceq[0][0][4]= 0;
      iceq[0][0][5]= 0;
      iceq[0][0][6]= 0;
      iceq[0][0][7]= 0;
      iceq[0][0][8]= 0;*/

      nce[1]= 2;
      icep[1]= new Int*[3];
      icep[1][0]= new Int[2];
      icep[1][1]= new Int[2];
      icep[1][2]= new Int[2];
      icep[1][0][ 0]= 2; icep[1][1][ 0]= 1; icep[1][2][ 0]= 0;
      icep[1][0][ 1]= 3; icep[1][1][ 1]= 4; icep[1][2][ 1]= 5;
      iceq[1]= new Int*[1];
      iceq[1][0]= new Int[2];
      iceq[1][0][0]= 0;
      iceq[1][0][1]= 0;

      nce[2]= 3;
      icep[2]= new Int*[4];
      icep[2][0]= new Int[3];
      icep[2][1]= new Int[3];
      icep[2][2]= new Int[3];
      icep[2][3]= new Int[3];
      icep[2][0][ 0]= 0; icep[2][1][ 0]= 1; icep[2][2][ 0]= 4; icep[2][3][ 0]= 3;
      icep[2][0][ 1]= 1; icep[2][1][ 1]= 2; icep[2][2][ 1]= 5; icep[2][3][ 1]= 4;
      icep[2][0][ 2]= 2; icep[2][1][ 2]= 0; icep[2][2][ 2]= 3; icep[2][3][ 2]= 5;
      iceq[2]= new Int*[1];
      iceq[2][0]= new Int[3];
      iceq[2][0][0]= 0;
      iceq[2][0][1]= 0;
      iceq[2][0][2]= 0;
  }


   cfp63::~cfp63()
  {
  }

   void cfp63::shpf( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {

      g(0)=         l[0]* (1-l[2]);
      g(1)=         l[1]* (1-l[2]);
      g(2)= (1-l[0]-l[1])*(1-l[2]);
      g(3)=         l[0]* l[2];
      g(4)=         l[1]* l[2];
      g(5)= (1-l[0]-l[1])*l[2];

      dgdl(0,0)=    (1-l[2]);
      dgdl(1,0)=         0.;
      dgdl(2,0)=      -l[0];

      dgdl(0,1)=         0.;
      dgdl(1,1)=    (1-l[2]);
      dgdl(2,1)=      -l[1];

      dgdl(0,2)=   -(1-l[2]);
      dgdl(1,2)=   -(1-l[2]);
      dgdl(2,2)=   -(1-l[0]-l[1]);

      dgdl(0,3)=       l[2];
      dgdl(1,3)=         0.;
      dgdl(2,3)=       l[0];

      dgdl(0,4)=         0.;
      dgdl(1,4)=       l[2];
      dgdl(2,4)=       l[1];

      dgdl(0,5)=      -l[2];
      dgdl(1,5)=      -l[2];
      dgdl(2,5)=    (1-l[0]-l[1]);

  }

   void cfp63::shpx( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {
      shpf( l, g, dgdl );
  }


