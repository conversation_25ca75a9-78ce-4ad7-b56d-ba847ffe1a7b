   using namespace std;


//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

#  include <domain/cfd/element/element.h>

   cft33::cft33()
  {
      nsp=3;
      nsq=1;

      ng=1;
      yg[0]= new Real[ng];
      yg[1]= new Real[ng];
      yg[2]= new Real[ng];
      wg=    new Real[ng];

      yg[0][0]= 0.333333333333e0;  yg[1][0]= 0.333333333333e0; yg[2][0]= 0.333333333333e0; wg[0]= 0.5e0;

/*    nce[0]= 3;
      icep[0]= new Int*[2];
      icep[0][0]= new Int[3];
      icep[0][1]= new Int[3];
      icep[0][0][ 0]= 0; icep[0][1][ 0]= 1; 
      icep[0][0][ 1]= 1; icep[0][1][ 1]= 2; 
      icep[0][0][ 2]= 2; icep[0][1][ 2]= 0; 
      iceq[0]= new Int*[1];
      iceq[0][0]= new Int[3];
      iceq[0][0][ 0]= 0;
      iceq[0][0][ 1]= 0;
      iceq[0][0][ 2]= 0;

      nce[1]= 1;
      icep[1]= new Int*[3];
      icep[1][0]= new Int[1];
      icep[1][1]= new Int[1];
      icep[1][2]= new Int[1];
      icep[1][0][ 0]= 0; icep[1][1][ 0]= 1; icep[1][2][ 0]= 2; 
      iceq[1]= new Int*[1];
      iceq[1][0]= new Int[1];
      iceq[1][0][ 0]= 0;*/
  }


   cft33::~cft33()
  {
  }

   void cft33::shpf( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {
       shpx( l,g,dgdl );
  }

   void cft33::shpx( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {

      g(0)= l[0];
      g(1)= l[1];
      g(2)= l[0]+l[1];
      g(2)= 1.-g(2);

      dgdl(0,0)= 1.;
      dgdl(1,0)= 0.;
            
      dgdl(0,1)= 0.;
      dgdl(1,1)= 1.;
           
      dgdl(0,2)=-1.;
      dgdl(1,2)=-1.;
  }
