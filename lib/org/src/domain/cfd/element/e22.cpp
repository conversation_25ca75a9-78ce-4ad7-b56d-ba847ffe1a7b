

   using namespace std;


//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

#  include <domain/cfd/element/element.h>

   cfe22::cfe22()
  {
      nsp=2;
      nsq=1;
      ng=1;
      yg[0]= new Real[1]; 
      yg[0][0]=0.5;
      wg=    new Real[1]; 
      wg[0]= 1.;

  }


   cfe22::~cfe22()
  {
  }

   void cfe22::shpf( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl ){}

//   void cfe22::shpx( Real *l, Real *g, Real *dgdl[] )
//  {
//      Real s;
//      s= l[0];
//      g[0]= 1-s;
//      g[1]=   s;
//      dgdl[0][0]= -1;
//      dgdl[0][1]=  1;
//  };

   void cfe22::shpx( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {
      Real s;
      s= l[0];
      g(0)= 1-s;
      g(1)=   s;
      dgdl(0,0)= -1;
      dgdl(0,1)=  1;
  };

