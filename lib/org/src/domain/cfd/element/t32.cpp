
   using namespace std;


//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

#  include <domain/cfd/element/element.h>

   cft32::cft32()
  {
      nsp=3;
      nsq=1;

      ng=3;
      yg[0]= new Real[3]; 
      yg[1]= new Real[3]; 
      wg=    new Real[3]; 

      yg[0][0]= 0.500000000000e0; yg[1][0]=0.000000000000e0; wg[0]=0.166666666667e0;
      yg[0][1]= 0.000000000000e0; yg[1][1]=0.500000000000e0; wg[1]=0.166666666667e0;
      yg[0][2]= 0.500000000000e0; yg[1][2]=0.500000000000e0; wg[2]=0.166666666667e0;

      nce[0]= 3;
      icep[0]= new Int*[2];
      icep[0][0]= new Int[3];
      icep[0][1]= new Int[3];
      icep[0][0][ 0]= 0; icep[0][1][ 0]= 1; 
      icep[0][0][ 1]= 1; icep[0][1][ 1]= 2; 
      icep[0][0][ 2]= 2; icep[0][1][ 2]= 0; 
      iceq[0]= new Int*[1];
      iceq[0][0]= new Int[3];
      iceq[0][0][ 0]= 0;
      iceq[0][0][ 1]= 0;
      iceq[0][0][ 2]= 0;

  }


   cft32::~cft32()
  {
  }

   void cft32::shpf( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl ){};

   void cft32::shpx( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {

      g(0)= l[0];
      g(1)= l[1];
      g(2)= l[0]+l[1];
      g(2)= 1.-g(2);

      dgdl(0,0)= 1.;
      dgdl(0,1)= 0.;
      dgdl(0,2)=-1.;

      dgdl(1,0)= 0.;
      dgdl(1,1)= 1.;
      dgdl(1,2)=-1.;
  }
