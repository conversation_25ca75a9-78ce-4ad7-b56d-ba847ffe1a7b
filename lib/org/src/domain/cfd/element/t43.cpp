   using namespace std;


#  include <domain/cfd/element/element.h>

   cft43::cft43()
  {
      nsp=4;
      nsq=1;

/*    nce[0]= 6;
      icep[0]= new Int*[2];
      icep[0][0]= new Int[6];
      icep[0][1]= new Int[6];
      icep[0][0][ 0]= 0; icep[0][1][ 0]= 1; 
      icep[0][0][ 1]= 1; icep[0][1][ 1]= 2; 
      icep[0][0][ 2]= 2; icep[0][1][ 2]= 0; 
      icep[0][0][ 3]= 0; icep[0][1][ 3]= 3; 
      icep[0][0][ 4]= 1; icep[0][1][ 4]= 3; 
      icep[0][0][ 5]= 2; icep[0][1][ 5]= 3; 
      iceq[0]= new Int*[1];
      iceq[0][0]= new Int[6];
      iceq[0][0][ 0]= 0;
      iceq[0][0][ 1]= 0;
      iceq[0][0][ 2]= 0;
      iceq[0][0][ 3]= 0;
      iceq[0][0][ 4]= 0;
      iceq[0][0][ 5]= 0;*/

      ng=1;
      yg[0]= new Real[ng];
      yg[1]= new Real[ng];
      yg[2]= new Real[ng];
      wg=    new Real[ng];
      yg[0][0]= 0.25; yg[1][0]= 0.25; yg[2][0]= 0.25; wg[0]= 0.166666666666666666667;

      nce[1]= 4;
      icep[1]= new Int*[3];
      icep[1][0]= new Int[4];
      icep[1][1]= new Int[4];
      icep[1][2]= new Int[4];
      icep[1][0][ 0]= 2; icep[1][1][ 0]= 1; icep[1][2][ 0]= 0;
      icep[1][0][ 1]= 3; icep[1][1][ 1]= 0; icep[1][2][ 1]= 1;
      icep[1][0][ 2]= 3; icep[1][1][ 2]= 1; icep[1][2][ 2]= 2;
      icep[1][0][ 3]= 3; icep[1][1][ 3]= 2; icep[1][2][ 3]= 0;
      iceq[1]= new Int*[1];
      iceq[1][0]= new Int[4];
      iceq[1][0][ 0]= 0;
      iceq[1][0][ 1]= 0;
      iceq[1][0][ 2]= 0;
      iceq[1][0][ 3]= 0;
  }


   cft43::~cft43()
  {
  }

   void cft43::shpf( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {
      
      g(0)= l[0];
      g(1)= l[1];
      g(2)= l[0]+l[1]+l[2];
      g(3)= l[2];
      g(2)= 1.-g(2);

      dgdl(0,0)= 1.;
      dgdl(1,0)= 0.;
      dgdl(2,0)= 0.;
            
      dgdl(0,1)= 0.;
      dgdl(1,1)= 1.;
      dgdl(2,1)= 0.;
           
      dgdl(0,2)=-1.;
      dgdl(1,2)=-1.;
      dgdl(2,2)=-1.;

      dgdl(0,3)= 0.;
      dgdl(1,3)= 0.;
      dgdl(2,3)= 1.;
  }

   void cft43::shpx( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {
      shpf( l,g,dgdl );
  }
