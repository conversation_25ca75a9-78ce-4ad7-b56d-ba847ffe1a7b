   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

#  include <domain/cfd/element/element.h>


   cfq43::cfq43()
  {
      nsp=4;
      nsq=1;

      ng=4;
      yg[0]= new Real[ng];
      yg[1]= new Real[ng];
      yg[2]= new Real[ng];
      wg=    new Real[ng];

      yg[0][0]= 0.211324865405e0;  yg[1][0]= 0.211324865405e0; wg[0]= 0.25e0;
      yg[0][1]= 0.788675134595e0;  yg[1][1]= 0.211324865405e0; wg[1]= 0.25e0;
      yg[0][2]= 0.788675134595e0;  yg[1][2]= 0.788675134595e0; wg[2]= 0.25e0;
      yg[0][3]= 0.211324865405e0;  yg[1][3]= 0.788675134595e0; wg[3]= 0.25e0;

/*    nce[0]= 4;
      icep[0]= new Int*[2];
      icep[0][0]= new Int[4];
      icep[0][1]= new Int[4];
      icep[0][0][ 0]= 0; icep[0][1][ 0]= 1; 
      icep[0][0][ 1]= 1; icep[0][1][ 1]= 2; 
      icep[0][0][ 2]= 2; icep[0][1][ 2]= 3; 
      icep[0][0][ 3]= 3; icep[0][1][ 3]= 0; 
      iceq[0]= new Int*[1];
      iceq[0][0]= new Int[4];
      iceq[0][0][ 0]= 0;
      iceq[0][0][ 1]= 0;
      iceq[0][0][ 2]= 0;
      iceq[0][0][ 3]= 0;

      nce[2]= 1;
      icep[2]= new Int*[4];
      icep[2][0]= new Int[1];
      icep[2][1]= new Int[1];
      icep[2][2]= new Int[1];
      icep[2][3]= new Int[1];
      icep[2][0][ 0]= 0; icep[2][1][ 0]= 1; icep[2][2][ 0]= 2; icep[2][3][ 0]= 3; 
      iceq[2]= new Int*[1];
      iceq[2][0]= new Int[1];
      iceq[2][0][ 0]= 0;*/

  }


   cfq43::~cfq43()
  {
  }

   void cfq43::shpf( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {

      Int                  ig,il;

      g(0)= (1.-l[0])*(1.-l[1]);
      g(1)=     l[0]* (1.-l[1]);
      g(2)=     l[0]*     l[1];
      g(3)= (1.-l[0])*    l[1];

      dgdl(0,0)= -(1.-l[1]);
      dgdl(1,0)= -(1.-l[0]);

      dgdl(0,1)=  (1.-l[1]);
      dgdl(1,1)=     -l[0];

      dgdl(0,2)=      l[1];
      dgdl(1,2)=      l[0];

      dgdl(0,3)=     -l[1];
      dgdl(1,3)=  (1.-l[0]);

  }

   void cfq43::shpx( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {
      shpf( l,g,dgdl );
  }


