using namespace std;

# include <fvc.h>

  void cFvcDev::wght2( Int iss, Int ise, Int isk, Int ny, Real yg[][3], Real *wg, Int nsk, 
                       Int *isp[], Real *x0[], Real *xd0[],
                       Int *isq[], Real *wns[], Real *wxds[], Real *xs[] )
 {
     Int ip,ix,ii,is,jp,iq;

// finite element representation
     Int  nY,nQ;
     Real Q[MxNPCs],dQdy[3*MxNPCs];
     Real *pdQdy[3];

// auxiliary subarray pointers
     Real *pwxds[1];

// workspaces
     Real *xwrk,*xdwrk;
     Real *pxwrk[3],*pxdwrk[3];
     Real *wrk0,*wrk1;
     Real *pwrk0[3],*pwrk1[3];

     Real  w,dn[3],xd[3],wsum;

// obtain range of up-to-date data dependencies

     if( ise > iss )
    {

// workspaces
        wrk0=  new Real[ nx*ise ]; subv( nx, ise,  wrk0,  pwrk0 );
        wrk1=  new Real[ nx*ise ]; subv( nx, ise,  wrk1,  pwrk1 );
        xwrk=  new Real[ nx*ise ]; subv( nx, ise,  xwrk,  pxwrk );
        xdwrk= new Real[ nx*ise ]; subv( nx, ise, xdwrk, pxdwrk );

// auxiliary pointers

        subv( 3,        MxNPCs,        dQdy,   pdQdy );

// set normals to 0

        setv( iss,ise,   nx, isq[0], ZERO,   xs );
        setv( iss,ise, nx+1, isq[0], ZERO,  wns );
        setv( iss,ise,    1, isq[0], ZERO, wxds );

// loop over integration points
        wsum= 0.;
        for( ii=0;ii<ny;ii++ )
       {

// shape functions and their derivatives
           w= wg[ii];
           wsum+= w;
           cshpe( isk, &nY,yg[ii], &(nQ),Q,dQdy );

// reset workspaces

           setv( iss,ise, nx, ZERO, pwrk0  );
           setv( iss,ise, nx, ZERO, pwrk1  );
           setv( iss,ise, nx, ZERO, pxwrk  );
           setv( iss,ise, nx, ZERO, pxdwrk );

// accumulate derivatives of physical to isparametric coordinates in workspaces
           coo->xatl(  iss,ise, nsk, isp, x0,  Q,        pxwrk );
           coo->xatl(  iss,ise, nsk, isp, xd0, Q,        pxdwrk );
           coo->dxdl(  iss,ise, nsk, isp, x0,  Q, pdQdy, pwrk0,pwrk1 );

// accumulate vector products of derivative vectors
           for( is=iss;is<ise;is++ )
          {
              iq= isq[0][is];

              xd[0]= pxdwrk[0][is];
              xd[1]= pxdwrk[1][is];
              xd[2]= pxdwrk[2][is];
              dn[0]= pwrk0[1][is]* pwrk1[2][is]-  pwrk0[2][is]* pwrk1[1][is];
              dn[1]= pwrk0[2][is]* pwrk1[0][is]-  pwrk0[0][is]* pwrk1[2][is];
              dn[2]= pwrk0[0][is]* pwrk1[1][is]-  pwrk0[1][is]* pwrk1[0][is];

              wns[0][iq]+= w*dn[0];
              wns[1][iq]+= w*dn[1];
              wns[2][iq]+= w*dn[2];
              wxds[0][iq]+= w*( xd[0]*dn[0]+ xd[1]*dn[1]+ xd[2]*dn[2] );
              xs[0][iq]+= w*pxwrk[0][is];
              xs[1][iq]+= w*pxwrk[1][is];
              xs[2][iq]+= w*pxwrk[2][is];
          }


// done loop over integration points
       }

        for( is=iss;is<ise;is++ )
       {
           iq= isq[0][is];
           w= wns[0][iq]*wns[0][iq]+ wns[1][iq]*wns[1][iq]+ wns[2][iq]*wns[2][iq];
           w= sqrt(w);
           wns[0][iq]= wns[0][iq]/w;
           wns[1][iq]= wns[1][iq]/w;
           wns[2][iq]= wns[2][iq]/w;
           wxds[0][iq]/= w;
           wns[3][iq]= w;
           xs[0][iq]/= wsum;
           xs[1][iq]/= wsum;
           xs[2][iq]/= wsum;
       }

        delete[] wrk0;
        delete[] wrk1;
        delete[] xwrk;
        delete[] xdwrk;
    }
 }


  void cFElemeng::areas( Int ies, Int iee, Int *iep[], Real *x0[], Real *xd0[],
                         Int *ieq[], Real *wns[], Real *wxds[], Real *xs[], Real *wrk, cCosystem *coo )
 {

     if( iee > ies )
    {

    }
/*   Int ip,ix,ii,is,jp,iq;

// finite element representation
     Int  nY,nQ;
     Real Q[MxNPCs],dQdy[3*MxNPCs];
     Real *pdQdy[3];

// auxiliary subarray pointers
     Real *pwxds[1];

// workspaces
     Real *xwrk,*xdwrk;
     Real *pxwrk[3],*pxdwrk[3];
     Real *wrk0,*wrk1;
     Real *pwrk0[3],*pwrk1[3];

     Real  w,dn[3],xd[3],wsum;

// obtain range of up-to-date data dependencies

     if( ise > iss )
    {

// workspaces
        wrk0=  new Real[ nx*ise ]; subv( nx, ise,  wrk0,  pwrk0 );
        wrk1=  new Real[ nx*ise ]; subv( nx, ise,  wrk1,  pwrk1 );
        xwrk=  new Real[ nx*ise ]; subv( nx, ise,  xwrk,  pxwrk );
        xdwrk= new Real[ nx*ise ]; subv( nx, ise, xdwrk, pxdwrk );

// auxiliary pointers

        subv( 3,        MxNPCs,        dQdy,   pdQdy );

// set normals to 0

        setv( iss,ise,   nx, isq[0], 0.,   xs );
        setv( iss,ise, nx+1, isq[0], 0.,  wns );
        setv( iss,ise,    1, isq[0], 0., wxds );

// loop over integration points
        wsum= 0.;
        for( ii=0;ii<ny;ii++ )
       {

// shape functions and their derivatives
           w= wg[ii];
           wsum+= w;
           cshpe( isk, &nY,yg[ii], &(nQ),Q,dQdy );

// reset workspaces

           setv( iss,ise, nx, 0., pwrk0  );
           setv( iss,ise, nx, 0., pwrk1  );
           setv( iss,ise, nx, 0., pxwrk  );
           setv( iss,ise, nx, 0., pxdwrk );

// accumulate derivatives of physical to isparametric coordinates in workspaces
           coo->xatl(  iss,ise, nsk, isp, x0,  Q,        pxwrk );
           coo->xatl(  iss,ise, nsk, isp, xd0, Q,        pxdwrk );
           coo->dxdl(  iss,ise, nsk, isp, x0,  Q, pdQdy, pwrk0,pwrk1 );

// accumulate vector products of derivative vectors
           for( is=iss;is<ise;is++ )
          {
              iq= isq[0][is];

              xd[0]= pxdwrk[0][is];
              xd[1]= pxdwrk[1][is];
              xd[2]= pxdwrk[2][is];
              dn[0]= pwrk0[1][is]* pwrk1[2][is]-  pwrk0[2][is]* pwrk1[1][is];
              dn[1]= pwrk0[2][is]* pwrk1[0][is]-  pwrk0[0][is]* pwrk1[2][is];
              dn[2]= pwrk0[0][is]* pwrk1[1][is]-  pwrk0[1][is]* pwrk1[0][is];

              wns[0][iq]+= w*dn[0];
              wns[1][iq]+= w*dn[1];
              wns[2][iq]+= w*dn[2];
              wxds[0][iq]+= w*( xd[0]*dn[0]+ xd[1]*dn[1]+ xd[2]*dn[2] );
              xs[0][iq]+= w*pxwrk[0][is];
              xs[1][iq]+= w*pxwrk[1][is];
              xs[2][iq]+= w*pxwrk[2][is];
          }


// done loop over integration points
       }

        for( is=iss;is<ise;is++ )
       {
           iq= isq[0][is];
           w= wns[0][iq]*wns[0][iq]+ wns[1][iq]*wns[1][iq]+ wns[2][iq]*wns[2][iq];
           w= sqrt(w);
           wns[0][iq]= wns[0][iq]/w;
           wns[1][iq]= wns[1][iq]/w;
           wns[2][iq]= wns[2][iq]/w;
           wxds[0][iq]/= w;
           wns[3][iq]= w;
           xs[0][iq]/= wsum;
           xs[1][iq]/= wsum;
           xs[2][iq]/= wsum;
       }

        delete[] wrk0;
        delete[] wrk1;
        delete[] xwrk;
        delete[] xdwrk;
    }*/
 }
