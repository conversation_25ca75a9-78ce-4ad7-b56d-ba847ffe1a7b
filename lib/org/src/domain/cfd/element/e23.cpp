using namespace std;


//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

#  include <domain/cfd/element/element.h>

   cfe23::cfe23()
  {
      nsp=2;
      nsq=1;
      ng=0;
      yg[0]= NULL; 
      yg[1]= NULL; 
      yg[2]= NULL; 
      wg=    NULL; 

/*    nce[0]= 1;
      icep[0]= new Int*[2];
      icep[0][0]= new Int[1];
      icep[0][1]= new Int[1];
      icep[0][0][ 0]= 0; icep[0][1][ 0]= 1; 
      iceq[0]= new Int*[1];
      iceq[0][0]= new Int[1];
      iceq[0][0][ 0]= 0; */

  }


   cfe23::~cfe23()
  {
  }

   void cfe23::shpf( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl ){};
   void cfe23::shpx( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl ){};

