
   using namespace std;

#  include <domain/cfd/element/element.h>

   cfp53::cfp53()
  {
      nsp=5;
      nsq=1;
      ng= 6;
      yg[0]= new Real[ng];
      yg[1]= new Real[ng];
      yg[2]= new Real[ng];
      wg   = new Real[ng];
      yg[0][0]= 0.500000000000e0; yg[1][0]= 0.000000000000e0; yg[2][0]= 0.211324865405e0; wg[0]= 0.083333333333e0;
      yg[0][1]= 0.000000000000e0; yg[1][1]= 0.500000000000e0; yg[2][1]= 0.211324865405e0; wg[1]= 0.083333333333e0;
      yg[0][2]= 0.500000000000e0; yg[1][2]= 0.500000000000e0; yg[2][2]= 0.211324865405e0; wg[2]= 0.083333333333e0;
      yg[0][3]= 0.500000000000e0; yg[1][3]= 0.000000000000e0; yg[2][3]= 0.788675134595e0; wg[3]= 0.083333333333e0;
      yg[0][4]= 0.000000000000e0; yg[1][4]= 0.500000000000e0; yg[2][4]= 0.788675134595e0; wg[4]= 0.083333333333e0;
      yg[0][5]= 0.500000000000e0; yg[1][5]= 0.500000000000e0; yg[2][5]= 0.788675134595e0; wg[5]= 0.083333333333e0;


      nce[1]= 4;
      icep[1]= new Int*[3];
      icep[1][0]= new Int[4];
      icep[1][1]= new Int[4];
      icep[1][2]= new Int[4];
      icep[1][0][ 0]= 0; icep[1][1][ 0]= 1; icep[1][2][ 0]= 4;
      icep[1][0][ 1]= 1; icep[1][1][ 1]= 2; icep[1][2][ 1]= 4;
      icep[1][0][ 2]= 2; icep[1][1][ 2]= 3; icep[1][2][ 2]= 4;
      icep[1][0][ 3]= 3; icep[1][1][ 3]= 0; icep[1][2][ 3]= 4;
      iceq[1]= new Int*[1];
      iceq[1][0]= new Int[4];
      iceq[1][0][0]= 0;
      iceq[1][0][1]= 0;
      iceq[1][0][2]= 0;
      iceq[1][0][3]= 0;

      nce[2]= 1;
      icep[2]= new Int*[4];
      icep[2][0]= new Int[1];
      icep[2][1]= new Int[1];
      icep[2][2]= new Int[1];
      icep[2][3]= new Int[1];
      icep[2][0][ 0]= 0; icep[2][1][ 0]= 3; icep[2][2][ 0]= 2; icep[2][3][ 0]= 1;
      iceq[2]= new Int*[1];
      iceq[2][0]= new Int[1];
      iceq[2][0][0]= 0;
  }


   cfp53::~cfp53()
  {
  }

   void cfp53::shpf( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {
  }

   void cfp53::shpx( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {
  }

//   void cfp53::volume( Int ies, Int iee, Int *iep[], Int nx, Real *x[], Int *ieq[], Real *wq[], Real *xq[], 
//                        Real *wrk[], cCosystem *coo )
   void cfp53::volume( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x, cAu3xView<Int>& ieq, cAu3xView<Real>& wq, cAu3xView<Real>& xq,
                       cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo )
  {
     Int ix, ie, ip0, ip1, ip2, ip3, ip4, iq;
     Real y0[3], y1[3], y2[3], y3[3], y4[3]; 
     Real vol, cent[3];
     Int np;
     Int ne_iek, *sieq;
     Int *siep;
     Real *sx;
     if( iee > ies )
    {
        ne_iek = ieq.get_dim1();
        np = x.get_dim1();

        sieq = ieq.get_data();  
        siep = iep.get_data();
        sx = x.get_data();

        for(ie=ies; ie<iee; ie++)
       {
           ip0 = siep[ADDR(0,ie,ne_iek)];
           ip1 = siep[ADDR(1,ie,ne_iek)];
           ip2 = siep[ADDR(2,ie,ne_iek)];
           ip3 = siep[ADDR(3,ie,ne_iek)];
           ip4 = siep[ADDR(4,ie,ne_iek)];

           for(ix=0; ix<nx; ix++)
          {
              y0[ix] = sx[ADDR(ix,ip0,np)];
              y1[ix] = sx[ADDR(ix,ip1,np)];
              y2[ix] = sx[ADDR(ix,ip2,np)];
              y3[ix] = sx[ADDR(ix,ip3,np)];
              y4[ix] = sx[ADDR(ix,ip4,np)];
          }

           pyramid_vol( y0, y1, y2, y3, y4, &vol, cent);

           //iq= ieq[0][ie];
           iq= sieq[ADDR(0,ie, ne_iek)];
           wq(0,iq) = vol;
           xq(0,iq) = cent[0]; 
           xq(1,iq) = cent[1]; 
           xq(2,iq) = cent[2]; 
       }
        
//        for( ie=ies;ie<iee;ie++ )
//       {
//           iq= ieq[0][ie];
//           cout << "coordinates and volumes "<<iq<<" "<< xq[0][iq]<<" "<< xq[1][iq]<<" "<< xq[2][iq]<<" "<<
//                    wq[0][iq]<<"\n";
//       }
//
//        for(ie=ies; ie<iee; ie++)
//       {
//           ip0 = iep[0][ie];
//           ip1 = iep[1][ie];
//           ip2 = iep[2][ie];
//           ip3 = iep[3][ie];
//           ip4 = iep[4][ie];
//
//           for(ix=0; ix<nx; ix++)
//          {
//              y0[ix] = x[ix][ip0];
//              y1[ix] = x[ix][ip1];
//              y2[ix] = x[ix][ip2];
//              y3[ix] = x[ix][ip3];
//              y4[ix] = x[ix][ip4];
//          }
//
//           cout << "iq " << ieq[0][ie] << "\n";
//           cout << y0[0] << " " << y0[1] << " " << y0[2] << "\n";
//           cout << y1[0] << " " << y1[1] << " " << y1[2] << "\n";
//           cout << y2[0] << " " << y2[1] << " " << y2[2] << "\n";
//           cout << y3[0] << " " << y3[1] << " " << y3[2] << "\n";
//           cout << y4[0] << " " << y4[1] << " " << y4[2] << "\n";
//       }
    }
//      exit(0);
  }

   void cfp53::pyramid_vol( Real *y0, Real *y1, Real *y2, Real *y3, Real *y4, Real *vol, Real *cent)
  {
      Real vol1, vol2, vol_all;
      Real cent1[3], cent2[3];

      vol1=0;
      tet_vol(y0, y1, y3, y4, &vol1, cent1);
      //cout << "volume of tet is " << vol1 << "\n";
      //cout << "centroid of tet is " << cent1[0] << " " << cent1[1] << " " << cent1[2] << "\n";

      vol2=0;
      tet_vol(y1, y2, y3, y4, &vol2, cent2);
      //cout << "volume of tet is " << vol1 << "\n";
      //cout << "centroid of tet is " << cent2[0] << " " << cent2[1] << " " << cent2[2] << "\n";

      vol_all = vol1 + vol2;

      cent[0] = vol1*cent1[0] + vol2*cent2[0];
      cent[1] = vol1*cent1[1] + vol2*cent2[1];
      cent[2] = vol1*cent1[2] + vol2*cent2[2];

      cent[0] /= vol_all;
      cent[1] /= vol_all;
      cent[2] /= vol_all;
     *vol = vol_all;
  }
 
