   using namespace std;


#  include <domain/cfd/element/element.h>

   cfq83::cfq83()
  {
      nsp=8;
      nsq=1;
      ng=8;
      yg[0]= new Real[ng];
      yg[1]= new Real[ng];
      yg[2]= new Real[ng];
      wg=    new Real[ng];
      yg[0][0]= 0.211324865405e0; yg[1][0]= 0.211324865405e0; yg[2][0]= 0.211324865405e0; wg[0]= 0.125;
      yg[0][1]= 0.788675134595e0; yg[1][1]= 0.211324865405e0; yg[2][1]= 0.211324865405e0; wg[1]= 0.125;
      yg[0][2]= 0.788675134595e0; yg[1][2]= 0.788675134595e0; yg[2][2]= 0.211324865405e0; wg[2]= 0.125;
      yg[0][3]= 0.211324865405e0; yg[1][3]= 0.788675134595e0; yg[2][3]= 0.211324865405e0; wg[3]= 0.125;
      yg[0][4]= 0.211324865405e0; yg[1][4]= 0.211324865405e0; yg[2][4]= 0.788675134595e0; wg[4]= 0.125;
      yg[0][5]= 0.788675134595e0; yg[1][5]= 0.211324865405e0; yg[2][5]= 0.788675134595e0; wg[5]= 0.125;
      yg[0][6]= 0.788675134595e0; yg[1][6]= 0.788675134595e0; yg[2][6]= 0.788675134595e0; wg[6]= 0.125;
      yg[0][7]= 0.211324865405e0; yg[1][7]= 0.788675134595e0; yg[2][7]= 0.788675134595e0; wg[7]= 0.125;


/*    nce[0]= 12;
      icep[0]= new Int*[2];
      icep[0][0]= new Int[12];
      icep[0][1]= new Int[12];
      icep[0][0][ 0]= 0; icep[0][1][ 0]= 1; 
      icep[0][0][ 1]= 1; icep[0][1][ 1]= 2; 
      icep[0][0][ 2]= 2; icep[0][1][ 2]= 3; 
      icep[0][0][ 3]= 3; icep[0][1][ 3]= 0; 
      icep[0][0][ 4]= 0; icep[0][1][ 4]= 4; 
      icep[0][0][ 5]= 1; icep[0][1][ 5]= 5; 
      icep[0][0][ 6]= 2; icep[0][1][ 6]= 6; 
      icep[0][0][ 7]= 3; icep[0][1][ 7]= 7; 
      icep[0][0][ 8]= 4; icep[0][1][ 8]= 5; 
      icep[0][0][ 9]= 5; icep[0][1][ 9]= 6; 
      icep[0][0][10]= 6; icep[0][1][10]= 7; 
      icep[0][0][11]= 7; icep[0][1][11]= 4; 
      iceq[0]= new Int*[1];
      iceq[0][0]= new Int[12];
      iceq[0][0][ 0]= 0;
      iceq[0][0][ 1]= 0;
      iceq[0][0][ 2]= 0;
      iceq[0][0][ 3]= 0;
      iceq[0][0][ 4]= 0;
      iceq[0][0][ 5]= 0;
      iceq[0][0][ 6]= 0;
      iceq[0][0][ 7]= 0;
      iceq[0][0][ 8]= 0;
      iceq[0][0][ 9]= 0;
      iceq[0][0][10]= 0;
      iceq[0][0][11]= 0;*/

      nce[2]= 6;
      icep[2]= new Int*[4];
      icep[2][0]= new Int[6];
      icep[2][1]= new Int[6];
      icep[2][2]= new Int[6];
      icep[2][3]= new Int[6];
      icep[2][0][ 0]= 3; icep[2][1][ 0]= 2; icep[2][2][ 0]= 1; icep[2][3][ 0]= 0;
      icep[2][0][ 1]= 4; icep[2][1][ 1]= 5; icep[2][2][ 1]= 6; icep[2][3][ 1]= 7;
      icep[2][0][ 2]= 0; icep[2][1][ 2]= 1; icep[2][2][ 2]= 5; icep[2][3][ 2]= 4;
      icep[2][0][ 3]= 1; icep[2][1][ 3]= 2; icep[2][2][ 3]= 6; icep[2][3][ 3]= 5;
      icep[2][0][ 4]= 2; icep[2][1][ 4]= 3; icep[2][2][ 4]= 7; icep[2][3][ 4]= 6;
      icep[2][0][ 5]= 3; icep[2][1][ 5]= 0; icep[2][2][ 5]= 4; icep[2][3][ 5]= 7;
      iceq[2]= new Int*[1];
      iceq[2][0]= new Int[6];
      iceq[2][0][ 0]= 0;
      iceq[2][0][ 1]= 0;
      iceq[2][0][ 2]= 0;
      iceq[2][0][ 3]= 0;
      iceq[2][0][ 4]= 0;
      iceq[2][0][ 5]= 0;
  }


   cfq83::~cfq83()
  {
  }

   void cfq83::shpf( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {

         g(0)=      (1-l[0])*(1-l[1])*(1-l[2]);
         g(1)=         l[0] *(1-l[1])*(1-l[2]);
         g(2)=         l[0] *   l[1] *(1-l[2]);
         g(3)=      (1-l[0])*   l[1] *(1-l[2]);
         g(4)=      (1-l[0])*(1-l[1])*   l[2];
         g(5)=         l[0] *(1-l[1])*   l[2];
         g(6)=         l[0] *   l[1] *   l[2];
         g(7)=      (1-l[0])*   l[1] *   l[2];

      dgdl(0,0)=           -(1-l[1])*(1-l[2]);
      dgdl(1,0)=  -(1-l[0])*         (1-l[2]);
      dgdl(2,0)=  -(1-l[0])*(1-l[1]);
              
      dgdl(0,1)=            (1-l[1])*(1-l[2]);
      dgdl(1,1)=     -l[0] *         (1-l[2]);
      dgdl(2,1)=     -l[0] *(1-l[1]);
             
      dgdl(0,2)=               l[1] *(1-l[2]);
      dgdl(1,2)=      l[0] *         (1-l[2]);
      dgdl(2,2)=     -l[0] *   l[1] ;
            
      dgdl(0,3)=              -l[1] *(1-l[2]);
      dgdl(1,3)=   (1-l[0])*         (1-l[2]);
      dgdl(2,3)=  -(1-l[0])*   l[1] ;
           
      dgdl(0,4)=           -(1-l[1])*   l[2];
      dgdl(1,4)=  -(1-l[0])*            l[2];
      dgdl(2,4)=   (1-l[0])*(1-l[1]);
          
      dgdl(0,5)=            (1-l[1])*   l[2];
      dgdl(1,5)=     -l[0]*             l[2];
      dgdl(2,5)=      l[0] *(1-l[1]);
         
      dgdl(0,6)=               l[1] *   l[2];
      dgdl(1,6)=      l[0] *            l[2];
      dgdl(2,6)=      l[0] *   l[1] ;
        
      dgdl(0,7)=              -l[1] *   l[2];
      dgdl(1,7)=   (1-l[0])*            l[2];
      dgdl(2,7)=   (1-l[0])*   l[1];

  }

   void cfq83::shpx( Real *l, cAu3xView<Real>& g, cAu3xView<Real>& dgdl )
  {
      shpf( l, g, dgdl );
  }


