   using namespace std;

#  include <domain/cfd/element/element.h>

   cFElement::cFElement()
  {

      ncp[0]= 2;
      ncp[1]= 3;
      ncp[2]= 4;

      ncq[0]= 1;
      ncq[1]= 1;
      ncq[2]= 1;

      setv( (Int)0,(Int)MxNSk,(Int)0,nce );
      setv( (Int)0,(Int)MxNSk,(Int**)NULL,icep );
      setv( (Int)0,(Int)MxNSk,(Int**)NULL,iceq );

      ng=0;
      yg[0]= NULL;
      yg[1]= NULL;
      yg[2]= NULL;
      wg=NULL;
  }

   cFElement::~cFElement()
  {
      Int jp,ik;
      for( ik=0;ik<MxNSk;ik++ ) 
     {
         if( nce[ik] > 0 )
        {
            for( jp=0;jp<ncp[ik];jp++ )
           {
               delete[] icep[ik][jp]; icep[ik][jp]=NULL;
           } 
            for( jp=0;jp<ncq[ik];jp++ )
           {
               delete[] iceq[ik][jp]; iceq[ik][jp]=NULL;
           } 
        }
         delete[] icep[ik];
         delete[] iceq[ik];
     }
      setv( (Int)0,(Int)MxNSk,(Int**)NULL,icep );
      setv( (Int)0,(Int)MxNSk,(Int**)NULL,iceq );
      setv( (Int)0,(Int)MxNSk,(Int)0,nce );

      ng=0;
      delete[] yg[0]; yg[0]= NULL;
      delete[] yg[1]; yg[1]= NULL;
      delete[] yg[2]; yg[2]= NULL;
      delete[] wg; wg=NULL;
  }

   void cFElement::getcost( Int ies, Int iee, Int *ieq[], Real *qc )
  {
      Int ik,ie,jq,iq;
      Int sum;
      sum= 0;
      for( ik=0;ik<MxNSk;ik++ )
     {
         sum+= nce[ik];
     }
      for( jq=0;jq<nsq;jq++ )
     {
         for( ie=ies;ie<iee;ie++ )
        {
            iq= ieq[jq][ie]; 
            qc[iq]+= sum;
        }
     } 
  }

   void cFElement::cmsk( Int ick, Int *n, Int ***icp, Int ***icq )
  {
     *n=   nce[ick];
     *icp= icep[ick];  
     *icq= iceq[ick];  
  }

   Int cFElement::getnd(){ return 1; };


//  void cFElement::volume3( Int ies, Int iee, Int *iep[], Int nx, cAu3xView<Real>& x, Int *ieq[], cAu3xView<Real>& wq, cAu3xView<Real>& xq,
//                           Real *wrk[], cCosystem *coo )
// {
//     Int ig,ix,jp,iq,ie;
//
//     Real *f,*dfdl[3];
//     Real  y[3];
//     Real *wrk0[3],*wrk1[3],*wrk2[3],*wrkx[3];
//     Real  wsum; 
//     Real  dn,w;
//     if( iee > ies )
//    {
//        f= new Real[nsp];
//        dfdl[0]= new Real[nsp];
//        dfdl[1]= new Real[nsp];
//        dfdl[2]= new Real[nsp];
//
//        setv( ies,iee,    nx, ieq[0], ZERO, xq );
//        setv( ies,iee,(Int)1, ieq[0], ZERO, wq );
//
//        for( ix=0;ix<nx;ix++ )
//       {
//           wrk0[ix]= wrk[ix     ];
//           wrk1[ix]= wrk[ix+  nx];
//           wrk2[ix]= wrk[ix+nx*2];
//           wrkx[ix]= wrk[ix+nx*3];
//       }
//
//        wsum=0;
//        for( ig=0;ig<ng;ig++ )
//       {
//           line( ig, nx,yg, y );
//           shpx( y, f,dfdl );
//           w= wg[ig];
//           wsum+= w;
// 
//           setv( ies,iee, nx,ZERO, wrkx ); 
//           setv( ies,iee, nx,ZERO, wrk0 ); 
//           setv( ies,iee, nx,ZERO, wrk1 ); 
//           setv( ies,iee, nx,ZERO, wrk2 ); 
//
//// accumulate derivatives of physical to isparametric coordinates in workspaces
//
//           coo->xatl(  ies,iee, nsp, iep, x, f,       wrkx );
//           coo->dxdl(  ies,iee, nsp, iep, x, f, dfdl, wrk0,wrk1,wrk2 );
//
//           for( ie=ies;ie<iee;ie++ )
//          {
//              iq= ieq[0][ie];
//              dn= ( wrk0[1][ie]* wrk1[2][ie]-  wrk0[2][ie]* wrk1[1][ie] )*wrk2[0][ie]+
//                  ( wrk0[2][ie]* wrk1[0][ie]-  wrk0[0][ie]* wrk1[2][ie] )*wrk2[1][ie]+
//                  ( wrk0[0][ie]* wrk1[1][ie]-  wrk0[1][ie]* wrk1[0][ie] )*wrk2[2][ie];
//              wq(0,iq)+= w*dn;
//
//              xq(0,iq)+= w*wrkx[0][ie];
//              xq(1,iq)+= w*wrkx[1][ie];
//              xq(2,iq)+= w*wrkx[2][ie];
//          }
//       }
//
//        for( ie=ies;ie<iee;ie++ )
//       {
//           iq= ieq[0][ie];
//           xq(0,iq)/= wsum;
//           xq(1,iq)/= wsum;
//           xq(2,iq)/= wsum;
//       }
//
///*      for( ie=ies;ie<iee;ie++ )
//       {
//           iq= ieq[0][ie];
//           cout << "coordinates and volumes "<<iq<<" "<< xq[0][iq]<<" "<< xq[1][iq]<<" "<< xq[2][iq]<<" "<<
//                    wq[0][iq]<<"\n";
//       }*/
//
//        delete[] f;
//        delete[] dfdl[0];
//        delete[] dfdl[1];
//        delete[] dfdl[2];
//        
//    }
// }

  void cFElement::volume3( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x, cAu3xView<Int>& ieq, cAu3xView<Real>& wq, cAu3xView<Real>& xq,
                           cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo )
 {
     Int ig,ix,jp,iq,ie;

     Real *sf, *sdfdl;
     cAu3xView<Real> dfdl, f;
     Real  y[3];
     Int me, nq;
     Real *swrk0,*swrk1,*swrk2,*swrkx;
     Real *swq, *sxq;
     Int ne_iek, *sieq;
     Real  wsum; 
     Real  dn,w;
     if( iee > ies )
    {
        me = wrk0.get_dim1();
        nq = xq.get_dim1();
        swrk0 = wrk0.get_data();
        swrk1 = wrk1.get_data();
        swrk2 = wrk2.get_data();
        swrkx = wrkx.get_data();

        swq = wq.get_data();
        sxq = xq.get_data();

        ne_iek = ieq.get_dim1(); 
        sieq = ieq.get_data(); 

        sf= new Real[nsp];
        sdfdl = new Real [nsp*3];
        f.subv_1d(nsp,sf);
        dfdl.subv(3,nsp,sdfdl);        

        #pragma acc enter data copyin (sf[0:nsp])
        #pragma acc enter data copyin (sdfdl[0:3*nsp])
//        #pragma acc enter data copyin (swrk0[0:me*nx])
//        #pragma acc enter data copyin (swrk1[0:me*nx])
//        #pragma acc enter data copyin (swrk2[0:me*nx])
//        #pragma acc enter data copyin (swrkx[0:me*nx])
//        #pragma acc enter data copyin (sxq[0:nx*nq])
//        #pragma acc enter data copyin (swq[0:(nx+1)*nq])


        //setv( ies,iee,    nx, ieq[0], ZERO, xq );
        //setv( ies,iee,(Int)1, ieq[0], ZERO, wq );
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop\
         present(sieq[0:ne_iek],sxq[0:nx*nq],swq[0:(nx+1)*nq],this) \
         default(none)
        for(ie=ies; ie<iee; ie++)
       {
           iq = sieq[ADDR(0,ie,ne_iek)];
           for(ix=0; ix<nx; ix++)
          {
              sxq[ADDR(ix,iq,nq)] = 0;
          }
           swq[ADDR(0,iq,nq)] = 0;
       }
        #pragma acc exit data copyout(this)

//        for( ix=0;ix<nx;ix++ )
//       {
//           wrk0[ix]= wrk[ix     ];
//           wrk1[ix]= wrk[ix+  nx];
//           wrk2[ix]= wrk[ix+nx*2];
//           wrkx[ix]= wrk[ix+nx*3];
//       }

        wsum=0;
        for( ig=0;ig<ng;ig++ )
       {
           line( ig, nx,yg, y ); //in cpu
           shpx( y, f,dfdl ); //in cpu
           w= wg[ig];
           wsum+= w;

           #pragma acc update device (sf[0:nsp])
           #pragma acc update device (sdfdl[0:3*nsp])
 
           setv( ies,iee, nx,ZERO, wrkx, "d"); 
           setv( ies,iee, nx,ZERO, wrk0, "d"); 
           setv( ies,iee, nx,ZERO, wrk1, "d"); 
           setv( ies,iee, nx,ZERO, wrk2, "d"); 

// accumulate derivatives of physical to isparametric coordinates in workspaces


           coo->xatl_gpu(  ies,iee, nsp, iep, x, f,       wrkx );
           coo->dxdl_gpu(  ies,iee, nsp, iep, x, f, dfdl, wrk0,wrk1,wrk2 );

           #pragma acc enter data copyin(this)
           #pragma acc parallel loop\
            present(sieq[0:ne_iek],swrk0[0:me*nx],swrk1[0:me*nx],swrk2[0:me*nx],swrkx[0:me*nx],swq[0:(nx+1)*nq],sxq[0:nx*nq],this) \
            default(none)
           for( ie=ies;ie<iee;ie++ )
          {
              //iq= ieq[0][ie];
              iq= sieq[ADDR(0,ie,ne_iek)];
              //dn= ( wrk0[1][ie]* wrk1[2][ie]-  wrk0[2][ie]* wrk1[1][ie] )*wrk2[0][ie]+
              //    ( wrk0[2][ie]* wrk1[0][ie]-  wrk0[0][ie]* wrk1[2][ie] )*wrk2[1][ie]+
              //    ( wrk0[0][ie]* wrk1[1][ie]-  wrk0[1][ie]* wrk1[0][ie] )*wrk2[2][ie];
              dn= ( swrk0[ADDR(1,ie,me)]* swrk1[ADDR(2,ie,me)]-  swrk0[ADDR(2,ie,me)]* swrk1[ADDR(1,ie,me)] )*swrk2[ADDR(0,ie,me)]+
                  ( swrk0[ADDR(2,ie,me)]* swrk1[ADDR(0,ie,me)]-  swrk0[ADDR(0,ie,me)]* swrk1[ADDR(2,ie,me)] )*swrk2[ADDR(1,ie,me)]+
                  ( swrk0[ADDR(0,ie,me)]* swrk1[ADDR(1,ie,me)]-  swrk0[ADDR(1,ie,me)]* swrk1[ADDR(0,ie,me)] )*swrk2[ADDR(2,ie,me)];
              //wq(0,iq)+= w*dn;
              swq[ADDR(0,iq,nq)]+= w*dn;

              //xq(0,iq)+= w*wrkx[0][ie];
              //xq(1,iq)+= w*wrkx[1][ie];
              //xq(2,iq)+= w*wrkx[2][ie];
              sxq[ADDR(0,iq,nq)]+= w*swrkx[ADDR(0,ie,me)];
              sxq[ADDR(1,iq,nq)]+= w*swrkx[ADDR(1,ie,me)];
              sxq[ADDR(2,iq,nq)]+= w*swrkx[ADDR(2,ie,me)];
          }
           #pragma acc exit data copyout(this)
       }

        #pragma acc enter data copyin(this)
        #pragma acc parallel loop\
         present(sieq[0:ne_iek],sxq[0:nx*nq],this) \
         default(none)
        for( ie=ies;ie<iee;ie++ )
       {
           //iq= ieq[0][ie];
           iq= sieq[ADDR(0,ie,nq)];
           //xq(0,iq)/= wsum;
           //xq(1,iq)/= wsum;
           //xq(2,iq)/= wsum;
           sxq[ADDR(0,iq,nq)]/= wsum;
           sxq[ADDR(1,iq,nq)]/= wsum;
           sxq[ADDR(2,iq,nq)]/= wsum;
       }
        #pragma acc exit data copyout(this)

        #pragma acc exit data copyout (sf[0:nsp])
        #pragma acc exit data copyout (sdfdl[0:3*nsp])
//        #pragma acc exit data copyout (swrk0[0:me*nx])
//        #pragma acc exit data copyout (swrk1[0:me*nx])
//        #pragma acc exit data copyout (swrk2[0:me*nx])
//        #pragma acc exit data copyout (swrkx[0:me*nx])
//        #pragma acc exit data copyout (sxq[0:nx*nq])
//        #pragma acc exit data copyout (swq[0:(nx+1)*nq])

/*      for( ie=ies;ie<iee;ie++ )
       {
           iq= ieq[0][ie];
           cout << "coordinates and volumes "<<iq<<" "<< xq[0][iq]<<" "<< xq[1][iq]<<" "<< xq[2][iq]<<" "<<
                    wq[0][iq]<<"\n";
       }*/

        delete[] sf; sf=NULL;
        //delete[] dfdl[0];
        //delete[] dfdl[1];
        //delete[] dfdl[2];
        delete[] sdfdl; sdfdl=NULL;
        
    }
 }

  void cFElement::volume2( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x, cAu3xView<Int>& ieq, cAu3xView<Real>& wq, cAu3xView<Real>& xq,
                           cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo )
 {
     Int ig,ix,jp,iq,ie;

     Real *sf,*sdfdl;
     cAu3xView<Real> f, dfdl;
     Real  y[2];
     Int me, nq;
     Real *swrk0,*swrk1,*swrkx;
     Real *swq, *sxq; 
     Int ne_iek, *sieq;
     Real  wsum; 
     Real  dn,w;
     if( iee > ies )
    {
        me = wrk0.get_dim1();
        nq = xq.get_dim1();
        swrk0 = wrk0.get_data();
        swrk1 = wrk1.get_data();
        swrkx = wrkx.get_data();

        swq = wq.get_data();
        sxq = xq.get_data();
     
        ne_iek = ieq.get_dim1(); 
        sieq = ieq.get_data(); 

        sf= new Real[nsp];
        sdfdl= new Real[2*nsp];

        f.subv_1d(nsp,sf);
        dfdl.subv(2,nsp,sdfdl);
//        dfdl[0]= new Real[nsp];
//        dfdl[1]= new Real[nsp];

        //setv( ies,iee,    nx, ieq, ZERO, xq, "h" );
        //setv( ies,iee,(Int)1, ieq, ZERO, wq, "h" );
        for(ie=ies; ie<iee; ie++)
       {
           iq = sieq[ADDR(0,ie,ne_iek)];
           for(ix=0; ix<nx; ix++)
          {
              sxq[ADDR(ix,iq,nq)] = 0;
          }
           swq[ADDR(0,iq,nq)] = 0;
       }

//        for( ix=0;ix<nx;ix++ )
//       {
//           wrk0[ix]= wrk[ix     ];
//           wrk1[ix]= wrk[ix+  nx];
//           wrkx[ix]= wrk[ix+nx*2];
//       }

        wsum=0;
        for( ig=0;ig<ng;ig++ )
       {
           line( ig, nx,yg, y );
           shpx( y, f,dfdl );
           w= wg[ig];
           wsum+= w;
 
           setv( ies,iee, nx,ZERO, wrkx, "h" ); 
           setv( ies,iee, nx,ZERO, wrk0, "h" ); 
           setv( ies,iee, nx,ZERO, wrk1, "h" ); 

// accumulate derivatives of physical to isparametric coordinates in workspaces

           coo->xatl(  ies,iee, nsp, iep, x, f,       wrkx );
           coo->dxdl(  ies,iee, nsp, iep, x, f, dfdl, wrk0,wrk1 );

           for( ie=ies;ie<iee;ie++ )
          {
              //iq= ieq[0][ie];
              iq= sieq[ADDR(0,ie,nq)];
              //dn= wrk0[0][ie]* wrk1[1][ie]-  wrk0[1][ie]* wrk1[0][ie];
              dn= swrk0[ADDR(0,ie,me)]* swrk1[ADDR(1,ie,me)]-  swrk0[ADDR(1,ie,me)]* swrk1[ADDR(0,ie,me)];
              //wq(0,iq)+= w*dn;
              swq[ADDR(0,iq,nq)]+= w*dn;

              //xq(0,iq)+= w*wrkx[0][ie];
              //xq(1,iq)+= w*wrkx[1][ie];
              sxq[ADDR(0,iq,nq)]+= w*swrkx[ADDR(0,ie,me)];
              sxq[ADDR(1,iq,nq)]+= w*swrkx[ADDR(1,ie,me)];
          }
       }

        for( ie=ies;ie<iee;ie++ )
       {
           //iq= ieq[0][ie];
           iq= sieq[ADDR(0,ie,nq)];
           //xq(0,iq)/= wsum;
           //xq(1,iq)/= wsum;
           sxq[ADDR(0,iq,nq)]/= wsum;
           sxq[ADDR(1,iq,nq)]/= wsum;
       }

/*      for( ie=ies;ie<iee;ie++ )
       {
           iq= ieq[0][ie];
           cout << "coordinates and volumes "<<iq<<" "<< xq[0][iq]<<" "<< xq[1][iq]<<" "<< wq[0][iq]<<"\n";
       }*/

        delete[] sf; sf=NULL;
        delete[] sdfdl; sdfdl=NULL;
        //delete[] f;
        //delete[] dfdl[0];
        //delete[] dfdl[1];
        
    }
 }

  void cFElement::area3( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, cAu3xView<Int>& ieq, cAu3xView<Real>& wns, cAu3xView<Real>& wxds, cAu3xView<Real>& xs, 
                         cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo )
 {
     Int ig,ix,jp,iq,ie;

     Real *sf,*sdfdl;
     cAu3xView<Real> f,dfdl;
     Real  y[3];
     Int me, nq,nb;
     Real *swrk0,*swrk1,*swrk2,*swrkx;
     Real *swq, *sxq, *swns, *swxds, *sxs;
     Int ne_iek, *sieq;
     Real  w,dn[3],xd[3],wsum;
     if( iee > ies )
    {
        me = wrk0.get_dim1();
        nb = wns.get_dim1();

        swrk0 = wrk0.get_data();
        swrk1 = wrk1.get_data();
        swrk2 = wrk2.get_data();
        swrkx = wrkx.get_data();

        swns = wns.get_data();
        swxds = wxds.get_data();
        sxs = xs.get_data();

        ne_iek = ieq.get_dim1(); 
        sieq = ieq.get_data(); 

        //f= new Real[nsp];
        //dfdl[0]= new Real[nsp];
        //dfdl[1]= new Real[nsp];
        //dfdl[2]= new Real[nsp];
        sf= new Real[nsp];
        sdfdl= new Real[3*nsp];

        f.subv_1d(nsp,sf);
        dfdl.subv(3,nsp,sdfdl);

        #pragma acc enter data copyin (sf[0:nsp])
        #pragma acc enter data copyin (sdfdl[0:3*nsp])
//        #pragma acc enter data copyin (swrk0[0:me*nx])
//        #pragma acc enter data copyin (swrk1[0:me*nx])
//        #pragma acc enter data copyin (swrk2[0:me*nx])
//        #pragma acc enter data copyin (swrkx[0:me*nx])

//        setv( ies,iee,   nx, ieq[0], ZERO,   xs );
//        setv( ies,iee, nx+1, ieq[0], ZERO,  wns );
//        setv( ies,iee,    1, ieq[0], ZERO, wxds );
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop\
         present(sieq[0:ne_iek],sxs[0:nx*nb],swns[0:(nx+1)*nb],swxds[0:nb],this) \
         default(none)
        for(ie=ies; ie<iee; ie++)
       {
           iq = sieq[ADDR(0,ie,ne_iek)];
           for(ix=0; ix<nx; ix++)
          {
              sxs[ADDR(ix,iq,nb)] = 0;
              swns[ADDR(ix,iq,nb)] = 0;
          }
           swns[ADDR(nx,iq,nb)] = 0;
           swxds[ADDR(0,iq,nb)] = 0;
       }
        #pragma acc exit data copyout(this)

//        for( ix=0;ix<nx;ix++ )
//       {
//           wrk0[ix]= wrk[ix     ];
//           wrk1[ix]= wrk[ix+  nx];
//           wrk2[ix]= wrk[ix+nx*2];
//           wrkx[ix]= wrk[ix+nx*3];
//       }

        wsum=0;
        for( ig=0;ig<ng;ig++ )
       {
           line( ig, nx-1,yg, y );
           shpx( y, f,dfdl );
           w= wg[ig];
           wsum+= w;

           #pragma acc update device (sf[0:nsp])
           #pragma acc update device (sdfdl[0:3*nsp])
 
           setv( ies,iee, nx,ZERO, wrkx, "d" ); 
           setv( ies,iee, nx,ZERO, wrk0, "d" ); 
           setv( ies,iee, nx,ZERO, wrk1, "d" ); 
           setv( ies,iee, nx,ZERO, wrk2, "d" ); 

// accumulate derivatives of physical to isparametric coordinates in workspaces

           coo->xatl_gpu(  ies,iee, nsp, iep, x0,  f,       wrkx );
           coo->xatl_gpu(  ies,iee, nsp, iep, xd0, f,       wrk2 );
           coo->dxdl_gpu(  ies,iee, nsp, iep, x0,  f, dfdl, wrk0,wrk1 );

           #pragma acc enter data copyin(this)
           #pragma acc parallel loop\
            private(xd,dn)\
            present(sieq[0:ne_iek],swrk0[0:me*nx],swrk1[0:me*nx],swrk2[0:me*nx],swrkx[0:me*nx],swns[0:(nx+1)*nb],sxs[0:nx*nb],swxds[0:nb],this) \
            default(none)
           for( ie=ies;ie<iee;ie++ )
          {
              //iq= ieq[0][ie];
              iq= sieq[ADDR(0,ie,ne_iek)];

              //xd[0]= wrk2[0][ie];
              //xd[1]= wrk2[1][ie];
              //xd[2]= wrk2[2][ie];
              xd[0]= swrk2[ADDR(0,ie,me)];
              xd[1]= swrk2[ADDR(1,ie,me)];
              xd[2]= swrk2[ADDR(2,ie,me)];
              //dn[0]= wrk0[1][ie]* wrk1[2][ie]-  wrk0[2][ie]* wrk1[1][ie];
              //dn[1]= wrk0[2][ie]* wrk1[0][ie]-  wrk0[0][ie]* wrk1[2][ie];
              //dn[2]= wrk0[0][ie]* wrk1[1][ie]-  wrk0[1][ie]* wrk1[0][ie];
              dn[0]= swrk0[ADDR(1,ie,me)]* swrk1[ADDR(2,ie,me)]-  swrk0[ADDR(2,ie,me)]* swrk1[ADDR(1,ie,me)];
              dn[1]= swrk0[ADDR(2,ie,me)]* swrk1[ADDR(0,ie,me)]-  swrk0[ADDR(0,ie,me)]* swrk1[ADDR(2,ie,me)];
              dn[2]= swrk0[ADDR(0,ie,me)]* swrk1[ADDR(1,ie,me)]-  swrk0[ADDR(1,ie,me)]* swrk1[ADDR(0,ie,me)];

              //wns(0,iq)+= w*dn[0];
              //wns(1,iq)+= w*dn[1];
              //wns(2,iq)+= w*dn[2];
              swns[ADDR(0,iq,nb)]+= w*dn[0];
              swns[ADDR(1,iq,nb)]+= w*dn[1];
              swns[ADDR(2,iq,nb)]+= w*dn[2];
              swxds[ADDR(0,iq,nb)]+= w*( xd[0]*dn[0]+ xd[1]*dn[1]+ xd[2]*dn[2] );
              sxs[ADDR(0,iq,nb)]+= w*swrkx[ADDR(0,ie,me)];
              sxs[ADDR(1,iq,nb)]+= w*swrkx[ADDR(1,ie,me)];
              sxs[ADDR(2,iq,nb)]+= w*swrkx[ADDR(2,ie,me)];
          }
           #pragma acc exit data copyout(this)
       }

        #pragma acc enter data copyin(this)
        #pragma acc parallel loop\
         present(sieq[0:ne_iek],swns[0:(nx+1)*nb],sxs[0:nx*nb],swxds[0:nb],this) \
         default(none)
        for( ie=ies;ie<iee;ie++ )
       {
           //iq= ieq[0][ie];
           iq= sieq[ADDR(0,ie,ne_iek)];
           //w= wns(0,iq)*wns(0,iq)+ wns(1,iq)*wns(1,iq)+ wns(2,iq)*wns(2,iq);
           w= swns[ADDR(0,iq,nb)]*swns[ADDR(0,iq,nb)]+ swns[ADDR(1,iq,nb)]*swns[ADDR(1,iq,nb)]+ swns[ADDR(2,iq,nb)]*swns[ADDR(2,iq,nb)];
           w= sqrt(w);
           //wns(0,iq)= wns(0,iq)/w;
           //wns(1,iq)= wns(1,iq)/w;
           //wns(2,iq)= wns(2,iq)/w;
           swns[ADDR(0,iq,nb)]= swns[ADDR(0,iq,nb)]/w;
           swns[ADDR(1,iq,nb)]= swns[ADDR(1,iq,nb)]/w;
           swns[ADDR(2,iq,nb)]= swns[ADDR(2,iq,nb)]/w;
           //wxds(0,iq)/= w;
           swxds[ADDR(0,iq,nb)]/= w;
           //wns(3,iq)= w;
           swns[ADDR(3,iq,nb)]= w;
           //xs(0,iq)/= wsum;
           //xs(1,iq)/= wsum;
           //xs(2,iq)/= wsum;
           sxs[ADDR(0,iq,nb)]/= wsum;
           sxs[ADDR(1,iq,nb)]/= wsum;
           sxs[ADDR(2,iq,nb)]/= wsum;
       }
        #pragma acc exit data copyout(this)

/*      for( ie=ies;ie<iee;ie++ )
       {
           iq= ieq[0][ie];
           cout << "coordinates and areas "<<iq<<" "<< xs[0][iq]<<" "<< xs[1][iq]<<" "<< xs[2][iq]<<" "<<
                    wns[0][iq]<<" "<< wns[1][iq]<<" "<< wns[2][iq]<<" "<< wns[3][iq]<<"\n";
       }*/

        #pragma acc exit data copyout (sf[0:nsp])
        #pragma acc exit data copyout (sdfdl[0:3*nsp])
//        #pragma acc exit data copyout (swrk0[0:me*nx])
//        #pragma acc exit data copyout (swrk1[0:me*nx])
//        #pragma acc exit data copyout (swrk2[0:me*nx])
//        #pragma acc exit data copyout (swrkx[0:me*nx])

        delete[] sf; sf=NULL;
        delete[] sdfdl; sdfdl=NULL;
        //delete[] dfdl[0];
        //delete[] dfdl[1];
        //delete[] dfdl[2];
        
    }
 }

  void cFElement::area2( Int ies, Int iee, cAu3xView<Int>& iep, Int nx, cAu3xView<Real>& x0, cAu3xView<Real>& xd0, cAu3xView<Int>& ieq, cAu3xView<Real>& wns, cAu3xView<Real>& wxds, cAu3xView<Real>& xs, 
                         cAu3xView<Real>& wrk0, cAu3xView<Real>& wrk1, cAu3xView<Real>& wrk2, cAu3xView<Real>& wrkx, cCosystem *coo )
 {
     Int ig,ix,jp,iq,ie;

     Real *sf,*sdfdl;
     cAu3xView<Real> f,dfdl;
     Real  y[2];
     Int me, nq, nb;
     Real *swrk0,*swrk2,*swrkx;
     Real *swq, *sxq, *swns, *swxds, *sxs;
     Int ne_iek, *sieq;
     Real  w,dn[2],xd[2],wsum;
     if( iee > ies )
    {
        me = wrk0.get_dim1();
        nb = wns.get_dim1();
        swrk0 = wrk0.get_data();
        swrk2 = wrk2.get_data();
        swrkx = wrkx.get_data();

        swns = wns.get_data();
        swxds = wxds.get_data();
        sxs = xs.get_data();

        ne_iek = ieq.get_dim1(); 
        sieq = ieq.get_data(); 

        sf= new Real[nsp];
        sdfdl= new Real[nsp];

        f.subv_1d(nsp,sf);
        dfdl.subv(1,nsp,sdfdl);

        //setv( ies,iee,   nx, ieq[0], ZERO,   xs );
        //setv( ies,iee, nx+1, ieq[0], ZERO,  wns );
        //setv( ies,iee,    1, ieq[0], ZERO, wxds );
        for(ie=ies; ie<iee; ie++)
       {
           iq = sieq[ADDR(0,ie,ne_iek)];
           for(ix=0; ix<nx; ix++)
          {
              sxs[ADDR(ix,iq,nb)] = 0;
              swns[ADDR(ix,iq,nb)] = 0;
          }
           swns[ADDR(nx,iq,nb)] = 0;
           swxds[ADDR(0,iq,nb)] = 0;
       }

//        for( ix=0;ix<nx;ix++ )
//       {
//           wrk0[ix]= wrk[ix     ];
//           wrk2[ix]= wrk[ix+  nx];
//           wrkx[ix]= wrk[ix+nx*2];
//       }

        wsum=0;
        for( ig=0;ig<ng;ig++ )
       {

           line( ig, nx-1,yg, y );
           shpx( y, f,dfdl );
           w= wg[ig];
           wsum+= w;
 
           setv( ies,iee, nx,ZERO, wrkx, "h" ); 
           setv( ies,iee, nx,ZERO, wrk0, "h" ); 
           setv( ies,iee, nx,ZERO, wrk2, "h" ); 

// accumulate derivatives of physical to isparametric coordinates in workspaces

           coo->xatl(  ies,iee, nsp, iep, x0,  f,       wrkx );
           coo->xatl(  ies,iee, nsp, iep, xd0, f,       wrk2 );
           coo->dxdl(  ies,iee, nsp, iep, x0,  f, dfdl, wrk0 );

           for( ie=ies;ie<iee;ie++ )
          {
              //iq= ieq[0][ie];
              iq= sieq[ADDR(0,ie,ne_iek)];

              //xd[0]= wrk2[0][ie];
              //xd[1]= wrk2[1][ie];
              //dn[0]= wrk0[1][ie];
              //dn[1]=-wrk0[0][ie];
              xd[0]= swrk2[ADDR(0,ie,me)];
              xd[1]= swrk2[ADDR(1,ie,me)];
              dn[0]= swrk0[ADDR(1,ie,me)];
              dn[1]=-swrk0[ADDR(0,ie,me)];

              //wns(0,iq)+= w*dn[0];
              //wns(1,iq)+= w*dn[1];
              swns[ADDR(0,iq,nb)]+= w*dn[0];
              swns[ADDR(1,iq,nb)]+= w*dn[1];
              //wxds(0,iq)+= w*( xd[0]*dn[0]+ xd[1]*dn[1] );
              swxds[ADDR(0,iq,nb)]+= w*( xd[0]*dn[0]+ xd[1]*dn[1] );
              //xs(0,iq)+= w*wrkx[0][ie];
              //xs(1,iq)+= w*wrkx[1][ie];
              sxs[ADDR(0,iq,nb)]+= w*swrkx[ADDR(0,ie,me)];
              sxs[ADDR(1,iq,nb)]+= w*swrkx[ADDR(1,ie,me)];
          }
       }

        for( ie=ies;ie<iee;ie++ )
       {
           //iq= ieq[0][ie];
           iq= sieq[ADDR(0,ie,ne_iek)];
           //w= wns(0,iq)*wns(0,iq)+ wns(1,iq)*wns(1,iq);
           w= swns[ADDR(0,iq,nb)]*swns[ADDR(0,iq,nb)]+ swns[ADDR(1,iq,nb)]*swns[ADDR(1,iq,nb)];
           w= sqrt(w);
           //wns(0,iq)= wns(0,iq)/w;
           //wns(1,iq)= wns(1,iq)/w;
           swns[ADDR(0,iq,nb)]= swns[ADDR(0,iq,nb)]/w;
           swns[ADDR(1,iq,nb)]= swns[ADDR(1,iq,nb)]/w;
           //wxds(0,iq)/= w;
           swxds[ADDR(0,iq,nb)]/= w;
           //wns(2,iq)= w;
           swns[ADDR(2,iq,nb)]= w;
           //xs(0,iq)/= wsum;
           //xs(1,iq)/= wsum;
           sxs[ADDR(0,iq,nb)]/= wsum;
           sxs[ADDR(1,iq,nb)]/= wsum;
       }

/*      for( ie=ies;ie<iee;ie++ )
       {
           iq= ieq[0][ie];
           cout << "coordinates and areas "<<iq<<" "<< xs[0][iq]<<" "<< xs[1][iq]<<" "<<
                    wns[0][iq]<<" "<< wns[1][iq]<<" "<< wns[2][iq]<<"\n";
       }*/

        delete[] sf; sf=NULL;
        delete[] sdfdl; sdfdl=NULL;
        
    }
  }

   void cFElement3V::tet_vol(Real *y0, Real *y1, Real *y2, Real *y3, Real *vol, Real *cent)
  {
      double a[3], b[3], c[3], tmp[3], tmpvol;
      double tri_cent[3];

//assume the tetrahedron is orented as base: 0-1-2, apex:3

//compute volume
      a[0] =y2[0] - y1[0];
      a[1] =y2[1] - y1[1];
      a[2] =y2[2] - y1[2];

      b[0] =y0[0] - y1[0];
      b[1] =y0[1] - y1[1];
      b[2] =y0[2] - y1[2];

      c[0] =y3[0] - y1[0];
      c[1] =y3[1] - y1[1];
      c[2] =y3[2] - y1[2];

      tmp[0] = a[1] * b[2] - a[2] * b[1];
      tmp[1] = a[2] * b[0] - a[0] * b[2];
      tmp[2] = a[0] * b[1] - a[1] * b[0];

      tmpvol = tmp[0]*c[0] + tmp[1]*c[1] + tmp[2]*c[2];
      tmpvol = tmpvol/6.0;

     *vol = tmpvol;

//compute centroid
      tri_cent[0] = (y0[0] + y1[0] + y2[0])/3.0;
      tri_cent[1] = (y0[1] + y1[1] + y2[1])/3.0;
      tri_cent[2] = (y0[2] + y1[2] + y2[2])/3.0;

      cent[0] = tri_cent[0]*0.75 + y3[0]*0.25;
      cent[1] = tri_cent[1]*0.75 + y3[1]*0.25;
      cent[2] = tri_cent[2]*0.75 + y3[2]*0.25;
  }

