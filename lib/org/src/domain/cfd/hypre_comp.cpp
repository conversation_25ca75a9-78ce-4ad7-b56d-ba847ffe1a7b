
   using namespace std;

#  include <cmath>
#  include <domain/cfd/domain.h>
#include "_hypre_utilities.h"

   void build_amg_dof_func(int local_num_rows, int first_local_row, int num_functions, int *dof_func)
  {
      Int j, k, tms;
      Int local_num_vars, rest;
      Int indx;

      local_num_vars = local_num_rows;
      rest = first_local_row-((first_local_row/num_functions)*num_functions);
      indx = num_functions-rest;
      if (rest == 0) indx = 0;
      k = num_functions - 1;
      for (j = indx-1; j > -1; j--)
         dof_func[j] = k--;
      tms = local_num_vars/num_functions;
      if (tms*num_functions+indx > local_num_vars) tms--;
      for (j=0; j < tms; j++)
      {
         for (k=0; k < num_functions; k++)
            dof_func[indx++] = k;
      }
      k = 0;
      while (indx < local_num_vars)
         dof_func[indx++] = k++;
  }


   void cFdDomain::hypre_comp_gmres()
  {
      Int it, i, iv, iq, j, jq, jv;
      int ilower, iupper, local_size;
      int nnz;
      double values[2000]; //should be big enough
      int cols[2000];
      Real *b_copy, *dsol_copy;
      int *rows;
      Real *r1;
      string precon_opt;

      HYPRE_ParCSRMatrix parcsr_A;
      HYPRE_ParVector par_b;
      HYPRE_ParVector par_dsol;

      int    restart = 100;
      int    modify = 1;
      double final_res_norm;
      int    num_iterations;

      r1 = new Real [nv];

      movegrid();
      frame();
      weights();


      ilower = 0;
      iupper = nq*nv - 1;
      local_size = iupper - ilower + 1;
      HYPRE_IJMatrixCreate(*(dev->getcomm()), ilower, iupper, ilower, iupper, &A);
      HYPRE_IJMatrixSetObjectType(A, HYPRE_PARCSR);

      /* Create the rhs and solution */
      HYPRE_IJVectorCreate(*(dev->getcomm()), ilower, iupper,&b);
      HYPRE_IJVectorSetObjectType(b, HYPRE_PARCSR);

      HYPRE_IJVectorCreate(*(dev->getcomm()), ilower, iupper,&dsol);
      HYPRE_IJVectorSetObjectType(dsol, HYPRE_PARCSR);

      precon_opt = "ilu";
      //precon_opt = "amg";

      /* Create solver */
      HYPRE_ParCSRGMRESCreate(*(dev->getcomm()), &gmres_solver);

      /* Create PC */
      if(precon_opt=="ilu")       HYPRE_ILUCreate(&precond);
      else if(precon_opt=="amg")  HYPRE_BoomerAMGCreate(&precond);

      b_copy = new Real [iupper+1];
      dsol_copy = new Real [iupper+1];
      rows = new int [local_size];

      for( Int it=0;it<niter;it++ )
     {

         //cfl= min( cfl1,cfl*dcfl );
         cfl= min( 100.,cfl*dcfl );

//assemble right hand side
         setv( 0,dof->size(), nv, 0., rhs );
         bcs();
         grad( q,dqdx );
   
         gradb( q,dqdx );
   
         if(limtype==1)
        {
            grads(dqdx);
   
            initvenklim();
            compvenklim();
            gtrhfm_venk(rhs);
        }
         else
        {
            gtrhf( rhs );
            gtrhm( rhs );
        }

//assemble jacobian matrix
         HYPRE_IJMatrixInitialize(A);
         zero_hypre_jac();
         setv( 0,dof->size(), nlhs, 0.,lhsa );
         assemble_jac();
         //assemble_jac_visc();
         assemble_jac_visc_fd();

//add pseudo time-stepping contribution to the diagonal
         for(iq=0; iq<nq; iq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               values[iv] = lhsa[nlhs-1][iq]/cfl; 
           }     
            add_to_hypre_matrix_diag( iq, values, 1);
        }
         HYPRE_IJMatrixAssemble(A);
         HYPRE_IJMatrixGetObject(A, (void**) &parcsr_A);


         //right hand side and solution vector
         HYPRE_IJVectorInitialize(b);
         HYPRE_IJVectorInitialize(dsol);

         j=0;
         for(iq=0; iq<nq; iq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               dsol_copy[j] = 0;
               b_copy[j] = rhs[iv][iq];
               rows[j] = j;
               j++;
           } 
        }
         HYPRE_IJVectorSetValues(b, local_size, rows, b_copy);
         HYPRE_IJVectorSetValues(dsol, local_size, rows, dsol_copy);

         HYPRE_IJVectorAssemble(b);
         HYPRE_IJVectorGetObject(b, (void **) &par_b);
         HYPRE_IJVectorAssemble(dsol);
         HYPRE_IJVectorGetObject(dsol, (void **) &par_dsol);


         /* Set some parameters (See Reference Manual for more parameters) */
         HYPRE_GMRESSetKDim(gmres_solver, restart);
         HYPRE_GMRESSetMaxIter(gmres_solver, 100); /* max iterations */
         HYPRE_GMRESSetTol(gmres_solver, 1e-1); /* conv. tolerance */
         HYPRE_GMRESSetPrintLevel(gmres_solver, 2); /* print solve info */
         HYPRE_GMRESSetLogging(gmres_solver, 1); /* needed to get run info later */
   
         if(precon_opt=="ilu")
        {
            int ilu_type, ilu_lfil, ilu_max_row_nnz;
            Real pc_tol, ilu_droptol, ilu_schur_max_iter;

            ilu_type = 10; //10: gmres-ilu(k), 11: gmres-ilut
            ilu_lfil = 0;
            pc_tol = 0.;
            ilu_max_row_nnz = 1000; 
            ilu_droptol = 1.0e-2;
            ilu_schur_max_iter = restart;

            HYPRE_ILUSetType(precond, ilu_type);
            HYPRE_ILUSetLevelOfFill(precond, ilu_lfil);
            HYPRE_ILUSetPrintLevel(precond, 1);
            HYPRE_ILUSetMaxIter(precond, 1);
            HYPRE_ILUSetTol(precond, pc_tol);
            HYPRE_ILUSetMaxNnzPerRow(precond,ilu_max_row_nnz);
            /* set the droptol */
            HYPRE_ILUSetDropThreshold(precond,ilu_droptol);
            /* set max iterations for Schur system solve */
            HYPRE_ILUSetSchurMaxIter( precond, ilu_schur_max_iter );
//            if(ilu_type == 20 || ilu_type == 21)
//            {
//               HYPRE_ILUSetNSHDropThreshold( pcg_precond, ilu_nsh_droptol);
//            }
            HYPRE_GMRESSetPrecond(gmres_solver,
                                  (HYPRE_PtrToSolverFcn) HYPRE_ILUSolve,
                                  (HYPRE_PtrToSolverFcn) HYPRE_ILUSetup,
                                  precond);

        }
         else if(precon_opt=="amg")
        {
            /* Now set up the AMG preconditioner and specify any parameters */
            HYPRE_BoomerAMGSetPrintLevel(precond, 1); /* print amg solution info */
            HYPRE_BoomerAMGSetCoarsenType(precond, 10);
            HYPRE_BoomerAMGSetInterpType(precond, 6);
            //HYPRE_BoomerAMGSetOldDefault(precond);
            HYPRE_BoomerAMGSetRelaxType(precond, 6); /* Sym G.S./Jacobi hybrid */
            HYPRE_BoomerAMGSetNumSweeps(precond, 1);
            HYPRE_BoomerAMGSetTol(precond, 0.0); /* conv. tolerance zero */
            HYPRE_BoomerAMGSetMaxIter(precond, 1); /* do only one iteration! */
            HYPRE_BoomerAMGSetNumFunctions(precond, nv);
      
            /* Set the GMRES preconditioner */
            HYPRE_GMRESSetPrecond(gmres_solver, 
                                  (HYPRE_PtrToSolverFcn) HYPRE_BoomerAMGSolve,
                                  (HYPRE_PtrToSolverFcn) HYPRE_BoomerAMGSetup, 
                                  precond);
   
        }


  
         /* Now setup and solve! */
         HYPRE_ParCSRGMRESSetup(gmres_solver, parcsr_A, par_b, par_dsol);
         HYPRE_ParCSRGMRESSolve(gmres_solver, parcsr_A, par_b, par_dsol);

         /* Run info - needed logging turned on */
         HYPRE_GMRESGetNumIterations(gmres_solver, &num_iterations);
         HYPRE_GMRESGetFinalRelativeResidualNorm(gmres_solver, &final_res_norm);
//         if (myid == 0)
//         {
//            printf("\n");
//            printf("Iterations = %d\n", num_iterations);
//            printf("Final Relative Residual Norm = %e\n", final_res_norm);
//            printf("\n");
//         }
   
         HYPRE_IJVectorGetValues(dsol, local_size, rows, dsol_copy);

         //copy back solution updates and update solution
         j=0;
         for(iq=0; iq<nq; iq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               dq[iv][iq] = dsol_copy[j];
               j++;
           } 
        }
         qupdt( dq );

         //write residuals to "hist.dat"
         for(iv=0; iv<nv; iv++) r1[iv]=0;
         resd( rhs, r1 );
         if(dev->getrank()==0 && ilev==0)
        {
           *flg << cfl<<" ";
           for( iv=0;iv<nv;iv++ )
          {
              *flg << r1[iv]<<" ";
          }
           *flg << "\n";
           flg->flush();
        }

         nexc++;
     }

      /* Destory solver and preconditioner */
      HYPRE_ParCSRGMRESDestroy(gmres_solver);
      if(precon_opt=="ilu")
     {
         HYPRE_ILUDestroy(precond);
     }
      else  if(precon_opt=="amg")
     {
         HYPRE_BoomerAMGDestroy(precond);
     }

      /* Clean up */
      HYPRE_IJMatrixDestroy(A);
      HYPRE_IJVectorDestroy(b);
      HYPRE_IJVectorDestroy(dsol);

      /* Finalize HYPRE */
      HYPRE_Finalize();


      save();

      delete[] b_copy; b_copy=NULL;
      delete[] dsol_copy; dsol_copy=NULL;
      delete[] r1; r1=NULL;
      delete[] rows; rows=NULL;
  }

   void cFdDomain::hypre_comp_ilu()
  {
      Int it, i, iv, iq, j, jq, jv;
      int ilower, iupper, local_size;
      int nnz;
      double values[2000]; //should be big enough
      int cols[2000];
      Real *b_copy, *dsol_copy;
      int *rows;
      Real *r1;

      HYPRE_ParCSRMatrix parcsr_A;
      HYPRE_ParVector par_b;
      HYPRE_ParVector par_dsol;

//      HYPRE_Solver solver;
      int    restart = 30;
      int    modify = 1;
      double final_res_norm;
      int    num_iterations;

      r1 = new Real [nv];

      movegrid();
      frame();
      weights();

      //checkjac();

      ilower = 0;
      iupper = nq*nv - 1;
      local_size = iupper - ilower + 1;
      HYPRE_IJMatrixCreate(*(dev->getcomm()), ilower, iupper, ilower, iupper, &A);
      HYPRE_IJMatrixSetObjectType(A, HYPRE_PARCSR);

      /* Create the rhs and solution */
      HYPRE_IJVectorCreate(*(dev->getcomm()), ilower, iupper,&b);
      HYPRE_IJVectorSetObjectType(b, HYPRE_PARCSR);

      HYPRE_IJVectorCreate(*(dev->getcomm()), ilower, iupper,&dsol);
      HYPRE_IJVectorSetObjectType(dsol, HYPRE_PARCSR);

      HYPRE_ILUCreate(&ilu_solver);


      b_copy = new Real [iupper+1];
      dsol_copy = new Real [iupper+1];
      rows = new int [local_size];

//      checkjac();
//      checkjac_visc();
      for( Int it=0;it<niter;it++ )
     {

         cfl= min( cfl1,cfl*dcfl );

//assemble right hand side
         setv( 0,dof->size(), nv, 0., rhs );
         bcs();
         grad( q,dqdx );
   
         gradb( q,dqdx );
   
         if(limtype==1)
        {
            grads(dqdx);
   
            initvenklim();
            compvenklim();
            gtrhfm_venk(rhs);
        }
         else
        {
            gtrhf( rhs );
            gtrhm( rhs );
        }

//assemble jacobian matrix A
         HYPRE_IJMatrixInitialize(A);
         zero_hypre_jac();
         setv( 0,dof->size(), nlhs, 0.,lhsa );
         assemble_jac();
         //assemble_jac_visc();
         assemble_jac_visc_fd();

//add pseudo time-stepping contribution to the diagonal
//         for(iq=0; iq<nq; iq++)
//        {
//            nnz=0;
//            for(iv=0; iv<nv; iv++)
//           {
//               lhs_jac[iq][iq].jac[iv][iv] += lhsa[nlhs-1][iq]/cfl; 
//              //cout << iq << " " << iv << " " << " " << lhs_jac[iq][iq].jac[iv][iv] << " " << lhsa[nlhs-1][iq]/cfl << "\n";
//           }     
//        }
         for(iq=0; iq<nq; iq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               values[iv] = lhsa[nlhs-1][iq]/cfl; 
           }     
            add_to_hypre_matrix_diag( iq, values, 1);
        }


//         //left hand side
//         for(iq=0; iq<nq; iq++)     
//        {
//            for(iv=0; iv<nv; iv++)
//           {
//               nnz=0;
//               for(jq=0; jq<nq; jq++) 
//              {
//                  for(jv=0; jv<nv; jv++)
//                 {
//                     if(fabs(lhs_jac[iq][jq].jac[iv][jv])>small)
//                    {
//                        cols[nnz] = jq*nv + jv;
//                        values[nnz] = lhs_jac[iq][jq].jac[iv][jv];
//                        nnz++;
//                    }      
//                 }
//              }
//               i = iq*nv + iv;
//               HYPRE_IJMatrixSetValues(A, 1, &nnz, &i, cols, values);
//           }
//        }
         HYPRE_IJMatrixAssemble(A);
         HYPRE_IJMatrixGetObject(A, (void**) &parcsr_A);

         //right hand side and solution vector
         HYPRE_IJVectorInitialize(b);
         HYPRE_IJVectorInitialize(dsol);
         j=0;
         for(iq=0; iq<nq; iq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               dsol_copy[j] = 0;
               b_copy[j] = rhs[iv][iq];
               rows[j] = j;
               j++;
           } 
        }
         HYPRE_IJVectorSetValues(b, local_size, rows, b_copy);
         HYPRE_IJVectorSetValues(dsol, local_size, rows, dsol_copy);

         HYPRE_IJVectorAssemble(b);
         HYPRE_IJVectorGetObject(b, (void **) &par_b);
         HYPRE_IJVectorAssemble(dsol);
         HYPRE_IJVectorGetObject(dsol, (void **) &par_dsol);

//         HYPRE_IJMatrixPrint(A, "IJ.out.A");  
//         exit(0);

         int ilu_type, ilu_lfil, ilu_max_row_nnz, max_iter;
         Real tol, ilu_droptol, ilu_schur_max_iter;

         ilu_type = 0; //0: ilu(k), 1: ilut. ilut is more expensive
         ilu_lfil = 0;
         ilu_droptol = 1.0e-02;
         ilu_max_row_nnz = 1000;
         ilu_schur_max_iter = 3;
         max_iter = 20;
         tol = 1.0e-2;

         /* set ilu type */
         HYPRE_ILUSetType(ilu_solver, ilu_type);
         /* set level of fill */
         HYPRE_ILUSetLevelOfFill(ilu_solver, ilu_lfil);
         /* set print level */
         HYPRE_ILUSetPrintLevel(ilu_solver, 2);
         /* set max iterations */
         HYPRE_ILUSetMaxIter(ilu_solver, max_iter);
         /* set max number of nonzeros per row */
         HYPRE_ILUSetMaxNnzPerRow(ilu_solver,ilu_max_row_nnz);
         /* set the droptol */
         HYPRE_ILUSetDropThreshold(ilu_solver,ilu_droptol);
         HYPRE_ILUSetTol(ilu_solver, tol);
         /* set max iterations for Schur system solve */
         HYPRE_ILUSetSchurMaxIter( ilu_solver, ilu_schur_max_iter );
   
         /* setup hypre_ILU solver */
         HYPRE_ILUSetup(ilu_solver, parcsr_A, par_b, par_dsol);
   
         /* hypre_ILU solve */
         HYPRE_ILUSolve(ilu_solver, parcsr_A, par_b, par_dsol);


         HYPRE_IJVectorGetValues(dsol, local_size, rows, dsol_copy);

         //copy back solution updates and update solution
         j=0;
         for(iq=0; iq<nq; iq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               dq[iv][iq] = dsol_copy[j];
               j++;
           } 
        }
         qupdt( dq );

         //write residuals to "hist.dat"
         for(iv=0; iv<nv; iv++) r1[iv]=0;
         resd( rhs, r1 );
         if(dev->getrank()==0 && ilev==0)
        {
           *flg << cfl<<" ";
           for( iv=0;iv<nv;iv++ )
          {
              *flg << r1[iv]<<" ";
          }
           *flg << "\n";
           flg->flush();
        }

         nexc++;
     }

      /* Destory solver and preconditioner */
      HYPRE_ILUDestroy(ilu_solver);

      /* Clean up */
      HYPRE_IJMatrixDestroy(A);
      HYPRE_IJVectorDestroy(b);
      HYPRE_IJVectorDestroy(dsol);

      /* Finalize HYPRE */
      HYPRE_Finalize();

      save();

      delete[] b_copy; b_copy=NULL;
      delete[] dsol_copy; dsol_copy=NULL;
      delete[] r1; r1=NULL;
      delete[] rows; rows=NULL;
  }

   void cFdDomain::hypre_comp_amg()
  {
      Int it, i, iv, iq, j, jq, jv;
      int ilower, iupper, local_size;
      int nnz;
      double values[2000]; //should be big enough
      int cols[2000];
      Real *b_copy, *dsol_copy;
      int *rows;
      Real *r1;

      HYPRE_ParCSRMatrix parcsr_A;
      HYPRE_ParVector par_b;
      HYPRE_ParVector par_dsol;

//      HYPRE_Solver solver;
      int    restart = 30;
      int    modify = 1;
      double final_res_norm;
      int    num_iterations;

      r1 = new Real [nv];

      movegrid();
      frame();
      weights();

      //checkjac();

      ilower = 0;
      iupper = nq*nv - 1;
      local_size = iupper - ilower + 1;
      HYPRE_IJMatrixCreate(*(dev->getcomm()), ilower, iupper, ilower, iupper, &A);
      HYPRE_IJMatrixSetObjectType(A, HYPRE_PARCSR);

      /* Create the rhs and solution */
      HYPRE_IJVectorCreate(*(dev->getcomm()), ilower, iupper,&b);
      HYPRE_IJVectorSetObjectType(b, HYPRE_PARCSR);

      HYPRE_IJVectorCreate(*(dev->getcomm()), ilower, iupper,&dsol);
      HYPRE_IJVectorSetObjectType(dsol, HYPRE_PARCSR);

      HYPRE_BoomerAMGCreate(&amg_solver);

      b_copy = new Real [iupper+1];
      dsol_copy = new Real [iupper+1];
      rows = new int [local_size];


//      checkjac();
      //for( Int it=0;it<niter;it++ )
      for( Int it=0;it<1000;it++ )
     {
         cout << "iteration=================================== " << it << "\n";
         cfl= min( cfl1,cfl*dcfl );

//assemble right hand side
         setv( 0,dof->size(), nv, 0., rhs );
         bcs();
         grad( q,dqdx );
   
         gradb( q,dqdx );
   
         if(limtype==1)
        {
            grads(dqdx);
   
            initvenklim();
            compvenklim();
            gtrhfm_venk(rhs);
        }
         else
        {
            gtrhf( rhs );
            gtrhm( rhs );
        }

//assemble jacobian matrix A
         HYPRE_IJMatrixInitialize(A);
         zero_hypre_jac();
         setv( 0,dof->size(), nlhs, 0.,lhsa );
         assemble_jac();

//add pseudo time-stepping contribution to the diagonal
         for(iq=0; iq<nq; iq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               values[iv] = lhsa[nlhs-1][iq]/cfl; 
           }     
            add_to_hypre_matrix_diag( iq, values, 1);
        }

         HYPRE_IJMatrixAssemble(A);
         HYPRE_IJMatrixGetObject(A, (void**) &parcsr_A);

         //right hand side and solution vector
         HYPRE_IJVectorInitialize(b);
         HYPRE_IJVectorInitialize(dsol);
         j=0;
         for(iq=0; iq<nq; iq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               dsol_copy[j] = 0;
               b_copy[j] = rhs[iv][iq];
               rows[j] = j;
               j++;
           } 
        }
         HYPRE_IJVectorSetValues(b, local_size, rows, b_copy);
         HYPRE_IJVectorSetValues(dsol, local_size, rows, dsol_copy);

         HYPRE_IJVectorAssemble(b);
         HYPRE_IJVectorGetObject(b, (void **) &par_b);
         HYPRE_IJVectorAssemble(dsol);
         HYPRE_IJVectorGetObject(dsol, (void **) &par_dsol);

         int coarsen_type = 10; //10:  HMIS-coarsening
         int interp_type = 6;  //6: extended+i
         int relax_type = 8; //hybrid SGS
         int num_sweeps = 1;
         int restri_type = 2; //1: AIR-1; 2: AIR-2
         double tol = 1.0e-5;
         int mg_max_inter = 20;
         double strong_threshold = 0.55;
         int p_max_elmts = 4;

         HYPRE_Int *dof_func;
         dof_func = hypre_CTAlloc(HYPRE_Int, local_size, HYPRE_MEMORY_HOST);
         build_amg_dof_func(local_size, ilower, nv, dof_func);

         //set up amg
         HYPRE_BoomerAMGSetPrintLevel(amg_solver, 1); 
         HYPRE_BoomerAMGSetCoarsenType(amg_solver, coarsen_type);
         HYPRE_BoomerAMGSetInterpType(amg_solver, interp_type);
         HYPRE_BoomerAMGSetRestriction(amg_solver, restri_type); 
         HYPRE_BoomerAMGSetRelaxType(amg_solver, relax_type); 
         HYPRE_BoomerAMGSetNumSweeps(amg_solver, num_sweeps);
         HYPRE_BoomerAMGSetTol(amg_solver, tol); 
         HYPRE_BoomerAMGSetMaxIter(amg_solver, mg_max_inter);
         HYPRE_BoomerAMGSetNumFunctions(amg_solver, nv);
         HYPRE_BoomerAMGSetStrongThreshold(amg_solver, strong_threshold);
         HYPRE_BoomerAMGSetPMaxElmts(amg_solver, p_max_elmts);
         HYPRE_BoomerAMGSetNodal(amg_solver, 0); //not sure which one to use
         HYPRE_BoomerAMGSetDofFunc(amg_solver, dof_func);

         //solver
         HYPRE_BoomerAMGSetup(amg_solver, parcsr_A, par_b, par_dsol);
         HYPRE_BoomerAMGSolve(amg_solver, parcsr_A, par_b, par_dsol);


         HYPRE_IJVectorGetValues(dsol, local_size, rows, dsol_copy);

         //copy back solution updates and update solution
         j=0;
         for(iq=0; iq<nq; iq++)
        {
            for(iv=0; iv<nv; iv++)
           {
               dq[iv][iq] = dsol_copy[j];
               j++;
           } 
        }
         qupdt( dq );

         //write residuals to "hist.dat"
         for(iv=0; iv<nv; iv++) r1[iv]=0;
         resd( rhs, r1 );
         if(dev->getrank()==0 && ilev==0)
        {
           *flg << cfl<<" ";
           for( iv=0;iv<nv;iv++ )
          {
              *flg << r1[iv]<<" ";
          }
           *flg << "\n";
           flg->flush();
        }

         nexc++;
     }

      /* Destory solver */
      HYPRE_BoomerAMGDestroy(amg_solver);

      /* Clean up */
      HYPRE_IJMatrixDestroy(A);
      HYPRE_IJVectorDestroy(b);
      HYPRE_IJVectorDestroy(dsol);

      /* Finalize HYPRE */
      HYPRE_Finalize();

      save();

      delete[] b_copy; b_copy=NULL;
      delete[] dsol_copy; dsol_copy=NULL;
      delete[] r1; r1=NULL;
      delete[] rows; rows=NULL;
  }
