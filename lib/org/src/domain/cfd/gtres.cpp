using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::gtres( cAu3xView<Real>& v, cAu3xView<Real>& r )
  {
      Int iqs,iqe, ibs,ibe, ics,ice;
      Int nb,nc;
      Int ix,ig,ic,iv,iq,iql,iqr,ib,ia;
      Int it,ick;

//      #pragma acc enter data copyin(sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],sres[0:nv*nq])
//      for( ig=0;ig<ng;ig++ )
//     {
//         #pragma acc enter data copyin( sxb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],sdqb[ig][0:nv*nbb[ig]],sdauxb[ig][0:nv*nbb[ig]],sresb[ig][0:nv*nbb[ig]],\
//                                        siqb[ig][0:nbb[ig]],swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]],sauxfb[ig][0:nauxf*nbb[ig]])
//     }
//      #pragma acc enter data copyin( swq[0:(nx+1)*nq],sxdq[0:nvel*nq])
//      #pragma acc enter data copyin (iprq0[0:nprq],iprq1[0:nprq])
//      #pragma acc enter data copyin (sqprd[0:nv*nprq],sauxprd[0:naux*nprq],sdqprd[0:nv*nprq],sdauxprd[0:nv*nprq],srhsprd[0:nv*nprq],\
//                                        swnprd[0:(nx+1)*nprq],swxdprd[0:nprq],sauxfprd[0:nauxf*nprq],sxprd[0:nx*nprq],sxqprd[0:nx*nprq] )
//      #pragma acc enter data copyin (sifq[0:2*nfc],\
//                                     swnc[0:(nx+1)*nfc],swxdc[0:nfc],sauxf[0:nauxf*nfc], \
//                                     sxc[0:nx*nfc])
//      #pragma acc enter data copyin (sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],slhsa[0:nlhs*nq])
//      #pragma acc enter data copyin (this)
//      start_acc_device();

      setv( 0,dof->size(), nv, ZERO, res,"d" );

      Real *sv;
      sv = v.get_data();
      dof->exchange( sv );
      while( dev->transit() )
     {

// auxiliary variables for the degrees of freedom

         dof->range( dev->avail(), &iqs,&iqe );
         //fld->dvar( iqs,iqe, q, aux, v, daux );
         fld->dvar( iqs,iqe, q, aux, dq, daux );

         //fld->daccel( iqs,iqe, omega, wq, xq,q,v,daux,aux, xdq,r );
         fld->daccel( iqs,iqe, omega, wq, xq,q,dq,daux,aux, xdq,res );

// fluxes - boundary faces
         for( ig=0;ig<ng;ig++ )
        {
            bdf[ig]->range( dev->avail(), &ibs,&ibe );
            setv( ibs,ibe, nv, ZERO, dqb[ig],"d" );
            setv( ibs,ibe, nv, ZERO, dauxb[ig],"d" );
            setv( ibs,ibe, nv, ZERO, resb[ig],"d" );
            //bbj[ig]->diflx( ibs,ibe, xb[ig],qb[ig],auxb[ig], dqb[ig],dauxb[ig], resb[ig], iqb[ig], xq,q,aux, v,daux,r,
            //               wnb[ig], wxdb[ig], auxfb[ig] );
            bbj[ig]->diflx( ibs,ibe, xb[ig],qb[ig],auxb[ig], dqb[ig],dauxb[ig], resb[ig], iqb_view[ig], xq,q,aux, dq,daux,res,
                            wnb[ig], wxdb[ig], auxfb[ig] );
            //bbj[ig]->dmflx( ibs,ibe, xb[ig],qb[ig],auxb[ig], dqb[ig],dauxb[ig], resb[ig], iqb[ig], xq,q,aux, v,daux,r,
            //               wnb[ig], wxdb[ig], auxfb[ig] );
            bbj[ig]->dmflx( ibs,ibe, xb[ig],qb[ig],auxb[ig], dqb[ig],dauxb[ig], resb[ig], iqb_view[ig], xq,q,aux, dq,daux,res,
                            wnb[ig], wxdb[ig], auxfb[ig] );
        }

// fluxes - periodic faces
         prd->range( dev->avail(), &ics,&ice );
         setv( ics,ice, nv, ZERO, rhsprd,"d" );
         setv( ics,ice, nv, ZERO, dqprd,"d" );
         setv( ics,ice, nv, ZERO, dauxprd,"d" );
         fld->roffset( ics,ice, -1., iprq1, dq, NULL_iview, dqprd );
         fld->voffset( ics,ice, -1., iprq1, daux, NULL_iview, dauxprd );
         fld->diflx( ics,ice, NULL_iview, qprd,auxprd,dqprd,dauxprd,rhsprd, iprq0, q,aux,dq,daux,res, wnprd,wxdprd, auxfprd );
         fld->dmflx( ics,ice, NULL_iview, xqprd,qprd,auxprd,dqprd,dauxprd,rhsprd, iprq0, xq,q,aux,dq,daux,res, xprd, wnprd, wxdprd, auxfprd );
         fld->roffset( ics,ice,  1,rhsprd);
        #pragma acc parallel loop gang vector\
         present(srhsprd[0:nv*nprq],sres[0:nv*nq],siprq1[0:nprq],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            for( iv=0;iv<nv;iv++ )
           {
               iq= siprq1[ic];
               //sres[iv][iq]+= rhsprd[iv][ic];
               sres[ADDR(iv,iq,nq)]+= srhsprd[ADDR(iv,ic,nprq)];
           }
        }

// fluxes - inner faces

         cnf->range( dev->avail(), &ics,&ice );
         //fld->diflx( ics,ice, ifq[0], q,aux,v,daux,r, ifq[1], q,aux,v,daux,r, wnc,wxdc,auxf );
         //fld->diflx( ics,ice, ifq, q,aux,v,daux,r,wnc,wxdc,auxf );
         fld->diflx( ics,ice, ifq, q,aux,dq,daux,res,wnc,wxdc,auxf );
         //fld->dmflx( ics,ice, ifq[0], xq,q,aux,v,daux,r, ifq[1], xq,q,aux,v,daux,r, xc, wnc, wxdc, auxf );
         fld->dmflx( ics,ice, ifq,xq,q,aux,dq,daux,res,xc, wnc, wxdc, auxf );

// source terms
     }

      dof->range( dev->getrank(), &iqs,&iqe );

      //fld->dsrhs( iqs,iqe, cfl, q, aux, v, daux, dqdx, dst, wq, r,lhsa );
      fld->dsrhs( iqs,iqe, cfl, q, aux, dq, daux, dqdx, dst, wq, res,lhsa );
      //fld->dvrhs( iqs,iqe, cfl, q, aux, v, daux, dqdx, dst, wq, r,lhsa );
      fld->dvrhs( iqs,iqe, cfl, q, aux, dq, daux, dqdx, dst, wq, res,lhsa );


//      #pragma acc exit data copyout(sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],sres[0:nv*nq])
//      for( ig=0;ig<ng;ig++ )
//     {
//         #pragma acc exit data copyout( sxb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],sdqb[ig][0:nv*nbb[ig]],sdauxb[ig][0:nv*nbb[ig]],sresb[ig][0:nv*nbb[ig]],\
//                                        siqb[ig][0:nbb[ig]],swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]],sauxfb[ig][0:nauxf*nbb[ig]])
//     }
//      #pragma acc exit data copyout( swq[0:(nx+1)*nq],sxdq[0:nvel*nq])
//      #pragma acc exit data copyout (iprq0[0:nprq],iprq1[0:nprq])
//      #pragma acc exit data copyout (sqprd[0:nv*nprq],sauxprd[0:naux*nprq],sdqprd[0:nv*nprq],sdauxprd[0:nv*nprq],srhsprd[0:nv*nprq],\
//                                     swnprd[0:(nx+1)*nprq],swxdprd[0:nprq],sauxfprd[0:nauxf*nprq],sxprd[0:nx*nprq],sxqprd[0:nx*nprq] )
//      #pragma acc exit data copyout (sifq[0:2*nfc],\
//                                     swnc[0:(nx+1)*nfc],swxdc[0:nfc],sauxf[0:nauxf*nfc], \
//                                     sxc[0:nx*nfc])
//      #pragma acc exit data copyout (sdqdx[0:nx*nv*nq],sdst[0:2*nq],slhsa[0:nlhs*nq])
//      #pragma acc exit data copyout (this)
//      exit_acc_device();
      if(fld->gettype()==mfroe_gas_cht && vsc->gettype()==komegalowre_visc)
     {
         //zero out the residual array for mass, x-mom, y-mom, z-mom, k and omega for the solid
        #pragma acc parallel loop gang vector\
         present(sres[0:nv*nq],sq[0:nv*nq],this) \
         default(none)
         for(int iq=0; iq<nq; iq++)
        {
            if(sq[ADDR(5,iq,nq)]==0)
           {
               sres[ADDR(0,iq,nq)] = 0; //mass
               sres[ADDR(1,iq,nq)] = 0; //x-mom
               sres[ADDR(2,iq,nq)] = 0; //y-mom
               sres[ADDR(3,iq,nq)] = 0; //z-mom
               sres[ADDR(5,iq,nq)] = 0; //medium
               sres[ADDR(nv-2,iq,nq)] = 0; //k
               sres[ADDR(nv-1,iq,nq)] = 0; //omega
           }
        }
     }
      else if(fld->gettype()==mfroe_gas_cht && vsc->gettype()==laminar_visc)
     {
         //zero out the residual array for mass, x-mom, y-mom, z-mom, k and omega for the solid
        #pragma acc parallel loop gang vector\
         present(sres[0:nv*nq],sq[0:nv*nq],this) \
         default(none)
         for(int iq=0; iq<nq; iq++)
        {
            if(sq[ADDR(5,iq,nq)]==0)
           {
               sres[ADDR(0,iq,nq)] = 0; //mass
               sres[ADDR(1,iq,nq)] = 0; //x-mom
               sres[ADDR(2,iq,nq)] = 0; //y-mom
               sres[ADDR(3,iq,nq)] = 0; //z-mom
               sres[ADDR(5,iq,nq)] = 0; //medium
           }
        }
     }

  }

