
   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::gtrhs( cAu3xView<Real>& r )
  {

      //cout << "mutex free gtrhs\n";
//      start_acc_device();

      //setv( 0,dof->size(), nv, 0., r );
      //setv( 0,dof->size(), nlhs, 0.,lhsa );
      setv( 0,dof->size(), nv, ZERO, rhs, "d" );
      setv( 0,dof->size(), nlhs, ZERO,lhsa,"d" );
      bcs();
      //grad( q,dqdx );
      grad();

      //gradb( q,dqdx );
      gradb();

      if(limtype==1)
     {
         //grads(dqdx);
         grads();

         initvenklim();
         compvenklim();
         gtrhfm_venk(r);
     }
      else
     {
         gtrhf( r );
         gtrhm( r );
     }

//      for(Int iq=0; iq<nq; iq++)
//     {
//         cout << r[0][iq] << " " << r[1][iq] << " " << r[2][iq] << " " << r[3][iq] << "\n";
//     } 

#ifdef PETSC
      if(fld->gettype()!=roe_gas || gmres_glb)
     {
         //matrix-free LHS
         gtlhs( lhsa);
     }
#else
      gtlhs( lhsa);
#endif

      gtrhv( r );

      //deterministic stress contribution
      gtrhsds(r);

//      for(int iq=0; iq<nq; iq++)
//     {
//         cout << iq << " " << r[0][iq] << " " << r[1][iq] << " " << r[2][iq] << " " << lhsa[0][iq] << "\n";
//     }
      if(fld->gettype()==mfroe_gas_cht && vsc->gettype()==komegalowre_visc)
     {
         //zero out the residual array for the energy equation for artificial compresibility method
        #pragma acc parallel loop gang vector\
         present(srhs[0:nv*nq],sq[0:nv*nq],this) \
         default(none)
         for(int iq=0; iq<nq; iq++)
        {
            if(sq[ADDR(5,iq,nq)]==0)
           {
               srhs[ADDR(0,iq,nq)] = 0; //mass
               srhs[ADDR(1,iq,nq)] = 0; //x-mom
               srhs[ADDR(2,iq,nq)] = 0; //y-mom
               srhs[ADDR(3,iq,nq)] = 0; //z-mom
               srhs[ADDR(5,iq,nq)] = 0; //medium
               srhs[ADDR(nv-2,iq,nq)] = 0; //k
               srhs[ADDR(nv-1,iq,nq)] = 0; //omega
           }
        }
     }
      else if(fld->gettype()==mfroe_gas_cht && vsc->gettype()==laminar_visc)
     {
         //zero out the residual array for the energy equation for artificial compresibility method
        #pragma acc parallel loop gang vector\
         present(srhs[0:nv*nq],sq[0:nv*nq],this) \
         default(none)
         for(int iq=0; iq<nq; iq++)
        {
            if(sq[ADDR(5,iq,nq)]==0)
           {
               srhs[ADDR(0,iq,nq)] = 0; //mass
               srhs[ADDR(1,iq,nq)] = 0; //x-mom
               srhs[ADDR(2,iq,nq)] = 0; //y-mom
               srhs[ADDR(3,iq,nq)] = 0; //z-mom
               srhs[ADDR(5,iq,nq)] = 0; //medium
           }
        }
     }
//      exit_acc_device();
  }
