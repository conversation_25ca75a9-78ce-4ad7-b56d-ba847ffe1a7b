   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::gtresd( cAu3xView<Real>& r, cAu3xView<Real>& s )
  {
      Int iqs,iqe;
      Int ix,iv,iq;

//      start_acc_device();
       
      dof->range( dev->getrank(), &iqs,&iqe );
     #pragma acc parallel loop \
      present(sres[0:nv*nprq],srhs[0:nv*nq],this) \
      default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            //s[iv][iq]= mgrhs[iv][iq]+ r[iv][iq]- s[iv][iq];
            sres[ADDR(iv,iq,nq)]= srhs[ADDR(iv,iq,nq)]- sres[ADDR(iv,iq,nq)];
        }
     }

//      exit_acc_device();
  }

