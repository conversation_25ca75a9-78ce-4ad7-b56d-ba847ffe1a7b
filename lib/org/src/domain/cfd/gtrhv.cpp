   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::gtrhv( cAu3xView<Real>& r )
  {
      Int iqs,iqe;
      Int id,iv,iq;

//     #pragma acc enter data copyin (swq[0:(nx+1)*nq])
//     #pragma acc enter data copyin (sq[0:nv*nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq],srhs[0:nv*nq])
//     #pragma acc enter data copyin (sdst[0:2*nq],slhsa[0:nlhs*nq])
//     #pragma acc enter data copyin (su[0][0:nv*nq],su[1][0:nv*nq],su[2][0:nv*nq])
//     #pragma acc enter data copyin (this)
//      start_acc_device();

      dof->exchange( slhsa );
      while( dev->transit() )
     {
         dof->range( dev->avail(), &iqs,&iqe );
         //fld->srhs( iqs,iqe, cfl, q, aux, dqdx,  dst, NULL,NULL, wq, r,lhsa );
         fld->srhs( iqs,iqe, cfl, q, aux, dqdx,  dst, wq, rhs,lhsa );
         if( unst )
        {
/*          fld->vrhs( iqs,iqe, -1./dtm, u[0], aux, dqdx, dst, wq, r,lhsa );
            fld->vrhs( iqs,iqe,  1./dtm, u[1], aux, dqdx, dst, wq, r,lhsa );*/
           #pragma acc parallel loop \
            present(srhs[0:nv*nq],su[0][0:nv*nq],su[1][0:nv*nq],su[2][0:nv*nq],swq[0:(nx+1)*nq],this) \
            default(none)
            for( iq=iqs;iq<iqe;iq++ )
           {
               for( iv=0;iv<nv;iv++ )
              {
                  //r[iv][iq]= -wq[0][iq]*( 1.5*u[0][iv][iq] -2*u[1][iv][iq] + 0.5*u[2][iv][iq] )/dtm+ r[iv][iq];
                  srhs[ADDR(iv,iq,nq)]= -swq[ADDR(0,iq,nq)]*( 1.5*su[0][ADDR(iv,iq,nq)] -2*su[1][ADDR(iv,iq,nq)] + 0.5*su[2][ADDR(iv,iq,nq)] )/dtm+ srhs[ADDR(iv,iq,nq)];
              }
           }
        }
     }
//     #pragma acc exit data copyout (swq[0:(nx+1)*nq])
//     #pragma acc exit data copyout (sq[0:nv*nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq],srhs[0:nv*nq])
//     #pragma acc exit data copyout (sdst[0:2*nq],slhsa[0:nlhs*nq])
//     #pragma acc exit data copyout (su[0][0:nv*nq],su[1][0:nv*nq],su[2][0:nv*nq])
//     #pragma acc exit data copyout (this)
//      exit_acc_device();
  }
