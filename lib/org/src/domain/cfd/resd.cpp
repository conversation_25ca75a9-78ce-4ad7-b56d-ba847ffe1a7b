

   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::resd( cAu3xView<Real>& r, Real *r1 )
  {
      Int iqs,iqe;
      Int iv,iq;
      Real tmp;
//     #pragma acc enter data copyin (srhs[0:nv*nq])
//     #pragma acc enter data copyin (this)

//      start_acc_device();

      setv( 0,nv, ZERO, r1 );
/*    dof->exchange( r[0] );
      while( dev->transit() );*/

// inversion
      dof->range( dev->getrank(), &iqs,&iqe );
      for( iv=0;iv<nv;iv++ )
     {
         tmp = 0;
        #pragma acc parallel loop\
         present(srhs[0:nv*nq],this) \
         reduction(+:tmp) \
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //r1[iv]+= abs( r[iv][iq] );
            tmp+= abs( srhs[ADDR(iv,iq,nq)] );
        }
         r1[iv] = tmp;
     } 
      
      dev->gsum( nv,r1 );

//     #pragma acc exit data copyout (srhs[0:nv*nq])
//     #pragma acc exit data copyout (this)
//      exit_acc_device();
  }

   void cFdDomain::resd2( cAu3xView<Real>& r, Real *r1 )
  {
      Int iqs,iqe;
      Int iv,iq;

      dof->range( dev->getrank(), &iqs,&iqe );
      for( iq=iqs;iq<iqe;iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            (*r1) +=  r(iv,iq)*r(iv,iq);
        }
     } 
      dev->gsum( 1,r1 );

  }
