   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::layers()
  {
      box_t bx;
      Int  ig,ip,jp;
      Int  ist,ien;
      Int  ilyr;
      Int *sipb;
      Int *ipb[1];
      Int *ihlp;
      Real *sy; cAu3xView<Real> y;
      Real dbx[2],lbx,dmin,d;
      Int  npl0,npl;

      setv( 0,MxNBG, 0, nbl );
      setv( 0,MxNBG, (Int*)NULL, ibpl );
      setv( 0,MxNBG, (Int*)NULL, ibql );

      ihlp= new Int[np];
      for( ig=0;ig<ng;ig++ )
     {

         nbl[ig]=0;
         ibql[ig]= new Int[nbb[ig]];
         setv( 0,nbb[ig], -1,ibql[ig] );

 //      cout << "boundary object for this group is "<<bbj[ig]<<"\n";
         if( bbj[ig]->layers() )
        {
            ist= 0;
            bpts( ig, &ien, &sipb );

            sy=   new Real[ien*3];
            y.subv( 3,ien, sy );
            subv( 1,ien, sipb,ipb );

            ibpl[ig]= new Int[ien];
            setv( ist,ien,   -1,ibpl[ig] );

            //find bounding box of this boundary
            coo->bcoor( ist,ien, ipb,xp, y );
            reset( &bx );
            add( ist,ien, y, &bx );

            diag( bx,dbx );
            lbx=  dbx[0]*dbx[0];
            lbx+= dbx[1]*dbx[1];
            lbx+=small;
            lbx= sqrt(lbx);
            dbx[0]/= lbx;
            dbx[1]/= lbx;

//          cout << "bounding box for boundary "<<ig<<" "<<bgnm[ig]<<": "<<bx.x1[0]<<" "<<bx.x1[1]<<" "<<bx.x0[0]<<" "<<bx.x0[1]<<"\n";


            dmin= big;
            for( jp=ist;jp<ien;jp++ )
           {
               d= ( y(0,jp)-bx.x0[0] )*dbx[0]+ ( y(1,jp)-bx.x0[1] )*dbx[1];
               dmin= fmin( dmin,d );
           }
//          cout << "minimum coordinate is "<<dmin << "\n";

// find points in the bottom-left  corner of the boundary bounding box
//       cout << "ihlp for first layer - group "<<ig<<"\n";

//          cout << "lbx is "<<lbx<<"\n";
            npl0= 0;
            setv( 0,np, -1,ihlp );
            for( jp=ist;jp<ien;jp++ )
           {
               ip= ipb[0][jp];
//             cout << y[0][jp]<<" "<<y[1][jp]<<" "<<y[0][jp]-bx.x0[0]<<" "<<y[1][jp]-bx.x0[1]<<"\n";
               d= ( y(0,jp)-bx.x0[0] )*dbx[0]+ ( y(1,jp)-bx.x0[1] )*dbx[1];
#ifdef FP32
               if( fabs(d-dmin) < 1e-7 )
#else
               if( fabs(d-dmin) < lbx*1.e-6 )
#endif
              {
                  ihlp[ip]= 0;
                  ibpl[ig][jp]= 0;
                  npl0++;
              }
           }
                  
//          cout << npl0 << " points on first layer\n";

            ilyr=0;
            do
           {
               layerc( nb[ig], ibp[ig], ibb[ig], ihlp, ibql[ig], ilyr, -1, ilyr+1 );
               npl=0;
               for( jp=ist;jp<ien;jp++ )
              {
                  ip= ipb[0][jp];
                  if( ihlp[ip] == ilyr+1 )
                 { 
                     ibpl[ig][jp]= ilyr+1;
                     npl++; 
                 };      
              }
               ilyr++;
               if( (npl != 0) && (npl != npl0) )
              {
                  ilyr= 0;
                  break;
              }
           }while( npl > 0 );
//          cout << "layers on this boundary "<<ilyr<<"\n";
         
            nbl[ig]= ilyr;

            delete[] sipb;
            delete[] sy;
        }
     }
      delete[] ihlp; ihlp= NULL;
  }


   void cFdDomain::layerc( Int *n, Int ***icp, Int ***icq, Int *imrk, Int *cmrk, Int iold, Int ifree, Int inew )
  {

      Int          ibk,ic,jp,ip,iq;

      bool         news,olds,avls;


      for( ibk=0;ibk<nbk;ibk++ )
     {
         if( blm[ibk]->layers() )
        {
            for( ic=0;ic<n[ibk];ic++ )
           {
               olds= false;
               news= false;
               avls= false;
               for( jp=0;jp<nbp[ibk];jp++ )
              {
                  ip= icp[ibk][jp][ic];
                  olds= olds || ( imrk[ip] == iold );
                  news= news || ( imrk[ip] == inew );
                  avls= avls || ( imrk[ip] == ifree );
     
              }
               if( olds )
              {
                  for( jp=0;jp<nbp[ibk];jp++ )
                 {
                     ip= icp[ibk][jp][ic];
                     if( imrk[ip] == ifree )
                    {
                        imrk[ip]= inew;
                    }
                 }
              }
               if( olds && ( news || avls ) )
              {
                  iq= icq[ibk][0][ic];
                  cmrk[iq]= iold;
              }
           }
        }
     }
  }
