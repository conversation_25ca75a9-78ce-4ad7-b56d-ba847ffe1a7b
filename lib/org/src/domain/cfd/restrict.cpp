   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::mg_restrict()
  {
//      Int          iqs,iqe;
//      Int          iq,jq,iv;
//      cFdDomain *cd= (cFdDomain*)crs;
//
//      gtrhs( rhs );
////    cout << "cFdDomain::restrict from "<<ilev<<" to "<<cd->ilev<<"\n";
//      setv( 0,cd->nq, nv+1,ZERO,cd->wrkq );
//      setv( 0,cd->nq, nv  ,ZERO,cd->mgrhs );
//
//      dof->range( dev->getrank(), &iqs,&iqe );
//      for( iv=0;iv<nv;iv++ )
//     {
//         for( iq=iqs;iq<iqe;iq++ )
//        {
//            jq= iqcrs[iq];
//            cd->wrkq[iv][jq]+= wq[0][iq]*q[iv][iq];
//            cd->mgrhs[iv][jq]+= ( rhs[iv][iq] +mgrhs[iv][iq] );
//        }
//     }
////      cd->debugf( nv,cd->mgrhs,"mgrhs.restricted" );
//      for( iq=iqs;iq<iqe;iq++ )
//     {
//         jq= iqcrs[iq];
//         cd->wrkq[nv][jq]+= wq[0][iq];
//     }
//      for( iv=0;iv<nv;iv++ )
//     {
//         for( jq=0;jq<cd->nq;jq++ )
//        {
//            cd->wrkq[iv][jq]/= cd->wrkq[nv][jq];
//            cd->q[iv][jq]= cd->wrkq[iv][jq];
//        } 
//     }
////      cd->debugf( nv+1,cd->wrkq,"wrkq.restricted" );
////      debugf( nv,dq,"dq.restrict" );
//
//      cd->cfl= cfl;
//      cd->gtrhs( cd->rhs );
//      for( iq=0;iq<cd->nq;iq++ )
//     {
//         for( iv=0;iv<nv;iv++ )
//        {
//            cd->mgrhs[iv][iq]-= cd->rhs[iv][iq];
//        }
//     }
////      cd->debugf( nv,cd->mgrhs,"mgrhs.reduced" );
//      
  }
