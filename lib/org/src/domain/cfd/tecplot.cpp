   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::tecplot()
  {
//     Int iv, iek, jp, ie,ip, iq, ix;
//     Int ig, ick, ib;
//     Int ip0, ip1, ip2, ip3;
//     Real *tmpxp[3];
//
////output
//    string sdum;
//    if(unst)
//   {
//       Int tci;
//       tci = tm/dtm;
//       if(tci<10)                                sdum = "00000" + strc(tci);
//       else if(tci<100     &&  tci>=10)          sdum = "0000" + strc(tci);
//       else if(tci<1000    &&  tci>=100)         sdum = "000" + strc(tci);
//       else if(tci<10000   &&  tci>=1000)        sdum = "00" + strc(tci);
//       else if(tci<100000  &&  tci>=10000)       sdum = "0" + strc(tci);
//       else if(tci<1000000 &&  tci>=100000)      sdum = strc(tci);
//   }
//    else
//   {
//       sdum = "0";
//   }
//
//    string fname;
//    //fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+".cell.tec";
//    fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+"." + sdum +".cell.tec";
//    cout << "save tecplot files to " << fname << "\n";
//
//    for(ix=0; ix<nx; ix++)
//   {
//      tmpxp[ix] = new Real [np];
//   }
//    for(ix=0; ix<nx; ix++)
//   {
//      for(ip=0; ip<np; ip++)
//     {
//        tmpxp[ix][ip] = xp[ix][ip];
//     }
//   }
//   cout << "tm " << tm << " omega " << omega << " " << 2*tm*omega*180/pi2 <<"\n";
//   //coo->toffset( 0,np, 1, (tm-dtm)*omega, tmpxp );
//   //coo->toffset( 0,nq, 1, (tm-dtm)*omega, q );
//
//    ofstream fle( fname.c_str() );
//    fle.setf(ios_base::scientific);
//    fle.precision(15);
//
//    Int *itmp, *itmp1, *itmp2;
//    itmp= new Int[np];
//    itmp1= new Int[np];
//    itmp2= new Int[np];
//    if( nx == 3 )
//   {
//       fle << "VARIABLES = ";
//       for( ix=0;ix<nx;ix++ )
//      {
//          fle << "\"X"<<strc(ix)<<"\" ";
//      } 
//       for( iv=0;iv<nv;iv++ )
//      {
//          fle << "\"V"<<strc(iv)<<"\" ";
//      }
//       for( iv=0;iv<naux;iv++ )
//      {
//          fle << "\"AUX"<<strc(iv)<<"\" ";
//      }
//       fle << "\n";
//       fle << "ZONE" << " " << "NODES=" << np << " " << "ELEMENTS=" << nq << " " << "DATAPACKING=BLOCK" << " " << "ZONETYPE=FEBRICK";
//       fle << " VARLOCATION=([";
//       for( iv=0;iv<nv;iv++ )
//      {
//          fle << nx+iv+1<<",";
//      } 
//       for( iv=0;iv<naux-1;iv++ )
//      {
//          fle << nx+nv+iv+1<<",";
//      }
//       fle << nx+nv+naux<<"]=CELLCENTERED)\n";
//       //Coordinates
//       for( ix=0;ix<nx;ix++ )
//      {
//          for(ip=0; ip<np; ip++)
//         {
//             //fle << xp[ix][ip] << "\n";
//             fle << tmpxp[ix][ip] << "\n";
//         }
//      }
//       //Flow variables
//       for( iv=0;iv<nv;iv++ )
//      {
//          for(iek=0; iek<nek; iek++)
//         {
//            if(ne[iek]>0)
//           {
//               for(ie=0; ie<ne[iek]; ie++)
//              {
//                  iq = ieq[iek][0][ie];
//                  fle << q[iv][iq] << "\n";
//              }
//           }
//         }
//      }
//       for( iv=0;iv<naux;iv++ )
//      {
//          for(iek=0; iek<nek; iek++)
//         {
//            if(ne[iek]>0)
//           {
//               for(ie=0; ie<ne[iek]; ie++)
//              {
//                  iq = ieq[iek][0][ie];
//                  fle << aux[iv][iq] << "\n";
//              }
//           }
//         }
//      }
//       //Connectivity
//       for(iek=0; iek<nek; iek++)
//      {
//          if(ne[iek]>0)
//         {
//             for(ie=0; ie<ne[iek]; ie++)
//            {
//                if(nep[iek]==8)
//               {
//                   for(jp=0; jp<8; jp++)
//                  {
//                      ip = iep[iek][jp][ie];
//                      ip=ip+1;
//                      fle << ip << " ";
//                  }
//                   fle << "\n";
//               }
//                else if(nep[iek]==4)
//               {
//                   for(jp=0; jp<3; jp++)
//                  {
//                      ip = iep[iek][jp][ie];
//                      ip=ip+1;
//                      fle << ip << " ";
//                  }
//                   fle << ip << " ";
//                   ip = iep[iek][3][ie];
//                   ip=ip+1;
//                   for(jp=4; jp<8; jp++)
//                  {
//                      fle << ip << " ";
//                  }
//                   fle << "\n";
//               }
//                else if(nep[iek]==5)
//               {
//                   for(jp=0; jp<4; jp++)
//                  {
//                      ip = iep[iek][jp][ie];
//                      ip=ip+1;
//                      fle << ip << " ";
//                  }
//                   fle << ip << " ";
//                   ip = iep[iek][4][ie];
//                   ip=ip+1;
//                   for(jp=5; jp<8; jp++)
//                  {
//                      fle << ip << " ";
//                  }
//                   fle << "\n";
//               }
//                else if(nep[iek]==6)
//               {
//                   for(jp=0; jp<3; jp++)
//                  {
//                      ip = iep[iek][jp][ie];
//                      ip=ip+1;
//                      fle << ip << " ";
//                  }
//                   fle << ip << " ";
//                   for(jp=3; jp<6; jp++)
//                  {
//                      ip = iep[iek][jp][ie];
//                      ip=ip+1;
//                      fle << ip << " ";
//                  }
//                   fle << ip << " ";
//                   fle << "\n";
//               }
//                else
//               {
//                   cout<<"Error in cell2tec - no treatment yet for element type "<<iek<<" nep[iek]="<<nep[iek]<<endl;
//                   exit(0);
//               }
//            }
//         }
//      }
//   }
//    else
//   {
//       fle << "VARIABLES = ";
//       for( ix=0;ix<nx;ix++ )
//      {
//          fle << "\"X"<<strc(ix)<<"\" ";
//      } 
//       for( iv=0;iv<nv;iv++ )
//      {
//          fle << "\"V"<<strc(iv)<<"\" ";
//      }
//       for( iv=0;iv<naux;iv++ )
//      {
//          fle << "\"AUX"<<strc(iv)<<"\" ";
//      }
//       fle << "\n";
//       fle << "ZONE" << " " << "NODES=" << np << " " << "ELEMENTS=" << nq << " " << "DATAPACKING=BLOCK" << " " << "ZONETYPE=FEQUADRILATERAL";
//       fle << " VARLOCATION=([";
//       for( iv=0;iv<nv;iv++ )
//      {
//          fle << nx+iv+1<<",";
//      } 
//       for( iv=0;iv<naux-1;iv++ )
//      {
//          fle << nx+nv+iv+1<<",";
//      }
//       fle << nx+nv+naux<<"]=CELLCENTERED)\n";
//       //Coordinates
//       for( ix=0;ix<nx;ix++ )
//      {
//          for(ip=0; ip<np; ip++)
//         {
//             //fle << xp[ix][ip] << "\n";
//             fle << tmpxp[ix][ip] << "\n";
//         }
//      }
//       //Flow variables
//       for( iv=0;iv<nv;iv++ )
//      {
//          for(iek=0; iek<nek; iek++)
//         {
//            if(ne[iek]>0)
//           {
//               for(ie=0; ie<ne[iek]; ie++)
//              {
//                  iq = ieq[iek][0][ie];
//                  fle << q[iv][iq] << "\n";
//              }
//           }
//         }
//      }
//       //auxiliary variables
//       for( iv=0;iv<naux;iv++ )
//      {
//          for(iek=0; iek<nek; iek++)
//         {
//            if(ne[iek]>0)
//           {
//               for(ie=0; ie<ne[iek]; ie++)
//              {
//                  iq = ieq[iek][0][ie];
//                  fle << aux[iv][iq] << "\n";
//              }
//           }
//         }
//      }
//       //Connectivity
//       for(iek=0; iek<nek; iek++)
//      {
//          if(ne[iek]>0)
//         {
//             for(ie=0; ie<ne[iek]; ie++)
//            {
//                for(jp=0; jp<nep[iek]; jp++)
//               {
//                   ip = iep[iek][jp][ie];
//                   ip=ip+1;
//                   fle << ip << " ";
//               }
//                if(nep[iek]==3){fle << ip;}
//                fle << "\n";
//            }
//         }
//      }
//  }
//     fle.close();
//
//    if(nfre>0)
//   {
//      Int ifre, jx;
//      //fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+".cell.tec";
//      fname= dev->getcpath()+"/"+dev->getname()+"."+strc(dev->getrank())+"." + sdum +".ds.tec";
//      cout << "save tecplot files to " << fname << "\n";
//
//      Real *tau[3][3];
//      for(ix=0; ix<nx; ix++)
//     {
//        for(Int jx=0; jx<nx; jx++)
//       {
//          tau[ix][jx] = new Real [nq];
//          for(iq=0; iq<nq; iq++)
//         {
//            tau[ix][jx][iq] = 0;
//         }
//       }
//     }
//
//      for( Int i=0; i<nfre; i++ )
//     {
//         zc_re[i]=    new Real*[nv];
//         zc_im[i]=    new Real*[nv];
//         szc_re[i] = new Real [nv*nq];
//         szc_im[i] = new Real [nv*nq];
//         subv( nv,  nq, szc_re[i],   zc_re[i] );    setv( 0,nq,    nv, ZERO,    zc_re[i] );
//         subv( nv,  nq, szc_im[i],   zc_im[i] );    setv( 0,nq,    nv, ZERO,    zc_im[i] );
//     }
//
//      for(ifre=0; ifre<nfre; ifre++)
//     {
//        fld->cnsv_z( 0,nq, q, z_re[ifre], zc_re[ifre] );
//        fld->cnsv_z( 0,nq, q, z_im[ifre], zc_im[ifre] );
//     }
//
//      for(iq=0; iq<nq; iq++)
//     {
//        for(ix=0; ix<nx; ix++)
//       {
//          for(jx=0; jx<nx; jx++)
//         {
//            for(ifre=0; ifre<nfre; ifre++)
//           {
//              tau[ix][jx][iq] += 2*(z_re[ifre][ix][iq]*zc_re[ifre][jx+1][iq] + z_im[ifre][ix][iq]*zc_im[ifre][jx+1][iq]);
//           }
//         }
//       }
//     }
//
//      fle.open(fname.c_str());
//      fle.setf(ios_base::scientific);
//      fle.precision(15);
//      if( nx == 3 )
//     {
//         fle << "VARIABLES = ";
//         for( ix=0;ix<nx;ix++ )
//        {
//            fle << "\"X"<<strc(ix)<<"\" ";
//        } 
//         for( iv=0;iv<nx*nx;iv++ )
//        {
//            fle << "\"V"<<strc(iv)<<"\" ";
//        }
//         fle << "\n";
//         fle << "ZONE" << " " << "NODES=" << np << " " << "ELEMENTS=" << nq << " " << "DATAPACKING=BLOCK" << " " << "ZONETYPE=FEBRICK";
//         fle << " VARLOCATION=([";
//         for( iv=0;iv<nx*nx;iv++ )
//        {
//            fle << nx+iv+1<<",";
//        } 
//         fle << nx+nx*nx+naux<<"]=CELLCENTERED)\n";
//         //Coordinates
//         for( ix=0;ix<nx;ix++ )
//        {
//            for(ip=0; ip<np; ip++)
//           {
//               //fle << xp[ix][ip] << "\n";
//               fle << tmpxp[ix][ip] << "\n";
//           }
//        }
//         //Flow variables
//         for(ix=0; ix<nx; ix++)
//        {
//           for(jx=0; jx<nx; jx++)
//          {
//             for(iek=0; iek<nek; iek++)
//            {
//               if(ne[iek]>0)
//              {
//                  for(ie=0; ie<ne[iek]; ie++)
//                 {
//                     iq = ieq[iek][0][ie];
//                     fle << tau[ix][jx][iq] << "\n";
//                 }
//              }
//            }
//          }
//        }
//         //Connectivity
//         for(iek=0; iek<nek; iek++)
//        {
//            if(ne[iek]>0)
//           {
//               for(ie=0; ie<ne[iek]; ie++)
//              {
//                  if(nep[iek]==8)
//                 {
//                     for(jp=0; jp<8; jp++)
//                    {
//                        ip = iep[iek][jp][ie];
//                        ip=ip+1;
//                        fle << ip << " ";
//                    }
//                     fle << "\n";
//                 }
//                  else if(nep[iek]==4)
//                 {
//                     for(jp=0; jp<3; jp++)
//                    {
//                        ip = iep[iek][jp][ie];
//                        ip=ip+1;
//                        fle << ip << " ";
//                    }
//                     fle << ip << " ";
//                     ip = iep[iek][3][ie];
//                     ip=ip+1;
//                     for(jp=4; jp<8; jp++)
//                    {
//                        fle << ip << " ";
//                    }
//                     fle << "\n";
//                 }
//                  else if(nep[iek]==5)
//                 {
//                     for(jp=0; jp<4; jp++)
//                    {
//                        ip = iep[iek][jp][ie];
//                        ip=ip+1;
//                        fle << ip << " ";
//                    }
//                     fle << ip << " ";
//                     ip = iep[iek][4][ie];
//                     ip=ip+1;
//                     for(jp=5; jp<8; jp++)
//                    {
//                        fle << ip << " ";
//                    }
//                     fle << "\n";
//                 }
//                  else if(nep[iek]==6)
//                 {
//                     for(jp=0; jp<3; jp++)
//                    {
//                        ip = iep[iek][jp][ie];
//                        ip=ip+1;
//                        fle << ip << " ";
//                    }
//                     fle << ip << " ";
//                     for(jp=3; jp<6; jp++)
//                    {
//                        ip = iep[iek][jp][ie];
//                        ip=ip+1;
//                        fle << ip << " ";
//                    }
//                     fle << ip << " ";
//                     fle << "\n";
//                 }
//                  else
//                 {
//                     cout<<"Error in cell2tec - no treatment yet for element type "<<iek<<" nep[iek]="<<nep[iek]<<endl;
//                     exit(0);
//                 }
//              }
//           }
//        }
//     }
//      else
//     {
//         fle << "VARIABLES = ";
//         for( ix=0;ix<nx;ix++ )
//        {
//            fle << "\"X"<<strc(ix)<<"\" ";
//        } 
//         for( iv=0;iv<nx*nx;iv++ )
//        {
//            fle << "\"V"<<strc(iv)<<"\" ";
//        }
//         fle << "\n";
//         fle << "ZONE" << " " << "NODES=" << np << " " << "ELEMENTS=" << nq << " " << "DATAPACKING=BLOCK" << " " << "ZONETYPE=FEQUADRILATERAL";
//         fle << " VARLOCATION=([";
//         for( iv=0;iv<nx*nx;iv++ )
//        {
//            fle << nx+iv+1<<",";
//        } 
//         fle << nx+nx*nx+naux<<"]=CELLCENTERED)\n";
//         //Coordinates
//         for( ix=0;ix<nx;ix++ )
//        {
//            for(ip=0; ip<np; ip++)
//           {
//               //fle << xp[ix][ip] << "\n";
//               fle << tmpxp[ix][ip] << "\n";
//           }
//        }
//         //Flow variables
//         for(ix=0; ix<nx; ix++)
//        {
//           for(jx=0; jx<nx; jx++)
//          {
//             for(iek=0; iek<nek; iek++)
//            {
//               if(ne[iek]>0)
//              {
//                  for(ie=0; ie<ne[iek]; ie++)
//                 {
//                     iq = ieq[iek][0][ie];
//                     fle << tau[ix][jx][iq] << "\n";
//                 }
//              }
//            }
//          }
//        }
//
//         //Connectivity
//         for(iek=0; iek<nek; iek++)
//        {
//            if(ne[iek]>0)
//           {
//               for(ie=0; ie<ne[iek]; ie++)
//              {
//                  for(jp=0; jp<nep[iek]; jp++)
//                 {
//                     ip = iep[iek][jp][ie];
//                     ip=ip+1;
//                     fle << ip << " ";
//                 }
//                  if(nep[iek]==3){fle << ip;}
//                  fle << "\n";
//              }
//           }
//        }
//     }
//       fle.close();
//
//      for(ix=0; ix<nx; ix++)
//     {
//        for(Int jx=0; jx<nx; jx++)
//       {
//          delete[] tau[ix][jx]; tau[ix][jx]=NULL;
//       }
//     }
//   }
//
//    delete[] itmp; itmp=NULL;
//    delete[] itmp1; itmp1=NULL;
//    delete[] itmp2; itmp2=NULL;
//
//    for(ix=0; ix<nx; ix++)
//   {
//      delete[] tmpxp[ix];
//      tmpxp[ix]=NULL;
//   }
  }

