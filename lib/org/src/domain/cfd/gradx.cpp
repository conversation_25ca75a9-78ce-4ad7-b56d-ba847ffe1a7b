   using namespace std;

#  include <domain/cfd/domain.h>


   void cFdDomain::gradx0( Int ics, Int ice, Int *icql, Real *xql[], Real *dxdxl[], 
                                             Int *icqr, Real *xqr[], Real *dxdxr[] )
  {
      Real      *wrk[1];
      Int        ix,jx,iv,ic,iql,iqr;
      Real       d,w;

      if( ice > ics )
     {

         wrk[0]= new Real[ice];
         setv( ics,ice, 1,0., wrk );

         for( ix=0;ix<nx;ix++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iql= ic;
               iqr= ic;
               if( icql ){ iql= icql[ic]; }
               if( icqr ){ iqr= icqr[ic]; }
               d= xqr[ix][iqr]- xql[ix][iql];
               d*= d;
               wrk[0][ic]+= d;
           } 
        }
      
         for( ic=ics;ic<ice;ic++ )
        {
            wrk[0][ic]= 1./wrk[0][ic];
        }
      
         for( ix=0;ix<nx;ix++ )
        {
            for( jx=0;jx<nx;jx++ )
           {
               for( ic=ics;ic<ice;ic++ )
              {
                  iql= ic;
                  iqr= ic;
                  if( icql ){ iql= icql[ic]; }
                  if( icqr ){ iqr= icqr[ic]; }
                  w= wrk[0][ic];
                  d= w*(xqr[ix][iqr]-xql[ix][iql])*( xqr[jx][iqr]-xql[jx][iql] );
                  dxdxl[ijdx[ix][jx]][iql]+= d;
                  dxdxr[ijdx[ix][jx]][iqr]+= d;
              }
           }
        }
         delete[] wrk[0];
     }
  }
