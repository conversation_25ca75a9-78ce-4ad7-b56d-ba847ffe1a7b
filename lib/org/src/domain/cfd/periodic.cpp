   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::assgnprq()
  {
      Int          iq,jq,ibk,ib,jp,jb;
      Int         *ihlp;
      cDomain::assgnprq();

// should only work for nbq=1 or if all faces have the same number of degrees of freedom

//    cout << "PERIODIC DOFS INSIDE CFDDOMAIN::ASSGNPRQ\n";
      ihlp= new Int[nq]; setv( (Int)0,nq, (Int)-1,ihlp ); 
      for( jq=0;jq<nprq;jq++ )
     {
         iq= iprq[0][jq]; 
         ihlp[iq]= jq;
//       cout << iq <<" "<<iprq[0][jq]<<" "<<iprq[1][jq]<<"\n";
     }
//    cout << "END DOFS INSIDE CFDDOMAIN::ASSGNPRQ\n";

//    cout << "IPRC\n";
      for( ibk=0;ibk<nbk;ibk++ )
     {
//       cout << nprb[ibk]<<"\n";
         siprc[ibk]= new Int[nbq[ibk]*nprb[ibk]];
         iprc[ibk]= new Int*[nbq[ibk]]; subv( nbq[ibk],nprb[ibk], siprc[ibk],iprc[ibk] );

         for( jq=0;jq<nbq[ibk];jq++ )
        {
            for( ib=0;ib<nprb[ibk];ib++ )
           {
               iq= ibq[ipr0][ibk][jq][ib];
               iprc[ibk][jq][ib]= ihlp[iq];
//             cout << ibk<<"-face "<<ib<<" " <<iprc[ibk][jq][ib]<<"\n";
           }
        }

         siprdp[ibk]= new Int[nbp[ibk]*nprb[ibk]];
         iprdp[ibk]= new Int*[nbp[ibk]]; subv( nbp[ibk],nprb[ibk], siprdp[ibk],iprdp[ibk] );
         
         for( jp=0;jp<nbp[ibk];jp++ )
        {
            for( ib=0;ib<nprb[ibk];ib++ )
           {
//             jb= iprc[ibk][0][ib];
               iprdp[ibk][jp][ib]= ibp[ipr0][ibk][jp][ib];
           }
        }

//       cout << "PERIODIC FACES\n";
         for( ib=0;ib<nprb[ibk];ib++ )
        {
            for( jp=0;jp<nbp[ibk];jp++ )
           {
//             cout << iprdp[ibk][jp][ib] << " ";
           }
            jq= iprc[ibk][0][ib];
//          cout << " "<<jq<<" "<<iprq[0][jq]<<" "<<iprq[1][jq]<<"\n";
        }
//       cout << "PERIODIC FACES END\n";

     }
      delete[] ihlp;
  }
 
