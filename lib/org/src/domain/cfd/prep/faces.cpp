   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::faces()
  {
      Int      ibk,iek;
      Int      jc,ic,ie,kc,id,iq,jq,ip,jp,jf;
      Int      mc[MxNSk];
      Int   ***iec[MxNSk];
      Int     *idx[MxNSk],*jdx[MxNSk];
      cDgraph *dg;


      for( ibk=0;ibk<nbk;ibk++ )
     {
         iec[ibk]= new Int**[nek];
         for( iek=0;iek<nek;iek++ )
        {
            iec[ibk][iek]= new Int*[nce[ibk][iek]];
            for( jc=0;jc<nce[ibk][iek];jc++ )
           {
               iec[ibk][iek][jc]= new Int[ne[iek]];
           }
        }
     }
      setv( (Int)0,nbk, (Int)0,mc );

      for( ibk=0;ibk<nbk;ibk++ )
     {

         dg= new cDgraph( np,nbp[ibk], nek,ne,nce[ibk],icep[ibk] );
         dg->build( iep );
         dg->pack( mc+ibk, iec[ibk] );
         delete dg;
     }

      jf= 0;
      nfc= 0;
      for( ibk=0;ibk<nbk;ibk++ )
     {

         idx[ibk]= new Int[mc[ibk]];
         jdx[ibk]= new Int[mc[ibk]];
         setv( (Int)0,mc[ibk], (Int)-1,idx[ibk] );
         setv( (Int)0,mc[ibk], (Int) 0,jdx[ibk] );
         for( iek=0; iek<nek;iek++ )
        {
            for( jc=0;jc<nce[ibk][iek];jc++ )
           {
               for( ie=0;ie<ne[iek];ie++ ) 
              {
                  ic= iec[ibk][iek][jc][ie];
                  jdx[ibk][ic]++;
              }
           } 
        }
         ic=0;
         for( jc=0;jc<mc[ibk];jc++ )
        {
            if( jdx[ibk][jc] == 2 )
           {
               idx[ibk][jc]= ic;
               ic++;
           }
        }
         nc[ibk]= ic;
         nfc+= nc[ibk];

         sicc[ibk]= new Int[nc[ibk]]; 
         icc[ibk]= new Int*[1];

         sicp[ibk]= new Int[nbp[ibk]*nc[ibk]];
         icp[ibk]= new Int*[nbp[ibk]];

         subv( nbp[ibk],nc[ibk], sicp[ibk],icp[ibk] );
         subv(        1,nc[ibk], sicc[ibk],icc[ibk] );

         setv( (Int)0,mc[ibk], (Int) 0,jdx[ibk] );
         for( iek=0;iek<nek;iek++ )
        {
            for( jc=0;jc<nce[ibk][iek];jc++ )
           {
               for( ie=0;ie<ne[iek];ie++ ) 
              {
                  kc= iec[ibk][iek][jc][ie];
                  ic= idx[ibk][kc];
                  if( ic != -1 )
                 {
                     jq= jdx[ibk][kc];
                     jdx[ibk][kc]++;
                     if( jq == 0 )
                    {
                        icc[ibk][0][ic]= jf++;
                        for( jp=0;jp<nbp[ibk];jp++ )
                       {
                           ip= icep[ibk][iek][jp][jc];
                           ip= iep[iek][ip][ie];
                           icp[ibk][jp][ic]= ip;
                       }
                    }
                 }
              }
           } 
        }
     }
 
      cout << "total number of connections "<<nfc<<"\n";

      sifq= new Int[2*nfc];
      ifq.subv( 2,nfc, sifq );


      for( ibk=0;ibk<nbk;ibk++ )
     {

        sicq[ibk]= new Int[2*nc[ibk]];
        icq[ibk]= new Int*[2];
        subv( 2,nc[ibk], sicq[ibk],icq[ibk] );

         setv( (Int)0,mc[ibk], (Int) 0,jdx[ibk] );
         for( iek=0;iek<nek;iek++ )
        {
            for( jc=0;jc<nce[ibk][iek];jc++ )
           {
               for( ie=0;ie<ne[iek];ie++ ) 
              {
                  kc= iec[ibk][iek][jc][ie];
                  ic= idx[ibk][kc];
                  if( ic != -1 )
                 {
                     jq= jdx[ibk][kc];
                     jdx[ibk][kc]++;
                     icq[ibk][jq][ic]= ieq[iek][0][ie];
                     ic= icc[ibk][0][ic];
                     ifq(jq,ic)= ieq[iek][0][ie];
                 }
              }
           } 
        }
     }

      for( ibk=0;ibk<nbk;ibk++ )
     {
         for( ic=0;ic<nc[ibk];ic++ )
        {

            jc= icc[ibk][0][ic];
//          cout << ic << " "<<jc <<" ";
            for( jp=0;jp<nbp[ibk];jp++ )
           {
//             cout << icp[ibk][jp][ic]<<" ";
           }
//          cout << ifq[0][jc]<<" "<<ifq[1][jc]<<"\n";
       
        }
     }

        // string fnme;
        // ofstream fle;

        // fnme= dev->getcpath();
        // fnme= fnme+"/"+dev->getname()+".0.ifq";
        // fle.open(fnme.c_str());
        // fle << nfc << "\n";
        // for(ic=0; ic<nfc; ic++)
        //{
        //   fle << ifq(0,ic) << " " << ifq(1,ic) << "\n";
        //}
        // fle.close();

        // //save ieq for postprocess
        // fnme= dev->getcpath();
        // fnme= fnme+"/"+dev->getname()+"."+strc(level())+".e2q";
        // fle.open(fnme.c_str());
        // for(Int iek=0; iek<6; iek++)
        //{
        //   if(ne[iek]>0)
        //  {
        //      for(Int ie=0; ie<ne[iek]; ie++)
        //     {
        //         iq = ieq[iek][0][ie];
        //         fle << iek << " " << ie << " " << iq << "\n";
        //     }
        //  }
        //}
        // fle.close();

        // fnme= dev->getcpath();
        // fnme= fnme+"/"+dev->getname()+"."+strc(level())+".prq";
        // fle.open(fnme.c_str());
        // fle << nprq << "\n";
        // for(iq=0; iq<nprq; iq++)
        //{
        //   fle << iprq[0][iq] << " " << iprq[1][iq] << "\n";
        //}
        // fle.close();

        // //save iqb for postprocess
        // fnme= dev->getcpath();
        // fnme= fnme+"/"+dev->getname()+"."+strc(level())+".iqb";
        // fle.open(fnme.c_str());
        // for(Int ig=0; ig<ng; ig++)
        //{
        //   fle << nbb[ig] << "\n";
        //   for(Int ib=0; ib<nbb[ig]; ib++)
        //  {
        //       fle << iqb[ig][0][ib] << "\n";
        //  }
        //}
        // fle.close();


      for( ibk=0;ibk<nbk;ibk++ )
     {
         for( iek=0;iek<nek;iek++ )
        {
            for( jc=0;jc<nce[ibk][iek];jc++ )
           {
               delete[] iec[ibk][iek][jc]; iec[ibk][iek][jc]= NULL;
           }
            delete[] iec[ibk][iek]; iec[ibk][iek]= NULL;
        }
         delete[] iec[ibk]; iec[ibk]= NULL;
         delete[] idx[ibk];
         delete[] jdx[ibk];
     }

  }
