   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::loadpre()
  {
      size_t len;
      pickle_t buf;

      //cout << dev->getname() << "call loadpre============================\n";
      Int        iv,ig,iek,ibk;
      Int        icpu;

      ofstream   lgf;
      string     path,fnme;

      icpu= dev->getrank();
      path= dev->getcpath();

      fnme= path+"/preprocessor."+strc( icpu )+"."+strc( ilev );
      FILE *f=fopen( fnme.c_str(),"r" );
      Int *tmpe=NULL,*tmpb=NULL; 
     
      len=0;
      buf=NULL;
      fread( (void*)&len,  1,sizeof( len), f ); buf=new pickle_v[ len*sizeof(*buf) ];
      fread( (void*) buf,len,sizeof(*buf), f );

      len=0;
      unpickle( &len,buf );
      delete[] buf;buf=NULL;len=0;


      pts->read( nx,&sxp,f ); xp.subv( nx,np, sxp );
      pts->create( nvel,&sxdp ); xdp.subv( nvel,np, sxdp ); setv( 0,np, nvel, ZERO, xdp, "h" );
      dof->read( nx,&sxq,f ); xq.subv( nx,nq, sxq );
      if( vsc->viscous() )
     {
         dof->read( 2,&sdst,f ); dst.subv( 2,nq, sdst );  
     }
     // q=    new Real*[nv];
     // dq=    new Real*[nv];
     // for( Int i=0;i<ntlv;i++ )
     //{
     //    u[i]=    new Real*[nv];
     //}
     // rhs=    new Real*[nv];
     // res=    new Real*[nv];
     // aux=    new Real*[naux];
     // daux=    new Real*[nv];
     // lhsa= new Real*[nlhs];
     // wrkq= new Real*[nv+1];
     // mgrhs= new Real*[nv];
     // dxdx= new Real*[nx*nx];

      //sdqdx= new Real*[nv];
      //dqdx= new Real**[nv];

      dof->create( nv,  &sq  );   q.subv( nv,  nq, sq );   setv( 0,nq,    nv, ZERO,    q, "h" );
      dof->create( nv,  &sdq  );  dq.subv( nv,  nq, sdq ); setv( 0,nq,    nv, ZERO,    dq, "h" );
      for( Int i=0;i<ntlv;i++ )
     {
         dof->create( nv,  &(su[i]) );   u[i].subv( nv,nq,su[i] ); setv( 0,nq,nv, ZERO,u[i], "h" );
     }
      dof->create( nv,  &srhs  );   rhs.subv( nv,  nq, srhs );  setv( 0,nq,    nv, ZERO,    rhs, "h" );
      dof->create( nv,  &sres  );   res.subv( nv,  nq, sres );  setv( 0,nq,    nv, ZERO,    res, "h" );
      dof->create( nv,  &sdaux  );  daux.subv( nv,  nq, sdaux);  setv( 0,nq,    nv, ZERO,    daux, "h" );
      dof->create( naux,  &saux  ); aux.subv( naux,  nq, saux ); setv( 0,nq,    naux, ZERO,    aux, "h" );
      dof->create( nlhs,&slhsa ); lhsa.subv( nlhs,nq, slhsa);   setv( 0,nq,  nlhs, ZERO, lhsa, "h" );
      dof->create( nx+1,&swq );   wq.subv( nx+1,nq, swq );  setv( 0,nq,(nx+1), ZERO,   wq, "h" );
      dof->create( nvel,&sxdq );  xdq.subv( nvel,nq, sxdq ); setv( 0,nq,nvel, ZERO,   xdq, "h" );
      dof->create( nv+1,  &swrkq  );  wrkq.subv( nv+1,  nq, swrkq ); setv( 0,nq,    nv+1, ZERO,    wrkq, "h" );
      dof->create( nv,  &smgrhs  );   mgrhs.subv( nv,  nq, smgrhs ); setv( 0,nq,    nv, ZERO,    mgrhs, "h" );
      for( Int i=0; i<nfre; i++ )
     {
         //z_re[i]=    new Real*[nv];
         //z_im[i]=    new Real*[nv];
         dof->create( nv,  &(sz_re[i])  );   z_re[i].subv( nv,  nq, sz_re[i] ); setv( 0,nq,    nv, ZERO,    z_re[i], "h" );
         dof->create( nv,  &(sz_im[i])  );   z_im[i].subv( nv,  nq, sz_im[i] ); setv( 0,nq,    nv, ZERO,    z_im[i], "h" );

         //zc_re[i]=    new Real*[nv];
         //zc_im[i]=    new Real*[nv];
         dof->create( nv,  &(szc_re[i])  );   zc_re[i].subv( nv,  nq, szc_re[i] ); setv( 0,nq,    nv, ZERO,    zc_re[i], "h" );
         dof->create( nv,  &(szc_im[i])  );   zc_im[i].subv( nv,  nq, szc_im[i] ); setv( 0,nq,    nv, ZERO,    zc_im[i], "h" );
     }

      //rhsz_re=    new Real*[nv];
      //rhsz_im=    new Real*[nv];
      dof->create( nv,  &(srhsz_re)  );   rhsz_re.subv( nv,  nq, srhsz_re ); setv( 0,nq,    nv, ZERO,    rhsz_re, "h" );
      dof->create( nv,  &(srhsz_im)  );   rhsz_im.subv( nv,  nq, srhsz_im ); setv( 0,nq,    nv, ZERO,    rhsz_im, "h" );

      sdzdx_re= new Real*[nv];
      //dzdx_re= new Real**[nv];
      sdzdx_im= new Real*[nv];
      //dzdx_im= new Real**[nv];



      //auxz_re= new Real*[nv];
      //auxz_im= new Real*[nv];
      dof->create( nv,  &(sauxz_re)  );   auxz_re.subv( nv,  nq, sauxz_re ); setv( 0,nq,    nv, ZERO,    auxz_re, "h" );
      dof->create( nv,  &(sauxz_im)  );   auxz_im.subv( nv,  nq, sauxz_im ); setv( 0,nq,    nv, ZERO,    auxz_im, "h" );

      //zcold_re= new Real*[nv];
      //zcold_im= new Real*[nv];
      dof->create( nv,  &(szcold_re)  );  zcold_re.subv( nv,  nq, szcold_re ); setv( 0,nq,    nv, ZERO,    zcold_re, "h" );
      dof->create( nv,  &(szcold_im)  );  zcold_im.subv( nv,  nq, szcold_im ); setv( 0,nq,    nv, ZERO,    zcold_im, "h" );

// gradient arrays
      //sdqdx= new Real*[nv];
      sdqdx = new Real[nv*nx*nq];
      //dqdx= new Real**[nv];
      dof->create( nx*nx,&sdxdx  );   dxdx.subv_3d( nx, nx,  nq, sdxdx ); setv_3d( 0,nq,    nx, nx, ZERO,    dxdx, "h" );
                                      dqdx.subv_3d( nv, nx,  nq, sdqdx ); setv_3d( 0,nq,    nv, nx, ZERO,    dqdx, "h" );
      for( iv=0;iv<nv;iv++ )
     {
         //dqdx[iv]= new Real*[nx]; sdqdx[iv]= NULL;
         //dof->create( nx, sdqdx+iv  ); subv( nx,  nq, sdqdx[iv], dqdx[iv] );  setv( 0,nq,    nx, ZERO, dqdx[iv] );

         //dzdx_re[iv]= new Real*[nx]; sdzdx_re[iv]= NULL;
         sdzdx_re[iv]= NULL;
         dof->create( nx, sdzdx_re+iv  ); dzdx_re[iv].subv( nx,  nq, sdzdx_re[iv] ); 

         //dzdx_im[iv]= new Real*[nx]; sdzdx_im[iv]= NULL;
         sdzdx_im[iv]= NULL;
         dof->create( nx, sdzdx_im+iv  ); dzdx_im[iv].subv( nx,  nq, sdzdx_im[iv] );
     }

      for ( iv=0;iv<nv;iv++ )
     {
         //register sdqdx address piece by piece
         dof->regaddr( nx, &(sdqdx[iv*nx*nq]) );

         //dqdx[iv]= new Real*[nx]; //sdqdx[iv]= NULL;
         //dqdx[iv].subv(nx, nq, &(sdqdx[iv*nx*nq]));
        // for( int ix=0;ix<nx;ix++ )
        //{
        //     dqdx[iv][ix]= sdqdx+ix*nq+iv*nq*nx;
        //}
        // setv( 0,nq,    nx, ZERO, dqdx[iv] );
     }

     {   Int ix,jx,i;
         i=0;
         for( ix=0;ix<nx;ix++ )
        {
            ijdx[ix]= new Int[nx];
            for( jx=0;jx<nx;jx++ )
           {
               ijdx[ix][jx]= i++;
           }
        }
     }


      for( iek=0;iek<nek;iek++ )
     {
         iep[iek]= new Int*[nep[iek]];
         ieq[iek]= new Int*[neq[iek]];
         iem[iek]= new Int*[       1];
         eld[iek]->read(        1,&(siem[iek]),f ); subv(        1,ne[iek], siem[iek],iem[iek] );
         eld[iek]->read( nep[iek],&(siep[iek]),f ); subv( nep[iek],ne[iek], siep[iek],iep[iek] ); iep_view[iek].subv( nep[iek],ne[iek], siep[iek] );
         eld[iek]->read( neq[iek],&(sieq[iek]),f ); subv( neq[iek],ne[iek], sieq[iek],ieq[iek] ); ieq_view[iek].subv( neq[iek],ne[iek], sieq[iek] );

         medium_marker[iek]= new Int*[1];
         eld[iek]->read( 1,&(smedium_marker[iek]),f ); subv( 1,ne[iek], smedium_marker[iek],medium_marker[iek] );
     }
      cnf->read( 2,&(sifq),f ); ifq.subv( 2,nfc, sifq );
      //auxf= new Real*[nauxf];
      cnf->create(nauxf,&(sauxf) ); auxf.subv(nauxf,nfc, sauxf ); setv( 0,nfc,nauxf, ZERO, auxf, "h" );
      cnf->create( nx+1,&( swnc) ); wnc.subv( nx+1,nfc,  swnc );  setv( 0,nfc, nx+1, ZERO,  wnc, "h" );
      cnf->create(   nx,&(  sxc) ); xc.subv(   nx,nfc,   sxc );   setv( 0,nfc,   nx, ZERO,   xc, "h" );
      cnf->create(    1,&(swxdc) ); wxdc.subv(    1,nfc, swxdc ); setv( 0,nfc,    1, ZERO, wxdc, "h" );
      for( ibk=0;ibk<nbk;ibk++ )
     {
         icp[ibk]= new Int*[nbp[ibk]];
         icc[ibk]= new Int*[       1];
         cnd[ibk]->read( nbp[ibk],&(sicp[ibk]), f ); subv( nbp[ibk],nc[ibk], sicp[ibk],icp[ibk] ); icp_view[ibk].subv( nbp[ibk],nc[ibk], sicp[ibk] );
         cnd[ibk]->read(        1,&(sicc[ibk]), f ); subv(        1,nc[ibk], sicc[ibk],icc[ibk] ); icc_view[ibk].subv(        1,nc[ibk], sicc[ibk] );
     }

      prd->read( 2,&(siprq),f ); subv( 2,nprq, siprq,iprq );
      prp->read( 2,&(siprp),f ); subv( 2,nprp, siprp,iprp );
      //xqprd= new Real*[nx];
      //qprd= new Real*[nv];
      //rhsprd= new Real*[nv];
      //resprd= new Real*[nv];
      //dqprd= new Real*[nv];
      //dauxprd= new Real*[nv];
      //auxprd= new Real*[naux];
      //lhsprd= new Real*[nlhs];
      prd->create( nx,&(sxqprd) ); xqprd.subv( nx,nprq, sxqprd );  setv( 0,nprq, nx, ZERO, xqprd, "h" );
      prd->create( nv,&(sqprd) );  qprd.subv( nv,nprq, sqprd );  setv( 0,nprq, nv, ZERO, qprd, "h" );
      prd->create( nv,&(srhsprd) ); rhsprd.subv( nv,nprq, srhsprd ); setv( 0,nprq, nv, ZERO, rhsprd, "h" );
      prd->create( nv,&(sresprd) ); resprd.subv( nv,nprq, sresprd ); setv( 0,nprq, nv, ZERO, resprd, "h" );
      prd->create( nv,&(sdauxprd) ); dauxprd.subv( nv,nprq, sdauxprd ); setv( 0,nprq, nv, ZERO, dauxprd, "h" );
      prd->create( nv,&(sdqprd) );   dqprd.subv( nv,nprq, sdqprd ); setv( 0,nprq, nv, ZERO, dqprd, "h" );
      prd->create( naux,&(sauxprd) ); auxprd.subv( naux,nprq, sauxprd ); setv( 0,nprq, naux, ZERO, auxprd, "h" );
      prd->create( nlhs,&(slhsprd) ); lhsprd.subv( nlhs,nprq, slhsprd ); setv( 0,nprq, nlhs, ZERO, lhsprd, "h" );
      prd->create( 1,&(siprq0) ); iprq0.subv_1d( nprq, siprq0 ); 
      prd->create( 1,&(siprq1) ); iprq1.subv_1d( nprq, siprq1 ); 

      for(Int i=0; i<nprq; i++) {iprq0(i)=iprq[0][i];}
      for(Int i=0; i<nprq; i++) {iprq1(i)=iprq[1][i];}

      for( ibk=0;ibk<nbk;ibk++ )
     {
         pld[ibk]->read( nbq[ibk],&( siprc[ibk]), f );
         pld[ibk]->read( nbp[ibk],&(siprdp[ibk]), f );
         iprc[ibk]= new  Int*[nbq[ibk]];
         iprdp[ibk]= new Int*[nbp[ibk]];
         subv( nbq[ibk],nprb[ibk],  siprc[ibk], iprc[ibk] );
         subv( nbp[ibk],nprb[ibk], siprdp[ibk],iprdp[ibk] );
         iprc_view[ibk].subv( nbq[ibk],nprb[ibk],  siprc[ibk] );
         iprdp_view[ibk].subv( nbp[ibk],nprb[ibk], siprdp[ibk] );
     }

      //zprd_re= new Real*[nv];
      //zprd_im= new Real*[nv];
      prd->create( nv,&(szprd_re) ); zprd_re.subv( nv,nprq, szprd_re ); setv( 0,nprq, nv, ZERO, zprd_re, "h" );
      prd->create( nv,&(szprd_im) ); zprd_im.subv( nv,nprq, szprd_im ); setv( 0,nprq, nv, ZERO, zprd_im, "h" );

      //zcprd_re= new Real*[nv];
      //zcprd_im= new Real*[nv];
      prd->create( nv,&(szcprd_re) ); zcprd_re.subv( nv,nprq, szcprd_re ); setv( 0,nprq, nv, ZERO, zcprd_re, "h" ); 
      prd->create( nv,&(szcprd_im) ); zcprd_im.subv( nv,nprq, szcprd_im ); setv( 0,nprq, nv, ZERO, zcprd_im, "h" );

      //rhszprd_re= new Real*[nv];
      //rhszprd_im= new Real*[nv];
      dof->create( nv,  &(srhszprd_re)  );   rhszprd_re.subv( nv,  nq, srhszprd_re ); setv( 0,nq,    nv, ZERO,    rhszprd_re, "h" );
      dof->create( nv,  &(srhszprd_im)  );   rhszprd_im.subv( nv,  nq, srhszprd_im ); setv( 0,nq,    nv, ZERO,    rhszprd_im, "h" );

   
      //wnprd=   new Real*[ nx+1];
      //wxdprd=  new Real*[    1];
      //xprd=    new Real*[   nx];
      //auxfprd= new Real*[nauxf];
      //dxdxprd= new Real*[nx*nx];
      //dqdxprd= new Real **[nv];
      //sdqdxprd= new Real*[nv];
      sdqdxprd= new Real [nv*nx*nprq];

      prd->create(  nx+1,& swnprd  );   wnprd.subv( nx+1,nprq,   swnprd );  setv( 0,nprq,  nx+1,  ZERO,   wnprd, "h" );
      prd->create(     1,&swxdprd  );  wxdprd.subv(    1,nprq,  swxdprd );  setv( 0,nprq,     1,  ZERO,  wxdprd, "h" );
      prd->create(    nx,&  sxprd  );   xprd.subv(   nx,nprq,    sxprd );   setv( 0,nprq,    nx,  ZERO,    xprd, "h" );
      prd->create( nauxf,&sauxfprd  );  auxfprd.subv(nauxf,nprq, sauxfprd); setv( 0,nprq, nauxf,  ZERO,  auxfprd, "h" );
      prd->create( nx*nx,&sdxdxprd  );  dxdxprd.subv_3d( nx, nx,  nprq, sdxdxprd ); setv_3d( 0,nprq, nx, nx, ZERO, dxdxprd, "h" );
                                        dqdxprd.subv_3d( nv, nx,  nprq, sdqdxprd ); setv_3d( 0,nprq, nv, nx, ZERO, dqdxprd, "h" );
      for( iv=0;iv<nv;iv++ )
     {
         //dqdxprd[iv]= new Real*[nx]; //sdqdxprd[iv]= NULL;
         //prd->create( nx, sdqdxprd+iv ); subv( nx, nprq, sdqdxprd[iv], dqdxprd[iv] );  setv( 0,nprq, nx, ZERO, dqdxprd[iv] );
         prd->regaddr( nx, &(sdqdxprd[iv*nx*nprq]) );

         //dqdxprd[iv].subv(nx, nq, &(sdqdxprd[iv*nx*nprq]));

        // for( int ix=0;ix<nx;ix++ )
        //{
        //     dqdxprd[iv][ix]= sdqdxprd+ix*nprq+iv*nprq*nx;
        //}
        // setv( 0,nprq, nx, ZERO, dqdxprd[iv] );
     }

      //dzdxprd_re= new Real **[nv];
      //dzdxprd_im= new Real **[nv];
      sdzdxprd_re= new Real*[nv];
      sdzdxprd_im= new Real*[nv];
      for( iv=0; iv<nv; iv++ ) 
     {
         //dzdxprd_re[iv]= new Real*[nx]; sdzdxprd_re[iv]= NULL;
         sdzdxprd_re[iv]= NULL;
         dof->create( nx, sdzdxprd_re+iv  ); dzdxprd_re[iv].subv( nx,  nq, sdzdxprd_re[iv] );

         //dzdxprd_im[iv]= new Real*[nx]; sdzdxprd_im[iv]= NULL;
         sdzdxprd_im[iv]= NULL;
         dof->create( nx, sdzdxprd_im+iv  ); dzdxprd_im[iv].subv( nx,  nq, sdzdxprd_im[iv] );
     }

      for( ig=0;ig<ng;ig++ )
     {
         //qb[ig]=  new Real*[nv];
         //dqb[ig]=  new Real*[nv];
         //rhsb[ig]=  new Real*[nv];
         //resb[ig]=  new Real*[nv];
         //lhsb[ig]=  new Real*[nlhs];
         //qb0[ig]=  new Real*[nv];
         //auxb[ig]=  new Real*[naux];
         //dauxb[ig]=  new Real*[naux];
         //auxb0[ig]=  new Real*[naux];
         //auxfb[ig]= new Real*[nauxf];
         iqb[ig]= new  Int*[1];
         bdf[ig]->read( nx,&(sxb[ig]),f );  xb[ig].subv( nx,nbb[ig],sxb[ig] );
         bdf[ig]->read(  1,&(siqb[ig]),f ); subv(  1,nbb[ig],siqb[ig],iqb[ig] ); iqb_view[ig].subv_1d(  nbb[ig],siqb[ig] );
         bdf[ig]->read(  1,&( ibql[ig]),f ); ibql_view[ig].subv_1d( nbb[ig],ibql[ig] );

         bdf[ig]->create( nv,&(sqb[ig]) ); qb[ig].subv( nv,nbb[ig],sqb[ig] ); setv( 0,nbb[ig], nv,ZERO,qb[ig], "h" );
         bdf[ig]->create( nv,&(sdqb[ig]) ); dqb[ig].subv( nv,nbb[ig],sdqb[ig] ); setv( 0,nbb[ig], nv,ZERO,dqb[ig], "h" );
         bdf[ig]->create( nv,&(srhsb[ig]) ); rhsb[ig].subv( nv,nbb[ig],srhsb[ig] );setv( 0,nbb[ig], nv,ZERO,rhsb[ig], "h" );
         bdf[ig]->create( nv,&(sresb[ig]) ); resb[ig].subv( nv,nbb[ig],sresb[ig] );setv( 0,nbb[ig], nv,ZERO,resb[ig], "h" );
         bdf[ig]->create( nlhs,&(slhsb[ig]) ); lhsb[ig].subv( nlhs,nbb[ig],slhsb[ig] );setv( 0,nbb[ig], nlhs,ZERO,lhsb[ig], "h" );
         bdf[ig]->create( nv,&(sqb0[ig]) ); qb0[ig].subv( nv,nbb[ig],sqb0[ig] );setv( 0,nbb[ig], nv,ZERO,qb0[ig], "h" );
         bdf[ig]->create( naux,&(sauxb[ig]) ); auxb[ig].subv( naux,nbb[ig],sauxb[ig] );setv( 0,nbb[ig], naux,ZERO,auxb[ig], "h" );
         bdf[ig]->create( nv,&(sdauxb[ig]) ); dauxb[ig].subv( nv,nbb[ig],sdauxb[ig] );setv( 0,nbb[ig],nv,ZERO,dauxb[ig], "h" );
         bdf[ig]->create( naux,&(sauxb0[ig]) ); auxb0[ig].subv( naux,nbb[ig],sauxb0[ig] );setv( 0,nbb[ig], naux,ZERO,auxb0[ig], "h" );

         bdf[ig]->create(   nx,&( sxqb[ig]) ); xqb[ig].subv(   nx,nbb[ig],sxqb[ig]); setv( 0,nbb[ig],nx,ZERO,xqb[ig], "h" );
         bdf[ig]->create( nx+1,&( swnb[ig]) ); wnb[ig].subv( nx+1,nbb[ig],swnb[ig]);setv( 0,nbb[ig],nx+1,ZERO,wnb[ig], "h" );
         bdf[ig]->create(    1,&(swxdb[ig]) ); wxdb[ig].subv(    1,nbb[ig],swxdb[ig]);setv( 0,nbb[ig],1,ZERO,wxdb[ig], "h" );
         bdf[ig]->create(nauxf,&(sauxfb[ig]) ); auxfb[ig].subv(nauxf,nbb[ig],sauxfb[ig]);setv( 0,nbb[ig],nauxf,ZERO,auxfb[ig], "h" );

         dsd[ig]->read(  2,&(siqdst[ig]),f ); iqdst[ig].subv(  2,ndst[ig],siqdst[ig] );
         dsd[ig]->read(  1,&(sibdst[ig]),f ); ibdst[ig].subv_1d( ndst[ig],sibdst[ig] );
         for( ibk=0;ibk<nbk;ibk++ )
        {
            ibp[ig][ibk]= new Int*[nbp[ibk]];
            ibb[ig][ibk]= new Int*[nbd[ibk]];
            bld[ig][ibk]->read( nbp[ibk],&(sibp[ig][ibk]),f ); subv( nbp[ibk],nb[ig][ibk], sibp[ig][ibk],ibp[ig][ibk] );
            bld[ig][ibk]->read( nbd[ibk],&(sibb[ig][ibk]),f ); subv( nbd[ibk],nb[ig][ibk], sibb[ig][ibk],ibb[ig][ibk] ); ibb_view[ig][ibk].subv( nbd[ibk],nb[ig][ibk], sibb[ig][ibk] );
        }

         for(Int i=0; i<nfre; i++)
        {
            //zb0_re[i][ig]=  new Real*[nv];
            //zb0_im[i][ig]=  new Real*[nv];
            bdf[ig]->create( nv,&(szb0_re[i][ig]) ); zb0_re[i][ig].subv( nv,nbb[ig],szb0_re[i][ig] ); setv( 0,nbb[ig], nv,ZERO,zb0_re[i][ig], "h" );
            bdf[ig]->create( nv,&(szb0_im[i][ig]) ); zb0_im[i][ig].subv( nv,nbb[ig],szb0_im[i][ig] );setv( 0,nbb[ig], nv,ZERO,zb0_im[i][ig], "h" );
        }

         //zb_re[ig]=  new Real*[nv];
         //zcb_re[ig]=  new Real*[nv];
         //rhsbz_re[ig]=  new Real*[nv];

         bdf[ig]->create( nv,&(szb_re[ig]) ); zb_re[ig].subv( nv,nbb[ig],szb_re[ig] ); setv( 0,nbb[ig], nv,ZERO,zb_re[ig], "h" );
         bdf[ig]->create( nv,&(szcb_re[ig]) ); zcb_re[ig].subv( nv,nbb[ig],szcb_re[ig] ); setv( 0,nbb[ig], nv,ZERO,zcb_re[ig], "h" );
         bdf[ig]->create( nv,&(srhsbz_re[ig]) ); rhsbz_re[ig].subv( nv,nbb[ig],srhsbz_re[ig] ); setv( 0,nbb[ig], nv,ZERO,rhsbz_re[ig], "h" );

         //zb_im[ig]=  new Real*[nv];
         //zcb_im[ig]=  new Real*[nv];
         //rhsbz_im[ig]=  new Real*[nv];

         bdf[ig]->create( nv,&(szb_im[ig]) ); zb_im[ig].subv( nv,nbb[ig],szb_im[ig] ); setv( 0,nbb[ig], nv,ZERO,zb_im[ig], "h" );
         bdf[ig]->create( nv,&(szcb_im[ig]) ); zcb_im[ig].subv( nv,nbb[ig],szcb_im[ig] ); setv( 0,nbb[ig], nv,ZERO,zcb_im[ig], "h" );
         bdf[ig]->create( nv,&(srhsbz_im[ig]) ); rhsbz_im[ig].subv( nv,nbb[ig],srhsbz_im[ig] ); setv( 0,nbb[ig], nv,ZERO,rhsbz_im[ig], "h" );
     }


      fclose(f);

      for(ig=0; ig<ng; ig++)
     {
         Int *tmpipb, ien;
         bpts( ig, &ien, &(tmpipb) );
         npb[ig] = ien;
         sxbp[ig] = new Real [nx*ien];     xbp[ig].subv( nx, ien, sxbp[ig] );
         sxdbp[ig] = new Real [nvel*ien];  xdbp[ig].subv( nvel, ien, sxdbp[ig] );
         delete[] tmpipb; tmpipb=NULL;
     }
      assgnipb();

      //qmin = new Real* [nv];
      //qmax = new Real* [nv];
      //vklim = new Real* [nv];
      dof->create( nv,  &sqmin  );   qmin.subv( nv,  nq, sqmin ); setv( 0,nq,    nv, (Real) big,  qmin, "h" );
      dof->create( nv,  &sqmax  );   qmax.subv( nv,  nq, sqmax ); setv( 0,nq,    nv, (Real)-big,  qmax, "h" );
      dof->create( nv,  &svklim  );  vklim.subv( nv,  nq, svklim ); setv( 0,nq,    nv, (Real)  1.,  vklim, "h" );

      //qface[0] = new Real* [nv];
      //qface[1] = new Real* [nv];
      sqface[0] = new Real [nv*nfc];
      sqface[1] = new Real [nv*nfc];
      qface[0].subv( nv,  nfc, sqface[0] );  setv( 0,nfc,    nv, ZERO,  qface[0], "h" );
      qface[1].subv( nv,  nfc, sqface[1] );  setv( 0,nfc,    nv, ZERO,  qface[1], "h" );

      //qprdface[0] = new Real* [nv];
      //qprdface[1] = new Real* [nv];
      sqprdface[0] = new Real [nv*nprq];
      sqprdface[1] = new Real [nv*nprq];
      qprdface[0].subv( nv,  nprq, sqprdface[0] ); setv( 0,nprq,    nv, ZERO,  qprdface[0], "h" );
      qprdface[1].subv( nv,  nprq, sqprdface[1] ); setv( 0,nprq,    nv, ZERO,  qprdface[1], "h" );


      if(fld->gettype()==roe_gas)
     {
         //when the mesh is small, nfc could be smaller than some nbb[ig]
         Int ntmp=-9999;
         for(ig=0; ig<ng; ig++) ntmp = max(nbb[ig], ntmp);
         ntmp = max(ntmp, nfc);
         jac_df[0] = new cJacBlk [ntmp];
         jac_df[1] = new cJacBlk [ntmp];
         jac_df_prd[0] = new cJacBlk [nprq];
         jac_df_prd[1] = new cJacBlk [nprq];
         jac_inv_prd[0] = new cJacBlk [nprq];
         jac_inv_prd[1] = new cJacBlk [nprq];
         jac_vis_prd[0] = new cJacBlk [nprq];
         jac_vis_prd[1] = new cJacBlk [nprq];

         //csv= new Real*[nv];
         dof->create( nv,  &scsv  );   csv.subv( nv,  nq, scsv ); setv( 0,nq,    nv, ZERO,    csv, "h" );
     }

/*    cout << "DUMP DEBUG LOG\n";
      fnme= path+"/debug.log."+strc(icpu)+"."+strc(ilev);
      ofstream fle;
      fle.open( fnme.c_str() );
      for( ig=0;ig<ng;ig++ )
     {
         fle << "\n\n BDF "<<ig<<" \n";
         bdf[ig]->debug( &fle );
     }
      fle.close();
      cout << "DUMP DEBUG LOG DONE\n";*/
     
     
/*    fnme= path+"/debug.log."+strc(icpu)+"."+strc(ilev);
      ofstream fle;
      fle.open( fnme.c_str() );
      fle << "\n\n DOF\n";
      dof->debug( &fle );
      fle << "\n\n PTS\n";
      pts->debug( &fle );
      for( ig=0;ig<ng;ig++ )
     {
         fle << "\n\n BDF "<<ig<<" \n";
         bdf[ig]->debug( &fle );
         fle << "\n\n DSD "<<ig<<" \n";
         dsd[ig]->debug( &fle );
     }
      fle << "\n\n CNF\n";
      cnf->debug( &fle );
      for( ibk=0;ibk<nbk;ibk++ )
     {
         fle << "\n\n CND "<<ibk<<" \n";
         cnd[ibk]->debug( &fle ); 
     }
      fle << "\n\n PRD \n";
      prd->debug( &fle );
      fle << "\n\n PRP \n";
      prp->debug( &fle );
      for( iek=0;iek<nek;iek++ )
     {
         fle << "\n\n ELD "<<iek<<" \n";
         eld[iek]->debug( &fle ); 
     }
      for( ig=0;ig<ng;ig++ )
     {
         for( ibk=0;ibk<nbk;ibk++ )
        {
            fle << "\n\n BND "<<ig<<" "<<ibk<<" \n";
            bld[ig][ibk]->debug( &fle );
        }
     }
      fle.close();*/

      dof->start();
      pts->start();
      for( ig=0;ig<ng;ig++ )
     {
         bdf[ig]->start();
     }
      prd->start();

/*    stophere();*/

//    cout << "SHOULD BE LOADING A NEW DOMAIN? "<<bcrs<<"\n";
      if( bcrs )
     {
         cFdDomain *tmp;
//       cout << "YES IT IS LOADING LOADING A NEW DOMAIN "<<bcrs<<"\n";
         tmp= new cFdDomain();
         tmp->assgndev( dev );
         tmp->level( ilev+1 );
         tmp->loadpre();
         //attach( tmp );
         readmgpart( tmp );
     }
      #ifdef _OPENACC
      start_acc_device();
      #endif
      init();
      maverage(false);

  }
