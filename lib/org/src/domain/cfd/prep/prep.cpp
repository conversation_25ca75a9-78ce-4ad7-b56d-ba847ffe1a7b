   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::loadpart()
  {
      cKdTree *kdt;
      FILE  *f;
      Int   *cprt;
      Int    ir,iq,ip,ib,jb,ig,ibk;
      Real   d;
      Real   y[3];
      string path;
      string fnme;

      if( !qcpu )
     {
         qcpu= new Int[nq];
// some auxiliary arrays
         for( ig=0;ig<ng;ig++ )
        {
            siqb[ig]= new Int[nbb[ig]]; 
            iqb[ig]= new Int*[1];
            subv( (Int)1,nbb[ig], siqb[ig], iqb[ig] );
            setv( (Int)0,nbb[ig],   (Int)0, iqb[ig][0] );
            for( ibk=0;ibk<nbk;ibk++ )
           {
               for( jb=0;jb<nb[ig][ibk];jb++ )
              {
                  ib= ibb[ig][ibk][0][jb];
                  iq= ibq[ig][ibk][0][jb];
                  iqb[ig][0][ib]= iq;
              }
           }
        }

         if( crs )
        {
            crs->loadpart();
            crs->getqcpu( &cprt );
            for( iq=0;iq<nq;iq++ )
           {
               qcpu[iq]= cprt[ iqcrs[iq] ];
           }
        }
         else
        {
            path= dev->getcpath();
            fnme= path+ "/part.dat";

            f=fopen( fnme.c_str(), "r" );
            fread( (void*)qcpu, sizeof(*qcpu),(size_t)nq, f );
            fclose( f );
        }


         delete[] pcpu; 
         pcpu= new Int[np];
         for( ig=0;ig<ng;ig++ )
        {
            delete[] bcpu[ig];
            bcpu[ig]= new Int[nbb[ig]];
        }

         delete[] rcpu;
         rcpu= new Int[nprq];

         if( nq > 3 )
        {
            Real *tmpxq[3];
            subv(nx,nq,sxq,tmpxq);
            kdt= new cKdTree();
            kdt->build( nx, nq,tmpxq );

            for( ip=0;ip<np;ip++ )
           {
               line( ip,nx,xp, y );
               kdt->nearest( y,&iq,&d );
               pcpu[ip]= qcpu[iq];
           }

            for( ig=0;ig<ng;ig++ )
           {
               for( ib=0;ib<nbb[ig];ib++ )
              {
/*                line( ib,nx,xb[ig], y );
                  kdt->nearest( y,&iq,&d );*/
                  iq= iqb[ig][0][ib];
                  bcpu[ig][ib]= qcpu[iq];
              }
           }
   
            delete kdt; kdt= NULL;
        }
         else
        {
            for( ip=0;ip<np;ip++ )
           {
               pcpu[ip]= qcpu[0];
           }

            for( ig=0;ig<ng;ig++ )
           {
               for( ib=0;ib<nbb[ig];ib++ )
              {
                  bcpu[ig][ib]= qcpu[0];
              }
           }
            assert( dev->getncpu() == 1 );
        }

         for( ir=0;ir<nprq;ir++ )
        {
            iq= iprq[0][ir];
            rcpu[ir]= qcpu[iq];
        }

         faces();
         distance();
         layers();

     }
  }

   void cFdDomain::prep( Int icpu )
  {

      Int        iek,ibk;
      Int        ig,iq,ip,jp,ie,ib;

      ofstream   lgf;
      string     path,fnme;

      Real *lxp,*lxq,*ldst;
      Int  *liep[MxNSk],*lieq[MxNSk],*liem[MxNSk], *lmedium_marker[MxNSk];
      Int  *libp[MxNBG][MxNSk],*liqb[MxNBG],*libb[MxNBG][MxNSk];
      Int  *licp[MxNSk],*licc[MxNSk],*lifq;
      Real *lxb[MxNBG];
      Int  *liprq,*liprp,*liprc[MxNSk],*liprdp[MxNSk];
      Int  *libdst[MxNBG],*liqdst[MxNBG];
      Int  *libql[MxNBG];

      bool       val;
      path= dev->getcpath();
      fnme= path+ "/domain.preprocessor.log."+strc( icpu )+"."+strc( ilev );
      lgf.open( fnme.c_str() );

      loadpart();
      cout << "cFeDomain class should be preprocessing cpu "<<icpu<<"\n";
 
      //if( unst ){ ntlv = 2; };

      dof= new cPdata( nq,dev,qcpu );
      pts= new cPdata( np,dev,pcpu );
      prd= new cPdata( nprq,dev );//,rcpu );
      prp= new cPdata( nprp,dev );
      for( ibk=0;ibk<nbk;ibk++ )
     {
        pld[ibk]= new cPdata( nprb[ibk],dev );
     }     

      for( iek=0;iek<nek;iek++ )
     {
         eld[iek]= new cPdata( ne[iek],dev );
     }
      cnf= new cPdata( nfc,dev );
      for( ibk=0;ibk<nbk;ibk++ )
     {
         cnd[ibk]= new cPdata( nc[ibk],dev );
     }
      for( ig=0;ig<ng;ig++ )
     {
         bdf[ig]= new cPdata( nbb[ig],dev,bcpu[ig] );
         dsd[ig]= new cPdata( ndst[ig],dev );
         cout << ndst[ig]<<"\n";
         for( ibk=0;ibk<nbk;ibk++ )
        {
            bld[ig][ibk]= new cPdata( nb[ig][ibk],dev ); 
        }
     }


      for( iek=0;iek<nek;iek++ )
     {
         eld[iek]->wrloop( dof, neq[iek],ieq[iek] );
         eld[iek]->wrloop( pts, nep[iek],iep[iek] );
     }

      prd->wrloop( dof, 2,iprq );
      prp->wrloop( pts, 2,iprp );
      for( ibk=0;ibk<nbk;ibk++ )
     {
         pld[ibk]->wrloop( prd, 1,iprc[ibk] );
     }

      cnf->wrloop( dof, 2,ifq );
      for( ibk=0;ibk<nbk;ibk++ )
     {
         cnd[ibk]->wrloop( cnf, 1,icc[ibk]);
     }


      for( ig=0;ig<ng;ig++ )
     {
         dsd[ig]->wrloop( bdf[ig],1,ibdst[ig] );//cout << ig <<" dsd1\n";
     }
      for( ig=0;ig<ng;ig++ )
     {
         dsd[ig]->wrloop( dof,2,iqdst[ig] );
     }
      for( ig=0;ig<ng;ig++ )
     {
         for( ibk=0;ibk<nbk;ibk++ )
        {
            bld[ig][ibk]->wrloop( bdf[ig], nbd[ibk],ibb[ig][ibk] );
        }
     }

      for( ig=0;ig<ng;ig++ )
     {
         bdf[ig]->wrloop( dof,1,iqb[ig] );
     }
      for( iek=0;iek<nek;iek++ )
     {
         eld[iek]->rdloop( dof, neq[iek],ieq[iek] );
         eld[iek]->rdloop( pts, nep[iek],iep[iek] );
     }

      prd->rdloop( dof, 2,iprq );
      prp->rdloop( pts, 2,iprp );
      for( ibk=0;ibk<nbk;ibk++ )
     {
         pld[ibk]->rdloop( pts, nbp[ibk],iprdp[ibk] );
     }

      cnf->rdloop( dof, 2,ifq );
      for( ibk=0;ibk<nbk;ibk++ )
     {
         cnd[ibk]->rdloop( pts, nbp[ibk],icp[ibk]);
     }

      for( ig=0;ig<ng;ig++ )
     {
         dsd[ig]->rdloop( bdf[ig],1,ibdst[ig] );//cout << ig <<" dsd1\n";
     }
      for( ig=0;ig<ng;ig++ )
     {
         dsd[ig]->rdloop( dof,2,iqdst[ig] );//cout << ig <<" dsd1\n";
     }
      for( ig=0;ig<ng;ig++ )
     {
         for( ibk=0;ibk<nbk;ibk++ )
        {
            bld[ig][ibk]->rdloop( pts, nbp[ibk],ibp[ig][ibk] );
        }
     }
      for( ig=0;ig<ng;ig++ )
     {
         bdf[ig]->rdloop( dof,1,iqb[ig]   );
     }

      pts->partition(  ); //cout << "pts \n";
      dof->partition(  ); //cout << "dof \n";
      dof->partition_mat(  ); //cout << "dof \n";

      for( iek=0;iek<nek;iek++ )
     {
         eld[iek]->partition( ); //cout <<iek<<" ele\n";
     }

      prd->partition( ); //cout << "prd\n";
      prp->partition( ); //cout << "prp\n";
      for( ibk=0;ibk<nbk;ibk++ )
     {
         pld[ibk]->partition(); //cout << "pld "<<ibk<<"\n";
     }

      cnf->partition( ); //cout << "cnf\n";
      for( ibk=0;ibk<nbk;ibk++ )
     {
         cnd[ibk]->partition( ); //cout << ibk<<" cnd\n";
     }

      for( ig=0;ig<ng;ig++ )
     {
         bdf[ig]->partition(  ); //cout << ig << "bdf\n";
         dsd[ig]->partition(  ); //cout << ig << "dsd\n";
         for( ibk=0;ibk<nbk;ibk++ )
        {
            bld[ig][ibk]->partition( ); //cout << ig << " bld\n";
        }
     }
      //cout << "everything partitioned\n";

      lxp=NULL;
      lxq=NULL;
      ldst=NULL;
      pts->makelocal( nx,xp, &lxp ); //cout << "xp\n";
      dof->makelocal( nx,xq, &lxq ); //cout << "xq\n";
      if( vsc->viscous() ){ dof->makelocal(  2,dst,&ldst); };//cout << "dst\n"; };

      for( iek=0;iek<nek;iek++ )
     {
         liem[iek]=NULL;
         liep[iek]=NULL;
         lieq[iek]=NULL;
         lmedium_marker[iek]=NULL;
         eld[iek]->makelocal(          1, iem[iek],&(liem[iek]) ); //cout << iek << "iem\n";
         eld[iek]->makelocal(   nep[iek], iep[iek],&(liep[iek]), pts ); //cout << iek << "iep\n";
         eld[iek]->makelocal(   neq[iek], ieq[iek],&(lieq[iek]), dof ); //cout << iek << "ieq\n";
         eld[iek]->makelocal(   1, medium_marker[iek],&(lmedium_marker[iek]) ); //cout << iek << "ieq\n";
     }

      liprq= NULL;
      liprp= NULL;
      prd->makelocal( 2,iprq, &liprq, dof ); //cout << "prq\n";
      prp->makelocal( 2,iprp, &liprp, pts ); //cout << "prp\n";
      for( ibk=0;ibk<nbk;ibk++ )
     {
         liprc[ibk]= NULL;
         liprdp[ibk]= NULL;
         pld[ibk]->makelocal( nbp[ibk],iprdp[ibk], &(liprdp[ibk]), pts ); //cout << ibk <<" pld iprdp\n";
         pld[ibk]->makelocal(        1, iprc[ibk], &( liprc[ibk]), prd ); //cout << ibk <<" pld  iprc\n";
     }

      lifq=NULL;
      cnf->makelocal( 2,ifq, &(lifq), dof ); //cout << "ifq\n";
      for( ibk=0;ibk<nbk;ibk++ )
     {
         licc[ibk]=NULL;
         licp[ibk]=NULL;
         cnd[ibk]->makelocal( nbp[ibk], icp[ibk], &(licp[ibk]), pts ); //cout << ibk <<" icp\n";
         cnd[ibk]->makelocal(        1, icc[ibk], &(licc[ibk]), cnf ); //cout << ibk <<" icc\n";
     }

      for( ig=0;ig<ng;ig++ )
     {
         lxb[ig]= NULL;
         liqb[ig]=NULL;
         libql[ig]=NULL;

         bdf[ig]->makelocal( nx, xb[ig], &(lxb[ig]) ); //cout << ig << " xb\n";
         bdf[ig]->makelocal(  1,iqb[ig], &(liqb[ig]), dof ); //cout << ig << " iqb\n";
         bdf[ig]->makelocal(  1,ibql+ig, &(libql[ig]) ); //cout << ig << " iqb\n";

         liqdst[ig]= NULL;
         libdst[ig]= NULL;
         dsd[ig]->makelocal(  2,iqdst[ig], &(liqdst[ig]), dof ); //cout << ig << " iqdst\n";
         dsd[ig]->makelocal(  1,ibdst[ig], &(libdst[ig]), bdf[ig] ); //cout << ig << " ibdst\n";

         for( ibk=0;ibk<nbk;ibk++ )
        {
            libp[ig][ibk]=NULL;
            libb[ig][ibk]=NULL;
            bld[ig][ibk]->makelocal( nbp[ibk], ibp[ig][ibk],&(libp[ig][ibk]), pts ); //cout << ig <<" "<<ibk<<" ibp\n";
            bld[ig][ibk]->makelocal( nbd[ibk], ibb[ig][ibk],&(libb[ig][ibk]), bdf[ig] ); //cout << ig << " " << ibk << "ibb\n";
        }
     }

// dump to file

      size_t len;
      pickle_t buf;
      fnme= path+"/preprocessor."+strc( icpu )+"."+strc( ilev );

      len=0;
      buf=NULL;

      pickle( &len,&buf );

      FILE *f=fopen( fnme.c_str(),"w" );
      fwrite( (void*)&len,  1,sizeof( len), f );
      fwrite( (void*) buf,len,sizeof(*buf), f );
      delete[] buf;buf=NULL;len=0;

      pts->write( nx,lxp,f );
      dof->write( nx,lxq,f );
      if( vsc->viscous() )
     {
         dof->write( 2,ldst,f );
     }
      for( iek=0;iek<nek;iek++ )
     {
         eld[iek]->write(        1,liem[iek], f );
         eld[iek]->write( nep[iek],liep[iek], f );
         eld[iek]->write( neq[iek],lieq[iek], f );
         eld[iek]->write( 1,lmedium_marker[iek], f );
     }
      cnf->write( 2,lifq,f );
      for( ibk=0;ibk<nbk;ibk++ )
     {
         cnd[ibk]->write( nbp[ibk],licp[ibk], f );
         cnd[ibk]->write(        1,licc[ibk], f );
     }

      prd->write( 2,liprq, f );
      prp->write( 2,liprp, f );
      for( ibk=0;ibk<nbk;ibk++ )
     {
         pld[ibk]->write(        1, liprc[ibk], f );
         pld[ibk]->write( nbp[ibk],liprdp[ibk], f );
     }
      
      for( ig=0;ig<ng;ig++ )
     {
         bdf[ig]->write( nx,lxb[ig], f );
         bdf[ig]->write(  1,liqb[ig], f );
         bdf[ig]->write(  1,libql[ig], f );
         dsd[ig]->write(  2,liqdst[ig], f );
         dsd[ig]->write(  1,libdst[ig], f );
         for( ibk=0;ibk<nbk;ibk++ )
        {
            bld[ig][ibk]->write( nbp[ibk],libp[ig][ibk], f );
            bld[ig][ibk]->write( nbd[ibk],libb[ig][ibk], f );
        }
     }

      fclose(f);

      //generate probes for unsteady computations
      if(unst)
     {
        //probe for unsteady simulations
        Int tmpnp, iq, n, qlist[100];
        Real y[3], d;
        cKdTree *kdt;
        ifstream fle;
        ofstream ofle;
        string path, fnme, fnme1;

        path= dev->getcpath();
        fnme= path+ "/probe.x";
        fle.open(fnme.c_str());
        if(fle.good())
       {
           Real *tmpxq[3];
           subv(nx,nq,sxq,tmpxq);
           kdt= new cKdTree();
           kdt->build( nx, nq,tmpxq );

           //find how many probe points in this partion
           n=0;
           fle >> tmpnp;
           for(ip=0; ip<tmpnp; ip++)
          {
              for(Int ix=0; ix<nx; ix++) fle >> y[ix];
              kdt->nearest( y,&iq,&d );
              qlist[ip] = iq;
              if(icpu == qcpu[iq]) n++;
          }

           //dump the global to local coorepondence
           if(n>0)
          {
             fnme1 = path + "/probe" + "." + strc(icpu) + ".part";
             ofle.open(fnme1.c_str());
             ofle << n << "\n";
             for(ip=0; ip<tmpnp; ip++)
            {
               iq = qlist[ip];
               if(icpu == qcpu[iq])
              {
                  ofle << iq << " " << qcpu[iq] << " " << dof->localindex(iq) << "\n";
              }
            }
             ofle.close();
          }

           delete kdt; kdt=NULL;
       }
        fle.close();
     }

      if(crs)
     {
         ofstream fle;
         Int crsnq, jq, ix, tmpnq;
         Real *crsxq[3];

         tmpnq=0;
         for(iq=0; iq<nq; iq++)
        {
            if(qcpu[iq]==icpu)
           {
               tmpnq++;
           }
        }

         cout << dof->gsize() << " " << dof->size() << " ==========\n"; 
         crs->getselfxq(&crsnq, crsxq);

         path= dev->getcpath();
         fnme= path+ "/mg."+strc( icpu )+"."+strc( ilev );
         fle.open(fnme.c_str());
         fle.precision(16);      
         fle << tmpnq << "\n";
         for(iq=0; iq<nq; iq++)
        {
            if(qcpu[iq]==icpu)
           {
               jq = iqcrs[iq];
               fle << scientific << dof->localindex(iq) << " ";
               for(ix=0; ix<nx; ix++)
              {
                  fle << crsxq[ix][jq] << " ";
              }
               fle << "\n";
           }
        }
         fle.close();
     }

// cleanup

      lgf << "\n DOF\n";
      dof->summary( &lgf );
//    dof->debug( &lgf );
      lgf << "\n PTS\n";
//    pts->debug( &lgf );
      for( iek=0;iek<nek;iek++ )
     {
         lgf << "\n ELD "<<iek<<"\n";
//       eld[iek]->debug( &lgf );
     }
      lgf << "\n CNF "<<ibk<<"\n";
//    cnf->debug( &lgf );
      for( ibk=0;ibk<nbk;ibk++  )
     {
         lgf << "\n CND "<<ibk<<"\n";
//       cnd[ibk]->debug( &lgf );
     }
      lgf << "\n PRD\n";
//    prd->debug( &lgf );
      lgf << "\n PRP\n";
//    prp->debug( &lgf );
      for( ig=0;ig<ng;ig++ )
     {
         for( ibk=0;ibk<nbk;ibk++ )
        {
            lgf << "\n BLD "<<ig<<" "<<ibk<<"\n";
//          bld[ig][ibk]->debug( &lgf );
        }
         lgf << "\n bdf "<<ig<<"\n";
//       bdf[ig]->debug( &lgf);
         lgf << "\n dsd "<<ig<<"\n";
//       dsd[ig]->debug( &lgf);
     }

      for( iek=0;iek<nek;iek++ )
     {
         eld[iek]->destroy( &liem[iek] );
         eld[iek]->destroy( &liep[iek] );
         eld[iek]->destroy( &lieq[iek] );
         eld[iek]->destroy( &lmedium_marker[iek] );
         delete eld[iek]; eld[iek]= NULL;
     }

      cnf->destroy( &lifq );
      delete cnf; cnf=NULL;
      for( ibk=0;ibk<nbk;ibk++  )
     {
         cnd[ibk]->destroy( &(licc[ibk]) );
         cnd[ibk]->destroy( &(licp[ibk]) );
         delete cnd[ibk]; cnd[ibk]= NULL; 
     }

      prd->destroy( &(liprq) );
      prp->destroy( &(liprp) );
      delete prd; prd= NULL;
      delete prp; prp= NULL;
      for( ibk=0;ibk<nbk;ibk++ )
     {
         pld[ibk]->destroy( &( liprc[ibk]) );
         pld[ibk]->destroy( &(liprdp[ibk]) );
         delete pld[ibk]; pld[ibk]= NULL;
     }

      for( ig=0;ig<ng;ig++ )
     {

         bdf[ig]->destroy( &lxb[ig] );
         bdf[ig]->destroy( &liqb[ig] );
         bdf[ig]->destroy( &libql[ig] );

         delete bdf[ig]; bdf[ig]= NULL;
         dsd[ig]->destroy( &liqdst[ig] );
         dsd[ig]->destroy( &libdst[ig] );
         delete dsd[ig]; dsd[ig]= NULL;
         for( ibk=0;ibk<nbk;ibk++ )
        {
            bld[ig][ibk]->destroy( &libp[ig][ibk] );
            bld[ig][ibk]->destroy( &libb[ig][ibk] );
        }
     }

      for( ig=0;ig<ng;ig++ )
     {
         for( ibk=0;ibk<nbk;ibk++ )
        {
            delete bld[ig][ibk]; bld[ig][ibk]= NULL;
        }
     }
      dof->destroy( &lxq );
      if( vsc->viscous() ){ dof->destroy( &ldst ); };
      pts->destroy( &lxp );
      delete dof; dof= NULL;
      delete pts; pts= NULL;

      lgf.close();

      if( crs )
     {
         crs->prep( icpu );
     }
  }
