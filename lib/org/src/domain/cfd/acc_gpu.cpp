
   using namespace std;

#  include <domain/cfd/domain.h>

    void cFdDomain::start_acc_device()
   {
       Int ig;

//classes
      #pragma acc enter data copyin (this)
      

//periodic boundary
      #pragma acc enter data copyin (siprq0[0:nprq])
      #pragma acc enter data copyin (siprq1[0:nprq])
      #pragma acc enter data copyin (sxqprd[0:nx*nprq])
      #pragma acc enter data copyin (sqprd[0:nv*nprq])
      #pragma acc enter data copyin (sdxdxprd[0:nx*nx*nprq])
      #pragma acc enter data copyin (sdqdxprd[0:nx*nv*nprq])
      #pragma acc enter data copyin (sauxprd[0:naux*nprq])
      #pragma acc enter data copyin (srhsprd[0:nv*nprq])
      #pragma acc enter data copyin (sxprd[0:nx*nprq])
      #pragma acc enter data copyin (swnprd[0:(nx+1)*nprq])
      #pragma acc enter data copyin (swxdprd[0:nprq])
      #pragma acc enter data copyin (sauxfprd[0:nauxf*nprq])
      #pragma acc enter data copyin (sdqprd[0:nv*nprq])
      #pragma acc enter data copyin (sdauxprd[0:nv*nprq])
      #pragma acc enter data copyin (slhsprd[0:nlhs*nprq])

//boundary
      for( ig=0;ig<ng;ig++ )
     {
         #pragma acc enter data copyin (sxb[ig][0:nx*nbb[ig]])
         #pragma acc enter data copyin (sqb[ig][0:nv*nbb[ig]])
         #pragma acc enter data copyin (sauxb[ig][0:naux*nbb[ig]])
         #pragma acc enter data copyin (srhsb[ig][0:nv*nbb[ig]])
         #pragma acc enter data copyin (siqb[ig][0:nbb[ig]])
         #pragma acc enter data copyin (ibql[ig][0:nbb[ig]])
         #pragma acc enter data copyin (swnb[ig][0:(nx+1)*nbb[ig]])
         #pragma acc enter data copyin (swxdb[ig][0:nbb[ig]])
         #pragma acc enter data copyin (sauxfb[ig][0:nauxf*nbb[ig]])
         #pragma acc enter data copyin (siqdst[ig][0:2*ndst[ig]])
         #pragma acc enter data copyin (sibdst[ig][0:ndst[ig]])
         #pragma acc enter data copyin (sdqb[ig][0:nv*nbb[ig]])
         #pragma acc enter data copyin (sdauxb[ig][0:nv*nbb[ig]])
         #pragma acc enter data copyin (sresb[ig][0:nv*nbb[ig]])
         #pragma acc enter data copyin (sqb0[ig][0:nv*nbb[ig]])
         #pragma acc enter data copyin (sauxb0[ig][0:naux*nbb[ig]])
         #pragma acc enter data copyin (sxqb[ig][0:nx*nbb[ig]])
         #pragma acc enter data copyin (slhsb[ig][0:nlhs*nbb[ig]])
     }

//volume data
      #pragma acc enter data copyin (swq[0:(nx+1)*nq])
      #pragma acc enter data copyin (sxdq[0:nvel*nq] )
      #pragma acc enter data copyin (sxq[0:nx*nq])
      #pragma acc enter data copyin (sq[0:nv*nq])
      #pragma acc enter data copyin (sdxdx[0:nx*nx*nq])
      #pragma acc enter data copyin (sdqdx[0:nx*nv*nq])
      #pragma acc enter data copyin (saux[0:naux*nq])
      #pragma acc enter data copyin (srhs[0:nv*nq])
      #pragma acc enter data copyin (sxc[0:nx*nfc])
      #pragma acc enter data copyin (swnc[0:(nx+1)*nfc])
      #pragma acc enter data copyin (swxdc[0:nfc])
      #pragma acc enter data copyin (sauxf[0:nauxf*nfc])
      #pragma acc enter data copyin (sdq[0:nv*nq])
      #pragma acc enter data copyin (sdaux[0:nv*nq])
      #pragma acc enter data copyin (sres[0:nv*nq])
      #pragma acc enter data copyin (sdst[0:2*nq] )
      #pragma acc enter data copyin (slhsa[0:nlhs*nq])
      #pragma acc enter data copyin (su[0][0:nv*nq])
      #pragma acc enter data copyin (su[1][0:nv*nq])
      #pragma acc enter data copyin (su[2][0:nv*nq])


//face connection
      #pragma acc enter data copyin (sifq[0:2*nfc])

//arrays to compute weights
      #pragma acc enter data copyin (sxp[0:nx*np])
      for( Int iek=0;iek<nek;iek++ )
     {
         #pragma acc enter data copyin (siep[iek][0:nep[iek]*ne[iek]])
         #pragma acc enter data copyin (sieq[iek][0:neq[iek]*ne[iek]])
     }
      #pragma acc enter data copyin (sxdp[0:nvel*np])
      for( Int ibk=0;ibk<nbk;ibk++ )
     {
         #pragma acc enter data copyin (sicp[ibk][0:nbp[ibk]*nc[ibk]])
         #pragma acc enter data copyin (sicc[ibk][0:1*nc[ibk]])
     }
      for( Int ig=0;ig<ng;ig++ )
     {
         #pragma acc enter data copyin (sxbp[ig][0:nx*npb[ig]])
         #pragma acc enter data copyin (sxdbp[ig][0:nvel*npb[ig]])
         for( Int ibk=0;ibk<nbk;ibk++ )
        {
            Int *sipbp_tmp, *sibb_tmp;

            sipbp_tmp = sipbp[ig][ibk];
            sibb_tmp = sibb[ig][ibk];

            //if I do sibp[ig][ibk][0:nbp[ibk]*nb[ig][ibk]], openACC is complaining about partial present
            //in the device
            #pragma acc enter data copyin (sipbp_tmp[0:nbp[ibk]*nb[ig][ibk]])
            #pragma acc enter data copyin (sibb_tmp[0:nbd[ibk]*nb[ig][ibk]])
        }
     }
      for( Int ibk=0;ibk<nbk;ibk++ )
     {
         #pragma acc enter data copyin (siprdp[ibk][0:nbp[ibk]*nprb[ibk]])
         #pragma acc enter data copyin (siprc[ibk][0:nbq[ibk]*nprb[ibk]])
     }

//frame speed
     #pragma acc enter data copyin(omegb[0:ng])

      acc_started = true;
      cout << "-----------------start acc device---------------------------\n";
   }

    void cFdDomain::exit_acc_device()
   {
      Int ig; 

//periodic boundary
      #pragma acc exit data copyout (iprq0[0:nprq])
      #pragma acc exit data copyout (iprq1[0:nprq])
      #pragma acc exit data copyout (sxqprd[0:nx*nprq])
      #pragma acc exit data copyout (sqprd[0:nv*nprq])
      #pragma acc exit data copyout (sdxdxprd[0:nx*nx*nprq])
      #pragma acc exit data copyout (sdqdxprd[0:nx*nv*nprq])
      #pragma acc exit data copyout (sauxprd[0:naux*nprq])
      #pragma acc exit data copyout (srhsprd[0:nv*nprq])
      #pragma acc exit data copyout (sxprd[0:nx*nprq])
      #pragma acc exit data copyout (swnprd[0:(nx+1)*nprq])
      #pragma acc exit data copyout (swxdprd[0:nprq])
      #pragma acc exit data copyout (sauxfprd[0:nauxf*nprq])
      #pragma acc exit data copyout (sdqprd[0:nv*nprq])
      #pragma acc exit data copyout (sdauxprd[0:nv*nprq])
      #pragma acc exit data copyout (slhsprd[0:nlhs*nprq])

//boundary
      for( ig=0;ig<ng;ig++ )
     {
         #pragma acc exit data copyout (sxb[ig][0:nx*nbb[ig]])
         #pragma acc exit data copyout (sqb[ig][0:nv*nbb[ig]])
         #pragma acc exit data copyout (sauxb[ig][0:naux*nbb[ig]])
         #pragma acc exit data copyout (srhsb[ig][0:nv*nbb[ig]])
         #pragma acc exit data copyout (siqb[ig][0:nbb[ig]])
         #pragma acc exit data copyout (ibql[ig][0:nbb[ig]])
         #pragma acc exit data copyout (swnb[ig][0:(nx+1)*nbb[ig]])
         #pragma acc exit data copyout (swxdb[ig][0:nbb[ig]])
         #pragma acc exit data copyout (sauxfb[ig][0:nauxf*nbb[ig]])
         #pragma acc exit data copyout (siqdst[ig][0:2*ndst[ig]])
         #pragma acc exit data copyout (sibdst[ig][0:ndst[ig]])
         #pragma acc exit data copyout (sdqb[ig][0:nv*nbb[ig]])
         #pragma acc exit data copyout (sdauxb[ig][0:nv*nbb[ig]])
         #pragma acc exit data copyout (sresb[ig][0:nv*nbb[ig]])
         #pragma acc exit data copyout (sqb0[ig][0:nv*nbb[ig]])
         #pragma acc exit data copyout (sauxb0[ig][0:naux*nbb[ig]])
         #pragma acc exit data copyout (sxqb[ig][0:nx*nbb[ig]])
         #pragma acc exit data copyout (slhsb[ig][0:nlhs*nbb[ig]])
     }

//volume data
      #pragma acc exit data copyout (swq[0:(nx+1)*nq])
      #pragma acc exit data copyout (sxdq[0:nvel*nq] )
      #pragma acc exit data copyout (sxq[0:nx*nq])
      #pragma acc exit data copyout (sq[0:nv*nq])
      #pragma acc exit data copyout (sdxdx[0:nx*nx*nq])
      #pragma acc exit data copyout (sdqdx[0:nx*nv*nq])
      #pragma acc exit data copyout (saux[0:naux*nq])
      #pragma acc exit data copyout (srhs[0:nv*nq])
      #pragma acc exit data copyout (sxc[0:nx*nfc])
      #pragma acc exit data copyout (swnc[0:(nx+1)*nfc])
      #pragma acc exit data copyout (swxdc[0:nfc])
      #pragma acc exit data copyout (sauxf[0:nauxf*nfc])
      #pragma acc exit data copyout (sdq[0:nv*nq])
      #pragma acc exit data copyout (sdaux[0:nv*nq])
      #pragma acc exit data copyout (sres[0:nv*nq])
      #pragma acc exit data copyout (sdst[0:2*nq] )
      #pragma acc exit data copyout (slhsa[0:nlhs*nq])
      #pragma acc exit data copyout (su[0][0:nv*nq])
      #pragma acc exit data copyout (su[1][0:nv*nq])
      #pragma acc exit data copyout (su[2][0:nv*nq])


//face connection
      #pragma acc exit data copyout (sifq[0:2*nfc])

//arrays to compute weights
      #pragma acc exit data copyout (sxp[0:nx*np])
      for( Int iek=0;iek<nek;iek++ )
     {
         #pragma acc exit data copyout (siep[iek][0:nep[iek]*ne[iek]])
         #pragma acc exit data copyout (sieq[iek][0:neq[iek]*ne[iek]])
     }
      #pragma acc exit data copyout (sxdp[0:nvel*np])
      for( Int ibk=0;ibk<nbk;ibk++ )
     {
         #pragma acc exit data copyout (sicp[ibk][0:nbp[ibk]*nc[ibk]])
         #pragma acc exit data copyout (sicc[ibk][0:1*nc[ibk]])
     }
      for( Int ig=0;ig<ng;ig++ )
     {
         #pragma acc exit data copyout (sxbp[ig][0:nx*npb[ig]])
         #pragma acc exit data copyout (sxdbp[ig][0:nvel*npb[ig]])
         for( Int ibk=0;ibk<nbk;ibk++ )
        {
            Int *sipbp_tmp, *sibb_tmp;

            sipbp_tmp = sipbp[ig][ibk];
            sibb_tmp = sibb[ig][ibk];

            //if I do sibp[ig][ibk][0:nbp[ibk]*nb[ig][ibk]], openACC is complaining about partial present
            //in the device
            #pragma acc exit data copyout (sipbp_tmp[0:nbp[ibk]*nb[ig][ibk]])
            #pragma acc exit data copyout (sibb_tmp[0:nbd[ibk]*nb[ig][ibk]])
        }
     }
      for( Int ibk=0;ibk<nbk;ibk++ )
     {
         #pragma acc exit data copyout (siprdp[ibk][0:nbp[ibk]*nprb[ibk]])
         #pragma acc exit data copyout (siprc[ibk][0:nbq[ibk]*nprb[ibk]])
     }

//frame speed
     #pragma acc exit data copyout(omegb[0:ng])

//classes
      #pragma acc exit data copyout (this)
      cout << "-----------------exit acc device---------------------------\n";
   }
