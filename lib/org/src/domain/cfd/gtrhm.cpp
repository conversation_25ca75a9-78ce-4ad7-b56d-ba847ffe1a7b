
using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::gtrhm( cAu3xView<Real>& r )
  {
      Int iqs,iqe, ibs,ibe, ics,ice, id, ids,ide;
      Int nb,nc;
      Int ix,ig,ic,iv,iq,iql,iqr,ib,ia;
      Int ick;
      
      Real wcfl,dt;

      //grads( dqdx );
      grads();
      if( vsc->viscous() ){

         for( ig=0;ig<ng;ig++ )
        {
            bdf[ig]->exchange( sauxb[ig] );
        }
//         #pragma acc enter data copyin ( iprq0[0:nprq],iprq1[0:nprq])
//         for( ig=0;ig<ng;ig++ )
//        {
//            #pragma acc enter data copyin( siqdst[ig][0:2*ndst[ig]],sibdst[ig][0:ndst[ig]],sdst[0:2*nq] )
//            #pragma acc enter data copyin( sxb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],srhsb[ig][0:nv*nbb[ig]],\
//                                           siqb[ig][0:nbb[ig]],swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]], \
//                                           sauxfb[ig][0:nauxf*nbb[ig]])
//        }
//         #pragma acc enter data copyin (sxqprd[0:nx*nprq],sqprd[0:nv*nprq],sauxprd[0:naux*nprq],sdqdxprd[0:nx*nv*nprq],srhsprd[0:nv*nprq],\
//                                        sxprd[0:nx*nprq],swnprd[0:(nx+1)*nprq],swxdprd[0:nprq], sauxfprd[0:nauxf*nprq])
//         #pragma acc enter data copyin (sifq[0:2*nfc],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],srhs[0:nv*nq],sxc[0:nx*nfc], \
//                                        swnc[0:(nx+1)*nfc], swxdc[0:nfc], sauxf[0:nauxf*nfc])
//         #pragma acc enter data copyin ( this)
//         start_acc_device();
         while( dev->transit() )
        {

// auxiliary variables for the degrees of freedom
            for( ig=0;ig<ng;ig++ )
           {
               dsd[ig]->range( dev->avail(), &ids,&ide );
               //fld->yplus( ids,ide, iqdst[ig][0], xq, q, aux, ibdst[ig][0], qb[ig],auxb[ig], dst );
               //#pragma acc enter data copyin( siqdst[ig][0:2*ndst[ig]],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sibdst[ig][0:ndst[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],sdst[0:2*nq] )
               fld->yplus( ids,ide, iqdst[ig], xq, q, aux, ibdst[ig], qb[ig],auxb[ig], dst );
               //#pragma acc exit data copyout( saux[0:naux*nq])
               //#pragma acc exit data delete( siqdst[ig][0:2*ndst[ig]],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sibdst[ig][0:ndst[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],sdst[0:2*nq] )
               //#pragma acc exit data copyout( siqdst[ig][0:2*ndst[ig]],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sibdst[ig][0:ndst[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],sdst[0:2*nq] )

           }
        }

         dof->exchange( sdst );
         while( dev->transit() )
        {

            dof->range( dev->avail(), &iqs,&iqe );
            //fld->maux( iqs,iqe, xq,q,dst,dqdx, aux, lmixmax );//;, ng,NULL,NULL,NULL,NULL );  // agruments after ng to be removed
//            #pragma acc enter data copyin( sxq[0:nx*nq],sq[0:nv*nq],sdst[0:2*nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq] )
            fld->maux( iqs,iqe,xq,q,dst,dqdx,aux );//;, ng,NULL,NULL,NULL,NULL );  // agruments after ng to be removed
//            #pragma acc exit data copyout( sxq[0:nx*nq],sq[0:nv*nq],sdst[0:nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq] )

// boundary faces

            for( ig=0;ig<ng;ig++ )
           {
               bdf[ig]->range( dev->avail(), &ibs,&ibe );
               //bbj[ig]->mflx( ibs,ibe, xb[ig],qb[ig],auxb[ig],rhsb[ig], iqb[ig], xq,q,aux,r, wnb[ig],wxdb[ig],auxfb[ig] );
//               #pragma acc enter data copyin( sxb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],srhsb[ig][0:nv*nbb[ig]],\
//                                              siqb[ig][0:nbb[ig]],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],\
//                                              srhs[0:nv*nq],swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]], \
//                                              sauxfb[ig][0:nauxf*nbb[ig]])
               bbj[ig]->mflx( ibs,ibe, xb[ig],qb[ig],auxb[ig],rhsb[ig],iqb_view[ig],xq,q,aux,rhs,wnb[ig],wxdb[ig],auxfb[ig] );
//               #pragma acc exit data copyout( sxb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],srhsb[ig][0:nv*nbb[ig]],\
//                                              siqb[ig][0:nbb[ig]],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],\
//                                              srhs[0:nv*nq],swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]], \
//                                              sauxfb[ig][0:nauxf*nbb[ig]])
           }

// periodic faces

         prd->range( dev->avail(), &ics,&ice );
         //setv( ics,ice, nv, 0.,rhsprd );
         setv( ics,ice, nv, ZERO,rhsprd,"d" );
        #pragma acc parallel loop \
         present(sauxprd[0:naux*nprq],saux[0:naux*nq],siprq1[0:nprq],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iq= siprq1[ic];
            for( iv=0;iv<naux;iv++ )
           {
               sauxprd[ADDR(iv,ic,nprq)]= saux[ADDR(iv,iq,nq)];
           }
        }
         //coo->coffset( ics,ice, -1.,       iprq[1],xq,                      NULL, xqprd );
         coo->coffset( ics,ice, -1.,       iprq1,xq,                      NULL_iview, xqprd );
         //fld->voffset( ics,ice, -1, iprq[1],  q, NULL,  qprd );
         fld->voffset( ics,ice, -1, iprq1,  q, NULL_iview,  qprd );
         //fld->goffset( ics,ice, -1., iprq[1], dqdx, NULL, dqdxprd );
         fld->goffset( ics,ice, -1., iprq1, dqdx, NULL_iview, dqdxprd );
//          #pragma acc enter data copyin (sxqprd[0:nx*nprq],sqprd[0:nv*nprq],sauxprd[0:naux*nprq],sdqdxprd[0:nx*nv*nprq],srhsprd[0:nv*nprq],\
//                                         iprq0[0:nprq],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],srhs[0:nv*nq], \
//                                         sxprd[0:nx*nprq],swnprd[0:(nx+1)*nprq], swxdprd[0:nprq], sauxfprd[0:nauxf*nprq])
         fld->mflx( ics,ice, NULL_iview,    xqprd,qprd,auxprd,dqdxprd,rhsprd, 
                             iprq0,         xq,   q,   aux,   dqdx,   rhs, 
                             xprd,wnprd,wxdprd,auxfprd );
//          #pragma acc exit data copyout (srhsprd[0:nv*nprq],srhs[0:nv*nq], sauxfprd[0:nauxf*nprq])
//          #pragma acc exit data delete (sxqprd[0:nx*nprq],sqprd[0:nv*nprq],sauxprd[0:naux*nprq],sdqdxprd[0:nx*nv*nprq],srhsprd[0:nv*nprq],\
//                                         iprq0[0:nprq],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],srhs[0:nv*nq], \
                                         sxprd[0:nx*nprq],swnprd[0:(nx+1)*nprq], swxdprd[0:nprq], sauxfprd[0:nauxf*nprq])
         //fld->roffset( ics,ice,  1, NULL, rhsprd, NULL, rhsprd );
         fld->roffset( ics,ice,  1, rhsprd );
        #pragma acc parallel loop \
         present(srhsprd[0:nv*nprq],srhs[0:nv*nq],siprq1[0:nprq],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iq= siprq1[ic];
            for( iv=0;iv<nv;iv++ )
           {
               //rhs[iv][iq]+= rhsprd[iv][ic];
               srhs[ADDR(iv,iq,nq)]+= srhsprd[ADDR(iv,ic,nprq)];
           }
        }

// Inviscid fluxes - inner faces

            cnf->range( dev->avail(), &ics,&ice );
            //fld->mflx( ics,ice, ifq[0], xq,q,aux,dqdx,r, ifq[1], xq,q,aux,dqdx,r, xc,wnc,wxdc,auxf );
//            #pragma acc enter data copyin (sifq[0:2*nfc],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],srhs[0:nv*nq],sxc[0:nx*nfc], \
//                                           swnc[0:(nx+1)*nfc], swxdc[0:nfc], sauxf[0:nauxf*nfc])
            fld->mflx( ics,ice, ifq, xq,q,aux,dqdx,rhs,xc,wnc,wxdc,auxf );
//            #pragma acc exit data copyout (srhs[0:nv*nq],sauxf[0:nauxf*nfc])
//            #pragma acc exit data delete (sifq[0:2*nfc],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],srhs[0:nv*nq],sxc[0:nx*nfc], \
//                                           swnc[0:(nx+1)*nfc], swxdc[0:nfc], sauxf[0:nauxf*nfc])

        } 

//         #pragma acc exit data copyout ( iprq0[0:nprq],iprq1[0:nprq])
//         #pragma acc exit data copyout( siqdst[ig][0:2*ndst[ig]],sibdst[ig][0:ndst[ig]],sdst[0:2*nq] )
//         for( ig=0;ig<ng;ig++ )
//        {
//            #pragma acc exit data copyout( sxb[ig][0:nx*nbb[ig]],sqb[ig][0:nv*nbb[ig]],sauxb[ig][0:naux*nbb[ig]],srhsb[ig][0:nv*nbb[ig]],\
//                                           siqb[ig][0:nbb[ig]],swnb[ig][0:(nx+1)*nbb[ig]],swxdb[ig][0:nbb[ig]], \
//                                           sauxfb[ig][0:nauxf*nbb[ig]])
//        }
//         #pragma acc exit data copyout (sxqprd[0:nx*nprq],sqprd[0:nv*nprq],sauxprd[0:naux*nprq],sdqdxprd[0:nx*nv*nprq],srhsprd[0:nv*nprq],\
//                                        sxprd[0:nx*nprq],swnprd[0:(nx+1)*nprq],swxdprd[0:nprq], sauxfprd[0:nauxf*nprq])
//         #pragma acc exit data copyout (sifq[0:2*nfc],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],srhs[0:nv*nq],sxc[0:nx*nfc], \
//                                        swnc[0:(nx+1)*nfc], swxdc[0:nfc], sauxf[0:nauxf*nfc])
//         #pragma acc exit data copyout ( this)
//         exit_acc_device();
     }
//      for(int iq=0; iq<nq; iq++)
//     {
//         for(iv=0; iv<nv; iv++)
//        {
//            cout << rhs(iv,iq) << " ";
//        }
//         cout << " gtrhm \n";
//     }
//exit(0);
  }
