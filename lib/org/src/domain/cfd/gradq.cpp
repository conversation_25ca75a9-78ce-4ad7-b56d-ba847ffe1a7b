   using namespace std;

#  include <domain/cfd/domain.h>


   void cFdDomain::gradq0( Int ics, Int ice, Int *icql, Real *xql[], Real *ql[], Real *dqdxl[][3],
                                             Int *icqr, Real *xqr[], Real *qr[], Real *dqdxr[][3] )
  {
      Real      *wrk[1];
      Int        ix,jx,iv,ic,iql,iqr;
      Real       d,w;

      if( ice > ics )
     {

         wrk[0]= new Real[ice];
         setv( ics,ice, 1,0., wrk );

         for( ix=0;ix<nx;ix++ )
        {
            for( ic=ics;ic<ice;ic++ )
           {
               iql= ic;
               iqr= ic;
               if( icql ){ iql= icql[ic]; }
               if( icqr ){ iqr= icqr[ic]; }
               d= xqr[ix][iqr]- xql[ix][iql];
               d*= d;
               wrk[0][ic]+= d;
           } 
        }
      
         for( ic=ics;ic<ice;ic++ )
        {
            wrk[0][ic]= 1./wrk[0][ic];
        }
      
         for( ix=0;ix<nx;ix++ )
        {
            for( iv=0;iv<nv;iv++ )
           {
               for( ic=ics;ic<ice;ic++ )
              {
                  iql= ic;
                  iqr= ic;
                  if( icql ){ iql= icql[ic]; }
                  if( icqr ){ iqr= icqr[ic]; }
                  w= wrk[0][ic];
                  d= w*(xqr[ix][iqr]-xql[ix][iql])*( qr[iv][iqr]-ql[iv][iql] );
                  dqdxl[iv][ix][iql]+= d;
                  dqdxr[iv][ix][iqr]+= d;
              }
           }
        }
         delete[] wrk[0];
     }
  }
