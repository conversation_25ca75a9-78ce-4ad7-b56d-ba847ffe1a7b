
   using namespace std;

#  include <domain/cfd/domain.h>

   void cFdDomain::pickle( size_t *len, pickle_t *buf )
  {
      Int       *tmpe,*tmpb,*tmpg;
      Int        ig;
      Int        iek,ibk,isk,ick,ivk;
      bool       val;

      cDomain::pickle( len,buf );

      pckle( len, ilev, buf );
      pckle( len, bcrs, buf );

      ivk= coo->gettype(); 
      pckle( len, ivk, buf );
      coo->pickle( len,buf );

      ivk= vsc->gettype(); 
      pckle( len, ivk, buf );
      vsc->pickle( len,buf );

      ivk= fld->gettype(); 
      pckle( len, ivk, buf );
      fld->pickle( len,buf );

      tmpb= new Int[nbk];
      for( ibk=0;ibk<nbk;ibk++ )
     {
         tmpb[ibk]= blm[ibk]->gettype();
     }
      tmpe= new Int[nek];
      for( iek=0;iek<nek;iek++ )
     {
         tmpe[iek]= elm[iek]->gettype();
     }
      tmpg= new Int[ng];
      for( ig=0;ig<ng;ig++ )
     {
         tmpg[ig]= bbj[ig]->gettype();
     }

      pckle(  len, nx,      buf );
      pckle(  len, nv,      buf );
      pckle(  len, naux,    buf );
      pckle(  len, nauxf,   buf );
      pckle(  len, nlhs,    buf );

      pckle(  len,nbk,tmpb, buf );
      pckle(  len,nek,tmpe, buf );
      pckle(  len, ng,tmpg, buf );
      pckle(  len, ng, nbl, buf );

      delete[] tmpe;
      delete[] tmpb;
      delete[] tmpg;


      pckle( len,niter,buf );
      pckle( len,npre,buf );
      pckle( len,npost,buf );
      pckle( len,nstep,buf );
      pckle( len,nout ,buf );
      pckle( len,cfl0 ,buf );
      pckle( len,cfl1 ,buf );
      pckle( len,dcfl ,buf );

      pckle( len,unst ,buf );
      pckle( len,dtm,  buf );
      pckle( len,ntlv, buf );

      pckle( len,spatial_order, buf );
      
      pckle( len,nstep_z, buf );
//      pckle( len,freq0, buf );
      pckle( len,nfre, buf );
      pckle( len,limtype, buf );
      pckle( len,limfac, buf );
//      pckle( len,ibpa, buf );

      val= (dof);
      pckle(  len, val,     buf );
      if( val )
     {

         dof->pickle(  len, buf );
         pts->pickle(  len, buf );
         for( iek=0;iek<nek;iek++ )
        {
            eld[iek]->pickle(  len, buf );
        }
         cnf->pickle( len,buf );
         for( ibk=0;ibk<nbk;ibk++ )
        {
            cnd[ibk]->pickle(  len, buf );
        }

         pckle( len,ipr0,buf );
         pckle( len,ipr1,buf );
         prd->pickle( len,buf );
         prp->pickle( len,buf );
         for( ibk=0;ibk<nbk;ibk++ )
        {
            pld[ibk]->pickle( len,buf );
        }

         for( ig=0;ig<ng;ig++ )
        {

            bdf[ig]->pickle(  len, buf );
            dsd[ig]->pickle(  len, buf );
            for( ibk=0;ibk<nbk;ibk++ )
           {
               bld[ig][ibk]->pickle(  len, buf );
           }
        }
     }
  }

   void cFdDomain::unpickle( size_t *len, pickle_t buf )
  {
      Int        ig,iek,ibk,isk,ick,ivk;
      Int        mek,mbk;
      Int       *tmpe=NULL,*tmpb=NULL,*tmpg=NULL,*tmpl=NULL; 
      cFElement *vel[MxNSk],*vbl[MxNSk];
      bool       val;

//    cout << "domain "<<this<<" unpickle\n";
      cDomain::unpickle( len,buf );

      unpckle( len, &ilev, buf );
      unpckle( len, &bcrs, buf );

      unpckle( len, &ivk, buf );
      coo= newcosystem( ivk );
      coo->unpickle( len,buf );

      unpckle( len, &ivk, buf );
      vsc= newvisc( ivk );
      vsc->unpickle( len,buf );

      unpckle( len, &ivk, buf );
      fld= newgas( ivk, coo,vsc );
      fld->unpickle( len,buf );

      unpckle(  len,& nx,buf );
      unpckle(  len,& nv,buf );
      unpckle(  len,& naux,buf );
      unpckle(  len,& nauxf,buf );
      unpckle(  len,& nlhs,buf );

      nx=    coo->getnx();
      nvel=  coo->getnvel();
      nv=    fld->getnv();
      naux=  fld->getnaux();
      nauxf= fld->getnauxf();
      nlhs=  fld->getnlhs();


      unpckle(  len,&mbk,&tmpb,buf ); 
      unpckle(  len,&mek,&tmpe,buf ); 
      unpckle(  len,&ng, &tmpg,buf );
      unpckle(  len,&ng, &tmpl,buf );

      for( ibk=0;ibk<mbk;ibk++ )
     { 
         vbl[ibk]= newelement( tmpb[ibk] ); 
     }
      for( iek=0;iek<mek;iek++ )
     { 
         vel[iek]= newelement( tmpe[iek] ); 
     }
      elements( mbk,vbl, mek,vel );

      bbj= new cFbndry*[ng];
      for( ig=0;ig<ng;ig++ )
     {
         bbj[ig]= newfbndry( tmpg[ig] );
         bbj[ig]->field( coo,fld );
         nbl[ig]= tmpl[ig];
     }
      

      delete[] tmpe; tmpe=NULL;
      delete[] tmpb; tmpb=NULL;
      delete[] tmpg; tmpg=NULL;
      delete[] tmpl; tmpl=NULL;

      unpckle( len,&niter,buf );
      unpckle( len,&npre,buf );
      unpckle( len,&npost,buf );
      unpckle( len,&nstep,buf );
      unpckle( len,&nout ,buf );
      unpckle( len,&cfl0 ,buf );
      unpckle( len,&cfl1 ,buf );
      unpckle( len,&dcfl ,buf );

      unpckle( len,&unst ,buf );
      unpckle( len,&dtm,  buf );
      unpckle( len,&ntlv, buf );

      unpckle( len,&spatial_order, buf );

      unpckle( len,&nstep_z, buf );
//      unpckle( len,&freq0, buf );
      unpckle( len,&nfre, buf );
      unpckle( len,&limtype, buf );
      unpckle( len,&limfac, buf );
//      unpckle( len,&ibpa, buf );

      unpckle(  len,&val,buf );

      if( val )
     {
         dof=new cPdata( dev );
         dof->unpickle(  len,buf );

         pts=new cPdata( dev );
         pts->unpickle(  len,buf );

         np= pts->size();
         nq= dof->size();

         for( iek=0;iek<nek;iek++ )
        {
            eld[iek]=new cPdata( dev );
            eld[iek]->unpickle(  len,buf );
            ne[iek]= eld[iek]->size();
        }

         cnf= new cPdata( dev );
         cnf->unpickle( len,buf );
         nfc= cnf->size();
         for( ibk=0;ibk<nbk;ibk++ )
        {
            cnd[ibk]= new cPdata( dev );
            cnd[ibk]->unpickle(  len, buf );
            nc[ibk]= cnd[ibk]->size();
        }

         unpckle( len,&ipr0,buf );
         unpckle( len,&ipr1,buf );
         prd= new cPdata( dev );
         prp= new cPdata( dev );
         prd->unpickle( len,buf );
         prp->unpickle( len,buf );
         nprq= prd->size();
         nprp= prp->size();
         for( ibk=0;ibk<nbk;ibk++ )
        {
            pld[ibk]= new cPdata( dev );
            pld[ibk]->unpickle( len,buf );
            nprb[ibk]= pld[ibk]->size();
        }

         for( ig=0;ig<ng;ig++ )
        {

/*          unpckle( len,&(bgnm[ig]),buf );
            unpckle( len, &(bpl[ig]),buf );
            unpckle( len, &(bpo[ig]),buf );*/

            bdf[ig]= new cPdata( dev );
            dsd[ig]= new cPdata( dev );
            bdf[ig]->unpickle( len,buf );
            dsd[ig]->unpickle( len,buf );
            nbb[ig]=  bdf[ig]->size();
            ndst[ig]= dsd[ig]->size();
            for( ibk=0;ibk<nbk;ibk++ )
           {
               bld[ig][ibk]=new cPdata( dev );
               bld[ig][ibk]->unpickle(  len,buf );
               nb[ig][ibk]= bld[ig][ibk]->size();
           }
        }
/*       if( ipr0 != -1 )
        {
            for( ibk=0;ibk<nbk;ibk++ )
           {
               nprb[ibk]= nb[ipr0][ibk];
           }
        }*/
     }
  }
