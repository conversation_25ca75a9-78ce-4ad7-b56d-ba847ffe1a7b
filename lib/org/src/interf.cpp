   using namespace std;

#  include <interf.h>
#  include <assert.h>

// define   NBUF 1000

   void cInterf::checkbuf( pickle_t buf )
  {
      size_t len;
      Int v0,v1,v2,v3;
      Real *sv,*v[3];

      return;

      len=0;
      unpckle( &len,&v0,buf ); 
      unpckle( &len,&v1,buf ); 
      unpckle( &len,&v2,buf ); 
      sv=NULL;
      cout << "buffer check "<<v0<<" "<<v1<<" "<<v2<<"\n";
      unpckle( &len,&v3,&sv,buf );
      subv( v0,v1, sv,v );
      for( Int i=0;i<v1;i++ )
     {
         for( Int j=0;j<v0;j++ )
        {
            cout << v[j][i]<<" ";
        }
         cout << "  (BUFFER CHECK)\n";
     }
      delete[] sv;
  }

   Real wrk;

   cInterf::cInterf()
  {
      ians=0;
      ldev=NULL;
      ireq=0;
      idst=NULL;
      irnk=0;
      ierr=0;
      itrm=0;
      rreq= NULL;
      rxreq0=NULL;
      rxreq1=NULL;
      sreq0= NULL;
      sreq1= NULL;
      sreq2= NULL;
      sreq3= NULL;
      sreq4= NULL;
      rwait= NULL;
      swait= NULL;
      rbuf= NULL;
      rlen= NULL;
      sbuf= NULL;
      slen= 0;
      plld= false;
      rlsd= true;
      werr= NULL;
      rxbuf = NULL;
  };

   cInterf::~cInterf()
  {
      Int i;
      ians=0;
      ireq=0;
      irnk=0;
      ierr=0;
      itrm=0;
      delete[] idst; idst=NULL;
      for( i=0;i<ndst;i++ )
     {
         delete[] rbuf[i]; rbuf[i]=NULL;
         delete[] abuf[i]; abuf[i]=NULL;
     }
      delete[] sbuf; sbuf=NULL;
      delete[] abuf; abuf=NULL;
      delete[] rbuf; rbuf=NULL;
      delete[] rlen; rlen=NULL;
      delete[] xlen; xlen=NULL;
      delete[] rreq; rreq=NULL;
      delete[] rxreq0; rxreq0=NULL;
      delete[] rxreq1; rxreq1=NULL;
      delete[] sreq0; sreq0= NULL;
      delete[] sreq1; sreq1= NULL;
      delete[] sreq2; sreq2= NULL;
      delete[] sreq3; sreq3= NULL;
      delete[] sreq4; sreq4= NULL;
      delete[] rwait; rwait= NULL;
      delete[] swait; swait= NULL;
      delete[] hwait; hwait= NULL;
      delete[] bxbuf; bxbuf= NULL;
//    MPI_Barrier( pcom );
      if(bposix) ldev->lock();
      MPI_Barrier( pcom );
      MPI_Comm_free( &pcom );
      if(bposix) ldev->unlock();
      ndst= 0;
      ldev=NULL;
      plld=false;
      rlsd=false;
      delete[] werr; werr= NULL;
      delete[] rxbuf; rxbuf=NULL;
  };

   void *intfthrf( void *data )
  {
      cInterf *intf;
      intf= (cInterf*)data;
      intf->listen();
  }

   void cInterf::boot()
  {
      lstn=true;
      pthread_attr_init(&attr);
      pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_JOINABLE);
      pthread_create( &tid,&attr,intfthrf,(void*)this);
      pthread_attr_destroy(&attr);

  }

   void cInterf::wait( MPI_Request *req )
  {
      Int          flag;
      MPI_Status   stat;

      milliseconds(IDLET);
      flag=-1;
      do 
     {
         if(bposix)
        {
           ldev->lock();
           MPI_Test( req,&flag,&stat );
           ldev->unlock();
        }
         else
        {
           MPI_Test( req,&flag,&stat );
        }
         milliseconds(IDLET);

     }while(!flag);

      if(bposix)
     { 
        ldev->lock();
        MPI_Wait( req,&stat );
        ldev->unlock();
     }
      else
     {
        MPI_Wait( req,&stat );
     }
  }

   void cInterf::wait( MPI_Request *req, Int *flag )
  {
      MPI_Status   stat;

     *flag=-1;

      if(bposix)
     {
        ldev->lock();
        MPI_Test( req,flag,&stat );
        if( *flag )
       {
           MPI_Wait( req,&stat );
       }
        ldev->unlock();
     }
      else
     {
        MPI_Test( req,flag,&stat );
        if( *flag )
       {
           MPI_Wait( req,&stat );
       }
     }
  }

   void cInterf::listen()
  {

      Int          i;
      Int          flag;
      MPI_Status   stat;
      MPI_Request  req;

      if( !ldev ){ return; };

      do
     {

         ldev->lock();
         MPI_Irecv( &i,1,MPI_INTEGER,MPI_ANY_SOURCE,tag,pcom,&req);
         ldev->unlock();

         wait( &req );

         service( i );

     }while( i != -1 );
      itrm++;

      ldev->lock();
      MPI_Request_free( &req ); req= MPI_REQUEST_NULL;
      ldev->unlock();
      lstn=false;
  }

   void cInterf::halt()
  {

      Int  i,j;
      i=-1;

      if( irnk == ildr )
     {
         rlsd= false;
         ldev->lock();
         for( j=0;j<ndst;j++ )
        {
            MPI_Isend( &i,1,MPI_INTEGER,idst[j],tag,pcom,sreq4+j );
            hwait[j]= true;
            fle << name <<" "<< irnk<<" sent halt signal to "<<idst[j]<<"\n";
            fle.flush();
        }
         ldev->unlock();
     }
      else
     {
         setv( 0,ndst, (bool)false,hwait );
     }
  }

   void cInterf::halt2()
  {
      cout << "mutex free halt\n";
      Int  i,j;
      i=-1;

      if( irnk == ildr )
     {
         rlsd= false;
         for( j=0;j<ndst;j++ )
        {
            MPI_Isend( &i,1,MPI_INTEGER,idst[j],tag,pcom,sreq4+j );
            hwait[j]= true;
            fle << name <<" "<< irnk<<" sent halt signal to "<<idst[j]<<"\n";
            fle.flush();
        }
     }
      else
     {
         setv( 0,ndst, (bool)false,hwait );
     }
  }

   void cInterf::join()
  {
      cout << "I should not go here====================\n";
      void *stat;
      pthread_join(tid, &stat);

      fle << name <<" "<< irnk <<" "<<tag<<" received "<<ians<<" and sent "<<ireq<<" messages with "<<ierr<<" errors "<<
              " and "<< itrm <<" termination signals\n"; fle.flush();
      fle << tid<<" JOINED\n";
  }



   void cInterf::build( Int i0, cDevice *dev0, string str0, Int i1, cDevice *dev1, string str1, Int itag, bool bvar )
  {
      Int      *ranks;
      cDevice  *rdev;
      MPI_Group world,local;
      MPI_Comm *lcom;
      Int       il,ir;
      Int       nl,nr,nn;
      Int       n,i,n0,n1;

/*    dev0->lock();
      dev1->lock();

      MPI_Barrier( MPI_COMM_WORLD );
      MPI_Comm_rank( MPI_COMM_WORLD, &i );*/

      bposix = bvar;

      n0=   dev0->getncpu();
      n1=   dev1->getncpu();
      nn=   n0+n1;
      ranks= new Int[nn];

      Int j=0; 
      for( i=0;i<dev0->getncpu();i++ )
     {
         ranks[j]= dev0->geticpu(i);
         j++;
     }
      for( i=0;i<dev1->getncpu();i++ )
     {
         ranks[j]= dev1->geticpu(i);
         j++;
     }

      MPI_Comm_group( MPI_COMM_WORLD,&world );
      MPI_Group_incl( world, nn,ranks, &local );
      Int v= MPI_Comm_create( MPI_COMM_WORLD, local, &pcom );
      MPI_Group_free( &world );
      MPI_Group_free( &local );

      delete[] ranks;
      tag= 10000;

      //work out the size of the coordinate message they will send to each other
      // the maximum size of both message is selected

/*    dev0->unlock();
      dev1->unlock();*/

      if( dev0->resides() || dev1->resides() )
     {
         cout << "dev0 " << dev0 << " dev1 " << dev1 << "\n";
         ldev= dev0;
         rdev= dev1;
         il=   i0;
         ir=   i1;
         ildr= 0;
         irem= n0;
         lcom= dev0->getcomm();
         if( dev1->resides() ) 
        {
            lcom= dev1->getcomm();
            swap( &ldev,&rdev );
            swap( &il,&ir );
            swap( &ildr,&irem );
        } 
         nl= ldev->getncpu(); 
         nr= rdev->getncpu(); 
         ib= il;

         if(bposix) ldev->lock();
         MPI_Comm_rank( pcom,&irnk );
         MPI_Comm_size( pcom,&nrnk );

         Int bcst=itag;
         MPI_Bcast( &bcst,1,MPI_Int,0,pcom );
         name= dev0->getname()+":"+str0+"-"+dev1->getname()+":"+str1;
         string fnme= name+ "."+ strc(irnk)+".log";
//         fle.open( fnme.c_str() ); 

         ndst= nr;
         idst= new Int[ndst];
         for( i=0;i<ndst;i++ ) 
        {
            idst[i]=i;
        }
         if( dev0->resides() )
        {
            for( i=0;i<ndst;i++ )
           {
               idst[i]+= nl;
           }
        }

         ldev->interface( this );
         if(bposix) mtx= ldev->mutex();
         rreq= new  MPI_Request[ndst];
         rxreq0= new  MPI_Request[ndst];
         rxreq1= new  MPI_Request[ndst];
         sreq0= new MPI_Request[ndst];
         sreq1= new MPI_Request[ndst];
         sreq2= new MPI_Request[ndst];
         sreq3= new MPI_Request[ndst];
         sreq4= new MPI_Request[ndst];
         rwait= new bool[ndst];
         swait= new bool[ndst];
         hwait= new bool[ndst];
         bxbuf= new bool[ndst];
         rbuf= new pickle_t[ndst];
         rlen= new size_t[ndst];
         xlen= new size_t[ndst];
         abuf= new pickle_t[ndst];
         rxbuf= new pickle_t[ndst];
         setv( 0,ndst, (pickle_t)NULL, rbuf );
         setv( 0,ndst, (pickle_t)NULL, abuf );
         setv( 0,ndst, (pickle_t)NULL, rxbuf );
         setv( 0,ndst, (MPI_Request)MPI_REQUEST_NULL, sreq0 );
         setv( 0,ndst, (MPI_Request)MPI_REQUEST_NULL, sreq1 );
         setv( 0,ndst, (MPI_Request)MPI_REQUEST_NULL, sreq2 );
         setv( 0,ndst, (MPI_Request)MPI_REQUEST_NULL, sreq3 );
         setv( 0,ndst, (MPI_Request)MPI_REQUEST_NULL, sreq4 );
         setv( 0,ndst, (MPI_Request)MPI_REQUEST_NULL, rxreq0 );
         setv( 0,ndst, (MPI_Request)MPI_REQUEST_NULL, rxreq1 );
         slen= 0;
         sbuf= NULL;
         if(bposix) ldev->unlock();
     }
      if(bposix) dev0->lock();
      if(bposix) dev1->lock();
      MPI_Barrier( MPI_COMM_WORLD );
      if(bposix) dev1->unlock();
      if(bposix) dev0->unlock();
  }

   void cInterf::request()
  {
      Int          flag;
      MPI_Status   stat;
      Real        *sx;
      
      Int i;
      size_t len;

      plld= false;

      slen=0;
      delete[]  sbuf; sbuf=NULL;
      delete[]  werr; werr= NULL;

      nx= -1;
      nq= -1;
      nv= -1;
      ldev->request( ib, &nx,&nq,&nv, &sx );

      Real *x[10];
      setv( 0,10, (Real*)NULL, x );
      subv( nx,nq,sx,x );
      //fle << "coordinates\n";
      //for( Int iq=0;iq<nq;iq++ )
     //{
     //    for( Int ix=0;ix<nx;ix++ )
     //   {
     //       fle << x[ix][iq]<<" ";
     //   } 
     //    fle<<"\n";
     //}
      
      if( nq > 0 )
     {

         pckle( &slen,nx,      &sbuf );
         pckle( &slen,nq,      &sbuf );
         pckle( &slen,nv,      &sbuf );
         pckle( &slen,nx*nq,sx,&sbuf );

         checkbuf( sbuf );

         werr= new Real[nq]; setv( 0,nq, big, werr );

         for( i=0;i<ndst;i++ )
        {

            ldev->lock();

            MPI_Isend( &irnk,   1, MPI_INTEGER, idst[i],tag+0,pcom,sreq0+i );
            MPI_Isend( &slen,   1, MPI_LONG,    idst[i],tag+2,pcom,sreq1+i );
            Int islen=slen;
            MPI_Isend(  sbuf,islen,MPI_BYTE,    idst[i],tag+4,pcom,sreq2+i );

            ldev->unlock();
   


            delete[] abuf[i]; abuf[i]=NULL;
            len= nq*(nv+1)*sizeof(*sx)+sizeof(nq);
            abuf[i]= new pickle_v[len];

            ldev->lock();
            MPI_Irecv( abuf[i],len,MPI_BYTE,idst[i],tag+1,pcom,rreq+i);
            rwait[i]= true;
            swait[i]= true;
            ldev->unlock();

            fle << name <<" "<< irnk << " sent request for "<<(Int)slen<<" byets "<<ireq<<" to "<<idst[i]<<" (expected reply "<<len<<")\n";
            fle.flush();

        }
         ireq++;
     }
      else
     {
         pckle( &slen,nx,      &sbuf );
         pckle( &slen,nq,      &sbuf );
         pckle( &slen,nv,      &sbuf );
         pckle( &slen,nx*nq,sx,&sbuf );

         werr= new Real[nq]; setv( 0,nq, big, werr );

         for( i=0;i<ndst;i++ )
        {

            ldev->lock();

            MPI_Isend( &irnk,   1, MPI_INTEGER, idst[i],tag+0,pcom,sreq0+i );
            MPI_Isend( &slen,   1, MPI_LONG,    idst[i],tag+2,pcom,sreq1+i );
            Int islen=slen;
            MPI_Isend(  sbuf,islen,MPI_BYTE,    idst[i],tag+4,pcom,sreq2+i );

            ldev->unlock();
   


            ldev->lock();
            rwait[i]= false;
            swait[i]= true;
            ldev->unlock();

        }
         ireq++;
         fle << name <<" "<< irnk << " does not need data on this interface\n";
         fle.flush();
     }
      delete[] sx; sx=NULL;

  }

   void cInterf::request2()
  {
      //cout << "mutex free request\n";
      Int          flag;
      MPI_Status   stat;
      Real        *sx;
      
      Int i;
      size_t len;

      plld= false;

      slen=0;
      delete[]  sbuf; sbuf=NULL;
      delete[]  werr; werr= NULL;

      nx= -1;
      nq= -1;
      nv= -1;
      ldev->request( ib, &nx,&nq,&nv, &sx );

      if( nq > 0 )
     {

         pckle( &slen,nx,      &sbuf );
         pckle( &slen,nq,      &sbuf );
         pckle( &slen,nv,      &sbuf );
         pckle( &slen,nx*nq,sx,&sbuf );

         //cout << "the size of the coordinate message from " << irnk << " is " << slen << "\n";

         checkbuf( sbuf );

         werr= new Real[nq]; setv( 0,nq, big, werr );

         for( i=0;i<ndst;i++ )
        {
            //send coordinates to other devices
            //MPI_Isend( &irnk,   1, MPI_INTEGER, idst[i],tag+0,pcom,sreq0+i );
            MPI_Isend( &slen,   1, MPI_LONG,    idst[i],tag+2,pcom,sreq1+i );
            Int islen=slen;
            MPI_Isend(  sbuf,islen,MPI_BYTE,    idst[i],tag+4,pcom,sreq2+i );


            //receive the size of the coordinates from other devices
            MPI_Irecv( xlen+i,1,MPI_LONG,idst[i], tag+2, pcom, rxreq0+i);
            bxbuf[i] = false;

            delete[] abuf[i]; abuf[i]=NULL;
            len= nq*(nv+1)*sizeof(*sx)+sizeof(nq);
            abuf[i]= new pickle_v[len];
            //cout << irnk << " plan to recerve boundary from " << idst[i] << " values with size " << len << "\n";

            //receive boundary values from other devices
            MPI_Irecv( abuf[i],len,MPI_BYTE,idst[i],tag+1,pcom,rreq+i);
            rwait[i]= true;
            swait[i]= true;

            fle << name <<" "<< irnk << " sent request for "<<(Int)slen<<" byets "<<ireq<<" to "<<idst[i]<<" (expected reply "<<len<<")\n";
            //cout << name <<" "<< irnk << " sent request for "<<(Int)slen<<" byets "<<ireq<<" to "<<idst[i]<<" (expected reply "<<len<<")\n";
            fle.flush();

        }
         ireq++;

     }
      else
     {
         pckle( &slen,nx,      &sbuf );
         pckle( &slen,nq,      &sbuf );
         pckle( &slen,nv,      &sbuf );
         pckle( &slen,nx*nq,sx,&sbuf );

         werr= new Real[nq]; setv( 0,nq, big, werr );

         for( i=0;i<ndst;i++ )
        {

            //MPI_Isend( &irnk,   1, MPI_INTEGER, idst[i],tag+0,pcom,sreq0+i );
            MPI_Isend( &slen,   1, MPI_LONG,    idst[i],tag+2,pcom,sreq1+i );
            Int islen=slen;
            MPI_Isend(  sbuf,islen,MPI_BYTE,    idst[i],tag+4,pcom,sreq2+i );

            //receive the size of the coordinates from other devices
            MPI_Irecv( xlen+i,1,MPI_LONG,idst[i], tag+2, pcom, rxreq0+i);
            bxbuf[i] = false;

            bxbuf[i] = false;

            rwait[i]= false;
            swait[i]= true;

        }
         ireq++;
         fle << name <<" "<< irnk << " does not need data on this interface\n";
         fle.flush();
     }
      delete[] sx; sx=NULL;

  }
   
   void cInterf::service( Int id )
  {
      Int          jd;
      Int          flag;
      MPI_Status   stat;
      MPI_Request  lreq,xreq;
      Int          rnx,rnq,rnv,rnn;
      Real        *sx=NULL;
      Real        *x[4];
      Real        *sq=NULL;
      size_t       len=0;
      pickle_t     buf=NULL;
      Int i,j;
      if( id < 0 ){ return; }


      jd= inlst( id, ndst,idst );
      ldev->lock();
      MPI_Wait( sreq3+jd,&stat );
      MPI_Irecv( &len,1,MPI_LONG,id,tag+2,pcom,&lreq);
      ldev->unlock();

      wait( &lreq );
      size_t l0=len;

      buf= new pickle_v[len];
      ldev->lock();
      MPI_Irecv( buf,len,MPI_BYTE,id,tag+4,pcom,&xreq);
      ldev->unlock();

      wait( &xreq );

      len=0;
      unpckle( &len,&rnx,buf );
      unpckle( &len,&rnq,buf );
      unpckle( &len,&rnv,buf );

      sx= NULL;
      unpckle( &len,&rnn,&sx,buf );
      subv( rnx,rnq, sx,x );
      delete[] buf; buf=NULL;
      len=0;

      sq=NULL;

      ldev->ulock();
      ldev->qlock();
      ldev->service( ib, rnx,rnv,rnq, x,&sq, true );
      ldev->qunlock();
      ldev->uunlock();

      ldev->lock();
      swait[jd]=false;
      ldev->unlock();
      //cout << "after service, receives \n";

               /*Real **q= new Real*[rnv+1];
               subv( rnv+1,rnq,sq,q );
               for( int k=0;k<rnq;k++ )
              {
                  for( int j=0;j<rnx+1;j++ )
                 {
                     fle << x[j][k]<<" ";
                 }
                  for( int j=0;j<rnv+1;j++ )
                 {
                     fle << q[j][k]<<" ";
                 }
                  fle << "\n";
              }
               delete[] q; q=NULL;*/

      len= 0;
      delete[] sx; sx=NULL;
      delete[] rbuf[jd]; rbuf[jd]=NULL;

      if( rnq > 0 )pckle( &len,rnq*(rnv+1),sq, rbuf+jd );
      delete[] sq;

      ldev->lock();
      if( rnq > 0 )MPI_Isend(rbuf[jd],(Int)len,MPI_BYTE,id,tag+1,pcom,sreq3+jd );
      ldev->unlock();
      fle << name <<" "<< irnk<<" servicing request of length "<<l0<<" from "<<id<<" (reply length "<<len<<")\n";
      fle.flush();
      ians++;
  }

   void cInterf::service2( Int id )
  {
      //cout << "mutex free service start\n";
      Int          jd;
      Int          flag;
      MPI_Status   stat;
      MPI_Request  lreq,xreq, rkreq;
      Int          rnx,rnq,rnv,rnn;
      Real        *sx=NULL;
      Real        *x[4];
      Real        *sq=NULL;
      size_t       len=0;
      Int i,j;

      jd= inlst( id, ndst,idst );

      len=0;
      unpckle( &len,&rnx,rxbuf[jd] );
      unpckle( &len,&rnq,rxbuf[jd] );
      unpckle( &len,&rnv,rxbuf[jd] );

      sx= NULL;
      unpckle( &len,&rnn,&sx,rxbuf[jd] );
      subv( rnx,rnq, sx,x );
      len=0;

      sq=NULL;

      //ldev->service( ib, rnx,rnv,rnq, x,&sq, false );
      ldev->service( ib, rnx,rnv,rnq, sx,&sq );
      //cout << "mutex free service done with getting boundary values\n";

      swait[jd]=false;
      //cout << "after service, receives \n";
      //
              /* Real **q= new Real*[rnv+1];
               subv( rnv+1,rnq,sq,q );
               for( int k=0;k<rnq;k++ )
              {
                  for( int j=0;j<rnx;j++ )
                 {
                     fle << x[j][k]<<" ";
                 }
                  for( int j=0;j<rnv+1;j++ )
                 {
                     fle << q[j][k]<<" ";
                 }
                  fle << " service2 \n";
              }
               delete[] q; q=NULL;*/

      len= 0;
      delete[] sx; sx=NULL;
      delete[] rbuf[jd]; rbuf[jd]=NULL;

      if( rnq > 0 )pckle( &len,rnq*(rnv+1),sq, rbuf+jd );
      delete[] sq;

      //cout << id << " send back boundary values to idwith size " << rnq*(rnv+1) << "\n";
      if( rnq > 0 )MPI_Isend(rbuf[jd],(Int)len,MPI_BYTE,id,tag+1,pcom,sreq3+jd );
      //cout << "mutex free service done with sending information back \n";
      //fle << name <<" "<< irnk<<" servicing request of length "<<l0<<" from "<<id<<" (reply length "<<len<<")\n";
      fle << name <<" "<< irnk<<" servicing request of length from "<<id<<" (reply length "<<len<<")\n";
      fle.flush();
      ians++;

      //cout << irnk << " successfully serve " << id << "\n";

      return;
      //cout << "mutex free service end\n";
  }

   bool cInterf::poll()
  {

      Int          i,j,k;
      Int          nn;
      Int          flag;
      MPI_Status   stat;
      MPI_Request  req;
      bool         done;
      size_t       len;
      pickle_t     buf;
      Real        *sq=NULL;

      for( i=0;i<ndst;i++ )
     {
         if( rwait[i] )
        {
            wait( rreq+i,&flag );
            if( flag )
           { 
               ldev->lock();
               MPI_Wait( sreq0+i,&stat );
               MPI_Wait( sreq1+i,&stat );
               MPI_Wait( sreq2+i,&stat );
               MPI_Request_free( rreq+i ); rreq[i]= MPI_REQUEST_NULL;
               MPI_Request_free( sreq0+i ); sreq0[i]= MPI_REQUEST_NULL;
               MPI_Request_free( sreq1+i ); sreq1[i]= MPI_REQUEST_NULL;
               MPI_Request_free( sreq2+i ); sreq2[i]= MPI_REQUEST_NULL;
               ldev->unlock();
               len=0;
               unpckle( &len,&nn,&sq,abuf[i] );
               fle << name <<" "<< irnk << " "<<" receives answer from "<<idst[i]<<"\n";
               //cout << name <<" "<< irnk << " "<<" receives answer from "<<idst[i]<<"\n";
               fle.flush();
               rwait[i]=false; 

               Real **q= new Real*[nv+1];
               subv( nv+1,nq,sq,q );

              /*for( k=0;k<nq;k++ )
              {
                  for( j=0;j<nv+1;j++ )
                 {
                     fle << q[j][k]<<" ";
                 }
                  fle << "\n";
              }*/
              

               ldev->qlock();
               ldev->accept( ib, nv,nq, sq,werr );
               ldev->qunlock();

               delete[] abuf[i]; abuf[i]=NULL;
               delete[] sq; sq= NULL;
               delete[] q; q= NULL;
               
           }
        }
     }

      plld=true;
      for( i=0;i<ndst;i++ )
     {
         plld= plld && (!(rwait[i]));
         plld= plld && (!(swait[i]));
     }

      return plld;
  }

   bool cInterf::poll2()
  {
      //cout << "mutex free poll\n";
      Int          i,j,k;
      Int          nn;
      Int          flag;
      MPI_Status   stat;
      MPI_Request  req;
      bool         done;
      size_t       len;
      pickle_t     buf;
      Real        *sq=NULL;
      bool         btmp;

      for( i=0;i<ndst;i++ )
     {
         //check if needs message
         if(swait[i])
        {
            //check if the size of the message has arrived
            flag= -1;
            wait( rxreq0+i,&flag );
            if(flag)
           {
               //check if the buffer has beel allocated to accept the message
               if(!bxbuf[i])
              {
                 //cout << irnk << "allocate buffer to take coordinates from " << idst[i] << "\n";
                 delete[] rxbuf[i]; rxbuf[i]=NULL;
                 rxbuf[i]= new pickle_v[xlen[i]];
                 MPI_Irecv( rxbuf[i],xlen[i],MPI_BYTE,idst[i],tag+4,pcom, rxreq1+i);
                 bxbuf[i] = true;
              }

               flag= -1;
//             wait( rxreq1+i,&flag );

 //            if(flag)
//            {
                  //the message has arrived
                  //cout << irnk << " serve " << idst[i] << "\n";
//                MPI_Wait( rxreq0+i,&stat );
                  MPI_Wait( rxreq1+i,&stat );
//                MPI_Request_free( rxreq0+i ); rxreq0[i]= MPI_REQUEST_NULL;
//                MPI_Request_free( rxreq1+i ); rxreq1[i]= MPI_REQUEST_NULL;
    
                  service2(idst[i]);
                  swait[i]=false; 
//            }
           }
        }

         if( rwait[i] )
        {
            wait( rreq+i,&flag );
            if( flag )
           { 
               //cout << irnk << " has received the information from " << idst[i] << "\n";
               //MPI_Wait( sreq0+i,&stat );
               MPI_Wait( sreq1+i,&stat );
               MPI_Wait( sreq2+i,&stat );
//             MPI_Request_free( rreq+i ); rreq[i]= MPI_REQUEST_NULL;

               //MPI_Request_free( sreq0+i ); sreq0[i]= MPI_REQUEST_NULL;
//             MPI_Request_free( sreq1+i ); sreq1[i]= MPI_REQUEST_NULL;
//             MPI_Request_free( sreq2+i ); sreq2[i]= MPI_REQUEST_NULL;
               len=0;
               unpckle( &len,&nn,&sq,abuf[i] );
               fle << name <<" "<< irnk << " "<<" receives answer from " <<idst[i]<<"\n";
               //cout << name <<" "<< irnk << " "<<" receives answer from "<<idst[i]<<"\n";
               fle.flush();
               rwait[i]=false; 

               Real **q= new Real*[nv+1];
               subv( nv+1,nq,sq,q );

               //ldev->accept( ib, nv,nq, q,werr );
               ldev->accept( ib, nv,nq, sq,werr );

               delete[] abuf[i]; abuf[i]=NULL;
               delete[] sq; sq= NULL;
               delete[] q; q= NULL;
              
           }
        }
     }

      plld=true;
      for( i=0;i<ndst;i++ )
     {
         plld= plld && (!(rwait[i]));
         plld= plld && (!(swait[i])); 
         //cout << "rwait " << rwait[i] << " swait " << swait[i] << "\n";
     }

      return plld;
  }


   bool cInterf::released()
  {

      Int          i;
      Int          flag;
      MPI_Status   stat;

      if( irnk == ildr )
     {

         for( i=0;i<ndst;i++ )
        {
            if( hwait[i] )
           {
               wait( sreq4+i,&flag );
               if( flag )
              { 
                  ldev->lock();
                  MPI_Wait( sreq4+i,&stat );
                  MPI_Request_free( sreq4+i ); sreq4[i]= MPI_REQUEST_NULL;
                  ldev->unlock();
                  hwait[i]= false;
              }
           }
        }

         rlsd= true;
         for( i=0;i<ndst;i++ )
        {
            rlsd= rlsd && (!(hwait[i]));
        }
     }

      return rlsd;
  }

   bool cInterf::released2()
  {

      Int          i;
      Int          flag;
      MPI_Status   stat;

      if( irnk == ildr )
     {

         for( i=0;i<ndst;i++ )
        {
            if( hwait[i] )
           {
               wait( sreq4+i,&flag );
               if( flag )
              { 
                  MPI_Wait( sreq4+i,&stat );
//                MPI_Request_free( sreq4+i ); sreq4[i]= MPI_REQUEST_NULL;
                  hwait[i]= false;
              }
           }
        }

         rlsd= true;
         for( i=0;i<ndst;i++ )
        {
            rlsd= rlsd && (!(hwait[i]));
        }
     }

      return rlsd;
  }
