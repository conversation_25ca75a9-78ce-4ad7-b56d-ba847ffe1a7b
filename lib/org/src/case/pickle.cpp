
   using namespace std;

#  include <case.h>

   void cCase::pickle( size_t *len, pickle_t *buf )
  {
      cAudited::pickle( len,buf );
      pckle( len,     path,buf );
      pckle( len,    rnode,buf );
      pckle( len,    nnode,buf );
      pckle( len,    ncore,buf );
      pckle( len,     nrnk,buf );
      pckle( len,    gload,buf );
      pckle( len,    tload,buf );
      pckle( len,      ocr,buf );
      pckle( len,finalised,buf );
      pckle( len,      ovc,buf );
      pckle( len, mnode,ld,buf );
      pckle( len,    rtime,buf );
//      cout << "cCase::pickle nrnk " << nrnk << "================\n";
  };

   void cCase::unpickle( size_t *len, pickle_t buf )
  {
      if( ld ){ delete[] ld; ld=NULL; };
      cAudited::unpickle( len,buf );
      unpckle( len,     &path,buf );
      unpckle( len,    &rnode,buf );
      unpckle( len,    &nnode,buf );
      unpckle( len,    &ncore,buf );
      unpckle( len,     &nrnk,buf );
      unpckle( len,    &gload,buf );
      unpckle( len,    &tload,buf );
      unpckle( len,      &ocr,buf );
      unpckle( len,&finalised,buf );
      unpckle( len,      &ovc,buf );
      unpckle( len,&mnode,&ld,buf );
      unpckle( len,    &rtime,buf );
//      cout << "cCase::unpickle nrnk " << nrnk << "================\n";

  }

