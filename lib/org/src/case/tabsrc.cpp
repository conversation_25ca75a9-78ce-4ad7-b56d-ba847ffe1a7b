   using namespace std;

#  include <case.h>


   void cCase::get( cTabData *data )
  {
      cTabItem *tmp;
      tmp= new cTabItem( finalised ); tmp->set( finalised ); data->append( "finalized",       tmp );
      tmp= new cTabItem( name      ); tmp->set(      name ); data->append( "name",            tmp );
      tmp= new cTabItem( path      ); tmp->set(      path ); data->append( "path",            tmp );
      tmp= new cTabItem( ovc       ); tmp->set(       ovc ); data->append( "overcommit",      tmp );
      tmp= new cTabItem( ncore     ); tmp->set(     ncore ); data->append( "cores",           tmp );
      tmp= new cTabItem( nnode     ); tmp->set(     nnode ); data->append( "nodes",           tmp );
      tmp= new cTabItem( rnode     ); tmp->set(     rnode ); data->append( "requested-nodes", tmp );
      tmp= new cTabItem( nrnk      ); tmp->set(      nrnk ); data->append( "ranks",           tmp );
      tmp= new cTabItem( ocr       ); tmp->set(       ocr ); data->append( "occupancy-ratio", tmp );
      tmp= new cTabItem( gload     ); tmp->set(     gload ); data->append( "overall-load",    tmp );
      tmp= new cTabItem( tload     ); tmp->set(     tload ); data->append( "target-load",     tmp );

      tmp= new cTabItem( *ld       ); tmp->n=nnode;
                                      tmp->set(        ld ); data->append( "node loads",      tmp );
      tmp= new cTabItem( rtime     ); tmp->set(     rtime ); data->append( "run-time",        tmp );
  }

   void cCase::set( cTabData *data )
  {
      bool chng=false,tmp;

      tmp=data->get( "name",            &name  ); chng= chng ||tmp;
      tmp=data->get( "path",            &path  ); chng= chng ||tmp;
      tmp=data->get( "overcommit",      &ovc   ); chng= chng ||tmp;
      tmp=data->get( "cores",           &ncore ); chng= chng ||tmp;
      tmp=data->get( "requested-nodes", &rnode ); chng= chng ||tmp;
      tmp=data->get( "overall-load"   , &gload ); chng= chng ||tmp;
      tmp=data->get( "run-time",        &rtime ); 

      if( chng )
     {
         reset();
     }
  }
