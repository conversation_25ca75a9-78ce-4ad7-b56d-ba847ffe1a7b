   using namespace std;

#  include <case.h>

   cCase::cCase()
  {
      path=unassigned;
/*    ncpu=0;
      mcpu=0;
      ncore=0;*/
      finalised=false;
      il=           0;
      ic=           0;
      in=           0;
      ir=           0;
//    token=        0;
      dtoken=       0;
      ovc=      false;
      ocr=         -1;
      nnode=       -1;
      rnode=       -1;
      ncore=       -1;
      nrnk=        -1;
      tload=       -1;
      gload=       -1;
      mnode=        5;
      dnode=        5;
      cld=          NULL;
      ld=           new Real[mnode];
      setv( 0,mnode, (Real)0., ld );
      rtime=       -1;
/*    rtime0=       MPI_Wtime(); cout << "starting time "<<rtime0<<"\n";
       rtick=       MPI_Wtick(); cout << "timer resolution "<<rtick<<"\n";*/
      bmetis = false;
  }

   cCase::~cCase()
  {
      path=unassigned;

      finalised=false;
      il=           0;
      ic=           0;
      in=           0;
      ir=           0;
//    token=        0;
      dtoken=       0;
      ovc=      false;
      ocr=         -1;
      nnode=       -1;
      rnode=       -1;
      nrnk=        -1;
      tload=       -1;
      gload=       -1;
      mnode=        0;
      dnode=        0;
      delete[]     ld; ld=NULL;
      delete[]    cld;
/*    ncpu=0;
      mcpu=0;
      ncore=0;*/
      rtime=      -1.;
/*    rtime0=     -1.;
      rtick=      -1.;*/
      bmetis = false;
  }

   void cCase::check( string tab ) 
  { 
//      cout <<tab<<" ";
//      cAudited::check( tab ); 
//      cout << tab << " name:            "<<  name <<"\n"; 
//      cout << tab << " ncore:           "<<ncore << "\n"; 
//      cout << tab << " requested nodes: "<<rnode << "\n"; 
//      cout << tab << " nodes:           "<<nnode << "\n"; 
//      cout << tab << " ranks:           "<<nrnk  << "\n"; 
//      cout << tab << " overcommit:      "<<ovc   << "\n"; 
//      cout << tab << " finalised:       "<<finalised << "\n"; 
//      cout << tab << " occupancy:       "<<ocr   << "\n"; 
//      cout << tab << " overall load:    "<<gload << "\n"; 
//      cout << tab << " target load:     "<<tload << "\n"; 
//      cout << tab << " running time:    "<<rtime << "\n"; 

  }



   void cCase::newnode()
  {
      in++;
      if( in == mnode )
     {
         realloc( &mnode,dnode, &ld );
         setv( in,mnode,(Real)0.,ld );
     }
  }

   void cCase::newlocal( Int *m, Int **inode, Int **icore, Int **irnk )
  {
      Int       l;
      Int       dsize=5;
      if( il == *m )
     {
         l= *m; realloc( &l,dsize, inode ); setv( *m,l, (Int)-1, *inode );
         l= *m; realloc( &l,dsize, icore ); setv( *m,l, (Int)-1, *icore );
         l= *m; realloc( &l,dsize,  irnk ); setv( *m,l, (Int)-1,  *irnk );
        *m= l;
     }
  }

   void cCase::costs( Real dcost, Real c0, Real c1 )
  {

      Int i;
//    token= c0;
      dtoken=c1;
      il=0;
      ic=0;
      lload= tload;
      if( !ovc )
     {
         lload= dcost/lload;
         lload= ceil(lload);
         lload= dcost/lload;
         if( ld[in] != 0 )
        {
            newnode();
        };
     }
      rload= dcost;
      lload/= (Real)ncore;
      delete[] cld; cld= new Real[ncore];
      for( i=0;i<ncore;i++ )
     {
         cld[i]= ld[in]/ncore;
     }
      ld[in]=0;
      finalised=false;

  }

   void cCase::load( Int *m, Int **inode, Int **icore, Int **irnk, Real tkn )
  {
      cld[ic]+= tkn;
      rload-= tkn;
      if( cld[ic] > lload-dtoken )
     { 
         newlocal( m,inode,icore,irnk );
       (*inode)[il]= in;
       (*icore)[il]= ic;
       (*irnk)[il]=  ir; 
         ld[in]+= cld[ic];
         cld[ic]= 0;
         ic++; 
         il++;
         ir++;
         if( ic == ncore )
        {
            ic=0;
            newnode();
            lload= min( rload/(Real)ncore,lload );
        }
     }
  }

   void cCase::finalize( Int *m, Int **inode, Int **icore, Int **irnk )
  {
      if(bmetis)
     { 
         in = 0;
         il = 0;
         for(Int i=0; i<ncore; i++)
        {
            newlocal( m,inode,icore,irnk );
            (*inode)[i] = in; 
            (*icore)[i] = il++;
            (*irnk)[i] = ir++;
             ic=0;
        }
     }
      else
     {
         if( ic == ncore-1 && cld[ic]!= 0)
        {
            newlocal( m,inode,icore,irnk );
          (*inode)[il]= in;
          (*icore)[il]= ic;
          (*irnk)[il]=  ir; 
            ld[in]+= cld[ic];
            cld[ic]= 0;
            il++;
            ir++;
            ic= 0;
            if( !ovc )
           {
               newnode();
           }
        }
     }
  }

   void cCase::finalize()
  {
      Int      i;
      Real     occ[2];

      if( ovc ){ in++; };

      nnode= in;
      nrnk= ir;
      finalised=true;

      occ[0]=     big;
      occ[1]=    -big;
      for( i=0;i<nnode;i++ )
     {
         occ[0]= min( occ[0],ld[i] );
         occ[1]= max( occ[1],ld[i] );
     }
      ocr= occ[0]/occ[1];
      delete[] cld; cld= NULL;

      cout << "********Total number of MPI ranks " << nrnk << "*********\n";
  }


   void cCase::reset()
  {
      finalised= false;
      il=           0;
      ic=           0;
      in=           0;
      ir=           0;
//    token=        0;
      dtoken=       0;
      ocr=         -1;
      nnode=       -1;
      nrnk=        -1;
      setv( 0,mnode, (Real)0., ld );

      tload= gload/(Real)rnode;
  }
