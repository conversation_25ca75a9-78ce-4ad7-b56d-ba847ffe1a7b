   using namespace std;

#  include <device/client/dom.h>
#  include <fstream>

   extern "C" void xpart(  cDom *dev, cDomain *dmn, Real tld, Real *cld, Int *ipcpu, Int *ilcpu, Int *ncpu, Int *icpu, 
                           Real *cpul )
  {
      FILE     *f;
      string    path,fnme;

      Int       nq,nx;
      Real     *sxq,*xq[3];
      Real     *qcst;
      Int      *qcpu;

      Int      *iprm;
      Int       iq,jq,ix,ic,jc,ic0,jc0;

      dmn->getxq( &nq,&nx,&sxq );
      subv( nx,nq, sxq,xq );

      cout << "at entry to xpart "<<nq<<"\n";
      cout << "target load "<<tld<<"\n";

      dmn->getqcost( &qcst );
      dmn->getqcpu( &qcpu );

      iprm= new  Int[nq];

      hsort( nq,xq[0], iprm ); 

      jc=0;
      ic=*ipcpu;
      icpu[jc]= ic;
      cpul[jc]= 0;
      for( jq=0;jq<nq;jq++ )
     {
         if( cld[ic] > tld )
        {
            jc++;
            ic++;
            icpu[jc]= ic;
        }
         iq=        iprm[jq];
         cld[ic]+=  qcst[iq];
         cpul[jc]+= qcst[iq];
         qcpu[iq]=  jc;
     }

      path= dev->getcpath();
      fnme= path+ "/part.dat";
      f= fopen( fnme.c_str(),"w" );
      fwrite( (void*)qcpu,sizeof(*qcpu),(size_t)nq,f );
      fclose( f );

     *ipcpu= ic;
     *ncpu= ++jc;
// next device will need a new logical cpu
    (*ilcpu)+= (*ncpu);

      delete[] iprm;

  }

