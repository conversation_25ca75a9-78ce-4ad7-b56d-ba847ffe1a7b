   using namespace std;

#  include <geo/boxtree.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Wed Dec  8 18:49:44 GMT 2010
// Changes History -
// Next Change(s)  -

   cBoxTree::cBoxTree()
  {
  }

   cBoxTree::~cBoxTree()
  {
  }

   void cBoxTree::init( Int nx0, Real *ymin, Real *ymax )
  {
      Real bmin[ADTMX],bmax[ADTMX]; 
      Int  ix;
      for( ix=0;ix<nx0;ix++ )
     {
         bmin[ix]=     ymin[ix];
         bmin[ix+nx0]= ymin[ix];
         bmax[ix]=     ymax[ix];
         bmax[ix+nx0]= ymax[ix];
     }
         cAdTree::init( 2*nx0, bmin,bmax );
  }

   void cBoxTree::inters( Real *ymin, Real *ymax, Int *np, Int *var )
  {
      Int  ix;
      Int  nx0=nx/2;
      Real bmin[ADTMX],bmax[ADTMX];

      for( ix=0;ix<nx0;ix++ )
     {
         bmin[ix]=     xmin[ix];
         bmin[ix+nx0]= ymin[ix]; 

         bmax[ix]=     ymax[ix];
         bmax[ix+nx0]= xmax[ix]; 
     }
      search( bmin,bmax, np,var );
     
  }

   void  cBoxTree::insert( Real *ymin, Real *ymax, Int in )
  {
      Int ix;
      Int nx0= nx/2;
      Real y[ADTMX];
      for( ix=0;ix<nx0;ix++ )
     {
         y[ix]=     ymin[ix];
         y[ix+nx0]= ymax[ix];
     }
      cAdTree::insert( y, in );
 
  }

   void cBoxTree::corners( Int i, Real *ymin, Real *ymax )
  {
      Int ic,ix,nx0;
      nx0= nx/2; 
      ic= indx[i];
      for( ix=0;ix<nx0;ix++ )
     {
         ymin[ix]= x[ix    ][ic];
         ymax[ix]= x[ix+nx0][ic];
     }
  } 

