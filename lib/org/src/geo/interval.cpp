
   using namespace std;

#  include <geo/interval.h>


   Real sgdst( Real x0, Real x1, Real x )
  {
      Real val=0;
      if( x < x0 )
     {
         val= x-x0;
     }
      else
     {
         if( x > x1 )
        {
            val= x-x1;
        }
     }
      return val;
  }

   void left( Real *x0, Real *x1, Real x )
  {

      if( x < *x0 )
     {
        *x1= *x0;
     }
      else
     {
         if( x < *x1 )
        {
           *x1= x;
        }
     }
  }

   void right( Real *x0, Real *x1, Real x )
  {

      if( x > *x1 )
     {
        *x0= *x1;
     }
      else
     {
         if( x > *x0 )
        {
           *x0= x;
        }
     }
  }

   void inters( Real *x0, Real *x1, Real y0, Real y1 )
  {
      right( x0,x1, y0 );
      left( x0,x1, y1 );
  }

   void sinters( Real *x0, Real *x1, Real x, Real d )
  {
      if( d > 0 )
     {
         right( x0,x1, x );
     }
      else
     {
         left( x0,x1, x );
     }
  }

   bool inside( Real x0, Real x1, Real x )
  {
      bool val= true;
      val= ( x <= x1 );
      val= val && ( x >= x0 );
      return val;
  }
 
   bool inters( Real x0, Real x1, Real y0, Real y1 )
  {
      bool val;

      val= y1 >= x0;
      val= val && ( y0 <= x1 );

      return val;
  }
