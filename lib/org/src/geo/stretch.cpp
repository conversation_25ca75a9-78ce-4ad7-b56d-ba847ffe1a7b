   using namespace std;

#  include <geo/stretch.h>

   Real stretchf( Real ratio, Real b, Real ss, Real se, Real s0 )
  {
     Real tmp0, tmp1, se0, se1, ss0, ss1;
     Real s1;
     Real slope;
     Real damp;

     slope=0;
     damp = 0.5*log((b-1)/(b+1));

     if( s0 <=ratio)
    {
       ss0 = ss;
       se0 = se*ratio;
       s0 = (s0-ss0)/(se0-ss0);
       tmp0 = slope * s0;
       tmp1 = ( 1-slope ) * ( 1- tanh( damp*(1-s0))/(tanh(damp)));
       s1 = tmp0 + tmp1;
       s1 = (1-s1)*ss0 + s1*se0;
    }
     else if(s0>=ratio)
    {
       ss1 = se;
       se1 = se*ratio;
       s0 = (s0-ss1)/(se1-ss1);
       tmp0 = slope * s0;
       tmp1 = ( 1-slope ) * ( 1- tanh( damp*(1-s0))/(tanh(damp)));
       s1 = tmp0 + tmp1;
       s1 = (1-s1)*ss1 + s1*se1;
    }

     return s1;
  }
