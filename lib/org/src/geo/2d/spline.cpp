using namespace std;

#     include        <geo/2d/spline.h>
#     include        <assert.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

   cSpline::cSpline()
  {
      iord=-1;
      l=-1;
      nv=0;
      np=0;
      s= NULL;
      sx= NULL;
      sz= NULL;
      dx0= NULL;
      dx1= NULL;
      bdx0= false;
      bdx1= false;
      x= NULL;
      z= NULL;
  }

   void cSpline::cleanup()
  {
      iord=-1;
      l=-1;
      nv=0;
      np=0;
      delete[] s; s= NULL;
      delete[] sx; sx= NULL;
      delete[] sz; sz= NULL;
      delete[] dx0; dx0= NULL;
      delete[] dx1; dx1= NULL;
      delete[] x; x= NULL;
      delete[] z; z= NULL;
      bdx0= false;
      bdx1= false;
  }

   cSpline::~cSpline()
  {
      cleanup();
  }

   void cSpline::displace(cInterp **var, Real ls[], Real d[], Real fct)
  {
     cSpline   *tmp=NULL;
     Real   y0[2], dy0[3];
     Int    iv, ip, Iord, Np, Nv;
     Real **X=NULL, *Dx0=NULL, *Dx1=NULL;
     ofstream  fle;

     Iord= iord;
     Np= np;
     Nv= nv;
     X= new Real*[Nv]; for(iv=0; iv<Nv; iv++){ X[iv]=NULL; }
     for(iv=0; iv<Nv; iv++) { X[iv]= new Real[Np]; }

//   fle.open("checksplinedisplaceddata");
     for( ip=0;ip<Np;ip++ )
    {
        interp( s[ip], y0, dy0);
        dy0[2]= norm22(dy0);
        norml(2, dy0);
        ::scale(2, dy0, d[ip]*fct);
        rot90(dy0);
        X[0][ip]= y0[0]+dy0[0];
        X[1][ip]= y0[1]+dy0[1];

//      fle << X[0][ip] <<"  "<< X[1][ip]<<"\n";
    }
//   fle.close();

     tmp= new cSpline();
     tmp->build( Iord, Np, Nv, X, Dx0, Dx1);

    *var= tmp;

     for(iv=0; iv<Nv; iv++)
    {
       delete[] X[iv]; X[iv]=NULL;
    }
     delete[] X; X=NULL;


  }

   void cSpline::displace( cInterp **var, Real *l0, Real d0, Real *l1, Real d1)
  {
     cSpline   *tmp=NULL;
     Real   y0[2], dy0[3];
     Int    iv, ip, Iord, Np, Nv;
     Real **X=NULL, *Dx0=NULL, *Dx1=NULL;

     if( d0 == d1)
    {
        Iord= iord;
        Np= np;
        Nv= nv;
        X= new Real*[Nv]; for(iv=0; iv<Nv; iv++){ X[iv]=NULL; }
        for(iv=0; iv<Nv; iv++) { X[iv]= new Real[Np]; }

        for( ip=0;ip<Np;ip++ )
       {
           interp( s[ip], y0, dy0);
           dy0[2]= norm22(dy0);
           norml(2, dy0);
           ::scale(2, dy0, d0);
           rot90(dy0);
           X[0][ip]= y0[0]+dy0[0];
           X[1][ip]= y0[1]+dy0[1];
       }

        tmp= new cSpline();
        tmp->build( Iord, Np, Nv, X, Dx0, Dx1);

       *var= tmp;

        for(iv=0; iv<Nv; iv++)
       {
          delete[] X[iv]; X[iv]=NULL;
       }
        delete[] X; X=NULL;
    }
     else
    {
       cInterp::displace(var, l0, d0, l1, d1);
    }
  }

  
   void cSpline::copy( cInterp **var )
  {
      cSpline *tmp;
      if( !(*var) )
     {
         tmp= new cSpline();
         tmp->build( iord, np,nv, x, dx0,dx1 );
        *var= tmp;
         cInterp::copy( var );
     }
      else
     {
         cout << "cannot copy into already existing object (cSpline::copy)\n";
     }
  }

   
   cCSpline::cCSpline()
  {
  }

   cCSpline::~cCSpline()
  {
  }

   void cCSpline::copy( cInterp **var )
  {
      cCSpline *tmp;
      if( !(*var) )
     {
         tmp= new cCSpline();
         tmp->build( iord, np,nv, x, dx0,dx1 );
        *var= tmp;
         cInterp::copy( var );
     }
      else
     {
         cout << "cannot copy into already existing object (cCSpline::copy)\n";
     }
       
  }

   void cSpline::fit( )
  {

      Real      *am,*a,*ap,*tmp0,*tmp1;
      Real       h0,h1,h;
      Int        iv,ip;

      const Real i3=1./3.,i6=0.5*i3;

      if( iord > 0 )
     {

         am=  new Real[np];
         a =  new Real[np];
         ap=  new Real[np];

         tmp0=new Real[nv];
         tmp1=new Real[nv];

         h1= s[1]- s[0];
         for( iv=0;iv<nv;iv++ )
        {   
           tmp1[iv]= x[iv][1]- x[iv][0];
           tmp1[iv]/= h1;
        }

// first point

         if( bdx0 )
        {
            am[0]=0;
            a[0]=-i3*h1;
            ap[0]= -i6*h1;
            for( iv=0;iv<nv;iv++ )
               z[iv][0]= dx0[iv]-tmp1[iv];
        }
         else
        {
            am[0]=0;
            a[0]= 1;
            ap[0]=0;
            for( iv=0;iv<nv;iv++ )
               z[iv][0]= 0;

        }

         for( ip=1;ip<np-1;ip++ )
        {
            h0= h1;
            h1= s[ip+1]-s[ip];
            h=  h1+h0;
            for( iv=0;iv<nv;iv++ )
           {
               tmp0[iv]= tmp1[iv];
               tmp1[iv]= x[iv][ip+1]- x[iv][ip];
               tmp1[iv]/= h1;
           }
            am[ip]= i6*h0;
            a[ip]=  i3*h;
            ap[ip]= i6*h1;
            for( iv=0;iv<nv;iv++ )
               z[iv][ip]= tmp1[iv]-tmp0[iv];
        }

// last node 

         if( bdx1 )
        {
            am[np-1]= i6*h1;
            a[np-1]=i3*h1;
            ap[np-1]=0;
            for( iv=0;iv<nv;iv++ )
               z[iv][np-1]= dx1[iv]-tmp1[iv];
        }
         else
        {
            am[np-1]= 0;
            a[np-1]=  1;
            ap[np-1]=0;
            for( iv=0;iv<nv;iv++ )
               z[iv][np-1]= 0;
        }

// inversion
         thomas( np, am,a,ap, nv,np, sz );

// cleanup

         delete[] am;
         delete[]  a;
         delete[] ap;

         delete[] tmp1;
         delete[] tmp0;
         
     }
  };

   void cSpline::build( Int Iord, Int Np, Int Nv, Real *X[], Real *Dx0, Real *Dx1 )
  {
      Int    iv,ip;
      Real   d1,d2,d3,d;

      cleanup();

      np= Np;
      nv= Nv;

      s= new Real[np]; setv(0, np, ZERO, s);
      sx= new Real[nv*np]; x= new Real*[nv]; subv( nv,np, sx,x );
      sz= new Real[nv*np]; z= new Real*[nv]; subv( nv,np, sz,z );
      dx0= new Real[nv]; setv( 0,nv, ZERO,dx0 );
      dx1= new Real[nv]; setv( 0,nv, ZERO,dx1 );
      
      for( iv=0;iv<nv;iv++ )
     {
         for( ip=0;ip<np;ip++ )
        {
            x[iv][ip]= X[iv][ip];
        }
     }
      setv( 0,np*nv, ZERO, sz);


      s[0]=0;
      if( nv == 2 )
     {
         for( ip=1;ip<np;ip++ )
        {
            d1= x[0][ip]-x[0][ip-1];
            d2= x[1][ip]-x[1][ip-1];
            d=  d1*d1+ d2*d2;
            d=  sqrt(d);
            s[ip]= s[ip-1]+d;
        }
     }
      else
     {
         if( nv >= 3 )
        {
            for( ip=1;ip<np;ip++ )
           {
               d1= x[0][ip]-x[0][ip-1];
               d2= x[1][ip]-x[1][ip-1];
               d3= x[2][ip]-x[2][ip-1];
               d=  d1*d1+ d2*d2+ d3*d3;
               d=  sqrt(d);
               s[ip]= s[ip-1]+d;
           }
        }
     }
      for( ip=0;ip<np-1;ip++ )
     {
         s[ip]/= s[np-1];
     }
      l= s[np-1];
      s[np-1]= 1.;

      if( Dx0 )
     { 
         bdx0= true;
         for( iv=0;iv<nv;iv++ )
        {
            dx0[iv]= Dx0[iv]; 
        }
     }
      else
     {
         bdx0= false;
         d= s[1]- s[0];
         for( iv=0;iv<nv;iv++ )
        {   
           dx0[iv]= x[iv][1]- x[iv][0];
           dx0[iv]/= d;
        }
     }
      if( Dx1 )
     {
         bdx1= true;
         for( iv=0;iv<nv;iv++ )
        { 
             dx1[iv]= Dx1[iv]; 
        }
     }
      else
     {
         bdx1= false;
         d= s[np-1]- s[np-2];
         for( iv=0;iv<nv;iv++ )
        {   
           dx1[iv]= x[iv][np-1]- x[iv][np-2];
           dx1[iv]/= d;
        }
     }
      iord= Iord;
      fit( );

  };

   void cSpline::interp( Real t0, Real *y, Real *dy )
  {
      Int          ip,iv;
      Real         t,a,b,c,d;
      Real         h,h6,ds;
      const Real   i3=1./3.,i6=0.5*i3;

      t= t0;
      wrap( &t );
      if( t < s[0] )
     {
         interp( s[0],y,dy );
         ds= t-s[0];
         for( iv=0;iv<nv;iv++ )
        {
            y[iv]+= dy[iv]*ds;
        }
         
     }
      else
     {
         if( t > s[np-1] )
        {

            interp( s[np-1],y,dy );
            ds= t-s[np-1];
            for( iv=0;iv<nv;iv++ )
           {
               y[iv]+= dy[iv]*ds;
           }
        } 
         else
        {
            ip= bsearch( t, np-1,s+1 );
//          cout << "bsearch finds "<<ip<<" "<<np<<"\n";
      
            h= s[ip+1]-s[ip];
            a= (s[ip+1]-t)/h;
            b= 1.-a;
     
            if( iord > 0 )
           {
               h6= i6*h*h;
               c= a*(a*a-1)*h6;
               d= b*(b*b-1)*h6;
               for( iv=0;iv<nv;iv++ )
              {
    
                 y[iv]=   a*x[iv][ip]+  b*x[iv][ip+1]+ 
                         c*z[iv][ip]+  d*z[iv][ip+1];
   
                 dy[iv]= (x[iv][ip+1]-x[iv][ip])/h-
                          i6*( 3.*a*a-1 )*h*z[iv][ip]+
                          i6*( 3.*b*b-1 )*h*z[iv][ip+1];
   
              }
           }
            else
           {
               for( iv=0;iv<nv;iv++ )
              {
  
                  y[iv]=   a*x[iv][ip]+  b*x[iv][ip+1];
                  dy[iv]= (x[iv][ip+1]-x[iv][ip])/h;
              }
           }
        }
     }
  }

   void cCSpline::fit( )
  {

      Real      *am,*a,*ap,*wrk,*wrk2;
      Real       h,hm;
      Int        iv,ip;

      if( iord > 0 )
     {

         am=  new Real[np];
         a =  new Real[np];
         ap=  new Real[np];

         setv( 0,np, ZERO, am );
         setv( 0,np, ZERO, a  );
         setv( 0,np, ZERO, ap );

// first node

         ip= 0;

         h= s[ip+1]-s[ip];
         hm=s[np-1]-  s[np-2];
 
         am[ip]=      hm;
         a[ip]=  2.*( hm+ h );
         ap[ip]=          h;

         for( iv=0;iv<nv;iv++ )
        {
            z[iv][ip]= 6.*( ( x[iv][ip+1]- x[iv][ip] )/h-
                            ( x[iv][np-1]-   x[iv][np-2])/hm );
        }
        
// all other nodes

         for( ip=1;ip<np-1;ip++ )
        {
            h= s[ip+1]-s[ip];
            hm=s[ip]-  s[ip-1];
 
            am[ip]=      hm;
            a[ip]=  2.*( hm+ h );
            ap[ip]=          h;

            for( iv=0;iv<nv;iv++ )
           {
               z[iv][ip]= 6.*( ( x[iv][ip+1]- x[iv][ip] )/h-
                               ( x[iv][ip]-   x[iv][ip-1])/hm );
           }
        }

// inversion

         wrk= new Real[np];
         wrk2= new Real[np];
         setv( 0,np, ZERO, wrk );
         setv( 0,np, ZERO, wrk2 );

         thomasp( np-1, am,a,ap, nv,np, sz, wrk,wrk2  );

         for( iv=0;iv<nv;iv++ )
        {
            z[iv][np-1]= z[iv][0];
        }

// cleanup

         delete[] am;
         delete[]  a;
         delete[] ap;

         delete[] wrk;
         delete[] wrk2;
         
     }
  };

   void cCSpline::wrap( Real *s1 )
  {
      Real ds;
      ds= s[np-1]-s[0]; 
      while( *s1 > s[np-1] )
     {
         *s1-= ds;
     }
      while( *s1 < s[0] )
     {
         *s1+= ds;
     }
  }

  void cSpline::rotate( Real *xr, Real dt )
 {
     rotv2( 0,np, xr,dt, x );
     rotv2( dt,dx0 );
     rotv2( dt,dx1 );
     fit();
     na=0;
 }

  void cSpline::translate( Real *dx )
 {
     Int ip;    
     for( ip=0;ip<np;ip++ )
    {
        x[0][ip]+= dx[0];
        x[1][ip]+= dx[1];
    }
     na=0;
 }

  void cSpline::mirror( Real *x0, Real *l0 )
 {
     mirv2( 0,np, x0,l0, x );
     mirv2( l0, dx0 );
     mirv2( l0, dx1 );
     fit();
     na=0;
 }

  void cSpline::scale( Real *x0, Real f )
 {
     sclv2( 0,np, x0,f, x );
     sclv2( f, dx0 );
     sclv2( f, dx1 );
     fit();
//     build( iord, np,nv, x, dx0,dx1 );
     na=0;
 }

  Real cSpline::gets( Int i )
 {
     cout << "cSpline::gets forbidden\n";
     assert( false );
     assert( i<np );
     assert( i>=0 );
     return( s[i] );
 }

  void cSpline::pickle( size_t *len, pickle_t *buf )
 {
     Int i;
     size_t len0=*len;
     cInterp::pickle( len,buf );
     pckle( len,iord,buf );           cout << " 0"<<*len-len0<<"\n";
     pckle( len,np,buf );             cout << " 1"<<*len-len0<<"\n";
     pckle( len,nv,buf );             cout << " 2"<<*len-len0<<"\n";
     pckle( len,nv,dx0,buf );         cout << " 3"<<*len-len0<<"\n";
     pckle( len,nv,dx1,buf );         cout << " 4"<<*len-len0<<"\n";
     pckle( len,  bdx0,buf );         cout << " 5"<<*len-len0<<"\n";
     pckle( len,  bdx1,buf );         cout << " 6"<<*len-len0<<"\n";
     for( i=0;i<nv;i++ )
    {
        pckle( len,np,x[i],buf );     cout << i<<" v  "<<*len-len0<<"\n";
    }
 }
  void cSpline::unpickle( size_t *len, pickle_t buf )
 {
     Int i,n;
     size_t  len0=*len;
     Real   **z;
     Real   *dX0=NULL;
     Real   *dX1=NULL;
     cInterp::unpickle( len,buf );
     unpckle( len,&iord,buf );        cout << " 0"<<*len-len0<<"\n";
     unpckle( len,&np,buf );          cout << " 1"<<*len-len0<<"\n";
     unpckle( len,&nv,buf );          cout << " 2"<<*len-len0<<"\n";
     unpckle( len,&n,&dX0,buf );      cout << " 3"<<*len-len0<<"\n";
     unpckle( len,&n,&dX1,buf );      cout << " 4"<<*len-len0<<"\n";
     unpckle( len, &bdx0,buf );       cout << " 5"<<*len-len0<<"\n";
     unpckle( len, &bdx1,buf );       cout << " 6"<<*len-len0<<"\n";
     z= new Real*[nv]; setv( 0,nv, (Real*)NULL, z );
     for( i=0;i<nv;i++ )
    {
        unpckle( len,&n,z+i,buf );    cout << i<<"  v "<<*len-len0<<"\n";
    }
     if( !bdx0 ){ delete[] dX0; dX0= NULL; };
     if( !bdx1 ){ delete[] dX1; dX1= NULL; };
     build( iord,np,nv,z,dX0,dX1 );
     delete[] dX0;
     delete[] dX1;
     for( i=0;i<nv;i++ )
    {
        delete[] z[i];
    }
     delete[] z;
 }

   void cSpline::get( cTabData *var )
  {
      Int i;
      cTabItem *tmp;
//    cInterp::get( var );
//    tmp= new cTabItem( iord );   tmp->hlp= "Interpolation order. 0 for piecwise straight segments, 1 for cubic splines.";
//                                 tmp->set( 0,iord );
//                                                                  var->append( "order",tmp );
//    tmp= new cTabItem();                                           var->append( "spline-fixed-end",tmp ); 
      tmp= new cTabItem();                                           var->append( tag+"-spline-support-start",tmp ); 
      for( i=0;i<np;i++ )
     {
         tmp= new cTabItem( x[0][i] ); tmp->n=2; tmp->hlp= tmp->hlp1="spline support points";
                                                 tmp->vrange=true;
                                                 tmp->set( 0,x[0][i]); tmp->lbc[0]="X-axis";
                                                 tmp->set( 1,x[1][i]); tmp->lbc[1]= "Y-axis";
                                                 var->append( tag+"-x"+strc(i),tmp );
     }
      tmp= new cTabItem();   tmp->vrange=true;                       var->append( tag+"-spline-support-end",tmp ); 

  }
   void cSpline::set( cTabData *var )
  {
      cTabItem *tmp;
      Real *x0[2];
      Int   i,j; 
//    var->get( "order",&io );
      Int ist,ien;
      ist= var->where( tag+"-spline-support-start" );
      ien= var->where( tag+"-spline-support-end" );
      if(ist>=ien) {return;}
      x0[0]= new Real[ien-ist-1];
      x0[1]= new Real[ien-ist-1];
      j=0;
      for( j=0,i=ist+1;i<ien;i++,j++ )
     {
         tmp= var->which(i);
         tmp->get( 0,x0[0]+j );
         tmp->get( 1,x0[1]+j );
     }
      build( iord,ien-ist-1,2,x0,NULL,NULL );
      delete[] x0[0];
      delete[] x0[1];
 
  }

   void cSpline::plot(Real l0, Real l1,  ofstream *fle )
  {
/*
      Int   i;
      Real  w;
      Real  y[2],dy[2];
      Real  s;

      s= (l1-l0)/np;

      for( i=0;i<=np;i++ )
     {
         w= l0+s*i;
         interp( w,y,dy );
        *fle << y[0]<<" "<<y[1]<<"\n";
     }
*/
      Int   i;
      Real  w;
      Real  y[2],dy[2];
      Real  s;

      s= (l1-l0)/np;

      Int ITPNCHECK= 1000;
      for( i=0;i<ITPNCHECK;i++ )
     {
         w= (Real)i/(Real)(ITPNCHECK-1);
         interp( w,y,dy );
        *fle << y[0]<<" "<<y[1]<<"\n";
     }

  }

   void cSpline::property( string lbl, Int *var )
  {
      if( lbl == "np" ){ var[0]= np; };
      if( lbl == "iord" ){ var[0]= iord; };
      if( lbl == "nv" ){ var[0]= nv; };
  }

   void cSpline::property( string lbl, Real *var )
  {
      for( int i=0;i<np;i++ )
     {
         if( lbl == "x"+strc(i) ){ var[0]= x[0][i]; var[1]= x[1][i]; };
     }
  }
                             
