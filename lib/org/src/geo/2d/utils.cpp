   using namespace std;

#  include <geo/2d/utils.h>
#  include <assert.h>

   void build( Int n, Real *x[], cInterp **p )
  {

      cStraight *tmp;
      Int    i;
      Real   x0[2],x1[2];
      line2( 0,x, x1 );
      for( i=1;i<n;i++ )
     {
         idv2( x1,x0 );
         line2( i,x, x1 );
         tmp= new cStraight();
         tmp->build( x0,x1 );
         p[i-1]= tmp;
     }
  }

   void build( Int n, Real *x[], cPolyline **p)
  {
     cPolyline *var=new cPolyline();
     cStraight *tmp=NULL;
     Int    i;
     Real   x0[2],x1[2];
     line2( 0,x, x1 );
     for( i=1;i<n;i++ )
    {
        idv2( x1,x0 );
        line2( i,x, x1 );
        tmp= new cStraight();
        tmp->build( x0,x1 );
        var->add((Real)0., (Real)1., tmp); 
        delete tmp;
     }
      *p= var;
  }

   void build( Int n, fillet_t *fl, cInterp **p, cInterp **f, Real *s[2], Real *d[2] )
  {


      Int    i;
      for( i=0;i<n;i++ )
     {
         fillet( fl+i, s[1]+i,p[i],true, s[0]+(i+1),p[i+1],true, f+i, d[0]+i, d[1]+i );
     }

  }

   void build( Int n, cInterp **p, fillet_t *f, cPolyline **v )
  {

      assert( !(*v) );

      Int    i;
      cPolyline *var;
      cInterp  **tmp;
      Real      *s[2];
      Real      *d[2];

      s[0]= new Real[n];
      s[1]= new Real[n];

      d[0]= new Real[n-1];
      d[1]= new Real[n-1];

      tmp= new cInterp*[n-1];

      setv( 0,n, ZERO, s[0] );
      setv( 0,n, (Real)1., s[1] );

      setv( 0,n-1, (cInterp*)NULL, tmp );

      build( n-1, f, p,tmp, s,d );

      var= new cPolyline();
      var->add( s[0][0],s[1][0],p[0] );
      for( i=1;i<n;i++ )
     {
         if( tmp[i-1] )
        {
            var->add( d[0][i-1],d[1][i-1], tmp[i-1] );
            delete tmp[i-1]; tmp[i-1]=NULL;
        }
         var->add( s[0][i],s[1][i], p[i] );
     }

      delete[] s[0];
      delete[] s[1];
      delete[] d[0];
      delete[] d[1];

      delete[] tmp;

     *v= var;
  }

   void build( cInterp *p, cInterp **w, Real *s[], Int nd, Real *l, Real *d0, Real *d1, Real fct )
  {

      Int    j;
      Real   l0,l1;
      l1= 0.;
      for( j=0;j<nd;j++ )
     {
         l0= l1;
         l1= l[j]; 
         s[0][j]= l0;
         s[1][j]= l1;
         p->displace( w+j, s[0]+j,fct*d0[j], s[1]+j,fct*d1[j] );
     }
  }

   void build( cInterp *p, Int ns, Real *ls, Real *ds0, Real *ds1, Real fct, fillet_t *fl, cPolyline **v )
  {
      Real *s[2];
      Real *d[2];
      cPolyline *var;
      cInterp **w;
      cInterp **f;

      assert( !(*v) );

      s[0]= new Real[ns];
      s[1]= new Real[ns];
      w= new cInterp*[ns]; setv( 0,ns, (cInterp*)NULL,w );
 
      d[0]= new Real[ns-1];
      d[1]= new Real[ns-1];
      f= new cInterp*[ns-1]; setv( 0,ns-1, (cInterp*)NULL,f );

      build( p, w,s, ns,ls,ds0,ds1, fct );
      build( ns-1,fl, w,f, s,d );

      var= new cPolyline();
      var->add( s[0][0],s[1][0],w[0] );
      delete w[0]; w[0]= NULL;
      for( Int i=1;i<ns;i++ )
     {
         if( f[i-1] )
        {
            var->add( d[0][i-1],d[1][i-1], f[i-1] );
            delete f[i-1]; f[i-1]=NULL;
        }
         var->add( s[0][i],s[1][i], w[i] );
         delete w[i]; w[i]=NULL;
     }
     *v= var;

      delete[] f;
      delete[] w;
      delete[] s[0];
      delete[] s[1];
      delete[] d[0];
      delete[] d[1];

  }

   void build( cInterp *p0, cInterp *p1, cap_t *c0, cap_t *c1, cCPolyline **z )
  {
      Real    z0,z1,z2,z3,d0,d1,d2,d3;
      cInterp *g0=NULL,*g1=NULL;
      cCPolyline *var;

      assert( !(*z) );

      cap( c0, &z0,p0,&z1,p1, &g0, &d0,&d1 );
      cap( c1, &z2,p1,&z3,p0, &g1, &d2,&d3 );
/*
      cout << "P0 "<<z3<<" "<<z0<<"\n";
      p0->debug("   ");
      if(g0)
     {  
         cout << "G0 "<<d0<<" "<<d1<<"\n";
         g0->debug("   ");
     }
      cout << "P1 "<<z1<<" "<<z2<<"\n";
      p1->debug("   ");
      if(g1)
     {
         cout << "G1 "<<d2<<" "<<d3<<"\n";
         g1->debug("   ");
     }

 */   var= new cCPolyline();
      var->add( z3,z0, p0 );
      if(d0!=d1) {var->add( d0,d1, g0 ); delete g0;} else {delete g0; g0=NULL;}
      var->add( z1,z2, p1 );
      if(d2!=d3) {var->add( d2,d3, g1 ); delete g1;} else {delete g1; g1=NULL;}

     // delete g;
     // delete g1;

      var->apprx( 0.1,0.1 );
      if( var->area() < 0 )
     {
         var->invert();
     }

     *z= var;
  }
   cInterp *newinterp( Int ityp )
  {
      cInterp *val=NULL;
      switch( ityp )
     {
         case( interp_spline ):
        {
            val= new cSpline();
            break;
        }
         case( interp_cspline ):
        {
            val= new cCSpline();
            break;
        }
         case( interp_polyline ):
        {
            val= new cPolyline();
            break;
        }
         case( interp_cpolyline ):
        {
            val= new cCPolyline();
            break;
        }
         case( interp_straight ):
        {
            val= new cStraight();
            break;
        }
         case( interp_bezier ):
        {
            val= new cBzr();
            break;
        }
         case( interp_circle ):
        {
            val= new cCircle();
            break;
        }
     }
      return val;
  }
