using namespace std;

#  include <geo/2d/bezier.h>

   cBzr::cBzr()
  {
      s= NULL;
      x[0]= NULL;
      x[1]= NULL;
      wrk0[0]=NULL;
      wrk0[1]=NULL;
      wrk1[0]=NULL;
      wrk1[1]=NULL;
      wrk2[0]=NULL;
      wrk2[1]=NULL;
      wrk3[0]=NULL;
      wrk3[1]=NULL;
      n= 0;
  }

   cBzr::~cBzr()
  {
      cleanup();
  }

   void cBzr::cleanup()
  {
      n=0;
      delete[] wrk0[0]; wrk0[0]= NULL;
      delete[] wrk0[1]; wrk0[1]= NULL;
      delete[] wrk1[0]; wrk1[0]= NULL;
      delete[] wrk1[1]; wrk1[1]= NULL;
      delete[] wrk2[0]; wrk2[0]= NULL;
      delete[] wrk2[1]; wrk2[1]= NULL;
      delete[] wrk3[0]; wrk2[0]= NULL;
      delete[] wrk3[1]; wrk2[1]= NULL;
      delete[] s;      s= NULL;
      delete[] x[0];    x[0]= NULL;
      delete[] x[1];    x[1]= NULL;
  }

   void cBzr::build( Int N, Real *X[2] )
  {
      Int i;
      Real ds, dx, dy;

      cleanup();
      n= N;

      x[0]= new Real[n];
      x[1]= new Real[n];
      for ( i=0; i<n; i++ )
     {
         x[0][i]= X[0][i];
         x[1][i]= X[1][i];
     }
 
      ds= 0.;
      s= new Real[n];
      s[0]= 0.;
      for ( i=1; i<n; i++ )
     {
         dx= x[0][i]-x[0][i-1];
         dy= x[1][i]-x[1][i-1];
         ds= sqrt( dx*dx + dy*dy );
         s[i]= s[i-1] + ds;
     } 
      wrk0[0]= new Real[n];
      wrk0[1]= new Real[n];
      wrk1[0]= new Real[n];
      wrk1[1]= new Real[n];
      wrk2[0]= new Real[n];
      wrk2[1]= new Real[n];
      wrk3[0]= new Real[n];
      wrk3[1]= new Real[n];
  
  }

   void cBzr::copy( cInterp **var )
  {
      cBzr *tmp;
      if( !(*var) )
     {
         tmp= new cBzr();
         tmp->build( n, x );
        *var= tmp;
         cInterp::copy( var );
     }
      else
     {
         cout << "cannot copy into existing object (cBzr::copy)\n";
     } 
  }

   void cBzr::bzint( Int i0, Int n1, Real t, Real *y, Real *dy, Real *z[], Real *dz[] )
  {
  
      Int     i, j;
  
      Real z0[2],z1[2],y0[2],dy0[2],dz0[2],dz1[2];

      for( i=i0;i<i0+n1;i++ )
     {
         wrk0[0][i]= z[0][i];
         wrk0[1][i]= z[1][i];
         wrk1[0][i]=dz[0][i];
         wrk1[1][i]=dz[1][i];
     }

       z1[0]= wrk0[0][i0];
       z1[1]= wrk0[1][i0];
      dz1[0]= wrk1[0][i0];
      dz1[1]= wrk1[1][i0];
    
      j=0;
      for( i=i0+1;i<i0+n1;i++ )
     {

         z0[0]=    z1[0];
         z0[1]=    z1[1];
         dz0[0]=  dz1[0];
         dz0[1]=  dz1[1];
         z1[0]=  wrk0[0][i];
         z1[1]=  wrk0[1][i];
         dz1[0]= wrk1[0][i];
         dz1[1]= wrk1[1][i];
	
         y0[0]= t*z1[0]+ (1.-t)*z0[0];
         y0[1]= t*z1[1]+ (1.-t)*z0[1];

         dy0[0]= z1[0]-z0[0];
         dy0[1]= z1[1]-z0[1];
         dy0[0]+= t*dz1[0]+ (1-t)*dz0[0];
         dy0[1]+= t*dz1[1]+ (1-t)*dz0[1];

         wrk2[0][j]=  y0[0]; 
         wrk2[1][j]=  y0[1];
         wrk3[0][j]= dy0[0]; 
         wrk3[1][j]= dy0[1];
	
         j++;
     }
  
      if( n1 > 2 )
     {
         bzint( 0,n1-1,t,y,dy,wrk2,wrk3 );
     }
      else
     {
         y[0]=  wrk2[0][0];
         y[1]=  wrk2[1][0];
         dy[0]= wrk3[0][0];
         dy[1]= wrk3[1][0];
     }
  
  }

   void cBzr::interp( Real t, Real *y, Real *dy )
  {
      setv( 0,n, ZERO,wrk1[0] );
      setv( 0,n, ZERO,wrk1[1] );
      bzint( 0, n, t, y, dy, x,wrk1 );
  }

   Real cBzr::length()
  {
      return s[n-1];
  }

   void cBzr::rotate( Real *xr, Real dt )
  {
      rotv2( 0,n, xr,dt, x );
      na= 0;
  }

   void cBzr::translate( Real *dx )
  {
      Int ip;
      for( ip=0;ip<n; ip++ )
     {
         x[0][ip]+= dx[0];
         x[1][ip]+= dx[1];
     }
      na= 0;
  }

   void cBzr::mirror( Real *x0, Real *l0 )
  {
      mirv2( 0,n, x0,l0, x );
      na= 0;
  }

   void cBzr::scale( Real *x0, Real f )
  {
      Int i;
      sclv2( 0,n, x0,f, x );
      for( i= 0;i<n;i++ )
     {
         s[i]*=f;
     }
      na= 0;
  }

   void cBzr::fillet( Real f, Real t0, cInterp *ln0, Real t1, cInterp *ln1 )
  {
      Real x0[2],dx0[3];
      Real x1[2],dx1[3];
      Real dx[3];
      Real x2[2];
      Real x3[2];
      Real *xdf[2];
      Int   ndf;
      ln0->interp( t0,x0,dx0 );
      ln1->interp( t1,x1,dx1 );
      xdf[0]= new Real[4];
      xdf[1]= new Real[4];

      dx0[2]= norm22( dx0 ); 
      dx1[2]= norm22( dx1 ); 
      sub2( x0,x1, dx );
      dx[2]= norm22( dx );
      dx[2]*= f;
      sclv2( dx[2]/dx0[2],dx0 );
      sclv2( dx[2]/dx1[2],dx1 );
      x2[0]= x0[0]+ dx0[0];   
      x2[1]= x0[1]+ dx0[1];   
      x3[0]= x1[0]- dx1[0];   
      x3[1]= x1[1]- dx1[1];   

      xdf[0][0]= x0[0];
      xdf[0][1]= x2[0];
      xdf[0][2]= x3[0];
      xdf[0][3]= x1[0];

      xdf[1][0]= x0[1];
      xdf[1][1]= x2[1];
      xdf[1][2]= x3[1];
      xdf[1][3]= x1[1];

      ndf= 4;
         
      build( ndf,xdf );

      delete[] xdf[0];
      delete[] xdf[1];
  }

  void cBzr::pickle( size_t *len, pickle_t *buf )
 {
     pckle( len,n,buf );
     pckle( len,n,x[0],buf );
     pckle( len,n,x[1],buf );
 }
  void cBzr::unpickle( size_t *len, pickle_t buf )
 {
     Int m;
     unpckle( len,&n,buf );
     unpckle( len,&m,x+0,buf );
     unpckle( len,&m,x+1,buf );
     build( n,x );
 }

   void cBzr::get( cTabData *var )
  {
      Int i;
      cTabItem *tmp;
      cInterp::get( var );
      tmp= new cTabItem();   tmp->vrange=true;                       var->append( tag+"-bezier-support-start",tmp ); 
      for( i=0;i<n;i++ )
     {
         tmp= new cTabItem( x[0][i] ); tmp->n=2; tmp->hlp= "bezier support points";
                                                 tmp->set( 0,x[0][i]),
                                                 tmp->set( 1,x[1][i]), var->append( "x"+strc(i),tmp );
     }
      tmp= new cTabItem();   tmp->vrange=true;                       var->append( tag+"-bezier-support-end",tmp ); 

  }
   void cBzr::set( cTabData *var )
  {
      cTabItem *tmp;
      Real *x0[2];
      Int   i,j; 
      Int ist,ien;
      ist= var->where( tag+"-bezier-support-start" );
      ien= var->where( tag+"-bezier-support-end" );
      cout << "there are "<<ien-ist<<" things\n";
      x0[0]= new Real[ien-ist-1];
      x0[1]= new Real[ien-ist-1];
      j=0;
      for( j=0,i=ist+1;i<ien;i++,j++ )
     {
         tmp= var->which(i);
         tmp->get( 0,x0[0]+j );
         tmp->get( 1,x0[1]+j );
         cout << x0[0][j]<<" "<<x0[1][j]<<"\n";
     }
      build( ien-ist-1,x0 );
      delete[] x0[0];
      delete[] x0[1];
 
  }
