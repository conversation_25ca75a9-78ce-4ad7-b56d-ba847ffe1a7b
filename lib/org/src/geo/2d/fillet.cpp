   using namespace std;

#  include <geo/2d/fillet.h>
#  include <assert.h>

   void settype(Int ityp, fillet_t  *var)
  {
    if(ityp<-1 || ityp>2 )
   {
      cout<<" THE TYPE INDEX NEED TO BE IN THE RANGE OF -1 TO 2! SET AGAIN! \n";
      return;
   }
          if (ityp==-1) {var->typ= fillet_bad; }
     else if (ityp==0)  {var->typ= fillet_arc; }
     else if (ityp==1)  {var->typ= fillet_bzr; }
     else if (ityp==2)  {var->typ= fillet_cmf; }
  }

   bool valid( fillet_t *var )
  {
      return valid( var->stat );
  }

   void fillet( fillet_t *fl, Real *z0, cInterp *p0, bool i0, Real *z1, cInterp *p1, bool i1, cInterp **f, Real *d0, Real *d1 )
  {

      cCircle    *crc;
      cBzr       *bzr;
      cStraight  *cmf;

      Real        s0,s1;
      Real        l0,l1;
      Int         k0,k1;
      Real        y0[2],y1[2],dy[2];
 
      inter_t     stat;

      k0= 1;
      k1= 1;
      if( !i0 ){ k0= -1; };
      if( !i1 ){ k1= -1; };

      assert( !(*f) );
      s0=*z0;
      s1=*z1;

      if( fl->r > 0 )
     {
         l0= p0->length();
         l1= p1->length();
         switch( fl->typ )
        {
          
            case( fillet_arc ):
           {
               s0-= k0*fl->r/l0;
               s1+= k1*fl->r/l1;
               crc= new cCircle();
               crc->fillet( fl->r, &s0,p0,i0,&s1,p1,i1, &(fl->stat) );
               if( valid( fl ) )
              {
                 *d0= fl->stat.rdat[0][0];
                 *d1= fl->stat.rdat[1][0];
                 *z0= s0;
                 *z1= s1;
                 *f= crc;
              }
               else
              {
                  delete crc;
              }
               break;
           }
            case( fillet_bzr ):
           {
               s0-= k0*fl->r/l0;
               s1+= k1*fl->r/l1;
               bzr= new cBzr();
               bzr->fillet( fl->r, s0,p0,s1,p1 );
              *z0= s0;
              *z1= s1;
              *d0= 0.;
              *d1= 1.;
              *f= bzr;
               break;
           }
            case( fillet_cmf ):
           {
               s0=*z0;
               s1=*z1;
               cmf= new cStraight();
               cmf->chamfer( fl->r, &s0,p0,&s1,p1 );
              *z0= s0;
              *z1= s1;
              *d0= 0.;
              *d1= 1.;
              *f= cmf;
               break;
           }
            case( fillet_bad ):
           {
               assert( false );
           }
        }
     }
      if( !(*f) )
     {
         p0->inters( p1, &s0,&s1, &stat );
         if( valid(stat) )
        {
           *z0= s0;
           *z1= s1;
//          cout << "replacing fillet with intersection "<<s0<<" "<<s1<<" "<<stat.y1[0]<<" "<<stat.y1[1]<<"\n";
        }
         else
        {
            if((s0>1||s0<0)||(s1>1||s1<0))
           {
              Real res;
              p0->mindisf2(p1, &s0, &s1, &res);
           }

            p0->interp( s0,y0,dy );    
            p1->interp( s1,y1,dy );    
            cmf= new cStraight(); 
            cmf->build( y1,y0 );
           *f= cmf;
           *d0= 0.;
           *d1= 1.;
//          cout << "replacing fillet with straight line \n";
//          cin.get();
        }
     }
  }

