   using namespace std;

#  include <fstream>
#  include <geo/2d/polyset.h>
#  include <assert.h>

   cPolyset::cPolyset()
  {
      np=0;
      mp=0;
      pl= NULL;
      xdef[0]=NULL;
      xdef[1]=NULL;

  }

   cPolyset::~cPolyset()
  {
      clear();
  }

   void cPolyset::clear()
  {
      Int  ip;
      delete[] xdef[0];
      delete[] xdef[1];
      for( ip=0;ip<np;ip++ )
     {
         delete pl[ip]; pl[ip]= NULL;
     }
      delete[] pl; pl=NULL;
      np= 0;
      mp= 0;
  }

   void cPolyset::add( cCPolyline *p )
  {
      Int      dsize= ITPDSIZE;
      cInterp *tmp=NULL;
      if( mp == np )
     {
         realloc( &mp,dsize, &pl ); 
     }
      p->copy( &tmp );
      pl[np++]= (cCPolyline*)tmp;
  }

   void cPolyset::copy( cPolyset **p )
  {
      assert( !(*p) ); 
      cPolyset *var;
      var= new cPolyset();
      for( Int i=0;i<np;i++ )
     {
         var->add( pl[i] );
     }
     *p= var;
  }

   void cPolyset::crossings( cPolyset *p, crossings_t *var, bool flag )
  {
      Int i0,i1,i;
      Int  n;
      Real l=0;

      l= big;
      for( i0=0;i0<np;i0++ )
     {
         l= fmin( l,pl[i0]->length() );
     }
      for( i1=0;i1<p->np;i1++ )
     {
         l= fmin( l,p->pl[i1]->length() );
     }
      l/= 5.; 
      for( i0=0;i0<np;i0++ )
     {
         pl[i0]->apprx( l, 0.2 );
     }
      for( i1=0;i1<p->np;i1++ )
     {
         p->pl[i1]->apprx( l, 0.2 );
     }

      for( i0=0;i0<np;i0++ )
     {
         for( i1=0;i1<p->np;i1++ )
        {
            n= var->n;
            pl[i0]->crossings( p->pl[i1], var );
            for( i= n;i<var->n;i++ )
           {
                var->idat[0][i]= i0;
                var->idat[1][i]= i1;
           }
        }
     }

      collect( var );
      sort( var );
      prune( var, flag );
  }

   cPolyset::cPolyset( cPolyset *p1, cPolyset *p2 , bool flag )
  {

      Int              ic,ip,io,inxt,il,ic0;
      cPolyset        *p[2];
      cCPolyline      *var,*tmp;
      Real             y[2];
      Real             s0,s1;
      bool             dum;
      Int             *iwrk[2];

      crossings_t      ct;

      np=0;
      mp=0;
      pl=NULL;
      xdef[0]=NULL;
      xdef[1]=NULL;

      p[0]= p1;
      p[1]= p2;
      iwrk[0]= new Int[p1->np];
      iwrk[1]= new Int[p2->np];

      setv( (Int)0,p1->np,(Int)0,iwrk[0] );
      setv( (Int)0,p2->np,(Int)0,iwrk[1] );

      init( &ct );
      p[0]->crossings( p[1], &ct, flag ); 

      if( ct.n > 0 )
     {
         ic=0;
         while( true )
        {
            ip=0;
            io=1;

            dum= ( ct.idat[2][ic] == 0 );
            if( ip == 0 ){ dum= !dum; };
            if( dum^flag ){ swap( &ip,&io ); }

            ic0= ic;
            start( ic,&ct);
//          print( &ct );

// start a new polygon
            var= new cCPolyline();

            while( true )
           {
               s0=   ct.s[ip][ic];
               il=   ct.idat[ip][ic];
               iwrk[ip][il]= 1;
               tmp= p[ip]->pl[il];

// look for the next crossing on the current polygon
               ic++;
               inxt= nextc( s0,ip,ic,&ct );
// wrap around the origin if needed
               if( inxt == -1 )
              {
                  s1= 1.;
                  var->add( s0,s1,tmp );
                  s0=0;
                  inxt= nextc( -big,ip,ic,&ct );
              }
               swap( ic,inxt, &ct );

// add a new tract
               s1= ct.s[ip][ic];
               var->add( s0,s1,tmp );

// change to the other polyset
               swap( &ip,&io );

               if( ct.idat[4][ic] == ic0 )
              { 
                  add( var );
                  delete var; var= NULL;
                  break;
              }
           }

            if( ic == ct.n-1 ){ break; };
            ic++;
        }
     }

// collect leftovers
      ip= 0;
      io= 1;
      for( ic=0;ic<2;ic++ )
     {
         for( il=0;il<p[ip]->np;il++ )
        {
            if( iwrk[ip][il] == 0 )
           {
               tmp= p[ip]->pl[il];
               tmp->any( y );
               if( (((p[io]->inside(y)))^flag) )
              {
                  add( tmp );
              }
           }
        }
         swap( &io,&ip );
     }
      delete[] iwrk[0];
      delete[] iwrk[1];


  }

   cPolyset::cPolyset( cPolyset *p1, cPolyset *p2 , bool flag, crossings_t *ct )
  {

      Int              ic,ip,io,inxt,il,ic0,k;
      cPolyset        *p[2];
      cCPolyline      *var,*tmp;
      Real             y[2];
      Real             s0,s1,l;
      bool             dum;
      Int             *iwrk[2];

      np=0;
      mp=0;
      pl=NULL;
      xdef[0]=NULL;
      xdef[1]=NULL;

      p[0]= p1;
      p[1]= p2;
      iwrk[0]= new Int[p1->np];
      iwrk[1]= new Int[p2->np];

      setv( (Int)0,p1->np,(Int)0,iwrk[0] );
      setv( (Int)0,p2->np,(Int)0,iwrk[1] );

      init( ct );
      p[0]->crossings( p[1], ct, flag ); 

      if( ct->n > 0 )
     {
         ic=0;
         while( true )
        {
            ip=0;
            io=1;

            dum= ( ct->idat[2][ic] == 0 );
            if( ip == 0 ){ dum= !dum; };
            if( dum^flag ){ swap( &ip,&io ); }

            ic0= ic;
            start( ic,ct);

// start a new polygon
            
            var= new cCPolyline();

            while( true )
           {
               s0=   ct->s[ip][ic];
               il=   ct->idat[ip][ic];
               iwrk[ip][il]= 1;
               tmp= p[ip]->pl[il];
               ct->idat[8][ic]= np;
               ct->s[2][ic]= var->length();

// look for the next crossing on the current polygon
               ic++;
               inxt= nextc( s0,ip,ic,ct );
// wrap around the origin if needed
               if( inxt == -1 )
              {
                  s1= 1.;
                  var->add( s0,s1,tmp );
                  s0=0;
                  inxt= nextc( -big,ip,ic,ct );
              }
               swap( ic,inxt, ct );

// add a new tract
               s1= ct->s[ip][ic];
               var->add( s0,s1,tmp );

// change to the other polyset
               swap( &ip,&io );

               if( ct->idat[4][ic] == ic0 )
              { 
                  l= var->length();
                  for( k=ic0;k<ic;k++ )
                 {
                     ct->s[2][k]/= l;
                 } 
                  add( var );
                  delete var; var= NULL;
                  break;
              }
           }

            if( ic == ct->n-1 ){ break; };
            ic++;
        }
     }

// collect leftovers
      ip= 0;
      io= 1;
      for( ic=0;ic<2;ic++ )
     {
         for( il=0;il<p[ip]->np;il++ )
        {
            if( iwrk[ip][il] == 0 )
           {
               tmp= p[ip]->pl[il];
               tmp->any( y );
               if( (((p[io]->inside(y)))^flag) )
              {
                  add( tmp );
              }
           }
        }
         swap( &io,&ip );
     }
      delete[] iwrk[0];
      delete[] iwrk[1];


  }

   bool cPolyset::inside( Real *x )
  {
      Int i,ipls,imns;
      Real a;
      bool plus,minus,val;
      plus= false; 
      minus=true;
      ipls=0;
      imns=0;
      for( i=0;i<np;i++ )
     {
         a= pl[i]->area();
         val= pl[i]->inside(x);
         if( a > 0 )
        {
            plus= plus || val;
            ipls++;
        }
         else
        {
            minus= minus && val;
            imns++;
        }
     }
      val= false;
      if( ipls > 0 && imns > 0 )
     {
         val= plus && minus;
     }
      else
     {
         if( imns == 0 )
        {
            val= plus;
        }
         if( ipls == 0 )
        {
            val= minus;
        }
     } 
      return( val );
  }

   void cPolyset::invert()
  {
      Int ip;
      for( ip=0;ip<np;ip++ )
     {
         pl[ip]->invert();
     }
  }

   void cPolyset::mirror( Real *x0, Real *l )
  {
      Int ip;
      for( ip=0;ip<np;ip++ )
     {
         pl[ip]->mirror( x0,l );
     }
  }

   void cPolyset::translate( Real *dx )
  {
      Int ip;
      for( ip=0;ip<np;ip++ )
     {
         pl[ip]->translate( dx );
     }
  }

   void cPolyset::rotate( Real *x0, Real dt )
  {
      Int ip;
      for( ip=0;ip<np;ip++ )
     {
         pl[ip]->rotate( x0,dt );
     }
  }

   void cPolyset::scale( Real *x0, Real f )
  {
      Int ip;
      for( ip=0;ip<np;ip++ )
     {
         pl[ip]->scale( x0,f );
     }
  }

   void cPolyset::check( string fnme )
  {
      string   fname;
      ofstream fle;
      Int      ip;
      for( ip=0;ip<np;ip++ )
     {
         fname= fnme+"."+strc( ip )+".dat";
         fle.open( fname.c_str() ); 
         pl[ip]->check( &fle );
         fle.close();
     } 
  }

   void cPolyset::gnuplot( string fname)
  {
      Int ip;
      ofstream fle, fle_ctr;
      string   filename;

      filename=fname+".gnu";
      fle.open(filename.c_str());

      for( ip=0;ip<np;ip++ )
     {
         pl[ip]->check( &fle );
         fle<<"\n";
     }
      fle.close();
  }

   void cPolyset::polygons( Int *n, cCPolyline *p[] )
  {
      Int ip;
      for( ip=0;ip<np;ip++ )
     {
         p[*n]= pl[ip];
       (*n)++;
     }
  }

   void cPolyset::sides( Int *n, Int *l, cPolyline *p[] )
  {
      Int ip,iof;
      iof=l[*n];
      for( ip=0;ip<np;ip++ )
     {
         pl[ip]->sides( &iof,p );
         l[*n]=iof;
       (*n)++;
     }
  }

   void cPolyset::add( cPolyset *var )
  {
      Int i;
      bool rmv=false;
      cPolyset *tmp;
      if( var->np > 0 ) 
     {
         if( np > 0 )
        {
            tmp= new cPolyset( this,var, true );
            clear();
            rmv=true;
        }
         else
        {
            tmp= var;
        }
         for( i=0;i<tmp->np;i++ )
        {
            add( tmp->pl[i] );
        }
         if( rmv ){ delete tmp; };
     }
  }

   void cPolyset::intersect( cPolyset *var )
  {
      Int i;
      cPolyset *tmp;
      bool      rmv;
      if( var->np > 0 ) 
     {
         rmv=false;
         if( np > 0 )
        {
            tmp= new cPolyset( this,var, false );
            clear();
            rmv=true;
        }
         else
        {
            tmp= var;
        }
         for( i=0;i<tmp->np;i++ )
        {
            add( tmp->pl[i] );
        }
         if( rmv ){ delete tmp; };
     }
  }

  /* void cPolyset::getdataset( cPltDataSet *pds )
  {
      Int    i;
      Int   *ilp;
      Int    na=0,ma=0;
      Int    n;
      Real  *ya[3]={NULL,NULL,NULL};
      ilp= new Int[np];
      if( xdef[0] ){ delete[] xdef[0]; xdef[0]= NULL; }
      if( xdef[1] ){ delete[] xdef[1]; xdef[1]= NULL; }

      n=0;
      for( i=0;i<np;i++ )
     {
         //delete[] ya[0]; ya[0]= NULL;
         //delete[] ya[1]; ya[1]= NULL;
         //delete[] ya[2]; ya[2]= NULL;
         na= 0;//changes made by Gan
         pl[i]->approx( 0.,1., 0.2*(pl[i]->length()),0.2, &na,ya );
         ma= n; append( &ma,xdef+0, na,ya[1] );
         ma= n; append( &ma,xdef+1, na,ya[2] );
         n= ma;
         ilp[i]= n;
         delete[] ya[0]; ya[0]= NULL;
         delete[] ya[1]; ya[1]= NULL;
         delete[] ya[2]; ya[2]= NULL;
     }
//    pds->clear();
      pds->setname( "polyset" );
      pds->setnp( n );
      pds->setnv( 2 );
     // pds->setx(  0,     xdef[0] ); pds->setvname(  0,"x" );
     // pds->setx(  1,     xdef[1] ); pds->setvname(  1,"r" );
      pds->setx(0, n, xdef[0]); pds->setvname(0, "x");
      pds->setx(1, n, xdef[1]); pds->setvname(1, "x");
      for( i=0;i<np-1;i++ )
     {
        pds->addbrk(ilp[i]);
     }
      delete[] ilp;
  }*/

   cUid *cPolyset::select( cBbox *data )
  {
      cUid *val=NULL;
      Int i;
      for( i=0;i<np;i++ )
     {
         val= pl[i]->select( data );
         if( val ){ break; };
     }
      return val;
  }

   void cPolyset::apprx()
  {
      Int i;
      for( i=0;i<np;i++ )
     {
         pl[i]->apprx( pl[i]->length()/5.,0.2);
     }
  }

