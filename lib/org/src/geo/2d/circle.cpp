   using namespace std;

#  include <fstream>
#  include <geo/2d/circle.h>

   cCircle::cCircle()
  {
      dir=1;
      ct= 1;
      st= 0;
      r=-big;
      x[0]= -big;
      x[1]= -big;
  }

   cCircle::~cCircle()
  {
      ct= 1;
      st= 0;
      r=-big;
      x[0]= -big;
      x[1]= -big;
  }

   void cCircle::build( Real r0, Real ct0, Real st0, Real *x0 )
  {
      Real l;
      r= r0;
      l= ct0*ct0+ st0*st0;
      l= sqrt(l);
      ct= ct0/l;
      st= st0/l;
      x[0]= x0[0];
      x[1]= x0[1];
  }

   void cCircle::copy( cInterp **var )
  {
      cCircle *tmp;
      if( !(*var) ) 
     {
         tmp= new cCircle();
         tmp->cCircle::build( r,ct,st,x );
        *var= tmp;
         cInterp::copy( var ); 
     }
      
  }

   Real cCircle::length()
  {
      return r*pi2;
  }

   void cCircle::wrap( Real *w )
  {
      while( *w > 1.  )
     {
       (*w)-= 1.;
     }
      while( *w < 0.  )
     {
       (*w)+= 1.;
     }
  }
   
   void cCircle::interp( Real w, Real *y, Real *dy )
  {
      Real cw,sw,tmp;
      wrap( &w );
      w*= pi2;
      w*= dir;
      cw= cos(w);
      sw= sin(w);
      tmp= cw;
      cw= tmp*ct- sw*st;
      sw= tmp*st+ sw*ct;
      y[0]= r*cw+x[0];
      y[1]= r*sw+x[1];
      dy[0]= -r*sw*pi2;
      dy[1]=  r*cw*pi2;
  }

   void cCircle::fillet( Real rf, Real *s0, cInterp *p0, bool k0, Real *s1, cInterp *p1, bool k1, inter_t *stat )
  {

      Real l0,l1,l2,l3,l;

      Real r0,r1;
      Real xf[2];
      Real y0[2],dy0[2];
      Real y1[2],dy1[2];
      Real y2[2],dy2[2];
      Real y3[2],dy3[2];
      Real dyo[2],dy[2];
      Real t0[2],t1[2];
      Real t2[2],t3[2];
      Real n0[2],n1[2];
      Real det;
      Real a00,a01,a10,a11;
      Real dr0,dr1;
      Real ds0,ds1;
      Real rlx,fct0,fct1;
      
      rlx= ITPRLX0;

      p0->interp( *s0,y0,dy0 );
      p1->interp( *s1,y1,dy1 );

      idv2( dy0,t0 );
      norml( 2,t0 );
      idv2( t0,n0 );
      rot90( n0 );

      idv2( dy1,t1 );
      norml( 2,t1 );
      idv2( t1,n1 );
      rot90( n1 );

      if( !k0 ){ rot180( t0 );rot180( n0 ); };
      if( !k1 ){ rot180( t1 );rot180( n1 ); };
/*    if( !k0 ){ rot180( n0 ); };
      if( !k1 ){ rot180( n1 ); };*/

      sub2( y1,y0,dyo );
      idv2( dyo, dy );

      a00= dyo[0]* n0[1]- dyo[1]* n0[0]; 
      a10= dyo[0]* t0[1]- dyo[1]* t0[0];

      a01= dyo[0]* n1[1]- dyo[1]* n1[0];
      a11= dyo[0]* t1[1]- dyo[1]* t1[0];

      fct0=1.; if(  a00*a10 > 0 ){ fct0= -1.; };
      fct1=1.; if(  a01*a11 < 0 ){ fct1= -1.; };

      stat->res= big;
      stat->it= 0;
      while( stat->res > ITPPTOL )
     {
   

// find the fillet centre
         det= t1[0]*n0[0]+ t1[1]*n0[1];
         stat->det= det;
         if( fabs(stat->det) < small )
        {
            break;
        }

         r0=  ( t1[0]*dy[0]+ t1[1]*dy[1] )/det;
         r1=  ( t0[0]*dy[0]+ t0[1]*dy[1] )/det;

         xf[0]= 0.5*( y0[0]+ r0*n0[0]+ y1[0]+ r1*n1[0] );
         xf[1]= 0.5*( y0[1]+ r0*n0[1]+ y1[1]+ r1*n1[1] );
      
// compute the derivatives of the radii with  respect to s0 and s1
         a00= -( t1[0]*dy0[0]+ t1[1]*dy0[1] )/det;
         a01=  ( t1[0]*dy1[0]+ t1[1]*dy1[1] )/det;

         a10= -( t0[0]*dy0[0]+ t0[1]*dy0[1] )/det;
         a11=  ( t0[0]*dy1[0]+ t0[1]*dy1[1] )/det;

         dr0= rf-r0;
         dr1= rf-r1;
         stat->res= fmax( fabs(dr0),fabs(dr1) );

         det= a00*a11- a01*a10;
         ds0= ( a11*dr0- a01*dr1 )/det;
         ds1= (-a10*dr0+ a00*dr1 )/det;

        *s0+= rlx*ds0;
        *s1+= rlx*ds1;

         stat->ds1= ds0;
         stat->ds2= ds1;

         p0->interp( *s0,y0,dy0 );
         p1->interp( *s1,y1,dy1 );

         idv2( y0,stat->y1 );
         idv2( y1,stat->y2 );
         idv2( dy0,stat->dy1 );
         idv2( dy1,stat->dy2 );

         rlx*= ITPDRLX;
         rlx= fmin( rlx,ITPMRLX );

         sub2( y1,y0, dy );

         idv2( dy0,t0 );
         norml( 2,t0 );
         idv2( t0,n0 );
         rot90( n0 );
         sclv2( fct0,n0 );

         idv2( dy1,t1 );
         norml( 2,t1 );
         idv2( t1,n1 );
         rot90( n1 );
         sclv2( fct1,n1 );

         if( !k0 ){ rot180( t0 );rot180( n0 ); };
         if( !k1 ){ rot180( t1 );rot180( n1 ); };

//       cout << ds0<<" "<<ds1<<"\n";
         if( stat->it++ > ITPMXIT )
        {
            break;
        }
     }

      if( valid( *stat ) )
     { 

/*       cout << "ok, got here\n";
         cout << *s0 <<" "<<*s1<<"\n";
         cout << "point on obj0 "<<y0[0]<<" "<<y0[1]<<"\n";
         cout << "point on obj1 "<<y1[0]<<" "<<y1[1]<<"\n";
         cout << "circle center "<<xf[0]<<" "<<xf[1]<<"\n";*/

         indp=false;

         r= rf;
         ct= 1.;
         st= 0.;
         idv2( xf,x );

         sub2( y0,x, dy0 );
         sub2( y1,x, dy1 );
         l0= atan2( dy0[1],dy0[0] );
         l1= atan2( dy1[1],dy1[0] );

         l2= 0.5*( l0+l1 );
         interp( l2/(pi2),y2,dy2 ); 
         idv2( dy2,t2 );
         l3= l2+pi;
         interp( l3/(pi2),y3,dy3 );
         idv2( dy3,t3 );

         sub2( y2,y0, dy0 );
         sub2( y3,y0, dy1 );

         sub2( y1,y2, dy2 );
         sub2( y1,y3, dy3 );

//       Real sg0,sg1,sg2,sg3;
//       sg1= t0[0]*dy1[0]+ t0[1]*dy1[1];
//       sg3= t1[0]*dy3[0]+ t1[1]*dy3[1];

         Real sg0,sg2;
         sg0= t0[0]*dy0[0]+ t0[1]*dy0[1];
         sg2= t1[0]*dy2[0]+ t1[1]*dy2[1];

         if( sg0 > 0 && sg2 > 0 )
        {
            det= vangl2( y0,y2, xf )+ vangl2( y2,y1, xf );
            l= l2;
        }
         else
        {
            det= vangl2( y0,y3, xf )+ vangl2( y3,y1, xf );
            l= l3;
        }

         if( det > 0 )
        {
            if( l0 > l ){ l0-= pi2; };
            if( l1 < l ){ l1+= pi2; };
        }
         else
        {
            if( l0 < l ){ l0+= pi2; };
            if( l1 > l ){ l1-= pi2; };
        }

         ct= cos(l);
         st= sin(l);

         l0-= l;
         l1-= l;

         l0/= pi2;
         l1/= pi2;

         stat->rdat[0][0]= l0;
         stat->rdat[1][0]= l1;

//       cout << x[0]<<" "<<x[1]<<"\n";

     }
  }

   void cCircle::rotate( Real *xr, Real dt )
  {
      Real cdt,sdt,tmp;
      rotv2( xr,dt,x );
      cdt= cos(dt);
      sdt= sin(dt);
      tmp= ct;
      ct= tmp*cdt- st*sdt;
      st= tmp*sdt+ st*cdt;
      na=0;
  }

   void cCircle::translate( Real *dx )
  {
      x[0]+= dx[0];
      x[1]+= dx[1];
      na=0;
  }

   void cCircle::mirror( Real *x0, Real *l0 )
  {
      Real    cl,sl,cdt,sdt,tmp;
      
      mirv2( x0,l0,x );
      cl= l0[0];
      sl= l0[1];
      tmp= cl*cl+ sl*sl;
      tmp= sqrt( tmp );
      if( tmp > small )
     {
         cl/= tmp;
         sl/= tmp;
         cdt= cl*ct+ sl*st;
         sdt=-cl*st+ sl*ct;
         tmp= cdt;
         cdt= tmp*cdt- sdt*sdt;
         sdt= 2*tmp*sdt;
         tmp= ct;
         ct= tmp*cdt- st*sdt;
         st= tmp*sdt+ st*cdt;
     }
      dir= -dir;
  }

   void cCircle::scale( Real *x0, Real f )
  {
      sclv2( x0,f, x );
      r*= f;
  }

   void cCircle::displace( cInterp **var, Real *l0, Real d0, Real *l1, Real d1 )
  {
      cCircle *tmp;
      if( d0 == d1 )
     {
         copy( var );
         tmp= (cCircle*)*var;
         tmp->r+= d0;
     }
      else
     {
         cInterp::displace( var,l0,d0,l1,d1 );
     }
  }

   void fillets( Int n, cInterp **var, Real *r )
  {
      cCircle *tmp;
      cInterp **flt;
      inter_t   stat;
      Real      l0,l1;
      Real      s0,s1;
      Real     *lim[2];
      Real     *flim[2];
      Real      rf;
      Int       i;

      flt= new cInterp*[n];
      setv( 0,n, (cInterp*)NULL, flt );
      lim[0]=  new Real[n];
      lim[1]=  new Real[n];
      flim[0]= new Real[n];
      flim[1]= new Real[n];

      l1= var[0]->length();
      lim[0][0]= 0.;
      for( i=1;i<n;i++ )
     {
         l0= l1;   
         l1= var[i]->length();
         rf= r[i-1];
         lim[1][i-1]= 1;
         lim[0][i]= 0;
         if( rf > small )
        {
            tmp= new cCircle();
            s0= 1.-rf/l0;
            s1= rf/l1;
//          cout << "attempt fillet "<<l0<<" "<<l1<<" s0 "<<s0<<" s1 "<<s1<<" "<<rf<<"\n";
            tmp->fillet( rf, &s0,var[i-1],true,&s1,var[i],true, &stat );
//          cout << stat.res<<" "<<valid( stat )<<"\n";
            if( valid( stat ) )
           {
//             tmp->copy( flt+i-1 );
               flim[0][i-1]= stat.rdat[0][0];
               flim[1][i-1]= stat.rdat[1][0];
               lim[1][i-1]= s0;
               lim[0][i]= s1;
           }
            else
           {
               ofstream fle( "failed.dat" );
               tmp->check( &fle );
               fle.close();
           }
            delete tmp; tmp= NULL;
        }
     }

      l0= l1;   
      l1= var[0]->length();
      lim[1][n-1]= 1;
      lim[0][0]= 0;
      rf= r[n-1];
      if( rf > small )
     {
         tmp= new cCircle();
         s0= 1.-rf/l0;
         s1= rf/l1;
//       cout << "attempt fillet "<<l0<<" "<<l1<<" s0 "<<s0<<" s1 "<<s1<<" "<<rf<<"\n";
         tmp->fillet( rf, &s0,var[n-1],true,&s1,var[0],true, &stat );
//       cout << stat.res<<" "<<valid( stat )<<"\n";
         if( valid( stat ) )
        {
//          tmp->copy( flt+n-1 );
            flim[0][n-1]= stat.rdat[0][0];
            flim[1][n-1]= stat.rdat[1][0];
            lim[1][n-1]= s0;
            lim[0][0]= s1;
        }
         delete tmp; tmp= NULL;
     }
      
      delete[] flt; flt=NULL;
  }



   void cCircle::unpack( Real l0, Real l1, Int *n, Int *m, Real *lim[], cInterp ***var )
  {
      cInterp::unpack( l0,l1,n,m,lim,var );
  }

   void cCircle::pickle( size_t *len, pickle_t *buf )
  {
      cInterp::pickle( len,buf );
      pckle( len,r,buf );
      pckle( len,ct,buf );
      pckle( len,st,buf );
      pckle( len,2,x,buf );
      pckle( len,dir,buf );
  }
   void cCircle::unpickle( size_t *len, pickle_t  buf )
  {
      Real r0,ct0,st0,x0[2];
      cInterp::unpickle( len,buf );
      unpckle( len,&r0,buf );
      unpckle( len,&ct0,buf );
      unpckle( len,&st0,buf );
      unpckle( len,2,x0,buf );
      unpckle( len,&dir,buf );
      build( r0,ct0,st0,x0 );
  }

   void cCircle::get( cTabData *var )
  {
      cTabItem *tmp;
      cInterp::get( var );
      tmp= new cTabItem( x[0] );   tmp->hlp=tmp->hlp1= "centre";    tmp->n=2; var->append( tag+"-x0", tmp );
      tmp= new cTabItem( ct    );  tmp->hlp=tmp->hlp1= "reference"; tmp->n=2; var->append( tag+"-l0", tmp );
      tmp= new cTabItem( r    );   tmp->hlp=tmp->hlp1= "radius";              var->append( tag+"-r0", tmp );
      tmp= new cTabItem( dir   );  tmp->hlp=tmp->hlp1= "direction";           var->append( tag+"-dir",tmp );
      tmp= new cTabItem(       );                                   var->append( tag+"-circle-end",tmp );
      var->set( "x0", 0,x[0] );
      var->set( "x0", 1,x[1] );
      var->set( "l0", 0,ct );
      var->set( "l0", 1,st );
      var->set( "r0", r   );
      var->set( "dir",dir );
  }
   void cCircle::set( cTabData *var )
  {
      Real r0,ct0,st0,x0[2];
      cInterp::set( var );
      var->get( tag+"-x0", 0,x0+0 );
      var->get( tag+"-x0", 1,x0+1 );
      var->get( tag+"-l0", 0,&ct0 );
      var->get( tag+"-l0", 1,&st0 );
      var->get( tag+"-r0", &r0    );
      var->get( tag+"-dir",&dir   );
      build( r0,ct0,st0,x0 );
  }

   void cCircle::property( string lbl, Int *var )
  { 
      if( lbl == "dir" ){ var[0]= dir; };
  }

   void cCircle::property( string lbl, Real *var )
  { 
      if( lbl == "x0" ){ var[0]= x[0]; var[1]= x[1]; };
      if( lbl == "r0" ){ var[0]= r; };
      if( lbl == "ct" ){ var[0]= ct; };
      if( lbl == "st" ){ var[0]= st; };
  }
