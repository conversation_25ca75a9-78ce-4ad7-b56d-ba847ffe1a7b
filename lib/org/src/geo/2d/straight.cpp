   using namespace std;

#  include <geo/2d/straight.h>

   cStraight::cStraight()
  {
      x0[0]= -big;
      x0[1]= -big;

      x1[0]= -big;
      x1[1]= -big;

      dx[0]= 0.;
      dx[1]= 0.;
     
      l=0.;
  }

   cStraight::~cStraight()
  {
      x0[0]= -big;
      x0[1]= -big;

      x1[0]= -big;
      x1[1]= -big;

      dx[0]= 0.;
      dx[1]= 0.;
  }

   Real cStraight::length()
  {
      return l;
  }
   void cStraight::build( Real *y0, Real *y1 )
  {
      x0[0]= y0[0];
      x0[1]= y0[1];

      x1[0]= y1[0];
      x1[1]= y1[1];

      dx[0]= x1[0]- x0[0];
      dx[1]= x1[1]- x0[1];

      l= dx[0]*dx[0]+ dx[1]*dx[1];
      if(l<=0) {l=0;}
      else {l= sqrt(l);};
  }

   void cStraight::interp( Real w, Real *y, Real *dy )
  {
      y[0]= w*x1[0]+ (1-w)*x0[0];
      y[1]= w*x1[1]+ (1-w)*x0[1];
      dy[0]= dx[0];
      dy[1]= dx[1];
  }

   void cStraight::copy( cInterp **var )
  {
      cStraight *tmp;
      if( !(*var) )
     {
         tmp= new cStraight;
         tmp->cStraight::build( x0,x1 );
        *var= tmp;
         cInterp::copy( var );
     }
      else
     {
         cout << "cannot copy into existing object (cStraight::copy)\n";
     }
  }

   void cStraight::rotate( Real *xr, Real dt )
  {
      rotv2( xr,dt,x0 );
      rotv2( xr,dt,x1 );
      cStraight::build( x0,x1 );
      na=0;
  }

   void cStraight::translate( Real *d )
  {
      x0[0]+= d[0];
      x0[1]+= d[1];
      x1[0]+= d[0];
      x1[1]+= d[1];
      cStraight::build( x0,x1 );
      na=0;
  }

   void cStraight::mirror( Real *x, Real *l )
  {
      mirv2( x,l, x0 );
      mirv2( x,l, x1 );
      cStraight::build( x0,x1 );
      na=0;
  }

   void cStraight::scale( Real *xs, Real f )
  {
      sclv2( xs,f,x0 );
      sclv2( xs,f,x1 );
      cStraight::build( x0,x1 );
      na=0;
  }

   void cStraight::chamfer( Real f, Real *l0, cInterp *p0, Real *l1, cInterp *p1 )
  {
    (*l0)-= f/p0->length();
    (*l1)+= f/p1->length();

      Real x0[2],dy[2];
      Real x1[2];

      p0->interp( *l0, x0,dy );
      p1->interp( *l1, x1,dy );

      cStraight::build( x0,x1 );
  }

   void cStraight::debug( string tab )
  {
      cInterp::debug( tab );
      cout << tab <<" straight "<<x0[0]<<" "<<x0[1]<<" "<<x1[0]<<" "<<x1[1]<<"\n";
  }

   void cStraight::shear( Real *x, Real *ds[2] )
  {
      shear2( x,ds, x0 );
      shear2( x,ds, x1 );
  }
  
   void cStraight::pickle( size_t *len, pickle_t *buf )
  {
      cInterp::pickle( len,buf );
      pckle( len,2,x0, buf );
      pckle( len,2,x1, buf );
  }
   void cStraight::unpickle( size_t *len, pickle_t buf )
  {
      Real y0[2],y1[2];
      cInterp::unpickle( len,buf );
      unpckle( len,2,y0, buf );
      unpckle( len,2,y1, buf );
      cStraight::build( y0,y1 );
  }

   void cStraight::get( cTabData *var )
  {
      cTabItem *tmp;
      cInterp::get( var );
     
       tmp= new cTabItem();       var->append( tag+"-straight-start",tmp );
      tmp= new cTabItem( x0[0] ); tmp->n= 2; 
                                  tmp->hlp=tmp->hlp1= "coordinates of the first point through the line"; 
                                  var->append( tag+"-x0",tmp );
      tmp= new cTabItem( x1[0] ); tmp->n= 2; 
                                  tmp->hlp=tmp->hlp1= "coordinates of the second point through the line"; 
                                  var->append( tag+"-x1",tmp );
       tmp= new cTabItem();       var->append( tag+"-straight-end",tmp );
       var->set( tag+"-x0",0,x0[0] );
       var->set( tag+"-x0",1,x0[1] );
       var->set( tag+"-x1",0,x1[0] );
       var->set( tag+"-x1",1,x1[1] );
  }
   void cStraight::set( cTabData *var )
  {
      cTabItem *tmp;
      cInterp::set( var );
      var->get( tag+"-x0",0,x0+0 );
      var->get( tag+"-x0",1,x0+1 );
      var->get( tag+"-x1",0,x1+0 );
      var->get( tag+"-x1",1,x1+1 );
      cStraight::build( x0,x1 );
  }

   void cStraight::property( string lbl, Int *var )
  { 
  }

   void cStraight::property( string lbl, Real *var )
  { 
      if( lbl=="x0"){ var[0]= x0[0]; var[1]= x0[1]; };
      if( lbl=="x1"){ var[0]= x1[0]; var[1]= x1[1]; };
  }
