
   using namespace std;

#  include <geo/2d/cap.h>
#  include <geo/2d/utils.h>
#  include <assert.h>

   void settype(Int indx, Int ityp, cap_t *var)
  {
    if(indx<0 || indx>1 ) 
   {
      cout<<" THE fl INDEX MUST BE 0 OR 1! SET AGAIN! \n";
      return;
   }
    if(ityp<-1 || ityp>2 ) 
   {
      cout<<" THE TYPE INDEX NEED TO BE IN THE RANGE OF -1 TO 2! SET AGAIN! \n";
      return;
   }
          if (ityp==-1) {var->fl[indx].typ= fillet_bad; }
     else if (ityp==0)  {var->fl[indx].typ= fillet_arc; }
     else if (ityp==1)  {var->fl[indx].typ= fillet_bzr; }
     else if (ityp==2)  {var->fl[indx].typ= fillet_cmf; }
  }

   void cap( cap_t *cp, Real *z0, cInterp *p0, Real *z1, cInterp *p1, cInterp **f, Real *u0, Real *u1 )
  {

       cPolyline *var;
       cStraight *t1;
       cInterp    *f1[3]={NULL,NULL,NULL};
       cInterp    *w1[3]={NULL,NULL,NULL};

       Real       *s1[2]; 
       Real       *d1[2]; 

       Real      y0[2],y1[2],dy[2];

       assert( !(*f) );
      
       p0->interp( 1.,y0,dy );
       p1->interp( 0.,y1,dy );
       t1= new cStraight(); 
       t1->build( y0,y1 );

       s1[0]= new Real[3];
       s1[1]= new Real[3];

       d1[0]= new Real[2];
       d1[1]= new Real[2];

       w1[0]= p0; s1[0][0]=0.;s1[1][0]= 1.;
       w1[1]= t1; s1[0][1]=0.;s1[1][1]= 1.;
       w1[2]= p1; s1[0][2]=0.;s1[1][2]= 1.;

       build( 2,cp->fl, w1,f1, s1,d1 );

       var= new cPolyline(); 

      *z0= s1[1][0];
      *z1= s1[0][2];

      *u0= 0.;
      *u1= 1.;

       if((y0[0]==y1[0])&&(y0[1]==y1[1]))
      {
          *u0=0;
          *u1=0;
      }

       if( f1[0] )
      { 
          var->add( d1[0][0],d1[1][0], f1[0] ); 
          delete f1[0];
      }
       var->add( s1[0][1],s1[1][1], t1 );
       delete t1;
       if( f1[1] )
      { 
          var->add( d1[0][1],d1[1][1], f1[1] ); 
          delete f1[1];
      };

      *f= var;

       delete[] s1[0];
       delete[] s1[1];
       delete[] d1[0];
       delete[] d1[1];
       
  }
