using namespace std;

#  include <fstream>
#  include <geo/2d/interp.h>

#  include <geo/2d/straight.h>

   cInterp::cInterp()
  {
      indp= true;
      mxd= -1.;
      mxa= -1.;
      na=0;
      xa[0]= NULL;
      xa[1]= NULL;
      xa[2]= NULL;
      bt= NULL;
  }

   cInterp::~cInterp()
  {
      mxd= -1.;
      mxa= -1.;
      na=0;
      delete[] xa[0]; xa[0]= NULL;
      delete[] xa[1]; xa[1]= NULL;
      delete[] xa[2]; xa[2]= NULL;
      delete bt; bt= NULL;
  }

   void cInterp::plot(Real l0, Real l1,  ofstream *fle)
  {
    Int   i;
      Int   n=0;
      Real *x[3]={NULL,NULL,NULL};
      approx( l0, l1, length()/20.,1./6., &n,x );
     *fle << "\n";
     *fle << "#\n";
     *fle << "\n";
      for( i=0;i<n;i++ )
     {
        *fle << x[1][i]<<" "<<x[2][i]<<" "<<x[0][i]<<"\n";
     }
      delete[] x[0];
      delete[] x[1];
      delete[] x[2];
 
  //    plot(0., 1., fle);
  }

   void cInterp::check( ofstream *fle )
  {
/*    Int   i;
      Real  w;
      Real  y[2],dy[2];
      for( i=0;i<ITPNCHECK;i++ )
     {
         w= (Real)i/(Real)(ITPNCHECK-1);
         interp( w,y,dy );
        *fle << y[0]<<" "<<y[1]<<"\n";
     }*/
 
      Int   i;
      Int   n=0;
      Real *x[3]={NULL,NULL,NULL};
      approx( 0.,1., length()/20.,1./6., &n,x );
     *fle << "\n";
     *fle << "#\n";
     *fle << "\n";
      for( i=0;i<n;i++ )
     {
        *fle << x[1][i]<<" "<<x[2][i]<<" "<<x[0][i]<<"\n";
     }
      delete[] x[0];
      delete[] x[1];
      delete[] x[2];


   // plot(0., 1., fle);
  }

   void cInterp::prjct( Real *y0, Real *s, inter_t *stat )
  {
      Real              y[2],dy[2];
      Real              y1[2];
      Real              n,ds;
      Real              rlx=ITPRLX0;

      stat->it=0;
      stat->res=big;
      while( stat->res > ITPPTOL )
     {
         interp( *s, y,dy );
         n= dy[0]*dy[0]+ dy[1]*dy[1];

         idv2( y,stat->y1 );
         idv2( dy,stat->dy1 );
         stat->det=   n;

         if( fabs(stat->det) < small )
        {
            break;
        }
         ds= ( y0[0]- y[0] )*dy[0]+ ( y0[1]-y[1] )*dy[1];
         ds/=n;

       (*s)+= rlx*ds;
         
         rlx*= ITPDRLX;
         rlx= min( rlx,(Real)ITPMRLX );

         sub2( y1,y,dy );
         idv2( y,y1 );

         stat->res= norminf2( dy );
         stat->ds1=    ds;

         if( ( ++(stat->it) ) > ITPMXIT )
        {
            break;
        }
     }
  }

   void cInterp::inters( cInterp *obj, Real *s1, Real *s2, inter_t *stat )
  { 

      Real       ds1,ds2,n;
      Real       y1[2],y2[2], dy1[2],dy2[2];

//      Real       rlx= ITPRLX0;
      Real       rlx= ITPRLX0*0.5;

      Real       dy[2];

      stat->it=0;
      stat->res=big;

      while( stat->res > ITPPTOL  )
     {

         interp( *s1, y1,dy1 );
         obj->interp( *s2, y2,dy2 );
         sub2( y2,y1,dy );

         n= dy1[0]*dy2[1]- dy1[1]*dy2[0];

         idv2( y1,stat->y1 );
         idv2( y2,stat->y2 );
         idv2( dy1,stat->dy1 );
         idv2( dy2,stat->dy2 );
         stat->det=   n;
         stat->res= norminf2( dy );

         if( fabs(stat->det) < small )
        {
            break;
        }

         ds1= dy[0]*dy2[1]- dy[1]*dy2[0];
         ds2= dy[0]*dy1[1]- dy[1]*dy1[0];

         ds1/= n;
         ds2/= n;

        *s1+= rlx*ds1;
        *s2+= rlx*ds2;

         rlx*= ITPDRLX;
         rlx= min( rlx,(Real)ITPMRLX );

         stat->ds1=    ds1;
         stat->ds2=    ds2;

         if( ( ++(stat->it) )> ITPMXIT )
        {
            break;
        }
     }

  };

   void cInterp::inters( Real *yl0, Real *dyl, Real *s1, Real *s2, inter_t *stat )
  { 

      Real       ds1,ds2,n;
      Real       y1[2],y2[2], dy1[2];
      Real       dy[2]={0., 0};

      Real rlx= ITPRLX0;

      stat->res=big;
      stat->it=0;

      idv2( dyl,stat->dy2 );

      while( stat->res > ITPPTOL )
     {

         interp( *s1, y1,dy1 );

         y2[0]= yl0[0]+ *s2*dyl[0];
         y2[1]= yl0[1]+ *s2*dyl[1];
         n= dy1[0]*dyl[1]- dy1[1]*dyl[0];

         idv2( y1,stat->y1 );
         idv2( y2,stat->y2 );
         idv2( dy1,stat->dy1 );
         stat->det=   n;
         sub2( y2,y1,dy );
         stat->res= norminf2( dy );

         if( fabs(stat->det) < small )
        {
            break;
        }

         ds1= dy[0]*dyl[1]- dy[1]*dyl[0];
         ds2= dy[0]*dy1[1]- dy[1]*dy1[0];

         ds1/= n;
         ds2/= n;

        *s1+= rlx*ds1;
        *s2+= rlx*ds2;

         rlx*= ITPDRLX;
         rlx= min( rlx,(Real)ITPMRLX );

         stat->ds1=    ds1;
         stat->ds2=    ds2;

         if( (++(stat->it) ) > ITPMXIT )
        {
            break;
        }
      }
  };

   cUid *select( Int nsh, cInterp *var[], cBbox *data )
  {
      cUid *val=NULL;
      Int i;
      for( i=0;i<nsh;i++ )
     {
         val= var[i]->select( data );
         if( val ){ break; };
     }
      return val;
  }

   cUid *cInterp::select( cBbox *data )
  {
      cUid     *val=NULL;
      cBbox     var;
      Real      s;
      assert( na > 0 );
      Int i;
      for( i=0;i<na-1;i++ )
     {
         var.resetCorners();
         var.setCorners( i,i+2, 2,xa+1 );
         if( boverlap( &var,data ) )
        {
            cout << "found matching segment ...\n";
            s= xa[0][i]+ xa[1][i];
            s= s/2;
            val= getsrc( s );
        }
         if( val ){ break; };
     }
      return val;
  }

   void cInterp::approx( Real l0, Real l1, Real md, Real ma, Int *ne, Real *xe[] )
  {
      Real    *wrk[6];
      Int      np;
      Int      is,ip,i0,i1;
      Int     *istk[2];
      Int     *iprm;
      Int      nstk;
      Int      mp,mstk,tmp,dsize;
      Real     md2,l,s,c;

      Real     y[2],dy[4];
      Real     y0[2],dy0[4];
      Real     y1[2],dy1[4];

      bool     inv;
      

      dsize= ITPDSIZE;
 
      mp= dsize;
      wrk[0]= new Real[dsize];
      wrk[1]= new Real[dsize];
      wrk[2]= new Real[dsize];
      wrk[3]= new Real[dsize];
      wrk[4]= new Real[dsize];
      wrk[5]= new Real[dsize];

      mstk=dsize;
      istk[0]=new Int[dsize];
      istk[1]=new Int[dsize];

      inv= false;
      if( l0 > l1 )
     {
         inv=true; 
         swap( &l0,&l1 );
     }

      wrk[0][0]=              l0;     
      wrk[0][1]= 0.25*l1+0.75*l0;
      wrk[0][2]= 0.5* l1+0.5* l0;
      wrk[0][3]= 0.75*l1+0.25*l0;
      wrk[0][4]=      l1;

      md2= md*md;

      np= 5;
      for( ip=0;ip<np;ip++ )
     {
         interp( wrk[0][ip],y,dy); 
         wrk[1][ip]=  y[0]; 
         wrk[2][ip]=  y[1];
         wrk[3][ip]= dy[0]; 
         wrk[4][ip]= dy[1];
         wrk[5][ip]= dy[0]*dy[0]+ dy[1]*dy[1];
         wrk[5][ip]= sqrt( wrk[5][ip] );
     }

      nstk=4;
      istk[0][0]= 0; istk[1][0]= 1;
      istk[0][1]= 1; istk[1][1]= 2;
      istk[0][2]= 2; istk[1][2]= 3;
      istk[0][3]= 3; istk[1][3]= 4;

      while( nstk > 0 )
     {
         is= --nstk; 
         i0= istk[0][is]; 
         i1= istk[1][is]; 

         y0[0]=  wrk[1][i0];
         y0[1]=  wrk[2][i0];
         dy0[0]= wrk[3][i0];
         dy0[1]= wrk[4][i0];
         dy0[2]= wrk[5][i0];

         y1[0]=  wrk[1][i1];
         y1[1]=  wrk[2][i1];
         dy1[0]= wrk[3][i1];
         dy1[1]= wrk[4][i1];
         dy1[2]= wrk[5][i1];

         dy[0]= y1[0]-y0[0];
         dy[1]= y1[1]-y0[1];
         dy[2]= dy[0]*dy[0]+ dy[1]*dy[1];
         
         l= dy0[2]*dy1[2];
         s= dy0[0]*dy1[1]- dy0[1]*dy1[0];
         c= dy0[0]*dy1[0]+ dy0[1]*dy1[1]; 
         c/=l;
         s/=l;

         if( ( dy[2] > md2 || fabs(s) > ma || c < 0 ) && (dy[2]>ITPMINLEN) )
        {
            if( np == mp )
           { 
               tmp= mp;
               realloc( &tmp,dsize, wrk+0 ); tmp= mp;
               realloc( &tmp,dsize, wrk+1 ); tmp= mp;
               realloc( &tmp,dsize, wrk+2 ); tmp= mp;
               realloc( &tmp,dsize, wrk+3 ); tmp= mp;
               realloc( &tmp,dsize, wrk+4 ); tmp= mp;
               realloc( &tmp,dsize, wrk+5 ); mp= tmp;
           }
            ip=   np++;
            wrk[0][ip]= 0.5*( wrk[0][i0]+ wrk[0][i1] );
            interp( wrk[0][ip],y,dy); 
            wrk[1][ip]=  y[0]; 
            wrk[2][ip]=  y[1];
            wrk[3][ip]= dy[0]; 
            wrk[4][ip]= dy[1];
            wrk[5][ip]= dy[0]*dy[0]+ dy[1]*dy[1];
            wrk[5][ip]= sqrt( wrk[5][ip] );
            if( nstk >= mstk-1 )
           { 
               tmp= mstk;
               realloc( &tmp,dsize, istk+0 ); tmp= mstk;
               realloc( &tmp,dsize, istk+1 ); mstk= tmp; 
           }
            is= nstk++;
            istk[0][is]= i0; 
            istk[1][is]= ip; 
            is= nstk++;
            istk[0][is]= ip; 
            istk[1][is]= i1; 

        }
     }

      if( np > 0 )
     {
         iprm= new Int[np];
         hsort( np,wrk[0], iprm );
         if( inv )
        {
            reverse( np,iprm );
        }

         dsize= np;
         tmp= (*ne);
         realloc( &tmp,dsize, xe+0 ); tmp= (*ne);
         realloc( &tmp,dsize, xe+1 ); tmp= (*ne);
         realloc( &tmp,dsize, xe+2 ); tmp= (*ne);
         for( ip=0;ip<np;ip++ )
        {
            xe[0][ip+tmp]= wrk[0][iprm[ip]];
            xe[1][ip+tmp]= wrk[1][iprm[ip]];
            xe[2][ip+tmp]= wrk[2][iprm[ip]];
        }
       (*ne)+= np;
         delete[] iprm;
     }

      delete[] wrk[0];
      delete[] wrk[1];
      delete[] wrk[2];
      delete[] wrk[3];
      delete[] wrk[4];
      delete[] wrk[5];

      delete[] istk[0];
      delete[] istk[1];

  }

   void cInterp::apprx( Real md, Real ma )
  {
      Int ia;
      Real xmin[2],xmax[2];
      if( md <= mxd || ma <= mxa || na <= 0 || (!bt) )
     {
         na= 0;
         delete[] xa[0]; xa[0]= NULL;
         delete[] xa[1]; xa[1]= NULL;
         delete[] xa[2]; xa[2]= NULL;
         mxd= md;
         mxa= ma;
         delete bt; bt= NULL;
         approx( 0.,1., md,ma, &na,xa );

         xmin[0]= big;
         xmin[1]= big;
         xmax[0]=-big;
         xmax[1]=-big;
         for( ia=0;ia<na;ia++ )
        {
            xmin[0]= fmin( xmin[0], xa[1][ia] );
            xmin[1]= fmin( xmin[1], xa[2][ia] );
            xmax[0]= fmax( xmax[0], xa[1][ia] );
            xmax[1]= fmax( xmax[1], xa[2][ia] );
        }
         xmin[0]-= 1.e-7;
         xmin[1]-= 1.e-7;
         xmax[0]+= 1.e-7;
         xmax[1]+= 1.e-7;
         delete bt; bt=NULL;
         bt= new cBoxTree();
         bt->init( 2,xmin,xmax );
         for( ia=1;ia<na;ia++ )
        {
            xmin[0]= fmin( xa[1][ia-1], xa[1][ia] );
            xmin[1]= fmin( xa[2][ia-1], xa[2][ia] );
            xmax[0]= fmax( xa[1][ia-1], xa[1][ia] );
            xmax[1]= fmax( xa[2][ia-1], xa[2][ia] );
            xmin[0]-= 1.e-7;
            xmin[1]-= 1.e-7;
            xmax[0]+= 1.e-7;
            xmax[1]+= 1.e-7;
            bt->insert( xmin,xmax, ia-1 );
        }
     }
  }

   void cInterp::copy( cInterp **var )
  { 
      if( !(*var) )
     {
         cout << "method only accepts existing objects ( cInterp::copy ) \n";
     }
      else
     {
         cTag::copy( *var ); 
       (*var)->indp= indp;
     }
  };

   void print( inter_t data )
  {
      cout << "ID: "<<data.idnt<<" ";
      cout << "position: ";
      cout << data.y1[0]<<" ";
      cout << data.y1[1]<<" ";
      cout << "residual: ";
      cout << data.res<<" ";

      cout << "data[0]: ";
      cout << data.rdat[0][0]<<" ";
      cout << data.rdat[0][1]<<" ";
      cout << data.idat[0][0]<<" ";
      cout << data.idat[0][1]<<" ";

      cout << "data[1]: ";
      cout << data.rdat[1][0]<<" ";
      cout << data.rdat[1][1]<<" ";
      cout << data.idat[1][0]<<" ";
      cout << data.idat[1][1]<<" ";

      cout << "direction: ";
      cout << data.idat[0][4]<<" ";
  }


   bool valid( inter_t data )
  {
      bool val;

      val= data.res < ITPPTOL;
      val= val && ( data.it < ITPMXIT );
      val= val && ( fabs(data.det) > small );

      return val;
  }

   void cInterp::boxes(){};

   void incrl( cInterp *p0, cInterp *p1, Real s0, Real *s1, Real *y, Real *r, Real *res )
  {
      Real   dy[2];
      Real   y1[2],dy1[3];
      Real   y0[2],dy0[3];
      Real   n1[2], n0[2];
      Real y2[2],dy2[3],dn1[2],n2[2];
      Real  rhs[2];
      Real  lhs[2][2];
      Real lhsi[2][3];
      Real    d[2];
      Int  ipiv[2];

      Real rlx= 0.1;
      Real det;

      p0->interp( s0,y0,dy0 );
      dy0[2]= norm22( dy0 );
      idv2( dy0, n0 );
      rot90( n0 );
      sclv2( 1./dy0[2],n0 );

      Real eps=1.e-6;

      p1->interp( *s1+eps,y2,dy2 ); 
      dy2[2]= norm22( dy2 );
      idv2( dy2,n2 );
      rot90( n2 );
      sclv2( 1./dy2[2],n2 );


      for( Int it=0;it<20;it++ )
     {

         p1->interp( *s1,y1,dy1 );
         idv2( dy1,n1 );
         rot90( n1 );
         dy1[2]= norm22( dy1 );
         sclv2( 1./dy1[2],n1 );

         if( fabs( eps ) > 1.e-9 )
        {
            sub2( n2,n1, dn1 );
            sclv2( 1./eps,dn1 );
//          break;
        }
         idv2( n1,n2 );


         lhs[0][0]=-dy1[0]-(*r)*dn1[0];
         lhs[0][1]=-dy1[1]-(*r)*dn1[1];

         lhs[1][0]= n0[0]-n1[0];
         lhs[1][1]= n0[1]-n1[1];

         luf2(  lhs[0], lhs[1], lhsi[0],lhsi[1], ipiv, &det );

         rhs[0]= y0[0]-y1[0]+ (*r)*( n0[0]- n1[0] );
         rhs[1]= y0[1]-y1[1]+ (*r)*( n0[1]- n1[1] );

         lus2( lhsi[0],lhsi[1],                          ipiv, d, rhs );
      
       (*s1)-= rlx*d[0];
       (*r)-=  rlx*d[1];

         eps= d[0];

         rlx*=1.2;
         rlx= fmin( 0.99,rlx );

     } 
/*    cout << "\n";
      cout << "# residual "<<d[0]<<" determinant "<<det<<"\n";
      cout << "\n";*/

      y[0]= y0[0]+n0[0]*(*r);
      y[1]= y0[1]+n0[1]*(*r);
      dy[0]= y[0]- ( y1[0]+n1[0]*(*r) );
      dy[1]= y[1]- ( y1[1]+n1[1]*(*r) );
     *res= norminf2( dy );

  }

   Real cInterp::curv( Real s )
  {
      Real eps=1.e-6;
      Real da,val;
      Real y1[2],dy1[3];
      Real y2[2],dy2[3];
      interp( s-eps,y1,dy1 );
      interp( s+eps,y2,dy2 );

      dy1[2]= norm22( dy1 );
      dy2[2]= norm22( dy2 );
      sclv2( 1./dy1[2],dy1 );
      sclv2( 1./dy2[2],dy2 );
      da= vec2( dy1,dy2 );
      da= fabs( da );
      da/= (2*eps);
      val= big;
      if( da > small )
     {
         val= length()/da;
     }
      return val;
  }

   void cInterp::displace( cInterp **var, Real *l0, Real d0, Real *l1, Real d1 )
  {
      Real y0[2],y1[2],dy0[3],dy1[3];
      cStraight *tmp=NULL;
      if( d0 == d1 )
     {
        copy( var );
        interp( 0.5*((*l0)+(*l1)), y0,dy0 );
        dy0[2]= norm22(dy0);
        rot90( dy0 );
        sclv2( d0/dy0[2],dy0 );
      (*var)->translate( dy0 );
     }
      else
     {
        if( !(*var) )
       {
           tmp= new cStraight();
          *var= tmp;
       }
        else
       {
           cout << "cannot displace onto existing object\n";
           exit(1);
       }
        interp( *l0,y0,dy0 );
        interp( *l1,y1,dy1 );
        dy0[2]= norm22(dy0);
        dy1[2]= norm22(dy1);
        rot90( dy0 );
        rot90( dy1 );
        sclv2( d0/dy0[2],dy0 );
        sclv2( d1/dy1[2],dy1 );
        y0[0]+= dy0[0];
        y0[1]+= dy0[1];
        y1[0]+= dy1[0];
        y1[1]+= dy1[1];
        tmp->build( y0,y1 );
       *l0= 0.;
       *l1= 1.;
     }
  }

   void cInterp::debug( string tab )
  {
      cout << tab <<" "<<this<<" "<<length()<<"\n";
  }

   
   void cInterp::unpack( Real l0, Real l1, Int *n, Int *m, Real *lim[], cInterp ***var )
  {
      size_t     dsize=5;
      Int        l;
      Real       p0[2],p1[2],dp[2];
      interp( l0,p0,dp );
      interp( l1,p1,dp );
      if( distinf2( p0,p1 ) > ITPPTOL )
     {

         if( *m== *n )
        {
            l= *m; realloc( &l,dsize, lim+0 ); setv( *m,l,ZERO,lim[0] );
            l= *m; realloc( &l,dsize, lim+1 ); setv( *m,l,ZERO,lim[1] );
            l= *m; realloc( &l,dsize, var   ); setv( *m,l,(cInterp*)NULL,(*var));
           *m=l;
        }
         lim[0][(*n)]= l0;
         lim[1][(*n)]= l1;
         copy( (*var)+(*n) );
       (*n)++;
     }
/*    else
     {
         cout << "WILL NOT UNPACK DEGENERATE SEGMENT\n";
     }*/
  }

   void cInterp::shear( Real *x0, Real *ds[] )
  {
      cout << "cInterp::shear not yet implemented for general case.\n";
      assert( false );
  }

   void cInterp::mindisf2(cInterp *obj, Real *s0, Real *s1,  Real *res)
  {
     Real y1[2], y2[2], dy[2], dy1[2], dy2[2], res1, res2, res3, res4;

     interp(0, y1, dy1);
     obj->interp(0, y2, dy2);
     sub2( y2,y1, dy);
     res1=norminf2( dy );

     interp(0, y1, dy1);
     obj->interp(1, y2, dy2);
     sub2( y2, y1, dy);
     res2=norminf2( dy );

     interp(1, y1, dy1);
     obj->interp(0, y2, dy2);
     sub2( y2,y1, dy);
     res3=norminf2( dy );

     interp(1, y1, dy1);
     obj->interp(1, y2, dy2);
     sub2( y2, y1, dy);
     res4=norminf2( dy );

    if((abs(res1)<=abs(res2))&& (abs(res1)<=abs(res3)) && (abs(res1)<=abs(res4)))
    {
       *s0= 0;
       *s1= 0;
    }
     else if((abs(res2)<=abs(res1))&& (abs(res2)<=abs(res3)) && (abs(res2)<=abs(res4)))
    {
       *s0=0;
       *s1=1;
    }
     else if((abs(res3)<=abs(res1))&& (abs(res3)<=abs(res2)) && (abs(res3)<=abs(res4)))
    {
       *s0=1;
       *s1=0;
    }
     else if((abs(res4)<=abs(res1))&& (abs(res4)<=abs(res2)) && (abs(res4)<=abs(res3)))
    {
       *s0=1;
       *s1=1;
    }

  }

   void cInterp::pickle( size_t *len, pickle_t *buf )
  {
      cTag::pickle( len,buf );
  }

   void cInterp::unpickle( size_t *len, pickle_t  buf )
  {
      cTag::unpickle( len,buf );
  }

   void cInterp::get( cTabData *var )
  {
/*    cTabItem *tmp;
      Real      l= length();
      tmp= new cTabItem( l ); tmp->hlp= "Interpolatable object length"; var->append( "length",tmp );
      var->set( "length",l );*/
  }
   void cInterp::set( cTabData *var )
  {
  }
