
   using namespace std;

#  include <geo/2d/crossings.h>

   void  init( crossings_t *var )
  {
      var->n= 0;
      var->m= 0;
      setv( 0,MXNC, 0, var->idat[0] );
      setv( 0,MXNC, 0, var->idat[1] );
      setv( 0,<PERSON>X<PERSON>, 0, var->idat[2] );
      setv( 0,MXNC, 0, var->idat[3] );
      setv( 0,MXNC,-1, var->idat[4] );
      setv( 0,MXNC, 0, var->idat[5] );
      setv( 0,MXNC,-1, var->idat[6] );
      setv( 0,MXNC,-1, var->idat[7] );
      setv( 0,MXNC,-1, var->idat[8] );
      setv( 0,MXNC, ZERO,var->s[0] );
      setv( 0,MXNC, ZERO,var->s[1] );
      setv( 0,MXNC, ZERO,var->s[2] );
      setv( 0,<PERSON>X<PERSON>, ZERO,var->x[0] );
      setv( 0,<PERSON>X<PERSON>, ZER<PERSON>,var->x[1] );
      setv( 0,MXNC, ZERO,var->r    );
  }

   void  sort( crossings_t *var )
  {
      Int                   i,j,k,l,m;
      Int                   iprm[MXNC]; 

//    if( var->n == 0 ){ return; };

// kill unused crossings
      for( i=(var->n)-1;i>=0;i-- )
     {
         if( (var->idat[3][i]) == -1 )
        {
            (var->n)--;
             swap( var->idat[0]+i,var->idat[0]+var->n );
             swap( var->idat[1]+i,var->idat[1]+var->n );
             swap( var->idat[2]+i,var->idat[2]+var->n );
             swap( var->idat[3]+i,var->idat[3]+var->n );
             swap( var->idat[4]+i,var->idat[4]+var->n );
             swap( var->idat[5]+i,var->idat[5]+var->n );
             swap( var->idat[6]+i,var->idat[6]+var->n );
             swap( var->idat[7]+i,var->idat[7]+var->n );
             swap( var->idat[8]+i,var->idat[8]+var->n );
             swap( var->s[0]+i,var->s[0]+      var->n );
             swap( var->s[1]+i,var->s[1]+      var->n );
             swap( var->s[2]+i,var->s[2]+      var->n );
             swap( var->x[0]+i,var->x[0]+      var->n );
             swap( var->x[1]+i,var->x[1]+      var->n );
             swap( var->r   +i,var->r   +      var->n );
        }
     }
      if( var->n == 0 ){ return; };
      if( var->n > 5 )
     {
         hsort( var->n,var->idat[0], iprm );
     }
      else
     {
         bsort( var->n,var->idat[0], iprm );
     }
      permute( var->n,var->idat[0], iprm );
      permute( var->n,var->idat[1], iprm );
      permute( var->n,var->idat[2], iprm );
      permute( var->n,var->idat[3], iprm );
      permute( var->n,var->idat[4], iprm );
      permute( var->n,var->idat[5], iprm );
      permute( var->n,var->idat[6], iprm );
      permute( var->n,var->idat[7], iprm );
      permute( var->n,var->idat[8], iprm );
      permute( var->n,var->x[0], iprm );
      permute( var->n,var->x[1], iprm );
      permute( var->n,var->s[0], iprm );
      permute( var->n,var->s[1], iprm );
      permute( var->n,var->s[2], iprm );
      permute( var->n,var->r,    iprm );
      
      j= 0;
      k= 0;
      m= var->idat[0][j];
      do 
     {
         j++;
         if( ( var->idat[0][j] != m ) || ( j == var->n ) )
        {
            l=  j-k;
            
            bsort( l,var->s[0]+k, iprm );
            permute( l,var->idat[0]+k, iprm );
            permute( l,var->idat[1]+k, iprm );
            permute( l,var->idat[2]+k, iprm );
            permute( l,var->idat[3]+k, iprm );
            permute( l,var->idat[4]+k, iprm );
            permute( l,var->idat[5]+k, iprm );
            permute( l,var->idat[6]+k, iprm );
            permute( l,var->idat[7]+k, iprm );
            permute( l,var->idat[8]+k, iprm );
            permute( l,   var->x[0]+k, iprm );
            permute( l,   var->x[1]+k, iprm );
            permute( l,   var->s[0]+k, iprm );
            permute( l,   var->s[1]+k, iprm );
            permute( l,   var->s[2]+k, iprm );
            permute( l,   var->r+k,    iprm );
           
            k=  j;
            m=  var->idat[0][j];
        }
         if( j == var->n ){ break; };
     }while( true );
  }

   void print( crossings_t *var )
  {
       Int  i;
       for( i=0;i<var->n;i++ )
      {
          cout << "x "<<var->x[0][i]<<" "<< var->x[1][i]<<" s "<< var->s[0][i]<< " "<< var->s[1][i]
                      <<" "<< var->s[2][i]<<" r "<<var->r[i]<<" idat "<<
                  var->idat[0][i]<<" "<<var->idat[1][i]<<" "<<var->idat[2][i]<<" "<<var->idat[3][i]<<" "<<
                  var->idat[4][i]<<" "<<var->idat[5][i]<<" "<<var->idat[6][i]<<" "<<var->idat[7][i]<<" "<<
                  var->idat[8][i]<<"\n";
      }
  }

   void prune( Int ist, Int ien, crossings_t *var, bool dir )
  {
      Int                   i;
      Int                   ifw,ibw;

      ifw= 0;
      ibw= 1;
      if( !dir ){ swap( &ifw,&ibw ); };

      i= ist;
      if( var->idat[2][i] == var->idat[2][ien-1] )
     {
         if( var->idat[2][i] == ifw ){ var->idat[3][i]= -1; };
     } 
      for( i=ist+1;i<ien;i++ ) 
     {
         if( var->idat[2][i] == var->idat[2][i-1] )
        {
            if( var->idat[2][i] == ifw ){ var->idat[3][i]= -1; };
        } 
     }     

      i= ien-1;
      if( var->idat[2][i] == var->idat[2][ist] )
     {
         if( var->idat[2][i] == ibw ){ var->idat[3][i]= -1; };
     } 
      for( i=ien-2;i>=ist;i-- ) 
     {
         if( var->idat[2][i] == var->idat[2][i+1] )
        {
            if( var->idat[2][i] == ibw ){ var->idat[3][i]= -1; };
        } 
     }     


  }

   void prune( crossings_t *var, bool dir )
  {
      Int                   j,k,m;

      if( var->n == 0 ){ return; };
      j= 0;
      k= 0;
      m= var->idat[0][j];
      do 
     {
         j++;
         if( ( var->idat[0][j] != m ) || ( j == var->n ) )
        {
            prune( k,j, var, dir );
            k=  j;
            m=  var->idat[0][j];
        }
         if( j == var->n ){ break; };
     }while( true );
      sort( var );
  }

   bool insert( inter_t *stat, crossings_t *var )
  { 

      Real             *xs[2];
      Real               x[2];
      Int                i;

      xs[0]= var->x[0];
      xs[1]= var->x[1];

      bool val=true;
      for( i=var->n-1;i>=0;i-- )
     {
         line2( i,xs, x );
//       if( distinf2(x,stat->y1) < ITPPTOL )
         if( distinf2(x,stat->y1) < ITPPTOL &&
             var->idat[6][i] == stat->idat[0][0] &&
             var->idat[7][i] == stat->idat[1][0] )
        {
            val= false;
            assert( stat->idat[0][4] == var->idat[2][i] );
            if( stat->res < var->r[i] )
           {
               var->r[i]= stat->res;
           }
            break;
        }
     }
      if( val )
     {
         i= var->n;
         var->idat[0][i]= -1;
         var->idat[1][i]= -1;
         var->idat[2][i]= stat->idat[0][4];
         var->idat[3][i]= 0;
         var->idat[5][i]= 0;
         var->idat[6][i]= stat->idat[0][0];
         var->idat[7][i]= stat->idat[1][0];
         var->s[0][i]= stat->rdat[0][1];
         var->s[1][i]= stat->rdat[1][1];
         var->x[0][i]= stat->y1[0];
         var->x[1][i]= stat->y1[1];
         var->r[i]   = stat->res;
        (var->n)++;
     }
      return val;
  }

   Int nextc( Real w, Int ic, Int ist, crossings_t *var )
  {
      Int     i; 
      Real    smin=big;
      Real    s=big;
      Int     val=-1;
      for( i=ist;i<var->n;i++ )
     {
         if( var->idat[ic][i] == var->idat[ic][ist-1] )
        {
            s= var->s[ic][i];
            if( s > w )
           {
               if( s < smin )
              {
                  val=i;
                  smin=s;
              }
           }
        }
     }
      return val;
  }

   void start( Int i, crossings_t *var )
  {
      Int n= var->n;
      var->idat[0][n]= var->idat[0][i];
      var->idat[1][n]= var->idat[1][i];
      var->idat[2][n]= var->idat[2][i];
      var->idat[3][n]= var->idat[3][i];
      var->idat[4][n]= i;
      var->idat[5][n]= var->idat[5][i];
      var->idat[6][n]= var->idat[6][i];
      var->idat[7][n]= var->idat[7][i];
      var->idat[8][n]= var->idat[8][i];

      var->x[0][n]= var->x[0][i];
      var->x[1][n]= var->x[1][i];
      var->s[0][n]= var->s[0][i];
      var->s[1][n]= var->s[1][i];
      var->s[2][n]= var->s[2][i];
      var->r[n]=    var->r[i];

     (var->n)++;
  }

   void swap( Int i0, Int i1, crossings_t *var )
  {
      swap( var->idat[0]+i0, var->idat[0]+i1 );
      swap( var->idat[1]+i0, var->idat[1]+i1 );
      swap( var->idat[2]+i0, var->idat[2]+i1 );
      swap( var->idat[3]+i0, var->idat[3]+i1 );
      swap( var->idat[4]+i0, var->idat[4]+i1 );
      swap( var->idat[5]+i0, var->idat[5]+i1 );
      swap( var->idat[6]+i0, var->idat[6]+i1 );
      swap( var->idat[7]+i0, var->idat[7]+i1 );
      swap( var->idat[8]+i0, var->idat[8]+i1 );

      swap( var->x[0]+i0, var->x[0]+i1 );
      swap( var->x[1]+i0, var->x[1]+i1 );
      swap( var->s[0]+i0, var->s[0]+i1 );
      swap( var->s[1]+i0, var->s[1]+i1 );
      swap( var->s[2]+i0, var->s[2]+i1 );
      swap( var->r+i0,    var->r+i1    );

  }

   void collect( crossings_t *var )
  {
      Int        i,j,l; 
      Int        ist=0,ien=0;
      Int        sig=0;
      Real       x0[2],x1[2];
      Real      *xs[2];

      xs[0]= var->x[0];
      xs[1]= var->x[1];

      i= 0;
// look for crossings matching the first crossing backwards ...
      line2( i,xs,x0 );
      ist= var->n;
      ien= var->n;
      sig=0;
      if( var->idat[2][i]== 0 ){ sig++; }else{ sig--; }
      for( j=var->n-1;j>0;j-- )
     {
         line2( j,xs,x1 );
         if( distinf2( x0,x1 ) > 2*ITPPTOL ){ break; }
         if( var->idat[2][j]== 0 ){ sig++; }else{ sig--; }
         ist= j;
     }
      for( j=ist;j<ien;j++ ){ var->idat[3][j]= -1; cout << "killed "<<j<<"\n";};
      l= ist;
// now carrry on forwards
      do
     {
         ist= i+1;
         ien= ist;
         for( j=ist;j<l;j++ )
        {
            ien= j;
            line2( j,xs,x1 );
            if( distinf2( x0,x1 ) > 2*ITPPTOL ){ break; }
            if( var->idat[2][j]== 0 ){ sig++; }else{ sig--; }
        }
         for( j=ist;j<ien;j++ ){ var->idat[3][j]= -1; };
         if( var->idat[3][i] == 0 )
        {
            if( sig == 0 )
           {
               var->idat[3][i]= -1;
           }
            else
           {
               if( sig > 0 )
              {
                  var->idat[2][i]= 0;
              }
               else
              {
                  var->idat[2][i]= 1;
              }
           }
        }
         i= ien;
         sig=0;
         if( var->idat[2][i]== 0 ){ sig++; }else{ sig--; }
         line2( i,xs,x0 );
     }while( i <l );
     
  }
