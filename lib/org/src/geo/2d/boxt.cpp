   using namespace std;

#  include <geo/2d/boxt.h>

   void add( Int ist, Int ien, Real *x[], box_t *b )
  {
      Int i;
      for( i=ist;i<ien;i++ )
     {
         b->x0[0]= min( b->x0[0],x[0][i] );
         b->x0[1]= min( b->x0[1],x[1][i] );
         b->x1[0]= max( b->x1[0],x[0][i] );
         b->x1[1]= max( b->x1[1],x[1][i] );
     }
  }

   void add( Int ist, Int ien, cAu3xView<Real>& x, box_t *b )
  {
      Int i;
      for( i=ist;i<ien;i++ )
     {
         b->x0[0]= min( b->x0[0],x(0,i) );
         b->x0[1]= min( b->x0[1],x(1,i) );
         b->x1[0]= max( b->x1[0],x(0,i) );
         b->x1[1]= max( b->x1[1],x(1,i) );
     }
  }

   void add(  box_t *b1, box_t *b2 )
  {
      b1->x0[0]= min( b1->x0[0],b2->x0[0] );
      b1->x0[1]= min( b1->x0[1],b2->x0[1] );
      b1->x1[0]= max( b1->x1[0],b2->x1[0] );
      b1->x1[1]= max( b1->x1[1],b2->x1[1] );
  }

   void diag(  box_t b, Real *d )
  {
      d[0]= b.x1[0]- b.x0[0];
      d[1]= b.x1[1]- b.x0[1];
  }

   void reset( box_t *b )
  {
      b->x0[0]= big;
      b->x0[1]= big;
      b->x1[0]=-big;
      b->x1[1]=-big;
  }

   void grow( Real d, box_t *b )
  {
      b->x0[0]-= d;
      b->x0[1]-= d;
      b->x1[0]+= d;
      b->x1[1]+= d;
  }

   bool overlap( box_t *b1, box_t *b2 )
  {
      bool val;
      Real d1[2],d2[2],d0[2];
      Real x0[2],x1[2];
      Real y0[2],y1[2];

      d1[0]= b1->x1[0]-b1->x0[0];
      d1[1]= b1->x1[1]-b1->x0[1];

      d2[0]= b2->x1[0]-b2->x0[0];
      d2[1]= b2->x1[1]-b2->x0[1];

      x0[0]= b1->x0[0];
      x0[1]= b1->x0[1];
      x1[0]= b1->x1[0];
      x1[1]= b1->x1[1];

      y0[0]= b2->x0[0];
      y0[1]= b2->x0[1];
      y1[0]= b2->x1[0];
      y1[1]= b2->x1[1];

      inters( x0+0,x1+0, y0[0],y1[0] );
      inters( x0+1,x1+1, y0[1],y1[1] );

      d0[0]=x1[0]-x0[0];
      d0[1]=x1[1]-x0[1];

      //val=        ( ( d0[0] > 0.95*d1[0] ) && ( d0[0] > 0.95*d2[0] ) );
      //val= val && ( ( d0[1] > 0.95*d1[1] ) && ( d0[1] > 0.95*d2[1] ) );
      val=        ( ( d0[0] > 0.85*d1[0] ) && ( d0[0] > 0.85*d2[0] ) );
      val= val && ( ( d0[1] > 0.85*d1[1] ) && ( d0[1] > 0.85*d2[1] ) );

      return val;
  }
