   using namespace std;

#  include <iostream>
#  include <cstdlib>
#  include <geo/2d/linear.h>

   void llint( Real *p0, Real *p1, Real *q0, Real *q1, Real *s, Real *t, Real *d )
  {
      Real dp[2];
      Real dq[2];
      Real dr[2];
      Real det;
     *s= -1;
     *t= -1;
      sub2( p1,p0, dp );
      sub2( q1,q0, dq );
      sub2( p0,q0, dr );
      det=  vec2( dq,dp );
      if( fabs( det ) > small )
     {
        *s= dr[0]*dq[1]- dr[1]*dq[0]; 
        *t= dr[0]*dp[1]- dr[1]*dp[0]; 
       (*s)/= det;
       (*t)/= det;
         dr[0]+= (*s)*dp[0]- (*t)*dq[0];
         dr[1]+= (*s)*dp[1]- (*t)*dq[1];
        *d= norminf2( dr );
     }
      else
     {
         det= norm22( dp );
         sclv2( 1./det,dp );
         rot90( dp );
        *d= dot2( dp,dr );
     }
  }

   void ldint( Real *p0, Real *t0, Real *p1, Real *t1, Real *s, Real *t, inter_t *stat )
  {
      Real x[2];
      Real v[2][2];
      Real a[2][3];
      Real b[2];
      Int  ipiv[2];

      idv2( t0,v[0] ); 
      idv2( t1,v[1] ); 
      luf2( v[0],v[1], a[0],a[1], ipiv, &(stat->det) );
      if( fabs( stat->det ) > small )
     {
         sub2( p1,p0, b ); 
         lus2( a[0],a[1], ipiv, x, b );
        *s= x[0];
        *t=-x[1];
     }
  }

   void lsinters( Real *x0, Real *l0, Real a0, Real a1, Real a2, Real *t0, Real *t1 )
  {
      Real b,d;
      b= a0*x0[0]+ a1*x0[1]+ a2;
      d= a0*l0[0]+ a1*l0[1];
      if( fabs(d) > small )
     {
         b/= -d;
         sinters( t0,t1, b,d );
     }
      else
     {
         if( b < 0 )
        {
            *t1= *t0;
        }
     }
  }

   void ltinters( Real *x0, Real *l0, Real *t0, Real *t1 )
  {
      Real b,d;
// semiplane x > 0
      b=-x0[0];
      d= l0[0];
      if( fabs(d) > small )
     {
         b/= d;
         sinters( t0,t1, b,d );
     }
      else
     {
         if( b > 0 )
        {
            *t1= *t0;
        }
     }
// semiplane y > 0
      b=-x0[1];
      d= l0[1];
      if( fabs(d) > small )
     {
         b/= d;
         sinters( t0,t1, b,d );
     }
      else
     {
         if( b > 0 )
        {
            *t1= *t0;
        }
     }
// semiplane x+y < 1
      b= 1- x0[0]- x0[1];
      d= l0[0]+ l0[1];
      if( fabs(d) > small )
     {
         b/= d;
         sinters( t0,t1, b,-d );
     }
      else
     {
         if( b < 0 )
        {
            *t1= *t0;
        }
     }
  }


   void lqinters( Real *x0, Real *l0, Real *t0, Real *t1, Real *c0, Real *c1 )
  {

      Real b0,b1,d;

      d= l0[0];
      if( fabs( d ) > small )
     {
         b0= c0[0]-x0[0];
         b1= c1[0]-x0[0];
         b0/= d;
         b1/= d;
         if( d > 0 )
        {
            inters( t0,t1, b0,b1 );
        }
         else
        {
            inters( t0,t1, b1,b0 );
        }
     }
      else
     {
         if( x0[0] < c0[0] || x0[0] > c1[0] )
        {
           *t0= *t1;
        }
     }

      d= l0[1];
      if( fabs( d ) > small )
     {
         b0= c0[1]-x0[1];
         b1= c1[1]-x0[1];
         b0/= d;
         b1/= d;
         if( d > 0 )
        {
            inters( t0,t1, b0,b1 );
        }
         else
        {
            inters( t0,t1, b1,b0 );

        }
     }
      else
     {
         if( x0[1] < c0[1] || x0[1] > c1[1] )
        {
           *t0= *t1;
        }
     }
  }

   void square2( Real *x0, Real *x1 )
  {
      Real d[2];
      d[0]= x1[0]-x0[0];
      d[1]= x1[1]-x0[1];
      if( d[0] > d[1] )
     {
         d[1]= x0[1]+x1[1];
         x0[1]= d[1]-d[0];
         x1[1]= d[1]+d[0];
         x0[1]/= 2;
         x1[1]/= 2;
     }
      else
     {
         d[0]= x0[0]+x1[0];
         x0[0]= d[0]-d[1];
         x1[0]= d[0]+d[1];
         x0[0]/= 2;
         x1[0]/= 2;
     }
  }

   void box2( Real *x0, Real *x1 )
  {
      if( x1[0] < x0[0] )
     { 
         swap( x1+0,x0+0 ); 
     };
      if( x1[1] < x0[1] )
     { 
         swap( x1+1,x0+1 ); 
     };
  }

   void box2( Real *x0, Real *x1, Real *x2, Real *xmin, Real *xmax )
  {
      xmin[0]= x0[0];
      xmin[0]= min( xmin[0],x1[0] );
      xmin[0]= min( xmin[0],x2[0] );

      xmax[0]= x0[0];
      xmax[0]= max( xmax[0],x1[0] );
      xmax[0]= max( xmax[0],x2[0] );

      xmin[1]= x0[1];
      xmin[1]= min( xmin[1],x1[1] );
      xmin[1]= min( xmin[1],x2[1] );

      xmax[1]= x0[1];
      xmax[1]= max( xmax[1],x1[1] );
      xmax[1]= max( xmax[1],x2[1] );
  }

   bool inside2( Real *x0, Real *x1, Real *x )
  {
      bool val;
      val= inside( x0[0],x1[0], x[0] );
      val= val && inside( x0[1],x1[1], x[1] );

      return val;
  }

   bool inters2( Real *x0, Real *x1, Real *y0, Real *y1 )
  {
      bool val;
  
      val= inters( x0[0],x1[0], y0[0],y1[0] );
      val= val && inters( x0[1],x1[1], y0[1],y1[1] );

      return val;
  }


