   using namespace std;

#  include <geo/2d/polyline.h>

   cPolyline::cPolyline()
  {
      ns=0;
      ms=0;
      sds= NULL;
      lims[0]= NULL;
      lims[1]= NULL;
      lims[2]= NULL;
  }

   cPolyline::~cPolyline()
  {
      Int is;
      for( is=0;is<ns;is++ )
     {
         delete sds[is]; sds[is]= NULL;
     }
      delete[] sds;     sds=     NULL;
      delete[] lims[0]; lims[0]= NULL;
      delete[] lims[1]; lims[1]= NULL;
      delete[] lims[2]; lims[2]= NULL;
  }

   void cPolyline::add( Real l0, Real l1, cInterp *var )
  {
      Int     i;
      Int     m;
      Int     n;
      Int    dm;
      Real    l;
      n= ns;
      m= ms;
      var->unpack( l0,l1, &n,&m, lims,&sds );
      dm= m-ms;
      m= ms; 
      realloc( &m,dm,lims+2 );
      l= 0;
      if( ns > 0 ){ l= lims[2][ns-1]; };
      for( i=ns;i<n;i++ )
     {
         lims[2][i]= fabs( lims[0][i]-lims[1][i] )*sds[i]->length()+ l;
         l= lims[2][i];
     }
      ms= m;
      ns= n;
 
  }

   void cPolyline::displace(cPolyline **pl, Real ls[], Real d[], Real fct)
  {
     cInterp *tmp=NULL;
     cPolyline *var=new cPolyline();; 
    
     for(Int i=0; i<ns; i++)
    {
       sds[i]->displace(&tmp, ls, d, fct);
    }

     for(Int is=0; is<ns; is++)
    {
       var->add(lims[0][is], lims[1][is], tmp);
    }

     *pl= var;
     delete tmp; tmp=NULL;
  }

   Real cPolyline::length()
  {
      Real val=0;
      if( ns > 0 )
     {
         val= lims[2][ns-1];
     }
      return val;
  }

   Int cPolyline::which( Real s )
  {
      Int  val=-1;
      s*= lims[2][ns-1];
      val= bsearch( s, ns,lims[2] );
      return val;
  }

   void cPolyline::local( Real *s, Int *i  )
  {
      Real l0,l1,dl,w;
      Int  is;
      w= *s;
      wrap( &w );
      is= which( w );
      l0= 0.;
      if( is > 0 ){ l0= lims[2][is-1]; }
      l1= lims[2][is];
      w*= length();
      w-= l0;
      dl= l1-l0;
      w/= dl;
      w= w*lims[1][is]+ ( 1-w )*lims[0][is];
     *i= is;
     *s= w;
  }

   void cPolyline::global( Real *s, Int i )
  {
      Real l0,l1,dl,w;
      l0=0;
      if( i > 0 ){ l0= lims[2][i-1]; };
      l1= lims[2][i]; 
      dl= lims[1][i]- lims[0][i];
      w= *s- lims[0][i];
      w/= dl;
     *s= l1*w+ (1-w)*l0;
     *s/= length();
      wrap( s );
  }

  void cPolyline::plot( Real l0, Real l1, ofstream *fle)
 {
    Int    is;
    for(is=0; is<ns; is++)
   {
      sds[is]->plot(lims[0][is], lims[1][is], fle);
      *fle <<  "\n";
   }
 }

   void cPolyline::interp( Real w, Real *y, Real *dy )
  {
      Int  is;
      Real s,d;
      s= w;
      local( &s,&is );
      sds[is]->interp( s,y,dy );
      d= length()/sds[is]->length();
      if( lims[1][is]<lims[0][is] ){ d=-d; };
      dy[0]*= d;
      dy[1]*= d;
  }

   void cPolyline::approx( Real s0, Real s1, Real maxd, Real maxe, Int *ne, Real *xe[] )
  {
      Int ia,is,is0,is1,iof;
      Int ist;
      bool flag;

      flag= false;
      if( s1 < s0 ){ swap( &s0,&s1 ); flag= true; };

      local( &s0,&is0 );
      local( &s1,&is1 );

      ist= *ne;
      iof= *ne;
      for( is=is0;is<is1;is++ )
     {
         sds[is]->approx( s0,lims[1][is], maxd,maxe, ne,xe ); 
         for( ia=iof;ia<*ne;ia++ )
        {
            global( xe[0]+ia,is );
        }
         s0= lims[0][is+1];
         iof= *ne;
     }
      sds[is1]->approx( s0,s1, maxd,maxe, ne,xe ); 
      for( ia=iof;ia<*ne;ia++ )
     {
         global( xe[0]+ia,is1 );
     }

      if( flag )
     {
         reverse( *ne-ist,xe[0]+ist );
         reverse( *ne-ist,xe[1]+ist );
         reverse( *ne-ist,xe[2]+ist );
     }
  }

   cCPolyline::cCPolyline()
  {
  }

   cCPolyline::~cCPolyline()
  {
  }
  
   void cCPolyline::wrap( Real *s )
  {
      while( *s > 1 ){ (*s)-= 1.; };
      while( *s < 0 ){ (*s)+= 1.; };
  }

   bool boxcross( Int ia0, Real *xa0[], Int ia1, Real *xa1[] )
  {
      bool val0,val1,val;
      Real d0,d1;
      Real xmin0[2]={big,big},xmax0[2]={-big,-big};
      Real xmin1[2]={big,big},xmax1[2]={-big,-big};

      xmin0[0]= fmin( xa0[1][ia0-1], xa0[1][ia0] );
      xmin0[1]= fmin( xa0[2][ia0-1], xa0[2][ia0] );
      xmax0[0]= fmax( xa0[1][ia0-1], xa0[1][ia0] );
      xmax0[1]= fmax( xa0[2][ia0-1], xa0[2][ia0] );

      xmin1[0]= fmin( xa1[1][ia1-1], xa1[1][ia1] );
      xmin1[1]= fmin( xa1[2][ia1-1], xa1[2][ia1] );
      xmax1[0]= fmax( xa1[1][ia1-1], xa1[1][ia1] );
      xmax1[1]= fmax( xa1[2][ia1-1], xa1[2][ia1] );

      d0= sgdst( xmin0[0],xmax0[0], xmin1[0] );
      d1= sgdst( xmin0[0],xmax0[0], xmax1[0] );
      val0= ( d0 <= 0 ) && ( d1 >= 0 );
  

      d0= sgdst( xmin0[1],xmax0[1], xmin1[1] );
      d1= sgdst( xmin0[1],xmax0[1], xmax1[1] );
      val1= ( d0 <= 0 ) && ( d1 >= 0 );

      val= val0 && val1;

      return val;
      
  }


   void cPolyline::crossings( cPolyline *pl, crossings_t *var )
  {

      Int           ia0,ia1;
      Int           is0,is1;
      Real          s0,s1,l00,l01,l11,l10;
      cInterp      *tmp0,*tmp1;
      inter_t       stat;

      bool          bval0,bval1,bval2;

      Int           ib;

      Real          af0,af1;

      Real          xmin[2],xmax[2];
      Int           nbx;
      Int          *ibx;

      ibx= new Int[pl->na];
      for( ia0=1;ia0<na;ia0++ )
     {
         xmax[0]= fmax( xa[1][ia0-1], xa[1][ia0] );
         xmax[1]= fmax( xa[2][ia0-1], xa[2][ia0] );
         xmin[0]= fmin( xa[1][ia0-1], xa[1][ia0] );
         xmin[1]= fmin( xa[2][ia0-1], xa[2][ia0] );
         nbx=0;
         pl->bt->inters( xmin,xmax,&nbx,ibx );
//       cout << nbx << " boxes found intersecting interval "<<xmin[0]<<" "<<xmin[1]<<" "<<xmax[0]<<" "<<xmax[1]<<"\n";
         for( ib=0;ib<nbx;ib++ )
        {
            ia1= ibx[ib]+1;
            s0= xa[0][ia0]+ xa[0][ia0-1]; s0/= 2;
            local( &s0,&is0 );   

            s1= pl->xa[0][ia1]+ pl->xa[0][ia1-1]; s1/= 2;
            pl->local( &s1,&is1 );   

            tmp0= sds[is0];
            tmp1= pl->sds[is1];

            tmp0->inters( tmp1, &s0,&s1, &stat );
            bval2= valid( stat );
            if( bval2 )
           {
               l00= fmin( lims[1][is0],lims[0][is0] );
               l10= fmax( lims[1][is0],lims[0][is0] );
               af0= 1;
               if( lims[1][is0]-lims[0][is0] < 0 ){ af0= -1; };
               

               l01= fmin( pl->lims[1][is1],pl->lims[0][is1] );
               l11= fmax( pl->lims[1][is1],pl->lims[0][is1] );
               af1= 1;
               if( pl->lims[1][is1]-pl->lims[0][is1] < 0 ){ af1= -1; };

               Real p0[2],dp0[2],p1[2],dp1[2];
               Real d0,d1;

               tmp0->interp( s0,p0,dp0 );
               tmp1->interp( s1,p1,dp1 );
               d0= norminf2( dp0 );
               d1= norminf2( dp1 );
               d0*= 1.5;
               d1*= 1.5;
               d0= ITPPTOL/d0;
               d1= ITPPTOL/d1;

               bval0= ( s0 >= l00-d0 ) && ( s0 <= l10+d0 );
               bval1= ( s1 >= l01-d1 ) && ( s1 <= l11+d1 );

               if( bval0 && bval1 )
              {

                  stat.rdat[0][0]= s0;
                  stat.rdat[0][1]= s0;    global( stat.rdat[0]+1,is0);
                  stat.rdat[1][0]= s1;
                  stat.rdat[1][1]= s1; pl->global(stat.rdat[1]+1,is1);
                  stat.idat[0][0]= is0;
                  stat.idat[1][0]= is1;
                  stat.idat[0][4]= 0;
                  if( stat.det*af0*af1 > 0 ){ stat.idat[0][4] = 1; };
                  stat.idat[1][4]=-1;
                  insert( &stat,var );

              }
           }
        }
     }
      delete[] ibx;
/*    cout << "==========\n";
      print( var );
      cout << "==========\n";
*///  assert( false );

  }

   void cPolyline::copy( cInterp **var )
  {
      Int        i;
      cPolyline *tmp;
      tmp= new cPolyline();
      tmp->lims[0]= new Real[ns];
      tmp->lims[1]= new Real[ns];
      tmp->lims[2]= new Real[ns];
      tmp->sds=     new cInterp*[ns];
      setv( 0,ns, ZERO,tmp->lims[0] );
      setv( 0,ns, ZERO,tmp->lims[1] );
      setv( 0,ns, ZERO,tmp->lims[2] );
      setv( 0,ns, (cInterp*)NULL,tmp->sds );
      tmp->ms= ns;
      tmp->ns= ns;
      for( i=0;i<ns;i++ )
     {
         tmp->lims[0][i]= lims[0][i];
         tmp->lims[1][i]= lims[1][i];
         tmp->lims[2][i]= lims[2][i];
         sds[i]->copy( tmp->sds+i );
     }
     *var= tmp;
      
  }

   void cCPolyline::copy( cInterp **var )
  {
      Int        i;
      cCPolyline *tmp;
      tmp= new cCPolyline();
      tmp->lims[0]= new Real[ns];
      tmp->lims[1]= new Real[ns];
      tmp->lims[2]= new Real[ns];
      tmp->sds=     new cInterp*[ns];
      tmp->ms= ns;
      tmp->ns= ns;
      setv( 0,ns, ZERO,tmp->lims[0] );
      setv( 0,ns, ZERO,tmp->lims[1] );
      setv( 0,ns, ZERO,tmp->lims[2] );
      setv( 0,ns, (cInterp*)NULL,tmp->sds );
      for( i=0;i<ns;i++ )
     {
         tmp->lims[0][i]= lims[0][i];
         tmp->lims[1][i]= lims[1][i];
         tmp->lims[2][i]= lims[2][i];
         sds[i]->copy( tmp->sds+i );
     }
     *var= tmp;
  }

   bool cCPolyline::closed()
  {
      bool val;
      Real y0[2],y1[2],dy[2];
      sds[0]->interp( lims[0][0],y0,dy );
      sds[ns-1]->interp( lims[1][ns-1],y1,dy );
      sub2( y1,y0,dy );
      val= ( norminf2( dy ) < ITPPTOL );
      return val; 
  }

   bool cCPolyline::inside( Real *y )
  {
      bool  val=false;
      Real  a,v;

      if( na <= 0 )
     {
         cout << "cannot evaluate inside predicate on a polyline without a valid approximation\n";
         exit(0);
     }


      a= area();
      v= vangl2( na,xa+1,y );

      if( a > 0 )
     {
         val= ( v >  pi ); 
     }
      else
     {
         val= ( v > -pi );
     }

      return val;
  }

   Real cCPolyline::area( )
  {
      Real  val=0;
      Real  y[2]; 

      if( na <= 0 )
     { 
         cout << "cannot compute the area of a polyline without a valid approximation\n";
         exit(0);
     }
      y[0]= xa[0][0];
      y[1]= xa[1][0];
      val= varea( na,xa+1,y );
 

      return val;
  }

   void cPolyline::any( Real *y )
  {
      Real dy[2];
      interp( 0,y,dy );
  }

   void cPolyline::invert()
  {
      Int is;
      Real l0,l1,l;
      reverse( ns,sds );
      reverse( ns,lims[0] );
      reverse( ns,lims[1] );
      l=0;
      for( is=0;is<ns;is++ )
     {
         l0= lims[0][is];
         l1= lims[1][is];
         swap( lims[0]+is,lims[1]+is );
         lims[2][is]= fabs( l1-l0 )*sds[is]->length()+ l;
         l= lims[2][is]; 
     }
      na=0;
      mxa=-1;
      mxd=-1;
  }

   void cPolyline::rotate( Real *xr, Real dt )
  {
      Int is;    
      for( is=0;is<ns;is++ )
     {
         sds[is]->rotate( xr,dt );
     }
      na=0;
  }

   void cPolyline::translate( Real *dx )
  {
      Int is;    
      for( is=0;is<ns;is++ )
     {
         sds[is]->translate( dx );
     }
      na=0;
  }

   void cPolyline::mirror( Real *x0, Real *l0 )
  {
      Int is;    
      for( is=0;is<ns;is++ )
     {
         sds[is]->mirror( x0,l0 );
     }
      na=0;
  }

   void cPolyline::scale( Real *x0, Real f )
  {
      Int is;    
      for( is=0;is<ns;is++ )
     {
         sds[is]->scale( x0,f );
         lims[2][is]*= f;
     }
      na=0;
  }

   void cPolyline::shear( Real *x, Real *ds[] )
  {
      Int is;
      for( is=0;is<ns;is++ )
     {
         sds[is]->shear( x,ds );
     }
      na=0;
  }

   void cPolyline::sides( Int *n, cPolyline ***var )
  {
      Int is;
      Int iof;
      cPolyline *tmp;
      iof= *n;
      cout << "cPolyline::sides( Int *, cPolyline *** ) forbidden\n";
      assert( false );
      exit(0);
      realloc( &iof,ns,var );
      iof= *n;
      for( is=0;is<ns;is++ )
     {
         tmp= new cPolyline(); 
         tmp->add( lims[0][is],lims[1][is],sds[is] );
       (*var)[iof++]= tmp;
     }
    (*n)= iof;
  }

   void cPolyline::sides( Int *n, cPolyline *var[] )
  {
      Int is;
      size_t iof;
      cPolyline *tmp;
      iof= *n;
      for( is=0;is<ns;is++ )
     {
         tmp= new cPolyline(); 
         tmp->add( lims[0][is],lims[1][is],sds[is] );
         var[iof++]= tmp;
     }
    (*n)= iof;
  }

   void cPolyline::debug( string tab )
  {
      Int i;
      cout << tab <<"object "<<this<<" number of segments "<<ns<<"\n";
      for( i=0;i<ns;i++ )
     {
         cout << tab <<" side "<<i<<" lims "<<lims[0][i]<<" "<<lims[1][i]<<" "<<lims[2][i]<<"\n";
         sds[i]->debug( tab+ "   " );
     }
  }

   void cPolyline::unpack( Real l0, Real l1, Int *n, Int *m, Real *lim[], cInterp ***var )
  {
      Real       s0=0,s1=0;
      Int        is=0;
      Int        is0=0,is1=0;
      s0= l0;
      if( s0 == -big )
     { 
         is0= 0;
         s0=  lims[0][is0];
     }
      else
     {
         local( &s0,&is0 );
     }
      s1= l1;
      if( s1 == big )
     {
         is1= ns-1;
         s1=  lims[1][is1];
     }
      else
     {
         local( &s1,&is1 );
     }
      for( is=is0;is<is1;is++ )
     {
         sds[is]->unpack( s0,lims[1][is], n,m,lim, var );
         s0= lims[0][is+1];
     }
      sds[is1]->unpack( s0,s1, n,m,lim, var );
  }

   void cPolyline::pickle( size_t *len, pickle_t *buf )
  {
      Int i;
      pckle( len,ns,buf );
      //pckle( len,ns,lims[0],buf );
      //pckle( len,ns,lims[1],buf );
      //pckle( len,ns,lims[2],buf );
      for( i=0;i<ns;i++) {pckle( len,lims[0][i],buf );} 
      for( i=0;i<ns;i++) {pckle( len,lims[1][i],buf );}
      for( i=0;i<ns;i++) {pckle( len,lims[2][i],buf );}
      for( i=0;i<ns;i++) 
     {
         pckle( len,sds[i]->gettype(),buf );
         sds[i]->pickle( len,buf );
     }
  }

   void cPolyline::unpickle( size_t *len, pickle_t buf )
  {

      Int i;
      interp_e ityp;

      if(lims[0]) { delete[] lims[0]; lims[0]= NULL; }
      if(lims[1]) { delete[] lims[1]; lims[1]= NULL; } 
      if(lims[2]) { delete[] lims[2]; lims[2]= NULL; }
      for(i=0;i<ns;i++) 
     {
        if(sds[i]) { delete sds[i]; sds[i]-=NULL; }
     }
      if(sds) { delete[] sds; sds=NULL; }
 
      unpckle( len,&ns,buf );
      //unpckle( len,&n,lims+0,buf );
      //unpckle( len,&n,lims+1,buf );
      //unpckle( len,&n,lims+2,buf );
      lims[0]= new Real[ns];
      lims[1]= new Real[ns];
      lims[2]= new Real[ns];
      for( i=0;i<ns;i++) {unpckle( len,&(lims[0][i]),buf );} 
      for( i=0;i<ns;i++) {unpckle( len,&(lims[1][i]),buf );}
      for( i=0;i<ns;i++) {unpckle( len,&(lims[2][i]),buf );}
  
      sds= new cInterp*[ns]; 
      for( i=0;i<ns;i++ )
     {
         unpckle( len,&ityp,buf );
         sds[i]= newinterp( ityp );
         sds[i]->unpickle( len,buf );
     }
  }

   cUid *cPolyline::getsrc( Real s )
  {
      Int i; 
      i= bsearch( s,ns-1,lims[2] );
      assert( i > -1 );
      assert( i < ns );
      return sds[i]->cTag::getsrc();
  }

   void cPolyline::settrace( cUid *obj, string data )
  {
      Int i;
      for( i=0;i<ns;i++ )
     {
         sds[i]->settrace( obj,data+"_side_"+strc(i) );
     }
  }

   void cPolyline::fillets( Int n, fillet_t *f, Real *s )
  {

      Real l;
      Int  i0[1000],i1[1000];
      Int  i,m,k;
      bool k0,k1;
      Real s0[1000],s1[1000];
      Real d0[1000],d1[1000];
      cInterp *w[1000];
      setv( 0,1000, (cInterp*)NULL,w );

      Real y0[2],y1[2],dy0[2],dy1[2];

      assert( ns > 1 );
     { 
         for( k=0;k<n;k++ )
        {

            cout << "is it doing this?\n";
            s0[k]= s[k]-0.1*f[k].r/length();
            s1[k]= s[k]+0.1*f[k].r/length();

            local( s0+k,i0+k );
            local( s1+k,i1+k );

            sds[i0[k]]->interp( s0[k],y0,dy0 );
            sds[i1[k]]->interp( s1[k],y1,dy1 );
            cout << "initial positions\n";
            cout << y0[0]<<" "<<y0[1]<<" "<<dy0[0]<<" "<<dy0[1]<<"\n";
            cout << y1[0]<<" "<<y1[1]<<" "<<dy1[0]<<" "<<dy1[1]<<"\n";
            k0= ( lims[1][i0[k]] > lims[0][i0[k]] );
            k1= ( lims[1][i1[k]] > lims[0][i1[k]] );
            cout << "directions are "<<k0<<" "<<k1<<"\n";

            ::fillet( f+k, s0+k,sds[i0[k]],k0, s1+k, sds[i1[k]],k1, w+k,d0+k,d1+k );

            if( i0[k] == ns-1 ){ i0[k]= -1; }; 
            if( valid( f ) )
           {
               cout << "fillet created.."<<i0[k]<<" "<<i1[k]<<"\n";
           }
        }
         for( k=n-1;k>=0;k-- )
        {
            if( n+ns >= ms )
           {
               m= ms; realloc(  &m,n, lims+0 );
               m= ms; realloc(  &m,n, lims+1 );
               m= ms; realloc(  &m,n, lims+2 );
               m= ms; realloc(  &m,n,&sds );
               ms= m;
           }
            if( valid( f+k ) )
           {
               if( i0[k] == -1 ){ i0[k]= ns-1; }
              
               lims[1][i0[k]]= s0[k];            
               lims[0][i1[k]]= s1[k];            
               for( i=ns-1;i>=i1[k];i-- )
              {
                  lims[0][i+1]= lims[0][i];
                  lims[1][i+1]= lims[1][i];
                  lims[2][i+1]= lims[2][i];
                  sds[i+1]= sds[i];
              }
               sds[i1[k]]= w[k];
               lims[0][i1[k]]= d0[k];
               lims[1][i1[k]]= d1[k];
               ns++;

           }
        }
         l= 0;
         for( i=0;i<ns;i++ )
        {
            l+= sds[i]->length()*abs(lims[1][i]-lims[0][i]);
            lims[2][i]= l;
        }
     }
  }

