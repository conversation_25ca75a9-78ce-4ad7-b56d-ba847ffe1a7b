   using namespace std;

#  include <geo/kdtree.h>

   cKdBox::cKdBox()
  { 
      x0[0]= -big;
      x0[1]= -big;
      x0[2]= -big;
      x1[0]=  big;
      x1[1]=  big;
      x1[2]=  big;
      parent=-1;
      child[0]=-1;
      child[1]=-1; 
      ips=-1; 
      ipe=-1; 
  };

   cKdBox::cKdBox(Int Parent, Int Child0, Int Child1, Int Ips, Int Ipe )
  {
      x0[0]= -big;
      x0[1]= -big;
      x0[2]= -big;
      x1[0]=  big;
      x1[1]=  big;
      x1[2]=  big;
      parent= Parent;
      child[0]= Child0;
      child[1]= Child1;
      ips= Ips;
      ipe= Ipe;
  };

   cKdBox::cKdBox( Real *X0, Real *X1, Int Parent, Int Child0, Int Child1, Int Ips, Int Ipe )
  {
      x0[0]= X0[0];
      x0[1]= X0[1];
      x0[2]= X0[2];
      x1[0]= X1[0];
      x1[1]= X1[1];
      x1[2]= X1[2];
      parent= Parent;
      child[0]= Child0;
      child[1]= Child1;
      ips= Ips;
      ipe= Ipe;
  };

   void cKdBox::corners( Real *X0, Real *X1 )
  {
      X0[0]= x0[0];
      X0[1]= x0[1];
      X0[2]= x0[2];
      X1[0]= x1[0];
      X1[1]= x1[1];
      X1[2]= x1[2];
  }

   void cKdBox::plot()
  {
      cout << x0[0] << " " <<x0[1] <<"\n";
      cout << x1[0] << " " <<x0[1] <<"\n";
      cout << x1[0] << " " <<x1[1] <<"\n";
      cout << x0[0] << " " <<x1[1] <<"\n";
      cout << x0[0] << " " <<x0[1] <<"\n";
      cout << "\n";
  }

   Real cKdBox::dist( Int Nx, Real *x )
  {
      Real d;
      Real dx;
      Int ix;
      d= 0.;
      for( ix=0;ix<Nx;ix++ )
     {
         if( x[ix]<x0[ix] ){ dx= x[ix]-x0[ix]; d+= dx*dx; };
         if( x[ix]>x1[ix] ){ dx= x[ix]-x1[ix]; d+= dx*dx; };
     }
      return (sqrt(d));
  }

   Real cKdBox::dist( Int Nx, Int ip, Real *x[] )
  {
      Real d,dx;
      Int ix;
      d= 0.;
      for( ix=0;ix<Nx;ix++ )
     {
         if( x[ix][ip]<x0[ix] ){ dx= x[ix][ip]-x0[ix]; d+= dx*dx; };
         if( x[ix][ip]>x1[ix] ){ dx= x[ix][ip]-x1[ix]; d+= dx*dx; };
     }
      return (sqrt(d));
  }

   cKdTree::cKdTree()
  {
      nx=0;
      nbxs=  0;
      bxs=   NULL;
      iprm=  NULL;
      iprmi= NULL;
      xp[0]= NULL;
      xp[1]= NULL;
      xp[2]= NULL;
  }

   void cKdTree::clean()
  {
      nx=0;
      nbxs=  0;
      delete[] bxs;
      delete[] iprm;
      delete[] iprmi;
      bxs=   NULL;
      iprm=  NULL;
      iprmi= NULL;
      delete[] xp[0]; xp[0]= NULL;
      delete[] xp[1]; xp[1]= NULL;
      delete[] xp[2]; xp[2]= NULL;
      xp[0]= NULL;
      xp[1]= NULL;
      xp[2]= NULL;
  }

   void cKdTree::build( Int Nx, Int Np, Real *x[] )
  {

      if( Np < 3 )
     {
         cout << "never use trees for less than three points\n";
         exit(0);
     }
// stacks of parent boxes to be partitioned and partitioning directions
      Int pstk[50],dstk[50];

      Int  i,j,m,ntmp,istk,jb,ix;
      Int  iprn,idim;
      Int  ips,ipe;

      Real x0[3],x1[3];
      Real y0[3],y1[3];

      np= Np;
      nx= Nx;
      iprm=  new Int[np];
      iprmi= new Int[np];

// points coordinates
     // for( ix=0;ix<nx;ix++ )
     //{
     //    xp[ix]= x[ix];
     //}

      for( ix=0;ix<nx;ix++ )
     {
         xp[ix] = new Real [np];
     }

      for(ix=0; ix<nx; ix++)
     {
        for(Int ip=0; ip<np; ip++)
       {
          xp[ix][ip] = x[ix][ip];
       }
     }

// initialise points list
      for( i=0;i<np;i++)iprm[i]=i;
  
// compute number of boxes
      j=1;
      for( i=np; i; i >>= 1 )
     {
         j <<= 1;
     }
      nbxs= 2*np-(j>>1);
      if( j < nbxs ) nbxs= j;
      nbxs--;

// allocate boxes
      bxs= new cKdBox[nbxs];
      bxs[0]= cKdBox(0,0,0, 0,np-1 );
      jb= 0;
      pstk[1]= 0;
      dstk[1]= 0;
      istk=1;

// create boxes: pending task list (kept in istk)

      while( istk )
     {

         iprn= pstk[istk];                        // parent box
         idim= dstk[istk--];                      // current dimension
         bxs[iprn].range( &ips, &ipe );           // range of points in parent box
         ntmp= ipe-ips+1;                           
         i= (ntmp-1)/2;
         select( i, ntmp,iprm+ips, x[idim] );       // split in two halves

         
         bxs[iprn].corners(x0,x1);
         bxs[iprn].corners(y0,y1);
         x0[idim]= x[idim][iprm[i+ips]];
         x1[idim]= x0[idim];
         bxs[++jb]= cKdBox( y0,x1, iprn,0,0, ips,ips+i );
         bxs[++jb]= cKdBox( x0,y1, iprn,0,0, ips+i+1,ipe );
         bxs[iprn].setChildren( jb-1,jb );
         if( i > 1 )
        {
            pstk[++istk]= jb-1;
            dstk[istk]= (idim+1)%nx;
        }
         if( ntmp-i > 3 )
        {
            pstk[++istk]= jb;
            dstk[istk]= (idim+1)%nx;
        }
     }
      for( i=0;i<np;i++ ) iprmi[iprm[i]]= i;

  }

   Int cKdTree::locate( Real *x )
  {

      Real x0[3],x1[3];
      Int ib,ib0,ib1,id;
      ib= 0;
      id= 0;
      bxs[ib].getChildren( &ib0,&ib1 );
      while( ib0 )
     {
         bxs[ib0].corners(x0,x1);
         if( x[id] <= x1[id] )
        {
            ib=ib0;
        }
         else
        {
            ib=ib1;
        }
         bxs[ib].getChildren( &ib0,&ib1 );
         id= ++id % nx;
     }
      return ib;
  }

   Int cKdTree::locate( Int ip0 )
  {
      Int ip,ips,ipe;
      Int ib,ib0,ib1;
      ip= iprmi[ip0];
      ib= 0; 
      bxs[ib].getChildren( &ib0,&ib1 );
      while( ib0 )
     {
         bxs[ib0].range( &ips,&ipe );
         if( ip <= ipe )
        {
            ib= ib0;
        }
         else
        {
            ib= ib1;
        }
         bxs[ib].getChildren( &ib0,&ib1 );
     }
      return ib;
  }

   void cKdTree::print( Int ib )
  {
      bxs[ib].print();
  }

   Real dist( Int nx, Real *x, Int ip, Real *xp[] )
  {
      Real tmp,d;
      Int  ix;
      tmp= xp[0][ip]-x[0]; 
      d= tmp*tmp;
      for( ix=1;ix<nx;ix++ )
     {
         tmp= xp[ix][ip]-x[ix]; d+= tmp*tmp;
     }
      d= sqrt(d);
      return d;
  }

   Real cKdTree::dist( Real *x, Int ip )
  {
      Real tmp,d;
      Int  ix;
      tmp= xp[0][ip]-x[0]; 
      d= tmp*tmp;
      for( ix=1;ix<nx;ix++ )
     {
         tmp= xp[ix][ip]-x[ix]; d+= tmp*tmp;
     }
      d= sqrt(d);
      return d;
  }

   Real cKdTree::dist( Int ip1, Int ip2 )
  {
      Real tmp,d;
      Int  ix;
      if( ip1 == ip2 )
     {
         return big;
     }
      else
     {
         tmp= xp[0][ip1]-xp[0][ip2]; 
         d= tmp*tmp;
         for( ix=1;ix<nx;ix++ )
        {
            tmp= xp[ix][ip1]-xp[ix][ip2]; d+= tmp*tmp;
        }
         d= sqrt(d);
         return d;
     }
  }

   void cKdTree::nearest( Real *x, Int *imin, Real *dmin )
  {
      Int ib,ip,ix,nstk;
      Int ips,ipe,ib0,ib1;
      Int stk[50];
      Real d;

     *dmin= big;

// nearest neighbour in the box containing the point

      ib= locate( x );
      bxs[ib].range( &ips,&ipe );
//    cout << "locate identifies box "<<ib<<" ";
//    print( ib );
      for( ip=ips;ip<=ipe;ip++ )
     {
         d= dist( x,iprm[ip] );
         if( d < *dmin )
        {
           *dmin= d;
           *imin=iprm[ip];
        }
     }
//    cout << "max distance is "<<d<<"\n";

// traverse tree discarding all boxes at distance >= dmin

      stk[1]= 0;
      nstk=1;
      while( nstk )
     {
         ib= stk[nstk--];
         if( bxs[ib].dist(nx,x) < *dmin )
        {
            bxs[ib].getChildren( &ib0,&ib1 );
            if( ib0 )
           {
               stk[++nstk]= ib0;
               stk[++nstk]= ib1;
           }
            else
           {
// end of tree box - find nearest neighbour
               bxs[ib].range( &ips,&ipe );
               for( ip=ips;ip<=ipe;ip++ )
              {
                  d= dist( x,iprm[ip] );
                  if( d < *dmin )
                 {
                    *dmin= d;
                    *imin=iprm[ip];
                 }
              }
           }
        }
     }
  }

   void cKdTree::nearest( Int ip0, Int n, Int *imin, Real *dmin )
  {

      Int ib,ib0,ib1,ib2;
      Int ip,jp,ips,ipe;

      Int nstk;
      Int stk[50];
      Real d;
      if( n > np-1 )
     {
         cout << "too many neighbours requested\n";
         std::exit(0);
     } 
      for( ip=0;ip<n;ip++ )  
     {
         dmin[ip]= big;
     }

      ib0= locate( ip0 );
      do 
     {
         ib0= bxs[ib0].getParent();
         bxs[ib0].range( &ips,&ipe );
     }while( ipe-ips < n );
 
      for( jp=ips;jp<=ipe;jp++ )
     {
         ip= iprm[jp];
         if( ip0 == ip )continue;
         d= dist( ip0,ip );
         if( d < dmin[0] )
        {
            dmin[0]= d;
            imin[0]= ip;
            if( n>1 )sift_down( n, dmin,imin );
        } 
     }

      nstk=1;
      stk[nstk]= 0;
      while( nstk )
     {
         ib= stk[nstk--];
         if( ib == ib0 ) continue;
         if( bxs[ib].dist(nx,ip0,xp) < dmin[0] )
        {
            bxs[ib].getChildren( &ib1,&ib2 );
            if( ib1 )
           {
               stk[++nstk]= ib1;
               stk[++nstk]= ib2;
           }
            else
           {
               bxs[ib].range( &ips,&ipe );
               for( jp=ips;jp<=ipe;jp++ )
              {
                  ip= iprm[jp];
                  if( ip0 == ip )continue;
                  d= dist( ip0,ip );
                  if( d < dmin[0] )
                 {
                     dmin[0]= d;
                     imin[0]= ip;
                     if( n>1 )sift_down( n, dmin,imin );
                 } 
              }
           }
        }
     }
  }

   cKdTree::~cKdTree()
  {
/*    np=0;
      nx=0;
      nbxs=  0;
      delete[] bxs;
      delete[] iprm;
      delete[] iprmi;
      bxs=NULL;
      iprm=NULL;
      iprmi=NULL;*/
      clean();
  }
   void cKdTree::nearest( Real *x0, Int n, Int *imin, Real *dmin )
  {
      nearest( x0, imin, dmin );
      nearest( imin[0] , n-1 , imin+1, dmin+1 );
  }
