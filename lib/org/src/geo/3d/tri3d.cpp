   using namespace std;

#  include <geo/3d/tri3d.h>
#  include <fstream>
#  include <sstream>

#  define  MXNP 10000
#  define  MXNE 10000
#  define  MXNS    10

   void init( tri3d_t *var )
  {
      Int     i;

      var->debug=        false;
      var->np=               0;
      var->ns=               0;
      var->mp=               0;
      var->ne=               0;
      var->srf= new cInterp3d*[MXNS];
      setv( 0,MXNS, (cInterp3d*)NULL, var->srf ); 
      var->x[0]=      new Real[MXNP];
      var->x[1]=      new Real[MXNP];
      var->x[2]=      new Real[MXNP];
      var->y[0]=      new Real[MXNP];
      var->y[1]=      new Real[MXNP];
      var->iep[0]=    new  Int[MXNE];
      var->iep[1]=    new  Int[MXNE];
      var->iep[2]=    new  Int[MXNE];
      var->jep[0]=    new  Int[MXNE];
      var->jep[1]=    new  Int[MXNE];
      var->jep[2]=    new  Int[MXNE];
      var->iee[0]=    new  Int[MXNE];
      var->iee[1]=    new  Int[MXNE];
      var->iee[2]=    new  Int[MXNE];
      var->dee[0]=    new Real[MXNE];
      var->dee[1]=    new Real[MXNE];
      var->dee[2]=    new Real[MXNE];
      var->ies=       new  Int[MXNE];
      setv( 0,MXNE, -1, var->ies );

      var->q=    new Real**[MXNE];
      var->r=    new  Real*[MXNE];
      for( i=0;i<MXNE;i++ )
     {
         var->r[i]=    new  Real[3];
         var->q[i]=    new Real*[2];
         var->q[i][0]= new  Real[3];
         var->q[i][1]= new  Real[3];
     }

      var->nb= 0;
      var->lbp=NULL;
      var->ibp[0]=NULL;
      var->ibp[1]=NULL;
      var->bnm= NULL;
  }

   void destroy( tri3d_t *var )
  {
      Int i;
      setv( 0,MXNS, (cInterp3d*)NULL, var->srf );
      delete[] var->srf; var->srf= NULL;
      for( i=0;i<MXNE;i++ )
     {
         delete[] var->q[i][0]; var->q[i][0]= NULL;
         delete[] var->q[i][1]; var->q[i][1]= NULL;
         delete[] var->r[i];    var->r[i]= NULL;
         delete[] var->q[i];    var->q[i]= NULL;
     }
      delete[] var->q;      var->q=      NULL;
      delete[] var->r;      var->r=      NULL;
      delete[] var->x[0];   var->x[0]=   NULL;
      delete[] var->x[1];   var->x[1]=   NULL;
      delete[] var->x[2];   var->x[2]=   NULL;
      delete[] var->y[0];   var->y[0]=   NULL;
      delete[] var->y[1];   var->y[1]=   NULL;
      delete[] var->iep[0]; var->iep[0]= NULL;
      delete[] var->iep[1]; var->iep[1]= NULL;
      delete[] var->iep[2]; var->iep[2]= NULL;
      delete[] var->jep[0]; var->jep[0]= NULL;
      delete[] var->jep[1]; var->jep[1]= NULL;
      delete[] var->jep[2]; var->jep[2]= NULL;
      delete[] var->iee[0]; var->iee[0]= NULL;
      delete[] var->iee[1]; var->iee[1]= NULL;
      delete[] var->iee[2]; var->iee[2]= NULL;
      delete[] var->dee[0]; var->dee[0]= NULL;
      delete[] var->dee[1]; var->dee[1]= NULL;
      delete[] var->dee[2]; var->dee[2]= NULL;
      delete[] var->ies;    var->ies=    NULL;
      var->np=0;
      var->mp=0;
      var->ne=0;

      delete[] var->lbp;    var->lbp=    NULL;
      delete[] var->ibp[0]; var->ibp[0]= NULL;
      delete[] var->ibp[1]; var->ibp[1]= NULL;
      delete[] var->bnm;    var->bnm=    NULL;
      var->nb= 0;
  }

   void build0( tri3d_t *var )
  {
      cDgraph     *gph;
      Int         nce;
      Int         npc;
      Int       **ice[1];
      Int       **iep;
      Int           n=0;
      Int       **jec[1]= {NULL};
      Int        *jce[2]= {NULL,NULL};
      Int        *kce[2]= {NULL,NULL};
      Int           i,j,k,h;
      Int           i0,i1;


      iep= new Int*[3];
      iep[0]= var->iep[0];
      iep[1]= var->iep[1];
      iep[2]= var->iep[2];

      jec[0]= new Int*[3];
      jec[0][0]= new Int[var->ne];
      jec[0][1]= new Int[var->ne];
      jec[0][2]= new Int[var->ne];

      triangles( &npc,&nce,ice );

      gph= new cDgraph( var->np,npc,1,&(var->ne),&nce,ice );
      gph->build( &iep );
      gph->pack( &n,jec );

      jce[0]= new Int[n];
      jce[1]= new Int[n];
      kce[0]= new Int[n];
      kce[1]= new Int[n];
      setv( 0,n, -1, jce[0] );
      setv( 0,n, -1, jce[1] );
      setv( 0,n, -1, kce[0] );
      setv( 0,n, -1, kce[1] );
      for( h=0;h<3;h++ )
     {
         for( j=0;j<var->ne;j++ )
        {
            i= jec[0][h][j];
            k= 0;
            if( jce[0][i] != -1 ){ k=1; };
            assert( jce[k][i] == -1 );
            jce[k][i]= j; 
            kce[k][i]= h; 
        }
     }

      for( j=0;j<n;j++ )
     {
         i0= jce[0][j];  
         i1= jce[1][j];  
         if( i0 != -1 )
        { 
            k= kce[0][j];
            var->iee[k][i0]= i1;
        }
         if( i1 != -1 )
        {
            k= kce[1][j];
            var->iee[k][i1]= i0;
        }
     }

      delete[] jec[0][0]; jec[0][0]=  NULL;
      delete[] jec[0][1]; jec[0][1]=  NULL;
      delete[] jec[0][2]; jec[0][2]=  NULL;
      delete[] jec[0]; jec[0]= NULL;
      delete[] iep; iep=NULL;

      delete[] jce[0]; jce[0]= NULL;
      delete[] jce[1]; jce[1]= NULL;
      delete[] kce[0]; kce[0]= NULL;
      delete[] kce[1]; kce[1]= NULL;
      gph->destroy( ice );
      delete gph; gph= NULL;
  }

   void build( string s, tri3d_t *var )
  {
      Int             n,m;
      Int             ng;
      Int             i,j,k,h,l;
      Int             i0,i1,i2;
      string          line,dum;
      stringstream    buf;
      ifstream        f;
      Real            x,y,z;

      f.open( s.c_str() );

      getline( f,line ); 
      getline( f,line ); 
      getline( f,line ); 
      getline( f,line ); 
      getline( f,line ); 

      getline( f,line ); 
      buf.clear(); buf.str( line );
      buf >> n >> m >> ng;

      getline( f,line ); 
      getline( f,line ); 
      getline( f,line ); 
      getline( f,line ); 
      getline( f,line ); 
      getline( f,line ); 
      getline( f,line ); 

      for( i=0;i<n;i++ )
     {
         getline( f,line ); 
         buf.clear(); buf.str( line );
         buf >> j >> x >> y >> z;
         var->x[0][i]= x;
         var->x[1][i]= y;
         var->x[2][i]= z;
     }
      var->np= n;

      getline( f,line ); 
      getline( f,line ); 
      getline( f,line ); 

      for( k=0;k<ng;k++ )
     {
         getline( f,line ); 
         buf.clear(); buf.str( line );
         buf >> dum >> j >> dum >> m >> dum >> l >> dum >> i >> dum >> h;
         getline( f,line ); 
         if( l == 3 && h == 2 && h == 2 )
        {
            j=var->ne;
            for( i=0;i<m;i++ )
           {
               getline( f,line );
               buf.clear(); buf.str( line );
               buf >> h >> i0 >> i1 >> i2; 
               var->iep[0][j]= --i0;
               var->iep[1][j]= --i1;
               var->iep[2][j]= --i2;
               j++;
           }
            var->ne= j;
        }
         else
        {
            for( i=0;i<m;i++ )
           {
               getline( f,line ); 
           }
        }
     }

      f.close(); 

      build0( var );
      normals( var );
  }

   
   void gnuplot( string s, tri3d_t *var )
  {
      Int        i; 
      Int        i0,i1,i2;
      ofstream   f;
      f.open( s.c_str() );
      for( i=0;i<var->ne;i++ )
     {
         i0= var->iep[0][i];
         i1= var->iep[1][i];
         i2= var->iep[2][i];
         f << "\n";
         f << "#\n";
         f << "\n";
         f << var->x[0][i0]<<" "<<var->x[1][i0]<<" "<<var->x[2][i0]<<"\n";
         f << var->x[0][i1]<<" "<<var->x[1][i1]<<" "<<var->x[2][i1]<<"\n";
         f << var->x[0][i2]<<" "<<var->x[1][i2]<<" "<<var->x[2][i2]<<"\n";
         f << var->x[0][i0]<<" "<<var->x[1][i0]<<" "<<var->x[2][i0]<<"\n";
     }
      f.close();
  }

   void tec( string s, tri3d_t *var )
  {
      ofstream fle;
      Int   i, i0,i1,i2,i3;

      fle.open( s.c_str() );

      fle << "VARIABLES = X0 X1 X2\n";
      fle << "ZONE N=" << var->np << " E=" << var->ne << " F=FEPOINT ET=QUADRILATERAL\n";
  
      for(i=0;i<var->np;i++)
     {
         fle << var->x[0][i] << " " << var->x[1][i] << " " << var->x[2][i] << "\n";
     }

      for(i=0;i<var->ne;i++)
     {
         i0 = var->iep[0][i];
         i1 = var->iep[1][i];
         i2 = var->iep[2][i];

         i0++; 
         i1++; 
         i2++; 

         fle << i0 << " " << i1 << " " << i2 << " " << i0 << "\n";
     }
      fle.close();

  }

   void normals( tri3d_t *var )
  {
      Int i;
      Int i0,i1,i2;
      Real x0[3],x1[3],x2[3];
      for( i=0;i<var->ne;i++ )
     {
         i0= var->iep[0][i];
         i1= var->iep[1][i];
         i2= var->iep[2][i];
         line3( i0,var->x, x0 );
         line3( i1,var->x, x1 );
         line3( i2,var->x, x2 );
         sub3( x1,x0, x1 );
         sub3( x2,x0, x2 );
         qrf23( x1,x2, var->q[i][0],var->q[i][1],var->r[i] );
     }

  }

   bool inside( Real *y )
  {
      bool val=false;
      Real tol= 1.e-9;
// pedestrian
      val=        ( y[0] > -tol ) && ( y[0] < 1+tol );
      val= val && ( y[1] > -tol ) && ( y[1] < 1+tol ); 
      val= val && ( y[2] > -tol ) && ( y[2] < 1+tol ); 
      return val;
  }

   void project( Int n, Real *x[], Real *y[], Int *ipe, tri3d_t *var )
  {
      Int        i,j,k;    
      Int        i0,i1,i2;
      Int        j0,j1,j2;
      Real       xp[3];
      Real       yp[2];
      Real       x0[3];
      Real        z[3];
      Real       dx[3];
      Real       y0[2];
      Real       y1[2];
      Real       y2[2];
      setv( 0,n, -1, ipe );
      for( i=0;i<n;i++ )
     {
         line3( i,x,xp );
         for( j=0;j<var->ne;j++ )
        {
            i0= var->iep[0][j];
            line3( i0,var->x,x0 );
            sub3( xp,x0, dx );  
            qrs23( var->q[j][0],var->q[j][1],var->r[j], z+1, dx );
            z[0]= 1-z[1]-z[2];
            if( inside( z ) )
// point more or less inside triangle
           {
                ipe[i]= j;
                j0= var->jep[0][j];
                j1= var->jep[1][j];
                j2= var->jep[2][j];
                line2( j0,var->y,y0 ); 
                line2( j1,var->y,y1 ); 
                line2( j2,var->y,y2 ); 
                yp[0]= z[0]*y0[0]+ z[1]*y1[0]+ z[2]*y2[0];
                yp[1]= z[0]*y0[1]+ z[1]*y1[1]+ z[2]*y2[1];
                break;
           }
        } 
         assert( ipe[i]> -1 );
         correct( xp, ipe[i], yp, var );
         x[0][i]= xp[0];
         x[1][i]= xp[1];
         x[2][i]= xp[2];
         y[0][i]= yp[0];
         y[1][i]= yp[1];
     }
  }

   void local( Real *x, Int i, Real *y, tri3d_t *var )
  {
      Real x0[3],dx[3];
      Int  i0;
      i0= var->iep[0][i];
      line3( i0,var->x,x0 );
      sub3( x,x0, dx );
      qrs23( var->q[i][0],var->q[i][1],var->r[i], y, dx );
  }

   void global( Real *y, Int i, Real *x, tri3d_t *var )
  {
      Real x0[3];
      Real x1[3];
      Real x2[3];
      Int  i0,i1,i2;

      assert( var->ns > 0 );
      i0= var->iep[0][i];
      i1= var->iep[1][i];
      i2= var->iep[2][i];
      line3( i0,var->x,x0 );
      line3( i1,var->x,x1 );
      line3( i2,var->x,x2 );
      x[0]= x0[0]+ y[0]*(x1[0]-x0[0])+ y[1]*(x2[0]-x0[0]);
      x[1]= x0[1]+ y[0]*(x1[1]-x0[1])+ y[1]*(x2[1]-x0[1]);
      x[2]= x0[2]+ y[0]*(x1[2]-x0[2])+ y[1]*(x2[2]-x0[2]);

  }

   Int minval3( Real *x )
  {
      Int val;
      val=0;
      if( x[1] < x[0]   ){ val=1; };
      if( x[2] < x[val] ){ val=2; };
      return val;
  }

   void project( Real *x, Int *h, Real *y, tri3d_t *var )
  {
      Int        i,j,k;    
      Int        i0,i1,i2;
      Int        j0,j1,j2;
      Real       x0[3];
      Real        z[3];
      Real       dx[3];
      Real       y0[3];
      Real       y1[3];
      Real       y2[3];
     *h= -1;
      for( j=0;j<var->ne;j++ )
     {
         i0= var->iep[0][j];
         line3( i0,var->x,x0 );
         sub3( x,x0, dx );  
         qrs23( var->q[j][0],var->q[j][1],var->r[j], z+1, dx );
         z[0]= 1-z[1]-z[2];
         if( inside( z ) )
// point more or less inside triangle
        {
           *h= j;
            j0= var->jep[0][j];
            j1= var->jep[1][j];
            j2= var->jep[2][j];
            line2( j0,var->y,y0 ); 
            line2( j1,var->y,y1 ); 
            line2( j2,var->y,y2 ); 
            y[0]= z[0]*y0[0]+ z[1]*y1[0]+ z[2]*y2[0];
            y[1]= z[0]*y0[1]+ z[1]*y1[1]+ z[2]*y2[1];
            break;
        }
     } 
  }

   void search( Real *x, Int *i, Real *y, tri3d_t *var )
  {
      
      Real x0[3],dx0[2][3];
      Real xp[3],dxp[2][3];
      Real yp[2];
      Real x1[3];
      Real x2[3];
      Real y0[2];
      Real y1[2];
      Real y2[2];
      Real z[3];
      Int  iprm[3]={1,2,0};
      Int  i0,i1,i2;
      Int  j0,j1,j2;
      Int  j;
      Int  nt;
      nt= 0;
      do
     {
//       cout << "visiting triangle "<<*i<<" "<<var->ies[*i]<<"\n";
         local( x,*i,z+1,var );
         z[0]= 1-z[1]-z[2];
         if( inside(z) )
        {
            j0= var->jep[0][*i];
            j1= var->jep[1][*i];
            j2= var->jep[2][*i];
            line2( j0,var->y,y0 ); 
            line2( j1,var->y,y1 ); 
            line2( j2,var->y,y2 ); 
            y[0]= z[0]*y0[0]+ z[1]*y1[0]+ z[2]*y2[0];
            y[1]= z[0]*y0[1]+ z[1]*y1[1]+ z[2]*y2[1];
            break;
        }
         else  
        {
            j= minval3( z );
            j= iprm[j];
            j= var->iee[j][*i];
            assert( j >= 0 );
           *i= j; 
        }
         nt++;
         assert( nt < var->ne );
     }while( true );
  }

   void build( Int n, cInterp3d **s, tri3d_t *var )
  {
      string          sn;

      Int              np,ne;
      Int              i,j,k;
      Int            *iep[3];
      Real           *y[2];
      Real            y0[2];
      Real            x0[2],dx0[2][3];
      Real            w0,w1;

      Int             m=4;
      iep[0]= new Int[2*m*m];
      iep[1]= new Int[2*m*m];
      iep[2]= new Int[2*m*m];
      y[0]=  new Real[(m+1)*(m+1)];
      y[1]=  new Real[(m+1)*(m+1)];
         

      k=0;
      for( j=0;j<m+1;j++ )
     {
         for( i=0;i<m+1;i++ )
        {
            w1= j; 
            w1/= m;
            w0= i; 
            w0/= m;
            y[0][k]= w0;
            y[1][k]= w1;
            k++;
        }
     }
      np= k;
      k=0;
      for( j=0;j<m;j++ )
     {
         for( i=0;i<m;i++ )
        {
            iep[0][k]=     i+(m+1)*j;
            iep[1][k]=   1+iep[0][k];
            iep[2][k]= m+1+iep[1][k];
            k++;
            iep[0][k]=     i+(m+1)*j;
            iep[1][k]= m+1+iep[0][k]+1;
            iep[2][k]= m+1+iep[0][k];
            k++;
        }
     }
      ne= k;


      for( i=0;i<n;i++ )
     {
         add( np,y,ne,iep,s[i], var );
     }
      build0( var );
      normals( var );

      delete[] iep[0]; iep[0]= NULL;
      delete[] iep[1]; iep[1]= NULL;
      delete[] iep[2]; iep[2]= NULL;
      delete[] y[0]; y[0]= NULL;
      delete[] y[1]; y[1]= NULL;
  }

   void add( Int n, Real *y[], Int m, Int *it[], cInterp3d *s, tri3d_t *var )
  {
      Int i,j,k;
      
      Int           n0;
      Real          tol= 1.e-15;
      Real          y0[2],x[3],dx[2][3];
      Int          *iprm;
      Int          *jprm;

      iprm= new Int[n];
      jprm= new Int[n];

      setv( 0,n, -1, iprm );
      setv( 0,n, -1, jprm );

      n0= var->np;
      for( i=0;i<n;i++ )
     {
         y0[0]= y[0][i];
         y0[1]= y[1][i];

         s->interp( y0, x,dx[0],dx[1] );

         j= ptinlst3( x,n0,var->x, tol );
         if( j == -1 )
        {
            j= var->np;
            assert( j < MXNP );
            var->np++; 
        }
         var->x[0][j]= x[0]; 
         var->x[1][j]= x[1]; 
         var->x[2][j]= x[2]; 
         iprm[i]= j;
         j= var->mp;
         assert( j < MXNP );
         var->y[0][j]= y0[0];
         var->y[1][j]= y0[1];
         jprm[i]= j;
         var->mp++;
     }
 
      for( i=0;i<m;i++ )
     {
         j= var->ne;
         var->iep[0][j]= iprm[ it[0][i] ];
         var->iep[1][j]= iprm[ it[1][i] ];
         var->iep[2][j]= iprm[ it[2][i] ];
         var->jep[0][j]= jprm[ it[0][i] ];
         var->jep[1][j]= jprm[ it[1][i] ];
         var->jep[2][j]= jprm[ it[2][i] ];
         var->ies[j]= var->ns;
         var->ne++;
         assert( var->ne < MXNE );
     }
      j= var->ns;
      assert( j < MXNS );
      var->srf[j]= s; 
      var->ns++;
      
      delete[] iprm;
      delete[] jprm;
 
  }

   void correct( Real *x, Int i, Real *y, Real *x0, Real *dy, tri3d_t *var )
  {
      Real       dx[3];
      Real       dx0[2][3];
      Real       v0[3],v1[3],r[3]; 

      Int        it;
      Int         j;

      j= var->ies[i];
     (var->srf[j])->interp( y, x0,dx0[0],dx0[1] ); 
      qrf23( dx0[0],dx0[1], v0,v1,r );
      sub3( x,x0, dx ); 
      qrs23( v0,v1,r, dy, dx );
  }

   void correct( Real *x, Int i, Real *y, tri3d_t *var )
  {
      Real       x0[3];
      Real       dy[2];

      Int        it;
      Int         j;

      for( it=0;it<10;it++ )
     {
         correct( x,i,y, x0,dy, var );
         y[0]+= dy[0];
         y[1]+= dy[1];
     }
      x[0]= x0[0];
      x[1]= x0[1];
      x[2]= x0[2];
  }

   void boundary( Int m, Int *ib[], string b, tri3d_t *var )
  {
      Int n0,n;
      Int i,j,k;
      k= inlst( b, var->nb,var->bnm );
      assert( k == -1 );

      n0= var->nb;
      n= n0; realloc( &n,1, &(var->lbp)  ); var->lbp[n0]=0;

      n0= 0;
      if( var->nb > 0 )
     {
         n0= var->lbp[var->nb-1]; 
     }
      n= n0; realloc( &n,m,   var->ibp+0 );
      n= n0; realloc( &n,m,   var->ibp+1 );
      n= n0; realloc( &n,m, &(var->bnm)  );
    

      cout << "reallocated\n";
      cout << var->ibp[0]<<"\n";
      cout << var->ibp[1]<<"\n";
      cout << var->lbp<<"\n";

      i= n0;
      for( j=0;j<m;j++ )
     {
         var->ibp[0][i]= ib[0][j];
         var->ibp[1][i]= ib[1][j];
         i++;
     }
      var->lbp[var->nb]= i;
      var->bnm[var->nb]= b;
     (var->nb)++;
      

  }

   void rename( string s, string b, tri3d_t *var )
  {
      Int k; 
      k= inlst( s,var->nb,var->bnm );
      if( k > -1 )
     {
         var->bnm[k]= b;
     }
  }

   void stitch( tri3d_t *var0, tri3d_t *var1 )
  {
      Int       h,k; 
      for( h=0;h<var0->nb;h++ )
     {
         for( k=0;k<var1->nb;k++ )
        {
            if( var0->bnm[h] == var1->bnm[k] )
           {
               cout << "stitch along "<<var0->bnm[h]<<"\n";
           }
        }
     }
  }

   void gnuplot( string s, string b, tri3d_t *var )
  {
      Int       j,k; 
      Int       i0,i1;
      Int       ist,ien;
      ofstream  f;

      k= inlst( s,var->nb,var->bnm );
      if( k > -1 )
     {
         ien= var->lbp[k]; 
         ist= 0;
         if( k > 0 )
        {
            ist= var->lbp[k-1]; 
        }
         f.open(s.c_str());
         for( j=ist;j<ien;j++ )
        {
            i0= var->ibp[0][j];
            i1= var->ibp[1][j];
            f<< "\n";
            f<< "#\n";
            f<< "\n";
            f<< var->x[0][i0]<<" "<<var->x[1][i0]<<" "<<var->x[2][i0]<<"\n";
            f<< var->x[0][i1]<<" "<<var->x[1][i1]<<" "<<var->x[2][i1]<<"\n";
        }
         f.close( );
     }
  }
