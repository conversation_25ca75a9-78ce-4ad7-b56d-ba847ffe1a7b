   using namespace std;

#  include <geo/3d/interp3d.h>

   cExtruded::cExtruded()
  {
      pl= NULL;
      setv( 0,3, 0.,l0 );
      setv( 0,3, 0.,m0 );
      setv( 0,3, 0.,a0 );
      setv( 0,3, 0.,a1 );
  }

   cExtruded::~cExtruded()
  {
      delete pl; pl= NULL;
      setv( 0,3, 0.,l0 );
      setv( 0,3, 0.,m0 );
      setv( 0,3, 0.,a0 );
      setv( 0,3, 0.,a1 );
  }
   
   void cExtruded::copy( cInterp3d **var )
  {
      cExtruded *tmp;
      if( !(*var) )
     {
         tmp= new cExtruded();
         tmp->build( pl,l0,m0,a0,a1);
       (*var)=tmp;
     }
      else
     {
         cout << "cannot copy into existing cInterp3d of unknown kind (cExtruded::copy)\n";
         exit(0);
     }

  }
   void cExtruded::build( cPolyline *pl0, Real *l, Real *m, Real *a, Real *b )
  {
      Real v0[4],v1[4],v2[4];
      pl0->copy( (cInterp**)&pl ); 

      idv3( l,v0 );
      idv3( m,v1 );
      v0[3]= norm23( v0 ); sclv3( 1./v0[3],v0 );
      vec3( v0,v1,v2 );
      vec3( v2,v0,v1 );
      v1[3]= norm23( v1 ); sclv3( 1./v1[3],v1 );
      idv3( v0,l0 );
      idv3( v1,m0 );
      idv3( a,a0 );
      idv3( b,a1 );
      sub3( a1,a0,da );
 
      mt0[0]= pl0->length();
      mt0[1]= 0;
      mt1[0]= 0;
      mt1[1]= norm23( da );
  }

   void cExtruded::interp( Real *s, Real *y, Real *dy0, Real *dy1 )
  {
      Real s0,s1;
    
      Real d,dd;
      Real cth,sth;
      Real z[2],dz[2];
      Real x[3],dx[3];
      Real v0[3],v1[3];
      Real dv0[3],dv1[3];

      s0= s[0];
      s1= s[1];

/*   {
         cout << "--------------------------------\n";
         cout << "differentiation check - polyline\n";
         Real z0[2],dz0[2];
         pl->interp( s0,z0,dz0 );
         cout << "polyline (0) "<< z0[0]<<" "<< z0[1]<<" (unperturbed position )\n";
         cout << "polyline (1) "<<dz0[0]<<" "<<dz0[1]<<" (unperturbed tg. vector )\n";
         s0+= 1.e-6;
         pl->interp( s0,z,dz );
         cout << "polyline (2) "<< (z[0]-z0[0] )/1.e-4<<" "<<(z[1]-z0[1])/1.e-4<<" (position perturbation )\n";
         s0-= 1.e-6;
         cout << "--------------------------------\n";
     }*/

      pl->interp( s0,z,dz );

      x[0]= a0[0]+  z[0]*l0[0]+ z[1]*m0[0];
      x[1]= a0[1]+  z[0]*l0[1]+ z[1]*m0[1];
      x[2]= a0[2]+  z[0]*l0[2]+ z[1]*m0[2];

      dx[0]=        dz[0]*l0[0]+dz[1]*m0[0];
      dx[1]=        dz[0]*l0[1]+dz[1]*m0[1];
      dx[2]=        dz[0]*l0[2]+dz[1]*m0[2];
      
      y[0]= s[1]*da[0]+ x[0];
      y[1]= s[1]*da[1]+ x[1];
      y[2]= s[1]*da[2]+ x[2];

      dy0[0]= dx[0];
      dy0[1]= dx[1];
      dy0[2]= dx[2];

      dy1[0]= da[0];
      dy1[1]= da[1];
      dy1[2]= da[2];
  }
