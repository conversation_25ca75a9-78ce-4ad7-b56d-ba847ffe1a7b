
   using namespace std;

#  include <geo/3d/fillet3d.h>
#  include <cassert>

   extern "C" void dgetrf_( int *, int *, Real *, int *, int *, int * );
   extern "C" void dgetrs_( char *, int *, int *, Real *, int *, int *, Real *, int *, int * );

   bool valid( fillet3d_t *var )
  {
      bool val;
      val= ( var->its < ITPMXIT ) && ( var->info == 0 ) && ( var->res < ITPPTOL );
      return val;
  }

   void reset( fillet3d_t *var )
  {
      var->rlx=ITPRLX0;
      var->s0[0]= 0;
      var->s0[1]= 0;
      var->s1[0]= 0;
      var->s1[1]= 0;
      var->ds0[0]= big;
      var->ds0[1]= big;
      var->ds1[0]= big;
      var->ds1[1]= big;
      var->res=    big;
      var->info=    -1;
      var->its=     -1;
  }

   void hints( fillet3d_t *var, Real *y0, Real *y1 )
  {
      idv2( y0,var->s0 );
      idv2( y1,var->s1 );
  }

   cFillet3d::cFillet3d()
  {
      ns=0;
      fast=  false;
      cyl=   false;
      r=    -big;
      sf0=   NULL;
      sf1=   NULL;
      ss=    NULL;
      xs[0]= NULL;
      xs[1]= NULL;
      xs[2]= NULL;
      ls[0]= NULL;
      ls[1]= NULL;
      ls[2]= NULL;
      s0s[0]= NULL;
      s0s[1]= NULL;
      s1s[0]= NULL;
      s1s[1]= NULL;
      x0s[0]= NULL;
      x0s[1]= NULL;
      x0s[2]= NULL;
      x1s[0]= NULL;
      x1s[1]= NULL;
      x1s[2]= NULL;
      n0s[0]= NULL;
      n0s[1]= NULL;
      n0s[2]= NULL;
      n1s[0]= NULL;
      n1s[1]= NULL;
      n1s[2]= NULL;
  }

   void cFillet3d::store( Int n )
  {
      ns=n;
      ss=    new Real[n];
      xs[0]= new Real[n];
      xs[1]= new Real[n];
      xs[2]= new Real[n];
      ls[0]= new Real[n];
      ls[1]= new Real[n];
      ls[2]= new Real[n];
      s0s[0]= new Real[n];
      s0s[1]= new Real[n];
      s1s[0]= new Real[n];
      s1s[1]= new Real[n];
      x0s[0]= new Real[n];
      x0s[1]= new Real[n];
      x0s[2]= new Real[n];
      x1s[0]= new Real[n];
      x1s[1]= new Real[n];
      x1s[2]= new Real[n];
      n0s[0]= new Real[n];
      n0s[1]= new Real[n];
      n0s[2]= new Real[n];
      n1s[0]= new Real[n];
      n1s[1]= new Real[n];
      n1s[2]= new Real[n];
  }
 
   cFillet3d::~cFillet3d()
  {
      clean();
  }

   void cFillet3d::clean()
  {

      delete    sf0; sf0= NULL;
      delete    sf1; sf1= NULL;

      r=   -big;
      delete[]  ss;     ss=    NULL;
      delete[]  xs[0];  xs[0]= NULL;
      delete[]  xs[1];  xs[1]= NULL;
      delete[]  xs[2];  xs[2]= NULL;
      delete[]  ls[0];  ls[0]= NULL;
      delete[]  ls[1];  ls[1]= NULL;
      delete[]  ls[2];  ls[2]= NULL;
      delete[] s0s[0]; s0s[0]= NULL;
      delete[] s0s[1]; s0s[1]= NULL;
      delete[] s1s[0]; s1s[0]= NULL;
      delete[] s1s[1]; s1s[1]= NULL;
      delete[] x0s[0]; x0s[0]= NULL;
      delete[] x0s[1]; x0s[1]= NULL;
      delete[] x0s[2]; x0s[2]= NULL;
      delete[] x1s[0]; x1s[0]= NULL;
      delete[] x1s[1]; x1s[1]= NULL;
      delete[] x1s[2]; x1s[2]= NULL;
      delete[] n0s[0]; n0s[0]= NULL;
      delete[] n0s[1]; n0s[1]= NULL;
      delete[] n0s[2]; n0s[2]= NULL;
      delete[] n1s[0]; n1s[0]= NULL;
      delete[] n1s[1]; n1s[1]= NULL;
      delete[] n1s[2]; n1s[2]= NULL;
      ns=0;
  }

   void d2dx( cInterp3d *var, Real *s0, Real x[3], Real dx[2][3], Real ddx[2][2][3] )
  {
      Real      eps=1.e-6;
      Real      s[2];
      
      idv2( s0,s ); s[0]+= eps;
      var->interp( s, x,ddx[0][0],ddx[0][1] );
      idv2( s0,s ); s[1]+= eps;
      var->interp( s, x,ddx[1][0],ddx[1][1] );
      var->interp( s0, x,dx[0],dx[1] );

      sub3( ddx[0][0],dx[0], ddx[0][0] ); sclv3( 1./eps, ddx[0][0] );
      sub3( ddx[0][1],dx[1], ddx[0][1] ); sclv3( 1./eps, ddx[0][1] );
      sub3( ddx[1][0],dx[0], ddx[1][0] ); sclv3( 1./eps, ddx[1][0] );
      sub3( ddx[1][1],dx[1], ddx[1][1] ); sclv3( 1./eps, ddx[1][1] );

  }

   void cFillet3d::support( Real *x, Real *l, fillet3d_t *stat )
  {

      Real      x0[3],dx0[2][3],ddx0[2][2][3];
      Real      x1[3],dx1[2][3],ddx1[2][2][3];
      Real      x01[3],x11[3];
      Real      dx2[3];
      Real      s0[2],s1[2];
      Real      n0[4],n1[4];
      Real      dn00[2][3],dn10[2][3];
      Real      dn0[2][3],dn1[2][3];
      Real     *a[4],rhs[4];
      Real      g0,g1;
      Real      d;
      Real      dn[3];
      Int       i;
      int       lda=4,ldb=4,n=4,nrhs=1;
      int       info;
      int      *ipiv;
      char      job='n';

      Real      res=big;
      Int       it;
//    Real      rlx=ITPRLX0;
      Real      rlx=stat->rlx;

      it= 0;
      g0= f0*r;
      g1= f1*r;

      idv2( stat->s0,s0 );
      idv2( stat->s1,s1 );

      ipiv= stat->ipiv;
      subv( 4,4, stat->a,a );

      while( res > ITPPTOL )
     {
         d2dx( sf0,s0,x0,dx0,ddx0 );
         d2dx( sf1,s1,x1,dx1,ddx1 );

         vec3( dx0[0],dx0[1], n0 );
         vec3( dx1[0],dx1[1], n1 );
         n0[3]= norm23( n0 );
         n1[3]= norm23( n1 );

         vec3(     dx0[0],ddx0[0][1],  dn0[0] );
         vec3( ddx0[0][0],    dx0[1], dn00[0] );
         vec3(     dx0[0],ddx0[1][1],  dn0[1] );
         vec3( ddx0[1][0],    dx0[1], dn00[1] );
         add3( dn0[0],dn00[0], dn0[0] );
         add3( dn0[1],dn00[1], dn0[1] );

         vec3(     dx1[0],ddx1[0][1],  dn1[0] );
         vec3( ddx1[0][0],    dx1[1], dn10[0] );
         vec3(     dx1[0],ddx1[1][1],  dn1[1] );
         vec3( ddx1[1][0],    dx1[1], dn10[1] );
         add3( dn1[0],dn10[0], dn1[0] );
         add3( dn1[1],dn10[1], dn1[1] );

         sclv3( 1./n0[3],n0 );
         sclv3( 1./n1[3],n1 );

         sclv3( 1./n0[3],dn0[0] );
         sclv3( 1./n0[3],dn0[1] );
         sclv3( 1./n1[3],dn1[0] );
         sclv3( 1./n1[3],dn1[1] );

         orth3( n0,dn0[0], dn0[0] );
         orth3( n0,dn0[1], dn0[1] );
         orth3( n1,dn1[1], dn1[1] );
         orth3( n1,dn1[1], dn1[1] );

         setv( 0,16, 0., stat->a );

         a[0][0]= dx0[0][0]+g0*dn0[0][0];
         a[0][1]= dx0[0][1]+g0*dn0[0][1];
         a[0][2]= dx0[0][2]+g0*dn0[0][2];
         a[0][3]= dot3(dx0[0],l)+ g0*dot3(dn0[0],l);

         a[1][0]= dx0[1][0]+g0*dn0[1][0];
         a[1][1]= dx0[1][1]+g0*dn0[1][1];
         a[1][2]= dx0[1][2]+g0*dn0[1][2];
         a[1][3]= dot3(dx0[1],l)+ g0*dot3(dn0[1],l);

         a[2][0]=-dx1[0][0]-g1*dn1[0][0];
         a[2][1]=-dx1[0][1]-g1*dn1[0][1];
         a[2][2]=-dx1[0][2]-g1*dn1[0][2];
         a[2][3]= 0;

         a[3][0]=-dx1[1][0]-g1*dn1[1][0];
         a[3][1]=-dx1[1][1]-g1*dn1[1][1];
         a[3][2]=-dx1[1][2]-g1*dn1[1][2];
         a[3][3]= 0;

         dgetrf_( &n,&n, stat->a,&lda, ipiv, &info );
         if( info != 0 )
        {
            cout << "singular jacobian encountered\n";
            assert(false);
            break;
        }

         x01[0]= x0[0]+ g0*n0[0];
         x01[1]= x0[1]+ g0*n0[1];
         x01[2]= x0[2]+ g0*n0[2];

         x11[0]= x1[0]+ g1*n1[0];
         x11[1]= x1[1]+ g1*n1[1];
         x11[2]= x1[2]+ g1*n1[2];
            
         rhs[0]= x01[0]- x11[0];
         rhs[1]= x01[1]- x11[1];
         rhs[2]= x01[2]- x11[2];

         sub3( x01,x, dx2 );
         rhs[3]= dot3( dx2,l );

         res= norminf( 4,rhs );

         dgetrs_( &job,&n,&nrhs, stat->a,&lda, ipiv, rhs,&lda, &info );

         s0[0]-= rlx*rhs[0];
         s0[1]-= rlx*rhs[1];
         s1[0]-= rlx*rhs[2];
         s1[1]-= rlx*rhs[3];

         rlx*= ITPDRLX;
         rlx= min( rlx,ITPMRLX );

/*       if( stat->its == -1 )
        { 
            if( (++it) > 5 )
           {
               break;
           }
        }
         else*/
        {
            if( (++it) > stat->its )
           {
               cout << "cFillet3d::support "<<s0[0]<<" "<<s0[1]<<" "<<s1[0]<<" "<<s1[1]<<" "<<res<<" "<< "WARNING: maximum iteration count exceeded\n";
               break;
           }
        }
     }

      stat->its=    it;
      stat->res=   res;
      stat->info= info;
      idv2( s0,stat->s0 );
      idv2( s1,stat->s1 );
      idv2( rhs+0,stat->ds0 );
      idv2( rhs+2,stat->ds1 );
      idv3( x0,stat->x0 );
      idv3( x1,stat->x1 );
      idv3( n0,stat->n0 );
      idv3( n1,stat->n1 );
      idv3( dn0[0],stat->dn0[0] );
      idv3( dn0[1],stat->dn0[1] );
      idv3( dn1[0],stat->dn1[0] );
      idv3( dn1[1],stat->dn1[1] );
      idv3( dx0[0],stat->dx0[0] );
      idv3( dx0[1],stat->dx0[1] );
      idv3( dx1[0],stat->dx1[0] );
      idv3( dx1[1],stat->dx1[1] );

/*    cout << s0[0]<<" "<<s0[1]<<" "<<rhs[0]<<" "<<rhs[1]<<"\n";
      cout << s1[0]<<" "<<s1[1]<<" "<<rhs[2]<<" "<<rhs[3]<<"\n";
      cout << s2[0]<<" "<<s2[1]<<" "<<rhs[4]<<" "<<rhs[5]<<"\n";
      cout << "plane (x)"<< x[0]<<" "<<x[1]<<" "<<x[2]<<"\n";
      cout << "plane (l)"<< l[0]<<" "<<l[1]<<" "<<l[2]<<"\n";*/


  }

   void cFillet3d::dsupport( Real *x, Real *l, Real *dx, Real *dl, fillet3d_t *stat, 
                                                                   fillet3d_t *dstat )
  {

      Real      g0,g1;
      Real      n0[4],n1[4];
      Real      x0[3],dx0[2][3],dn0[2][3];
      Real      x1[3],dx1[2][3],dn1[2][3];
      Real      ds0[2],ds1[2];
      Real      x01[3],x11[3];
      Real      dx2[3];

      Real     *a[4],rhs[4];
      int      *ipiv;
      int       info;
      char      job='n';
      int       lda=4,ldb=4,n=4,nrhs=1;

      ipiv= stat->ipiv;
      subv( 4,4, stat->a, a );

      g0= f0*r;
      g1= f1*r;

      idv3( stat->x0,x0 );
      idv3( stat->x1,x1 );
      idv3( stat->n0,n0 );
      idv3( stat->n1,n1 );
      idv3( stat->dn0[0],dn0[0] );
      idv3( stat->dn0[1],dn0[1] );
      idv3( stat->dn1[0],dn1[0] );
      idv3( stat->dn1[1],dn1[1] );
      idv3( stat->dx0[0],dx0[0] );
      idv3( stat->dx0[1],dx0[1] );
      idv3( stat->dx1[0],dx1[0] );
      idv3( stat->dx1[1],dx1[1] );

      x01[0]= x0[0]+ g0*n0[0];
      x01[1]= x0[1]+ g0*n0[1];
      x01[2]= x0[2]+ g0*n0[2];

      sub3( x01,x, dx2 );

      rhs[0]= 0.;
      rhs[1]= 0.;
      rhs[2]= 0.;
      rhs[3]= dot3( dx2,dl )- dot3( dx,l );
         
      dgetrs_( &job,&n,&nrhs, stat->a,&lda, ipiv, rhs,&lda, &info );

      ds0[0]= -rhs[0]; 
      ds0[1]= -rhs[1]; 
      ds1[0]= -rhs[2]; 
      ds1[1]= -rhs[3]; 

      dx0[0][0]= dx0[0][0]*ds0[0]+ dx0[1][0]*ds0[1];
      dx0[0][1]= dx0[0][1]*ds0[0]+ dx0[1][1]*ds0[1];
      dx0[0][2]= dx0[0][2]*ds0[0]+ dx0[1][2]*ds0[1];

      dx1[0][0]= dx1[0][0]*ds1[0]+ dx1[1][0]*ds1[1];
      dx1[0][1]= dx1[0][1]*ds1[0]+ dx1[1][1]*ds1[1];
      dx1[0][2]= dx1[0][2]*ds1[0]+ dx1[1][2]*ds1[1];

      dn0[0][0]= dn0[0][0]*ds0[0]+ dn0[1][0]*ds0[1];
      dn0[0][1]= dn0[0][1]*ds0[0]+ dn0[1][1]*ds0[1];
      dn0[0][2]= dn0[0][2]*ds0[0]+ dn0[1][2]*ds0[1];

      dn1[0][0]= dn1[0][0]*ds1[0]+ dn1[1][0]*ds1[1];
      dn1[0][1]= dn1[0][1]*ds1[0]+ dn1[1][1]*ds1[1];
      dn1[0][2]= dn1[0][2]*ds1[0]+ dn1[1][2]*ds1[1];


      idv3( dx0[0], dstat->x0 );
      idv3( dx1[0], dstat->x1 );
      idv3( dn0[0], dstat->n0 );
      idv3( dn1[0], dstat->n1 );


  }
 
   void cFillet3d::build( cInterp3d *srf0, cInterp3d *srf1, Real g0, Real g1, Real rd, 
                          Int n, Real *s, Real *x[], Real *l[], fillet3d_t *stat )
  {

      Real      xi[3],li[3];
      Int       i;

      store( n );

      srf0->copy( &sf0 );
      srf1->copy( &sf1 );

      f0= g0;
      f1= g1;
      r= rd;

      line3(   0,x,xi );
      line3( n-1,x,li );
      sub3( li,xi, li );
      cyl= ( norminf3( li ) < ITPPTOL );
      if( cyl ){ cout << "fillet is closed\n"; };

      for( i=0;i<n;i++ )
     {

         line3( i,x, xi );
         line3( i,l, li );
         stat->its= ITPMXIT;
         support( xi,li, stat );
         if( valid( stat ) )
        {
            ss[i]=     s[i];
            xs[0][i]= xi[0];
            xs[1][i]= xi[1];
            xs[2][i]= xi[2];
            ls[0][i]= li[0];
            ls[1][i]= li[1];
            ls[2][i]= li[2];
            s0s[0][i]= stat->s0[0];
            s0s[1][i]= stat->s0[1];
            s1s[0][i]= stat->s1[0];
            s1s[1][i]= stat->s1[1];
            x0s[0][i]= stat->x0[0];
            x0s[1][i]= stat->x0[1];
            x0s[2][i]= stat->x0[2];
            x1s[0][i]= stat->x1[0];
            x1s[1][i]= stat->x1[1];
            x1s[2][i]= stat->x1[2];
            n0s[0][i]= stat->n0[0];
            n0s[1][i]= stat->n0[1];
            n0s[2][i]= stat->n0[2];
            n1s[0][i]= stat->n1[0];
            n1s[1][i]= stat->n1[1];
            n1s[2][i]= stat->n1[2];
             
        }
         else
        {
            clean();
            cout << "failed\n";
            assert( false );
        }
     }
  }

   void cFillet3d::check( string name )
  {
      Int i,j,n;
      ofstream fle;
      Real z[3],dz[2][3];
      Real w[2];

      
      if( ns > 0 )
     {
         n= 50;
         fle.open( name.c_str() );
         fle << "VARIABLES = \"X\", \"Y\", \"Z\"\n";
         fle << "ZONE I="<<n<<", J="<<ns<<", F=POINT\n";
         for( j=0;j<ns;j++ )
        {
            w[0]= ss[j];
            for( i=0;i<n;i++ )
           {
               w[1]= (Real)i/(Real)(n-1);
               interp( w, z,dz[0],dz[1] );
               fle <<  z[0]<<" "<< z[1]<<" "<< z[2]<<"\n";

           }
        }
         fle.close();
     }
  }

   void cFillet3d::interp( Real *w, Real *x, Real *dx0, Real *dx1 )
  {

      Real w1; 
      Real w2; 
      Real g0,g1;

      Real ds;
      Real z[4]; 
      Real dz[4]; 
      Real xi[3],xi1[3]; 
      Real li[3],li1[3]; 
      Real x0i[3]; 
      Real n0i[3]; 
      Real n1i[3]; 
      Real dxi[3]; 
      Real dli[3]; 
      Real dx0i[3]; 
      Real dn0i[3]; 
      Real dn1i[3]; 
      Int  i;
      fillet3d_t stat,dstat;

      w1= w[0];
      w2= w[1];
      

      if( cyl )
     {
         while( w1<0 ){ w1++; };
         while( w1>1 ){ w1--; };
         while( w[0]<0 ){ w[0]++; };
         while( w[0]>1 ){ w[0]--; };
     }
      w[1]= max( -0.5,min(1.5,w[1]) );
      w2= max( -0.5,min(1.5,w2) );

      i=bsearch( w1,ns-1,ss+1 );
      assert( i>=0 );
      assert( i<ns-1 );

      reset( &stat );
      reset( &dstat );

      line3( i,xs, xi );
      line3( i,ls, li );
      line3( i+1,xs, xi1 );
      line3( i+1,ls, li1 );

      ds= ss[i+1]-ss[i];
      ds=1./ds;

      w1-= ss[i];
      w1*=ds;

      sub3( xi1,xi, dxi );
      sub3( li1,li, dli );

      sclv3( ds,dxi );
      sclv3( ds,dli );

      xi[0]= w1*xi1[0]+(1-w1)*xi[0];
      xi[1]= w1*xi1[1]+(1-w1)*xi[1];
      xi[2]= w1*xi1[2]+(1-w1)*xi[2];

      li[0]= w1*li1[0]+(1-w1)*li[0];
      li[1]= w1*li1[1]+(1-w1)*li[1];
      li[2]= w1*li1[2]+(1-w1)*li[2];

      stat.s0[0]= w1*s0s[0][i+1]+ (1-w1)*s0s[0][i];
      stat.s0[1]= w1*s0s[1][i+1]+ (1-w1)*s0s[1][i];

      stat.s1[0]= w1*s1s[0][i+1]+ (1-w1)*s1s[0][i];
      stat.s1[1]= w1*s1s[1][i+1]+ (1-w1)*s1s[1][i];

      stat.rlx= ITPMRLX;
      stat.its= ITPMXIT;
//    if( fast ){ stat.its= -1; };
      support( xi,li, &stat );
      dsupport( xi,li, dxi,dli, &stat,&dstat );
      idv3( stat.x0,x0i );
      idv3( stat.n0,n0i );
      idv3( stat.n1,n1i );
      idv3( dstat.x0,dx0i );
      idv3( dstat.n0,dn0i );
      idv3( dstat.n1,dn1i );

      z[0]= f0*w2*n0i[0]+ f1*(1-w2)*n1i[0];
      z[1]= f0*w2*n0i[1]+ f1*(1-w2)*n1i[1];
      z[2]= f0*w2*n0i[2]+ f1*(1-w2)*n1i[2];

      z[3]= norm23( z ); 
      sclv3( 1./z[3],z );

      dz[0]= f0*w2*dn0i[0]+ f1*(1-w2)*dn1i[0];
      dz[1]= f0*w2*dn0i[1]+ f1*(1-w2)*dn1i[1];
      dz[2]= f0*w2*dn0i[2]+ f1*(1-w2)*dn1i[2];

      sclv3( 1./z[3],dz );
      orth3(  z,dz, dz );
      sclv3( -r,dz );

      dx0[0]= dx0i[0]+ r*f0*dn0i[0]+ dz[0];
      dx0[1]= dx0i[1]+ r*f0*dn0i[1]+ dz[1];
      dx0[2]= dx0i[2]+ r*f0*dn0i[2]+ dz[2];

      dz[0]= f0*n0i[0]- f1*n1i[0];
      dz[1]= f0*n0i[1]- f1*n1i[1];
      dz[2]= f0*n0i[2]- f1*n1i[2];

      sclv3( 1./z[3],dz );
      orth3(  z,dz, dz );
      sclv3( -r,dz );

      dx1[0]= dz[0];
      dx1[1]= dz[1];
      dx1[2]= dz[2];

      sclv3( -r,z );
      x[0]= x0i[0]+ r*f0*n0i[0]+ z[0];
      x[1]= x0i[1]+ r*f0*n0i[1]+ z[1];
      x[2]= x0i[2]+ r*f0*n0i[2]+ z[2];

  }

   void cFillet3d::interp( Int i, Real w, Real *x, Real *dx )
  {
      Real z[4]; 
      Real dz[4]; 

      z[0]= f0*w*n0s[0][i]+ f1*(1-w)*n1s[0][i];
      z[1]= f0*w*n0s[1][i]+ f1*(1-w)*n1s[1][i];
      z[2]= f0*w*n0s[2][i]+ f1*(1-w)*n1s[2][i];

      dz[0]= f0*n0s[0][i]- f1*n1s[0][i];
      dz[1]= f0*n0s[1][i]- f1*n1s[1][i];
      dz[2]= f0*n0s[2][i]- f1*n1s[2][i];

      z[3]= norm23( z ); 
      sclv3( 1./z[3],z );
      sclv3( 1./z[3],dz );
      orth3(  z,dz, dz );
      sclv3( -r,z );
      sclv3( -r,dz );

      x[0]= x0s[0][i]+ r*f0*n0s[0][i]+ z[0];
      x[1]= x0s[1][i]+ r*f0*n0s[1][i]+ z[1];
      x[2]= x0s[2][i]+ r*f0*n0s[2][i]+ z[2];

      dx[0]= dz[0];
      dx[1]= dz[1];
      dx[2]= dz[2];
  }

   void cFillet3d::edges( Real *e0[], Real *e1[] )
  {
      Int i;
      for( i=0;i<ns;i++ )
     {
         e0[0][i]= x0s[0][i];
         e0[1][i]= x0s[1][i];
         e0[2][i]= x0s[2][i];
         e1[0][i]= x1s[0][i];
         e1[1][i]= x1s[1][i];
         e1[2][i]= x1s[2][i];
     }
  }
