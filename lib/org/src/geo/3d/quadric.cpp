   using namespace std;

#  include <geo/3d/interp3d.h>

   void cQuadric::build( Real *v0, Real *v1, Real *v2, Real *v3, Real *v4, Real *v5 )
  {
      idv3( v0,v[0] );
      idv3( v1,v[1] );
      idv3( v2,v[2] );
      idv3( v3,v[3] );
      idv3( v4,v[4] );
      idv3( v5,v[5] );
  }

   void cQuadric::interp( Real *s, Real *y, Real *dy0, Real *dy1 )
  {
      y[0]= v[0][0]+ s[0]*v[1][0]+ s[1]*v[2][0]+ s[0]*s[0]*v[3][0]+ s[0]*s[1]*v[4][0]+ s[1]*s[1]*v[5][0];
      y[1]= v[0][1]+ s[0]*v[1][1]+ s[1]*v[2][1]+ s[0]*s[0]*v[3][1]+ s[0]*s[1]*v[4][1]+ s[1]*s[1]*v[5][1];
      y[2]= v[0][2]+ s[0]*v[1][2]+ s[1]*v[2][2]+ s[0]*s[0]*v[3][2]+ s[0]*s[1]*v[4][2]+ s[1]*s[1]*v[5][2];

      dy0[0]= v[1][0]+ 2*s[0]*v[3][0]+ s[1]*v[4][0];
      dy0[1]= v[1][1]+ 2*s[0]*v[3][1]+ s[1]*v[4][1];
      dy0[2]= v[1][2]+ 2*s[0]*v[3][2]+ s[1]*v[4][2];

      dy1[0]= v[2][0]+ s[0]*v[4][0]+ 2*s[1]*v[5][0];
      dy1[1]= v[2][1]+ s[0]*v[4][0]+ 2*s[1]*v[5][1];
      dy1[2]= v[2][2]+ s[0]*v[4][0]+ 2*s[1]*v[5][2];
  }

   void cQuadric::copy( cInterp3d **var )
  {
      cQuadric *tmp;
      if( !(*var) )
     {
         tmp= new cQuadric();
         tmp->build( v[0],v[1],v[2],v[3],v[4],v[5] );
       (*var)=tmp;
     }
      else
     {
         cout << "cannot copy into existing cInterp3d of unknown kind (cQuadric::copy) \n";
         exit(0);
     }
  }
