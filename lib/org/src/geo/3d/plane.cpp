   using namespace std;

#  include <geo/3d/interp3d.h>


   void cPlane::build( Real *v0, Real *v1, Real *v2 )
  {
      idv3( v0,v[0] );
      idv3( v1,v[1] );
      idv3( v2,v[2] );

      mt0[0]= 1.;
      mt0[1]= 0.;
      mt1[0]= 0.;
      mt1[1]= 1.;
  }

   void cPlane::copy( cInterp3d **var )
  {
      cPlane *tmp;
      if( !(*var) )
     {
         tmp= new cPlane();
         tmp->build( v[0],v[1],v[2] );
       (*var)= tmp;
     }
      else
     {
         cout << "cannot copy into existing cInterp3d of unknown type (cPlane::copy)\n";
         exit(0);
     }
  }

   void cPlane::interp( Real *s, Real *y, Real *dy0, Real *dy1 )
  {
      y[0]= v[0][0]+ s[0]*v[1][0]+ s[1]*v[2][0]; 
      y[1]= v[0][1]+ s[0]*v[1][1]+ s[1]*v[2][1]; 
      y[2]= v[0][2]+ s[0]*v[1][2]+ s[1]*v[2][2]; 

      idv3( v[1],dy0 );
      idv3( v[2],dy1 );
  }
