   using namespace std;

#  include <geo/3d/interp3d.h>

   cXrRevolution::cXrRevolution()
  {
      pl= NULL;
  }

   cXrRevolution::~cXrRevolution()
  {
      delete pl; pl= NULL;
  }
   

   void cXrRevolution::copy( cInterp3d **var )
  {
      cXrRevolution *tmp;
      if( !(*var) )
     {
         tmp= new cXrRevolution();
         tmp->build( m0,m1,pl );
       (*var)= tmp;
     }
      else
     {
         cout << "cannot copy into existing cInterp3d of unknown kind (cXrRevolution::copy)\n";
         exit(0);
     }
  }

   void cXrRevolution::build( Real l0, Real l1, cVSpline *pl0 )
  {
      Real y[10],dy[10];

      m0= l0;
      m1= l1;
      pl0->copy( &pl ); 

      mt0[0]= m1-m0; // far from ideal, should have a length associated with the cVSpline
      mt0[1]= 0;

      pl0->interp( 0.5*(m1-m0),y,dy );
      mt1[0]= 0;
      mt1[1]= pi2*y[1];
 
  }

   void cXrRevolution::interp( Real *s, Real *y, Real *dy0, Real *dy1 )
  {
      Real s0,s1;
      Real dm;
      Real cth,sth;
      Real z[10],dz[10];

      s0= s[0];
      s1= s[1];

/*   {
         cout << "--------------------------------\n";
         cout << "differentiation check - polyline\n";
         Real z0[2],dz0[2];
         pl->interp( s0,z0,dz0 );
         cout << "polyline (0) "<< z0[0]<<" "<< z0[1]<<" (unperturbed position )\n";
         cout << "polyline (1) "<<dz0[0]<<" "<<dz0[1]<<" (unperturbed tg. vector )\n";
         s0+= 1.e-6;
         pl->interp( s0,z,dz );
         cout << "polyline (2) "<< (z[0]-z0[0] )/1.e-4<<" "<<(z[1]-z0[1])/1.e-4<<" (position perturbation )\n";
         s0-= 1.e-6;
         cout << "--------------------------------\n";
     }*/

      dm= m1-m0;
      s0= m0*(1-s0)+m1*s0;
      pl->interp( s0,z,dz );

      s1*= pi2;
      cth= cos(s1);
      sth= sin(s1);

      y[0]= z[0];
      y[1]= sth*z[1];
      y[2]= cth*z[1];

      dy0[0]=     dz[0];
      dy0[1]= sth*dz[1];
      dy0[2]= cth*dz[1];
      sclv3( dm,dy0 );

      dy1[0]= 0;
      dy1[1]= cth*z[1];
      dy1[2]=-sth*z[1];
      sclv3( pi2,dy1 );
  }

/* void cXrRevolution::triangulate( tri3d_t **var )
  {
      cout << "inside cXrRevolution triangulate\n";
      cout << "metrics for this surface "<<mt0[0]<<" "<<mt0[1]<<"\n";
      cout << "metrics for this surface "<<mt1[0]<<" "<<mt1[1]<<"\n";
  }*/
