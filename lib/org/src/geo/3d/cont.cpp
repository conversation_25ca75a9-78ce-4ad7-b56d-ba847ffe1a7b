   using namespace std;

#  include <geo/3d/interp3d.h>

   extern "C" void dgetrf_( int *, int *, Real *, int *, int *, int * );
   extern "C" void dgetrs_( char *, int *, int *, Real *, int *, int *, Real *, int *, int * );

   void cont( cInterp3d *sf0, cInterp3d *sf1, Real *s0, Real *s1, Real l, Real ang, inter3d_t *stat )
  {

      Int  i;
      Real x0[3],dx0[2][3],n0[4];
      Real x1[3],dx1[2][3],n1[4];

      Real z0[2],z1[2];
      Real dz0[2],dz1[2];

      Real dx[4];

      Real dl;
      Real x[3];
      Real v[4],g[4];

      Real sa[16],*a[4]={ sa+0,sa+4,sa+8,sa+12 };
      Real r[4];
      Real rlx;
      Real res;

      int  ipiv[4];
      int  info;
      int  na=4,nr=1;
      char ct='n';
      
      idv2( s0,z0 );
      idv2( s1,z1 );

      sf0->interp( z0,x0,dx0[0],dx0[1] );      
      sf0->interp( z1,x1,dx1[1],dx1[1] );

      rlx= stat->rlx;

      for( i=0;i<100;i++ )
     {
         add3( x0,x1,x );
         sclv3( 0.5,x );

         vec3( dx0[0],dx0[1], n0 ); 
         vec3( dx1[0],dx1[1], n1 );

         n0[3]= norm23( n0 ); sclv3( 1./n0[3],n0 );
         n1[3]= norm23( n1 ); sclv3( 1./n1[3],n1 );
         vec3( n0,n1, v );
         v[3]=  norm23(  v ); sclv3( 1./v[3],v );

         dz0[0]= dot3( dx0[1],n1 );
         dz0[1]=-dot3( dx0[0],n1 );

         dz1[0]= dot3( dx1[1],n0 );
         dz1[1]=-dot3( dx1[0],n0 );

         dx[0]= dz0[0]*dx0[0][0]+ dz0[1]*dx0[1][0];
         dx[1]= dz0[0]*dx0[0][1]+ dz0[1]*dx0[1][1];
         dx[2]= dz0[0]*dx0[0][2]+ dz0[1]*dx0[1][2];
         dl= dot3( v,dx );
         sclv2( l/dl,dz0 );

         dx[0]= dz1[0]*dx1[0][0]+ dz1[1]*dx1[1][0];
         dx[1]= dz1[0]*dx1[0][1]+ dz1[1]*dx1[1][1];
         dx[2]= dz1[0]*dx1[0][2]+ dz1[1]*dx1[1][2];
         dl= dot3( v,dx );
         sclv2( l/dl,dz1 );

         add2( z0,dz0, z0 );
         add2( z1,dz1, z1 );

         res=big;
         while( res > ITPPTOL )
        {

            sf0->interp( z0,x0,dx0[0],dx0[1] );
            sf1->interp( z1,x1,dx1[0],dx1[1] );

            sub3( x0,x, dx );

            a[0][0]=  dx0[0][0];
            a[0][1]=  dx0[0][1];
            a[0][2]=  dx0[0][2];
            a[0][3]=  2*dot3( dx0[0],dx );
            
            a[1][0]=  dx0[1][0];
            a[1][1]=  dx0[1][1];
            a[1][2]=  dx0[1][2];
            a[0][3]=  2*dot3( dx0[1],dx );

            a[2][0]= -dx1[0][0];
            a[2][1]= -dx1[0][1];
            a[2][2]= -dx1[0][2];
            a[2][3]= 0;

            a[3][0]= -dx1[1][0];
            a[3][1]= -dx1[1][1];
            a[3][2]= -dx1[1][2];
            a[3][3]= 0;

            sub3( x0,x1, r );
            r[3]= dot3( dx,dx )-l*l;
            
            dgetrf_( &na,&na, sa,&na, ipiv, &info );
            if( info != 0 ){ break; };
            dgetrs_( &ct,&na,&nr,sa,&na, ipiv, r,&na, &info );

            z0[0]-= rlx*r[0];
            z0[1]-= rlx*r[1];

            z1[0]-= rlx*r[2];
            z1[1]-= rlx*r[3];

            rlx*= ITPDRLX;
            rlx= min( ITPMRLX,rlx );

            sub3( x0,x1,dx );
            res= norminf3( dx );

        }
         cout << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<" "<<x1[0]<<" "<<x1[1]<<" "<<x1[2]<<"\n";
     }
  }
  
