   using namespace std;

#  include <geo/3d/interp3d.h>
#  include <assert.h>
// include <tri/mesh1d.h>
// include <tri/del/delaunay.h>

   void cInterp3d::approx( Real md, Real ma, Real *s0, Real *s1, Int *na, Real *xa[] )
  {

      Int                  nstk;
      Int                  istk;
      Int                  tmp,mstk,m,dsize=1000;
      
      Real y00[3],dy000[4],dy100[4];
      Real y01[3],dy001[4],dy101[4];
      Real y10[3],dy010[4],dy110[4];
      Real y11[3],dy011[4],dy111[4];
      Real x0[3],x1[3];

      Real s00[2],s01[2],s10[2],s11[2],s[2];
      Real d0,d;
      Real v0[3];

      m=0;
      Real *xbx[6]= { NULL, NULL, NULL, NULL, NULL, NULL };
      Real *sbx[4]= { NULL, NULL, NULL, NULL };

      mstk=dsize;
      nstk=1;
      Real *stk[4];
      stk[0]= new Real[dsize];
      stk[1]= new Real[dsize];
      stk[2]= new Real[dsize];
      stk[3]= new Real[dsize];

      stk[0][0]= s0[0];
      stk[1][0]= s0[1];
      stk[2][0]= s1[0];
      stk[3][0]= s1[1];
      
      while( nstk > 0 )
     {
         istk= --nstk;

         s00[0]= stk[0][istk]; s00[1]= stk[1][istk];
         s01[0]= stk[0][istk]; s01[1]= stk[3][istk];
         s10[0]= stk[2][istk]; s10[1]= stk[1][istk];
         s11[0]= stk[2][istk]; s11[1]= stk[3][istk];

         interp( s00, y00,dy000,dy100 );
         interp( s10, y10,dy010,dy110 );
         interp( s01, y01,dy001,dy101 );
         interp( s11, y11,dy011,dy111 );

         dy000[3]= norm23( dy000 ); sclv3( 1./dy000[3],dy000 );
         dy001[3]= norm23( dy001 ); sclv3( 1./dy001[3],dy001 );
         dy010[3]= norm23( dy010 ); sclv3( 1./dy010[3],dy010 );
         dy011[3]= norm23( dy011 ); sclv3( 1./dy011[3],dy011 );

         dy100[3]= norm23( dy100 ); sclv3( 1./dy100[3],dy100 );
         dy101[3]= norm23( dy101 ); sclv3( 1./dy101[3],dy101 );
         dy110[3]= norm23( dy110 ); sclv3( 1./dy110[3],dy110 );
         dy111[3]= norm23( dy111 ); sclv3( 1./dy111[3],dy111 );

         vec3( dy000,dy001, v0 ); d0= norm23( v0 ); d= d0;
         vec3( dy010,dy011, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );
         vec3( dy000,dy010, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );
         vec3( dy001,dy011, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );

         vec3( dy100,dy101, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );
         vec3( dy110,dy111, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );
         vec3( dy100,dy110, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );
         vec3( dy101,dy111, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );

         if( d > 0.12 )
        {
            s[0]= 0.5*( s00[0]+ s11[0] );
            s[1]= 0.5*( s00[1]+ s11[1] );

            if( nstk == mstk )
           {
               tmp= mstk;
               realloc( &tmp,dsize, stk+0 ); tmp=mstk;
               realloc( &tmp,dsize, stk+1 ); tmp=mstk;
               realloc( &tmp,dsize, stk+2 ); tmp=mstk;
               realloc( &tmp,dsize, stk+3 ); 
               mstk=tmp;
           }

            stk[0][istk]= s00[0]; stk[1][istk]= s00[1]; stk[2][istk]=   s[0]; stk[3][istk]=   s[1]; istk++;
            stk[0][istk]=   s[0]; stk[1][istk]=   s[1]; stk[2][istk]= s11[0]; stk[3][istk]= s11[1]; istk++;
            stk[0][istk]= s01[0]; stk[1][istk]= s01[1]; stk[2][istk]=   s[0]; stk[3][istk]=   s[1]; istk++;
            stk[0][istk]=   s[0]; stk[1][istk]=   s[1]; stk[2][istk]= s10[0]; stk[3][istk]= s10[1]; istk++;
            nstk= istk;

        }
         else
        {
            corners( y00, dy000, dy100, y10, dy010, dy110,
                     y01, dy001, dy101, y11, dy011, dy111, x0, x1 );
//          sbprint( x0, x1, &fle );

/*          Int n0;
            n0= *n;

            xbx[0][n0]= x0[0];
            xbx[1][n0]= x0[1];
            xbx[2][n0]= x0[2];
            xbx[3][n0]= x1[0];
            xbx[4][n0]= x1[1];
            xbx[5][n0]= x1[2];

            sbx[0][n0]= s0[0];
            sbx[1][n0]= s0[1];
            sbx[2][n0]= s1[0];
            sbx[3][n0]= s1[1];
          (*n)++;*/
        }
     }

      delete[] stk[0];
      delete[] stk[1];
      delete[] stk[2];
      delete[] stk[3];
  }

   void inters( cInterp3d *p0, cInterp3d *p1, cInterp3d *p2, Real *s0, Real *s1, Real *s2, inter3d_t *stat )
  {

      Real      *ds0,    *ds1, *ds2;
      Real      *y0,  *y1, *y2;
      Real      *dy0[2], *dy1[2], *dy2[2];
      Real       d=0;
      Real       res0[3],res1[3],res2[3];
      Real rhs[3]={0.,0.,0.};

      y0= stat->y0;
      dy0[0]= stat->dy0[0];
      dy0[1]= stat->dy0[1];
      ds0=    stat->ds0;
      y1= stat->y1;
      dy1[0]= stat->dy1[0];
      dy1[1]= stat->dy1[1];
      ds1=    stat->ds1;
      y2= stat->y2;
      dy2[0]= stat->dy2[0];
      dy2[1]= stat->dy2[1];
      ds2=    stat->ds2;
      
      for( Int it=0;it<10;it++ )
     {

         p0->interp( s0,y0,dy0[0],dy0[1] );
         p1->interp( s1,y1,dy1[0],dy1[1] );
         p2->interp( s2,y2,dy2[0],dy2[1] );

/*       cout << "tangent vectors\n";
         cout << "dy0 "<<dy0[0][0]<<" "<<dy0[1][0]<<" "<<p0<<"\n";
         cout << "dy0 "<<dy0[0][1]<<" "<<dy0[1][1]<<"\n";
         cout << "dy0 "<<dy0[0][2]<<" "<<dy0[1][2]<<"\n";
         cout << "dy1 "<<dy1[0][0]<<" "<<dy1[1][0]<<" "<<p1<<"\n";
         cout << "dy1 "<<dy1[0][1]<<" "<<dy1[1][1]<<"\n";
         cout << "dy1 "<<dy1[0][2]<<" "<<dy1[1][2]<<"\n";
         cout << "dy2 "<<dy2[0][0]<<" "<<dy2[1][0]<<" "<<p2<<"\n";
         cout << "dy2 "<<dy2[0][1]<<" "<<dy2[1][1]<<"\n";
         cout << "dy2 "<<dy2[0][2]<<" "<<dy2[1][2]<<"\n";*/

         pppint( y0, dy0[0], dy0[1], y1, dy1[0], dy1[1], y2, dy2[0], dy2[1], rhs, ds0,ds1,ds2, stat );
         if( fabs( stat->det ) < 1000*small ){ cout << "geo/3d/interp3d::inters d= 0\n";break; };
        
         s0[0]+= ds0[0];
         s0[1]+= ds0[1];

         s1[0]+= ds1[0];
         s1[1]+= ds1[1];

         s2[0]+= ds2[0];
         s2[1]+= ds2[1];

         sub3( y0,y1, res0 );
         sub3( y1,y2, res1 );
         stat->res= fmax( norminf3( res0 ),norminf3( res1 ) );
     }
//    cout << y0[0]<<" "<<y0[1]<<" "<<y1[2]<<"<==========================\n";
  }

   void inters( cInterp3d *p0, cInterp3d *p1, Real *p2, Real *n2, Real *s0, Real *s1, inter3d_t *stat )
  {
      Real      *ds0,    *ds1;
      Real      *y0,  *y1;
      Real      *dy0[2], *dy1[2];
      Real       d=0;
      Real       res0[3],res1[3];
      Real rhs[3]={0.,0.,0.};
      Real rlx;

      y0= stat->y0;
      dy0[0]= stat->dy0[0];
      dy0[1]= stat->dy0[1];
      ds0=    stat->ds0;
      y1= stat->y1;
      dy1[0]= stat->dy1[0];
      dy1[1]= stat->dy1[1];
      ds1=    stat->ds1;

      rlx= 0.5;
      for( Int it=0;it<10;it++ )
     {
         p0->interp( s0,y0,dy0[0],dy0[1] );
         p1->interp( s1,y1,dy1[0],dy1[1] );
         ppnint( y0, dy0[0], dy0[1], y1, dy1[0], dy1[1], p2, n2, rhs, ds0,ds1, stat );

         if( fabs( stat->det ) < small )
        { 
            cout << "d=0\n";break; 
        };
        
         s0[0]+= rlx*ds0[0];
         s0[1]+= rlx*ds0[1];

         s1[0]+= rlx*ds1[0];
         s1[1]+= rlx*ds1[1];

         rlx*= 1.1;
         rlx= min( 0.99,rlx );

         sub3( y0,y1, res0 );
         stat->res= norminf3( res0 );
     }
  }

   void cont0( cInterp3d *p0, cInterp3d *p1, Real *s0, Real *s1, Real l, inter3d_t *stat )
  {
      Real      *ds0, *ds1;
      Real      *y0,  *y1, *y2,y20[3];
      Real      *dy0[2], *dy1[2];
      Real       d=0;
      Real       res0[3],res1[3];
      Real      *q0[3],*q1[3];
      Real      *r0,*r1;
      Real       n2[4];
      Real       rhs[3]={0.,0.,0.};

      y0= stat->y0;
      dy0[0]= stat->dy0[0];
      dy0[1]= stat->dy0[1];
      ds0= stat->ds0;
      q0[0]=stat->q0[0];
      q0[1]=stat->q0[1];
      q0[2]=stat->q0[2];
      r0=   stat->r0;

      y1= stat->y1;
      dy1[0]= stat->dy1[0];
      dy1[1]= stat->dy1[1];
      ds1= stat->ds1;
      q1[0]=stat->q1[0];
      q1[1]=stat->q1[1];
      q1[2]=stat->q1[2];
      r1=   stat->r1;

      y2= stat->y2;

      vec3( q0[2],q1[2],n2 ); n2[3]= norm23( n2 ); sclv3( 1./n2[3],n2 );

      qrs23( q0[0],q0[1],r0, ds0, n2 );
      qrs23( q1[0],q1[1],r1, ds1, n2 );

      s0[0]+= l*ds0[0];
      s0[1]+= l*ds0[1];
      s1[0]+= l*ds1[0];
      s1[1]+= l*ds1[1];

      y20[0]= y2[0];
      y20[1]= y2[1];
      y20[2]= y2[2];

      for( Int it=0;it<15;it++ )
     {
         p0->interp( s0,y0,dy0[0],dy0[1] );
         p1->interp( s1,y1,dy1[0],dy1[1] );

         y2[0]= 0.5*( y0[0]+ y1[0] );
         y2[1]= 0.5*( y0[1]+ y1[1] );
         y2[2]= 0.5*( y0[2]+ y1[2] );
         sub3(y2,y20,n2); n2[3]= norm23( n2 ); 
         if( l < 0 ){ n2[3]= -n2[3]; };
         sclv3( 1./n2[3],n2 );
         rhs[2]= l;
         
         ppnint( y0,dy0[0],dy0[1], y1,dy1[0],dy1[1], y20,n2, rhs, ds0,ds1, stat );

         if( fabs( stat->det ) < small ){ cout << "d=0\n";break; };
        
         s0[0]+= ds0[0];
         s0[1]+= ds0[1];

         s1[0]+= ds1[0];
         s1[1]+= ds1[1];

         sub3( y0,y1, res0 );
         stat->res= norminf3( res0 );
//       cout << it << " "<< stat->res << "\n";
     }
  }
   cInterp3d::cInterp3d()
  {
//    fle.open( "SURF.dat" );
  }

   cInterp3d::~cInterp3d()
  {
//    fle.close();
  }
 
   void cInterp3d::orth( Real *dy0, Real *dy1, Real n[3][4] )
  {
      Real d0[4],d1[4];

      d0[0]= dy0[0];
      d0[1]= dy0[1];
      d0[2]= dy0[2];
      d0[3]= norm23( d0 ); scale( 3,d0,1./d0[3] );

      d1[0]= dy1[0];
      d1[1]= dy1[1];
      d1[2]= dy1[2];
      d1[3]= norm23( d1 ); scale( 3,d1,1./d1[3] );

      vec3( d0,    d1, n[2] ); n[2][3]= norm23( n[2] ); scale( 3,n[2],1./n[2][3] ); 
      vec3( d1,  n[2], n[0] ); n[0][3]= norm23( n[0] ); scale( 3,n[0],1./n[0][3] ); 
      vec3( n[2],  d0, n[1] ); n[1][3]= norm23( n[1] ); scale( 3,n[1],1./n[1][3] ); 

      n[0][3]= dot3( d0,n[0] ); n[0][3]*= d0[3];
      n[1][3]= dot3( d1,n[1] ); n[1][3]*= d1[3];

  }

   void cInterp3d::prjct( Real *v0, Real *v1, Real n[3][4] )
  {
      v1[0]= dot3( v0,n[0] );
      v1[1]= dot3( v0,n[1] );
 
      v1[0]/= n[0][3];
      v1[1]/= n[1][3];
  }

   void cInterp3d::prjct( Real *v0, Real *v1, Real *dy0, Real *dy1 )
  {
      Real r0,r1;
      Real a00,a01,a11;

      a00= dot3( dy0,dy0 );
      a11= dot3( dy1,dy1 );
      a01= dot3( dy0,dy1 );

      a00= sqrt( a00 );
      a01/= a00;
      a11= a11- a01*a01;

      r0=  dot3( dy0,v0 );
      r1=  dot3( dy1,v0 );

      v1[0]= r0/a00;
      v1[1]= r1- v1[0]*a01;
      v1[1]/= a11;
      v1[0]= v1[0]- v1[1]*a01;
      v1[0]/= a00;

  }


   void cInterp3d::discr( Real *s0, Real *s1, Int *n, Real *sbx[], Real *xbx[] )
  {
      Real y00[3],dy000[4],dy100[4];
      Real y01[3],dy001[4],dy101[4];
      Real y10[3],dy010[4],dy110[4];
      Real y11[3],dy011[4],dy111[4];
      Real x0[3],x1[3];

      Real s00[2],s01[2],s10[2],s11[2],s[2];
      Real d0,d;
      Real v0[3];

      s00[0]= s0[0]; s00[1]= s0[1];
      s01[0]= s0[0]; s01[1]= s1[1];
      s10[0]= s1[0]; s10[1]= s0[1];
      s11[0]= s1[0]; s11[1]= s1[1];

      interp( s00, y00,dy000,dy100 );
      interp( s10, y10,dy010,dy110 );
      interp( s01, y01,dy001,dy101 );
      interp( s11, y11,dy011,dy111 );

      dy000[3]= norm23( dy000 ); sclv3( 1./dy000[3],dy000 );
      dy001[3]= norm23( dy001 ); sclv3( 1./dy001[3],dy001 );
      dy010[3]= norm23( dy010 ); sclv3( 1./dy010[3],dy010 );
      dy011[3]= norm23( dy011 ); sclv3( 1./dy011[3],dy011 );

      dy100[3]= norm23( dy100 ); sclv3( 1./dy100[3],dy100 );
      dy101[3]= norm23( dy101 ); sclv3( 1./dy101[3],dy101 );
      dy110[3]= norm23( dy110 ); sclv3( 1./dy110[3],dy110 );
      dy111[3]= norm23( dy111 ); sclv3( 1./dy111[3],dy111 );

      vec3( dy000,dy001, v0 ); d0= norm23( v0 ); d= d0;
      vec3( dy010,dy011, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );
      vec3( dy000,dy010, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );
      vec3( dy001,dy011, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );

      vec3( dy100,dy101, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );
      vec3( dy110,dy111, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );
      vec3( dy100,dy110, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );
      vec3( dy101,dy111, v0 ); d0= norm23( v0 ); d= fmax( d,d0 );

      if( d > 0.12 )
     {
         s[0]= 0.5*( s0[0]+ s1[0] );
         s[1]= 0.5*( s0[1]+ s1[1] );

         discr( s0, s , n, sbx,xbx );
         discr( s , s1, n, sbx,xbx );
         discr( s01, s, n, sbx,xbx );
         discr( s, s10, n, sbx,xbx );
     }
      else
     {
         corners( y00, dy000, dy100, y10, dy010, dy110,
                  y01, dy001, dy101, y11, dy011, dy111, x0, x1 );
//       sbprint( x0, x1, &fle );

         Int n0;
         n0= *n;

         xbx[0][n0]= x0[0];
         xbx[1][n0]= x0[1];
         xbx[2][n0]= x0[2];
         xbx[3][n0]= x1[0];
         xbx[4][n0]= x1[1];
         xbx[5][n0]= x1[2];

         sbx[0][n0]= s0[0];
         sbx[1][n0]= s0[1];
         sbx[2][n0]= s1[0];
         sbx[3][n0]= s1[1];
       (*n)++;
     }
  }


   void cInterp3d::print()
  {
      Real s[2];
      Real y[3],dy0[3],dy1[3];
      Int i0,i1;
      for( i1=0;i1<20;i1++ )
     {
         s[1]= (Real)i1/19.;
         s[1]-= 0.5;
         cout << "\n";
         cout << "#\n";
         cout << "\n";
         for( i0=0;i0<20;i0++ )
        {
            s[0]= (Real)i0/19.;
            s[0]-= 0.5;
            interp( s,y,dy0,dy1 );
            cout << y[0]<<" "<<y[1]<<" "<<y[2]<<"\n";
        }
         cout << "\n";
     }

      for( i1=0;i1<20;i1++ )
     {
         s[0]= (Real)i1/19.;
         s[0]-= 0.5;
         cout << "\n";
         cout << "#\n";
         cout << "\n";
         for( i0=0;i0<20;i0++ )
        {
            s[1]= (Real)i0/19.;
            s[1]-= 0.5;
            interp( s,y,dy0,dy1 );
            cout << y[0]<<" "<<y[1]<<" "<<y[2]<<"\n";
        }
         cout << "\n";
     }
 
  };

   void cInterp3d::check( string fnme )
  {
      const int n=200;
      Real s[2];
      Real y[3],dy0[3],dy1[3];
      Int i0,i1;
      ofstream fle;
      fle.open( fnme.c_str() );
      fle << "VARIABLES = \"X\", \"Y\", \"Z\"\n";
      fle << "ZONE I="<<n<<", J="<<n<<", F=POINT"<<"\n";
      fle.setf( ios_base::scientific );
      fle.width( 15 );
      fle.precision( 9 );
      for( i1=0;i1<n;i1++ )
     {
         s[1]= (Real)i1/(Real)(n-1);
         for( i0=0;i0<n;i0++ )
        {
            s[0]= (Real)i0/(Real)(n-1);
            interp( s,y,dy0,dy1 );
            fle << y[0]<<" "<<y[1]<<" "<<y[2]<<"\n";
        }
     }
      fle.close();
 
  };

   void cInterp3d::check( string fnme, Real dth )
  {
      const int n0=400;
      const int n1=40;
      Real s[2];
      Real y[3],dy0[3],dy1[3];
      Int i0,i1;
      ofstream fle;
      fle.open( fnme.c_str() );
      fle << "VARIABLES = \"X\", \"Y\", \"Z\"\n";
      fle << "ZONE I="<<n0<<", J="<<n1<<", F=POINT"<<"\n";
      fle.setf( ios_base::scientific );
      fle.width( 15 );
      fle.precision( 9 );
      for( i1=0;i1<n1;i1++ )
     {
         s[1]= (Real)i1/(Real)(n1-1);
         for( i0=0;i0<n0;i0++ )
        {
            s[0]= (Real)i0/(Real)(n0-1);
            interp( s,y,dy0,dy1 );
            Real tmp= y[1];
            y[1]= tmp*cos(dth)+ y[2]*sin(dth);
            y[2]=-tmp*sin(dth)+ y[2]*cos(dth);
            fle << y[0]<<" "<<y[1]<<" "<<y[2]<<"\n";
        }
     }
      fle.close();
 
  };

   void cInterp3d::gsrc( Real *y, Real *v0, Real *v1, Real *d )
  {
      Real x[3],dx0[3],dx1[3];
      Real q0[3],q1[3],r[3];
      interp( y,x,dx0,dx1 );
      qrf23( dx0,dx1, q0,q1,r );
      r[2]= 1./r[2];
      r[0]= 1./r[0];
      r[1]*= -r[2];

      v0[0]= r[0];
      v0[1]= 0;

      v1[0]= r[1];
      v1[1]= r[2];

     *d= big;
  }

/* void cInterp3d::gsrc( Real *y, Real *v0, Real *v1, Real *d )
  {
      Real y1[2],y2[2];
      Real x1[3],dx1[2][3];
      Real x2[3],dx2[2][3];
      Real eps= 1.e-4;
      Real x[3],dx[2][3];
      Real q[3][3],r[3],z[3];
      Real t[2][2];
      Real e[2];

      interp( y,x,dx[0],dx[1] );
      qrf23( dx[0],dx[1], q[0],q[1],r );

      vec3( q[0],q[1],q[2] );
            
      y1[0]= y[0]+eps;
      y1[1]= y[1];

      y2[0]= y[0];
      y2[1]= y[1]+eps;

      interp( y1,x1,dx1[0],dx1[1] );
      interp( y2,x2,dx2[0],dx2[1] );

      z[0]=       dot3( dx1[0],q[2] );
      z[1]= 0.5*( dot3( dx1[1],q[2] )+ dot3( dx2[0],q[2] ) );
      z[2]=       dot3( dx2[1],q[2] );

      y1[0]= y[0]-eps;
      y1[1]= y[1];

      y2[0]= y[0];
      y2[1]= y[1]-eps;

      interp( y1,x1,dx1[0],dx1[1] );
      interp( y2,x2,dx2[0],dx2[1] );

      z[0]-=       dot3( dx1[0],q[2] );
      z[1]-= 0.5*( dot3( dx1[1],q[2] )+ dot3( dx2[0],q[2] ) );
      z[2]-=       dot3( dx2[1],q[2] );

      z[0]/= (2*eps);
      z[1]/= (2*eps);
      z[2]/= (2*eps);


      sye2( z, t[0],t[1], e );
      e[0]= fmax( fabs( e[0] ), 1. );
      e[1]= fmax( fabs( e[1] ), 1. );

      sclv2( 1./e[0],t[0] );
      sclv2( 1./e[1],t[1] );

      t[0][1]/= r[2];
      t[0][0]-= r[1]*t[0][1];
      t[0][0]/= r[0];

      t[1][1]/= r[2];
      t[1][0]-= r[1]*t[1][1];
      t[1][0]/= r[0];

      idv2( t[0],v0 );
      idv2( t[1],v1 );
     *d= big;
  }*/

/* void cInterp3d::gsrc( Real *y, Real *v0, Real *v1, Real *d )
  {
      Real y1[2],y2[2];
      Real x1[3],dx1[2][3];
      Real x2[3],dx2[2][3];
      Real eps= 1.e-2;
      Real x[3],dx[2][3];
      Real q[3][3],r[3],z[3];
      Real t[2][2],e[2];

      interp( y,x,dx[0],dx[1] );
      qrf23( dx[0],dx[1], q[0],q[1],r );

      vec3( q[0],q[1],q[2] );
            
      y1[0]= y[0]+eps;
      y1[1]= y[1];

      y2[0]= y[0];
      y2[1]= y[1]+eps;

      interp( y1,x1,dx1[0],dx1[1] );
      interp( y2,x2,dx2[0],dx2[1] );

      z[0]=       dot3( dx1[0],q[2] );
      z[1]= 0.5*( dot3( dx1[1],q[2] )+ dot3( dx2[0],q[2] ) );
      z[2]=       dot3( dx2[1],q[2] );

      y1[0]= y[0]-eps;
      y1[1]= y[1];

      y2[0]= y[0];
      y2[1]= y[1]-eps;

      interp( y1,x1,dx1[0],dx1[1] );
      interp( y2,x2,dx2[0],dx2[1] );

      z[0]-=       dot3( dx1[0],q[2] );
      z[1]-= 0.5*( dot3( dx1[1],q[2] )+ dot3( dx2[0],q[2] ) );
      z[2]-=       dot3( dx2[1],q[2] );

      z[0]/= (2*eps);
      z[1]/= (2*eps);
      z[2]/= (2*eps);

      sye2( z, t[0],t[1], e );
      e[0]= fmax( fabs( e[0] ), 1. );

      r[0]*= e[0];
      r[1]*= e[0];
      r[2]*= e[0];

      r[2]= 1./r[2];
      r[0]= 1./r[0];
      r[1]*= -r[2];

      v0[0]= r[0];
      v0[1]= 0;

      v1[0]= r[1];
      v1[1]= r[2];

     *d= big;
  }*/

   void cInterp3d::tria( Real *ymin, Real *ymax, Real dinf, Int *np, Real *y[], Real *x[], Int *ne, Int *iep[], 
                         Int *nb, Int *ibp[], Real *sbp[]  )
  {

      cout << "interp3d tria quarantined\n";
      assert(false);
/*    cMeshCtrl         *mco;
      Real               y0[2],y1[2],y2[2],y3[2];
      Real               x0[3],dx[2][3];
      Real               p0[3],l0[3],m0[3];
      Real              *tmp[3];
      Int               *itmp[2];
      Int               *isp[2];
      Int                ntmp,ns,ip;
      Int                ns0;
      ofstream           fle;

      y0[0]= ymin[0]; y0[1]= ymin[1];
      y1[0]= ymax[0]; y1[1]= ymin[1];
      y2[0]= ymax[0]; y2[1]= ymax[1];
      y3[0]= ymin[0]; y3[1]= ymax[1];

      tmp[0]= new Real[10000];
      tmp[1]= new Real[10000];
      tmp[2]= new Real[10000];
      y[0]= new Real[10000];
      y[1]= new Real[10000];
      x[0]= new Real[10000];
      x[1]= new Real[10000];
      x[2]= new Real[10000];
      isp[0]= new Int[10000];
      isp[1]= new Int[10000];
      iep[0]= new Int[10000];
      iep[1]= new Int[10000];
      iep[2]= new Int[10000];
      ibp[0]= new Int[10000];
      ibp[1]= new Int[10000];
      ibp[2]= new Int[10000];
      ibp[3]= new Int[10000];
      sbp[0]= new Real[10000];
      sbp[1]= new Real[10000];

      itmp[0]= new Int[10000];
      itmp[1]= new Int[10000];

      mco= new cMeshCtrl();
      mco->setdefault( dinf );
      mco->background( this );
     *np=0;
      ns=0;
     *ne=0;

      ntmp=0;
      mesh1d( &ntmp,tmp[2],tmp, y0,y1, mco );
      ns0=ns;
      addtofront( 0,ntmp, tmp,  np,&ns, y,isp, itmp, false );
      for( Int i=ns0;i<ns;i++ )
     {
         ibp[0][i]= isp[0][i];
         ibp[1][i]= isp[1][i];
         ibp[2][i]=-1;
         ibp[3][i]= 0;
         sbp[0][i]= tmp[2][itmp[0][i]];
         sbp[1][i]= tmp[2][itmp[1][i]];
     }

      ntmp=0;
      mesh1d( &ntmp,tmp[2],tmp, y1,y2, mco );
      ns0=ns;
      addtofront( 0,ntmp, tmp,  np,&ns, y,isp, itmp, false );
      for( Int i=ns0;i<ns;i++ )
     {
         ibp[0][i]= isp[0][i];
         ibp[1][i]= isp[1][i];
         ibp[2][i]=-2;
         ibp[3][i]= 0;
         sbp[0][i]= tmp[2][itmp[0][i]];
         sbp[1][i]= tmp[2][itmp[1][i]];
     }

      ntmp=0;
      mesh1d( &ntmp,tmp[2],tmp, y2,y3, mco );
      ns0=ns;
      addtofront( 0,ntmp, tmp,  np,&ns, y,isp, itmp, false );
      for( Int i=ns0;i<ns;i++ )
     {
         ibp[0][i]= isp[0][i];
         ibp[1][i]= isp[1][i];
         ibp[2][i]=-3;
         ibp[3][i]= 0;
         sbp[0][i]= tmp[2][itmp[0][i]];
         sbp[1][i]= tmp[2][itmp[1][i]];
     }

      ntmp=0;
      mesh1d( &ntmp,tmp[2],tmp, y3,y0, mco );
      ns0=ns;
      addtofront( 0,ntmp, tmp,  np,&ns, y,isp, itmp, false );
      for( Int i=ns0;i<ns;i++ )
     {
         ibp[0][i]= isp[0][i];
         ibp[1][i]= isp[1][i];
         ibp[2][i]=-4;
         ibp[3][i]= 0;
         sbp[0][i]= tmp[2][itmp[0][i]];
         sbp[1][i]= tmp[2][itmp[1][i]];
     }
    
     *nb= ns;

      cCDT *del= new cCDT();
      for( ip=0;ip<*np;ip++ )
     {
         y[0][ip]*= mt0[0];
         y[1][ip]*= mt1[1];
     }
      del->mesh( ns,isp,  *np,y, ne,iep );
      del->refinebytri( ns,isp,  np,y, ne,iep, mco );
      delete del;
      
      for( ip=0;ip<*np;ip++ )
     {
         y[0][ip]/=mt0[0];
         y[1][ip]/=mt1[1];
         y0[0]= y[0][ip];
         y0[1]= y[1][ip];
         interp( y0, x0,dx[0],dx[1] );
         x[0][ip]= x0[0];
         x[1][ip]= x0[1];
         x[2][ip]= x0[2];
     }

      delete mco;

      delete[] tmp[0];
      delete[] tmp[1];
      delete[] isp[0];
      delete[] isp[1];*/
     
  }

   bool cInterp3d::refine( Real *y0, Real *y1, Real *y2, Real dinf, Real *yg )
  {
      bool val;
      Real y[2];
      Real x[3],dx[2][3];
      Real x0[3],dx0[2][3];
      Real x1[3],dx1[2][3];
      Real x2[3],dx2[2][3];
      Real x3[3];
      Real l0[4];
      Real l1[4];
      Real l2[4];
      Real l3[4];
      Real q0[3];
      Real q1[3];
      Real r[3];
      Real yl[3];
      Real b[3];
      Real l;
      Real z0[2];
      Real z1[2];
      Real z2[2];

      idv2( y0,z0 ); z0[0]/= mt0[0]; z0[1]/= mt1[1];
      idv2( y1,z1 ); z1[0]/= mt0[0]; z1[1]/= mt1[1];
      idv2( y2,z2 ); z2[0]/= mt0[0]; z2[1]/= mt1[1];

      interp( z0,x0,dx0[0],dx0[1] );
      interp( z1,x1,dx1[0],dx1[1] );
      interp( z2,x2,dx2[0],dx2[1] );

      y[0]= ( z0[0]+ z1[0]+ z2[0] )/3;
      y[1]= ( z0[1]+ z1[1]+ z2[1] )/3;
      interp( y,x,dx[0],dx[1] );

      x3[0]= ( x0[0]+ x1[0]+ x2[0] )/3;
      x3[1]= ( x0[1]+ x1[1]+ x2[1] )/3;
      x3[2]= ( x0[2]+ x1[2]+ x2[2] )/3;


      sub3( x1,x0, l2 ); l2[3]= norm23( l2 );
      sub3( x0,x2, l1 ); l1[3]= norm23( l1 );
      sub3( x2,x1, l0 ); l0[3]= norm23( l0 );

      sub3( x3,x,  l3 ); l3[3]= norm23( l3 );

      l= ( l0[3]+ l1[3]+ l2[3] )/3;

// void cInterp3d::gsrc( Real *y, Real *v0, Real *v1, Real *d )

      val= l3[3] > 0.3*l || l > 1.8*dinf ;
//    val= l > dinf;

      if( val )
     {

         yg[0]= y[0];
         yg[1]= y[1];
     }

      return val;

  }

   bool cInterp3d::refine( Real *y0, Real *y1, Real dinf, Real *yg )
  {
      bool val;
      Real y[2];
      Real x[3],dx[2][3];
      Real x0[3],dx0[2][3];
      Real x1[3],dx1[2][3];
      Real x2[3],dx2[2][3];
      Real x3[3];
      Real l0[4];
      Real l1[4];
      Real l2[4];
      Real l3[4];
      Real q0[3];
      Real q1[3];
      Real r[3];
      Real yl[3];
      Real b[3];
      Real l;
      Real z0[2];
      Real z1[2];

      idv2( y0,z0 ); z0[0]/= mt0[0]; z0[1]/= mt1[1];
      idv2( y1,z1 ); z1[0]/= mt0[0]; z1[1]/= mt1[1];

      interp( z0,x0,dx0[0],dx0[1] );
      interp( z1,x1,dx1[0],dx1[1] );

      y[0]= ( z0[0]+ z1[0] )/2;
      y[1]= ( z0[1]+ z1[1] )/2;
      interp( y,x,dx[0],dx[1] );


      x3[0]= ( x0[0]+ x1[0] )/2;
      x3[1]= ( x0[1]+ x1[1] )/2;


      sub3( x1,x0, l2 ); l2[3]= norm23( l2 );
      sub3( x3,x,  l3 ); l3[3]= norm23( l3 );

      val= l3[3] > 0.1*l2[3] || l2[3] > 1.8*dinf ;

      if( val )
     {
         yg[0]= y[0];
         yg[1]= y[1];
     }

      return val;

  }

   void cInterp3d::metrics( Real *v0, Real *v1 )
  {
      idv2( mt0,v0 );
      idv2( mt1,v1 );
  }

   void inters( cInterp3d *p0, Real *p2, Real *n2, Real *s0, Real *s1, inter3d_t *stat )
  {
      Real      *ds0,    *ds1;
      Real      *y0,  *y1;
      Real      *dy0[2], *dy1[2];
      Real       d=0;
      Real       res0[3],res1[3];
      Real rhs[3]={0.,0.,0.};
      Real rlx;

      y0= stat->y0;
      dy0[0]= stat->dy0[0];
      dy0[1]= stat->dy0[1];
      ds0=    stat->ds0;
      y1= stat->y1;
      dy1[0]= stat->dy1[0];
      dy1[1]= stat->dy1[1];
      ds1=    stat->ds1;

      rlx= 0.5;
      for( Int it=0;it<20;it++ )
     {
         p0->interp( s0,y0,dy0[0],dy0[1] );
         y1[0]= p2[0]+ (*s1)*n2[0];
         y1[1]= p2[1]+ (*s1)*n2[1];
         y1[2]= p2[2]+ (*s1)*n2[2];
         p1l1int( y0, dy0[0], dy0[1], y1,n2, ds0,ds1, stat );

         if( fabs( stat->det ) < small )
        { 
            cout << "d=0\n";break; 
        };
        
         s0[0]+= rlx*ds0[0];
         s0[1]+= rlx*ds0[1];

       (*s1)+= rlx*ds1[0];

         rlx*= 1.1;
         rlx= min( 0.99,rlx );

         sub3( y0,y1, res0 );
         stat->res= norminf3( res0 );
//       cout << y0[0]<<" "<<y0[1]<<" "<<y0[2]<<" "<<y1[0]<<" "<<y1[1]<<" "<<y1[2]<<" <============ \n";
/*       cout << s0[0]<<" "<<s0[1]<<" "<<*s1<<" <============ ";
         cout << "newton iteration "<<it<<" "<<rlx<<" "<<stat->res<<"\n";*/
     }
  }

   void cInterp3d::triangulate( tri3d_t **var )
  {
      string          sn;

      Int              np,ne;
      Int              i,j,k;
      Int            *iep[3];
      Real           *y[2];
      Real            y0[2];
      Real            x0[2],dx0[2][3];
      Real            w0,w1;

      Int           *ib0[2];
      Int           *ib1[2];
      Int           *ib2[2];
      Int           *ib3[2];

      Int             m0;
      Int             m1;


      if( mt0[0] > mt1[1] )
     {
         w0= mt0[0]/mt1[1];
         m1= 10;
         m0= ((Int)w0+1)*m1;
     }
      else
     {
         w1= mt1[1]/mt0[0];
         m0= 10;
         m1= ((Int)w1+1)*m0;
     }

      ib0[0]= new Int[m0];
      ib0[1]= new Int[m0];

      ib2[0]= new Int[m0];
      ib2[1]= new Int[m0];

      ib1[0]= new Int[m1];
      ib1[1]= new Int[m1];

      ib3[0]= new Int[m1];
      ib3[1]= new Int[m1];

      iep[0]= new Int[2*m0*m1];
      iep[1]= new Int[2*m0*m1];
      iep[2]= new Int[2*m0*m1];
      y[0]=  new Real[(m0+1)*(m1+1)];
      y[1]=  new Real[(m0+1)*(m1+1)];
         
      k=0;
      for( j=0;j<m1+1;j++ )
     {
         for( i=0;i<m0+1;i++ )
        {
            w1= j; 
            w1/= m1;
            w0= i; 
            w0/= m0;
            y[0][k]= w0;
            y[1][k]= w1;
            k++;
        }
     }
      np= k;
      k=0;
      for( j=0;j<m1;j++ )
     {
         for( i=0;i<m0;i++ )
        {
            iep[0][k]=     i+(m0+1)*j;
            iep[1][k]=   1+iep[0][k];
            iep[2][k]= m0+1+iep[1][k];
            k++;
            iep[0][k]=     i+(m0+1)*j;
            iep[1][k]= m0+1+iep[0][k]+1;
            iep[2][k]= m0+1+iep[0][k];
            k++;
        }
     }
      ne= k;

      for( i=0;i<m0;i++ )
     {
         ib0[0][i]=   i;
         ib0[1][i]= i+1;
         ib2[0][i]= (m0+1)*m1+i+1;
         ib2[1][i]= (m0+1)*m1+i  ;
     }
      for( i=0;i<m1;i++ )
     {
         ib1[0][i]= (m0+1)* i   +m0;
         ib1[1][i]= (m0+1)*(i+1)+m0;
         ib3[0][i]= (m0+1)*(i+1);
         ib3[1][i]= (m0+1)* i;
     }

      assert( !(*var) );
    (*var)= new tri3d_t;
      ::init( *var );
      ::add( np,y,ne,iep,this, *var );
      ::build0( *var );
      ::normals( *var );
      ::boundary( m0,ib0,"SIDE0",*var );
      ::boundary( m1,ib1,"SIDE1",*var );
      ::boundary( m0,ib2,"SIDE2",*var );
      ::boundary( m1,ib3,"SIDE3",*var );

      delete[] iep[0]; iep[0]= NULL;
      delete[] iep[1]; iep[1]= NULL;
      delete[] iep[2]; iep[2]= NULL;
      delete[] y[0]; y[0]= NULL;
      delete[] y[1]; y[1]= NULL;
      delete[] ib0[0]; ib0[0]= NULL;
      delete[] ib0[1]; ib0[1]= NULL;
      delete[] ib1[0]; ib1[0]= NULL;
      delete[] ib1[1]; ib1[1]= NULL;
      delete[] ib2[0]; ib2[0]= NULL;
      delete[] ib2[1]; ib2[1]= NULL;
      delete[] ib3[0]; ib3[0]= NULL;
      delete[] ib3[1]; ib3[1]= NULL;
  }
