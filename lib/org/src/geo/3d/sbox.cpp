   using namespace std;

#  include <fstream>
#  include <geo/3d/sbox.h>

   void sbprint( Real x0[3], Real x1[3], ofstream *fle )
  {

     
     *fle << "\n"; 
     *fle << "# BOX \n"; 
     *fle << "\n"; 
     *fle << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n";
     *fle << x1[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n";
     *fle << x1[0]<<" "<<x1[1]<<" "<<x0[2]<<"\n";
     *fle << x0[0]<<" "<<x1[1]<<" "<<x0[2]<<"\n";
     *fle << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n";
     *fle << "\n"; 
     *fle << "#\n"; 
     *fle << "\n"; 
     *fle << x0[0]<<" "<<x0[1]<<" "<<x1[2]<<"\n";
     *fle << x1[0]<<" "<<x0[1]<<" "<<x1[2]<<"\n";
     *fle << x1[0]<<" "<<x1[1]<<" "<<x1[2]<<"\n";
     *fle << x0[0]<<" "<<x1[1]<<" "<<x1[2]<<"\n";
     *fle << x0[0]<<" "<<x0[1]<<" "<<x1[2]<<"\n";
     *fle << "\n"; 
     *fle << "#\n"; 
     *fle << "\n"; 

     *fle << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n";
     *fle << x0[0]<<" "<<x0[1]<<" "<<x1[2]<<"\n";
     *fle << "\n"; 
     *fle << "#\n"; 
     *fle << "\n"; 

     *fle << x1[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n";
     *fle << x1[0]<<" "<<x0[1]<<" "<<x1[2]<<"\n";
     *fle << "\n"; 
     *fle << "#\n"; 
     *fle << "\n"; 

     *fle << x1[0]<<" "<<x1[1]<<" "<<x0[2]<<"\n";
     *fle << x1[0]<<" "<<x1[1]<<" "<<x1[2]<<"\n";
     *fle << "\n"; 
     *fle << "#\n"; 
     *fle << "\n"; 

     *fle << x0[0]<<" "<<x1[1]<<" "<<x0[2]<<"\n";
     *fle << x0[0]<<" "<<x1[1]<<" "<<x1[2]<<"\n";
  }

 
   void corners( Real y00[3], Real dy000[3], Real dy100[3],
                 Real y10[3], Real dy010[3], Real dy110[3],
                 Real y01[3], Real dy001[3], Real dy101[3],
                 Real y11[3], Real dy011[3], Real dy111[3],
                 Real  x0[3], Real x1[3] )
  {

      x0[0]= big;
      x0[1]= big;
      x0[2]= big;

      x1[0]= -big;
      x1[1]= -big;
      x1[2]= -big;
 
      x0[0]= fmin( y00[0],x0[0] );
      x0[0]= fmin( y01[0],x0[0] );
      x0[0]= fmin( y10[0],x0[0] );
      x0[0]= fmin( y11[0],x0[0] );

      x0[1]= fmin( y00[1],x0[1] );
      x0[1]= fmin( y01[1],x0[1] );
      x0[1]= fmin( y10[1],x0[1] );
      x0[1]= fmin( y11[1],x0[1] );

      x0[2]= fmin( y00[2],x0[2] );
      x0[2]= fmin( y01[2],x0[2] );
      x0[2]= fmin( y10[2],x0[2] );
      x0[2]= fmin( y11[2],x0[2] );


      x1[0]= fmax( y00[0],x1[0] );
      x1[0]= fmax( y01[0],x1[0] );
      x1[0]= fmax( y10[0],x1[0] );
      x1[0]= fmax( y11[0],x1[0] );

      x1[1]= fmax( y00[1],x1[1] );
      x1[1]= fmax( y01[1],x1[1] );
      x1[1]= fmax( y10[1],x1[1] );
      x1[1]= fmax( y11[1],x1[1] );

      x1[2]= fmax( y00[2],x1[2] );
      x1[2]= fmax( y01[2],x1[2] );
      x1[2]= fmax( y10[2],x1[2] );
      x1[2]= fmax( y11[2],x1[2] );

   }
