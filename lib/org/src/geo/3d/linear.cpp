   using namespace std;

#  include <iostream>
#  include <cstdlib>
#  include <geo/2d/linear.h>
#  include <geo/3d/linear.h>

   void plint( Real *p0, Real *p1, Real *p2, Real *q0, Real *q1, Real *s, Real *t, inter3d_t *stat )
  {

      Real v0[3],v1[3],v2[3],v3[3],v4[3];
      Real a0[4],a1[4],a2[4];
      Real det;
      Int  ipiv[3];
      sub3( p1,p0, v0 ); 
      sub3( p2,p0, v1 ); 
      sub3( q1,q0, v2 ); sclv3( -1.,v2 );

      luf3( v0,v1,v2,a0,a1,a2,ipiv,&det );
      stat->det= det;
      if( fabs( det ) > small )
     {

         sub3( q0,p0, v4 );
         lus3( a0,a1,a2,ipiv,v3,v4 );

         s[1]= v3[0];
         s[2]= v3[1];
         t[1]= v3[2];
         t[0]= 1.-t[1];
         s[0]= 1.-( s[1]+ s[2] );

/*       cout << "points \n";
         cout << s[0]*p0[0]+ s[1]*p1[0]+ s[2]*p2[0] <<" "<<t[0]*q0[0]+ t[1]*q1[0]<<"\n";
         cout << s[0]*p0[1]+ s[1]*p1[1]+ s[2]*p2[1] <<" "<<t[0]*q0[1]+ t[1]*q1[1]<<"\n";
         cout << s[0]*p0[2]+ s[1]*p1[2]+ s[2]*p2[2] <<" "<<t[0]*q0[2]+ t[1]*q1[2]<<"\n";
         cout << "determinant "<<det<<"\n";*/
     }
  }

   void p1l1int( Real *p0, Real *v0, Real *v1, Real *q0, Real *v2, Real *s, Real *t, inter3d_t *stat )
  {

      Real v3[3],b[3],x[3];
      Real a0[4],a1[4],a2[4];
      Real det;
      Int  ipiv[3];
/*    sub3( p1,p0, v0 ); 
      sub3( p2,p0, v1 ); 
      sub3( q1,q0, v2 ); sclv3( -1.,v2 );*/

      idv3( v2,v3 ); sclv3( -1.,v3 );

      luf3( v0,v1,v3,a0,a1,a2,ipiv,&det );
      stat->det= det;
      if( fabs( det ) > small )
     {

         sub3( q0,p0, b );
         lus3( a0,a1,a2,ipiv,x,b );

         s[0]= x[0];
         s[1]= x[1];
        *t   = x[2];

/*       cout << "points \n";
         cout << p0[0]+ s[0]*v0[0]+ s[1]*v1[0]<<" "<<(*t)*v2[0]+ q0[0]<<"\n";
         cout << p0[1]+ s[0]*v0[1]+ s[1]*v1[1]<<" "<<(*t)*v2[1]+ q0[1]<<"\n";
         cout << p0[2]+ s[0]*v0[2]+ s[1]*v1[2]<<" "<<(*t)*v2[2]+ q0[2]<<"\n";
         cout << "determinant "<<det<<"\n";*/
     }
  }

   void pppint( Real *p0, Real *l0, Real *m0, 
                Real *p1, Real *l1, Real *m1, 
                Real *p2, Real *l2, Real *m2, 
                Real *rhs,
                Real *s0, Real *s1, Real *s2,
                inter3d_t *stat )
  {

      Real *q0[3],*q1[3],*q2[3];
      Real *r0,   *r1,   *r2;

      Real x[3],b[3];
      Real det;

      Real *a0,*a1,*a2;
      Int  *ipiv;

      q0[0]= stat->q0[0];
      q0[1]= stat->q0[1];
      q0[2]= stat->q0[2];
      r0   = stat->r0;
      q1[0]= stat->q1[0];
      q1[1]= stat->q1[1];
      q1[2]= stat->q1[2];
      r1   = stat->r1;
      q2[0]= stat->q2[0];
      q2[1]= stat->q2[1];
      q2[2]= stat->q2[2];
      r2   = stat->r2;

      a0   = stat->a[0];
      a1   = stat->a[1];
      a2   = stat->a[2];
      ipiv = stat->ipiv;

      qrf23( l0,m0, q0[0],q0[1],r0 ); vec3( q0[0],q0[1], q0[2] );
      qrf23( l1,m1, q1[0],q1[1],r1 ); vec3( q1[0],q1[1], q1[2] );
      qrf23( l2,m2, q2[0],q2[1],r2 ); vec3( q2[0],q2[1], q2[2] );

/*    cout << "pppint\n";
      cout << "q0 "<<q0[2][0]<<" "<<q0[2][1]<<" "<<q0[2][2]<<"\n";
      cout << "q1 "<<q1[2][0]<<" "<<q1[2][1]<<" "<<q1[2][2]<<"\n";
      cout << "q2 "<<q2[2][0]<<" "<<q2[2][1]<<" "<<q2[2][2]<<"\n";*/

      luf3( q0[2],q1[2],q2[2],a0,a1,a2,ipiv,&det );
      stat->det= det;

//    cout << "det "<<det<<"\n";
      if( fabs( det ) > small )
     {
         b[0]= rhs[0]+dot3( p0,q0[2] );
         b[1]= rhs[1]+dot3( p1,q1[2] );
         b[2]= rhs[2]+dot3( p2,q2[2] );
         lus3t( a0,a1,a2,ipiv,x,b );

/*       cout << "plane check (pppint)\n";
         cout << "plane 0 "<<(x[0]-p0[0])*q0[2][0]+ (x[1]-p0[1])*q0[2][1]+ (x[2]-p0[2])*q0[2][2]<<"\n";
         cout << "plane 1 "<<(x[0]-p1[0])*q1[2][0]+ (x[1]-p1[1])*q1[2][1]+ (x[2]-p1[2])*q1[2][2]<<"\n";
         cout << "plane 2 "<<(x[0]-p2[0])*q2[2][0]+ (x[1]-p2[1])*q2[2][1]+ (x[2]-p2[2])*q2[2][2]<<"\n";*/
 
         sub3( x,p0, b );
         qrs23( q0[0],q0[1],r0, s0, b );

         sub3( x,p1, b );
         qrs23( q1[0],q1[1],r1, s1, b );

         sub3( x,p2, b );
         qrs23( q2[0],q2[1],r2, s2, b );

     }

  }

   void ppnint( Real *p0, Real *l0, Real *m0, 
                Real *p1, Real *l1, Real *m1, 
                Real *p2, Real *n2, 
                Real *rhs,
                Real *s0, Real *s1, 
                inter3d_t *stat )
  {

      Real *q0[3],*q1[3];
      Real *r0,*r1;

      Real *a0,*a1,*a2;
      Int  *ipiv;
      Real x[3],b[3];
      Real det;

      q0[0]= stat->q0[0];
      q0[1]= stat->q0[1];
      q0[2]= stat->q0[2];
      r0   = stat->r0;
      q1[0]= stat->q1[0];
      q1[1]= stat->q1[1];
      q1[2]= stat->q1[2];
      r1   = stat->r1;
      a0   = stat->a[0];
      a1   = stat->a[1];
      a2   = stat->a[2];
      ipiv = stat->ipiv;

      qrf23( l0,m0, q0[0],q0[1],r0 ); vec3( q0[0],q0[1], q0[2] );
      qrf23( l1,m1, q1[0],q1[1],r1 ); vec3( q1[0],q1[1], q1[2] );

      luf3( q0[2],q1[2],n2,a0,a1,a2,ipiv,&det );
      stat->det= det;
      if( fabs( stat->det ) > small )
     {
//       cout << "rhs in pppn is "<<rhs[0]<<"\n";
         b[0]= rhs[0]+dot3( p0,q0[2] );
         b[1]= rhs[1]+dot3( p1,q1[2] );
         b[2]= rhs[2]+dot3( p2,n2    );
         lus3t( a0,a1,a2,ipiv,x,b );

/*       cout << "lus3t check \n";
         cout << "plane check (pppnint)\n";
         cout << "plane 0 "<<rhs[0]<<" "<<(x[0]-p0[0])*q0[2][0]+ (x[1]-p0[1])*q0[2][1]+ (x[2]-p0[2])*q0[2][2]<<"\n";
         cout << "plane 1 "<<rhs[1]<<" "<<(x[0]-p1[0])*q1[2][0]+ (x[1]-p1[1])*q1[2][1]+ (x[2]-p1[2])*q1[2][2]<<"\n";
         cout << "plane 2 "<<rhs[2]<<" "<<(x[0]-p2[0])*n2[0]+ (x[1]-p2[1])*n2[1]+ (x[2]-p2[2])*n2[2]<<"\n";*/
 
         sub3( x,p0, b );
         qrs23( q0[0],q0[1],r0, s0, b );

         sub3( x,p1, b );
         qrs23( q1[0],q1[1],r1, s1, b );

     }
  }

   void nnnint( Real *p0, Real *n0,
                Real *p1, Real *n1, 
                Real *p2, Real *n2, 
                Real *rhs,
                Real *x, 
                inter3d_t *stat )
  {

      Real a0[4],a1[4],a2[4];
      Real b[3];
      Int  ipiv[3];

      Real det;

      luf3( n0,n1,n2,a0,a1,a2,ipiv,&det );
      stat->det= det;

      if( fabs( det ) > small )
     {
         b[0]= rhs[0]+dot3( p0,n0 );
         b[1]= rhs[1]+dot3( p1,n1 );
         b[2]= rhs[2]+dot3( p2,n2 );
         lus3t( a0,a1,a2,ipiv,x,b );
 
     }

  }

   void ppint( Real *p0, Real *l0, Real *m0, 
               Real *p1, Real *l1, Real *m1, 
               inter3d_t *stat )
  {

      Real *q0[3],*q1[3];
      Real *r0,*r1;

      Real a0[4],a1[4],a2[4];
      Real *x,b[3];
      Real *n2;
      Real det;
      Int  ipiv[3];

      q0[0]= stat->q0[0];
      q0[1]= stat->q0[1];
      q0[2]= stat->q0[2];
      r0   = stat->r0;
      q1[0]= stat->q1[0];
      q1[1]= stat->q1[1];
      q1[2]= stat->q1[2];
      r1   = stat->r1;

      n2=    stat->q2[2];
      x=     stat->y2;

/*    cout << "vectors\n";
      cout << l0[0]<<" "<<m0[0]<<" "<<p0[0]<<"\n";
      cout << l0[1]<<" "<<m0[1]<<" "<<p0[1]<<"\n";
      cout << l0[2]<<" "<<m0[2]<<" "<<p0[2]<<"\n";

      cout << l1[0]<<" "<<m1[0]<<" "<<p1[0]<<"\n";
      cout << l1[1]<<" "<<m1[1]<<" "<<p1[1]<<"\n";
      cout << l1[2]<<" "<<m1[2]<<" "<<p1[2]<<"\n";*/

      qrf23( l0,m0, q0[0],q0[1],r0 ); vec3( q0[0],q0[1], q0[2] );
      qrf23( l1,m1, q1[0],q1[1],r1 ); vec3( q1[0],q1[1], q1[2] );

/*    cout << "qr\n";
      cout << q0[0][0]<<" "<<q0[1][0]<<" "<<q0[2][0]<<"\n";
      cout << q0[0][1]<<" "<<q0[1][1]<<" "<<q0[2][1]<<"\n";
      cout << q0[0][2]<<" "<<q0[1][2]<<" "<<q0[2][2]<<"\n";

      cout << q1[0][0]<<" "<<q1[1][0]<<" "<<q1[2][0]<<"\n";
      cout << q1[0][1]<<" "<<q1[1][1]<<" "<<q1[2][1]<<"\n";
      cout << q1[0][2]<<" "<<q1[1][2]<<" "<<q1[2][2]<<"\n";*/

      vec3( q0[2],q1[2], n2 );
/*    cout << n2[0]<<"\n";
      cout << n2[1]<<"\n";
      cout << n2[2]<<"\n";*/

      luf3( q0[2],q1[2],n2,a0,a1,a2,ipiv,&det );
      stat->det= det;
      if( fabs( stat->det ) > small )
     {
         b[0]= dot3( p0,q0[2] );
         b[1]= dot3( p1,q1[2] );
         b[2]= dot3( p0,n2    );

         lus3t( a0,a1,a2,ipiv,x,b );

     }
  }


   void ttint( Real *p00, Real *p01, Real *p02,
               Real *p10, Real *p11, Real *p12,
               Real *s00, Real *s01,
               Real *s10, Real *s11, 
               inter3d_t *stat )
  {

//    cout << "ttint\n";
      Real v00[3],v01[3];
      Real v10[3],v11[3];
      Real b[3];
      Real s0[2],t0[2];
      Real s1[2],t1[2];

      Real *y2;
      Real *q0[3],*r0;
      Real *q1[3],*r1;
      Real *q2[3];

      Real  z00,z01,z10,z11;
      Real  z0,z1;

      sub3( p00,p02, v00 );
      sub3( p01,p02, v01 );

      sub3( p10,p12, v10 );
      sub3( p11,p12, v11 );

      ppint( p02,v00,v01, p12,v10,v11, stat );

//    cout << stat->det << "\n";
      if( fabs( stat->det ) > small )
     {


         y2= stat->y2;
         q2[2]= stat->q2[2];

/*       cout << "intersection line\n";
         cout << y2[0]<<" "<<y2[1]<<" "<<y2[2]<<"\n";
         cout << q2[2][0]<<" "<<q2[2][1]<<" "<<q2[2][2]<<"\n";*/

         q0[0]= stat->q0[0]; 
         q0[1]= stat->q0[1]; 
         r0=    stat->r0; 

         sub3( y2,p02, b );
         qrs23( q0[0],q0[1],r0,s0,b );
         qrs23( q0[0],q0[1],r0,t0,q2[2] );

         q1[0]= stat->q1[0]; 
         q1[1]= stat->q1[1]; 
         r1=    stat->r1; 

         sub3( y2,p12, b );
         qrs23( q1[0],q1[1],r1,s1,b );
         qrs23( q1[0],q1[1],r1,t1,q2[2] );

         z00=-big;
         z01= big;
         ltinters( s0, t0, &z00, &z01 );

         z10=-big;
         z11= big;
         ltinters( s1, t1, &z10, &z11 );

/*       cout << "linear coordinates\n";
         cout << z00<<" "<<z01<<"\n";
         cout << z10<<" "<<z11<<"\n";

         cout << "points\n";
         cout << y2[0]+ z00*q2[2][0]<<" "<< y2[1]+ z00*q2[2][1]<<" "<< y2[2]+ z00*q2[2][2]<<"\n";
         cout << y2[0]+ z01*q2[2][0]<<" "<< y2[1]+ z01*q2[2][1]<<" "<< y2[2]+ z01*q2[2][2]<<"\n";

         cout << y2[0]+ z10*q2[2][0]<<" "<< y2[1]+ z10*q2[2][1]<<" "<< y2[2]+ z10*q2[2][2]<<"\n";
         cout << y2[0]+ z11*q2[2][0]<<" "<< y2[1]+ z11*q2[2][1]<<" "<< y2[2]+ z11*q2[2][2]<<"\n";*/

         z0= z00;
         z1= z01;
         inters( &z0,&z1, z10,z11 );

/*       z0= fmax( z00,fmin(z11,z01) );
         z1= fmin( z01,fmax(z10,z00) );*/

/*       cout << "points\n";
         cout << y2[0]+ z0*q2[2][0]<<" "<< y2[1]+ z0*q2[2][1]<<" "<< y2[2]+ z0*q2[2][2]<<"\n";
         cout << y2[0]+ z1*q2[2][0]<<" "<< y2[1]+ z1*q2[2][1]<<" "<< y2[2]+ z1*q2[2][2]<<"\n";*/

         s00[0]= s0[0]+ z0*t0[0];
         s00[1]= s0[1]+ z0*t0[1];

         s01[0]= s0[0]+ z1*t0[0];
         s01[1]= s0[1]+ z1*t0[1];

         s10[0]= s1[0]+ z0*t1[0];
         s10[1]= s1[1]+ z0*t1[1];

         s11[0]= s1[0]+ z1*t1[0];
         s11[1]= s1[1]+ z1*t1[1];

     }
  }

   Int onedg3( Real *s, inter3d_t *stat, Real tol )
  {
      Int val= -1;
      bool wrk[6]= {false,false,false,false,false,false};
      if( abs( s[0] ) < small ){ wrk[0]= true; }
      if( abs( s[1] ) < small ){ wrk[1]= true; }
      if( abs( s[2] ) < small ){ wrk[2]= true; }
      if( wrk[1] && wrk[2] ){ val= 3; return val; }
      if( wrk[2] && wrk[0] ){ val= 4; return val; }
      if( wrk[0] && wrk[1] ){ val= 5; return val; }
      if( wrk[2] ){ val= 2; return val; }
      if( wrk[1] ){ val= 1; return val; }
      if( wrk[0] ){ val= 0; return val; }
      return val;
  }

   void box3( Real *x0, Real *x1, Real *x2, Real *xmin, Real *xmax )
  {
      xmin[0]= x0[0];
      xmin[0]= min( xmin[0],x1[0] );
      xmin[0]= min( xmin[0],x2[0] );

      xmax[0]= x0[0];
      xmax[0]= max( xmax[0],x1[0] );
      xmax[0]= max( xmax[0],x2[0] );

      xmin[1]= x0[1];
      xmin[1]= min( xmin[1],x1[1] );
      xmin[1]= min( xmin[1],x2[1] );

      xmax[1]= x0[1];
      xmax[1]= max( xmax[1],x1[1] );
      xmax[1]= max( xmax[1],x2[1] );

      xmin[2]= x0[2];
      xmin[2]= min( xmin[2],x1[2] );
      xmin[2]= min( xmin[2],x2[2] );

      xmax[2]= x0[2];
      xmax[2]= max( xmax[2],x1[2] );
      xmax[2]= max( xmax[2],x2[2] );
  }

   bool inters3( Real *x0, Real *x1, Real *y0, Real *y1 )
  {
      bool val;

      val= inters( x0[0],x1[0], y0[0],y1[0] );
      val= val && inters( x0[1],x1[1], y0[1],y1[1] );
      val= val && inters( x0[2],x1[2], y0[2],y1[2] );

      return val;
  }


   extern "C" void dgetrf_( int *, int *, Real *, int *, int *, int * );
   extern "C" void dgetrs_( char *, int *, int *, Real *, int *, int *, Real *, int *, int * );

/* void sphere( Real *x0, Real *l0, Real *m0, Real d0, Real *x1, Real *l1, Real *m1, Real d1, 
                Real *x2, Real *l2, Real *m2, Real r,  Real *y0, Real *y1, Real *y2, Int *flag )
  {

      Real      n0[4],n1[4],n2[4];
      Real      sa[36],*a[6],rhs[6];
      Real      r=0.1;
      int       lda=6,ldb=6,n=6,nrhs=1;
      int       info;
      int       ipiv[6];
      char      job='n';
  
   
      vec3( l0,m0, n0 );
      vec3( l1,m1, n1 );

      n0[3]= norm23( n0 );
      n1[3]= norm23( n1 );

      sclv3( d0*r/n0[3],n0 );
      sclv3( d1*r/n1[3],n1 );

      subv( 6,6, sa,a );
      setv( 0,36, 0., sa );

      a[0][0]= -l0[0];
      a[0][1]= -l0[1];
      a[0][2]= -l0[2];
      a[1][0]= -m0[0];
      a[1][1]= -m0[1];
      a[1][2]= -m0[2];

      a[2][3]= -l1[0];
      a[2][4]= -l1[1];
      a[2][5]= -l1[2];
      a[3][3]= -m1[0];
      a[3][4]= -m1[1];
      a[3][5]= -m1[2];

      a[4][0]=  l2[0];
      a[4][1]=  l2[1];
      a[4][2]=  l2[2];
      a[4][3]=  l2[0];
      a[4][4]=  l2[1];
      a[4][5]=  l2[2];

      a[5][0]=  m2[0];
      a[5][1]=  m2[1];
      a[5][2]=  m2[2];
      a[5][3]=  m2[0];
      a[5][4]=  m2[1];
      a[5][5]=  m2[2];

      rhs[0]= x0[0]-x2[0]+n0[0];
      rhs[1]= x0[1]-x2[1]+n0[1];
      rhs[2]= x0[2]-x2[2]+n0[2];
      rhs[3]= x1[0]-x2[0]+n1[0];
      rhs[4]= x1[1]-x2[1]+n1[1];
      rhs[5]= x1[2]-x2[2]+n1[2];

      dgetrf_( &n,&n, sa,&lda, ipiv, &info );
     *flag= info;
      if( info == 0 )
     {
         dgetrs_( &job,&n,&nrhs, sa,&lda, ipiv, rhs,&lda, &info );
         y0[0]= rhs[0]; 
         y0[1]= rhs[1]; 
         y1[0]= rhs[2]; 
         y1[1]= rhs[3]; 
         y2[0]= rhs[4]; 
         y2[1]= rhs[5]; 
     }
  
  
  }*/
