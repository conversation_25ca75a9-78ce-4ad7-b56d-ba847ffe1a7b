   using namespace std;

#  include <geo/3d/interp3d.h>

   cSections::cSections()
  {
      ns=0;
      sec=NULL;
      wrk[0]=NULL;
      wrk[1]=NULL;
      wrk[2]=NULL;
      wrk[3]=NULL;
      wrk[4]=NULL;
      wrk[5]=NULL;
  }

   cSections::~cSections()
  {
      Int is;
      clean();
  }

   void cSections::clean()
  {
      Int is;
      for( is=0;is<ns;is++ )
     {
         delete sec[is]; sec[is]=NULL;
     }
      delete[] sec;
      sec=NULL;
      ns=0;
      delete[] wrk[0]; wrk[0]=NULL;
      delete[] wrk[1]; wrk[1]=NULL;
      delete[] wrk[2]; wrk[2]=NULL;
      delete[] wrk[3]; wrk[3]=NULL;
      delete[] wrk[4]; wrk[4]=NULL;
      delete[] wrk[5]; wrk[5]=NULL;
  }

   void cSections::build( Int ns0, cInterp **sec0 )
  {
      Int          i;
      bool         p;
      Real y0[3],dy0[3];
      Real y1[3],dy1[3];

      p= sec0[0]->periodic();
      for( i= 1;i<ns0;i++)
     {
         assert( sec0[i]->periodic() == p );
     }

      clean();
      ns=ns0;
      sec= new cInterp*[ns];
      setv( 0,ns, (cInterp*)NULL, sec );
      for( Int i=0;i<ns;i++ )
     {
         cInterp *w=NULL;
         sec0[i]->copy( &w ); 
         sec[i]= w;
     } 
      wrk[0]= new Real[ns];
      wrk[1]= new Real[ns];
      wrk[2]= new Real[ns];
      wrk[3]= new Real[ns];
      wrk[4]= new Real[ns];
      wrk[5]= new Real[ns];

      mt0[0]= (sec[ns/2])->length();
      mt0[1]= 0;
      mt1[0]= 0;
      sec[0]->interp( 0.,y0,dy0 );
      sec[ns-1]->interp( 0.,y1,dy1 );
      sub3( y1,y0, dy0 );
      mt1[1]= norm23( dy0 );

      cout << "metrics for this surface "<<mt0[0]<<" "<<mt1[1]<<"\n";

  }

   void cSections::interp( Real *y, Real *x, Real *dx0, Real *dx1 )
  {
      Real v[6];
      Real w[6];
      for( Int i=0;i<ns; i++ )
     {
         sec[i]->interp( y[0], v,v+3 );
         wrk[0][i]= v[0];
         wrk[1][i]= v[1];
         wrk[2][i]= v[2];
         wrk[3][i]= v[3];
         wrk[4][i]= v[4];
         wrk[5][i]= v[5];
     }
      tmp.build( 1,ns,6, wrk,NULL,NULL );
      tmp.interp( y[1],v,w  );
      x[0]=v[0];
      x[1]=v[1];
      x[2]=v[2];
      dx0[0]= v[3];
      dx0[1]= v[4];
      dx0[2]= v[5];
      dx1[0]= w[0];
      dx1[1]= w[1];
      dx1[2]= w[2];
  }

   void cSections::copy( cInterp3d **var )
  {
      cSections *tmp;
      if( *var )
     {
         cout << "cannot copy onto existing object\n";
         exit(0);
     }

      tmp= new cSections();
      tmp->build( ns,sec );
     *var= tmp;
  }
