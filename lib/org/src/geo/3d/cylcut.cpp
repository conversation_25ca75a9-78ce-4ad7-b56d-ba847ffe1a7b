
   using namespace std;

#  include <geo/3d/interp3d.h>

   void cylcut( cInterp3d *sf, cVSpline *cs, Real *x0, Real *n0, Real *s, Real *m, inter3d_t *stat )
  {

      Real     s0[2];
      Real     xp[3],dxp[2][3];
      Real     z[10],dz[10];
      Real     dy[3];
      Real     v[3],dv[3][3];
      Real     det;
      Real     a[3][4],rhs[3];
      Int      ipiv[3];
      Int      it,j;
      ofstream fle;
      
      Real rlx;
      Real res=big;

      rlx=stat->rlx;
      sf->quick();
      
      it= 0;
      while( res >= ITPPTOL )
     {

         sf->interp(  s, xp,dxp[0],dxp[1] );
         cs->interp( *m, z,dz );

         v[0]= xp[0];
         v[1]= xp[1]*xp[1]+ xp[2]*xp[2];
         v[1]= sqrt(v[1]);

         dv[0][0]= dxp[0][0];
         dv[0][1]= xp[1]*dxp[0][1]+ xp[2]*dxp[0][2];
         dv[0][1]/=v[1];
         dv[0][2]= dot3( dxp[0],n0 );

         dv[1][0]= dxp[1][0];
         dv[1][1]= xp[1]*dxp[1][1]+ xp[2]*dxp[1][2];
         dv[1][1]/=v[1];
         dv[1][2]= dot3( dxp[1],n0 );

         dv[2][0]=-dz[0];
         dv[2][1]=-dz[1];
         dv[2][2]=0;

         sub2( v,z, v );
         sub3( xp,x0, xp );
         v[2]= dot3( xp,n0 );

         res= norminf3( v );
         luf3( dv[0],dv[1],dv[2], a[0],a[1],a[2], ipiv, &det );
         lus3( a[0],a[1],a[2], ipiv, dy, v );

         s[0]-= rlx*dy[0]; 
         s[1]-= rlx*dy[1]; 
        *m-=    rlx*dy[2]; 

/*       cout << "()cylcut "<<it<<" "<<s[0]<<" "<<s[1]<<" "<<*m<<" dy "<<
                  dy[0]<<" "<<dy[1]<<" "<<dy[2]<<" v "<<
                   v[0]<<" "<< v[1]<<" "<< v[2]<<" res "<<res<<" "<<det<<"\n";*/

         rlx*= ITPDRLX;
         rlx= min( rlx,ITPMRLX );

         if( (++it) > ITPMXIT )
        {
//          cout << res<<" WARNING: maximum iteration count exceeded\n";
            break;
        }

     }

//    cout << "cylcut "<<it<<" "<<s[0]<<" "<<s[1]<<" "<<*m<<" "<<res<<"\n";

      stat->res=res;
      idv2( s,stat->s0 );
      stat->s1[0]= *m;
      stat->det= det;
      sf->accurate();
  }
