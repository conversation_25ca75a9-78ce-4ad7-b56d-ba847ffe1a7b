   using namespace std;

#  include <geo/3d/wedge.h>

   cWedge::cWedge()
  {
      ib=-1;
      ii=-1;
      ig=-1;
      itg=false;
      cls=false;
      na=0;
      s= NULL;
      s0[0]= NULL;
      s0[1]= NULL;
      s1[0]= NULL;
      s1[1]= NULL;
      x0[0]= NULL;
      x0[1]= NULL;
      x0[2]= NULL;
      x1[0]= NULL;
      x1[1]= NULL;
      x1[2]= NULL;
      l=0;

      sfi= NULL;
      sfb= NULL;
  };

   cWedge::~cWedge()
  {
      ib=-1;
      ii=-1;
      ig=-1;
      itg=false;
      cls=false;
      na=0;
      delete[] s; s= NULL;
      delete[] s0[0]; s0[0]= NULL; 
      delete[] s0[1]; s0[1]= NULL; 
      delete[] s1[0]; s1[0]= NULL; 
      delete[] s1[1]; s1[1]= NULL; 
      delete[] x0[0]; x0[0]= NULL; 
      delete[] x0[1]; x0[1]= NULL; 
      delete[] x0[2]; x0[2]= NULL; 
      delete[] x1[0]; x1[0]= NULL; 
      delete[] x1[1]; x1[1]= NULL; 
      delete[] x1[2]; x1[2]= NULL; 
      l=0;
      sfi= NULL;
      sfb= NULL;
  };

   void cWedge::build( Int ib0, Int ii0, bool it0, Int n, Real *s00[], bool b0, Real *s10[], bool b1, cInterp3d *srf[] )
  {
      Int    ia; 
      Real   z0[2],z1[2];
      Real   y0[3],y1[3],dy0[3],dy1[3];

      na=n;
      s= new Real[na];
      s0[0]= new Real[na];
      s0[1]= new Real[na];
      s1[0]= new Real[na];
      s1[1]= new Real[na];
      x0[0]= new Real[na];
      x0[1]= new Real[na];
      x0[2]= new Real[na];
      x1[0]= new Real[na];
      x1[1]= new Real[na];
      x1[2]= new Real[na];

      ib=ib0; 
      ii=ii0; 
      ig= 0; 
      itg= it0; 
      if( itg ){ cout << "w-edge "<<this<<" is tangent\n"; }

// definition points in surface coordinates

      if( b0 )
     {
         for( ia=0;ia<na;ia++ )
        {
            s0[0][ia]= s00[0][ia];
            s0[1][ia]= s00[1][ia];
        }
     }
      else
     {
         for( ia=0;ia<na;ia++ )
        {
            s0[0][ia]= s00[0][na-ia-1];
            s0[1][ia]= s00[1][na-ia-1];
        }
     }
      if( b1 )
     {
         for( ia=0;ia<na;ia++ )
        {
            s1[0][ia]= s10[0][ia];
            s1[1][ia]= s10[1][ia];
        }
     }
      else
     {
         for( ia=0;ia<na;ia++ )
        {
            s1[0][ia]= s10[0][na-ia-1];
            s1[1][ia]= s10[1][na-ia-1];
        }
     }

// definition points in cartesian coordinates and consistency check

      for( ia=0;ia<na;ia++ )
     {
         z0[0]= s0[0][ia];
         z0[1]= s0[1][ia];
         z1[0]= s1[0][ia];
         z1[1]= s1[1][ia];

       
         srf[ib]->interp( z0,y0,dy0,dy1 );
         srf[ii]->interp( z1,y1,dy0,dy1 );
         sub3( y1,y0, dy0 ); 
         if( norminf3( dy0 ) > ITPPTOL )
        {
            cout << "definitions do not match\n"; 
            exit(0);
        }
         x0[0][ia]= y0[0];
         x0[1][ia]= y0[1];
         x0[2][ia]= y0[2];

         x1[0][ia]= y1[0];
         x1[1][ia]= y1[1];
         x1[2][ia]= y1[2];
     }

// lengths
      s[0]= 0;
      z0[0]= s0[0][0];
      z0[1]= s0[1][0];
      srf[ib]->interp( z0,y1,dy0,dy1 );
      for( ia=1;ia<na;ia++ )
     {
         idv3( y1,y0 );
         z0[0]= s0[0][ia];
         z0[1]= s0[1][ia];
         srf[ib]->interp( z0,y1,dy0,dy1 );
         sub3( y1,y0, dy0 );
         s[ia]= s[ia-1]+ norm23( dy0 );
     }
      l= s[na-1];
      for( ia=0;ia<na;ia++ )
     {
         s[ia]/= l;
     }

// closed-loop w-edges

      y0[0]= x0[0][0];
      y0[1]= x0[1][0];
      y0[2]= x0[2][0];
      y1[0]= x0[0][na-1];
      y1[1]= x0[1][na-1];
      y1[2]= x0[2][na-1];
      sub3( y0,y1, dy0 );
      cls= ( norminf3( dy0 )< ITPPTOL );

      if( cls )
     {
         cout << "winged edge is closed\n";
     }
  };

   Int cWedge::surf( Int iws, Int iwe, cWedge *w[] )
  {
      Int iw;
      Int val=-1;
      for( iw=iws;iw<iwe;iw++ )
     {
         if( w[iw]->ib == ib )
        {
            val= iw;
            break;
        }
     }
      return val;
  }

   Int cWedge::next( Int iws, Int iwe, cWedge *w[] )
  {
      Int iw;
      Int val=-1;
      Real y0[3],y1[3],d[3];
      y0[0]= x0[0][na-1];
      y0[1]= x0[1][na-1];
      y0[2]= x0[2][na-1];
      for( iw=iws;iw<iwe;iw++ )
     {
         if( w[iw]->ib == ib )
        {
            y1[0]= w[iw]->x0[0][0];
            y1[1]= w[iw]->x0[1][0];
            y1[2]= w[iw]->x0[2][0];
            sub3( y0,y1, d );
            if( norminf3( d ) < ITPPTOL )
           { 
               val= iw;
               break;
           }
/*          else
           {
               cout << "looking for wedge following "<<iw0<<": "<<iw<<" rejected because "<<norminf3(d)<<"\n";
               cout << y0[0]<<" "<<y0[1]<<" "<<y0[2]<<"\n";           
               cout << y1[0]<<" "<<y1[1]<<" "<<y1[2]<<"\n";           
           }*/
        }
     }
      return val;
  }

   Int cWedge::twin( Int iws, Int iwe, cWedge *w[] )
  {
      Int iw;
      Int i;
      Int  val=-1;
      Int  ia0,ia1;
      Int  na1;
      Real y0[3],y1[3],d[3];
      bool twn;
      for( iw=iws;iw<iwe;iw++ )
     {
         if( w[iw]->ib == ii && w[iw]->ii == ib )
        {
            na1= w[iw ]->na;          
            if( na == na1 )
           {
               ia0= 0;
               ia1= na-1;
               twn= true;
               for( i=0;i<na;i++ )
              {
                  y0[0]= x0[0][ia0];
                  y0[1]= x0[1][ia0];
                  y0[2]= x0[2][ia0];
                  y1[0]= w[iw]->x0[0][ia1];
                  y1[1]= w[iw]->x0[1][ia1];
                  y1[2]= w[iw]->x0[2][ia1];
                  sub3( y0,y1, d );
                  if( norminf3( d ) > ITPPTOL )
                 {
                     twn=false;    
                     break;
                 }
                  ia0++;
                  ia1--;
              }
               if( twn )
              {
                  val= iw;
                  break;
              }
           }
        }
     }
      return val;
  }

   void cWedge::interp2( Real w0, Real *y, Real *dy, cInterp3d *srf[] )
  {
      inter3d_t stat;
      Real      w,w1;
      Real      z0[2],z1[2];
      Real      dz0[2],dz1[2];
      Real      dy0[3],dy1[3];
      Real      p2[3],n2[3];
      Real      b[3],x[3];
      Real      ds;
      Real     *y0,*y1;
      Real     *a0,*a1,*a2;
      Int      *ipiv;
      Real     *q0,*q1,*r;
      w= w0;
      if( cls )
     {
         while( w < 0 ){ w+= 1; };
         while( w > 1 ){ w-= 1; };
     }
    
      Int i;
      if( w < 0 )
     {
         cout << "extrapolate below 0 not done yet\n";
     }
      else
     {
         if( w > 1 )
        {
            cout << "extrapolate above 1 not done yet\n";
        }
         else
        {

//          i= bsearch( w,na,s );
            i= bsearch( w, na-1,s+1 );
            ds= s[i+1]- s[i];
            w1= ( w-s[i] )/ds;
            z0[0]= w1*s0[0][i+1]+ (1-w1)*s0[0][i];
            z0[1]= w1*s0[1][i+1]+ (1-w1)*s0[1][i];

            z1[0]= w1*s1[0][i+1]+ (1-w1)*s1[0][i];
            z1[1]= w1*s1[1][i+1]+ (1-w1)*s1[1][i];

            p2[0]= w1*x0[0][i+1]+ (1-w1)*x0[0][i]; 
            p2[1]= w1*x0[1][i+1]+ (1-w1)*x0[1][i]; 
            p2[2]= w1*x0[2][i+1]+ (1-w1)*x0[2][i]; 

            n2[0]= x0[0][i+1]- x0[0][i];
            n2[1]= x0[1][i+1]- x0[1][i];
            n2[2]= x0[2][i+1]- x0[2][i];

            if( !itg )
           {
               inters( srf[ib], srf[ii], p2, n2, z0,z1, &stat );
               a0= stat.a[0];
               a1= stat.a[1];
               a2= stat.a[2];
               ipiv= stat.ipiv;
               b[0]= 0;
               b[1]= 0;
               b[2]= l*l*ds;
               lus3t( a0,a1,a2,ipiv, x, b );
               q0= stat.q0[0];
               q1= stat.q0[1];
               r = stat.r0;
               qrs23( q0,q1,r, dy, x );
               y[0]= z0[0];
               y[1]= z0[1];
           }
            else
           {
               y[0]= z0[0];
               y[1]= z0[1];
               dy[0]= s0[0][i+1]- s0[0][i];
               dy[1]= s0[1][i+1]- s0[1][i];
               sclv2( 1./ds,dy );
           }
        }
     }
  }

   void cWedge::interp3( Real w0, Real *y, Real *dy, cInterp3d *srf[] )
  {
      inter3d_t stat;
      Real      w,w1;
      Real      z0[2],z1[2];
      Real      dz0[2],dz1[2];
      Real      dy0[3],dy1[3];
      Real      p2[3],n2[3];
      Real      b[3],x[3];
      Real      ds;
      Real     *y0,*y1;
      Real     *a0,*a1,*a2;
      Int      *ipiv;
      Real     *q0,*q1,*r;
      w= w0;
      if( cls )
     {
         while( w < 0 ){ w+= 1; };
         while( w > 1 ){ w-= 1; };
     }
    
      Int i;
      if( w < 0 )
     {
         cout << "extrapolate below 0 not done yet\n";
     }
      else
     {
         if( w > 1 )
        {
            cout << "extrapolate above 1 not done yet\n";
        }
         else
        {
//          i= bsearch( w,na,s );
            i= bsearch( w, na-1,s+1 );
            ds= s[i+1]- s[i];
            w1= ( w-s[i] )/ds;
            z0[0]= w1*s0[0][i+1]+ (1-w1)*s0[0][i];
            z0[1]= w1*s0[1][i+1]+ (1-w1)*s0[1][i];

            z1[0]= w1*s1[0][i+1]+ (1-w1)*s1[0][i];
            z1[1]= w1*s1[1][i+1]+ (1-w1)*s1[1][i];

            p2[0]= w1*x0[0][i+1]+ (1-w1)*x0[0][i]; 
            p2[1]= w1*x0[1][i+1]+ (1-w1)*x0[1][i]; 
            p2[2]= w1*x0[2][i+1]+ (1-w1)*x0[2][i]; 

            n2[0]= x0[0][i+1]- x0[0][i];
            n2[1]= x0[1][i+1]- x0[1][i];
            n2[2]= x0[2][i+1]- x0[2][i];

            if( !itg )
           {
               inters( srf[ib], srf[ii], p2, n2, z0,z1, &stat );
               y[0]= stat.y0[0];
               y[1]= stat.y0[1];
               y[2]= stat.y0[2];
               a0= stat.a[0];
               a1= stat.a[1];
               a2= stat.a[2];
               ipiv= stat.ipiv;
               b[0]= 0;
               b[1]= 0;
               b[2]= l*l*(s[i+1]-s[i]);
               lus3t( a0,a1,a2,ipiv, dy, b );
           }
            else
           {
               Real y0[3];
               dz0[0]= s0[0][i+1]- s0[0][i];
               dz0[1]= s0[1][i+1]- s0[1][i];
               srf[ib]->interp( z0,y,dy0,dy1 );
               dy[0]= dy0[0]*dz0[0]+ dy1[0]*dz0[1]; 
               dy[1]= dy0[1]*dz0[0]+ dy1[1]*dz0[1]; 
               dy[2]= dy0[2]*dz0[0]+ dy1[2]*dz0[1];
               sclv3( 1./ds,dy );
           }
        }
     }
  }

   void cWedge::bind( cInterp3d *s[] )
  {
      sfb= s[ib];    
      sfi= s[ii];    
  }

   void cWedge::unbind()
  {
      sfb= NULL;
      sfi= NULL;
  }

   void cWedge::head( Real *x )
  {
      x[0]= x0[0][0];    
      x[1]= x0[1][0];    
      x[2]= x0[2][0];    
  }

   void cWedge::tail( Real *x )
  {
      x[0]= x0[0][na-1];    
      x[1]= x0[1][na-1];    
      x[2]= x0[2][na-1];    
  }
