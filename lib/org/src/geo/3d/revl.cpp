   using namespace std;

#  include <geo/3d/interp3d.h>

   cRevolution::cRevolution()
  {
      pl= NULL;
      setv( 0,3, 0.,p0 );
      setv( 0,3, 0.,l0 );
      setv( 0,3, 0.,m0 );
      setv( 0,3, 0.,ax );
  }

   cRevolution::~cRevolution()
  {
      delete pl; pl= NULL;
      setv( 0,3, 0.,p0 );
      setv( 0,3, 0.,l0 );
      setv( 0,3, 0.,m0 );
      setv( 0,3, 0.,ax );
  }
   

   void cRevolution::copy( cInterp3d **var )
  {
      cRevolution *tmp;
      if( !(*var) )
     {
         tmp= new cRevolution();
         tmp->build( pl,p0,l0,m0,a0,ax );
       (*var)= tmp;
     }
      else
     {
         cout << "cannot copy into existing cInterp3d of unknown kind (cRevolution::copy)\n";
         exit(0);
     }
  }

   void cRevolution::build( cPolyline *pl0, Real *p, Real *l, Real *m, Real *q, Real *x )
  {
      Real v0[4],v1[4],v2[4];
      Real y[2],dy[2];
      pl0->copy( (cInterp**)&pl ); 


      idv3( p,p0 );
      idv3( l,v0 );
      idv3( m,v1 );
      v0[3]= norm23( v0 ); sclv3( 1./v0[3],v0 );
      vec3( v0,v1,v2 );
      vec3( v2,v0,v1 );
      v1[3]= norm23( v1 ); sclv3( 1./v1[3],v1 );
      idv3( v0,l0 );
      idv3( v1,m0 );
      idv3( q,a0 );
      idv3( x,v0 );
      v0[3]= norm23( v0 ); sclv3( 1./v0[3],v0 );
      idv3( v0,ax );

      mt0[0]= pl0->length();
      mt0[1]= 0;

      Real z[3],d;
      pl0->interp( 0.5,y,dy );
      z[0]= p0[0]+  y[0]*l0[0]+ y[1]*m0[0];
      z[1]= p0[1]+  y[0]*l0[1]+ y[1]*m0[1];
      z[2]= p0[2]+  y[0]*l0[2]+ y[1]*m0[2];

      sub3( z,a0, z );
      d= dot3( z,ax );
      z[0]-= d*ax[0];
      z[1]-= d*ax[1];
      z[2]-= d*ax[2];

      mt1[0]= 0;
      mt1[1]= pi2*norm23( z );
 
  }

   void cRevolution::interp( Real *s, Real *y, Real *dy0, Real *dy1 )
  {
      Real s0,s1;
    
      Real d,dd;
      Real cth,sth;
      Real z[2],dz[2];
      Real x[3],dx[3];
      Real v0[3],v1[3];
      Real dv0[3],dv1[3];

      s0= s[0];
      s1= s[1];
      s1*= pi2;
      cth= cos( s1 );
      sth= sin( s1 );

/*   {
         cout << "--------------------------------\n";
         cout << "differentiation check - polyline\n";
         Real z0[2],dz0[2];
         pl->interp( s0,z0,dz0 );
         cout << "polyline (0) "<< z0[0]<<" "<< z0[1]<<" (unperturbed position )\n";
         cout << "polyline (1) "<<dz0[0]<<" "<<dz0[1]<<" (unperturbed tg. vector )\n";
         s0+= 1.e-6;
         pl->interp( s0,z,dz );
         cout << "polyline (2) "<< (z[0]-z0[0] )/1.e-4<<" "<<(z[1]-z0[1])/1.e-4<<" (position perturbation )\n";
         s0-= 1.e-6;
         cout << "--------------------------------\n";
     }*/

      pl->interp( s0,z,dz );

      x[0]= p0[0]+  z[0]*l0[0]+ z[1]*m0[0];
      x[1]= p0[1]+  z[0]*l0[1]+ z[1]*m0[1];
      x[2]= p0[2]+  z[0]*l0[2]+ z[1]*m0[2];

      dx[0]=        dz[0]*l0[0]+dz[1]*m0[0];
      dx[1]=        dz[0]*l0[1]+dz[1]*m0[1];
      dx[2]=        dz[0]*l0[2]+dz[1]*m0[2];
      
      sub3( x,a0, v0 );
      d= dot3( v0,ax );
      v0[0]-= d*ax[0];
      v0[1]-= d*ax[1];
      v0[2]-= d*ax[2];

      vec3( v0,ax, v1 );
      y[0]= a0[0]+ d*ax[0]+ cth*v0[0]+ sth*v1[0];
      y[1]= a0[1]+ d*ax[1]+ cth*v0[1]+ sth*v1[1];
      y[2]= a0[2]+ d*ax[2]+ cth*v0[2]+ sth*v1[2];

      dd= dot3( dx,ax );
      dv0[0]= dx[0]- dd*ax[0];
      dv0[1]= dx[1]- dd*ax[1];
      dv0[2]= dx[2]- dd*ax[2];

      vec3( dv0,ax, dv1 );
      dy0[0]= dd*ax[0]+ cth*dv0[0]+ sth*dv1[0];
      dy0[1]= dd*ax[1]+ cth*dv0[1]+ sth*dv1[1];
      dy0[2]= dd*ax[2]+ cth*dv0[2]+ sth*dv1[2];

      dy1[0]=          -sth* v0[0]+ cth* v1[0];
      dy1[1]=          -sth* v0[1]+ cth* v1[1];
      dy1[2]=          -sth* v0[2]+ cth* v1[2];

      dy1[0]*= pi2;
      dy1[1]*= pi2;
      dy1[2]*= pi2;
  }
