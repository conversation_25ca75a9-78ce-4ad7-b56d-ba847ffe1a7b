   using namespace std;

#  include <utils/proto.h>
#  include <sort/proto.h>
#  include <geo/3d/polyset3d.h>
#  include <assert.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

   void cPolyset3d::tria1( Real dinf )
  {
         cout << "cPolyset3d::tria1 quarantined\n";
         assert(false);

/*    Int                is,id,idr,iw,jw,il,is0,is1,ib,ip;
      Int                ist,ien;
      Real               yp[2],dyp[2];
      Real               xp[3],dxp[2][3];
      Real              *tmp[3];
      Int               *itmp[2];
      Int                nbp0;

      Int                ntmp;

      cMeshCtrl         *mco[100];
      cJm28Unstructured  *jm28;


      Int i0,i1,i2;

      nfs= new Int[ns];
      nfp= new Int[ns];
      nfe= new Int[ns];
      nfb= new Int[ns];

      yfp= new real2_t[ns];
      xfp= new real3_t[ns];
      ifsp= new int2_t[ns];
      ifep= new int3_t[ns];
      ifbp= new int4_t[ns];
      sfbp= new real2_t[ns];

      for( is=0;is<ns;is++ )
     {
         nfs[is]=0;
         nfp[is]=0;
         nfe[is]=0;
         nfb[is]=0;
         yfp[is][0]= new Real[100000];
         yfp[is][1]= new Real[100000];
         xfp[is][0]= new Real[100000];
         xfp[is][1]= new Real[100000];
         xfp[is][2]= new Real[100000];
         ifsp[is][0]= new Int[100000];
         ifsp[is][1]= new Int[100000];
         ifep[is][0]= new Int[100000];
         ifep[is][1]= new Int[100000];
         ifep[is][2]= new Int[100000];
         ifbp[is][0]= new Int[100000];
         ifbp[is][1]= new Int[100000];
         ifbp[is][2]= new Int[100000];
         ifbp[is][3]= new Int[100000];
         sfbp[is][0]= new Real[100000];
         sfbp[is][1]= new Real[100000];
         mco[is]= new cMeshCtrl();
         mco[is]->background( srf[is] );
         mco[is]->setdefault( dinf );
     }
      tmp[0]= new Real[100000];
      tmp[1]= new Real[100000];
      tmp[2]= new Real[100000];
      itmp[0]= new Int[100000];
      itmp[1]= new Int[100000];

      ien=0;
      for( il=0;il<nl;il++ )
     {
         ist= ien;
         ien= llw[il];
         for( jw=ist;jw<ien;jw++ )
        {
            iw=  ilw[jw];
            cout << "processing loop "<<il<<" side "<<jw<<" "<<iw<<"\n";
            id=  iwd[0][iw];
            idr= iwd[1][iw];
            is=  iws[0][iw];
            is0= ids[0][id];
            is1= ids[1][id];
            ntmp= (Int)(wdf[id]->length()/dinf)+2;
            for( Int i=0;i<ntmp;i++ )
           {
               Real w=(Real)i/(Real)(ntmp-1);
               if( idr == 1 ){ w= 1-w; };
               wdf[id]->interp2( w,yp,dyp, srf[is0],srf[is1], idr );
               tmp[0][i]= yp[0];
               tmp[1][i]= yp[1];
               tmp[2][i]= w;
           }

            nbp0= nfs[is];
            addtofront( 0,ntmp, tmp, nfp+is, nfs+is, yfp[is], ifsp[is], itmp, false );

            cout << "adding boundaries between "<<nbp0<<" and "<<nfs[is]<<" for surface "<<is<<" from edge "<<iw<<" \n";
            for( ib=nbp0;ib<nfs[is];ib++ )
           {
               ifbp[is][0][ib]= ifsp[is][0][ib];
               ifbp[is][1][ib]= ifsp[is][1][ib];
               ifbp[is][2][ib]= iwd[0][iw];
               ifbp[is][3][ib]= iwd[1][iw];
               sfbp[is][0][ib]= tmp[2][itmp[0][ib]];
               sfbp[is][1][ib]= tmp[2][itmp[1][ib]];
           }
            nfb[is]= nfs[is];
        }
     }
      delete[] tmp[0];
      delete[] tmp[1];
      delete[] tmp[2];
      delete[] itmp[0];
      delete[] itmp[1];

      for( is=0;is<ns;is++ )
     {
         string str;
         str= "front."+strc(is)+".dat";
         ofstream fle( str.c_str() );
         for( Int i=0;i<nfs[is];i++ )
        {
            fle << "\n";
            fle << "#\n";
            fle << "\n";
            fle << yfp[is][0][ifsp[is][0][i]]<<" "<<yfp[is][1][ifsp[is][0][i]]<<"\n";
            fle << yfp[is][0][ifsp[is][1][i]]<<" "<<yfp[is][1][ifsp[is][1][i]]<<"\n";
        }
         fle.close();
        

         jm28= new cJm28Unstructured();
         jm28->advncefront( nfs+is,ifsp[is], nfp+is,yfp[is], nfe+is,ifep[is], mco[is] );
         delete jm28;
     }

      for( is=0;is<ns;is++ )
     {
         for( ip=0;ip<nfp[is];ip++ )
        {
            yp[0]= yfp[is][0][ip];      
            yp[1]= yfp[is][1][ip];      
            srf[is]->interp( yp,xp,dxp[0],dxp[1] );
            xfp[is][0][ip]= xp[0];
            xfp[is][1][ip]= xp[1];
            xfp[is][2][ip]= xp[2];
        }
     }

      for( is=0;is<ns;is++ )
     {
         delete mco[is];
     }

      return;*/

  }
 
