   using namespace std;

#  include <geo/3d/polyset3d.h>
// void tecplot2( Int ies, Int iee, Int nv, Int nep, Int *iep[], Real *x[], string );


   void cPolyset3d::autos( cInterp3d* srf0 )
  {
      Int is,iv,ix,ib;
      Int iv00,iv01,iv11,iv10;
      Real       *s;
      Real       *zo[2];
      Real        y1[2],y0[2],dy1[2],dy0[2];
      Real        x0[3],dx0[2][3];
      Real        x1[3],dx1[2][3];
      Real *tmp0[2],*tmp1[2];

      Real y[2],dy[2];
      Real x[3],dx[2][3];

      srf0->copy( srf+ns );
      is=ns;
      ns++;

      Real ymin[2]={0,0},ymax[2]={1,1};
      srf[is]->tria( ymin,ymax, 0.0099, nfp+is,yfp[is],xfp[is],nfe+is,ifep[is], nfb+is,ifbp[is],sfbp[is] );

/*    tecplot2( 0,nfe[is], 3,3, ifep[is], xfp[is], "3surface."+strc(is)+".tec" );
      tecplot2( 0,nfe[is], 2,3, ifep[is], yfp[is], "2surface."+strc(is)+".tec" );*/


      y[0]=0; y[1]= 0; 
      srf[is]->interp( y,x,dx[0],dx[1] );
      iv00= insert2( &nv,ivs,yv, is,y );
      ix= insert3( &nx,    xx, x );
      ivx[iv00]= ix;

      y[0]=1; y[1]= 0; 
      srf[is]->interp( y,x,dx[0],dx[1] );
      iv10= insert2( &nv,ivs,yv, is,y );
      ix= insert3( &nx,    xx, x );
      ivx[iv10]= ix;

      y[0]=1; y[1]= 1; 
      srf[is]->interp( y,x,dx[0],dx[1] );
      iv11= insert2( &nv,ivs,yv, is,y );
      ix= insert3( &nx,    xx, x );
      ivx[iv11]= ix;

      y[0]=0; y[1]= 1; 
      srf[is]->interp( y,x,dx[0],dx[1] );
      iv01= insert2( &nv,ivs,yv, is,y );
      ix= insert3( &nx,    xx, x );
      ivx[iv01]= ix;

      y0[0]= 0; y0[1]= 0.5;
      srf[is]->interp( y0,x0,dx[0],dx[1] );
      y1[0]= 1; y1[1]= 0.5;
      srf[is]->interp( y1,x1,dx[0],dx[1] );
      sub3( x0,x1, dx[0] );
      if( norminf3( dx[0] ) < ITPPTOL )
     {
         s= new Real[21];
         zo[0]= new Real[21];
         zo[1]= new Real[21];
         for( Int i=0;i<21;i++ )
        {
            s[i]= (Real)i/20.;
            zo[0][i]= 0;
            zo[1][i]= 1;
        }
         Real *tmp0[2],*tmp1[2];
         tmp0[0]= zo[1];
         tmp0[1]= s;
         tmp1[0]= zo[0];
         tmp1[1]= s;
         wdf[nd]= new cWedgeDef();
         wdf[nd]->build( true, (Int)21, tmp0,true, tmp1,true, srf[is],srf[is] );
         ids[0][nd]= is;
         ids[1][nd]= is;
         idv[0][nd]= iv10;
         idv[1][nd]= iv11;
         idv[2][nd]= iv00;
         idv[3][nd]= iv01;

         for( ib=0;ib<nfb[is];ib++ )
        {
            if( ifbp[is][2][ib] == -2 )
           {
               ifbp[is][2][ib]= nd;
               ifbp[is][3][ib]= 0;
           }
            if( ifbp[is][2][ib] == -4 )
           {
               ifbp[is][2][ib]= nd;
               ifbp[is][3][ib]= 1;
               sfbp[is][0][ib]= 1-sfbp[is][0][ib];
               sfbp[is][1][ib]= 1-sfbp[is][1][ib];
           }
        }
         nd++;
     }

      y0[0]= 0.5; y0[1]= 0;
      srf[is]->interp( y0,x0,dx[0],dx[1] );
      y1[0]= 0.5; y1[1]= 1;
      srf[is]->interp( y1,x1,dx[0],dx[1] );
      sub3( x0,x1, dx[0] );
      if( norminf3( dx[0] ) < ITPPTOL )
     {
         s= new Real[21];
         zo[0]= new Real[21];
         zo[1]= new Real[21];
         for( Int i=0;i<21;i++ )
        {
            s[i]= (Real)i/20.;
            zo[0][i]= 0;
            zo[1][i]= 1;
        }
         Real *tmp0[2],*tmp1[2];
         tmp0[0]= s;
         tmp0[1]= zo[0];
         tmp1[0]= s;
         tmp1[1]= zo[1];
         wdf[nd]= new cWedgeDef();
         wdf[nd]->build( true, (Int)21, tmp0,true, tmp1,true, srf[is],srf[is] );
         ids[0][nd]= is;
         ids[1][nd]= is;
         idv[0][nd]= iv00;
         idv[1][nd]= iv10;
         idv[2][nd]= iv01;
         idv[3][nd]= iv11;
         for( ib=0;ib<nfb[is];ib++ )
        {
            if( ifbp[is][2][ib] == -1 )
           {
               ifbp[is][2][ib]= nd;
               ifbp[is][3][ib]= 0;
           }
            if( ifbp[is][2][ib] == -3 )
           {
               ifbp[is][2][ib]= nd;
               ifbp[is][3][ib]= 1;
               sfbp[is][0][ib]= 1-sfbp[is][0][ib];
               sfbp[is][1][ib]= 1-sfbp[is][1][ib];
           }
        }
         nd++;
     }

       

  }
