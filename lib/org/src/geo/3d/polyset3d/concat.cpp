   using namespace std;

#  include <geo/3d/polyset3d.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Sat Jan 15 19:26:11 GMT 2011
// Changes History -
// Next Change(s)  -

   void concat1( Int n, Int *iwrk[], Int *nl, Int *lprm )
  {
      Int  i,j,inxt,iprv,ist,ien;
      Int *iprm;

      iprm= new Int[n];
      for( i=0;i<n;i++ )
     {
         iprm[i]= i;
     }

      ien= 0;
     *nl=0;
      while( ien < n )
     {
         ist= ien;
         iprv= nextI( ien+1,n, ist, iwrk[0],iwrk[1],iprm );
         while( iprv != -1 )
        {
            ien++;
            swap( iprm+ien,iprm+iprv );
            shftc( ist,ien+1,iprm );
            iprv= nextI( ien+1,n, ist, iwrk[0],iwrk[1],iprm );
        }
         inxt= nextI( ien+1,n, ien, iwrk[1],iwrk[0],iprm );
         while( inxt != -1 )
        {
            ien++;
            swap( iprm+ien,iprm+inxt );
            inxt= nextI( ien+1,n, ien, iwrk[1],iwrk[0],iprm );
        }
         ien++;

         Int ntmp=0;
         Int ltmp[100];
         for( i=ist+1;i<ien;i++ )
        {
            if( ( iwrk[3][iprm[i-1]] != iwrk[2][iprm[i]] ) ||
                ( iwrk[5][iprm[i-1]] != iwrk[4][iprm[i]] ) )
           {
               ltmp[ntmp]= i;
               ntmp++;
               lprm[*nl]= i;
              (*nl)++;
           }
        }
         lprm[*nl]= ien;
        (*nl)++;
     }

      permute( n, iwrk[0], iprm );
      permute( n, iwrk[1], iprm );
      permute( n, iwrk[2], iprm );
      permute( n, iwrk[3], iprm );
      permute( n, iwrk[4], iprm );
      permute( n, iwrk[5], iprm );

      delete[] iprm;
  }
