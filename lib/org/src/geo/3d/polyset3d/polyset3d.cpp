   using namespace std;

#  include <utils/proto.h>
#  include <sort/proto.h>
#  include <geo/3d/polyset3d.h>

   cPolyset3d::cPolyset3d()
  {
      nullify();
  };

   void cPolyset3d::nullify()
  {
      dbg= false;
      dbgstr= "";

      nl= 0;
      nx= 0;
      nv= 0;
      ns= 0;
      nw= 0;
      np= 0;

      iwd[0]= NULL;
      iwd[1]= NULL;
      iws[0]= NULL;
      iws[1]= NULL;
      iwv[0]= NULL;
      iwv[1]= NULL;
      itw= NULL;
      ilw= NULL;
      iwl= NULL;

      llw= NULL;
      jlp= NULL;


      llp= NULL;
      itw= NULL;
      idx= NULL;

      ibs= NULL;
      iis= NULL;

      ilp[0]= NULL;
      ilp[1]= NULL;

      lpl= NULL;
      ipl= NULL;

      ids[0]=NULL;
      ids[1]=NULL;
      idv[0]=NULL;
      idv[1]=NULL;
      idv[2]=NULL;
      idv[3]=NULL;

      xx[0]= NULL;
      xx[1]= NULL;
      xx[2]= NULL;

      yv[0]= NULL;
      yv[1]= NULL;
      ivs=   NULL;
      ivx=   NULL;

      comd= NULL;
      comw= NULL;
      comv= NULL;

      wdf= NULL;
      srf= NULL;

      nfs=NULL;
      nfp=NULL;
      nfe=NULL;
      nfb=NULL;

      yfp=NULL;
      xfp=NULL;
      ifsp=NULL;
      ifep=NULL;
      ifbp=NULL;
      sfbp=NULL;
  }

   cPolyset3d::~cPolyset3d()
  {
      Int is,id;

      delete[] itw;    itw= NULL;
      delete[] idx;    idx= NULL;
      delete[] llp;    llp= NULL;
      delete[] ilp[0]; ilp[0]= NULL;
      delete[] ilp[1]; ilp[1]= NULL;

      delete[] iis;    iis= NULL;
      delete[] ibs;    ibs= NULL;

      delete[] lpl; lpl=NULL;
      delete[] ipl; ipl=NULL;

      delete[] ids[0]; ids[0]= NULL;
      delete[] ids[1]; ids[1]= NULL;
      delete[] idv[0]; idv[0]= NULL;
      delete[] idv[1]; idv[1]= NULL;
      delete[] idv[2]; idv[2]= NULL;
      delete[] idv[3]; idv[3]= NULL;

      delete[] iwd[0]; iwd[0]= NULL;
      delete[] iwd[1]; iwd[1]= NULL;
      delete[] iws[0]; iws[0]= NULL;
      delete[] iws[1]; iws[1]= NULL;
      delete[] iwv[0]; iwv[0]= NULL;
      delete[] iwv[1]; iwv[1]= NULL;
      delete[] itw; itw= NULL;
      delete[] ilw; ilw= NULL;
      delete[] iwl; iwl= NULL;

      delete[] xx[0]; xx[0]= NULL;
      delete[] xx[1]; xx[1]= NULL;
      delete[] xx[2]; xx[2]= NULL;

      delete[] yv[0]; yv[0]= NULL;
      delete[] yv[1]; yv[1]= NULL;
      delete[] ivs;   ivs= NULL;
      delete[] ivx;   ivx= NULL;

      delete[] comd; comd= NULL;
      delete[] comw; comw= NULL;
      delete[] comv; comv= NULL;

      delete[] llw; llw= NULL;
      delete[] jlp; jlp= NULL;

      for( id=0;id<nd;id++ )
     {
         delete wdf[id]; wdf[id]= NULL;
     }
      delete[] wdf;
      for( is=0;is<ns;is++ )
     {
         delete srf[is]; srf[is]= NULL;
     }
      delete[] srf;

      if( nfs )
     {
         for( is=0;is<ns;is++ )
        {
            nfs[is]=0;
            nfp[is]=0;
            nfe[is]=0;
            nfb[is]=0;
            delete[] yfp[is][0];
            delete[] yfp[is][1];
            delete[] xfp[is][0];
            delete[] xfp[is][1];
            delete[] xfp[is][2];
            delete[] ifsp[is][0];
            delete[] ifsp[is][1];
            delete[] ifep[is][0];
            delete[] ifep[is][1];
            delete[] ifep[is][2];
            delete[] ifbp[is][0];
            delete[] ifbp[is][1];
            delete[] ifbp[is][2];
            delete[] ifbp[is][3];
            delete[] sfbp[is][0];
            delete[] sfbp[is][1];
        }
     }

      delete[] nfs;
      delete[] nfp;
      delete[] nfe;
      delete[] nfb;
      delete[] yfp;
      delete[] xfp;
      delete[] ifsp;
      delete[] ifep;
      delete[] ifbp;
      delete[] sfbp;

      nl= 0;
      ns= 0;
      nw= 0;
      np= 0;
  }

   void cPolyset3d::build( Int nv0, Int *ivs0, Real *yv0[], Int nd0, Int *ids0[], Int *idv0[], cWedgeDef *wdf0[], Int ns0, cInterp3d *srf0[] )
  {
      Int  iv,is,id,iw,jw,il,jl,ist,ien;
      Real x[3],dx[2][3];
      Real y[2];
      Int  nstk;
      Int *istk;

      ns= ns0;
      srf= new cInterp3d*[ns];
      for( is=0;is<ns;is++ )
     {
         srf[is]= srf0[is];
     }

/*    nv= nv0;
      xv[0]= new Real[nv];
      xv[1]= new Real[nv];
      xv[2]= new Real[nv];
      comv= new string[nv];
      for( iv=0;iv<nv;iv++ )
     {
         xv[0][iv]= xv0[0][iv];
         xv[1][iv]= xv0[1][iv];
         xv[2][iv]= xv0[2][iv];
         comv[iv]= "vertex "+strc(iv);
     }*/

      nx=0;
      xx[0]= new Real[1000];
      xx[1]= new Real[1000];
      xx[2]= new Real[1000];
      

      nv= nv0;
      yv[0]= new Real[nv];
      yv[1]= new Real[nv];
      ivs=   new Int[nv];
      ivx=   new Int[nv];
      comv= new string[nv];
      for( iv=0;iv<nv;iv++ )
     {
         yv[0][iv]= yv0[0][iv];
         yv[1][iv]= yv0[1][iv];
         ivs[iv]= ivs0[iv];
         comv[iv]= "vertex "+strc(iv);
         is= ivs[iv];
         line2( iv,yv, y );
         srf[is]->interp( y, x,dx[0],dx[1] );
         ivx[iv]= insert3( &nx,xx, x );
     }
      vsummary();

      nd= nd0;
      wdf= new cWedgeDef*[nd];
      for( id=0;id<nd;id++ )
     {
         wdf[id]= wdf0[id];
     }
      ids[0]= new Int[nd];
      ids[1]= new Int[nd];
      idv[0]= new Int[nd];
      idv[1]= new Int[nd];
      idv[2]= new Int[nd];
      idv[3]= new Int[nd];
      comd= new string[nd];
      for( id=0;id<nd;id++ )
     {
         ids[0][id]= ids0[0][id];
         ids[1][id]= ids0[1][id];
         idv[0][id]= idv0[0][id];
         idv[1][id]= idv0[1][id];
         idv[2][id]= idv0[2][id];
         idv[3][id]= idv0[3][id];
         comd[id]= "w-edge definition "+strc(id);
     }
      dsummary();

      build();

  }

   void cPolyset3d::build()
  {

      wedges();
      wsummary();
      
      loops();
      lsummary();

      polyhedra();
      psummary();
  }

   void cPolyset3d::summary()
  {
      cout << "\n"; 
      vsummary();
      cout << "\n"; 
      dsummary();
      cout << "\n"; 
      wsummary();
      cout << "\n"; 
      lsummary();
      cout << "\n"; 
      psummary();
  }

   void cPolyset3d::vsummary()
  {
      Int iv,ix;
      cout.setf( ios_base::scientific );
      cout.width( 15 );
      cout.precision( 6 );
      cout << "\n";
      cout << "true vertex summary - polyhedron set "<<this<<"\n";
      for( ix=0;ix<nx;ix++ )
     {
         cout << "vertex: " << ix;
         cout << " position "<< xx[0][ix]<<" "<<xx[1][ix]<<" "<<xx[2][ix]<<"\n";
     }
      cout << "\n";
      cout << "vertex summary - polyhedron set "<<this<<"\n";
      for( iv=0;iv<nv;iv++ )
     {
         cout << "vertex: " << iv;
         cout << " surface " << ivs[iv];
         cout << " true vertex "<< ivx[iv];
         cout << " position "<< yv[0][iv]<<" "<<yv[1][iv];
         cout <<" # "<<comv[iv]<<"\n";
     }
  }

   void cPolyset3d::dsummary()
  {
      Int id;
      cout << "\n";
      cout << "w-edge definition summary - polyhedron set "<<this<<"\n";
      for( id=0;id<nd;id++ )
     {
         cout << "w-edge definition "<<id;
         cout <<" surfaces "<< ids[0][id]<<" "<<ids[1][id];
         cout <<" vertices (0) "<< idv[0][id]<<" "<<idv[1][id];
         cout <<" vertices (1) "<< idv[2][id]<<" "<<idv[3][id];
         cout <<" vertices (0) "<< ivx[idv[0][id]]<<" "<<ivx[idv[1][id]];
         cout <<" vertices (1) "<< ivx[idv[2][id]]<<" "<<ivx[idv[3][id]];
         cout << " # " << comd[id] <<"\n" ;

     }
  }

   void cPolyset3d::wsummary()
  {
      Int iw;
      cout << "\n";
      cout << "w-edge summary - polyhedron set "<<this<<"\n";
      for( iw=0;iw<nw;iw++ )
     {
         cout << "w-edge "<<iw;
         cout << " definition "<<iwd[0][iw]<<" "<<iwd[1][iw];
         cout << " surfaces "<<iws[0][iw]<<" "<<iws[1][iw];
         cout << " vertices "<< iwv[0][iw]<<" "<<iwv[1][iw];
         cout << " # "<< comw[iw]<<"\n";
     }
  }

   void cPolyset3d::lsummary()
  {
      Int iw,jw,il;
      Int ist,ien;
      cout << "\n";
      cout << "loop summary - polyhedron set "<<this<<"\n";
      ien=0;
      for( il=0;il<nl;il++ )
     {
         ist= ien;
         ien= llw[il];
         cout << "w-edges in loop "<<il<<"\n";
         for( jw=ist;jw<ien;jw++ )
        {
            iw= ilw[jw];
            cout << iw <<" ( "<<iwv[0][iw]<<"-"<<iwv[1][iw]<<" on "<<iws[0][iw]<<"/"<<iws[1][iw]<<")\n"; 
        }
//       cout << "loop is earmarked for removal "<<ilrm[il]<<"\n";
     }
  }

   void cPolyset3d::psummary()
  {
      Int il,jl,ip;
      Int ist,ien;
      cout << "\n";
      cout << "polyhedron summary - polyhedron set "<<this<<"\n";
      ien=0;
      for( ip=0;ip<np;ip++ )
     {
         ist= ien;
         ien= lpl[ip];
         cout << "loops in polyhedron "<<ip<<"\n";
         for( jl=ist;jl<ien;jl++ )
        {
            il= ipl[jl];
            cout << il <<" ";
        }
         cout << "\n";
     }
  }
  
   void cPolyset3d::build( cPolyset3d *p0, cPolyset3d *p1 )
  {
      Int ix,iv,id,is;
      Int nx0,nv0,nd0,ns0;

      wdf= new cWedgeDef*[1000]; setv( 0,1000, (cWedgeDef*)NULL, wdf );
      srf= new cInterp3d*[1000]; setv( 0,1000, (cInterp3d*)NULL, srf );


// merge the definitions of the two polyhedra

// surfaces
      ns=0;
      for( is=0;is<p0->ns;is++ )
     {
         p0->srf[is]->copy( srf+ns );
         ns++;
     }
      ns0= ns;
      for( is=0;is<p1->ns;is++ )
     {
         p1->srf[is]->copy( srf+ns );
         ns++;
     }

// true vertices
      nx=0;
      xx[0]= new Real[1000];
      xx[1]= new Real[1000];
      xx[2]= new Real[1000];
      for( ix=0;ix<p0->nx;ix++ )
     {
         xx[0][nx]= p0->xx[0][ix];
         xx[1][nx]= p0->xx[1][ix];
         xx[2][nx]= p0->xx[2][ix];
         nx++;
     }
      nx0= nx;
      for( ix=0;ix<p1->nx;ix++ )
     {
         xx[0][nx]= p1->xx[0][ix];
         xx[1][nx]= p1->xx[1][ix];
         xx[2][nx]= p1->xx[2][ix];
         nx++;
     }

// vertices

      yv[0]= new Real[1000];
      yv[1]= new Real[1000];
      ivs= new Int[1000];
      ivx= new Int[1000];
//    ivrem=new Int[1000];
      comv= new string[1000];

      nv= 0;
      for( iv=0;iv<p0->nv;iv++ )
     {
         yv[0][nv]= p0->yv[0][iv];
         yv[1][nv]= p0->yv[1][iv];
         ivs[nv]= p0->ivs[iv];
         ivx[nv]= p0->ivx[iv];
         comv[nv]= "vertex "+strc(iv)+" from polyhedron 0";
         nv++;
     }
      nv0= nv;
      for( iv=0;iv<p1->nv;iv++ )
     {
         yv[0][nv]= p1->yv[0][iv];
         yv[1][nv]= p1->yv[1][iv];
         ivs[nv]= p1->ivs[iv]+ns0;
         ivx[nv]= p1->ivx[iv]+nx0;
         comv[nv]= "vertex "+strc(iv)+" from polyhedron 1";
         nv++;
     }


// w-edge defintions

      ids[0]= new Int[1000];
      ids[1]= new Int[1000];
      idv[0]= new Int[1000];
      idv[1]= new Int[1000];
      idv[2]= new Int[1000];
      idv[3]= new Int[1000];
      comd= new string[1000];

      nd=0;
      for( id=0;id<p0->nd;id++ )
     {
         ids[0][nd]= p0->ids[0][id];
         ids[1][nd]= p0->ids[1][id];
         idv[0][nd]= p0->idv[0][id];
         idv[1][nd]= p0->idv[1][id];
         idv[2][nd]= p0->idv[2][id];
         idv[3][nd]= p0->idv[3][id];
//       wdf[nd]=    p0->wdf[id];
        (p0->wdf[id])->copy( wdf+nd );

         comd[nd]= "w-edge definition "+strc(id)+" from polyhedron 0";
         nd++;
     }
      nd0= nd;
      for( id=0;id<p1->nd;id++ )
     {
         ids[0][nd]= p1->ids[0][id]+ ns0;
         ids[1][nd]= p1->ids[1][id]+ ns0;
         idv[0][nd]= p1->idv[0][id]+ nv0;
         idv[1][nd]= p1->idv[1][id]+ nv0;
         idv[2][nd]= p1->idv[2][id]+ nv0;
         idv[3][nd]= p1->idv[3][id]+ nv0;
//       wdf[nd]=    p1->wdf[id];
        (p1->wdf[id])->copy( wdf+nd );
         comd[nd]= "w-edge definition "+strc(id)+" from polyhedron 1";
         nd++;
     }
  }

   void cPolyset3d::tecplot( string fnme )
  {
      ofstream fle;
      Int is,ip,ie,i0,i1,i2;
      for( is=0;is<ns;is++ )
     {
         string nme;
         nme= fnme+".space.triangulation."+strc(is)+".tec";

         fle.open( nme.c_str() );
         fle << "VARIABLES = " << " \"X\"," << "\"Y\"," << "\"Z\"" << "\n";
         fle << "ZONE" << " " << "N=" << nfp[is] << " " << "E=" << nfe[is] << " " << "F=FEPOINT" << " " << "ET=QUADRILATERAL\n";
         for(ip=0; ip<nfp[is]; ip++)
        {
            fle << xfp[is][0][ip] << " " << xfp[is][1][ip] << " " << xfp[is][2][ip] << " "  << "\n";
        }

         fle << "\n";
         for( ie=0;ie<nfe[is];ie++ )
        {
            i0= ifep[is][0][ie];
            i1= ifep[is][1][ie];
            i2= ifep[is][2][ie];

            fle << i0+1<<" "<<i1+1<<" "<<i2+1<<" "<<i0+1<<"\n";
        }
         fle.close();

         nme= fnme+".surface.triangulation."+strc(is)+".tec";

         fle.open( nme.c_str() );
         fle << "VARIABLES = " << " \"X\"," << "\"Y\"," << "\n";
         fle << "ZONE" << " " << "N=" << nfp[is] << " " << "E=" << nfe[is] << " " << "F=FEPOINT" << " " << "ET=QUADRILATERAL\n";
         for(ip=0; ip<nfp[is]; ip++)
        {
            fle << yfp[is][0][ip] << " " << yfp[is][1][ip] << "\n";
        }

         fle << "\n";
         for( ie=0;ie<nfe[is];ie++ )
        {
            i0= ifep[is][0][ie];
            i1= ifep[is][1][ie];
            i2= ifep[is][2][ie];

            fle << i0+1<<" "<<i1+1<<" "<<i2+1<<" "<<i0+1<<"\n";
        }
         fle.close();
      }
  }

   void cPolyset3d::gnuplot( string fnme )
  {
      ofstream fle;
      Int is,ip,ie,i0,i1,i2;
      for( is=0;is<ns;is++ )
     {
         string nme;
         nme= fnme+".space.triangulation."+strc(is)+".dat";

         fle.open( nme.c_str() );
         for( ie=0;ie<nfe[is];ie++ )
        {
            i0= ifep[is][0][ie];
            i1= ifep[is][1][ie];
            i2= ifep[is][2][ie];

            fle << "\n";
            fle << "#\n";
            fle << "\n";

            fle << xfp[is][0][i0] << " " << xfp[is][1][i0] << " " << xfp[is][2][i0] << " "  << "\n";
            fle << xfp[is][0][i1] << " " << xfp[is][1][i1] << " " << xfp[is][2][i1] << " "  << "\n";
            fle << xfp[is][0][i2] << " " << xfp[is][1][i2] << " " << xfp[is][2][i2] << " "  << "\n";
            fle << xfp[is][0][i0] << " " << xfp[is][1][i0] << " " << xfp[is][2][i0] << " "  << "\n";
        }
         fle.close();

         nme= fnme+".surface.triangulation."+strc(is)+".tec";

         fle.open( nme.c_str() );
         for( ie=0;ie<nfe[is];ie++ )
        {
            i0= ifep[is][0][ie];
            i1= ifep[is][1][ie];
            i2= ifep[is][2][ie];

            fle << "\n";
            fle << "#\n";
            fle << "\n";

            fle << yfp[is][0][i0] << " " << yfp[is][1][i0] << "\n";
            fle << yfp[is][0][i1] << " " << yfp[is][1][i1] << "\n";
            fle << yfp[is][0][i2] << " " << yfp[is][1][i2] << "\n";
            fle << yfp[is][0][i0] << " " << yfp[is][1][i0] << "\n";

        }
         fle.close();
      }
  }

   void cPolyset3d::init()
  {
      Int ix,iv,id,is;
      Int nx0,nv0,nd0,ns0;

      wdf= new cWedgeDef*[1000]; setv( 0,1000, (cWedgeDef*)NULL, wdf );
      srf= new cInterp3d*[1000]; setv( 0,1000, (cInterp3d*)NULL, srf );


// merge the definitions of the two polyhedra

// surfaces
      ns=0;

// true vertices
      nx=0;
      xx[0]= new Real[1000];
      xx[1]= new Real[1000];
      xx[2]= new Real[1000];

// vertices

      nv=0;
      yv[0]= new Real[1000];
      yv[1]= new Real[1000];
      ivs= new Int[1000];
      ivx= new Int[1000];
//    ivrem=new Int[1000];
      comv= new string[1000];

// w-edge defintions

      nd=0;
      ids[0]= new Int[1000];
      ids[1]= new Int[1000];
      idv[0]= new Int[1000];
      idv[1]= new Int[1000];
      idv[2]= new Int[1000];
      idv[3]= new Int[1000];
      comd= new string[1000];

      nfs= new Int[1000];
      nfp= new Int[1000];
      nfe= new Int[1000];
      nfb= new Int[1000];

      yfp= new real2_t[1000];
      xfp= new real3_t[1000];
      ifsp= new int2_t[1000];
      ifep= new int3_t[1000];
      ifbp= new int4_t[1000];
      sfbp= new real2_t[1000];


  }
