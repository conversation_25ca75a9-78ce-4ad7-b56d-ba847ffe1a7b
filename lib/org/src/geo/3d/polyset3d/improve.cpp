   using namespace std;

#  include <geo/3d/polyset3d.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Sat Jan 15 19:30:00 GMT 2011
// Changes History
// Next Change(s)  -
   void cPolyset3d::improve( Int ist, Int ien, Int *iwrk[], Real *rwrk[], Int is, Int js, 
                             Int *icap, Int *jcap, Real *scap, Int *ntmp, Real *tmp[] )
  {
      Int ic;
      Int id,idr,idrs;
      Int is0,is1;
      Int i0,i1,i2;
      Int ks;
      Real s;
      Real y0[2],y1[2],y2[2];
      Real x0[3],x1[3],x2[3];
      Real dy0[2],dy1[2],dy2[2];
      Real dx0[2][3],dx1[2][3],dx2[2][3];
      Real dx[3];
      inter3d_t stat;

      cout << "\n";
      cout << "\n";
      i0= iwrk[2][ist];
      i1= iwrk[4][ist];
      i2= iwrk[0][ist];
      for( ic=0;ic<2;ic++ )
     {
         cout << "\n";
         id= icap[ic];
         idr= jcap[ic];
         if( id != -1 ) 
        {
            is0= ids[0][id];
            is1= ids[1][id];
            s= scap[ic];
            idr= jcap[ic];
            if( idr == 0 )
           { 
               idr =1; 
               idrs=0;
               ks= ids[idr][id];
               line2( i0,rwrk+3, y0 );
               wdf[id]->interp2( s, y1,dy1, srf[is0],srf[is1], idr );
               line2( i1,rwrk+5, y2 );

               srf[is]->interp( y0, x0,dx0[0],dx0[1] ); 
               srf[ks]->interp( y1, x1,dx1[0],dx1[1] );  // this is the odd one out for the w-edge
               srf[js]->interp( y2, x2,dx2[0],dx2[1] ); 
               cout << "points on the surfaces seem to be\n";
               cout << y0[0]<<" "<<y0[1]<<"\n";
               cout << y1[0]<<" "<<y1[1]<<"\n";
               cout << y2[0]<<" "<<y2[1]<<"\n";
               cout << "points in space seem to be \n";
               cout << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n"; 
               cout << x1[0]<<" "<<x1[1]<<" "<<x1[2]<<"\n"; 
               cout << x2[0]<<" "<<x2[1]<<" "<<x2[2]<<"\n"; 

               wdf[id]->inters1( srf[js], &s, y2, &stat, srf[is0],srf[is1] );

               wdf[id]->interp2( s, y0,dy0, srf[is0],srf[is1], idrs );
               wdf[id]->interp2( s, y1,dy1, srf[is0],srf[is1], idr  );

               rwrk[3][i0]= y0[0];
               rwrk[4][i0]= y0[1];
               rwrk[5][i1]= y2[0];
               rwrk[6][i1]= y2[1];
               srf[is]->interp( y0, x0,dx0[0],dx0[1] ); 
               srf[ks]->interp( y1, x1,dx1[0],dx1[1] );  // this is the odd one out for the w-edge
               srf[js]->interp( y2, x2,dx2[0],dx2[1] ); 


           }
            else
           { 
               idr= 0; 
               idrs=1;
               ks= ids[idr][id];
               wdf[id]->interp2( s, y0,dy0, srf[is0],srf[is1], idr );
               line2( i0,rwrk+3, y1 );
               line2( i1,rwrk+5, y2 );
               srf[ks]->interp( y0, x0,dx0[0],dx0[1] ); 
               srf[is]->interp( y1, x1,dx1[0],dx1[1] );  // this is the odd one out for the w-edge
               srf[js]->interp( y2, x2,dx2[0],dx2[1] ); 
               cout << "points on the surfaces seem to be\n";
               cout << y0[0]<<" "<<y0[1]<<"\n";
               cout << y1[0]<<" "<<y1[1]<<"\n";
               cout << y2[0]<<" "<<y2[1]<<"\n";
               cout << "points in space seem to be \n";
               cout << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n"; 
               cout << x1[0]<<" "<<x1[1]<<" "<<x1[2]<<"\n"; 
               cout << x2[0]<<" "<<x2[1]<<" "<<x2[2]<<"\n"; 

               wdf[id]->inters1( srf[js], &s, y2, &stat, srf[is0],srf[is1] );

               wdf[id]->interp2( s, y0,dy0, srf[is0],srf[is1], idr );
               wdf[id]->interp2( s, y1,dy1, srf[is0],srf[is1], idrs );

               rwrk[3][i0]= y1[0];
               rwrk[4][i0]= y1[1];
               rwrk[5][i1]= y2[0];
               rwrk[6][i1]= y2[1];
               srf[ks]->interp( y0, x0,dx0[0],dx0[1] ); 
               srf[is]->interp( y1, x1,dx1[0],dx1[1] );  // this is the odd one out for the w-edge
               srf[js]->interp( y2, x2,dx2[0],dx2[1] ); 

           }
            cout << "cap for surfaces "<<is<<" "<<js<<" "<<ks<<"\n";
            cout << "points on the surfaces are \n";
            cout << y0[0]<<" "<<y0[1]<<"\n";
            cout << y1[0]<<" "<<y1[1]<<"\n";
            cout << y2[0]<<" "<<y2[1]<<"\n";
            cout << "points in space are \n";
            cout << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n"; 
            cout << x1[0]<<" "<<x1[1]<<" "<<x1[2]<<"\n"; 
            cout << x2[0]<<" "<<x2[1]<<" "<<x2[2]<<"\n"; 
            rwrk[0][i2]= ( x0[0]+ x1[0]+ x2[0] )/3.;
            rwrk[1][i2]= ( x0[1]+ x1[1]+ x2[1] )/3.;
            rwrk[2][i2]= ( x0[2]+ x1[2]+ x2[2] )/3.;

        } 
         i0= iwrk[3][ien-1];
         i1= iwrk[5][ien-1];
         i2= iwrk[1][ien-1];
     }

      i0= iwrk[2][ist];
      i1= iwrk[4][ist];
      i2= iwrk[0][ist];
      for( ic=2;ic<4;ic++ )
     {
         cout << "\n";
         id= icap[ic];
         if( id != -1 ) 
        {
            is0= ids[0][id];
            is1= ids[1][id];
            s= scap[ic];
            idr= jcap[ic];
            if( idr == 0 )
           { 
               idr =1; 
               idrs=0;
               ks= ids[idr][id];
               line2( i1,rwrk+5, y0 );
               wdf[id]->interp2( s, y1,dy1, srf[is0],srf[is1], idr );
               line2( i0,rwrk+3, y2 );
               srf[js]->interp( y0, x0,dx0[0],dx0[1] ); 
               srf[ks]->interp( y1, x1,dx1[0],dx1[1] );  
               srf[is]->interp( y2, x2,dx2[0],dx2[1] ); // this is the odd one out for the w-edge

               cout << "points on the surfaces seem to be\n";
               cout << y0[0]<<" "<<y0[1]<<"\n";
               cout << y1[0]<<" "<<y1[1]<<"\n";
               cout << y2[0]<<" "<<y2[1]<<"\n";

               cout << "points in space seem to be \n";
               cout << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n"; 
               cout << x1[0]<<" "<<x1[1]<<" "<<x1[2]<<"\n"; 
               cout << x2[0]<<" "<<x2[1]<<" "<<x2[2]<<"\n"; 

               wdf[id]->inters1( srf[is], &s, y2, &stat, srf[is0],srf[is1] );

               wdf[id]->interp2( s, y1,dy1, srf[is0],srf[is1], idr );
               wdf[id]->interp2( s, y0,dy0, srf[is0],srf[is1], idrs );

               rwrk[3][i0]= y2[0];
               rwrk[4][i0]= y2[1];
               rwrk[5][i1]= y0[0];
               rwrk[6][i1]= y0[1];
               srf[js]->interp( y0, x0,dx0[0],dx0[1] ); 
               srf[ks]->interp( y1, x1,dx1[0],dx1[1] );  
               srf[is]->interp( y2, x2,dx2[0],dx2[1] ); // this is the odd one out for the w-edge

           }
            else
           { 
               idr= 0; 
               idrs=1;
               ks= ids[idr][id];
               wdf[id]->interp2( s, y0,dy0, srf[is0],srf[is1], idr );
               line2( i1,rwrk+5, y1 );
               line2( i0,rwrk+3, y2 );
               srf[ks]->interp( y0, x0,dx0[0],dx0[1] ); 
               srf[js]->interp( y1, x1,dx1[0],dx1[1] );  
               srf[is]->interp( y2, x2,dx2[0],dx2[1] ); // this is the odd one out for the w-edge
               cout << "points on the surfaces seem to be\n";
               cout << y0[0]<<" "<<y0[1]<<"\n";
               cout << y1[0]<<" "<<y1[1]<<"\n";
               cout << y2[0]<<" "<<y2[1]<<"\n";
               cout << "points in space seem to be \n";
               cout << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n"; 
               cout << x1[0]<<" "<<x1[1]<<" "<<x1[2]<<"\n"; 
               cout << x2[0]<<" "<<x2[1]<<" "<<x2[2]<<"\n"; 

               wdf[id]->inters1( srf[is], &s, y2, &stat, srf[is0],srf[is1] );

               wdf[id]->interp2( s, y0,dy0, srf[is0],srf[is1], idr );
               wdf[id]->interp2( s, y1,dy1, srf[is0],srf[is1], idrs );

               rwrk[3][i0]= y2[0];
               rwrk[4][i0]= y2[1];
               rwrk[5][i1]= y1[0];
               rwrk[6][i1]= y1[1];
               srf[ks]->interp( y0, x0,dx0[0],dx0[1] ); 
               srf[js]->interp( y1, x1,dx1[0],dx1[1] );  
               srf[is]->interp( y2, x2,dx2[0],dx2[1] ); // this is the odd one out for the w-edge
           }
            cout << "cap for surfaces "<<is<<" "<<js<<" "<<ks<<"\n";
            cout << "points on the surfaces are\n";
            cout << y0[0]<<" "<<y0[1]<<"\n";
            cout << y1[0]<<" "<<y1[1]<<"\n";
            cout << y2[0]<<" "<<y2[1]<<"\n";
            cout << "points in space are \n";
            cout << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n"; 
            cout << x1[0]<<" "<<x1[1]<<" "<<x1[2]<<"\n"; 
            cout << x2[0]<<" "<<x2[1]<<" "<<x2[2]<<"\n"; 
            rwrk[0][i2]= ( x0[0]+ x1[0]+ x2[0] )/3.;
            rwrk[1][i2]= ( x0[1]+ x1[1]+ x2[1] )/3.;
            rwrk[2][i2]= ( x0[2]+ x1[2]+ x2[2] )/3.;

        } 
         i0= iwrk[3][ien-1];
         i1= iwrk[5][ien-1];
         i2= iwrk[1][ien-1];
     }

      for( Int i=ist;i<ien-1;i++ )
     {

         i0= iwrk[0][i];
         i1= iwrk[1][i+1];
         i2= iwrk[1][i];//should be the same as iwrk[0][i+1];

         x0[0]= rwrk[ 0][i2];
         x0[1]= rwrk[ 1][i2];
         x0[2]= rwrk[ 2][i2];

         dx[0]=       rwrk[ 0][i1]- rwrk[ 0][i0];
         dx[1]=       rwrk[ 1][i1]- rwrk[ 1][i0];
         dx[2]=       rwrk[ 2][i1]- rwrk[ 2][i0];

         i2= iwrk[3][i];
         y0[0]= rwrk[ 3][i2];
         y0[1]= rwrk[ 4][i2];

         i2= iwrk[5][i];
         y1[0]= rwrk[ 5][i2];
         y1[1]= rwrk[ 6][i2];
 
         inters( srf[is], srf[js], x0,dx, y0,y1, &stat );

         srf[is]->interp( y0,x0,dx0[0],dx0[1] );
         srf[js]->interp( y1,x1,dx0[0],dx0[1] );

         i2= iwrk[1][i];
         rwrk[ 0][i2]= 0.5*( x0[0]+ x1[0] );
         rwrk[ 1][i2]= 0.5*( x0[1]+ x1[1] );
         rwrk[ 2][i2]= 0.5*( x0[2]+ x1[2] );

         i2= iwrk[3][i];
         rwrk[ 3][i2]= y0[0];
         rwrk[ 4][i2]= y0[1];

         i2= iwrk[5][i];
         rwrk[ 5][i2]= y1[0];
         rwrk[ 6][i2]= y1[1];

     }

      *ntmp=0;
                  
      Int i;
      for( Int j=ist;j<ien;j++ )
     {
         i= iwrk[2][j];
         tmp[0][*ntmp]= rwrk[3][i];
         tmp[1][*ntmp]= rwrk[4][i];
         i= iwrk[4][j];
         tmp[2][*ntmp]= rwrk[5][i];
         tmp[3][*ntmp]= rwrk[6][i];
       (*ntmp)++;
     }
      i= iwrk[3][ien-1];
      tmp[0][*ntmp]= rwrk[3][i];
      tmp[1][*ntmp]= rwrk[4][i];
      i= iwrk[5][ien-1];
      tmp[2][*ntmp]= rwrk[5][i];
      tmp[3][*ntmp]= rwrk[6][i];
    (*ntmp)++;

  }

