   using namespace std;
#  include <geo/3d/polyset3d.h>

#  define MN 1000
// remove from here

// void tecplot2( Int ies, Int iee, Int nv, Int nep, Int *iep[], Real *x[], string );


   void cPolyset3d::crossings1( cSections *p0, cInterp3d *p1[], bool uni )
  {
 
      Real           s00[3],s10[3],s01[3],s11[3];
      Real           z00,z10,z01,z11;
      Real           d00,d10,d01,d11;
      Real           xmin0[3],xmax0[3], xmin1[3],xmax1[3];
      Real           x00[3], x01[3], x02[3], x10[3], x11[3], x12[3];
      Real           y00[2], y01[2], y02[2], y10[2], y11[2], y12[2];

      Int            i0,i1,i2;
      Real           s0,s1,d0;
      Int            iw,ix,iv,is,ie,js,je,id,idr,ix0,ix1;
      Int            iv0,iv1,jv0;
      Int            iv00,iv01, iv10,iv11;
      Int            id00,id01, id10,id11;
      Int            idr00,idr01, idr10,idr11;
      Int            ist,ien;

      Int            nv0,ns0,nd0;

      inter3d_t      stat;
 
      Real           y0[2],y1[2];
      Real           x0[3],x1[3];
      Real           dx0[2][3];

      Int            ni;
      Real          *rwrk[7];
      Int           *iwrk[6];
      Int            na,nx0,nx1,nxi;
      Int            lprm[1000];

      Int            icap[4];
      Int            jcap[4];
      Real           scap[4];
      Real           dcap[4];

      Int            nsplt;
      Int           *isplt[4];
      Real          *ssplt[2];

      Int            ntmp;
      Real          *tmp[4];

      ofstream       fle2,fle3;
      string         fnme2,fnme3;

      iwrk[ 0]= new Int[1000];
      iwrk[ 1]= new Int[1000];
      iwrk[ 2]= new Int[1000];
      iwrk[ 3]= new Int[1000];
      iwrk[ 4]= new Int[1000];
      iwrk[ 5]= new Int[1000];

      rwrk[ 0]= new Real[1000];
      rwrk[ 1]= new Real[1000];
      rwrk[ 2]= new Real[1000];
      rwrk[ 3]= new Real[1000];
      rwrk[ 4]= new Real[1000];
      rwrk[ 5]= new Real[1000];
      rwrk[ 6]= new Real[1000];

      tmp[0]= new Real[1000];
      tmp[1]= new Real[1000];
      tmp[2]= new Real[1000];
      tmp[3]= new Real[1000];

      isplt[0]= new Int[1000];
      isplt[1]= new Int[1000];
      isplt[2]= new Int[1000];
      isplt[3]= new Int[1000];
      ssplt[0]= new Real[1000];
      ssplt[1]= new Real[1000];


// merge the definitions of the two polyhedra
      nullify();

      init();
      autos( p0 );

      nx0= nx;
      nv0= nv;
      ns0= ns;
      nd0= nd;

      autos( p1[0] );
      autos( p1[1] );

      cout << "\n";
      vsummary();
      dsummary();
      cout << "\n";

      nsplt=0;
      for( is=0;is<ns0;is++ )
     {
         for( js=ns0;js<ns;js++ )
        {
            na=0;
            nxi=0;
            nx0=0;
            nx1=0;
            for( ie=0;ie<nfe[is];ie++ )
           {
               i0= ifep[is][0][ie];
               i1= ifep[is][1][ie];
               i2= ifep[is][2][ie];

               line3( i0,xfp[is],x00 );
               line3( i1,xfp[is],x01 );
               line3( i2,xfp[is],x02 );
               line2( i0,yfp[is],y00 );
               line2( i1,yfp[is],y01 );
               line2( i2,yfp[is],y02 );

               box3( x00,x01,x02, xmin0,xmax0 );
               xmin0[0]-= 1.e-7;
               xmin0[1]-= 1.e-7;
               xmin0[2]-= 1.e-7;
               xmax0[0]+= 1.e-7;
               xmax0[1]+= 1.e-7;
               xmax0[2]+= 1.e-7;

               for( je=0;je<nfe[js];je++ )
              {
                  i0= ifep[js][0][je];
                  i1= ifep[js][1][je];
                  i2= ifep[js][2][je];

                  line3( i0,xfp[js],x10 );
                  line3( i1,xfp[js],x11 );
                  line3( i2,xfp[js],x12 );
                  line2( i0,yfp[js],y10 );
                  line2( i1,yfp[js],y11 );
                  line2( i2,yfp[js],y12 );

                  box3( x10,x11,x12, xmin1,xmax1 );
                  xmin1[0]-= 1.e-7;
                  xmin1[1]-= 1.e-7;
                  xmin1[2]-= 1.e-7;
                  xmax1[0]+= 1.e-7;
                  xmax1[1]+= 1.e-7;
                  xmax1[2]+= 1.e-7;
                  if( inters3( xmin0,xmax0, xmin1,xmax1 ) )
                 {
                     ttint( x00,x01,x02, x10,x11,x12, s00,s01, s10,s11, &stat );
                     if( fabs( stat.det ) > small )
                    {
                        Real ds[2];
                        sub2( s00,s01, ds );
                        if( norminf2( ds ) > small )
                       {
                           s00[2]= 1-s00[0]-s00[1];
                           s01[2]= 1-s01[0]-s01[1];

                           s10[2]= 1-s10[0]-s10[1];
                           s11[2]= 1-s11[0]-s11[1];

                           x0[0]= s00[0]*x00[0]+ s00[1]*x01[0]+ s00[2]*x02[0];
                           x0[1]= s00[0]*x00[1]+ s00[1]*x01[1]+ s00[2]*x02[1];
                           x0[2]= s00[0]*x00[2]+ s00[1]*x01[2]+ s00[2]*x02[2];

                           x1[0]= s01[0]*x00[0]+ s01[1]*x01[0]+ s01[2]*x02[0];
                           x1[1]= s01[0]*x00[1]+ s01[1]*x01[1]+ s01[2]*x02[1];
                           x1[2]= s01[0]*x00[2]+ s01[1]*x01[2]+ s01[2]*x02[2];

                           ix0= insert3( &nxi, rwrk+0, x0 );
                           ix1= insert3( &nxi, rwrk+0, x1 );

                           iwrk[0][na]= ix0;
                           iwrk[1][na]= ix1;

                           x0[0]= s00[0]*y00[0]+ s00[1]*y01[0]+ s00[2]*y02[0];
                           x0[1]= s00[0]*y00[1]+ s00[1]*y01[1]+ s00[2]*y02[1];

                           x1[0]= s01[0]*y00[0]+ s01[1]*y01[0]+ s01[2]*y02[0];
                           x1[1]= s01[0]*y00[1]+ s01[1]*y01[1]+ s01[2]*y02[1];

                           ix0= insert2( &nx0, rwrk+3, x0 );
                           ix1= insert2( &nx0, rwrk+3, x1 );

                           iwrk[2][na]= ix0;
                           iwrk[3][na]= ix1;

                           x0[0]= s10[0]*y10[0]+ s10[1]*y11[0]+ s10[2]*y12[0];
                           x0[1]= s10[0]*y10[1]+ s10[1]*y11[1]+ s10[2]*y12[1];

                           x1[0]= s11[0]*y10[0]+ s11[1]*y11[0]+ s11[2]*y12[0];
                           x1[1]= s11[0]*y10[1]+ s11[1]*y11[1]+ s11[2]*y12[1];

                           ix0= insert2( &nx1, rwrk+5, x0 );
                           ix1= insert2( &nx1, rwrk+5, x1 );

                           iwrk[4][na]= ix0;
                           iwrk[5][na]= ix1;

                           na++;

                       }
                    }
                 }
              }
           }

            cout << is << " "<< js <<" " << na << "\n";
            if( na > 0 )
           {

               cout << "\n";
               cout << "\n";
               cout << "\n";
               cout << "surfaces "<<is<<" "<<js<<" intersect in "<<nxi<<" points in space, ";
               cout << nx0 << "/"<<nx1<<" points in parametric space and "<<na<<" segments\n";
 
               fnme3= "inters3.raw."+strc(is)+"."+strc(js)+".dat";
               fle3.open( fnme3.c_str() );
               for( Int i=0;i<nxi;i++ )
              {
                  fle3 << rwrk[ 0][i]<<" "<<rwrk[ 1][i] <<" "<<rwrk[ 2][i]<<"\n";
              }
               fle3.close();

               fnme2= "inters2.raw.0."+strc(is)+"."+strc(js)+".dat";
               fle2.open( fnme2.c_str() );
               for( Int i=0;i<nx0;i++ )
              {
                  fle2 << rwrk[ 3][i]<<" "<<rwrk[ 4][i] <<"\n";
              }
               fle2.close();
               fnme2= "inters2.raw.1."+strc(is)+"."+strc(js)+".dat";
               fle2.open( fnme2.c_str() );
               for( Int i=0;i<nx1;i++ )
              {
                  fle2 << rwrk[ 5][i]<<" "<<rwrk[ 6][i] <<"\n";
              }
               fle2.close();

               concat1( na, iwrk, &ni, lprm );

               ien= 0;
               for( iw=0;iw<ni;iw++ )
              {
                  fnme3= "inters3."+strc(is)+"."+strc(js)+"."+strc(iw)+".dat";
                  fle3.open( fnme3.c_str() );
                  ist= ien;
                  ien= lprm[iw];
                  for( Int i=ist;i<ien;i++ )
                 {
                     fle3 <<"\n";
                     fle3 <<"#\n";
                     fle3 <<"\n";
                     i0= iwrk[0][i];
                     i1= iwrk[1][i];
                     fle3 << rwrk[ 0][i0]<<" "<<rwrk[ 1][i0]<<" "<<rwrk[ 2][i0]<<" "<<"\n";
                     fle3 << rwrk[ 0][i1]<<" "<<rwrk[ 1][i1]<<" "<<rwrk[ 2][i1]<<" "<<"\n";
                 }
                  fle3.close();
              }

               cout << "surfaces "<<is<<" "<<js<<" intersect in "<<ni<<" w-edges made from "<<na<<" triangle crossings\n"; 
               ien= 0;
               for( iw=0;iw<ni;iw++ )
              {
                  
                  ist=ien;
                  ien=lprm[iw];

                  i1= iwrk[2][ist];
                  i0= iwrk[3][ist];
                  line2( i0,   rwrk+3,  y0 );
                  line2( i1,   rwrk+3,  y1 );
                  cap( y0,y1, is, icap+0,jcap+0,scap+0,dcap+0 );
                  dcap[0]= -dcap[0];

                  i0= iwrk[2][ien-1];
                  i1= iwrk[3][ien-1];
                  line2( i0, rwrk+3,y0 );
                  line2( i1, rwrk+3,y1 );
                  cap( y0,y1, is, icap+1,jcap+1,scap+1,dcap+1 );

                  i1= iwrk[4][ist];
                  i0= iwrk[5][ist];
                  line2( i0,   rwrk+5,  y0 );
                  line2( i1,   rwrk+5,  y1 );
                  cap( y0,y1, js, icap+2,jcap+2,scap+2,dcap+2 );
//                if( icap[2] != -1 ){ icap[2]+= nd0; };

                  i0= iwrk[4][ien-1];
                  i1= iwrk[5][ien-1];
                  line2( i0, rwrk+5,y0 );
                  line2( i1, rwrk+5,y1 );
                  cap( y0,y1, js, icap+3,jcap+3,scap+3,dcap+3 );
//                if( icap[3] != -1 ){ icap[3]+= nd0; };
                  dcap[3]= -dcap[3];

                  improve( ist, ien, iwrk,rwrk, is,js, icap,jcap,scap, &ntmp,tmp );

                  fnme2= "inters2.definition.0."+strc(iw)+"."+strc(is)+"."+strc(js)+".dat";
                  fle2.open( fnme2.c_str() );
                  for( Int i=0;i<ntmp;i++ )
                 {
                     fle2 << tmp[ 0][i]<<" "<<tmp[ 1][i] <<"\n";
                 }
                  fle2.close();
                  fnme2= "inters2.definition.1."+strc(iw)+"."+strc(is)+"."+strc(js)+".dat";
                  fle2.open( fnme2.c_str() );
                  for( Int i=0;i<ntmp;i++ )
                 {
                     fle2 << tmp[ 2][i]<<" "<<tmp[ 3][i] <<"\n";
                 }
                  fle2.close();

                  iv0= iwrk[0][ist];
                  iv1= iwrk[1][ien-1];
                  line3( iv0,   rwrk+0,  x0 );
                  line3( iv1,   rwrk+0,  x1 );
                  iv0= insert3( &nx,xx, x0 );
                  iv1= insert3( &nx,xx, x1 );

                  jv0= iwrk[2][ist];
                  line2( jv0,   rwrk+3,  y0 );
                  iv00= insert2( &nv,ivs,yv, is,y0 );
                  ivx[iv00]= iv0;
                  comv[iv00]+= (" 00-crossing "+strc(is)+"/"+strc(js));


                  jv0= iwrk[3][ien-1];
                  line2( jv0,   rwrk+3,  y0 );
                  iv01= insert2( &nv,ivs,yv, is,y0 );
                  ivx[iv01]= iv1;
                  comv[iv01]+= (" 01-crossing "+strc(is)+"/"+strc(js));

                  jv0= iwrk[4][ist];
                  line2( jv0,   rwrk+5,  y0 );
                  iv10= insert2( &nv,ivs,yv, js,y0 );
                  ivx[iv10]= iv0;
                  comv[iv10]+= (" 10-crossing "+strc(is)+"/"+strc(js));

                  jv0= iwrk[5][ien-1];
                  line2( jv0,   rwrk+5,  y0 );
                  iv11= insert2( &nv,ivs,yv, js,y0 );
                  ivx[iv11]= iv1;
                  comv[iv11]+= (" 11-crossing "+strc(is)+"/"+strc(js));

//                ids[0][nd]= js+ns0;
                  ids[0][nd]= js;
                  ids[1][nd]= is;
                  idv[0][nd]= iv10;
                  idv[1][nd]= iv11;
                  idv[2][nd]= iv00;
                  idv[3][nd]= iv01;
                  if( !uni )
                 {
                    swap( idv[0]+nd,idv[1]+nd );
                    swap( idv[2]+nd,idv[3]+nd );
                 }
                  comd[nd]= "crossing "+strc(is)+" -> "+strc(js);

                  wdf[nd]= new cWedgeDef();
                  wdf[nd]->build( false, ntmp, tmp+2,true, tmp+0,true, srf[js],srf[is] );


                  fnme3= "inters3.defined."+strc(is)+"."+strc(js)+"."+strc(iw)+".dat";
                  fle3.open( fnme3.c_str() );
                  for( Int i=0;i<100;i++ )
                 {
                     Real w=(Real)i/99.; 
                     wdf[nd]->interp3( w,x0,dx0[0], srf[js],srf[is] ); 
                     fle3 << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n";
                 }
                  fle3.close();
                  nd++;

                  id= icap[0];
                  if( id != -1 )
                 {
                     idr=jcap[0];
                     isplt[0][nsplt]= id;
                     isplt[1][nsplt]= idr;
                     isplt[2][nsplt]= iv00;
                     isplt[3][nsplt]= iv10;
                     ssplt[0][nsplt]= scap[0];
                     ssplt[1][nsplt]= dcap[0];
                     nsplt++;    
                 }
                  id= icap[1];
                  if( id != -1 )
                 {
                     idr=jcap[1];
                     isplt[0][nsplt]= id;
                     isplt[1][nsplt]= idr;
                     isplt[2][nsplt]= iv01;
                     isplt[3][nsplt]= iv11;
                     ssplt[0][nsplt]= scap[1];
                     ssplt[1][nsplt]= dcap[1];
                     nsplt++;    
                 }
                  id= icap[2];
                  if( id != -1 )
                 {
                     idr=jcap[2];
                     isplt[0][nsplt]= id;
                     isplt[1][nsplt]= idr;
                     isplt[2][nsplt]= iv10;
                     isplt[3][nsplt]= iv00;
                     ssplt[0][nsplt]= scap[2];
                     ssplt[1][nsplt]= dcap[2];
                     nsplt++;    
                 }
                  id= icap[3];
                  if( id != -1 )
                 {
                     idr=jcap[3];
                     isplt[0][nsplt]= id;
                     isplt[1][nsplt]= idr;
                     isplt[2][nsplt]= iv11;
                     isplt[3][nsplt]= iv01;
                     ssplt[0][nsplt]= scap[3];
                     ssplt[1][nsplt]= dcap[3];
                     nsplt++;    
                 }
              }

               ien= 0;
               for( iw=0;iw<ni;iw++ )
              {
                  fnme3= "inters3.improved."+strc(is)+"."+strc(js)+"."+strc(iw)+".dat";
                  fle3.open( fnme3.c_str() );
                  ist= ien;
                  ien= lprm[iw];
                  for( Int i=ist;i<ien;i++ )
                 {
                     fle3 <<"\n";
                     fle3 <<"#\n";
                     fle3 <<"\n";
                     i0= iwrk[0][i];
                     i1= iwrk[1][i];
                     fle3 << rwrk[ 0][i0]<<" "<<rwrk[ 1][i0]<<" "<<rwrk[ 2][i0]<<" "<<"\n";
                     fle3 << rwrk[ 0][i1]<<" "<<rwrk[ 1][i1]<<" "<<rwrk[ 2][i1]<<" "<<"\n";
                 }
                  fle3.close();
              }
           }
        }
     }

  
      cout << "\n";
      cout << "\n";
      vsummary();
      dsummary();
      cout << "\n";
      cout << "\n";
      cout << "raw cuts\n";
      cout << "\n";

      split( nsplt, isplt, ssplt, uni );

      for( id=0;id<nd;id++ )
     {
         fnme3= "wedge."+strc(id)+".dat";
         fle3.open( fnme3.c_str() );
         for( Int i=0;i<1000;i++ )
        {
            Real w=(Real)i/999.;
            wdf[id]->interp3(w,x0,dx0[0], srf[ids[0][id]],srf[ids[1][id]] );
            fle3 << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n";
        }
         fle3.close();
     }
      cout << "\n";
      cout << "w-edge summary with removed split w-edges\n";
      cout << "\n";
      vsummary();
      dsummary();

      build();

      delete[] rwrk[ 0];
      delete[] rwrk[ 1];
      delete[] rwrk[ 2];
      delete[] rwrk[ 3];
      delete[] rwrk[ 4];
      delete[] rwrk[ 5];
      delete[] rwrk[ 6];

      delete[] isplt[0];
      delete[] isplt[1];
      delete[] isplt[2];
      delete[] isplt[3];

      delete[] ssplt[0];
      delete[] ssplt[1];

      delete[] iwrk[ 0];
      delete[] iwrk[ 1];
      delete[] iwrk[ 2];
      delete[] iwrk[ 3];
      delete[] iwrk[ 4];
      delete[] iwrk[ 5];

      delete[] tmp[0];
      delete[] tmp[1];
      delete[] tmp[2];
      delete[] tmp[3];

  }

