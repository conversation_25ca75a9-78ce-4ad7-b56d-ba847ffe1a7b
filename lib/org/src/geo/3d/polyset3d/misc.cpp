   using namespace std;

#  include <utils/proto.h>
#  include <sort/proto.h>
#  include <geo/3d/polyset3d.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Sat Jan 15 18:54:49 GMT 2011
// Changes History -
// Next Change(s)  -

   Int nextI( Int ist, Int ien, Int i0, Int *iprv, Int *inxt, Int *idnt, Int *iprm )
  {
      Int i,val;
      Int iv0,iv1;
      val= -1;
      iv1=  iprv[iprm[i0]];
      for( i=ist;i<ien;i++ )
     {
         if( idnt[iprm[i]] != idnt[iprm[i0]] )
        {
            iv0= inxt[iprm[i]];
            if( iv0 == iv1 )
           {
               val= i;
               break;
           }
        }
     } 
      return val;
  }

   Int nextI( Int ist, Int ien, Int i0, Int *iprv, Int *inxt, Int *iprm )
  {
      Int i,val;
      Int iv0,iv1;
      val= -1;
      iv1=  iprv[iprm[i0]];
      for( i=ist;i<ien;i++ )
     {
         iv0= inxt[iprm[i]];
         if( iv0 == iv1 )
        {
            val= i;
            break;
        }
     } 
      return val;
  }

   Int same( Int ist, Int ien, Int i0, Int *iks, Int *iprm )
  {
      Int val=-1;
      Int ik;
      ik= iprm[i0];
      ik= iks[ik];
      for( Int i=ist;i<ien;i++ )
     {
         Int j= iprm[i];
         if( iks[j] == ik )
        {
            val= i;
            break;
        }
     }
      return val;
  }

   bool tg( Int i0, Int i1, cPolyline *p[] )
  {
      Real y0[2],dy0[3];
      Real y1[2],dy1[3];
      Real cth;
      bool val;

      p[i0]->interp( 1., y0,dy0 );
      p[i1]->interp( 0., y1,dy1 );
   
      dy0[2]= norm22( dy0 ); sclv2( 1./dy0[2],dy0 );
      dy1[2]= norm22( dy1 ); sclv2( 1./dy1[2],dy1 );

      cth= dot2( dy0,dy1 );
      cth= 1-fabs( cth ); 

      val= ( fabs(cth) < small );
      return val;
       
  }
   Int insert3( Int *n, Real *x[], Real *x0 )
  {
      Int i;
      Int val=-1;
      Real x1[3];
      Real dx[3];
      for( i=0;i<*n;i++ )
     {
         line3( i,x,x1 );
         sub3( x0,x1, dx ); 
         if( norminf3( dx ) < 5*ITPPTOL )
        {
            val= i;
            break;
        }
     }
      if( val == -1 )
     {
         x[0][*n]= x0[0];
         x[1][*n]= x0[1];
         x[2][*n]= x0[2];
         val= (*n);
       (*n)++;
     }
      return val;
  }

   Int insert2( Int *n, Real *x[], Real *x0 )
  {
      Int i;
      Int val=-1;
      Real x1[2];
      Real dx[2];
      for( i=0;i<*n;i++ )
     {
         line2( i,x,x1 );
         sub2( x0,x1, dx ); 
         if( norminf2( dx ) < 5*ITPPTOL )
        {
            val= i;
            break;
        }
     }
      if( val == -1 )
     {
         x[0][*n]= x0[0];
         x[1][*n]= x0[1];
         val= (*n);
       (*n)++;
     }
      return val;
  }

   Int insert2( Int *n, Int *idnt, Real *x[], Int id0, Real *x0 )
  {
      Int i;
      Int val=-1;
      Real x1[2];
      Real dx[2];
      for( i=0;i<*n;i++ )
     {
         if( id0 == idnt[i] )
        {
            line2( i,x,x1 );
            sub2( x0,x1, dx ); 
            if( norminf2( dx ) < 5*ITPPTOL )
           {
               val= i;
               break;
           }
        }
     }
      if( val == -1 )
     {
         x[0][*n]= x0[0];
         x[1][*n]= x0[1];
         idnt[*n]= id0;
         val= (*n);
       (*n)++;
     }
      return val;
  }

   void select0( Int n, Int *m, Int *indx, Int *iprm )
  {
      Int i,j,k;
      j=0;
      k=n;
      for( i=0;i<n;i++ )
     {
         if( indx[i] == 0 )
        {
            iprm[j]= i; 
            indx[i]= j;
            j++;
        }
         else
        {
            k--;
            iprm[k]= i;
            indx[i]= k;
        }
     }
     *m=j;
  }
