   using namespace std;

#  include <geo/3d/polyset3d.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

   void cPolyset3d::wedges() 
  {

      Int  id,iw;

      nw= 2*nd;            

      iwd[0]= new Int[nw];
      iwd[1]= new Int[nw];
      iws[0]= new Int[nw];
      iws[1]= new Int[nw];
      iwv[0]= new Int[nw];
      iwv[1]= new Int[nw];
      itw= new Int[nw];
      ilw= new Int[nw];
      iwl= new Int[nw];

      comw= new string[nw];

      iw=0;
      for( id=0;id<nd;id++ )
     {

         iwd[0][iw]= id;
         iwd[1][iw]=  0;

         iws[0][iw]= ids[0][id];
         iws[1][iw]= ids[1][id];

         iwv[0][iw]= idv[0][id];
         iwv[1][iw]= idv[1][id];

         ilw[iw]= iw;
         iwl[iw]= -1;

         comw[iw]= "positive child of "+comd[id];
         iw++;

         iwd[0][iw]= id;
         iwd[1][iw]=  1;

         iws[0][iw]= ids[1][id];
         iws[1][iw]= ids[0][id];

         iwv[0][iw]= idv[3][id];
         iwv[1][iw]= idv[2][id];

         ilw[iw]= iw;
         iwl[iw]= -1;

         comw[iw]= "negative child of "+comd[id];

         itw[iw]= iw-1;
         itw[iw-1]= iw;
         iw++;

     }
  
  }
