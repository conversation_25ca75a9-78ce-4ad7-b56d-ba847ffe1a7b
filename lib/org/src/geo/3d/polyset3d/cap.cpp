   using namespace std;

#  include <geo/3d/polyset3d.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Sat Jan 15 19:17:03 GMT 2011
// Changes History -
// Next Change(s)  -

   void cPolyset3d::cap( Real *y0, Real *y1, Int is, Int *id, Int *idr, Real *sc, Real *d )
  {
      Real ymin[2],ymax[2],yb0[2],yb1[2],dy[2],l0[3],l1[2],z[2],s0,s1;

      Int ib,i0,i1;

    (*id)= -1;
    (*idr)= -1;
    (*sc)= big;
    (*d)= -big;

      for( ib=0;ib<nfb[is];ib++ )
     {
         i0= ifbp[is][0][ib];
         i1= ifbp[is][1][ib];
         line2( i0,yfp[is], yb0 );
         line2( i1,yfp[is], yb1 );
         idv2( yb0,ymin );
         idv2( yb1,ymax );
         box2( ymin,ymax );
         ymin[0]-= 1.e-6;
         ymin[1]-= 1.e-6;
         ymax[0]+= 1.e-6;
         ymax[1]+= 1.e-6;
         if( inside2( ymin,ymax, y1 ) )
        {
            sub2( yb1,yb0, l0 );
            sub2( y1,yb0,  dy );
            l0[2]= norm22( l0 );
            sclv2( 1./l0[2],l0 );
            idv2( l0,l1 );
            rot90( l1 );
            z[0]= dot2( l0,dy );
            if( inside( 0,l0[2],z[0] ) )
           {
               z[1]= dot2( l1,dy );
               if( fabs( z[1] ) < 10*small )
              {
                  z[0]/= l0[2];
                  s0= sfbp[is][0][ib];
                  s1= sfbp[is][1][ib];
                (*id)= ifbp[is][2][ib];
                (*idr)= ifbp[is][3][ib];
                (*sc)= z[0]*s1+ (1-z[0])*s0;
                  sub2( y1,y0,dy );
                (*d)= dot2( l1,dy );
                  break;
              }
           }
        }
     }
  }
