   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Sat Jan 15 18:24:52 GMT 2011
// Changes History -
// Next Change(s)  -

#  include <geo/3d/polyset3d.h>

   Int nextI( Int ist, Int ien, Int i0, Int *iprv, Int *inxt, Int *idnt, Int *iprm );
   Int same( Int ist, Int ien, Int i0, Int *iks, Int *iprm );

   void cPolyset3d::loops()
  {
      Int  iw,jw,il,ist,ien;
      Int  iv0,iv1;

// build loops

      llw= new Int[nw];
      jlp= new Int[nw];
//    ilrm= new Int[nw];

      nl=0;
      ien=0;
      while( ien < nw )
     {
         ist= ien;
         jw= iws[0][ilw[ien]];
// group edges
         iw= same( ien+1,nw,ist,iws[0],ilw ); 
         while( iw != -1 )
        {
             ien++;
             swap( ilw+ien,ilw+iw );
             iw= same( ien+1,nw,ist,iws[0],ilw ); 
        }
         ien++;

// pack in loops
         while( ist < ien )
        {
            iw= nextI( ist+1, ien, ist, iwv[1],iwv[0], iwd[0], ilw );
            while( iw != -1 )
           {
               ist++;
               swap( ilw+ist,ilw+iw );
               iw= nextI( ist+1, ien, ist, iwv[1],iwv[0], iwd[0], ilw );
           }
            ist++;
            llw[nl]= ist;
            jlp[nl]= -1;
//          ilrm[nl]= 0;
            nl++;
        }
     } 

      ien=0;
      for( il=0;il<nl;il++ )
     {
         ist= ien;
         ien= llw[il];
         for( jw=ist;jw<ien;jw++ )
        {
            iwl[ilw[jw]]= il;
        }
         iv0= iwv[0][ilw[ist]];
         iv1= iwv[1][ilw[ien-1]];
//       ilrm[il]= 0;
//       if( iv0 != iv1 ){ ilrm[il]= 1; };
     }

  }
