   using namespace std;

#  include <geo/3d/polyset3d.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Sat Jan 15 19:22:57 GMT 2011
// Changes History -
// Next Change(s)  -

   void sortsplt1( Int nsplt, Int *isplt[], Real *ssplt[], Int *nssplt, Int *lsplt[], Int *iss )
  {
      Int ns0,ns1;
      Int is,js,is0,is1;
      Int ist,ien,ist0;
      Real *wrk= new Real[1000];
      Real s0,s1;

      for( is=0;is<nsplt;is++ )
     {
         iss[is]= is;
         wrk[is]= ssplt[0][is];
     }

    (*nssplt)= 0;

      ien=0;
      while( ien < nsplt )
     {

         ist0=ien;
         ist= ist0;
// group splits on the dame w-edge definition
         is= same( ien+1,nsplt, ien, isplt[0],iss );
         while( is != -1 )
        {
            ien++;
            swap( iss+ien, iss+ is );
            is= same( ien+1,nsplt, ien, isplt[0],iss );
        }
         ien++;

// group splits on the same side of the w-edge
         is= same( ist+1,ien, ist, isplt[1],iss );
         while( is != -1 )
        {
            ist++;
            swap( iss+ist, iss+ is );
            is= same( ist+1,ien, ist, isplt[1],iss );
        }
         ist++;

         lsplt[0][(*nssplt)]= ist;
         lsplt[1][(*nssplt)]= ien;

         cout << lsplt[0][(*nssplt)]<<" "<<lsplt[1][(*nssplt)]<<"\n";;

       (*nssplt)++;
     }

      permute( nsplt, wrk, iss );

// sort according to the curvilinear coordinate

      Int *iprm= new Int[nsplt];
      ien=0;
      for( is=0;is<(*nssplt);is++ )
     {

         ist= ien;
         ien= lsplt[0][is];
         bsort(   ien-ist,  wrk+ist,  iprm );
         permute( ien-ist,  iss+ist,  iprm );

         ist= ien;
         ien= lsplt[1][is];
         bsort(   ien-ist,  wrk+ist,  iprm );
         permute( ien-ist,  iss+ist,  iprm );
     }

// now check that the split edge definitions are consistent
      ien=0;
      for( is=0;is<(*nssplt);is++ )
     {
         ist0=ien;
         ien= lsplt[0][is];
         ist= ien;
         ien= lsplt[1][is];

         ns0= ist-ist0;
         ns1= ien-ist;

         if( ns0 != ns1 )
        {
            cout << "split w-edge definitions are not consistent: number of cuts differ on the two sides\n";
            exit(0);
        }

         for( js=ist0;js<ist;js++ )
        {
            is0= iss[js];
            is1= iss[js+ns0];
            s0= ssplt[1][is0];
            s1= ssplt[1][is1];
            if( s0*s1 > 0 )
           {
               cout << "split w-edge definitions are not consistent: direction of cuts differ on the two sides\n";
               exit(0);
           }
        }

         is0= iss[ist0];
         if( isplt[1][is0] == 1 )
        {
            for( js=ist0;js<ist;js++ )
           {
               swap( iss+js, iss+js+ns0 ); 
           }
        }
     }

      delete[] iprm;
      delete[] wrk;
  }

   void cPolyset3d::split( Int nsplt, Int *isplt[], Real *ssplt[], bool uni )
  {
      Int          nssplt;
      Int         *lsplt[2];
      Int          iss[1000];

      Int         *ixrm;
      Int         *idrm;


      Int          ist,ien;
      Int          nd0;
      Int          ns0;
      Int          iv,kd,jd,id,is,ip;
      Int          id0,ids0,ids1,id1;
      Int          iv00,iv01,iv10,iv11;
      Int          ix00,ix01,ix10,ix11;
      Real         s0,s1;
      Real         y00[2],y10[2];
      Real         y01[2],y11[2];

      bool         live;

      ixrm= new Int[1000];
      idrm= new Int[1000];

      setv( (Int)0,nx, (Int)0, ixrm );
      setv( (Int)0,nd, (Int)0, idrm );

      lsplt[0]= new Int[1000];
      lsplt[1]= new Int[1000];

      sortsplt1( nsplt, isplt,ssplt, &nssplt,lsplt, iss );

      ien= 0;
      for( Int i=0;i<nssplt;i++ )
     {
         ist= ien;
         ien= lsplt[0][i];
         cout << "\n";
         cout << "cuts on w-edge definition "<<isplt[0][iss[ist]]<<" "<<isplt[1][iss[ist]]<<"\n";
         for( Int j=ist;j<ien;j++ )
        {
            Int k= iss[j];
            iv= isplt[2][k];
            cout << isplt[0][k]<<" "<<isplt[1][k]<<" "<<isplt[2][k]<<" "<<isplt[3][k]<<" "<<ssplt[0][k]<<" "<<ssplt[1][k]<< "\n";//<< xv[0][iv]<< " "<<xv[1][iv]<< " "<<xv[2][iv]<< "\n";
        }
         ist= ien;
         ien= lsplt[1][i];
         cout << "\n";
         cout << "cuts on w-edge definition "<<isplt[0][iss[ist]]<<" "<<isplt[1][iss[ist]]<<"\n";
         for( Int j=ist;j<ien;j++ )
        {
            Int k= iss[j];
            iv= isplt[2][k];
            cout << isplt[0][k]<<" "<<isplt[1][k]<<" "<<isplt[2][k]<<" "<<isplt[3][k]<<" "<<ssplt[0][k]<<" "<<ssplt[1][k]<< "\n";//<< xv[0][iv]<< " "<<xv[1][iv]<< " "<<xv[2][iv]<< "\n";
        }
     }

      ien=0;
      for( is=0;is<nssplt;is++ )
     {

         ist=  ien;
         ien=  lsplt[0][is];
         ns0= ien-ist;

         id= iss[ist];

         id0=  isplt[0][id];
         idrm[id0]= 1;
         ids0= ids[0][id0];
         ids1= ids[1][id0];

         iv01= idv[0][id0];
         iv11= idv[2][id0];

         line2( iv01,yv, y01 );
         line2( iv11,yv, y11 );
      
         s1=    0.;

         live= ( ssplt[1][id] < 0 );
         if( !uni ){ live= !live; };
         if( !live )
        {
            ix01= ivx[iv01];
            ix11= ivx[iv11];
            ixrm[ix01]= 1;
            ixrm[ix11]= 1;
        }
         ip=0;
         for( jd=ist;jd<ien;jd++ )
        {
            id= iss[jd];
            kd= iss[jd+ns0];
            iv00= iv01;
            iv10= iv11;
            iv01= isplt[2][id];
            iv11= isplt[2][kd];
            idv2( y01,y00 );
            idv2( y11,y10 );
            line2( iv01,yv, y01 );
            line2( iv11,yv, y11 );
            s0= s1;
            s1= ssplt[0][id];
            if( live )
           {
               ids[0][nd]= ids0;
               ids[1][nd]= ids1;
               idv[0][nd]= iv00;
               idv[1][nd]= iv01;
               idv[2][nd]= iv10;
               idv[3][nd]= iv11;
               comd[nd]= "live part "+strc(ip)+" of w-edge definition "+strc(id0)+" ("+comd[id0]+")";
               wdf[nd]= new cWedgeDef();
               wdf[nd]->build( wdf[id0], s0,y00,y10, s1,y01,y11, srf[ids0],srf[ids1] );
               idrm[nd]=0;
               nd++;
               ip++;
           }
            live= !live;
        }
         s0= s1;
         s1= 1.;
         iv00= iv01;
         iv10= iv11;
         iv01= idv[1][id0];
         iv11= idv[3][id0];
         idv2( y01,y00 );
         idv2( y11,y10 );
         line2( iv01,yv, y01 );
         line2( iv11,yv, y11 );
         if( live )
        {
            ids[0][nd]= ids0;
            ids[1][nd]= ids1;
            idv[0][nd]= iv00;
            idv[1][nd]= iv01;
            idv[2][nd]= iv10;
            idv[3][nd]= iv11;
            comd[nd]= "live part "+strc(ip)+" of w-edge definition "+strc(id0)+" ("+comd[id0]+")";
            wdf[nd]= new cWedgeDef();
            wdf[nd]->build( wdf[id0], s0,y00,y10, s1,y01,y11, srf[ids0],srf[ids1] );
            idrm[nd]=0;
            ip++;
            nd++;
        }
         else 
        {
            ix01= ivx[iv01];
            ix11= ivx[iv11];
            ixrm[ix01]= 1;
            ixrm[ix11]= 1;
        }
         ien=  lsplt[1][is];
     }

      removed( idrm );
//    removex( ixrm );

      delete[] lsplt[0];
      delete[] lsplt[1];
      delete[] idrm;
      delete[] ixrm;

   }
