
   using namespace std;

#  include <string>
#  include <fstream>
#  include <geo/2d/spline.h>
#  include <geo/3d/interp3d.h>
#  include <misc/tecplot.h>

   void sections( Int ns, cSpline **sec, cPolyset3d **pl )
  {

      Int         nl=0;
      Int         ns=0;
      Int         nl0;
      Int         lls[MN];
      Int         id,iv,jl,il,is,ils,ile;
      Int         j1,j2;
      Int         jus,jls;
      Int         juv,jlv;

      Int         nv,nvl;
      Real       *xv[3];
      Real       *yv[2];
      cWedgeDef  *wdf[MN];

      Real       *s[MN],*y[MN][2];
      Real       *zo[MN][2];
      Real        y0[2],dy0[2];
      Real        x0[3],dx0[2][3];
      Real *tmp0[2],*tmp1[2];

      Int        *ids[2];
      Int        *idv[4];
      Int        *ivs;

      ids[0]= new Int[1000];
      ids[1]= new Int[1000];

      idv[0]= new Int[1000];
      idv[1]= new Int[1000];
      idv[2]= new Int[1000];
      idv[3]= new Int[1000];

      xv[0]= new Real[MN];
      xv[1]= new Real[MN];
      xv[2]= new Real[MN];

      yv[0]= new Real[MN];
      yv[1]= new Real[MN];
      ivs=   new Int[MN];

// surface definitions

      srf= new cSections();
    ((cSections*)srf)->build( ns,sec );

      Real   y0[2],y1[2],dy[2];
      cInterp3d *srf;

      sec[0]->interp( 0.,y0,dy );
      sec[1]->interp( 1.,y1,dy );
      sub2( y0,y1, dy );

      nv= 0;
      if( norminf2( dy ) < ITPPTOL )
     {
// the surface is closed and needs one stitch line 

// vertex definitions
// "vertical surfaces"
      ile= 0;
      for( il=0;il<nl;il++ )
     {
         ils=0;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            yv[0][nv]= 0; 
            yv[1][nv]= 0; 
            ivs[nv]= jl;
            nv++;
        }
     }
      ile= 0;
      for( il=0;il<nl;il++ )
     {
         ils=0;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            yv[0][nv]= 1; 
            yv[1][nv]= 0; 
            ivs[nv]= jl;
            nv++;
        }
     }
      ile= 0;
      for( il=0;il<nl;il++ )
     {
         ils=0;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            yv[0][nv]= 1; 
            yv[1][nv]= 1; 
            ivs[nv]= jl;
            nv++;
        }
     }
      ile= 0;
      for( il=0;il<nl;il++ )
     {
         ils=0;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            yv[0][nv]= 0; 
            yv[1][nv]= 1; 
            ivs[nv]= jl;
            nv++;
        }
     }

// bases
      jlv= nv;
      ile= 0;
      for( il=0;il<nl;il++ )
     {
         ils=0;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            pls[jl]->interp( 0.,y0,dy0 );
            yv[0][nv]= y0[1]; 
            yv[1][nv]= y0[0]; 
            ivs[nv]= jls;
            nv++;
        }
     }
      juv= nv;
      ile= 0;
      for( il=0;il<nl;il++ )
     {
         ils=0;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            pls[jl]->interp( 0.,y0,dy0 );
            yv[0][nv]= y0[0]; 
            yv[1][nv]= y0[1]; 
            ivs[nv]= jus;
            nv++;
        }
     }

/*    cout << "\n";
      cout << "\n";
      cout << "vertex summary\n";
      for( iv=0;iv<nv;iv++ )
     {
         cout << "vertex "<<iv << " surface "<<ivs[iv]<<" surface coordinates "<< yv[0][iv]<<" "<<yv[1][iv]<<"\n";
     }
      cout << "\n";*/

// w-edge definition supports

      ile=0;
      for( il=0;il<nl;il++ )
     {
         ils=ile;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            s[jl]= new Real[21];
            y[jl][0]= new Real[21];
            y[jl][1]= new Real[21];
            zo[jl][0]= new Real[21];
            zo[jl][1]= new Real[21];
            for( Int i=0;i<21;i++ )
           {
               s[jl][i]= (Real)i/20.;
               pls[jl]->interp( s[jl][i],y0,dy0 );
               y[jl][0][i]= y0[0];
               y[jl][1][i]= y0[1];
               zo[jl][0][i]= 0;
               zo[jl][1][i]= 1;
           }
        }
     }

// winged edge definitions

// "vertical" edges
      Int nd=0;
      ile=0; 
      for( il=0;il<nl;il++ )
     {
         ils= ile;
         ile= lls[il];
         j1= ile-1;
         j2= ils;
         for( is=ils;is<ile-1;is++ )
        {
            j1=j2;
            j2++;

            cout << "w-edge "<<nd<<" is a vertical edge between surfaces "<<j1<<" "<<j2<<"\n";
            tmp0[0]= zo[j1][1]; tmp0[1]= s[j1];
            tmp1[0]= zo[j1][0]; tmp1[1]= s[j1];
            ids[0][nd]= j1;
            ids[1][nd]= j2;
            idv[0][nd]= j1+nl0*1;
            idv[1][nd]= j1+nl0*2;
            idv[2][nd]= j2  ;
            idv[3][nd]= j2+nl0*3;
            wdf[nd]= new cWedgeDef(); 
            wdf[nd]->build( tg(j1,j2,pls), (Int)21, tmp0,true, tmp1,true, srf[j1],srf[j2] ); 
            nd++;

        }
         j1=j2;
         j2=ils;
         cout << "w-edge "<<nd<<" is a vertical edge between surfaces "<<j1<<" "<<j2<<"\n";
         tmp0[0]= zo[j1][1]; tmp0[1]= s[j1];
         tmp1[0]= zo[j1][0]; tmp1[1]= s[j1];
         ids[0][nd]= j1;
         ids[1][nd]= j2;
         idv[0][nd]= j1+nl0*1;
         idv[1][nd]= j1+nl0*2;
         idv[2][nd]= j2  ;
         idv[3][nd]= j2+nl0*3;
         wdf[nd]= new cWedgeDef(); 
         wdf[nd]->build( tg(j1,j2,pls), (Int)21, tmp0,true, tmp1,true, srf[j1],srf[j2] ); 
         nd++;

     }


         
     }



     *p=  new cPolyset3d();
    (*p)->build( nv, ivs, yv, nd, ids,idv, wdf, ns, srf );

  }
