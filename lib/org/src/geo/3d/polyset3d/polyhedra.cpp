   using namespace std;

#  include <geo/3d/polyset3d.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Sat Jan 15 18:31:16 GMT 2011
// Changes History
// Next Change(s)  -

   void cPolyset3d::polyhedra()
  {

      Int  iw,jw,il,jl,ist,ien;

      Int  nstk;
      Int *istk;

// assemble polyhedra

      lpl= new Int[nl];
      ipl= new Int[nl];

      istk= new Int[nw];

      ien=0;
      np= 0;
      while( true )
     {

// find first unassigned loop
         nstk=0;
         for( il=0;il<nl;il++ )
        {
            if( jlp[il] == -1 )//&& ilrm[il] == 0 )
           {
               istk[0]= il;
               nstk=1;
               break;
           }
        }
         if( nstk == 0 ){ break; }
         lpl[np]= 0;

         while( nstk > 0 )
        {
            nstk--;
            il=istk[nstk];
            jlp[il]= np;
            jl= lpl[np];
            ipl[jl]= il;
            lpl[np]++;
            ist=0; 
            if( il > 0 ){ ist= llw[il-1]; }; 
            ien= llw[il];
            for( jw=ist;jw<ien;jw++ )
           {
               iw= ilw[jw];
               iw= itw[iw];
               jl= iwl[iw];
// add to the stack loops that aren't or haven't already been on the stack and share an edge with this loop
// and that have not been earmarked for removal
               if( jlp[jl] == -1 )//&& ilrm[jl] == 0 )
              {
                  jlp[jl]= -2;
                  istk[nstk]= jl;
                  nstk++;
              }
           }
        }
         np++;
     }

      delete[] istk;
  }
