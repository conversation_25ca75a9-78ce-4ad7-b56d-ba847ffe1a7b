
   using namespace std;

#  include <geo/3d/polyset3d.h>
#  define MN 100

   void   revolve( cPolyset *pl, Real *q0, Real *q1, Real *l0, cPolyset3d **p )
  {
      Int         nl=0;
      Int         ns=0;
      Int         nl0;
      Int         lls[MN];
      cPolyline  *pls[MN];
      cInterp3d  *srf[MN];
      Int         id,iv,jl,il,is,ils,ile;
      Int         j1,j2;

      Int         nv,nvl;
      Real       *xv[3];
      Real       *yv[2];
      cWedgeDef  *wdf[MN];

      Real       *s[MN],*y[MN][2];
      Real       *zo[MN][2];
      Real        y0[2],dy0[2];
      Real        x0[3],dx0[2][3];
      Real *tmp0[2],*tmp1[2];

      Int        *ids[2];
      Int        *idv[4];
      Int        *ivs;

      ids[0]= new Int[1000];
      ids[1]= new Int[1000];

      idv[0]= new Int[1000];
      idv[1]= new Int[1000];
      idv[2]= new Int[1000];
      idv[3]= new Int[1000];

      xv[0]= new Real[MN];
      xv[1]= new Real[MN];
      xv[2]= new Real[MN];

      yv[0]= new Real[MN];
      yv[1]= new Real[MN];
      ivs=   new Int[MN];

      Real m0[3];

      sub3( q1,q0, m0 );
      Real qr0[3],qr1[3],rr[3];
      qrf23( l0,m0, qr0,qr1,rr );

// polygon set sides

      nl=0;
      lls[0]=0;
      pl->sides( &nl,lls,pls ); 
      nl0= lls[nl-1];

// surface definitions
      ile=0;
      for( il=0;il<nl;il++ )
     {
         ils=0;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            srf[jl]= new cRevolution();
          ((cRevolution*)srf[jl])->build( pls[jl], q1,qr0,qr1,q0,l0 ); 
            string fnme;
            fnme= "surface."+strc(jl)+".tec";
            srf[jl]->check( fnme );
        }
     }
      ns= lls[nl-1];

// vertex definitions
// "vertical surfaces"
      nv= 0;
      ile= 0;
      for( il=0;il<nl;il++ )
     {
         ils=0;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            yv[0][nv]= 0; 
            yv[1][nv]= 0; 
            ivs[nv]= jl;
            nv++;
        }
     }
      ile= 0;
      for( il=0;il<nl;il++ )
     {
         ils=0;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            yv[0][nv]= 1; 
            yv[1][nv]= 0; 
            ivs[nv]= jl;
            nv++;
        }
     }
      ile= 0;
      for( il=0;il<nl;il++ )
     {
         ils=0;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            yv[0][nv]= 1; 
            yv[1][nv]= 1; 
            ivs[nv]= jl;
            nv++;
        }
     }
      ile= 0;
      for( il=0;il<nl;il++ )
     {
         ils=0;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            yv[0][nv]= 0; 
            yv[1][nv]= 1; 
            ivs[nv]= jl;
            nv++;
        }
     }

      cout << "\n";
      cout << "\n";
      cout << "vertex summary\n";
      for( iv=0;iv<nv;iv++ )
     {
         cout << "vertex "<<iv << " surface "<<ivs[iv]<<" surface coordinates "<< yv[0][iv]<<" "<<yv[1][iv]<<"\n";
     }
      cout << "\n";

// w-edge definition supports

      ile=0;
      for( il=0;il<nl;il++ )
     {
         ils=ile;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            s[jl]= new Real[21];
            y[jl][0]= new Real[21];
            y[jl][1]= new Real[21];
            zo[jl][0]= new Real[21];
            zo[jl][1]= new Real[21];
            for( Int i=0;i<21;i++ )
           {
               s[jl][i]= (Real)i/20.;
               pls[jl]->interp( s[jl][i],y0,dy0 );
               y[jl][0][i]= y0[0];
               y[jl][1][i]= y0[1];
               zo[jl][0][i]= 0;
               zo[jl][1][i]= 1;
           }
        }
     }

// winged edge definitions

// "vertical" edges
      Int nd=0;
      ile=0; 
      for( il=0;il<nl;il++ )
     {
         ils= ile;
         ile= lls[il];
         j1= ile-1;
         j2= ils;
         for( is=ils;is<ile-1;is++ )
        {
            j1=j2;
            j2++;

            cout << "w-edge "<<nd<<" is a vertical edge between surfaces "<<j1<<" "<<j2<<"\n";
            tmp0[0]= zo[j1][1]; tmp0[1]= s[j1];
            tmp1[0]= zo[j1][0]; tmp1[1]= s[j1];
            ids[0][nd]= j1;
            ids[1][nd]= j2;
            idv[0][nd]= j1+nl0*1;
            idv[1][nd]= j1+nl0*2;
            idv[2][nd]= j2  ;
            idv[3][nd]= j2+nl0*3;
            cout << "vertices on first surface "<<idv[0][nd]<<" "<<idv[1][nd]<<"\n";
            cout << "vertices on second surface "<<idv[2][nd]<<" "<<idv[3][nd]<<"\n";
            wdf[nd]= new cWedgeDef(); 
            wdf[nd]->build( tg(j1,j2,pls), (Int)21, tmp0,true, tmp1,true, srf[j1],srf[j2] ); 
            nd++;

        }
         j1=j2;
         j2=ils;
         cout << "w-edge "<<nd<<" is a vertical edge between surfaces "<<j1<<" "<<j2<<"\n";
         tmp0[0]= zo[j1][1]; tmp0[1]= s[j1];
         tmp1[0]= zo[j1][0]; tmp1[1]= s[j1];
         ids[0][nd]= j1;
         ids[1][nd]= j2;
         idv[0][nd]= j1+nl0*1;
         idv[1][nd]= j1+nl0*2;
         idv[2][nd]= j2  ;
         idv[3][nd]= j2+nl0*3;
         cout << "vertices on first surface "<<idv[0][nd]<<" "<<idv[1][nd]<<"\n";
         cout << "vertices on second surface "<<idv[2][nd]<<" "<<idv[3][nd]<<"\n";
         wdf[nd]= new cWedgeDef(); 
         wdf[nd]->build( tg(j1,j2,pls), (Int)21, tmp0,true, tmp1,true, srf[j1],srf[j2] ); 
         nd++;

     }

// "bases"
      ile=0; 
      for( il=0;il<nl;il++ )
     {
         ils= ile;
         ile= lls[il];
         for( is=ils;is<ile;is++ )
        {
            cout << "w-edge "<<nd<<" is a lower base edge between surfaces "<<is<<" "<<is<<"\n";
            tmp0[0]= s[is];    tmp0[1]= zo[is][1];
            tmp1[0]= s[is];    tmp1[1]= zo[is][0];
            ids[0][nd]= is;
            ids[1][nd]= is;
            idv[0][nd]= is+nl0*2;
            idv[1][nd]= is+nl0*3;
            idv[2][nd]= is+nl0*1;
            idv[3][nd]= is+nl0*0;
            wdf[nd]= new cWedgeDef();  
            wdf[nd]->build( true, 21, tmp0,false, tmp1,false, srf[is],srf[is] ); 
            nd++;

        }
     }

      cout << "\n";
      cout << "\n";
      cout << "w-edge definition symmary \n";
      for( id=0;id<nd;id++ )
     {
         cout << "w.e. definition "<<id;
         cout << " surfaces "<<ids[0][id]<<" "<<ids[1][id];
         cout << " verteces (0) "<<idv[0][id]<<" "<<idv[1][id];
         cout << " verteces (1) "<<idv[2][id]<<" "<<idv[3][id];
         cout << "\n";
     }

// cleanup
      ile=0;
      for( il=0;il<nl;il++ )
     {
         ils=ile;
         ile=lls[il];
         for( jl=ils;jl<ile;jl++ )
        {
            delete pls[jl]; pls[jl]= NULL;
            delete[] s[jl]; s[jl]= NULL;
            delete[] y[jl][0]; y[jl][0]= NULL;
            delete[] y[jl][1]; y[jl][1]= NULL;
            delete[] zo[jl][0]; zo[jl][0]= NULL;
            delete[] zo[jl][1]; zo[jl][1]= NULL;
        }
     }

// build the polyhedron

     *p=  new cPolyset3d();
    (*p)->build( nv, ivs, yv, nd, ids,idv, wdf, ns, srf );

  }

