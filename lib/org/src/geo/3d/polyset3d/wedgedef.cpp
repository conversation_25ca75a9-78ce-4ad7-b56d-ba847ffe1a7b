   using namespace std;

#  include <geo/3d/wedge.h>

   cWedgeDef::cWedgeDef()
  {
      itg= false;
      cls=false;
      n= 0;
      s= NULL;
      y[0][0]= NULL;
      y[0][1]= NULL;
      y[1][0]= NULL;
      y[1][1]= NULL;
      x[0]= NULL;
      x[1]= NULL;
      x[2]= NULL;
      l= 0;
  };

   cWedgeDef::~cWedgeDef()
  {
      itg= false;
      cls=false;
      n= 0;
      delete[] s; s= NULL;
      delete[] y[0][0]; y[0][0]= NULL;
      delete[] y[0][1]; y[0][1]= NULL;
      delete[] y[1][0]; y[1][0]= NULL;
      delete[] y[1][1]; y[1][1]= NULL;
      delete[] x[0]; x[0]= NULL;
      delete[] x[1]; x[1]= NULL;
      delete[] x[2]; x[2]= NULL;
      l= 0.;
  };

   void cWedgeDef::build( bool it0, Int np, Real *s00[], bool b0, Real *s10[], bool b1, cInterp3d *srf0, cInterp3d *srf1 )
  {
      Int    ip;
      Real   y0[2],y1[2];
      Real   x0[3],x1[3],dx0[3],dx1[3],dx[3];
      Real   dmin;

      n=np;

      s= new Real[n];
      y[0][0]= new Real[n];
      y[0][1]= new Real[n];
      y[1][0]= new Real[n];
      y[1][1]= new Real[n];
      x[0]= new Real[n];
      x[1]= new Real[n];
      x[2]= new Real[n];

      itg= it0; 
      if( itg ){ cout << "w-edge definition "<<this<<" is tangent\n"; }

// definition points in surface coordintes

      for( ip=0;ip<n;ip++ )
     {
         y[0][0][ip]= s00[0][ip];
         y[0][1][ip]= s00[1][ip];
     }
      if( !b0 )
     {  
         reverse( n, y[0][0] );
         reverse( n, y[0][1] ); 
     }

      for( ip=0;ip<n;ip++ )
     {
         y[1][0][ip]= s10[0][ip];
         y[1][1][ip]= s10[1][ip];
     }
      if( !b1 )
     {  
         reverse( n, y[1][0] );
         reverse( n, y[1][1] ); 
     }

// definition points in cartesian coordintes and consistency check

      build( srf0,srf1 );

  }

   void cWedgeDef::build( cInterp3d *srf0, cInterp3d *srf1 )
  {
      Int    ip;
      Real   y0[2],y1[2];
      Real   x0[3],x1[3],dx0[3],dx1[3],dx[3];
      Real   dmin;
// definition points in cartesian coordintes and consistency check

      dmin=big;
      for( ip=0;ip<n;ip++ )
     {
         y0[0]= y[0][0][ip];
         y0[1]= y[0][1][ip];
         y1[0]= y[1][0][ip];
         y1[1]= y[1][1][ip];

         srf0->interp( y0,x0,dx0,dx1 );
         srf1->interp( y1,x1,dx0,dx1 );

         sub3( x1,x0, dx ); 
         dmin= fmin( dmin,norminf3( dx ) );

         x[0][ip]= 0.5*( x0[0]+ x1[0] );
         x[1][ip]= 0.5*( x0[1]+ x1[1] );
         x[2][ip]= 0.5*( x0[2]+ x1[2] );
     }
      if( dmin= norminf3( dx ) > ITPPTOL )
     {
         cout << "DEFINITIONS DO NOT MATCH <=====================================\n"; 
     }

// length in physical space
      s[0]= 0.;
      line3( 0,x,x1 );
      for( ip=1;ip<n;ip++ )
     {
         idv3(  x1,x0 );
         line3( ip,x,x1 );
         sub3(  x1,x0, dx );
         s[ip]= s[ip-1]+ norm23( dx );
     }
      l= s[n-1];
      for( ip=0;ip<n;ip++ )
     {
         s[ip]/= l;
     }

// closed w-edge
      line3( 0,x,x0 ); 
      line3( n-1,x,x1 ); 
      sub3( x1,x0,dx );
      cls= ( norminf3( dx ) < ITPPTOL );
  }

   void cWedgeDef::wrap( Real *w )
  {
      if( cls )
     {
         while( *w < 0 )
        {
          (*w)+= 1;
        }
         while( *w > 1 )
        {
          (*w)-= 1;
        }
     }
  }

   void cWedgeDef::interp2( Real w0, Real *yp, Real *dyp, cInterp3d *srf0, cInterp3d *srf1, Int is )
  {
      inter3d_t stat;
      Real      w;
      Real      y0[2],y1[2];
      Real      p2[3],n2[3];
      Real      b[3],dx[3];
      Real      ds;
      Real     *a0,*a1,*a2;
      Int      *ipiv;
      Real     *q0,*q1,*r;

      w= w0;
      wrap( &w );
    
      Int i;
      if( w < 0 )
     {
         cout << "extrapolate below 0 not done yet\n";
     }
      else
     {
         if( w > 1 )
        {
            cout << "extrapolate above 1 not done yet\n";
        }
         else
        {

            i= bsearch( w, n-1,s+1 );
            ds= s[i+1]- s[i];
            w= ( w-s[i] )/ds;

            y0[0]= w*y[0][0][i+1]+ (1-w)*y[0][0][i];
            y0[1]= w*y[0][1][i+1]+ (1-w)*y[0][1][i];

            y1[0]= w*y[1][0][i+1]+ (1-w)*y[1][0][i];
            y1[1]= w*y[1][1][i+1]+ (1-w)*y[1][1][i];

            p2[0]= w*x[0][i+1]+ (1-w)*x[0][i]; 
            p2[1]= w*x[1][i+1]+ (1-w)*x[1][i]; 
            p2[2]= w*x[2][i+1]+ (1-w)*x[2][i]; 

            n2[0]= x[0][i+1]- x[0][i];
            n2[1]= x[1][i+1]- x[1][i];
            n2[2]= x[2][i+1]- x[2][i];

            if( !itg )
           {
               inters( srf0, srf1, p2,n2, y0,y1, &stat );
               a0= stat.a[0];
               a1= stat.a[1];
               a2= stat.a[2];
               ipiv= stat.ipiv;
               b[0]= 0;
               b[1]= 0;
               b[2]= l*l*ds;
               lus3t( a0,a1,a2,ipiv, dx, b );
               if( is == 0 )
              {
                  q0= stat.q0[0];
                  q1= stat.q0[1];
                  r = stat.r0;
                  qrs23( q0,q1,r, dyp, dx );
                  yp[0]= y0[0];
                  yp[1]= y0[1];
              }
               else
              {
                  q0= stat.q1[0];
                  q1= stat.q1[1];
                  r = stat.r1;
                  qrs23( q0,q1,r, dyp, dx );
                  yp[0]= y1[0];
                  yp[1]= y1[1];
              }
           }
            else
           {
               if( is == 0 ) 
              {
                  yp[0]= y0[0];
                  yp[1]= y0[1];
              }
               else
              {
                  yp[0]= y1[0];
                  yp[1]= y1[1];

              }
               dyp[0]= y[is][0][i+1]- y[is][0][i];
               dyp[1]= y[is][1][i+1]- y[is][1][i];
               sclv2( 1./ds,dyp );
           }
        }
     }
  }

   void cWedgeDef::interp3( Real w0, Real *xp, Real *dxp, cInterp3d *srf0, cInterp3d *srf1 )
  {
      inter3d_t stat;
      Real      w;
      Real      y0[2],y1[2];
      Real      p2[3],n2[3];
      Real      p21[3];
      Real      p20[3];
      Real      b[3],dx[3];
      Real      ds;
      Real     *a0,*a1,*a2;
      Int      *ipiv;
               Real dp2[2][3];
               Real dp20[2][3];
               Real dp21[2][3];
               Real dy[2];
               Real dy0[2];
               Real dy1[2];

      w= w0;
      wrap( &w );
    
      Int i;
      if( w < 0 )
     {
         cout << "extrapolate below 0 not done yet\n";
     }
      else
     {
         if( w > 1 )
        {
            cout << "extrapolate above 1 not done yet\n";
        }
         else
        {

            i= bsearch( w, n-1,s+1 );
            ds= s[i+1]- s[i];
            w= ( w-s[i] )/ds;

            y0[0]= w*y[0][0][i+1]+ (1-w)*y[0][0][i];
            y0[1]= w*y[0][1][i+1]+ (1-w)*y[0][1][i];

            y1[0]= w*y[1][0][i+1]+ (1-w)*y[1][0][i];
            y1[1]= w*y[1][1][i+1]+ (1-w)*y[1][1][i];

            p2[0]= w*x[0][i+1]+ (1-w)*x[0][i]; 
            p2[1]= w*x[1][i+1]+ (1-w)*x[1][i]; 
            p2[2]= w*x[2][i+1]+ (1-w)*x[2][i]; 

            n2[0]= x[0][i+1]- x[0][i];
            n2[1]= x[1][i+1]- x[1][i];
            n2[2]= x[2][i+1]- x[2][i];

            dy0[0]= y[0][0][i+1]- y[0][0][i];
            dy0[1]= y[0][1][i+1]- y[0][1][i];
            dy1[0]= y[0][0][i+1]- y[0][0][i];
            dy1[1]= y[0][1][i+1]- y[0][1][i];

            if( !itg )
           {
/*             srf0->interp( y0,p20,dp20[0],dp20[1] ); 
               srf1->interp( y1,p21,dp21[0],dp21[1] ); 

               p2[0]= 0.5*( p20[0]+p21[0] );
               p2[1]= 0.5*( p20[1]+p21[1] );
               p2[2]= 0.5*( p20[2]+p21[2] );

               dp20[0][0]= dy0[0]*dp20[0][0]+ dy0[1]*dp20[1][0];
               dp20[0][1]= dy0[0]*dp20[1][0]+ dy0[1]*dp20[1][1];
               dp20[0][2]= dy0[0]*dp20[2][0]+ dy0[1]*dp20[1][2];

               dp21[0][0]= dy1[0]*dp21[0][0]+ dy1[1]*dp21[1][0];
               dp21[0][1]= dy1[0]*dp21[1][0]+ dy1[1]*dp21[1][1];
               dp21[0][2]= dy1[0]*dp21[2][0]+ dy1[1]*dp21[1][2];

               n2[0]= 0.5*( dp20[0][0]+ dp21[0][0] );
               n2[1]= 0.5*( dp20[0][1]+ dp21[0][1] );
               n2[2]= 0.5*( dp20[0][2]+ dp21[0][2] );*/

               inters( srf0, srf1, p2,n2, y0,y1, &stat );
               xp[0]= ( stat.y0[0]+ stat.y1[0] )/2;
               xp[1]= ( stat.y0[1]+ stat.y1[1] )/2;
               xp[2]= ( stat.y0[2]+ stat.y1[2] )/2;
               a0= stat.a[0];
               a1= stat.a[1];
               a2= stat.a[2];
               ipiv= stat.ipiv;
               b[0]= 0;
               b[1]= 0;
               b[2]= l*l*ds;
               lus3t( a0,a1,a2,ipiv, dxp, b );
           }
            else
           {
               srf0->interp( y0,p2,dp2[0],dp2[1] ); 
               xp[0]= p2[0];
               xp[1]= p2[1];
               xp[2]= p2[2];

               dy[0]= y[0][0][i+1]- y[0][0][i];
               dy[1]= y[0][1][i+1]- y[0][1][i];
               
               dxp[0]= dp2[0][0]*dy[0]+ dp2[1][0]*dy[1];
               dxp[1]= dp2[0][1]*dy[0]+ dp2[1][1]*dy[1];
               dxp[2]= dp2[0][2]*dy[0]+ dp2[1][2]*dy[1];

               sclv3( 1./ds,dxp );
           }
        }
     }
  }

   void cWedgeDef::head( Real *xp )
  {
      if( n > 0 )
     {
         line3( 0,x,xp );
     }
  }

   void cWedgeDef::tail( Real *xp )
  {
      if( n > 0 )
     {
         line3( n-1,x,xp );
     }
  }

   void cWedgeDef::build( cWedgeDef *wdf, Real s0, Real *y00, Real *y10, Real s1, Real *y01, Real *y11, cInterp3d *srf0, cInterp3d *srf1 )
  {
      Int ist, ien;
      Int i,j;
      Real *sd,*yd[2][2],*xd[3];
      Real ds;
      Real w;

      ist= bsearch( s0, wdf->n-1, (wdf->s)+1 );
      ien= bsearch( s1, wdf->n-1, (wdf->s)+1 );
      for( Int i=0;i<wdf->n;i++ )
     {
         cout << wdf->s[i]<<" ";
     }
      cout << "\n";
      cout << s0<<" "<<s1<<"\n";
      cout << "use positions "<<ist<<" to "<<ien<<"\n";
 

      sd= wdf->s;
      yd[0][0]= wdf->y[0][0];
      yd[0][1]= wdf->y[0][1];
      yd[1][0]= wdf->y[1][0];
      yd[1][1]= wdf->y[1][1];
      xd[0]= wdf->x[0];
      xd[1]= wdf->x[1];
      xd[2]= wdf->x[2];

      n= ien-ist+1;
      if( n < 2 ){ n=2; };

      s= new Real[n];
      y[0][0]= new Real[n];
      y[0][1]= new Real[n];
      y[1][0]= new Real[n];
      y[1][1]= new Real[n];
      x[0]= new Real[n];
      x[1]= new Real[n];
      x[2]= new Real[n];

      itg= wdf->itg; 

      i=0;
      y[0][0][i]= y00[0];
      y[0][1][i]= y00[1];
      y[1][0][i]= y10[0];
      y[1][1][i]= y10[1];
      i++; 
      for( j=ist+1;j<ien;j++ )
     {
         y[0][0][i]= yd[0][0][j];
         y[0][1][i]= yd[0][1][j];
         y[1][0][i]= yd[1][0][j];
         y[1][1][i]= yd[1][1][j];
         i++;
     }
      y[0][0][i]= y01[0];
      y[0][1][i]= y01[1];
      y[1][0][i]= y11[0];
      y[1][1][i]= y11[1];
      i++;
      cout << "FOR THIS WEDGE DEF "<<i<<" "<<n<<"\n";
     
      build( srf0,srf1 );
  }

   void cWedgeDef::inters1( cInterp3d *srf2, Real *w, Real *y2, inter3d_t *stat, cInterp3d *srf0, cInterp3d *srf1 )
  {

      Real x0[3],x1[3],x2[3];//dxp[3];
      Real dx0[2][3],dx1[2][3],dx2[2][3];//dxp[3];
      Real xq[3],dxq[2][3];
      Real dx[3];
      Real dy2[2],dw;
      Real y0[2],y1[2];
      Real dy0[2],dy1[2];
      
      cout << "compute the intersection between "<<srf0<<" "<<srf1<<" "<<srf2<<"\n";
      cout << "starting at "<<*w<<" "<<y2[0]<<" "<<y2[1]<<"\n";

      if( !itg )
     {
         interp2( *w, y0,dy0, srf0,srf1, 0 );
         interp2( *w, y1,dy1, srf0,srf1, 1 );
         inters( srf0,srf1,srf2, y0,y1,y2, stat );
         srf0->interp( y0, x0,dx0[0],dx0[1] );
         srf1->interp( y1, x1,dx1[0],dx1[1] );
         srf2->interp( y2, x2,dx2[0],dx2[1] );
         cout << "\n";
         cout << "inters residual "<<stat->res<<"<========================\n";
         cout << "\n";
     }
      else
     {

         for( Int it=0;it<10;it++ )
        {
            interp3( *w, x0,dx0[0], srf0, srf1 );
            srf2->interp( y2, x2,dx2[0],dx2[1] );
            p1l1int( x2, dx2[0], dx2[1], x0, dx0[0], dy2, &dw, stat );
          (*w)+= dw;
            y2[0]+= dy2[0];
            y2[1]+= dy2[1];
        }
         interp2( *w, y0,dy0, srf0,srf1, 0 );
         interp2( *w, y1,dy1, srf0,srf1, 1 );
         interp3( *w, x0,dx0[0], srf0,srf1 );
         srf2->interp( y2, x2,dx2[0],dx2[1] );
         cout << "w-edge intersection would produce \n";
         cout << y0[0]<<" "<<y0[1]<<"\n";
         cout << y1[0]<<" "<<y1[1]<<"\n";
         cout << y2[0]<<" "<<y2[1]<<"\n";
         cout << x0[0]<<" "<<x0[1]<<" "<<x0[2]<<"\n";
         cout << x2[0]<<" "<<x2[1]<<" "<<x2[2]<<"\n";
         sub3( x2,x0, dx0[1] );
         cout << stat->det<<" "<<norminf3( dx0[1] )<<"\n";
     }
  }


   void cWedgeDef::copy( cWedgeDef **w )
  {
      cWedgeDef *tmp;
      if( *w )
     { 
         cout << "cannot copy onto existing wedgedef\n";
         exit(0);
     }
      tmp= new cWedgeDef();
      tmp->itg= itg;
      tmp->cls= cls;
      tmp->n=   n;
      tmp->s= new Real[n];
      tmp->y[0][0]= new Real[n];
      tmp->y[1][0]= new Real[n];
      tmp->y[0][1]= new Real[n];
      tmp->y[1][1]= new Real[n];
      tmp->x[0]= new Real[n];
      tmp->x[1]= new Real[n];
      tmp->x[2]= new Real[n];
      for( Int i=0;i<n;i++ )
     {
         tmp->s[i]= s[i];
         tmp->y[0][0][i]= y[0][0][i];
         tmp->y[1][0][i]= y[1][0][i];
         tmp->y[0][1][i]= y[0][1][i];
         tmp->y[1][1][i]= y[1][1][i];
         tmp->x[0][i]= x[0][i];
         tmp->x[1][i]= x[1][i];
         tmp->x[2][i]= x[2][i];
     }
      tmp->l= l;
    (*w)= tmp;
  }
