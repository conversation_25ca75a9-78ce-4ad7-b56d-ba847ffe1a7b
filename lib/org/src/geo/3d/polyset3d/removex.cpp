   using namespace std;

#  include <geo/3d/polyset3d.h>

   void cPolyset3d::removex( Int *ixrm )
  {
      Int ix,jx,kx,iv,id, iv0,ix0, iv1,ix1, nx1;
      Int idrm[1000];
      Int ivrm[1000];
      Int iprm[1000];
      Int n;

      setv( (Int)0,nd, (Int)0,idrm );
      setv( (Int)0,nv, (Int)0,ivrm );

      n=1;
      while( n > 0 )
     {
         n=0;
         for( id=0;id<nd;id++ )
        {
            if( idrm[id] == 0 )
           {
               iv0= idv[0][id]; 
               iv1= idv[1][id]; 
               ix0= ivx[iv0];
               ix1= ivx[iv1];
               if( ixrm[ix0] == 1 || ixrm[ix1] == 1 )
              {
                  cout << "remove w-edge "<<id<<"\n";
            
                  idrm[id]= 1;
                  ixrm[ix0]= 1;
                  ixrm[ix1]= 1;
                  ivrm[iv0]= 1;
                  ivrm[iv1]= 1;
                  n++;
              }
           }
        } 
     }

// do not remove entities addressed by at least one active w-edge definition

      removed( idrm );
      removev( idrm );

      select0( nx,&nx1,ixrm,iprm );

      permute( nx, xx[0], iprm );
      permute( nx, xx[1], iprm );
      permute( nx, xx[2], iprm );

      for( iv=0;iv<nv;iv++ )
     {
         ix= ivx[iv];
         ivx[iv]= ixrm[ix];
     }

      nx= nx1;
      
  }

   void cPolyset3d::removed( Int *idrm )
  {
      Int id,jd,kd,nd1;
      Int iprm[1000];
      select0( nd,&nd1,idrm,iprm );

      permute( nd, ids[0], iprm );
      permute( nd, ids[1], iprm );
      permute( nd, idv[0], iprm );
      permute( nd, idv[1], iprm );
      permute( nd, idv[2], iprm );
      permute( nd, idv[3], iprm );
      permute( nd, comd,   iprm );
      permute( nd, wdf,    iprm );

      for( id=nd1;id<nd;id++ )
     {
         delete wdf[id]; wdf[id]= NULL;
     }
      nd= nd1;
      
  }

   void cPolyset3d::removev( Int *ivrm )
  {
      Int iv,jv,kv,id,nv1;

      Int iprm[1000];

      select0( nv,&nv1,ivrm,iprm );

      permute( nv, yv[0], iprm );
      permute( nv, yv[1], iprm );
      permute( nv, ivx,   iprm );
      permute( nv, ivs,   iprm );
      permute( nv, comv,  iprm );

      for( id=0;id<nd;id++ )
     {
         iv= idv[0][id];
         idv[0][id]= ivrm[iv];

         iv= idv[1][id];
         idv[1][id]= ivrm[iv];

         iv= idv[2][id];
         idv[2][id]= ivrm[iv];

         iv= idv[3][id];
         idv[3][id]= ivrm[iv];

     } 

      nv= nv1;
      
  }

