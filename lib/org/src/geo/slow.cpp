   using namespace std;

#  include <geo/slow.h>

   void slow( Int ist, Int ien, Int nx, Real *x[], Real *x0, Int *imin, Real *dmin )
  {
      Int i,j;
      Real x1[3];
      Real  d,dx;
     *dmin= big;
     *imin=-1;
      for( i=ist;i<ien;i++ )
     {
         line( i,nx,x, x1 );
         d= 0;
         for( j=0;j<nx;j++ )
        {
            dx= x1[j]-x0[j];
            d+= dx*dx;
        } 
         if( d < *dmin )
        {
           *imin= i;
           *dmin= d;
        }
     }
  }
