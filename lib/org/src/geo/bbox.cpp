using namespace std;

#include <geo/bbox.h>

    cBbox::cBbox()
   {
       Int i;
       nx= 0;
       empty=true;
       for( i=0;i<3;i++ )
      {
          x0[i]=big;
          x1[i]=-big;
      }
   }

    void cBbox::setCorners( Int Nx, Real *x )
   {
       Int ip,ix;
       nx= Nx; 
       empty= false;
       for( ix=0;ix<nx;ix++ )
      {
          x0[ix]= fmin( x0[ix],x[ix] );
          x1[ix]= fmax( x1[ix],x[ix] );
      }
   }

    void cBbox::setCorners( Int ips, Int ipe, Int Nx, Real *x[] )
   {
       Int ip,ix;
       nx= Nx; 
       empty= false;
       for( ix=0;ix<nx;ix++ )
      {
          for( ip=ips;ip<ipe;ip++ )
         {
             x0[ix]= fmin( x0[ix],x[ix][ip] );
             x1[ix]= fmax( x1[ix],x[ix][ip] );
         }
      }
   }

    void cBbox::setCorners( Int ips, Int ipe, Int *ipx[], Int Nx, Real *x[] )
   {
       Int jp,ip,ix;
       nx= Nx; 
       empty= false;
       for( ix=0;ix<nx;ix++ )
      {
          for( jp=ips;jp<ipe;jp++ )
         {
             ip= ipx[0][jp];
             x0[ix]= fmin( x0[ix],x[ix][ip] );
             x1[ix]= fmax( x1[ix],x[ix][ip] );
         }
      }
   }

    void cBbox::resetCorners()
   {
       Int i;
       empty=true;
       nx= 0;
       for( i=0;i<3;i++ )
      {
          x0[i]=big;
          x1[i]=-big;
      }
   }

    void cBbox::print()
   {
        cout << "BBox corners \n";
        cout << x0[0]<<" "<<x0[1]<<"\n";
        cout << x1[0]<<" "<<x0[1]<<"\n";
        cout << x1[0]<<" "<<x1[1]<<"\n";
        cout << x0[0]<<" "<<x1[1]<<"\n";
        cout << x0[0]<<" "<<x0[1]<<"\n";
   }

    Real cBbox::area()
   {
       return (x1[0]-x0[0])*(x1[1]-x0[1]);
   }

    Real cBbox::area( cBbox *box )
   {
       Real d[3],s[3],a;


       if( empty || (box->empty) )
      {
          return 0.;
      }
       else
      {
       s[0]= box->x1[0]- box->x0[0];
       d[0]= sgdst( x0[0],x1[0], box->x1[0] )- sgdst( x0[0],x1[0], box->x0[0] );

       s[1]= box->x1[1]- box->x0[1];
       d[1]= sgdst( x0[1],x1[1], box->x1[1] )- sgdst( x0[1],x1[1], box->x0[1] );
    
       return( (s[0]-d[0])*(s[1]-d[1]) );
      }
   }

    bool cBbox::inside( Real x[] )
   {
       bool in;
       in= (x[0]<=x1[0]) && (x[0]>=x0[0]);
       in= in && (x[1]<=x1[1]) && (x[1]>=x0[1]);
       return in;
   }

    bool cBbox::inside( cBbox *box )
   {
       bool in;
       in= inside( box->x0 );
       in= in && inside( box->x1 );
       return in;
   }

    Real cBbox::length()
   {
       Real l0,l1;
       l0= x1[0]-x0[0];
       l1= x1[1]-x0[1];
       return( max(l0,l1) );
   };

    Real cBbox::minlength()
   {
       Real l0,l1;
       l0= x1[0]-x0[0];
       l1= x1[1]-x0[1];
       return( min(l0,l1) );
   };

    bool bintrsct( cBbox *box1, cBbox *box2 )
   {
       Real ao,a1,a2,l1,l2,s1,s2;
       a1= box1->area();
       a2= box2->area();
       l1= box1->length();
       l2= box2->length();
// if HIGHAR is a small number then  r*l1*l1 is the area of a box with the same largest length as the current box but very high aspect 
// ratio: use this to define high aspect ratio boxes with almost null-area
       s1= HIGHAR*l1*l1;
       s2= HIGHAR*l2*l2;
//     cout << "box1: ";box1->print();cout <<" box2: ";box2->print();
       if( a1 > s1 && a2 > s1 )
// both boxes have finite area: check that the overlap area exceeds 99% of the area of each box
      {
          ao= box1->area( box2 );
//        cout << " finite areas "<<a1<<" "<<a2<<" "<<ao<<" - returning "<<(( ao > 0.99*a1 ) && ( ao > 0.99*a2 ))<<"\n";
          return ( ( ao > 0.99*a1 ) && ( ao > 0.99*a2 ) );
      }
       else
// one of the boxes is degenerate: check that all its vertices fall inside the other box
      {
//        cout << " vanishing areas - returning "<<( box1->inside( box2 ) || box2->inside( box1 ) )<<"\n";
          return ( box1->inside( box2 ) || box2->inside( box1 ) );
      }
   };

    bool boverlap( cBbox *box1, cBbox *box2 )
   {
       Real a,l1,l2,l;
       a= box1->area( box2 );
       l1= box1->minlength();
       l2= box2->minlength();
       l= min( l1,l2 );
       return( a > small );//0.1*l*l );
   };

/*  void cBbox::global()
   {
       Real gx0[3];
       Int i,gnx;
       Real gx1[3];
       bool gempty;
       MPI_Allreduce( &empty,&gempty, 1,MPI_BYTE, MPI_BAND, MPI_COMM_WORLD );
       MPI_Allreduce( &nx,&gnx, 1,MPI_Int,MPI_MAX, MPI_COMM_WORLD );
       MPI_Allreduce( x0,gx0, 3,MPI_Real,MPI_MIN, MPI_COMM_WORLD );
       MPI_Allreduce( x1,gx1, 3,MPI_Real,MPI_MAX, MPI_COMM_WORLD );
       nx= gnx;
       empty=gempty;
       for( i=0;i<3;i++ )
      {
          x0[i]= gx0[i];
          x1[i]= gx1[i];
      }
   }*/

    void cBbox::add( cBbox *box )
   {
       setCorners( box->nx,box->x0 );
       setCorners( box->nx,box->x1 );
   }
    void cBbox::grow( Real eps )
   {
        Int ix;
        for( ix=0;ix<nx;ix++ )
       {
           x0[ix]-= eps;
           x1[ix]+= eps;
       }
   }
