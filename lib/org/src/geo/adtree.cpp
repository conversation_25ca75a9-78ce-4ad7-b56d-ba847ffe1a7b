   using namespace std;

#  include <geo/adtree.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         Wed Dec  8 18:49:44 GMT 2010
// Changes History -
// Next Change(s)  -

   cAdTree::cAdTree()
  {
      Int   ix;
      iroot=-1;
      n=0;
      nl=0;
      nx=0;
      m=0;
      ml=0;

      for( ix=0;ix<ADTMX;ix++ )
     {
         x[ix]=NULL;
         xmin[ix]=  big;
         xmax[ix]= -big;
     }

      xsep=NULL;
      iprn[0]=NULL;
      iprn[1]=NULL;
      ichl[0]=NULL;
      ichl[1]=NULL;
      indx=NULL;
      ilbl=NULL;

      dbg=false;

  }

   cAdTree::~cAdTree()
  {
      clear(); 
  }

   void cAdTree::clear()
  {

      Int i;

      for( i=0;i<nx;i++ )
     {
         delete[] x[i]; x[i]= NULL;
     }
      delete[] xsep; xsep=NULL;

      delete[] ichl[0]; ichl[0]=NULL;
      delete[] ichl[1]; ichl[1]=NULL;
      delete[] iprn[0]; iprn[0]=NULL;
      delete[] iprn[1]; iprn[1]=NULL;
      delete[] ilbl; ilbl=NULL;
      delete[] indx; indx=NULL;

      n=0;
      nl=0;
      m=0;
      ml=0;
      nx=0;
      iroot=-1;
      
  }

   void cAdTree::init( Int nx0, Real *xmin0, Real *xmax0 )
  {
      Int     ix;
      if( n == 0 )
     {
         clear();
         nx=nx0;
         for( ix=0;ix<nx;ix++ )
        {
            xmin[ix]= xmin0[ix];                          
            xmax[ix]= xmax0[ix];                          
        }
     }
      else
     {
         cout << "cannot initialise non-empty tree\n";
         exit(0);
     }
  }

   void cAdTree::insert( Real *y, Int in )
  {
      Real     x0[ADTMX],x1[ADTMX],xm[ADTMX];
      Int      ix,ic,jc,ip,jx;
      Int      tmp,dsize= ADTDSIZE;


// traverse the tree
      ic=iroot;
      ip=iroot;
      jc=-1;
      for( jx=0;jx<nx;jx++ )
     {
         x0[jx]= xmin[jx];
         x1[jx]= xmax[jx];
         xm[jx]= x0[jx]+x1[jx];
         xm[jx]/= 2;
     }
      ix=0;
      while( ic >= 0 )
     {
         ip= ic;
         if( ilbl[ip] == -1 ){ break; };
         if( y[ix] < xm[ix] )
        {
            ic= ichl[0][ip];
            jc= 0;
            x1[ix]= xm[ix];
        }
         else
        {
            ic= ichl[1][ip];
            jc= 1;
            x0[ix]= xm[ix];
        }
         xm[ix]= x0[ix]+ x1[ix];
         xm[ix]/= 2;
         ix++;
         if( ix == nx ){ ix=0; };
     }

// a new node is added
      if( ip != ic || ip < 0 )
     {
         if( m == n )
        {
            tmp=m;
            realloc( &tmp,dsize,&ilbl    ); 
            setv( (Int)m,(Int)tmp, (Int)-1, ilbl ); tmp=m; 
            realloc( &tmp,dsize,&ichl[0] ); tmp=m;
            realloc( &tmp,dsize,&ichl[1] ); tmp=m;
            realloc( &tmp,dsize,&iprn[0] ); tmp=m;
            realloc( &tmp,dsize,&iprn[1] ); tmp=m;
            for( jx=0;jx<nx;jx++ )
           {
              realloc( &tmp,dsize, x+jx ); tmp=m;
           }
            realloc( &tmp,dsize, &xsep ); //tmp=m;
            m= tmp;
        }
         ic=n;
         ichl[0][ic]=-1;
         ichl[1][ic]=-1;
         iprn[0][ic]= ip;
         iprn[1][ic]= jc;
         ilbl[ic]= in;
         xsep[ic]= xm[ix];
         if( ip >= 0 )
        { 
            ichl[jc][ip]= ic; 
        }
         else
        {
            iroot=ic; 
        }
         n++;
     }

// data
      for( jx=0;jx<nx;jx++ )
     {
         x[jx][ic]= y[jx];
     }

// label indexing
      tmp= nl;
      nl= max( nl,in+1 );
      if( nl >= ml )
     {
         realloc( &ml,dsize,&indx );
         setv( (Int)tmp,(Int)ml,(Int)-1,indx );
     }
      indx[in]= ic;
  }

   void cAdTree::print( Int ic, string tab )
  {
      Int ic0,ic1,ix;
      if( ic > -1 )
     { 
         ic0= ichl[0][ic];
         ic1= ichl[1][ic];
         cout << tab << " node: "<<ilbl[ic]<<": coordinates ";
         for( ix=0;ix<nx;ix++ )
        {
            cout << x[ix][ic]<<" ";
        }
         cout << " separation "<<xsep[ic] << " children "<<ic0<<" "<<ic1<<"";
         if( ilbl[ic] == -1 ){ cout << "<----------- hidden"; };
         cout << "\n";
         if( ic0 >-1 ){ print( ic0,tab+"   " ); }
         if( ic1 >-1 ){ print( ic1,tab+"   " ); }
     }
  }

   Int cAdTree::remove( Int in )
  {
      Int  ic,il,ir;
      Int  val=-1;
      ic= indx[in];
      if( ic > -1 )
     {
         il= nl-1;
         ir= indx[il];
         swap( ilbl+ic,ilbl+ir );
         indx[in]= indx[il];
         indx[il]= -1;
         ilbl[ic]= -1;
         val= --nl;
     }
      return val;
  }

   Int cAdTree::nchl( Int jc, Int ip )
  {
      Int ic;
      Int val;
      val= 0;
      ic= ichl[jc][ip];
      if( ic > -1 )
     { 
         val+= 1;
         val+= nchl(0,ic);
         val+= nchl(1,ic); 
     }
      return val;
  }

   void cAdTree::search( Real *bmin, Real *bmax, Int *np, Int *var )
  {
      Int               nstk=0;
      Int              *istk=NULL;
      Int              *dstk=NULL;

      Int               ix,jx,ip;
      bool              d;
      
      nstk= nchl(0,0)+ nchl(1,0)+ 1;
      istk= new Int[nstk+1];
      dstk= new Int[nstk+1];

      
      nstk=1;
      istk[nstk]= 0;
      dstk[nstk]= 0;

      while (nstk)
     {
         ip =   istk[nstk];
         ix =   dstk[nstk]; 
         nstk--;

         if( dbg ){ cout << "visiting node "<<ip<<" "<<ichl[0][ip]<<" "<<ichl[1][ip]<<" "<<ix<<"\n"; }

         if ( ilbl[ip] > -1 )
        {
            d= true;
            for( jx=0;jx<nx;jx++ )
           {
               d= d && ( x[jx][ip] >= bmin[jx] && x[jx][ip] <= bmax[jx] );
           }
            if( d )
           {
              var[*np] = ilbl[ip];
            (*np)++;
           }
        }

         jx= ix+1;
         if( jx == nx ){ jx=0; };

         if( ichl[0][ip] > -1 && bmin[ix] <= xsep[ip] )
        {
            if( dbg ){ cout << "  adding node "<<ichl[0][ip]<<" to the stack\n"; }
            nstk++;
            istk[nstk] = ichl[0][ip];
            dstk[nstk] = jx;
        }
         else
        {
            if( dbg )
           {  
               if( ichl[0][ip] >= 0 )
              {
                  cout << "  discarding left node "<<ichl[0][ip]<<" because "<<bmin[ix]<<" > "<<xsep[ip]<<" "<<ix<<"\n";
              }
           }

        }
         if ( ichl[1][ip] > -1 && bmax[ix] >= xsep[ip] )
        {
            if( dbg ){ cout << "  adding node "<<ichl[1][ip]<<" to the stack\n"; }
            nstk++;
            istk[nstk] = ichl[1][ip];
            dstk[nstk] = jx;
        }
         else
        {
            if( dbg )
           {  
               if( ichl[1][ip] >= 0 )
              {
                  cout << "  discarding right node "<<ichl[1][ip]<<" because "<<bmax[ix]<<" > "<<xsep[ip]<<" "<<ix<<"\n";
              }
           }
        }

      }

      delete[] istk;
      delete[] dstk;
      
      if( dbg ){ exit(0); };
  }
