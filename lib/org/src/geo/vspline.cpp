using namespace std;

#     include        <const.h>
#     include        <geo/vspline.h>
#     include        <utils/proto.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2
//
// Author          <PERSON> <<EMAIL>>
// Created         
// Changes History
// Next Change(s)  -

   cVSpline::cVSpline()
  {
      iord=-1;
      l=-1;
      nv=0;
      np=0;
      s= NULL;
      sx= NULL;
      sz= NULL;
      dx0= NULL;
      dx1= NULL;
      x= NULL;
      z= NULL;
  }

   void cVSpline::cleanup()
  {
      iord=-1;
      l=-1;
      nv=0;
      np=0;
      delete[] s; s= NULL;
      delete[] sx; sx= NULL;
      delete[] sz; sz= NULL;
      delete[] dx0; dx0= NULL;
      delete[] dx1; dx1= NULL;
      delete[] x; x= NULL;
      delete[] z; z= NULL;
  }

   cVSpline::~cVSpline()
  {
      cleanup();
  }

   void cVSpline::copy( cVSpline **var )
  {
      cVSpline *tmp;
      if( !(*var) )
     {
         tmp= new cVSpline();
         tmp->build( iord, np,nv, s,x, dx0,dx1 );
        *var= tmp;
     }
      else
     {
         cout << "cannot copy into already existing object (cVSpline::copy)\n";
     }
  }

   
   void cVSpline::fit( )
  {

      Real      *am,*a,*ap,*tmp0,*tmp1;
      Real       h0,h1,h;
      Int        iv,ip;

      const Real i3=1./3.,i6=0.5*i3;

      if( iord > 0 )
     {

         am=  new Real[np];
         a =  new Real[np];
         ap=  new Real[np];

         tmp0=new Real[nv];
         tmp1=new Real[nv];

         h1= s[1]- s[0];
         for( iv=0;iv<nv;iv++ )
        {   
           tmp1[iv]= x[iv][1]- x[iv][0];
           tmp1[iv]/= h1;
        }

// first point

         am[0]=0;
         a[0]=-i3*h1;
         ap[0]= -i6*h1;
         for( iv=0;iv<nv;iv++ )
            z[iv][0]= dx0[iv]-tmp1[iv];

         for( ip=1;ip<np-1;ip++ )
        {
            h0= h1;
            h1= s[ip+1]-s[ip];
            h=  h1+h0;
            for( iv=0;iv<nv;iv++ )
           {
               tmp0[iv]= tmp1[iv];
               tmp1[iv]= x[iv][ip+1]- x[iv][ip];
               tmp1[iv]/= h1;
           }
            am[ip]= i6*h0;
            a[ip]=  i3*h;
            ap[ip]= i6*h1;
            for( iv=0;iv<nv;iv++ )
               z[iv][ip]= tmp1[iv]-tmp0[iv];
        }

// last node 

         am[np-1]= i6*h1;
         a[np-1]=i3*h1;
         ap[np-1]=0;
         for( iv=0;iv<nv;iv++ )
            z[iv][np-1]= dx1[iv]-tmp1[iv];

// inversion
         thomas( np, am,a,ap, nv,np, sz );

// cleanup

         delete[] am;
         delete[]  a;
         delete[] ap;

         delete[] tmp1;
         delete[] tmp0;
         
     }
  };

   void cVSpline::build( Int Iord, Int Np, Int Nv, Real *S, Real *X[], Real *Dx0, Real *Dx1 )
  {
      Int    iv,ip;
      Real   d1,d2,d3,d;

      cleanup();

      np= Np;
      nv= Nv;

      s= new Real[np];
      sx= new Real[nv*np]; x= new Real*[nv]; subv( nv,np, sx,x );
      sz= new Real[nv*np]; z= new Real*[nv]; subv( nv,np, sz,z );
      dx0= new Real[nv]; setv( 0,nv, ZERO,dx0 );
      dx1= new Real[nv]; setv( 0,nv, ZERO,dx1 );
      
      for( iv=0;iv<nv;iv++ )
     {
         for( ip=0;ip<np;ip++ )
        {
            x[iv][ip]= X[iv][ip];
        }
     }
      setv( 0,np*nv, ZERO, sz);


      for( ip=0;ip<np;ip++ )
     {
         s[ip]= S[ip];
     }

      if( Dx0 )
     { 
         for( iv=0;iv<nv;iv++ )
        {
            dx0[iv]= Dx0[iv]; 
        }
     }
      else
     {
         for( iv=0;iv<nv;iv++ )
        {   
           dx0[iv]= x[iv][1]- x[iv][0];
           dx0[iv]/= ( s[1]-s[0] );
        }
     }
      if( Dx1 )
     {
         for( iv=0;iv<nv;iv++ )
        { 
             dx1[iv]= Dx1[iv]; 
        }
     }
      else
     {
         for( iv=0;iv<nv;iv++ )
        {   
           dx1[iv]= x[iv][np-1]- x[iv][np-2];
           dx1[iv]/= ( s[np-1]-s[np-2] );
        }
     }
      iord= Iord;
      fit( );

  };

   void cVSpline::interp( Real t0, Real *y, Real *dy )
  {
      Int          ip,iv;
      Real         t,a,b,c,d;
      Real         h,h6,ds;
      const Real   i3=1./3.,i6=0.5*i3;

      t= t0;
      if( t < s[0] )
     {
         interp( s[0],y,dy );
         ds= t-s[0];
         for( iv=0;iv<nv;iv++ )
        {
            y[iv]+= dy[iv]*ds;
        }
         
     }
      else
     {
         if( t > s[np-1] )
        {

            interp( s[np-1],y,dy );
            ds= t-s[np-1];
            for( iv=0;iv<nv;iv++ )
           {
               y[iv]+= dy[iv]*ds;
           }
        } 
         else
        {
            ip= bsearch( t, np-1,s+1 );
      
            h= s[ip+1]-s[ip];
            a= (s[ip+1]-t)/h;
            b= 1.-a;
     
            if( iord > 0 )
           {
               h6= i6*h*h;
               c= a*(a*a-1)*h6;
               d= b*(b*b-1)*h6;
               for( iv=0;iv<nv;iv++ )
              {
    
                 y[iv]=   a*x[iv][ip]+  b*x[iv][ip+1]+ 
                         c*z[iv][ip]+  d*z[iv][ip+1];
   
                 dy[iv]= (x[iv][ip+1]-x[iv][ip])/h-
                          i6*( 3.*a*a-1 )*h*z[iv][ip]+
                          i6*( 3.*b*b-1 )*h*z[iv][ip+1];
   
              }
           }
            else
           {
               for( iv=0;iv<nv;iv++ )
              {
  
                  y[iv]=   a*x[iv][ip]+  b*x[iv][ip+1];
                  dy[iv]= (x[iv][ip+1]-x[iv][ip])/h;
              }
           }
        }
     }
  }
