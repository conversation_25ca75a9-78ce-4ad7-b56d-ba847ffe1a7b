   using namespace std;

#  include <device/worker/dom.h>

   void cDom::picklec( size_t *len, pickle_t *buf )
  {
      cDevice::picklec( len,buf );
      pckle( len,dlpl,buf );
      pckle( len,dlpo,buf );
      pckle( len,drpl,buf );
      pckle( len,drpo,buf );
      pckle( len,dppl,buf );
      pckle( len,dppo,buf );
      pckle( len,dbpl,buf );
      pckle( len,dbpo,buf );
      pckle( len,nlev,buf );
      pckle( len,misc,buf );
  }

   void cDom::unpicklec( size_t *len, pickle_t buf )
  {
      cDevice::unpicklec( len,buf );
      unpckle( len,&dlpl,buf );
      unpckle( len,&dlpo,buf );
      unpckle( len,&drpl,buf );
      unpckle( len,&drpo,buf );
      unpckle( len,&dppl,buf );
      unpckle( len,&dppo,buf );
      unpckle( len,&dbpl,buf );
      unpckle( len,&dbpo,buf );
      unpckle( len,&nlev,buf );
      unpckle( len,&misc,buf );
  }
