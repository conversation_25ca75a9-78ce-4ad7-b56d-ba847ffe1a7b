   using namespace std;

#  include <device/worker/dom.h>

   void cDom::get( cTabData *rfl )
  {
      cTabItem *tmp;

      cDevice::get( rfl );

      tmp= new cTabItem( dlpl ); rfl->append( "domain-loader-library",      tmp );
      tmp= new cTabItem( dlpo ); rfl->append( "domain-loader-object",       tmp );
      tmp= new cTabItem( drpl ); rfl->append( "domain-runtime-library",     tmp );
      tmp= new cTabItem( drpo ); rfl->append( "domain-runtime-object",      tmp );
      tmp= new cTabItem( dppl ); rfl->append( "domain-partitioner-library", tmp );
      tmp= new cTabItem( dppo ); rfl->append( "domain-partitioner-object",  tmp );
      tmp= new cTabItem( dbpl ); rfl->append( "domain-boundary-library",    tmp );
      tmp= new cTabItem( dbpo ); rfl->append( "domain-boundary-object",     tmp );
      tmp= new cTabItem( nlev ); rfl->append( "multigrid-levels",           tmp );
      tmp= new cTabItem( misc ); rfl->append( "misc",                       tmp );
      rfl->set( "domain-loader-library",     dlpl );
      rfl->set( "domain-loader-object",      dlpo );
      rfl->set( "domain-runtime-library",    drpl );
      rfl->set( "domain-runtime-object",     drpo );
      rfl->set( "domain-partitioner-library",dppl );
      rfl->set( "domain-partitioner-object", dppo );
      rfl->set( "domain-boundary-library",   dbpl );
      rfl->set( "domain-boundary-object",    dbpo );
      rfl->set( "multigrid-levels",          nlev );
      rfl->set( "misc",                      misc );
  }

   void cDom::set( cTabData *rfl )
  {

      cDevice::set( rfl );

      rfl->get( "domain-loader-library",     &dlpl );
      rfl->get( "domain-loader-object",      &dlpo );
      rfl->get( "domain-runtime-library",    &drpl );
      rfl->get( "domain-runtime-object",     &drpo );
      rfl->get( "domain-partitioner-library",&dppl );
      rfl->get( "domain-partitioner-object", &dppo );
      rfl->get( "domain-boundary-library",   &dbpl );
      rfl->get( "domain-boundary-object",    &dbpo );
      rfl->get( "multigrid-levels",          &nlev );
      rfl->get( "misc",                      &misc );

/*    cout << dlpl<<"\n";
      cout << dlpo<<"\n";
      cout << drpl<<"\n";
      cout << drpo<<"\n";
      cout << dppl<<"\n";
      cout << dppo<<"\n";
      cout << dbpl<<"\n";
      cout << dbpo<<"\n";
      cout << nlev<<"\n";
      cout << misc<<"\n";*/
  }

