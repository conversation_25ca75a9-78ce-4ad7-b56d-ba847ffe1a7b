   using namespace std;

#  include <device/worker/dom.h>
#  include <domain/cfd/domain.h>

   cDom::cDom()
  {
      dlpl="fallback";
      dlpo="fallback";
      drpl="fallback";
      drpo="fallback";
      dppl="fallback";
      dppo="fallback";
      dbpl="fallback";
      dbpo="fallback";
      misc="defaults";

      dlp= NULL;
      drp= NULL;
      dbp= NULL;
      dpp= NULL;

      dmn=  NULL;
      nlev=0;
      b_get_resd_info = false;
  }

   cDom::~cDom()
  {

      dlpl="fallback";
      dlpo="fallback";
      drpl="fallback";
      drpo="fallback";
      dppl="fallback";
      dppo="fallback";
      dbpl="fallback";
      dbpo="fallback";

      delete dmn; dmn=NULL;
      nlev=0;

      delete dlp; dlp= NULL;
      delete drp; drp= NULL;
      delete dbp; dbp= NULL;
      delete dpp; dpp= NULL;
      b_get_resd_info = false;
  }

   void cDom::loaddmn()
  {
//      cout << "should be loading the domain " << dmn << " ???\n";
      dlp_t     dlp_f;
      if( !dmn )
     {
         if( !dlp )
        {
            dlp= new cPlugin( dlpl,dlpo );
        }
         dlp_f= (dlp_t)dlp->Sub();
       (*dlp_f)( this,NULL,NULL,rcse, &dmn );
     }
  }

   void cDom::getcost( Real *cost )
  {
      Real wrk;
      cDevice::getcost( cost );
      if( active )
     {
         if( !delegate )
        {
            loaddmn();
            wrk= 0;
            dmn->getcost( &wrk );
            cout << "the domain returned "<<wrk<<"\n";
           *cost+= wrk;
        }
     }
  }

   void cDom::partition()//; Int mcpu, Int *jcpu, Int *jlcpu, Real *cload, Real tload )
  {
	      
      Int i;
      dpp_t   dpp_f;

      cDevice::partition();// mcpu,jcpu,jlcpu,cload,tload );
      if( active && !delegate )
     {
         if( !dpp )
        {
            dpp= new cPlugin( dppl,dppo );
        }
         dpp_f= (dpp_t)dpp->Sub();

         delete[]  icpu; icpu= NULL;
         delete[]  acpu; acpu= NULL;
         delete[] inode;inode= NULL;
         delete[] icore;icore= NULL;

//       delete[]  cpul;
/*       icpu= new Int[mcpu];  setv( (Int)0,mcpu,  (Int)0,  icpu );
         acpu= new Int[mcpu];  setv( (Int)0,mcpu,  (Int)0,  acpu );
         cpul= new Real[mcpu]; setv( (Int)0,mcpu, (Real)0., cpul );*/

         ncpu=0;
       (*dpp_f)( dmn, rcse, &ncpu,&inode,&icore,&icpu );//this,dmn,tload,cload, jcpu,jlcpu, &ncpu,icpu,cpul );
       
         acpu= new Int[ncpu]; setv(0,ncpu, (Int)0,acpu );
//       cout << "device will occupy cpus up to "<<*jlcpu<<"\n";

         delete dmn; dmn= NULL;

     }
  }

   void cDom::preprocess()
  {
      Int ic;
      cDevice::preprocess();
      if( active && !delegate )
     {

         for( ic=0;ic<ncpu;ic++ )
        {
            if( acpu[ic] == rank )
           {
               impersonate( ic );
               loaddmn();
               dmn->prep( ic );
           }
        }
         delete dmn; dmn= NULL;
     }
  }

   void cDom::compute()
  {
      Int ic;
      Int ir,jr;
      cDevice::compute();
      if( active && !delegate )
     {
         if( resident )
        {
            MPI_Comm_rank( pcom,&ir );
            MPI_Comm_rank( MPI_COMM_WORLD,&jr );
//          cout << "device "<<name<<" computes local rank "<<ir<<" global rank "<<jr<<"\n";

            if(!b_get_resd_info) 
           {
               b_get_resd_info = true;
               cout << this->getname() << " " << dmn->get_resd_info() << "\n";
           }

            loadpre();
            dmn->comp();
            //dmn->comp2();
            ////dmn->comp3();
        }
     }
  }

   void cDom::loadpre()
  {
      drp_t     drp_f;
//    cout << "loadpre called for device "<<name<<"\n";
      if( !dmn )
     {
/*       cout << "domain does not exist: loading ...\n";
         cout << "Dom device compute "<<irnk << "\n";*/
         if( !dmn )
        {
/*          if( !drp )
           {
               drp= new cPlugin( drpl,drpo );
           }
            drp_f= (drp_t)drp->Sub();
          (*drp_f)( this,NULL,NULL,rcse, &dmn );*/
            dmn= new cFdDomain();
            dmn->assgndev( this );
        }
         dmn->loadpre( );
//       cout << ".. done\n";
     }
      else
     {
//       cout << "no action taken\n";
     }
  }

   void cDom::makebox()
  {
      cout << "forbidden \n";
      return;
      Int ix;
      Real x0[2],x1[2];
      if( active && !delegate )
     {
         if( resident )
        {
            cout << "cDom makebox "<<name<<"\n";
            loadpre();
//          dmn->makebox( &bx );
            cout << "loaded domain\n";
        }
         else
        {
            reset( &bx );
        }
         gming( 2,bx.x0 );
         gmaxg( 2,bx.x1 );
/*       cout << name <<" ";
         for( ix=0;ix<2;ix++ )
        {
            cout << x0[ix]<<" "<<x1[ix]<<" ";
        }
         cout << "\n";*/

     }
      cDevice::makebox();
  }



   void cDom::makeboxes( Int *n, Int *m, box_t **b, Int **i, cDevice ***d, string **str )
  {
      Int      n1,m1,m0,dsz;
      Int      j;
      cDevice *dev;
      cDevice::makeboxes( n,m,b,i,d,str );
      if( active && !delegate )
     {
         n1= *n;
         m1= *m;
         m0= *m;
         if( resident )
        {
            loadpre(); 
            dmn->makeboxes( n,m,b,i,str );
        }
         gmaxg( 1,n );
         gmaxg( 1,m );
         if( !resident )
        {
             if( *m > m1 )
            {
                dsz=*m-m1;
                m0=m1; realloc( &m0,dsz, b );
                m0=m1; realloc( &m0,dsz, i );
                m0=m1; realloc( &m0,dsz, str );
            }
            for( j=n1;j<*n;j++ )
           {
               reset( (*b)+j ); 
             (*i)[j]=-1;
             (*str)[j]=unassigned;
           }
        }
         if( *m > m1 )
        {
            dsz=*m-m1;
            m0=m1; realloc( &m0,dsz, d );
        }
         for( j=n1;j<*n;j++ )
        {
            gmaxg( 2,((*b)[j]).x1 );
            gming( 2,((*b)[j]).x0 );
            gmaxg( 1,(*i)+j );
          (*d)[j]= this;
        }
       
      
     }
  }

   void cDom::request( Int ig, Int *n, Int *m, Int *l, Real **x )
  {
      dmn->request( ig,n,m,l,x );
  }

   void cDom::service( Int ig, Int nx, Int nv, Int nq, Real *x[], Real **q, bool bposix )
  {
      dmn->service( ig,nx,nv,nq,x,q, bposix );
  }

   void cDom::service( Int ig, Int nx, Int nv, Int nq, Real *x, Real **q )
  {
      dmn->service( ig,nx,nv,nq,x,q );
  }

   void cDom::accept( Int ig, Int nv, Int nq, Real *sq, Real *w )
  {
      dmn->accept( ig,nv,nq,sq,w );
  }

   void cDom::postprocess()
  {
      Int ic;
      cDevice::postprocess();
      if( active && !delegate )
     {
         for( ic=0;ic<ncpu;ic++ )
        {
            if( acpu[ic] == rank )
           {
               impersonate( ic );
               loaddmn();
               cout << "DOMAIN POSTPROCESS DOMAIN IS "<<dmn<<"\n";
               dmn->rebuild( ic );
           }
        }
         dmn->postprocess();
         delete dmn; dmn= NULL;
     }
  }

   void cDom::postprocess( Int it )
  {
      Int ic;
      cDevice::postprocess();
      if( active && !delegate )
     {
         for( ic=0;ic<ncpu;ic++ )
        {
            if( acpu[ic] == rank )
           {
               impersonate( ic );
               loaddmn();
               cout << "DOMAIN POSTPROCESS DOMAIN IS "<<dmn<<"\n";
               dmn->rebuildunst( ic, it );
           }
        }
         delete dmn; dmn= NULL;
     }
  }

   void cDom::ppostprocess()
  {
      Int ic;
      cDevice::ppostprocess();
      if( active && !delegate )
     {
         for( ic=0;ic<ncpu;ic++ )
        {
            if( acpu[ic] == rank )
           {
//             loaddmn();
               loadpre();
               cout << "DOMAIN POSTPROCESS DOMAIN IS "<<dmn<<"\n";
               dmn->ppostprocess();
           }
        }
         delete dmn; dmn= NULL;
     }
  }

   void cDom::outputjl09()
  {
      Int ic;
      cDevice::postprocess();
      if( active && !delegate )
     {
         for( ic=0;ic<ncpu;ic++ )
        {
            if( acpu[ic] == rank )
           {
               impersonate( ic );
               loaddmn();
               cout << "output jl09 files for domain " <<dmn<<"\n";
               dmn->rebuild( ic );
           }
        }
         dmn->outputjl09();
         delete dmn; dmn= NULL;
     }
  }

   void cDom::starthist()
  {
      Int ic;
      cDevice::starthist();
      if( active && !delegate )
     {
         string fnm;
         fnm= getcpath()+"/hist.dat";
         //cout << "dump residuals to " << fnm << "\n";
         flg.setf( ios_base::scientific );
         flg.width( 20 );
         flg.precision( 12 );
         gopen(&flg, fnm);
         if(dmn)
        {
           dmn->starthist(&flg);
        }
     }
  }

   void cDom::endhist()
  {
      Int ic;
      cDevice::endhist();
      if( active && !delegate )
     {
         gclose(&flg);
     }
  }

