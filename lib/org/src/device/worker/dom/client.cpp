   using namespace std;

#  include <device/worker/dom.h>

   void cDom::checkc( string tab )
  {
//      cout << tab << "domain device ";
//      cDevice::checkc( tab );
//      cout << tab << " domain load library      "<<dlpl<<"\n";
//      cout << tab << " domain load object       "<<dlpo<<"\n";
//      cout << tab << " domain runtime library   "<<drpl<<"\n";
//      cout << tab << " domain runtime object    "<<drpo<<"\n";
//      cout << tab << " domain partition library "<<dppl<<"\n";
//      cout << tab << " domain partition object  "<<dppo<<"\n";
//      cout << tab << " domain boundary library  "<<dbpl<<"\n";
//      cout << tab << " domain boundary object   "<<dbpo<<"\n";
//      cout << tab << " multigrid levels         "<<nlev<<"\n";
//      cout << tab << " misc                     "<<misc<<"\n";
  }
