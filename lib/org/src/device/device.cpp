   using namespace std;

#  include <device/device.h>

   cDevice::cDevice()
  {
      cpath=    unassigned;
      active=   false;
      delegate= false;
      rcse=     NULL;
      reset( &bx );
      ntfs=0;
      mtfs=0;
      ntf=NULL;
      pthread_mutex_init( &qmutx,NULL );
      pthread_mutex_init( &umutx,NULL );
      sync= true;

      stime=0;

//      chl = NULL;
//      nxt = NULL;
//      prn = NULL;
      b_show_leaf_info = false;

      dev_type = "empty";
  };

   cDevice::cDevice( cDevice *data )
  {
      
      ntfs=0;
      mtfs=0;
      ntf=NULL;
      cpath=    unassigned;
      active=   false;
      delegate= false;
      rcse=     NULL;
      //context( data );
      uid= -1;
      reset( &bx );
      sync= true;
      pthread_mutex_init( &qmutx,NULL );
      pthread_mutex_init( &umutx,NULL );

//      chl = NULL;
//      nxt = NULL;
//      prn = NULL;
      b_show_leaf_info = false;

      dev_type = "empty";
  }

   cDevice::~cDevice()
  {
      Int i;
      active=   false;
      delegate= false;
      rcse=      NULL;
      reset( &bx );
      for( i=0;i<ntfs;i++ )
     { 
         delete ntf[i]; 
         ntf[i]=NULL; 
     }
      delete[] ntf; ntf=NULL;
      ntfs=0;mtfs=0;
      pthread_mutex_destroy( &qmutx );
      pthread_mutex_destroy( &umutx );

//      chl = NULL;
//      nxt = NULL;
//      prn = NULL;

      dev_type = "empty";
  };

   void cDevice::assigncase( cCase *cse )
  {
      Int        n=0,i;
      string    *names=NULL;
      string     tmp;
      char       buf[1000];
      int        val;
      cDevice   *dev;

      cout << "void cDevice::assigncase( cCase *cse ) not used anymore \n";
      assert(0);
      if( cse )
     {
         rcse= cse;

         //ancestors( &n,&names );
         cpath= ".";
         for( i=n-1;i>=0;i-- )
        {  
            cpath= cpath+"/"+names[i]; 
        }
         cpath= cpath+"/"+name;
         tmp= cpath;
         cpath= cpath+"/"+rcse->getname();
         delete[] names; 
         n=0;

         getwd(buf);
         val= chdir( tmp.c_str() );
         if( val != 0 )
        {
            mkdir( tmp.c_str(), 0777U );
        }
         chdir(buf);
         val= chdir( cpath.c_str() );
         if( val != 0 )
        {
            mkdir( cpath.c_str(), 0777U );
        }
         chdir(buf);

         delegate= false; 
         //if( chl )
         if( !children.empty() )
        {
            //delegate= delegate || chl->active;
            delegate= delegate || children[0]->active;
        }
     }
      
//      dev= chl;
//      while( dev )
//     {
//         dev->assigncase( cse );
//         dev= dev->nxt;
//     } 
      for (size_t i = 0; i < children.size(); i++)
     {
         dev = children[i];
         dev->assigncase( cse );
     }

  }

   void cDevice::dismisscase()
  {
      cDevice *dev;
      rcse= NULL;
      cpath=unassigned;
//      dev= chl;
//      while( dev )
//     {
//         dev->dismisscase();
//         dev= dev->nxt;
//     }
      for (size_t i = 0; i < children.size(); i++)
     {
         dev = children[i];
         dev->dismisscase();
     }
  }

   void cDevice::getcost( Real *cost )
  {
      cDevice *dev;
      if( active )
     {
         cout << "evaluating cost for "<<name<<" \n";
         if( delegate )
        {
            cout << "device "<<name<<" delegates\n";
           // dev= chl;
           // while( dev )
           //{
           //    dev->getcost( cost );
           //    dev= dev->nxt;
           //}
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->getcost(cost);
           }
        }
     }
  }

   void cDevice::partition()//Int mcpu, Int *jcpu, Int *jlcpu, Real *cload, Real tload )
  {
      cDevice *dev;
      cout << "inside cDevice::partition " <<  getname() << " active " << active << "\n";
      if( active )
     {
//         cout << "   inside cDevice::partition " <<  getname() << " delegate " <<   delegate << " \n";
         if( delegate )
        {
//            dev= chl;
//            while( dev )
//           {
////               cout << "       inside cDevice::partition " <<  getname() << " parition device " << dev << "\n";
//               dev->partition( );//mcpu,jcpu,jlcpu,cload,tload );
//               dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               cout << "       inside cDevice::partition " <<  getname() << " parition device " << dev << "\n";
               dev->partition();
           }
        }
     }
  }

   void cDevice::occupy( Int *ist, Int mcpu )
  {
      Int ic;
      cDevice *dev; 

      if( active )
     {
         if( delegate )
        {
//            dev= chl;
//            while( dev )
//           {
//               dev->occupy( ist,mcpu );
//               dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->occupy( ist,mcpu );
           }
        }
         else
        {
            for( ic=0;ic<ncpu;ic++ )
           {
               acpu[ic]= (*ist)++;
               if( *ist == mcpu )
              {
                *ist=0;
              }
           }
        }
     }
  }

   void cDevice::occupy()
  {
      Int ic;
      cDevice *dev;
      if( active )
     {
         if( delegate )
        {
//            dev= chl;
//            while( dev )
//           {
//               dev->occupy();
//               dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->occupy();
           }
        }
         else
        {
            for( ic=0;ic<ncpu;ic++ )
           {
               acpu[ic]= icpu[ic];
           }
            identify();
            if( resides() )
           {
               Int i;
               MPI_Comm_rank( MPI_COMM_WORLD,&i );
               cout << name << " resides on cpu "<<i<<"\n";
           }
        }
     }
  }

   void cDevice::preprocess()
  {
      Int i;
      cDevice *dev;
      if( active )
     {
         if( delegate )
        {
//            dev= chl;
//            while( dev )
//           {
//                dev->preprocess();
//                dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->preprocess();
           }
        }
     }
  }

   void cDevice::compute()
  {
      Int i;
      cDevice *dev;
      //if(!b_show_leaf_info) show_leaf_info();
      if( active )
     {
         if( delegate )
        {
//            dev= chl;
//            while( dev )
//           {
//                dev->compute();
//                dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->compute();
           }
        }
     }
  }

   void cDevice::postprocess()
  {
      Int i;
      cDevice *dev;
      if( active )
     {
         cout << name <<" POSTPROCESS <================================================\n";
         if( delegate )
        {
//            dev= chl;
//            while( dev )
//           {
//                dev->postprocess();
//                dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->postprocess();
           }
        }
     }
  }

   void cDevice::postprocess( Int it0)
  {
      Int i;
      cDevice *dev;
      if( active )
     {
         cout << name <<" POSTPROCESS <================================================\n";
         if( delegate )
        {
//            dev= chl;
//            while( dev )
//           {
//                dev->postprocess( it0 );
//                dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->postprocess(it0);
           }
        }
     }
  }

   void cDevice::ppostprocess()
  {
      Int i;
      cDevice *dev;
      if( active )
     {
         cout << name <<" POSTPROCESS <================================================\n";
         if( delegate )
        {
//            dev= chl;
//            while( dev )
//           {
//                dev->ppostprocess();
//                dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->ppostprocess();
           }
        }
     }
  }

   void cDevice::outputjl09()
  {
//      Int i;
//      cDevice *dev;
//      if( active )
//     {
//         cout << "=========output jl09 files for " << name <<"==============\n";
//         if( delegate )
//        {
//            dev= chl;
//            while( dev )
//           {
//                dev->outputjl09();
//                dev= dev->nxt;
//           }
//        }
//     }
  }

   void cDevice::starthist()
  {
      Int i;
      cDevice *dev;
      if( active )
     {
         if( delegate )
        {
//            dev= chl;
//            while( dev )
//           {
//                dev->starthist();
//                dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->starthist();
           }
        }
     }
  }

   void cDevice::endhist()
  {
      Int i;
      cDevice *dev;
      if( active )
     {
         if( delegate )
        {
//            dev= chl;
//            while( dev )
//           {
//                dev->endhist();
//                dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->endhist();
           }
        }
     }
  }

   void cDevice::makebox()
  {
      cDevice *dev;
      if( active )
     {
         if( delegate )
        {
            reset( &bx );
//            dev= chl;
//            while( dev )
//           {
//                dev->makebox();
//                ::add( &bx,&(dev->bx) );
//                dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->makebox();
               ::add( &bx,&(dev->bx) );
           }
        }
         cout << name<<" "<<bx.x0[0]<<" "<<bx.x0[1]<<" "<<bx.x1[0]<<" "<<bx.x1[1]<<"\n";
     }
  }

   void cDevice::makeboxes( Int *n, Int *m, box_t **b, Int **i, cDevice ***d, string **str )
  {
      cDevice *dev;
      if( active )
     {
         if( delegate )
        {
//            dev= chl;
//            while( dev )
//           {
//               dev->makeboxes( n,m,b,i,d,str );
//               dev= dev->nxt;
//           }
            for (size_t id = 0; id < children.size(); id++)
           {
               dev = children[id];
               dev->makeboxes( n,m,b,i,d,str );
           }
        }
     }
  }

   void cDevice::interface( cInterf *data )
  {
      Int dsize=5;
      if( ntfs == mtfs )
     {
         realloc( &mtfs,dsize, &ntf ); 
     }
      ntf[ntfs]= data;
      ntfs++;
  }

   void cDevice::shutdown()
  {
      Int i;
      cDevice *dev;
      if( active )
     {
         if( delegate )
        {
//            dev= chl; 
//            while( dev )
//           {
//               dev->shutdown();
//               dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->shutdown();
           }
        }
         else
        {
            if( resident )
           {
               int i;
               bool val;
               for( i=0;i<ntfs;i++ ){ ntf[i]->halt(); }
               do
              {
                  val= true;
                  for( i=0;i<ntfs;i++ )
                 {
                     val= val && ntf[i]->released();
                 }
              }while( !val );
               for( i=0;i<ntfs;i++ ){ ntf[i]->join(); }

           }
        }
     }
  }

   void cDevice::shutdown2()
  {
      Int i;
      cDevice *dev;
      if( active )
     {
         if( delegate )
        {
//            dev= chl; 
//            while( dev )
//           {
//               dev->shutdown2();
//               dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->shutdown2();
           }
        }
         else
        {
            if( resident )
           {
/*             int i;
               bool val;
               for( i=0;i<ntfs;i++ ){ ntf[i]->halt2(); }
               do
              {
                  val= true;
                  for( i=0;i<ntfs;i++ )
                 {
                     val= val && ntf[i]->released2();
                 }
              }while( !val );*/
           }
        }
     }
  }

   void cDevice::request()
  {
      Int i;
      cDevice *dev;
      if( active )
     {
         if( delegate )
        {
//            dev= chl; 
//            while( dev )
//           {
//               dev->request();
//               dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->request();
           }
        }
         else
        {
            if( resident )
           {
               for( i=0;i<ntfs;i++ )
              { 
                  ntf[i]->request(); 
              }
           }
        }
     }
  }

   void cDevice::request2()
  {
      Int i;
      cDevice *dev;
      if( active )
     {
         if( delegate )
        {
//            dev= chl; 
//            while( dev )
//           {
//               dev->request2();
//               dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->request2();
           }
        }
         else
        {
            if( resident )
           {
               for( i=0;i<ntfs;i++ )
              { 
                  ntf[i]->request2(); 
              }
           }
        }
     }
  }

   void cDevice::poll()
  {
      Int i;
      cDevice *dev;
      bool     done;
      if( active )
     {
         if( delegate )
        {
//            dev= chl; 
//            while( dev )
//           {
//               dev->poll();
//               dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->poll();
           }
        }
         else
        {
            if( resident )
           {
               do
              {
                  done=true;
                  for( i=0;i<ntfs;i++ )
                 { 
                     done= done && ntf[i]->poll(); 
                 }
              }while( !done );
/*             lock();
               MPI_Barrier(pcom);
               unlock();*/
           }
        }
     }
  }

   void cDevice::poll2()
  {
      Int i;
      cDevice *dev;
      bool     done;
      if( active )
     {
         if( delegate )
        {
//            dev= chl; 
//            while( dev )
//           {
//               dev->poll2();
//               dev= dev->nxt;
//           }
            for (size_t i = 0; i < children.size(); i++)
           {
               dev = children[i];
               dev->poll2();
           }
        }
         else
        {
            if( resident )
           {
               do
              {
                  done=true;
                  for( i=0;i<ntfs;i++ )
                 { 
                     done= done && ntf[i]->poll2(); 
           //          cout << i << " " << ntf[i]->poll2() << "\n";
                 }
              }while( !done );
           }
        }
     }
  }

   void cDevice::request( Int ig, Int *n, Int *m, Int *l, Real **x )
  {
      *n=0;
      *m=0;
      *l=0;
      *x=NULL;
  }

   void cDevice::service( Int ib, Int nx, Int nv, Int nq, Real *x[], Real **q, bool bposix )
  {
      if( nq > 0 )
     {
        *q= new Real[nq*(nv+1)];
         setv( 0,nq*(nv+1), ZERO, *q );
     }
      else
     {
         *q=NULL;
     }
  }

   void cDevice::service( Int ib, Int nx, Int nv, Int nq, Real *x, Real **q )
  {
      if( nq > 0 )
     {
        *q= new Real[nq*(nv+1)];
         setv( 0,nq*(nv+1), ZERO, *q );
     }
      else
     {
         *q=NULL;
     }
  }

   void cDevice::accept( Int ib, Int nv, Int nq, Real *sq, Real *w )
  {

  }
   void cDevice::qlock()
  {
      pthread_mutex_lock(&qmutx);
  }

   void cDevice::qunlock()
  {
      pthread_mutex_unlock(&qmutx);
  }
   void cDevice::ulock()
  {
      pthread_mutex_lock(&umutx);
  }

   void cDevice::uunlock()
  {
      pthread_mutex_unlock(&umutx);
  }
