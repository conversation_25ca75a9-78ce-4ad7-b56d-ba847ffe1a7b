   using namespace std;

#  include <device/device.h>
#  include <utils/proto.h>

   void cDevice::picklec( size_t *len, pickle_t *buf )
  {
      cAudited::pickle( len,buf );
      pckle( len,active,buf );
      pckle( len,ncpu, icpu,buf );
      pckle( len,ncpu, acpu,buf );
      pckle( len,ncpu,inode,buf );
      pckle( len,ncpu,icore,buf );
  }

   void cDevice::unpicklec( size_t *len, pickle_t buf )
  {
      Int tmp,mcpu;
      cAudited::unpickle( len,buf );
      unpckle( len,&active,buf );
      delete[]  icpu;  icpu=NULL;
      delete[]  acpu;  acpu=NULL;
      delete[] inode; inode=NULL;
      delete[] icore; icore=NULL;
      unpckle( len,&ncpu, &icpu,buf );
      unpckle( len,&ncpu, &acpu,buf );
      unpckle( len,&ncpu,&inode,buf );
      unpckle( len,&ncpu,&icore,buf );
  }
