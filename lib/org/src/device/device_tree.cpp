   using namespace std;

#  include <filesystem>
#  include <sys/stat.h>
#  include <device/device.h>
#  include <device/client.h>

namespace fs = std::filesystem;

   string trim(const string & s)
  {
      size_t start = s.find_first_not_of(" \t");
      size_t end = s.find_last_not_of(" \t");
      return (start == std::string::npos) ? "" : s.substr(start, end - start + 1);
  }

   int dir_exist(const char *path)
  {
      struct stat stats;

      stat(path, &stats);

      // Check for file existence
      if (S_ISDIR(stats.st_mode)) 
     {
         return 1;
     }
      return 0;
  }

   cDevice* newobject( string full_nm )
  {
      Int ityp;
      cDevice *var;
      var= NULL;
      string dev_type, name;;

      size_t pos = full_nm.find(':');
      if (pos != std::string::npos)
     {
         dev_type = full_nm.substr(0, pos);
         name = full_nm.substr(pos + 1);
     }
      else
     {
         dev_type = "empty";
         name = full_nm;
     } 

      ityp= inlst( dev_type,dev_num,(string*)device_s );

      switch( ityp )
     {
         case( dev_empty ):
        {
            var= new cEmptyDevice();
            break;
        }
         case( dev_dom ):
        {
//            cout << "--------------create cDom object---------\n";
            var= new cDom();
            break;
        }
     }
      return var;
  }

//   cDevice* cDevice::newobject( Int typ )
//  {
//      cDevice *var;
//      var= NULL;
//      switch( typ )
//     {
//         case( dev_empty ):
//        {
//            var= new cEmptyDevice();
//            break;
//        }
//         case( dev_dom ):
//        {
////            cout << "--------------create cDom object---------\n";
//            var= new cDom();
//            break;
//        }
//     }
//      return var;
//  }


   bool cDevice::checkin_dev( cDevice *data )
  {
      bool btmp=false;
      bool val = false;

//      cout << "device " << this->getname() << " try to checkin device " << data->getname() << "\n";
      btmp = find_dev(data->getname());
      if(!btmp)
     {
//         cout << data->getname() << " did not exit, try to add " << data->getname() << "\n";
         add_dev( data );
         val= true;
     }

      return val;
  }

   void cDevice::add_dev( cDevice *data )
  { 
//      if( chl )
//     {
//         cout << "child exist, append " << data->getname() << " to child list \n";
//         chl->append_dev( data );
//     }
//      else
//     {
//         cout << "child does not exist, set " << data->getname() << " as the first child\n";
//         chl= data;
//     }
////      data->prn= this;
  }

   void cDevice::append_dev( cDevice *data )
  {
//      if( nxt )
//     {
//         cout << "child next, exist, append " << data->getname() << " to child list \n";
//         nxt->append_dev(data);
//     }
//      else
//     {
//         cout << "child next does not exist, append " << data->getname() << " in front of " << this->getname() << " \n";
//         nxt= data;
//         data->prv= this;
//     }
  };

   cDevice* cDevice::find_dev( string data )
  {
//      cDevice    *val= NULL;
//      cDevice    *tmp= NULL;
//
//      if( name == data )
//     {
//         val=this;
//     }
//      else
//     {
//         tmp= chl;
//         while( tmp )
//        {
//            val= tmp->find_dev( data );
//            if( val )
//           {
//               break;
//           }
//            tmp= tmp->nxt;
//        }
//     }
//      return val;
  };

   void cDevice::show_devtree()
  {
//      Int tmpndev;
//      cDevice    *tmp= NULL;
//
//      tmpndev = 1;
//      cout << "device " << tmpndev << " " << this->getname() << "\n";
//
//      tmp=chl;
//      while( tmp )
//     {
//         tmpndev++;
//         cout << "   +device " << tmpndev << " " << tmp->getname() << "\n";
//         tmp= tmp->nxt;
//     }
//      cout << "total number of devices " << tmpndev << "\n";
  }

   void cDevice::show_leaf_info()
  {
//      Int tmpndev;
//      cDevice    *tmp= NULL;
//      string devlist="";
//
//      tmpndev = 0;
//      tmp=chl;
//      while( tmp )
//     {
//         tmpndev++;
//         tmp= tmp->nxt;
//     }
//
//      tmp=chl;
//      while( tmp )
//     {
//         devlist += tmp->getname() + " ";
//         tmp= tmp->nxt;
//     }
//      if(tmpndev>0) cout << "ModelName " << tmpndev << " " << devlist << "\n";
//      b_show_leaf_info = true;
  }

   void cDevice::save_dev()
  {
      string                                fnme, tmppath;
      FILE                                    *f;
      size_t                                 len=0;
      size_t                                   n=0;
      pickle_t                               buf=NULL;
      char cwd[1000]; 

      bool                                   val;


      pickle_dev(&len,&buf );

      tmppath = "./";
      fnme= "./" + this->getname()+".dev";
      f= fopen( fnme.c_str(),"w" );
      fwrite( &len, sizeof( len), (size_t)1, f );
      fwrite(  buf, sizeof(*buf),       len, f );
      fclose( f );

      delete[] buf; buf=NULL; len=0;
  }

   void cDevice::pickle_dev( size_t *len, pickle_t *buf )
  {
      Int n;
      Int typ;
      cDevice *tmp;

//      cout << "pickling "<< this->getname() << " "<< this->gettype()<<"\n";
      typ= this->gettype();
      cPickle::pckle( len,typ, buf );
      //var->picklec( len,buf );
      picklec( len,buf );
//      n=0;
//      tmp= this->chl;
//      n= this->nchl_dev();
//      pckle( len,n,buf );
////      cout << "the number of children to be pickled is "<<n<<"\n";
//      while( tmp )
//     {
//         tmp->pickle_dev( len,buf );
//         tmp= tmp->nxt;
//     }
      for(size_t i=0; i<children.size(); i++)
     {
         children[i]->pickle_dev(len,buf);
     }
  }

   void cDevice::load_dev()
  {
      string                                fnme;
      FILE                                    *f;
      size_t                                 len=0,l;
      pickle_t                               buf=NULL;

      fnme= getname()+".dev";
      cout << "read device file " << fnme << "\n";
      f= fopen( fnme.c_str(),"r" );
      if( f )
     {
         l= fread( &len,(size_t)1,sizeof(len), f ); buf= new pickle_v[len];
         l= fread(  buf,(size_t)1,       len,  f );
         fclose( f );

         l= 0;
         unpickle_dev( &l,buf );
         delete[] buf;
     }
  }

   void cDevice::unpickle_dev( size_t *len, pickle_t buf )
  {
      cDevice *tmp;
      Int     typ, n;

//      cout << "unpickling "<< this->getname() << " "<< this->gettype()<<"\n";
      cPickle::unpckle( len,&typ, buf );
      unpicklec( len,buf );
//      unpckle( len,&n,buf );
//      tmp= this->chl;
////      cout << "the number of children to be unpickled is "<<n<<"\n";
//      while( tmp )
//     {
//         tmp->unpickle_dev( len,buf );
//         tmp= tmp->nxt;
//     }
      for(size_t i=0; i<children.size(); i++)
     {
         children[i]->unpickle_dev(len,buf);
     }
  }

   int cDevice::nchl_dev()
  {
//      Int tmpndev;
//      cDevice    *tmp= NULL;
//
//      tmpndev = 0;
//
//      tmp=chl;
//      while( tmp )
//     {
//         tmpndev++;
//         tmp= tmp->nxt;
//     }
//
//      return tmpndev;
  }

//   void cDevice::create_folders( cCase *cse )
//  {
//      Int        n=0,i;
//      string    *names=NULL;
//      string     tmpstr;
//      char       buf[1000];
//      int        val;
//      cDevice   *dev, *tmpdev;
//
//      if( cse )
//     {
//         rcse= cse;
//
//         //create folders for root device
//         cpath= ".";
//         cpath= cpath+"/"+getname();
//         tmpstr= cpath;
//         if(dir_exist(tmpstr.c_str())==0)
//        {
//            cout << "create folder " << tmpstr << "\n";
//            mkdir( tmpstr.c_str(), 0777U );
//        }
//         delegate = true;
//
//         //create folders for children devices
//         tmpdev=chl;
//         while( tmpdev )
//        {
//            cpath= ".";
//            cpath= cpath+"/"+getname()+"/"+tmpdev->getname();
//            tmpstr = cpath; 
//            if(dir_exist(tmpstr.c_str())==0)
//           {
//               cout << "create folder " << tmpstr << "\n";
//               mkdir( tmpstr.c_str(), 0777U );
//           }
//
//
//            cpath= cpath+"/"+rcse->getname();
//            tmpstr = cpath; 
//            if(dir_exist(tmpstr.c_str())==0)
//           {
//               cout << "create folder " << tmpstr << "\n";
//               mkdir( tmpstr.c_str(), 0777U );
//           }
//            //cout << cpath << " " << tmpdev << "\n";
//            tmpdev->setcpath(tmpstr);  
//
//            tmpdev->set_rcse(cse); 
//            tmpdev->delegate= false;
//            //cout << tmpdev->getname() << " delegae " << tmpdev->delegate << "\n";
//            tmpdev= tmpdev->nxt;
//        }
//
//     }
//  }

   void cDevice::create_case_folders(const string & case_number, const string & parent_path )
  {
      std::string current_path;
      if (parent_path.empty())
     {
         current_path = name;
     }
      else
     {
         current_path = parent_path + "/" + name;
     }

      // Create the current device folder.
      fs::create_directories(current_path);

      // Create the case folder inside the current device folder.
      std::string case_folder = current_path + "/" + case_number;
      fs::create_directories(case_folder);
      cpath = case_folder;

      // Recursively call children with the updated current path.
      for (size_t i = 0; i < children.size(); i++)
     {
         children[i]->create_case_folders(case_number, current_path);
     }
  }

   // Add a child device and mark this device as a delegate.
   void cDevice::add_child(cDevice * child)
  {
      children.push_back(child);
      delegate = true;
  }


  // Find a direct child with the given full_name.
   cDevice* cDevice::find_direct_child(const string & child_full_name)
  {
      for (size_t i = 0; i < children.size(); i++)
     {
         if (children[i]->full_name == child_full_name)
        {
            return children[i];
        }
     }
     return 0;
  }

   // Retrieve an existing child with the given full_name or create a new one.
   cDevice* cDevice::get_or_create_child(const string & child_full_name)
  {
      cDevice * child = find_direct_child(child_full_name);
      if (!child)
     {
         //child = new cDevice(child_full_name, true);
         child = newobject(child_full_name);
         child->set_fullname(child_full_name);
         add_child(child);
     }
      return child;
  }

   // Recursively print the device tree with indentation and attributes.
   void cDevice::print_tree(const string & prefix)
  {
      cout << prefix << full_name
           << " (Delegate: " << (delegate ? "Yes" : "No")
           << ", Active: " << (active ? "Yes" : "No") << ")"
           << std::endl;
      for (size_t i = 0; i < children.size(); i++)
     {
         children[i]->print_tree(prefix + "  ");
     }
  }

   // Helper function to collect the full names of all devices in the subtree.
   void cDevice::collect_all_names(vector<string> & names)
  {
      names.push_back(full_name);
      for (size_t i = 0; i < children.size(); i++)
     {
         children[i]->collect_all_names(names);
     }
  }

   // Print all devices (count and names).
   // Output format: number-of-nodes, node1-full_name, node2-full_name, ...
   void cDevice::print_all_devices() 
  {
      vector<string> names;
      collect_all_names(names);
      cout << names.size();
      for (size_t i = 0; i < names.size(); i++)
     {
          cout << ", " << names[i];
     }
      cout << endl;
  }

  // Helper function to collect the full names of all leaf devices in the subtree.
   void cDevice::collect_leaf_names(vector<string> & names) 
  {
      if (!delegate)
     {
          names.push_back(full_name);
     }
      else
     {
         for (size_t i = 0; i < children.size(); i++)
        {
            children[i]->collect_leaf_names(names);
        }
     }
  }

  // Print leaf devices (count and names).
  // Output format: number-of-leaf-nodes, leaf1-full_name, leaf2-full_name, ...
   void cDevice::print_leaf_devices()
  {
      vector<string> names;
      collect_leaf_names(names);
      cout << names.size();
      for (size_t i = 0; i < names.size(); i++)
     {
         cout << ", " << names[i];
     }
      cout << endl;
  }

   void cDevice::build_dev_tree(string line)
  {
      std::stringstream ss(line);
      std::string token;
      while (std::getline(ss, token, ','))
     {
         token = trim(token);
         if (token.empty())
        {
            continue;
        }

         // Split the token by hyphen to get the hierarchy.
         std::vector<std::string> path;
         std::stringstream ss_token(token);
         std::string part;
         while (std::getline(ss_token, part, '-'))
        {
            part = trim(part);
            if (!part.empty())
           {
               path.push_back(part);
           }
        }
         if (path.empty())
        {
            continue;
        }

         // The first token should match the root's full_name.
         if (path[0] != this->full_name)
        {
            std::cerr << "Error: Root device name mismatch in token: " << token << std::endl;
            continue;
        }

         // Traverse the tree: starting from the root, create or retrieve each child.
         cDevice * current = this;
         for (size_t i = 1; i < path.size(); i++)
        {
            current = current->get_or_create_child(path[i]);
        }
     }
  }

   // Return a vector of pointers to the leaf node devices.
    vector<cDevice*> cDevice::get_leaf_devices()
   {
       vector<cDevice*> leaf_devices;
       if (!delegate)
      {
          // Since this function is const, we use const_cast to return a non-const pointer.
          leaf_devices.push_back(this);
      }
       else
      {
          for (size_t i = 0; i < children.size(); i++)
         {
             vector<cDevice*> child_leaves = children[i]->get_leaf_devices();
             leaf_devices.insert(leaf_devices.end(), child_leaves.begin(), child_leaves.end());
         }
      }
       return leaf_devices;
   }

   void cDevice::set_fullname(string nm)
  {
      full_name = nm;
      size_t pos = full_name.find(':');
      if (pos != std::string::npos)
     {
         dev_type = full_name.substr(0, pos);
         name = full_name.substr(pos + 1);
     }
      else
     {
         dev_type = "empty";
         name = full_name;
     }
  }
