#ifndef _2DHYBRID_
#define _2DHYBRID_

# include <cprec.h>
# include <unmesh/handlequad.h>
# include <vector>
# include <set>
# include <string>

/*#ifndef _PI_
#define _PI_
   const Real PI = 3.14159265358979323846264338327950288419716939937510582;
#endif*/

  /*class cFrontedge
  {
     public:
       short alive;
       int vtx[2];
       int nextfront;
       int prevfront;


       int quad;
       int tri;
       short state;
       int lev;
       Real len;
       Real angle[2];
  };*/


   class c2Dhybrid
  {
     private:
        int nbasep, nbaseedge;
        vector<cFrontedge> baseedge, front;   
        vector<Real> x, marchdir, marchd, inflatedx, lfs;
        vector<int> infectedpt, infectedptlog, infectededge, infectededgelog, pt2edgemap;
        vector<int> marchptstat, isfixednorm, ptlink, isiopsnorm;
        vector<int> quad, tri, inflatededge, bndedge;
        vector<string> basebndnm, bndedgenm;

        void uninfectpt();
        void infectpt( int ie, int marker=1 );
        bool isptinfected( int ie );
        void uninfectedge();
        void infectedge( int ie, int marker=1 );
        bool isedgeinfected( int ie );

        bool buildedgetopo();
        void localnormal( int qp, Real *dn, Real *beta, bool *isconcave );
        void updatefront( vector<int>& marchpt );
        int advancept( int qp );
        int makenewpt( Real *y );
        int storequad( int ip0, int ip1, int ip2, int ip3 );
        int storetri( int ip0, int ip1, int ip2 );
        void fillgap( int ie, int ip0, int ip1, int ip2, int ip3 );
        void smoothnorm( int nt, vector<int>& marchpt, vector<Real>& visiangle );
        void findneigpt( int qp, int *tmps );
        void getcliffpt( set<int>& cliffpt );
        bool iswall( int i );
        Real quadquali( int ip0, int ip1, int ip2, int ip3 );
        void updateiops();
        int getmarchend( int qp );
        void smoothbndpatchbnd( vector<int>& patch );
        void makebndgroup();
        void jettison();


        void detectgap( int ntdns );
        bool ismarchingtoofar( int qp );
        string isoniops( int ip );




     public:
        c2Dhybrid();
       ~c2Dhybrid();
     
        bool init( vector<Real>& tmpx, vector<int>& isp );
        void setbndtag( vector<string> tmpbndtag );
        bool inflate( int nlev, Real dfirst, Real stretch );
        bool filltri();
        void cachegrid( string fnm );
        void toparaview_hybrid( string fnm );
  };

#endif
