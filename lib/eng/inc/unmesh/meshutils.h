#ifndef _MESHUTILS_
#define _MESHUTILS_

# include <cprec.h>

#ifndef _PI_
#define _PI_
   const Real PI = 3.14159265358979323846264338327950288419716939937510582;
#endif

   //predicates
   Real exactinit();
   Real orient2d( Real *, Real *, Real * );
   Real orient3d( Real *, Real *, Real *, Real* );
   Real incircle( Real *, Real *, Real *, Real * );
   Real insphere( Real *, Real *, Real *, Real *, Real * );

   //stretch function
   Real onedstretch( Real b, Real ss, Real se, Real s0 );
   int existingpoint( int nx, Real *y, int np, Real *x[3] );


   //other
   Real dist2( int nx, Real *x[3], int ip0, int ip1 );
   Real dist2( int nx, Real *x0, Real *x1);
   void crossproduct( Real *dl0, Real *dl1, Real *dn );
   Real dotproduct( int nx, Real *dl0, Real *dl1 );
   void normvec( int nx, Real *dl );
   Real facenormal( Real y0[3], Real y1[3], Real y2[3], Real dn[3] );



#endif
