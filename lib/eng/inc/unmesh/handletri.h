#ifndef _HANDLETRI_
#define _HANDLETRI_

# include <cprec.h>

   const int plus1mod3[3] = {1, 2, 0};
   const int minus1mod3[3] = {2, 0, 1};

   const int triedge[2][3] = { {1, 2, 0},
                               {2, 0, 1} };
   const int locorg[3]  = { 1, 2, 0 }; 
   const int locdest[3] = { 2, 0, 1 }; 
   const int locapex[3] = { 0, 1, 2 }; 

   const int locver2edge[3][3] = 
  {
     { -1,   2,  1 },
     {  2,  -1,  0 },
     {  1,   0, -1 },
  };

   class cTriEle
  {
     public:
        int vtx[3];
        int stat;
        int neig[3];

        cTriEle(){};
        void init( int , int , int );
        void setneig( int , int , int  );

        short mode;
  };

   class cTriedge
  {
     public:
        int tri;
        int loc;

        cTriedge(): tri(0), loc(0){}
        // Operators;
        cTriedge& operator=(const cTriedge& t)
       {
          tri = t.tri; loc = t.loc; 
          return *this;
       }
        bool operator==(cTriedge& t)
       {
          return tri == t.tri && loc == t.loc;
       }
        bool operator!=(cTriedge& t)
       {
          return tri != t.tri || loc != t.loc;
       }
  };

   int symloc( cTriedge &tri0, cTriEle *gtri );
   void sym( cTriedge &t0, cTriedge &t1, cTriEle *gtri )  ;    
   void symself( cTriedge &t, cTriEle *gtri );
   void lnext( cTriedge &t0, cTriedge &t1, cTriEle *gtri );
   void lnextself( cTriedge &t, cTriEle *gtri );
   void lprev( cTriedge &t0, cTriedge &t1, cTriEle *gtri )  ; 
   void lprevself( cTriedge &t, cTriEle *gtri );                    
   void onext( cTriedge &t0, cTriedge &t1, cTriEle *gtri );
   void onextself( cTriedge &t, cTriEle *gtri );
   void oprev( cTriedge &t0, cTriedge &t1, cTriEle *gtri );
   void oprevself( cTriedge &t, cTriEle *gtri );
   void dnext( cTriedge &t0, cTriedge &t1, cTriEle *gtri );
   void dnextself( cTriedge &t, cTriEle *gtri );
   void dprev( cTriedge &t0, cTriedge &t1, cTriEle *gtri );
   void dprevself( cTriedge &t, cTriEle *gtri );
   void rnext( cTriedge &t0, cTriedge &t1, cTriEle *gtri );
   void rnextself( cTriedge &t, cTriEle *gtri );
   void rprev( cTriedge &t0, cTriedge &t12, cTriEle *gtri );
   void rprevself( cTriedge &t, cTriEle *gtri );
   int org( cTriedge &t, cTriEle *gtri );
   int dest( cTriedge &t, cTriEle *gtri );
   int apex( cTriedge &t, cTriEle *gtri );

   void setcoord( int nx, Real *y, int ip, Real *x[3] );


#endif

