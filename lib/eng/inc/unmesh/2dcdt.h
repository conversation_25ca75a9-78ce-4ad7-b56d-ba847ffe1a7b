#ifndef _DELAUNAY_
#define _DELAUNAY_

# include <string>
# include <iostream>
# include <fstream>
# include <ctime>
# include <vector>
# include <cprec.h>
# include <unmesh/meshutils.h>
# include <geo/kdtree.h>
# include <unmesh/handletri.h>

   const int MXNTRI = 1000000;

   enum eLocateresult { OUTSIDE, INSIDE,
                        ONVERTEX0, ONVERTEX1, ONVERTEX2, ONVERTEX3,
                        ONEDGE0, ONEDGE1, ONEDGE2, ONEDGE3, ONEDGE4, ONEDGE5,
                        ONFACE0, ONFACE1, ONFACE2, ONFACE3,
                        LOCNOTDEF
                      };

#ifndef _PI_
#define _PI_
   const Real PI = 3.14159265358979323846264338327950288419716939937510582;
#endif

   struct cBadtri
  {
     int *tri;
     int *vtx[3];
     Real *c[2];
  };

   class c2DCDT
  {
     protected:
        cTriEle *tri;
        int np;    // number of points
        int ntri;  // number of triangles ever exist in the triangulation process
        int ne;    //actuall number of triangles
        Real *x[2], *glfs;
        int irp0, irp1, irp2, irp3, nbndp;
        int ndeadtri, *deadtri;
        int gninfectedpt, *ginfectedptlog, gmaxinfectedpt; 
        short *ginfectedpt;
        int gninfectedtri, *ginfectedtrilog, gmaxinfectedtri; 
        short *ginfectedtri;
        int *pt2trimap;

        vector<string> bndtag;
        vector<int> bndseg;

        Real sfactor;
        Real pfactor;

//mesh control
        cKdTree kdtree;
        bool bsrc;
        vector<int> srchlp;
        Real gdinf, gsrcratio;

        vector<int> isbndpt;
        Real defaultsize;

        int tricapacity, badtricapacity, deadtricapacity, ptcapacity;        

        clock_t tv[20]; 


        vector<cTriedge> encroseg;

        //for debug
        bool battention;

//delaunay
        void findboundingtri( Real [2][3] ) ;
        void findboundingbox( Real [2][4] ) ;
        void insert( int );
        void splittri( int ie0, int ip0, eLocateresult loc );
        int  storetri( int , int , int  );
        void erasetri( int , int , int , int  );
        int  findoppsite( int , int , int , int * );
        void updatetheother( int , int , int  );
        void outputneig();
        void setpt2trimap( int ip, int ie ) { pt2trimap[ip] = ie; };

        void updateedgeneig( int qe0, int oldneig, int newneig );


        int findoppsite( int ke0, int ip0, int * );
        int findlocal( int, int );
        int findlocal( int ie0, int ip0, int ip1 );

//        void inittask( int ip0, int jp0, int jp1, int jp2, int ie0, int *ntask0, int *taski,
//                       int *taskj, int *taskk, int *taske, Real *subarea );
        void inittask( int ip0, int jp0, int jp1, int jp2, int ie0, int *ntask0, int *taski,
                       int *taskj, int *taskk, int *taske, eLocateresult loc );


        void split13( int ip0, int jp0, int jp1, int jp2, int ie0, int *ntask0, int *taski,
                      int *taskj, int *taskk, int *taske );
        void split24( int ip0, int jp0, int jp1, int jp2, int ie0, int *ntask0, int *taski,
                      int *taskj, int *taskk, int *taske );
        void swap22( int jp0, int jp1, int jp2, int jp3, int ke0, int je3, int *ntask0,
                     int *taski, int *taskj, int *taskk, int *taske );
        bool validtri( int ke0, int jp0, int jp1, int jp2 );

        void removeroottri();
        bool isrootpt( int ip );
        bool ishelptri( int ie );
        void removehelptri( int ie );
        void inflatedeadtri();
        void inflatetri();
        void makept2trimap();

//infection
        void infectpt( int ip );
        void uninfectpt( int ip );
        void uninfectpt();
        bool isptinfected( int ip );             
        void infecttri( int ip );
        void uninfecttri( int ip );
        void uninfecttri();
        bool istriinfected( int ip );             

//jump and walk
        int locate( Real *y, eLocateresult *loc0 );
        eLocateresult preciselocate( int ies, Real *y, cTriedge *searchtri );
        eLocateresult preciselocate2( int ies, Real *y, cTriedge *searchtri );
        int randomsample( Real *y );

//boundary recovery
        void countbp( int ns, int*isp[2], int *nbp, int *bp );
        bool buildbndconnection( int ns, int *isp[2], int *icq[2] );
        void recoverseg( int ns, int *isp[2], int *icq[2] );
        void markmissingseg( int ns, int *isp[2], int *issegdone );
        int issegmissing(int ips, int ipe );
        bool sameedge( int ip0, int ip1, int jp0, int jp1 );
        int finddirection( int ips, int ipe );
        void formcavity( int ies, int ips, int ipe, int *ncrosstri, int *crosstri, int *ntopp,
                         int *topp, int *nbotp, int *botp, int *ntopedge, cTriedge *topedge,
                         int *nbotedge, cTriedge *botedge );
        void findcavitybnd( int ips, int ipe, int ncrosstri, int *crosstri, int ncavityp,
                            int *cavityp, int *ncavityedge, cTriedge *cavityedge );
        void delaunizecavity( int ips, int ipe, int ncavityp, int *cavityp, int ncavityedge,
                              cTriedge *cavityedge, int *ncavitytri, int *cavitytri,
                              int *icq[2], int *issegdone, int *isp[2], bool breverse );
        int findwrappoint( int ip0, int ip1, int ncavityp, int *cavityp );
        void bondtri2cavityedge( cTriedge &t, int ncavityedge, cTriedge *cavityedge );
        void bondcavitytri( int ncavitytri, int *cavitytri );
        int scoutbndseg( int jp0, int jp1, int *icq[2], int *isp[2] );

//carve holes
        void removeexterior( int ns, int *isp[2] );
        void isolatetri( int ie );

//enforce quality by delaunay refinement
        void findlfs( );
        void findlfs( int ip );
        void improvequality();
        bool isbadtri( int ie, Real *yc );
        void repairbadtri( int nbadtri, cBadtri *badtri, int mode );
        //void findcircumcenter( Real *y0, Real *y1, Real *y2, Real *c, Real *r );
        bool accepttrisplitpt( int je0, Real *yc );
        void intertrilfs( int ie0, int newp );
        void findbadtri( int ip0, int *nbadtri, cBadtri *badtri, int mode, int *task );
        void findbadtri( int *nbadtri, cBadtri *badtri, int mode );
        bool istrisparse( int ie, Real *yc );
        void storebadtri( int ie, Real yc[2], int *nbadtri, cBadtri *badtri );
        void inflatebadtri( int *nbadtri, cBadtri *badtri );
        int makenewpoint( Real yc[2] );
        void initencroachedseg();
        void repairencroseg();
        void splitedge( cTriedge *tmpedge, Real *ym );
        void scoutencroseg( int ie );

//enforce quality by advancing front
        void initactivetri( int *nbadtri, cBadtri *badtri);
        bool istriacceptable( int ie );
        void repairactiveetri( int nbadtri, cBadtri *badtri);
        int popactivetri( int nbadtri, cBadtri *badtri );
        int selectedge( int ie0 );
        void createoptvertex( int ie0, int ic, Real *yopt );
        void updatetrimode( int newp, int *nbadtri, cBadtri *badtri );
        int awaitingoractive( int ie );
        bool formstarpolygon( int ip, int *npoly, cTriedge *polybnd );
        Real computelocallfs( int ipsrc, int ipdest );
        unsigned long randomnation(unsigned int choices, unsigned long &randomseed );

//mesh optimization
        void relaxmesh( );
        bool swapedge( int ie0, int ic0 );
        Real maxcos( int ip0,  int ip1, int ip2 );
        void smoothmesh();
        bool formstarpolygon( int ip, int *nc, int *icp[2] );
        void smoothpt( int qp, int nc, int *icp[2] );
        void anglesmoothpt( int ips );
        Real ln2lnangle( Real *p0, Real *p1, Real *q0, Real *q1, bool direction );
        void rotatdir( Real *dl, Real angle);

//mesh control
        void initbndsrc();
        Real getsrc( Real *y );


//check
        void checkpt2trimap();
        void checkneig();

     public:
        c2DCDT();  
        ~c2DCDT();

        cTriEle* gettri() { return tri; };
        int      getntri() { return ntri; };
        int      getnp()   { return np; };
        Real*      getx( int ix )   { return x[ix]; };

        void triangulate( int , Real *[2] );
        void recoverbnd( int ns, int *isp[2] );
        void enforcequality( bool clean=true );
        void aft( bool clean=true );
        void mesh( vector<Real>& bx, vector<int>& bedge );
        Real detectgap( int qp, Real *dir );
        void setdf( Real df ) {defaultsize = df; };


        void cleanupmesh();
        void outputmesh(string fname);
        void output( string );
        void info();
        void setbndsrc( bool b, Real d, Real r ) { bsrc=b; gdinf=d; gsrcratio=r; };
        void setbndtag( int ns, int *isp[2], vector<string>& tag );
        void getbndtag( vector<int>& seg, vector<string>& tag );
        void cachegrid( string fname );
        void toparaview_tri( string fnm );


  };

   void findcircumcenter( Real *y0, Real *y1, Real *y2, Real *c, Real *r );


# endif
