#ifndef _HANDLEQUAD_

# include <cprec.h>

   const int plus1mod4[4] = { 1, 2, 3, 0 };
   const int minus1mod4[4] = { 3, 0, 1, 2 };

   const int quadedge[2][4] =
  {
     { 0, 1, 2, 3 },
     { 1, 2, 3, 0 },
  };

   const int locver2quadedge[4][4] =
  {
     { -1,  0, -1, 3 },
     {  0, -1,  1,-1 },
     { -1,  1, -1, 2 },
     {  3, -1,  2, -1}
  };

   struct cQuad
  {
     short stat;
     int vtx[4];
     int neig[4];
  };

    class cQuadedge
   {
      public:
         int quad, loc;

         cQuadedge(): quad(0), loc(0){}
         // Operators;
         cQuadedge& operator=(const cQuadedge& t)
        {
           quad = t.quad; loc = t.loc;
           return *this;
        }
         bool operator==(cQuadedge& t)
        {
           return quad == t.quad && loc == t.loc;
        }
         bool operator!=(cQuadedge& t)
        {
           return quad != t.quad || loc != t.loc;
        }
   };

   struct cFrontedge
  {
     short alive;
     int vtx[2];
     int quad;
     int tri;

     short state;
     int lev;
     Real len;

     Real angle[2];
     int nextfront;
     int prevfront;
  };

#define _HANDLEQUAD_
#endif
