#ifndef _QUADGEN_
#define _QUADGEN_

#include <string>
#include <set>
#include <vector>
# include <unmesh/handletri.h>
# include <unmesh/handlequad.h>
# include <unmesh/miniblock.h>

   enum ePttype { UNUSEDVERTEX, FREEVERTEX, FRONTVERTEX };
   const int MXVNEIG = 20;

   struct cStatelist
  {
       int ntlev;
       int ns[1000];
       int *lev[1000];
  };

   struct cVertex
  {
     short inputver;
     ePttype pttype;
     int frontneig[MXVNEIG];   
     Real y[2];
     Real lfs; //not used?????
     int nneigedge;
  };

  /*struct cFrontedge
  {
     short alive;
     int vtx[2];
     int quad;
     int tri;

     short state;
     int lev;
     Real len;

     Real angle[2];
     int nextfront;
     int prevfront;
  };*/

   class cQuadgen
  {
     private:
        int nquad, ntri, np;
        int nfront;
        Real stateangle, seamangle0, seamangle1;
        int npave; 
        bool battention;
        int nbndp, *bndp;
        Real *bndangle;
        bool survive;

        std::set<int> statelist[4];
        std::vector<int> failededge;
        std::vector<int> prevpave, currentpave;

        int *pt2trimap;
        int *pt2quadmap;
        int gninfectedtri, *ginfectedtrilog;
        short *ginfectedtri;
        int gninfectedpt, *ginfectedptlog;
        short *ginfectedpt;
        int gninfectedquad, *ginfectedquadlog;
        short *ginfectedquad;
  
        cTriEle *tri;
        cQuad *quad;
        cFrontedge *front;
        cVertex *vertex;

        vector<string> bndnm;
        vector<int> bndedge;


        clock_t tv[20];

        //control parameters:
        int TI;    //total iteration
        int LCI;   //local cleanup iteration
        int NLCI;  //non-local cleanup iteration
        int NEL;   //number of extention layer
        int LSI;   //laplacian smoothing iteration
        int ASI;   //angle smoothing iteration 

     public: 
        cQuadgen();
       ~cQuadgen();
        void setparam( int ti, int lci, int nlci, int nel, int lsi, int asi );


        void setpt2trimap( int ip, int ie ) { pt2trimap[ip] = ie; };
        void setpt2quadmap( int ip, int ie ) { pt2quadmap[ip] = ie; };
        void infecttri( int ip );
        void uninfecttri( int ip );
        void uninfecttri();
        bool istriinfected( int ip );
        void infectpt( int ip, int marker=1 );
        void uninfectpt( int ip );
        void uninfectpt();
        bool isptinfected( int ip );
        void infectquad( int ip );
        void uninfectquad( int ip );
        void uninfectquad();
        bool isquadinfected( int ip );

        bool inputtri( int np, Real *x[2], int ne, int *iep[3] );
        int findtrilocal( int ie0, int ip0, int ip1 );
        int findtrilocal( int ie0, int ip0 );
        void morph();
        bool initfront();
        void frontedgenormal( int is0, Real *dn );
        //void makeside( int is0, int ips, cTriedge *side, cTriedge *refside );
        bool makeside( int is0, int ip0, cTriedge *side, cTriedge *refside, int *refs );

        bool usetriedge( int ips, Real *yref, Real epsilon, cTriedge *side, cTriedge *refside,
                         int ibase );
        Real ln2lnangle( Real *p0, Real *p1, Real *q0, Real *q2, bool direction );
        int popfront();
        bool maketop( int ip0, int ip1, cTriedge *side0, cTriedge *side1, cTriedge*topside );
        int edgemissing( int ips, int ipe );
        bool sameedge( int ip0, int ip1, int jp0, int jp1 );
        int createquad( int is0, cTriedge *side0, cTriedge *side1, cTriedge *topside );
        int storequad( int ip0, int ip1, int ip2, int ip3 );
        int storetri( int ip0, int ip1, int ip2 );
        void removetrappedtri( int ie, cTriedge *side );
        int isfrontedge( int ip0, int ip1 );
        int isfrontedgeslow( int ip0, int ip1 );
        void erasetri( int ie0, bool isolate );
        void erasefront( int ie0, bool detachvertex );
        bool useswapedge( int qs, int ips, Real *yref, Real epsilon, cTriedge *side,
                          cTriedge *refside );
        void usefrontedge( int is0, int ip0, cTriedge *side );
        void splittri( cTriedge *tmpedge, Real *ym, int ips );
        int findquadlocal( int iquad, int ip0 );
        int findquadlocal( int iquad, int ip0, int ip1 );
        void smoothquad( int iq, int is0 );
        void quadaroundvertex( int ips, int *tmpne, int *tmpe );
        void quadaroundvertex( int ips, int ies, int *tmpne, int *tmpe );
        void isoparasmooth( int ip0, int tmpne, int *tmpe, Real *delta );
        void complexsmooth( int ips, int tmpne, int *tmpe );
        bool angularsmooth( int ips, int tmpne, int *tmpe, Real *dm2 );
        void adjusttri( int ip0, int ip1, cTriedge *side );
        void improvefronttriquali( int is0, int qp );
        int swap22( int ip0, int ip1, int ip2, int ip3, int ie, int ig );
        void correctside( int qs, cTriedge *side, int ips );
        void saveside( cTriedge *side, int s[2] );
        void updateside( cTriedge *side, int s[2] );
        void loadstatelist( int is );
        int findfrontstate( int is );
        void updatefrontangle( int iq, int ibase );
        //void updatefrontstate( int iq );
        void updatefrontstate( int iq, int nnewfront, int *newfront );
        void updatefrontstate( int tmpns, int *tmps );
        int selectstatelist( int state );
        bool formstarpolygon( int ip, int *npoly, cTriedge *polybnd );
        //void correctsmoothmove( int ips, Real *dm );
        void correctsmoothmove( int ips, Real *yq, int nneigq, int *neigq );
        void markfrontvertex();
        void buildfrontloops();
        void computefrontangle( int is );
        int countfrontinloop( int qs, int ip0, int ip1 );
        void updatefrontneig( int iq, int *sg0, int *sg1, int *sg2, int *sg3, int nnewfront,
                              int *newfront );
        void updatefrontvertex( int iquad, int nnewfront, int *newfront );
        bool getownerinloop( int is0, int qp, int *s);
        void getquadlocalfrontneig( int qs, int qp0, int qp1, int qp2, int qp3,
                                    int *sg0, int *sg1, int *sg2, int *sg3 );
        bool seamquad( int is0);
        void checkseamtri( int *je0, int ip0, int ip1);
        void removetrappedtri( int seedtri, int nbnd, int bndside[2][20] );
        void fronttriaroundvertex( int ip0, int *tmpne, int *tmpe );
        void smoothseamlocaltri( int newp );
        void getstarpt( int npoly, cTriedge *poly, int *npolyp, int *polyp );
        void smoothtript( int qp );
        void anglesmoothtript( int qp );
        void anglesmoothquadpt( int qp );
        void anglesmoothquadpt1( int qp ); //tmp!!!!!!
        void rotatdir( Real *dl, Real angle);
        bool getfrontcrack( int is0, int *is1, int *ip0, int *ip1, int *ip2, int *quadcrack0,
                            int *quadcrack1 );
        void updatevertexneig( int newp, int ip0, int ip1, int ip2 );
        void triaroundvertex( int ips, int *ntmpt, cTriedge *tmpt );
        void updatevertextype( int ip );
        bool seamfourfrontquad( int is0 );
        bool seamsixfrontquad( int is0 );
        void fourfrontquad( int is0 );
        void sixfrontquad( int is0 );
        bool ptinsameloop( int qs, int jp0, int jp1 );
        bool getownerinanotherloop( int ibasep, int itop, int *sg );;
        bool transitseam( int is0, int is1, int ip0, int ip1, int ip2, int *quadcrack0,
                          int *quadcrack1 );
        void updatelocalfrontstate( int newfront );
        void createquadforstate0( int is0, int is1 );
        void createquadforstate1( int is0, int is1 );
        void createquadforstate2( int is0, int is1 );
        void createquadforstate3( int is0, int is1 );
        int findgoodneig( int is0 );
        Real getld( int ips, int ipprev, int ip0, int ip1 );
        void getneigtrip( int qp, int *nneigp, int *neigp );
        void smoothlocaltri( int iq );
        void reconnectlocaltri( int iq );
        int merge3tri( int itri, int ip0, int ip1 );
        int merge4tri( int itri, int ip0, int ip1 );
        void smoothtriquad( int ips, int npoly, cTriedge *polybnd, int nneigq, int *neigq,
                            Real *ym );
        void smoothtriquad( int ip0, int nneigq, int *neigq );
        bool transitsplit0( int is0 );
        bool transitsplit1( int is0 );
        bool transitsplit2( int is0 );
        bool transitsplit10( int is0, int is1 );
        bool transitsplit11( int is0, int is1 );
        bool transitsplit20( int is0, int is1 );
        bool transitsplit21( int is0, int is1 );


//cleanup
        void localcleanup();
        void cleanupsimple();
        bool removedoublet( int ies, int ips );
        bool removediamond( int ies, int ips );
        bool collapsequad( int ies, int ip0, int ip2 );
        void bondquad( int ig0, int ip0, int ip1, int ig1, int jp0, int jp1 );
        bool fillbnd3( int qe );
        bool fillbnd3_1( int qe );
        bool fillbnd3_2( int qe );
        bool fillbnd3_3( int qe );
        void fill3( int ip0, int ip1, int ip2, int ip3, int ip4, int ip5, int qe, int qg);
        void fill31( int ip0, int ip1, int ip2, int ip3, int ip4, int ip5, int qe, int qg);
        bool removebnd3edge( int qe );
        void updatequadvertex( int ie, int oldvtx, int newvtx );
        void bondquad( int nnewquad, int *newquad, int ncp, int icp[2][100], int *icg );
        bool flipquadedge( int ie, int ic );
        bool flipbndquadedge( int ie, int ic );
        void nonlocalcleanup();
        void remeshpatch( int qp, int qe );
        bool makepatchconvex( vector<int>& patchquad );
        bool extendpatch( vector<int>& patchquad );
        bool solvepatch( vector<int>& patchquad );
        bool solvesidelen( int nside, Real *sidelen, Real *a );
        void remeshpatchtemp3( Real* len, Real *s, int npatchbnd, int patchbnd[4][1000], 
                               vector<int>& side0, vector<int>& side1, vector<int>& side2, 
                               vector<int>& patchquad );
        void remeshpatchtemp4( Real *s, int npatchbnd, int patchbnd[4][1000],
                                    vector<int>& side0, vector<int>& side1, vector<int>& side2,
                                    vector<int>& side3, vector<int>& patchquad );
        void remeshpatchtemp5( Real* len, Real *s, int npatchbnd, int patchbnd[4][1000],
                               vector<int>& side0, vector<int>& side1, vector<int>& side2, 
                               vector<int>& side3, vector<int>& side4, vector<int>& patchquad );
        void add2patchbnd( int iss, int ise, vector<int>& side, int patchbnd[4][1000],
                           int *tmnp, Real *tmpx[2], bool breverse );
        void add2ptachbnd( int ns, Real *y0, Real *y1, int *tmpnp, Real *tmpx[2] );
        void meshpatch5_0( vector<int>& side0, vector<int>& side1, vector<int>& side2,
                           vector<int>& side3, vector<int>& side4, Real *ym, int *a, int *b,
                           int patchbnd[4][1000], cMiniDomain *dmn, vector<Real>& subx,
                           vector<int>& subquad );
        void meshpatch5_1( vector<int>& side0, vector<int>& side1, vector<int>& side2,
                           vector<int>& side3, vector<int>& side4, int *a, int *b,
                           int patchbnd[4][1000], cMiniDomain *dmn, vector<Real>& subx,
                           vector<int>& subquad );
        void meshpatch5_2(vector<int>& side0, vector<int>& side1, vector<int>& side2,
                          vector<int>& side3, vector<int>& side4, int *a, int *b,
                          int patchbnd[4][1000], cMiniDomain *dmn, vector<Real>& subx, 
                          vector<int>& subquad );
        void bondquad( vector<int>& subquad );
        void loadnewpatch( int npatchbnd, int patchbnd[4][1000], vector<int>& oldpatchquad,
                           vector<Real>& subx, vector<int>& newquad );
        void meshpatch3_0( vector<int>& side0, vector<int>& side1, vector<int>& side2,
                           Real *ym, int *a, int *b,  int patchbnd[4][1000], cMiniDomain *dmn,
                           vector<Real>& subx, vector<int>& subquad );
        void meshpatch3_1( vector<int>& side0, vector<int>& side1, vector<int>& side2,
                           int *a, int *b,  int patchbnd[4][1000], cMiniDomain *dmn,
                           vector<Real>& subx, vector<int>& subquad );
        bool isbowtie( int ie );
        void removechevron();
        bool removechevron( int ie );



        void smoothinterior( int iquad );
        void localsmoothing( int iquad, int ibase );
        int quickquad( int is0 );
        void optimizequad();
        void smoothquadpt();
        void laplacianquadpt();
        void smoothfrontvertex( int ip );
        void shapemetric();


//recover edge (front 2d cdt)
        bool recoveredge( int ips, int ipe );
        int finddirection( int ips, int ipe );
        bool formcavity( int ies, int ips, int ipe, int *ncrosstri, int *crosstri, int *ntopp,
                         int *topp, int *nbotp, int *botp, int *ntopedge, cTriedge *topedge,
                         int *nbotedge, cTriedge *botedge );
        void findcavitybnd( int ips, int ipe, int ncrosstri, int *crosstri, int ncavityp,
                            int *cavityp, int *ncavityedge, cTriedge *cavityedge );
        void delaunizecavity( int ips, int ipe, int ncavityp, int *cavityp, int ncavityedge,
                              cTriedge *cavityedge, int *ncavitytri, int *cavitytri, 
                              bool breverse );
        int findwrappoint( int ip0, int ip1, int ncavityp, int *cavityp );
        void bondtri2cavityedge( cTriedge &t, int ncavityedge, cTriedge *cavityedge );
        void bondcavitytri( int ncavitytri, int *cavitytri );

//boundary layer
        void readbndlayer( string fnm );
        void visclayer();
        bool iswall( int ie,  int ic);
        void classifybndquad( set<int>& bndquad, set<int>& bndpt, vector<int>& bndquadtype );
        bool isbndpt( set<int>& bndpt, int ip);
        void getnormline( set<int>& bndquad, vector<int>& ray, int *nbndray, int *bndray[10] );
        void getridgeline( set<int>& bndquad, set<int>& bndpt, vector<int>& ray, int *nbndray,
                           int *bndray[10], vector<int>& bndquadtype );
        void getcornerline( set<int>& bndquad, vector<int>& ray, int *nbndray, int *bndray[10],
                            vector<int>& bndquadtype );
        int add2ray( int ip0, int ip1, int *nbndray, int *bndray[10], vector<int>& ray);
        int rayexist( int ip0, int ip1, int *nbndray, int *bndray[10], vector<int>& ray );
        int makenewquadpt( Real *y0 );
        void refinebndquad1( int qe, int nl, int *nbndray, int *bndray[10], vector<int>& ray,
                             int **lrayp );
        void refinebndquad2( int qe, int nl, int *nbndray, int *bndray[10], vector<int>& ray,
                             int **lrayp );
        void refinebndquad3( int qe, int nl, int *nbndray, int *bndray[10], vector<int>& ray,
                             int **lrayp, set<int>& bndpt );

//output mesh
       void jettisonvertex();
       void outputmesh( string fname );


//output
        void outputfront();
        void outputfrontvertex();
        void outputtri();
        void outputquad( string fname );
        void outputquad( string fname, vector<int>& patchquad );
        void outputquad( vector<Real>& subx, vector<int>& subquad, string fnm );


//checking
        void checkfrontvertexneig();
        void checktrineig();
        void checkfrontquad();
        bool checkquadneig();
        void checkfronttri();
        void checkloops();
        void checkpt2quadmap();
        void checkpt2trimap();
  };

#endif
