#ifndef _MINIBLOCK_
#define _MINIBLOCK_

# include <string>
# include <cprec.h>

   class cMiniBlock
  {
     protected:
        int ni, nj, **ijn;
        int np;
        Real *x[2];
        int *gip;

     public:
        cMiniBlock();
        cMiniBlock( int Ni, int Nj );
        ~cMiniBlock();        
        void initbnd( int side, int tmpnp, Real *tmpx[2] );
        void minit();
        void blockbnd( int iside, int *tmpnp, int *tmpp[2] );

        int** getijn() { return ijn; };
        int getni() { return ni; };
        int getnj() { return nj; };
        Real *getx( int ix ) { return x[ix]; };
        void setgip( int i, int j, int ip0 ) { gip[ijn[j][i]] = ip0; };
        int getgip( int ip ) {return gip[ip];};
        int* getgip( ) {return gip;};

        void output( string fnm );
        void outputbnd( string fnm );
        void output( string fnm, Real *gx[2] );

  };


   class cMiniDomain
  {
     protected:
        static const int MxNBlk = 200;
        static const int MxNLine = 200;
        static const int MxNP = 10000;

        int nblk;
        cMiniBlock *blk[MxNBlk];
        int np;
        Real *x[2]; 

        int lne, *liep[4];

        void loadblkpt( cMiniBlock *tmpblk );
        void loadblkpt( cMiniBlock *tmpblk, int iside );

     public:
        cMiniDomain();
        virtual ~cMiniDomain();
        void addblk( cMiniBlock *tmpblk );
        void outputdomain();
        void unstruct();
        void loadblk( cMiniBlock *tmpblk );
 
        int          getnblk()         { return nblk; };
        cMiniBlock*  getblk( int ib )  { return blk[ib]; }
        Real*        getx( int ix )    { return x[ix];};
        int          getnp()           { return np;};
        int          getlne()          { return lne; };
        int*         getliep( int ip ) { return liep[ip]; };
  };

   string int2str( int i );
//   int existingpoint( int nx, Real *y, int np, Real *x[3] );



#endif

