#ifndef _UGRAPH_
#define _UGRAPH_

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 13:55:16 BST 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         basic topology

#  include <utils/proto.h>
#  include <sort/proto.h>
#  include <const.h>
#  include <string>
#  include <fstream>

/**@ingroup engineering
  *@{
 **/

/**@defgroup ugraph Unsymmetric graphs
  *@{
 **/

/* cUgraph describes an unsymmetric graph. The graph is represented as a transpose connectivity table.
   This graph is useful when matching a set of symplex entities to an existing set of higher-dimensional symplexes 
  @brief Unsymmetric graph.*/

   class cUgraph
  {
      protected:
         Int                   n;                      //!< Number of node in the graph.
         Int                  *lpe ;                   //!< Number of symplexes attached to each node.
         Int                  *mpe ;                   //!< Storage size for the symplex list of each  node.
         Int                 **ipe ;                   //!< Symplex index for each node.
         Int                 **jpe ;                   //!< Local numbering of the node with respect to each symplex in the list.
         Int                 **kpe ;                   //!< Type flag for the symplexes attached to each  node.
      public:

/** Default constructor.
   @brief Constructor.
 **/ 
         cUgraph();

/** Runtime constructor. Initialise the graph for a set of nodes of known size.
   @param n              Number of nodes. 
   @brief Constructor.
 **/ 
         cUgraph( Int n );

/** Destructor.
   @brief Destructor.
 **/ 
         virtual ~cUgraph();

/** Add a set of symplexes to the graph.
   @param ne               Number of symplexes in the set. 
   @param nep              Number of nodes in each symplex.
   @param iep              Symplex-node list.
   @param iflag            Type flag for the symplex.
   @brief                  Graph update.
 **/
         virtual void  build( Int ne, Int nep, Int *iep[], Int iflag );

/** Matches a set of symplexes to those already listed in the graph using known transformation rules.
   @param nb               Number of symplexes in the set. 
   @param nbp              Number of nodes in each symplex.
   @param ibp              Symplex-node list.
   @param ibe              low-symplex to high-symplex list.
   @param nep              Number of nodes in each symplex.
   @param iep              Symplex-node list.
   @param neb              Number  of low-symplex per high-symplex.
   @param ieb              Transformation rules from high-symplex to low-symplex.
   @brief                  Graph match.
 **/
         virtual void      match( Int  nb, Int nbp,  Int  *ibp[], Int *ibe[],
                                  Int *nep,  Int **iep[], Int *neb, Int **ieb[] );

         virtual void      match( Int  nb, Int nbp,  Int  *ibp[], Int *ibe[],
                                  Int *nep,  Int **iep[], Int *neb, Int **ieb[], Int *medium_marker[] );
         
  };

/**
  *@}
 **/
/**
  *@}
 **/

#  endif
