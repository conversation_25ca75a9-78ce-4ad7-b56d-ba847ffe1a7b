#ifndef _TOPO_
#define _TOPO_

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 13:55:16 BST 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         basic topology

#  include <utils/proto.h>
#  include <sort/proto.h>
#  include <const.h>
#  include <string>
#  include <fstream>

/* class cUgraph
  {
      protected:
         Int                   n;
         Int                  *lpe ;
         Int                  *mpe ;
         Int                 **ipe ;
         Int                 **jpe ;
         Int                 **kpe ;
      public:
         cUgraph();
         cUgraph( Int );
         virtual ~cUgraph();

         virtual void transpose( Int ne, Int nep, Int *iep[], Int iflag );
         virtual void      match( Int  nb, Int nbp,  Int  *ibp[], Int *ibe[],
                                  Int *nep,  Int **iep[], Int *neb, Int **ieb[] );
         
  };*/

   void tpgraph( Int iss, Int ise, Int nsp, Int *isq[], Int *lgq[], Int **igq, Int *isg[] );
   void marktp( Int iss, Int ise, Int nsp, Int *isp[], Int *imrks, Int ikey, Int *imrkp );
   void tpmark( Int iss, Int ise, Int nsp, Int *isp[], Int *imrkp, Int ikey, Int *imrks );
/** Reverse Cuthill-McKee ordering for symmetric graphs
   @param            n              number of nodes
   @param            lgp            number of neighbours for each node. lgp[0][i] is the number of nodes for node i;
   @param            igp            neighbours for each node. igp[i][k] is the k-th neighbour of node i;
   @param            iprm           permutation array for the nodes. Size is n
   @param            indx           index array for the nodes (reverse permuation). Size is n.
   @param            iwrk           workspace. Size is n.
 **/

   void tpgrcmk( Int n, Int *lgp[], Int **igp, Int *iprm, Int *indx, Int *iwrk );
   void tpermute( Int n, Int *lgp, Int **igp, Int *, Int * );
   void tprint( string, Int n, Int *lgp, Int **igp );
   Int tpbwidth( Int n, Int *lgp, Int **igp );

   void tpinvert( Int ne, Int nep, Int *iep[], Int *lpe, Int *mpe, Int **ipe, Int **jpe, Int **kpe, Int iflag );
   void tpmatch( Int  nb, Int nbp,  Int  *ibp[], Int *ibe, Int *jbe, Int *kbe, 
                         Int *nep,  Int **iep[], Int *neb, Int **ieb[], Int *lpe, Int **ipe, Int **jpe, Int **kpe );

   void tpperm( Int n, Int *data );
   void tpconn( Int ncp, Int nek, Int *ne, Int *nep,  Int **iep[], Int *nec, Int **iec[], Int *lpc, Int *mpc, Int ***ipc, Int ***jpc, Int ***hpc, Int ***kpc );
   void tpcpack( Int np, Int ncp, Int *lpc, Int ***ipc, Int ***jpc, Int ***hpc, Int ***kpc, 
                 Int *n, Int **sicp, Int **sjcp, Int **shcp, Int **skcp );

#  endif
