#ifndef _DGRAPH_
#define _DGRAPH_

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 13:55:16 BST 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         basic topology

#  include <utils/proto.h>
#  include <sort/proto.h>
#  include <const.h>
#  include <string>
#  include <fstream>

/**@ingroup engineering
  *@{
 **/

/**@defgroup dgraph Directed graphs
  *@{
 **/

/** cDgrah represents a directed graph. **/

   class cDgraph
  {
      protected:

         Int            np; //!< number of nodes in the graph.
         Int           npc; //!< number of nodes per connection.
         Int          *lpc; //!< number of connections associated to each node.
         Int          *mpc; //!< storage size for the connection list associated to each node.
         Int        ***ipc; //!< connections associated to each node. Each connection is represented as a list of nodes of length npc.

         Int            nk; //!< number of symplex kinds used to build the graph.
         Int           *ne; //!< number of symplex of each kind.
         Int          *nce; //!< number of connections generated by each symplex.
         Int        ***ice; //!< Rules to generate connections from symplexes.

         Int        ***iec; //!< Correspondence between connections and symplexes.
         Int        ***hec; //!< Correspondence between connections and symplex kinds.

      public:

/** Default constructor.
   @brief Constructor.
 **/
         cDgraph();

/** This constructor initialises the directed graph for a set of nodes of known size and for 
    an known set of generating symplexes. The number of symplexes of each kinde is known, as well
    as the rules to generate connections from these symplexes. Sych rules are expressed by the 
    arguments nnce and nice. As an example,  for a graph created from a set of triangles and quadrilaterals, 
    m=2 (two nodes per connection), nek= 2 (tri and quad), nne[0]= 3 (tri), nne[1]= 4 (quad), 
    nice[0]= { {0,1,2},{1,2,0} }, nice[1]= { {0,1,2,3},{1,2,3,0} }.
   @brief Runtime constructor.
   @param         n      Number of nodes in the topology.
   @param         m      Number of nodes per connection. 
                         grid, m=2.
   @param       nek      Number of symplex kinds. nek[ik] is the number of symplexes of kind ik.
   @param       nne      Number of symplexes. nne[ik] is the number of symplexs of kind ik in the graph.
   @param      nnce      Number of connections generated by each symplex. ncne[ik] is the number of local connections
                         generated by symplexes of kind ik.
   @param      nice      Local representation of the connections generated by each symplex. nice[ik][ic][in] is the 
                         vertex number (in local numbering) of node in the local connection ic of symplexes of kind ik.
 **/
         cDgraph( Int n, Int m, Int nek, Int *nne, Int *nnce, Int ***nice ) ;

/** Destructor.
   @brief Destructor.
 **/
         virtual ~cDgraph();

/** Create the connections in the graph using the symplex-node correspondence in the array iep.
   @param iep      Symplex-node correspondence.
   @brief Build connections.
 **/
         virtual void build( Int **iep[] );

/** Export the total number of connections and the correspondence between local symplex connections 
    and global connections.  
   @param  n         Number of connections.
   @param  jec       Correspondence between local symplex connections and global connections. jec[ik][jc][ie]
                     represents the global number of local connection jc in element ie of kind ik. 
   @brief  Export graph.
 **/
         virtual void pack( Int *n, Int **jec[] );

/** Utility function to destroy the lists of arguments required for the constructor. All pointers are deallocated
    and set to null.
   @param      nice      Local representation of the connections generated by each symplex. nice[ik]=NULL on exit;
   @brief  Utility
 **/
         void destroy( Int ***nice );

  };

/** Initialize the arguments for the cDgrah constructor for the edges of triangular elements.
   @param      npc      Number of nodes per edge. npc[0]= 2;  
   @param      nce      Number of edges generated by each triangle. nne[0]=3.
   @param      ice      Local representation of the edges generated by each triangle. nice[0]={{0,1,2},{1,2,0}};
 **/
   void      triangles( Int *npc, Int *nce, Int ***ice );

/** Initialize the arguments for the cDgrah constructor for the edges of quadrilateral elements.
   @param      npc      Number of nodes per edge. npc[0]= 2;  
   @param      nce      Number of edges generated by each triangle. nne[0]=4.
   @param      ice      Local representation of the edges generated by each triangle. nice[0]={{0,1,2,3},{1,2,3,0}};
 **/
   void quadrilaterals( Int *npc, Int *nce, Int ***ice );

/**
  *@}
 **/

/**
  *@}
 **/

#  endif
