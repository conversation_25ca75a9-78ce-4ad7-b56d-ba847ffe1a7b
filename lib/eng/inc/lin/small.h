#  ifndef _SMALL_
#  define _SMALL_

#  include <lin/vect.h>

/**@ingroup engineering
  *@{
 **/

/**@defgroup small Inline factorization of small matrices.
  *@{
 **/

/**@defgroup qrls QR and least squares
  *@{
 **/  

/**
    QR factorization of a 3x2 matrix.
   @param            v0            First column of the matrix. Input.
   @param            v1            Second column of the matrix. Input.
   @param            q0            First column of the Q factor. Output.
   @param            q1            First column of the Q factor. Output.
   @param            r             R factor (compressed). Output.
 **/
   void qrf23( Real *v0, Real *v1, Real *q0, Real *q1, Real *r );


/**@ingroup qrls 
    QR least square solution for 3x2 problems.
   @param            q0            First column of the Q factor. Input.
   @param            q1            First column of the Q factor. Input.
   @param            r             R factor (compressed). Input.
   @param            x             Unknowns. Output.
   @param            b             Right hand side. Input.
 **/
   void qrs23( Real *q0, Real *q1, Real *r, Real *x, Real *b );
/**
  *@}
 **/

/**@defgroup lu Linear systems
  *@{
 **/


/**
    3x3 LU=A factorization. Non-pivoting version.
   @param            a0            First column of A. Input.
   @param            a1            Second column of A. Input.
   @param            a2            Third column of A. Input.
   @param            q0            First column of LU. Needs q0[M],M>=4; Output.
   @param            q1            Second column of LU. Needs q1[M],M>=4; Output.
   @param            q2            Third column of LU. Needs q2[M],M>=4; Output.
   @param            d             Determinant.
   @deprecated                     Use at own risk.
 **/
   void luf3( Real *a0, Real *a1, Real *a2, Real *q0, Real *q1, Real *q2, Real *d );


/**
    3x3 solution Ax=b. A has been factorized using luf3. Non-pivoting version.
   @param            q0            First column of LU. Needs q0[M],M>=4; Input.
   @param            q1            Second column of LU. Needs q1[M],M>=4; Input.
   @param            q2            Third column of LU. Needs q2[M],M>=4; Input.
   @param            x             Unknowns. Output.
   @param            b             Right hand side. Input.
   @deprecated                     Use at own risk.
 **/
   void lus3( Real *q0, Real *q1, Real *q2, Real *x, Real *b );


/**
    3x3 solution A'x=b. A has been factorized using luf3. Non-pivoting version.
   @param            q0            First column of LU. Needs q0[M],M>=4; Input.
   @param            q1            Second column of LU. Needs q1[M],M>=4; Input.
   @param            q2            Third column of LU. Needs q2[M],M>=4; Input.
   @param            x             Unknowns. Output.
   @param            b             Right hand side. Input.
   @deprecated                     Use at own risk.
 **/
   void lus3t( Real *q0, Real *q1, Real *q2, Real *x, Real *b );

/**
    2x2 LU=A factorization. Non-pivoting version.
   @param            a0            First column of A. Input.
   @param            a1            Second column of A. Input.
   @param            q0            First column of LU. Needs q0[M],M>=3. Output.
   @param            q1            Second column of LU. Needs q1[M],M>=3. Output.
   @param            d             Determinant.
   @deprecated                     Use at own risk.
 **/
   void luf2( Real *a0, Real *a1, Real *q0, Real *q1, Real *d );


/**
    2x2 solution Ax=b. A has been factorized using luf2. Non-pivoting version.
   @param            q0            First column of LU. Needs q0[M],M>=3; Input.
   @param            q1            Second column of LU. Needs q1[M],M>=2; Input.
   @param            x             Unknowns. Output.
   @param            b             Right hand side. Input.
   @deprecated                     Use at own risk.
 **/
   void lus2( Real *q0, Real *q1, Real *x, Real *b );


/**
    2x2 solution A'x=b. A has been factorized using luf2. Non-pivoting version.
   @param            q0            First column of LU. Needs q0[M],M>=3; Input.
   @param            q1            Second column of LU. Needs q1[M],M>=3; Input.
   @param            x             Unknowns. Output.
   @param            b             Right hand side. Input.
   @deprecated                     Use at own risk.
 **/
   void lus2t( Real *, Real *, Real *, Real * );



/**
    2x2 LU=A factorization. Pivoting version.
   @param            a0            First column of A. Input.
   @param            a1            Second column of A. Input.
   @param            q0            First column of LU. Needs q0[M],M>=3. Output.
   @param            q1            Second column of LU. Needs q1[M],M>=3. Output.
   @param            ipiv          Row permutation array. Needs ipiv[M], M>=2. Output.
   @param            d             Determinant.
 **/
   void luf2( Real *a0, Real *a1, Real *q0, Real *q1, Int *ipiv, Real *d );


/**
    2x2 solution Ax=b. A has been factorized using luf2. Pivoting version.
   @param            q0            First column of LU. Needs q0[M],M>=3; Input.
   @param            q1            Second column of LU. Needs q1[M],M>=2; Input.
   @param            ipiv          Row permutation array. Needs ipiv[M], M>=2. Input.
   @param            x             Unknowns. Output.
   @param            b             Right hand side. Input.
   @deprecated                     Use at own risk.
 **/
   void lus2( Real *q0, Real *q1, Int *ipiv, Real *x, Real *b );



/**
    2x2 solution A'x=b. A has been factorized using luf2. Pivoting version.
   @param            q0            First column of LU. Needs q0[M],M>=3; Input.
   @param            q1            Second column of LU. Needs q1[M],M>=3; Input.
   @param            ipiv          Row permutation array. Needs ipiv[M], M>=2. Input.
   @param            x             Unknowns. Output.
   @param            b             Right hand side. Input.
 **/
   void lus2t( Real *q0, Real *q1, Int *ipiv, Real *x, Real *b );


/**
    3x3 LU=A factorization. Pivoting version.
   @param            a0            First column of A. Input.
   @param            a1            Second column of A. Input.
   @param            a2            Third column of A. Input.
   @param            q0            First column of LU. Needs q0[M],M>=4; Output.
   @param            q1            Second column of LU. Needs q1[M],M>=4; Output.
   @param            q2            Third column of LU. Needs q2[M],M>=4; Output.
   @param            ipiv          Row permutation array. Needs ipiv[M], M>=3. Output.
   @param            d             Determinant.
 **/
   void luf3( Real *, Real *, Real *, Real *, Real *, Real *, Int *, Real * );


/**
    3x3 solution Ax=b. A has been factorized using luf3. Pivoting version.
   @param            q0            First column of LU. Needs q0[M],M>=4; Input.
   @param            q1            Second column of LU. Needs q1[M],M>=4; Input.
   @param            q2            Third column of LU. Needs q2[M],M>=4; Input.
   @param            ipiv          Row permutation array. Needs ipiv[M], M>=3. Input.
   @param            x             Unknowns. Output.
   @param            b             Right hand side. Input.
 **/
   void lus3( Real *, Real *, Real *, Int *, Real *, Real * );

/**
    3x3 solution A'x=b. A has been factorized using luf3. Pivoting version
   @param            q0            First column of LU. Needs q0[M],M>=4; Input.
   @param            q1            Second column of LU. Needs q1[M],M>=4; Input.
   @param            q2            Third column of LU. Needs q2[M],M>=4; Input.
   @param            ipiv          Row permutation array. Needs ipiv[M], M>=3. Input.
   @param            x             Unknowns. Output.
   @param            b             Right hand side. Input.
 **/
   void lus3t( Real *, Real *, Real *, Int *, Real *, Real * );


/**@defgroup eig Eigenvalue problems.
  *@{
 **/

/**
    2x2 Jacobi transformation. 
   @param           a              Symmetrix 2x2 matrix, compressed. a[0],a[1],a[2] are a_11,a_21,a_22, respectively. 
                                   Input
   @param           c              Cosine of the Jacobi transformation. Output.
   @param           s              Sine of the Jacobi transformation. Output.
 **/
   void jac2( Real *a, Real *c, Real *s );


/**
    2x2 symmetric eigenvalue problem.
   @param           a              Symmetrix 2x2 matrix, compressed. a[0],a[1],a[2] are a_11,a_21,a_22, respectively.
   @param           q0             First eigenvector. Output.
   @param           q1             Second eigenvector. Output.
   @param           d              Eigenvalues. Output.
 **/

   void sye2( Real *a, Real *q0, Real *q1, Real *d );

/**
  *@}
 **/

/**
  *@}
 **/

/**
  *@}
 **/

# endif
