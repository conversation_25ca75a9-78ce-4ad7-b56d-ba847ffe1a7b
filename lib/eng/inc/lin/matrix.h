//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Fri Jul  3 12:57:08 BST 2009
// Changes History -
// Next Change(s)  -
// Purpose         LU factorisation - development program for AU3X

#ifndef _MATRIX_

#  include <cprec.h>

   void getrf( Int , Int *[], Real * );
   void getrs( Int , Int *[], Real *, Real * );

   void getrf( Int, Int, Int , Int *[], Real *[] );
   void getrs( Int, Int, Int , Int *[], Real *[], Real *[] );

   void getrf( Int iqs, Int iqe, Int n, Real *a0, Int nq );
   void getrs( Int iqs, Int iqe, Int n, Real *a, Real *b, Int nv, Int nq );

//#pragma acc routine seq
//   void getrf( Int iprm[3][3], Real *a );
//#pragma acc routine seq
//   void getrs( Int iprm[3][3], Real *a, Real *b );

#define _MATRIX_
#endif
