#  ifndef _VECT_
#  define _VECT_

#  include <cmath>
#  include <complex>
#  include <const.h>
#  include <utils/proto.h>

/**@ingroup engineering
  *@{
 **/

/**@defgroup vect Vector operation
  *@{
 **/

   void    norml( Int n, Real *x );
   void    scale( Int n, Real *x, Real l );
   void     vsum( Int n, Real *x, Real *y, Real *z );
   Real  norminf( Int n, Real *x );
   Real    norm1( Int n, Real *x );
   Real    norm2( Int n, Real *x );


   Real varea( Int n, Real *x[], Real *y );
   Real vangl2( Int n, Real *x[], Real *y );
   Real vangl2( Real *x0, Real *x1, Real *y );

   void rot90( Real *x );
   void rot180( Real *x );
   void rot270( Real *x );
   void rotv2( Real *xr, Real cth, Real sth, Real *x );
   void rotv2( Real *xr, Real dt, Real *x );
   void rotv2( Real dt, Real *x );
   void rotv2( Real cdt, Real sdt, Real *x );
   void rotv2( Int, Int, Real *xr, Real cth, Real sth, Real *x[] );
   void rotv2( Int, Int, Real *xr, Real dt, Real *x[] );

   void mirv2( Real *xr, Real *l0, Real *x );
   void mirv2( Real *l0, Real *x );
   void mirv2( Int, Int, Real *xr, Real *l0, Real *x[] );

   Real dot2( Real *, Real * );
   Real vec2( Real *, Real * );
   void sclv2( Real , Real * );
   void sub2( Real *y1, Real *y2, Real *dy );
   void add2( Real *y1, Real *y2, Real *dy );
   void idv2( Real *y1, Real *y2 );
   Real norm12( Real *v0 );
   Real norm22( Real *v0 );
   Real norminf2( Real *x );

   void sclv2( Real *, Real , Real * );
   void sclv2( Int, Int, Real , Real *[] );
   void sclv2( Int, Int, Real *, Real , Real *[] );


   Real dot3( Real *, Real * );
   void vec3( Real *, Real *, Real * );
   void sclv3( Real , Real * );
   void idv3( Real *y1, Real *y2 );
   void sub3( Real *y1, Real *y2, Real *dy );
   void add3( Real *y1, Real *y2, Real *dy );

   Real norm13( Real *x );
   Real norm23( Real *v0 );
   Real norminf3( Real *x );

   void orth2( Real *v0, Real *v1, Real *w );
   void orth3( Real *v0, Real *v1, Real *w );

   Real dot( Int, Real *, Real * );
   void sub( Int, Real *y1, Real *y2, Real *dy );

   Real dist22( Real *x0, Real *x1 );
   Real dist23( Real *x0, Real *x1 );
   Real distinf2( Real *x0, Real *x1 );
   Real distinf3( Real *x0, Real *x1 );
   Int ptinlst2( Real *y, Int n, Real *x[], Real tol );
   Int ptinlst3( Real *y, Int n, Real *x[], Real tol );

   void shear2(Real *x0, Real *sh[2], Real *x);

   struct cJacBlk
  {
      Real jac[7][7];
  } ;
   struct cJacBlkZ
  {
      complex<Real> jac[7][7];
  } ;

/**
  *@}
 **/

/**
  *@}
 **/

# endif
