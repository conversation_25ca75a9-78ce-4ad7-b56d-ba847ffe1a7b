#  ifndef _LINS_
#  define _LINS_

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Wed Oct 27 13:58:03 BST 2010
// Changes History -
// Next Change(s)  -
// Purpose         

#  include <cprec.h>
#  include <const.h>
#  include <cassert>

   typedef Real *Realu;
   typedef Realu *Realv;

   class cLins
  {
      protected:

                                                cLins();

      public:

         virtual Real                           dot( Real *[], Real *[] ){};
         virtual Real                          setv( Real    , Real *[] ){};
         virtual void                          copy( Real *[], Real *[] ){};
         virtual void                         scalv( Real , Real *[] ){};
         virtual void                         saxpy( Real , Real *[], Real , Real *[] ){};
         virtual void                          adot( Real *[], Real, Real *[] ){};
         virtual void                          prec( Real *[], Real *[] ){};
         void                                   mgscm( Int n, Realv *v, Real *w, Realv u );

         virtual                               ~cLins();
         virtual void                           cg( Real *[], Real *[], Real *[], Real *[], Real *[], Real * );
         virtual void                          fom( Int n, Realv x, Realv b, Realv *v, Real *h[], Realv wrk );

  };

#  endif
