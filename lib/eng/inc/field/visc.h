#  ifndef _VISC_
#  define _VISC_

#  include <field/grad.h>
#  include <pickle/proto.h>
#  include <field/field.h>


   enum visc_t { bad_visc=-1, laminar_visc, cebeci_visc, komega_visc, kepsilon_visc, spalart_visc, spalartR_visc,
                              spalarthighre_visc, kepsilonlowre_visc, komegasst_visc, komegalowre_visc, 
                              cebecilowre_visc, komegasst2003_visc, sbslearsm_visc, num_visc };

   static const string visc_s[num_visc]= { "laminar", "cebeci", "komega", "kepsilon", "spalart", "spalartR", 
                                           "spalarthighre", "kepsilonlowre", "komegasst", "komegalowre", 
                                           "cebecilowre", "komegasst2003", "sbslearsm" };

   class cVisc: public cPickle
  {
      protected:
         Int          nv0,naux0,nlhs0,nauxf0;
         Int          nx,nv,nvel,naux,nlhs,nauxf;
      public:
         cVisc(){nv0=0;naux0=0;};
         virtual visc_t gettype(){ return bad_visc; };
         virtual bool viscous(){ return false; };
         virtual void setvrs( Int , Int , Int *, Int *, Int *, Int * ){};
         virtual void cnsv( Int,Int, Real *[], Real *[] ){};

         //virtual void   maux( Int, Int, Real *[], Real *[], Real **[], Real *[] ){};
         //virtual void   yplus( Int ist, Int ien, Int *iqd, Real *xq[], Real *q[], Real *aux[], Int *iqb, Real *qb[], Real *auxb[],
         //                      Real *dst[] ){};
         virtual void yplus( Int ist, Int ien, Int *siqd, Real *sxq, Real *sq, Real *saux, Int *siqb, Real *sqb, Real *sauxb, Real *sdst, Int nbb, Int nq, Int ndst ){};
         virtual void yplus( Int ist, Int ien, cAu3xView<Int>& iqd, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Int>& iqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                             cAu3xView<Real>& dst ){};
         virtual void maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real ){};//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] ){};
         virtual void maux( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq ){};
         virtual void maux( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux ){};

         virtual void dvar( Int , Int , Real *[], Real *[], Real *[], Real *[] ){};
         virtual void dvar( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq ){};
         virtual void dvargpu( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq ){};
         virtual void dvar( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dU, cAu3xView<Real>& dq ){};

         //virtual void  mwflx( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], 
         //                               Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                      Real *[], Real *[], Real *[] ){};
         virtual void   mwflx( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,      
                                                Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr,
                                                Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr ){};
         virtual void   mwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};

         virtual void dmwflx( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
                                        Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
                                               Real *[], Real *[], Real *[] ){};

         //virtual void   mflx( Int, Int, Int *, Real *[], Real *[], Real *[], Real **[], Real *[],
         //                               Int *, Real *[], Real *[], Real *[], Real **[], Real *[],
         //                                      Real *[], Real *[], Real *[], Real *[] ){};
         virtual void   mflx( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdqdxl, Real *srhsl,
                                                Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqdxr, Real *srhsr,
                                                Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ){};
         virtual void   mflx( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& rhsl,
                                                cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& rhsr,
                                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};

         //virtual void   mflx( Int, Int, Int *[2], Real *[], Real *[], Real *[], Real **[], Real *[],
         //                                      Real *[], Real *[], Real *[], Real *[] ){};
         virtual void   mflx( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdqdx, Real *srhs,
                              Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) {};
         virtual void   mflx( Int ics, Int ice,cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& rhs,
                              cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ) {};

         //virtual void  dmflx( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                               Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], 
         //                                      Real *[], Real *[], Real *[], Real *[] ){};
         virtual void  dmflx( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) {};
         virtual void  dmflx( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ) {};

         //virtual void  dmflx( Int ics, Int ice, Int *icq[2], Real *x[], Real *q[], Real *aux[], Real *dq[], Real *daux[], Real *res[],
         //                     Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] ) {};
         virtual void  dmflx( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdq, Real *sdaux,
                              Real *sres, Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) {};
         virtual void  dmflx( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux,
                              cAu3xView<Real>& res, cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ) {};

         virtual void   mlhs( Int, Int, Int *, Real *[], Real *[], Real *[], Real **[], Real *[],
                                        Int *, Real *[], Real *[], Real *[], Real **[], Real *[],
                                               Real *[], Real *[], Real *[], Real *[] ){};
         virtual void nondim( Int , Int , Real *[], Int * ){};
         virtual void nondim( Int , Int , cAu3xView<Real>& , Int * ){};

         virtual void redim( Int , Int , Real *[] ){};
         virtual void redim( Int , Int , cAu3xView<Real>& ){};
         virtual void redim_cpu( Int , Int , cAu3xView<Real>& ){};

         virtual void  ilhs( Int , Int , Int *, Real *[], Real *[], Real *[],
                                         Int *, Real *[], Real *[], Real *[],
                                                Real *[], Real *[], Real *[] ){};
         virtual void ilhs( Int ics, Int ice, Int *sicql, Real *sql, Real *sauxl, Real *slhsl,
                                              Int *sicqr, Real *sqr, Real *sauxr, Real *slhsr,
                                              Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ){}; 
         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                              cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                              cAu3xView<Real>& wc,  cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};
         virtual void  ilhs( Int ics, Int ice, Int *sicq, Real *sq, Real *saux, Real *slhs,          
                             Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ){};
         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& lhs,
                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ) {};


         //virtual void  wlhs( Int , Int,  Int *, Real *[], Real *[], Real *[],
         //                                Int *, Real *[], Real *[], Real *[], 
         //                                       Real *[], Real *[], Real *[] ){};
         virtual void wlhs( Int ics, Int ice, Int *icql, Real *sql, Real *sauxl, Real *slhsl,
                                              Int *icqr, Real *sqr, Real *sauxr, Real *slhsr,
                                              Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ){};
         virtual void wlhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                              cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};
         //virtual void slhs( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Real *[], Real *[] ){};
         virtual void slhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *slhsa, Int nq ) {};
         virtual void slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa ) {};
         //virtual void srhs( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Int *idst[], Int *igdst[], Real *[], Real *[], Real *[] ){};
         virtual void srhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Int *sidst, Int *sigdst, Real *swq, Real *srhs, Real *slhsa, Int nq ){};
         virtual void srhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& rhs, cAu3xView<Real>& lhsa ){};

         //virtual void dsrhs( Int , Int , Real , Real *[], Real *[], Real *[], Real *[], Real **[], Real *[], 
         //               Real *[], Real *[], Real *[] ){};
         virtual void dsrhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sdqdx, Real *sdst,
                             Real *swq, Real *sres, Real *slhs, Int nq ){};
         virtual void dsrhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                             cAu3xView<Real>& wq, cAu3xView<Real>& res, cAu3xView<Real>& lhs ){};
         //virtual void vlhs( Int iqs, Int iqe, Real cfl, Real *wq[], Real *lhs[] ){};
         virtual void vlhs( Int iqs, Int iqe, Real cfl, Real *swq, Real *slhs, Int nq ){};
         virtual void vlhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& wq, cAu3xView<Real>& lhs ){};
         //virtual void invdg( Int iqs, Int iqe, Real *lhs[], Real *res[] ){};
         virtual void invdg( Int iqs, Int iqe, Real *slhs, Real *sres, Int nq ){};
         virtual void invdg( Int iqs, Int iqe, cAu3xView<Real>& lhs, cAu3xView<Real>& res ){};

         virtual void dmwflx_z( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *zl_re[], Real *zl_im[], Real *rhsl_re[], Real *rhsl_im[],
                                                 Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *zr_re[], Real *zr_im[], Real *rhsr_re[], Real *rhsr_im[],
                                                 Real *wc[], Real *wxdc[], Real *auxc[] ) {};
         //virtual void dmwflx_new( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                   Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                   Real *[], Real *[], Real *[] ){};
         virtual void dmwflx_new( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                   Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                   Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ){};
         virtual void dmwflx_new( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                   cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                   cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};

  };

   class cLaminar: public cVisc
  {
      protected:
         //virtual void   mflx33( Int, Int, Int *, Real *[], Real *[], Real *[], Real **[], Real *[],
         //                                 Int *, Real *[], Real *[], Real *[], Real **[], Real *[],
         //                                        Real *[], Real *[], Real *[], Real *[] ){};
         virtual void   mflx33( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdqdxl, Real *srhsl,
                                                  Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqdxr, Real *srhsr,
                                                  Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ){};
         virtual void   mflx33( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& rhsl,
                                                  cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& rhsr,
                                                  cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};
         //virtual void   mflx33( Int, Int, Int *[2], Real *[], Real *[], Real *[], Real **[], Real *[],
         //                                        Real *[], Real *[], Real *[], Real *[] ){};
         virtual void   mflx33( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdqdx, Real *srhs,
                                Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) {};
         virtual void   mflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& rhs,
                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};
         //virtual void  dmflx33( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                 Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], 
         //                                        Real *[], Real *[], Real *[], Real *[] ){};
         virtual void   dmflx33( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                   Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                   Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) {};
         virtual void   dmflx33( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& sxl, cAu3xView<Real>& sql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                   cAu3xView<Int>& icqr, cAu3xView<Real>& sxr, cAu3xView<Real>& sqr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                   cAu3xView<Real>& xc, cAu3xView<Real>& swc, cAu3xView<Real>& swxdc, cAu3xView<Real>& auxc ) {};

         //virtual void  dmflx33( Int ics, Int ice, Int *icq[2], Real *x[], Real *q[], Real *aux[], Real *dq[], Real *daux[], Real *res[],
         //                     Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] ){};
         virtual void  dmflx33( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdq, Real *sdaux,
                              Real *sres, Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) {};
         virtual void  dmflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux,
                              cAu3xView<Real>& res, cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ) {};

         //virtual void  mwflx33( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                 Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                        Real *[], Real *[], Real *[] );
         virtual void   mwflx33( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,      
                                                  Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr,
                                                  Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr );
         virtual void   mwflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                  cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                  cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void dmwflx33( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
                                          Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
                                                 Real *[], Real *[], Real *[] );

         void dmwflx33_z( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *zl_re[], Real *zl_im[], Real *rhsl_re[], Real *rhsl_im[],
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *zr_re[], Real *zr_im[], Real *rhsr_re[], Real *rhsr_im[],
                                           Real *wc[], Real *wxdc[], Real *auxc[] );

      public:
         cLaminar();
         virtual bool viscous(){ return true; };
         visc_t gettype(){ return laminar_visc; };
         void setvrs( Int , Int , Int *, Int *, Int *, Int * );
         //virtual void  mwflx( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], 
         //                               Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                      Real *[], Real *[], Real *[] );
         virtual void   mwflx( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,      
                                                Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr,
                                                Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr );
         virtual void   mwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void dmwflx( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
                                        Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
                                               Real *[], Real *[], Real *[] );
         virtual void dmwflx_z( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *zl_re[], Real *zl_im[], Real *rhsl_re[], Real *rhsl_im[],
                                                 Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *zr_re[], Real *zr_im[], Real *rhsr_re[], Real *rhsr_im[],
                                                 Real *wc[], Real *wxdc[], Real *auxc[] );
         //virtual void dmwflx_new( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                   Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                   Real *[], Real *[], Real *[] ){};
         virtual void dmwflx_new( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                   Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                   Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ){};
         virtual void dmwflx_new( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                   cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                   cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};

  };

   class cCebeci: public cLaminar
  {
      protected:
         Real karm;
         Real b;
         virtual void   maux33( Int, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real );
         virtual void   maux33( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq );
         virtual void   maux33( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux );

         //virtual void  mwflx33( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                 Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                        Real *[], Real *[], Real *[] );
         virtual void   mwflx33( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,      
                                                  Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr,
                                                  Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr );
         virtual void   mwflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                  cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                  cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
      public:
         cCebeci();
         visc_t gettype(){ return cebeci_visc; };
         void setvrs( Int , Int , Int *, Int *, Int *, Int * );
         virtual void   maux( Int, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real );//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] );
         virtual void   maux( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq );
         virtual void   maux( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux );

         //virtual void  mwflx( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], 
         //                               Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                      Real *[], Real *[], Real *[] );
         virtual void   mwflx( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,      
                                                Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr,
                                                Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr );
         virtual void   mwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         //virtual void dmwflx_new( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                   Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                   Real *[], Real *[], Real *[] );
         virtual void dmwflx_new( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                   Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                   Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );

         virtual void dmwflx_new( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                   cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                   cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
  };

   class cKomega: public cLaminar
  {
      protected:
         Real karm;
         Real b;
         Real stbeta,beta,alpha;
         Real stsigma,sigma;
         //virtual void  mwflx33( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                 Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                        Real *[], Real *[], Real *[] );
         virtual void mwflx33( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl, 
                                                Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr, 
                                                Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr );
         virtual void   mwflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                  cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                  cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         //virtual void   srhs33( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Real *[], Real *[], Real *[] );
         virtual void srhs33( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *srhs, Real *slhs, Int nq );
         virtual void srhs33( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& rhs, cAu3xView<Real>& lhs );
         //virtual void   mflx33( Int, Int, Int *, Real *[], Real *[], Real *[], Real **[], Real *[],
         //                                 Int *, Real *[], Real *[], Real *[], Real **[], Real *[],
         //                                        Real *[], Real *[], Real *[], Real *[] );
         virtual void   mflx33( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdqdxl, Real *srhsl,
                                                  Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqdxr, Real *srhsr,
                                                  Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void   mflx33( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& rhsl,
                                                  cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& rhsr,
                                                  cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         //virtual void   mflx33( Int, Int, Int *[2], Real *[], Real *[], Real *[], Real **[], Real *[],
         //                                        Real *[], Real *[], Real *[], Real *[] );
         virtual void   mflx33( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdqdx, Real *srhs,
                                Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void   mflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& rhs,
                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         //virtual void  dmflx33( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                 Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], 
         //                                        Real *[], Real *[], Real *[], Real *[] );
         virtual void  dmflx33( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                  Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                  Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void   dmflx33( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& sxl, cAu3xView<Real>& sql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                   cAu3xView<Int>& icqr, cAu3xView<Real>& sxr, cAu3xView<Real>& sqr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                   cAu3xView<Real>& xc, cAu3xView<Real>& swc, cAu3xView<Real>& swxdc, cAu3xView<Real>& auxc ) ;
         //virtual void  dmflx33( Int ics, Int ice, Int *icq[2], Real *x[], Real *q[], Real *aux[], Real *dq[], Real *daux[], Real *res[],
         //                       Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] );
         virtual void  dmflx33( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdq, Real *sdaux,
                              Real *sres, Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) ;
         virtual void  dmflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux,
                              cAu3xView<Real>& res, cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

      public:
         cKomega();
         visc_t gettype(){ return komega_visc; };
         void setvrs( Int , Int , Int *, Int *, Int *, Int * );
         virtual void   maux( Int, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real );//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] );
         virtual void   maux( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq );
         virtual void   maux( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux );

         virtual void dvar( Int , Int , Real *[], Real *[], Real *[], Real *[] );
         virtual void dvar( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq );
         virtual void dvargpu( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq );
         virtual void dvar( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dU, cAu3xView<Real>& dq );

         //virtual void   mflx( Int, Int, Int *, Real *[], Real *[], Real *[], Real **[], Real *[],
         //                               Int *, Real *[], Real *[], Real *[], Real **[], Real *[],
         //                                      Real *[], Real *[], Real *[], Real *[] );
         virtual void   mflx( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdqdxl, Real *srhsl,
                                                Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqdxr, Real *srhsr,
                                                Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void   mflx( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& rhsl,
                                                cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& rhsr,
                                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         //virtual void   mflx( Int, Int, Int *[2], Real *[], Real *[], Real *[], Real **[], Real *[],
         //                                      Real *[], Real *[], Real *[], Real *[] );
         virtual void   mflx( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdqdx, Real *srhs,
                              Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void   mflx( Int ics, Int ice,cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& rhs,
                              cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         //virtual void  dmflx( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                               Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], 
         //                                      Real *[], Real *[], Real *[], Real *[] );
         virtual void  dmflx( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void  dmflx( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         //virtual void  dmflx( Int ics, Int ice, Int *icq[2], Real *x[], Real *q[], Real *aux[], Real *dq[], Real *daux[], Real *res[],
         //                     Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] );
         virtual void  dmflx( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdq, Real *sdaux,
                              Real *sres, Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) ;
         virtual void  dmflx( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux,
                              cAu3xView<Real>& res, cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ) ;

         //virtual void  mwflx( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], 
         //                               Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                      Real *[], Real *[], Real *[] );
         virtual void mwflx( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl, 
                                              Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr, 
                                              Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr );
         virtual void mwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                              cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void nondim( Int , Int , Real *[], Int * );
         virtual void nondim( Int , Int , cAu3xView<Real>& , Int * );

         virtual void redim( Int , Int , Real *[] );
         virtual void redim( Int , Int , cAu3xView<Real>& );
         virtual void redim_cpu( Int , Int , cAu3xView<Real>& );

         //virtual void vlhs( Int iqs, Int iqe, Real cfl, Real *wq[], Real *lhs[] );
         virtual void vlhs( Int iqs, Int iqe, Real cfl, Real *swq, Real *slhs, Int nq );
         virtual void vlhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& wq, cAu3xView<Real>& lhs );
         //virtual void invdg( Int iqs, Int iqe, Real *lhs[], Real *res[] );
         virtual void invdg( Int iqs, Int iqe, Real *slhs, Real *sres, Int nq );
         virtual void invdg( Int iqs, Int iqe, cAu3xView<Real>& lhs, cAu3xView<Real>& res );

         virtual void  ilhs( Int , Int , Int *, Real *[], Real *[], Real *[],
                                         Int *, Real *[], Real *[], Real *[],
                                                Real *[], Real *[], Real *[] );
         virtual void ilhs( Int ics, Int ice, Int *sicql, Real *sql, Real *sauxl, Real *slhsl,
                                              Int *sicqr, Real *sqr, Real *sauxr, Real *slhsr,
                                              Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ); 
         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                              cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                              cAu3xView<Real>& wc,  cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void  ilhs( Int ics, Int ice, Int *sicq, Real *sq, Real *saux, Real *slhs,          
                             Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& lhs,
                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         //virtual void slhs( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Real *[], Real *[] );
         virtual void slhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *slhsa, Int nq );
         virtual void slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa );
         //virtual void srhs( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Int *idst[], Int *igdst[], Real *[], Real *[], Real *[] );
         virtual void srhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Int *sidst, Int *sigdst, Real *swq, Real *srhs, Real *slhsa, Int nq );
         virtual void srhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& rhs, cAu3xView<Real>& lhsa );
         //virtual void dsrhs( Int , Int , Real , Real *[], Real *[], Real *[], Real *[], Real **[], Real *[], 
         //               Real *[], Real *[], Real *[] );
         virtual void dsrhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sdqdx, Real *sdst,
                             Real *swq, Real *sres, Real *slhs, Int nq );
         virtual void dsrhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                             cAu3xView<Real>& wq, cAu3xView<Real>& res, cAu3xView<Real>& lhs );
  };

   class cKomegaLowRe: public cKomega
   {
      protected:
         //virtual void   srhs33( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Real *[], Real *[], Real *[] );
         virtual void srhs33( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *srhs, Real *slhs, Int nq );
         virtual void srhs33( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& rhs, cAu3xView<Real>& lhs );

      public:
         visc_t gettype(){ return komegalowre_visc; };
         void setvrs( Int , Int , Int *, Int *, Int *, Int * );
         virtual void   maux( Int, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real );//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] );
         virtual void   maux( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq );
         virtual void   maux( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux );

         //virtual void slhs( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Real *[], Real *[] );
         virtual void slhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *slhsa, Int nq );
         virtual void slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa );
         //virtual void dsrhs( Int , Int , Real , Real *[], Real *[], Real *[], Real *[], Real **[], Real *[], 
         //               Real *[], Real *[], Real *[] );
         virtual void dsrhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sdqdx, Real *sdst,
                             Real *swq, Real *sres, Real *slhs, Int nq );
         virtual void dsrhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                             cAu3xView<Real>& wq, cAu3xView<Real>& res, cAu3xView<Real>& lhs );
         //virtual void   yplus( Int ist, Int ien, Int *iqd, Real *xq[], Real *q[], Real *aux[], Int *iqb, Real *qb[], Real *auxb[],
         //                      Real *dst[] );
         virtual void yplus( Int ist, Int ien, Int *siqd, Real *sxq, Real *sq, Real *saux, Int *siqb, Real *sqb, Real *sauxb, Real *sdst, Int nbb, Int nq, Int ndst );
         virtual void yplus( Int ist, Int ien, cAu3xView<Int>& iqd, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Int>& iqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, 
                             cAu3xView<Real>& dst );

   };

   class cCebeciLowRe: public cCebeci
   {
      protected:
         Real lmixmax;

         //virtual void  mwflx33( Int, Int, Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                 Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                        Real *[], Real *[], Real *[] );
         virtual void mwflx33( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl, 
                                                Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr, 
                                                Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr );
         virtual void   mwflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                  cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                  cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void   maux33( Int, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real );//, Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] );
         virtual void   maux33( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq );
         virtual void   maux33( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux );

      public:
         visc_t gettype(){ return cebecilowre_visc; };
         virtual void   maux( Int, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real );//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] );
         virtual void   maux( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq );
         virtual void   maux( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux );

         //virtual void   yplus( Int ist, Int ien, Int *iqd, Real *xq[], Real *q[], Real *aux[], Int *iqb, Real *qb[], Real *auxb[],
         //                      Real *dst[] );
         virtual void yplus( Int ist, Int ien, Int *siqd, Real *sxq, Real *sq, Real *saux, Int *siqb, Real *sqb, Real *sauxb, Real *sdst, Int nbb, Int nq, Int ndst );
         virtual void yplus( Int ist, Int ien, cAu3xView<Int>& iqd, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Int>& iqb, cAu3xView<Real>& qb, 
                             cAu3xView<Real>& auxb, cAu3xView<Real>& dst );

   };

    cVisc *newvisc( Int ityp );
#endif
