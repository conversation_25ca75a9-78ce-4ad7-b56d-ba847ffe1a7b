#ifndef _GRAD_
#define _GRAD_

#  define MxNGrp 100
#  include <field/field.h>
#  include <mem/proto.h>
#  include <cmath>
#  include <lin/matrix.h>
#  include <const.h>

   class cGrad
  {
      protected:
         bool gdebug;
         Int     limtype;

      public:
         cGrad(){gdebug=false;};
        ~cGrad(){};
         bool getdbg(){ return gdebug; };
         virtual void deltq( Int, Int, Real *[], Real *[], Real *[], Real **[],
                             Int, Int, Real *[], Real *[], Real *[], Real **[],
                             Real *, Real *, Real *, Real *, Real *, Real * ){};
         virtual void deltq_exact( Int, Int, Real *[], Real *[], Real *[], Real **[],
                                   Int, Int, Real *[], Real *[], Real *[], Real **[],
                                   Real *, Real *, Real *, Real *, Real *, Real * ){};
         virtual Int getlimtype() { return limtype; };


         void deltq_venk( Int nx, Int nv, Int ic, Int iql, Real *xql[], Real *limvenkl[], Real **dqdxl[], Real *dql, 
                                                  Int iqr, Real *xqr[], Real *limvenkr[], Real **dqdxr[], Real *dqr,
                                                  Real *xc[], Real *wc[] )
        {
            Int ix, ia;
            Real dxl[3], dxr[3];

            for(ix=0; ix<nx; ix++)
           {
               dxl[ix] = xc[ix][ic] - xql[ix][iql];
               dxr[ix] = xc[ix][ic] - xqr[ix][iqr];
           }
            for(ia=0; ia<nv; ia++)
           {
               dql[ia] = 0;
               dqr[ia] = 0;
               for(ix=0; ix<nx; ix++)
              {
                  dql[ia] += dqdxl[ia][ix][iql]*dxl[ix];
                  dqr[ia] += dqdxr[ia][ix][iqr]*dxr[ix];
              }
               if(limvenkl!=NULL) dql[ia] *= limvenkl[ia][iql];
               else               dql[ia]  = 0;
               if(limvenkr!=NULL) dqr[ia] *= limvenkr[ia][iqr];
               else               dqr[ia] *= 0;
           }
        }

  };

   inline void valbada2( Int nv, Real *r1, Real *r2, Real *eps, Real *val )
  {
      Int iv;
      Real r;
      for( iv=0;iv<nv;iv++ )
     {
         r= r1[iv]*r2[iv];
         if( r <= 0 )
        {
            val[iv]= 0;
        }
         else
        {
            val[iv]= ( r2[iv]*(r1[iv]*r1[iv]+eps[iv])+r1[iv]*(r2[iv]*r2[iv]+ eps[iv]) )/
                     ( r1[iv]*r1[iv]+ r2[iv]*r2[iv]+ 2*eps[iv] );
        }
     }
   }


   inline void deltq( Int nv, Int iql, Int iqr, Real *sxq, Real *sq, Real *sdxdx, Real *sdqdx, 
                      Real *xn, Real *wn,  Real *dql0, Real *dqr0, Real *dql, Real *dqr, Int nq )
  {
      Int ix,jx,iv;
      Real w,dx,dxi,dxj,wl,wr;
      Real dqi[MxNVs];
      Real eps[MxNVs];

      Real dxdxl1[9];
      Real dxdxr1[9];
      Real dqdxl1[MxNVs][3];
      Real dqdxr1[MxNVs][3];

      for( ix=0;ix<3;ix++ )
     {
         for( jx=0;jx<3;jx++ )
        {
            //dxdxl1[ix*3+jx]= sdxdx[(ix*3+jx)*nq+iql];
            dxdxl1[ix*3+jx]= sdxdx[ADDR(ix,jx,iql,nq)];
        }
         for( iv=0;iv<nv;iv++ )
        {
            //dqdxl1[iv][ix]= sdqdx[iql+ix*nq+iv*nq*3];
            dqdxl1[iv][ix]= sdqdx[ADDR(iv,ix,iql,nq)];
        }
     }
      for( ix=0;ix<3;ix++ )
     {
         for( jx=0;jx<3;jx++ )
        {
            //dxdxr1[ix*3+jx]= sdxdx[(ix*3+jx)*nq+iqr];
            dxdxr1[ix*3+jx]= sdxdx[ADDR(ix,jx,iqr,nq)];
        }
         for( iv=0;iv<nv;iv++ )
        {
            //dqdxr1[iv][ix]= sdqdx[iqr+ix*nq+iv*nq*3];
            dqdxr1[iv][ix]= sdqdx[ADDR(iv,ix,iqr,nq)];
        }
     }
      w= 0;
      Real wi=0;
      for( ix=0;ix<3;ix++ )
     {
         //dx= sxq[ix*nq+iqr]- sxq[ix*nq+iql];
         dx= sxq[ADDR(ix,iqr,nq)]- sxq[ADDR(ix,iql,nq)];
         w+= ( dx*dx );
         wi+= dx*wn[ix];
      
     } 
      w= 1./w;

      for( iv=0;iv<nv;iv++ )
     {
         //dqi[iv]= ( sq[iv*nq+iqr]- sq[iv*nq+iql] );
         dqi[iv]= ( sq[ADDR(iv,iqr,nq)]- sq[ADDR(iv,iql,nq)] );
     }

      for( ix=0;ix<3;ix++ )
     {
         //dxi= sxq[ix*nq+iqr]- sxq[ix*nq+iql];
         dxi= sxq[ADDR(ix,iqr,nq)]- sxq[ADDR(ix,iql,nq)];
         for( jx=0;jx<3;jx++ )
        {
            //dxj= sxq[jx*nq+iqr]- sxq[jx*nq+iql];
            dxj= sxq[ADDR(jx,iqr,nq)]- sxq[ADDR(jx,iql,nq)];
            dxdxl1[ix*3+jx]-= w*dxi*dxj;
            dxdxr1[ix*3+jx]-= w*dxi*dxj;
        }
         for( iv=0;iv<nv;iv++ )
        {
            dqdxl1[iv][ix]-= w*dxi*dqi[iv];
            dqdxr1[iv][ix]-= w*dxi*dqi[iv];
        }
     }

// mininum norm
      wl=0;
      wr=0;
      for( ix=0;ix<3;ix++ )
     {
         for( jx=0;jx<3;jx++ )
        {
            wl+= dxdxl1[ix*3+jx]*wn[ix]*wn[jx];
            wr+= dxdxr1[ix*3+jx]*wn[ix]*wn[jx];
        }
     }
      for( iv=0;iv<nv;iv++ )
     {
         dql[iv]=0;
         dqr[iv]=0;
         for( ix=0;ix<3;ix++ )
        {
            dql[iv]+= dqdxl1[iv][ix]*wn[ix];       
            dqr[iv]+= dqdxr1[iv][ix]*wn[ix];       
        }
         dql[iv]/= wl;
         dqr[iv]/= wr;
         dqi[iv]/= wi;
         //eps[iv]= small;
         eps[iv]= 1.e-16;
     }

      valbada2( nv,dql,dqi,eps,dql ); // failed
      valbada2( nv,dqr,dqi,eps,dqr );

      wl= 0;
      wr= 0;
      for( ix=0;ix<3;ix++ )
     {
         //wl+= wn[ix]* ( xn[ix]- sxq[ix*nq+iql] );
         //wr+= wn[ix]* ( sxq[ix*nq+iqr] -xn[ix] );
         wl+= wn[ix]* ( xn[ix]- sxq[ADDR(ix,iql,nq)] );
         wr+= wn[ix]* ( sxq[ADDR(ix,iqr,nq)] -xn[ix] );
     }
      for( iv=0;iv<nv;iv++ )
     {
         dql[iv]*=( wl);
         dqr[iv]*=(-wr);
     }
  }

   inline void deltq( Int nv, Int iql, Real *xql, Real *ql, Real *dxdxl, Real *dqdxl, 
                      Int iqr, Real *xqr, Real *qr, Real *dxdxr, Real *dqdxr, 
                      Real *xn, Real *wn,  Real *dql0, Real *dqr0, Real *dql, Real *dqr, Int nql, Int nqr )
  {
      Int ix,jx,iv;
      Real w,dx,dxi,dxj,wl,wr;
      Real dqi[MxNVs];
      Real eps[MxNVs];

      Real dxdxl1[9];
      Real dxdxr1[9];
      Real dqdxl1[MxNVs][3];
      Real dqdxr1[MxNVs][3];

      for( ix=0;ix<3;ix++ )
     {
         for( jx=0;jx<3;jx++ )
        {
            //dxdxl1[ix*3+jx]= dxdxl[(ix*3+jx)*nql+iql];
            dxdxl1[ix*3+jx]= dxdxl[ADDR(ix,jx,iql,nql)];
        }
         for( iv=0;iv<nv;iv++ )
        {
            //dqdxl1[iv][ix]= dqdxl[iql+ix*nql+iv*nql*3];
            dqdxl1[iv][ix]= dqdxl[ADDR(iv,ix,iql,nql)];
        }
     }
      for( ix=0;ix<3;ix++ )
     {
         for( jx=0;jx<3;jx++ )
        {
            //dxdxr1[ix*3+jx]= dxdxr[(ix*3+jx)*nqr+iqr];
            dxdxr1[ix*3+jx]= dxdxr[ADDR(ix,jx,iqr,nqr)];
        }
         for( iv=0;iv<nv;iv++ )
        {
            //dqdxr1[iv][ix]= dqdxr[iqr+ix*nqr+iv*nqr*3];
            dqdxr1[iv][ix]= dqdxr[ADDR(iv,ix,iqr,nqr)];
        }
     }
      w= 0;
      Real wi=0;
      for( ix=0;ix<3;ix++ )
     {
         //dx= xqr[ix*nqr+iqr]- xql[ix*nql+iql];
         dx= xqr[ADDR(ix,iqr,nqr)]- xql[ADDR(ix,iql,nql)];
         w+= ( dx*dx );
         wi+= dx*wn[ix];
      
     } 
      w= 1./w;

      for( iv=0;iv<nv;iv++ )
     {
         //dqi[iv]= ( qr[iv*nqr+iqr]- ql[iv*nql+iql] );
         dqi[iv]= ( qr[ADDR(iv,iqr,nqr)]- ql[ADDR(iv,iql,nql)] );
     }

      for( ix=0;ix<3;ix++ )
     {
         //dxi= xqr[ix*nqr+iqr]- xql[ix*nql+iql];
         dxi= xqr[ADDR(ix,iqr,nqr)]- xql[ADDR(ix,iql,nql)];
         for( jx=0;jx<3;jx++ )
        {
            //dxj= xqr[jx*nqr+iqr]- xql[jx*nql+iql];
            dxj= xqr[ADDR(jx,iqr,nqr)]- xql[ADDR(jx,iql,nql)];
            dxdxl1[ix*3+jx]-= w*dxi*dxj;
            dxdxr1[ix*3+jx]-= w*dxi*dxj;
        }
         for( iv=0;iv<nv;iv++ )
        {
            dqdxl1[iv][ix]-= w*dxi*dqi[iv];
            dqdxr1[iv][ix]-= w*dxi*dqi[iv];
        }
     }

// mininum norm
      wl=0;
      wr=0;
      for( ix=0;ix<3;ix++ )
     {
         for( jx=0;jx<3;jx++ )
        {
            wl+= dxdxl1[ix*3+jx]*wn[ix]*wn[jx];
            wr+= dxdxr1[ix*3+jx]*wn[ix]*wn[jx];
        }
     }
      for( iv=0;iv<nv;iv++ )
     {
         dql[iv]=0;
         dqr[iv]=0;
         for( ix=0;ix<3;ix++ )
        {
            dql[iv]+= dqdxl1[iv][ix]*wn[ix];       
            dqr[iv]+= dqdxr1[iv][ix]*wn[ix];       
        }
         dql[iv]/= wl;
         dqr[iv]/= wr;
         dqi[iv]/= wi;
         //eps[iv]= small;
         eps[iv]= 1.e-16;
     }

      valbada2( nv,dql,dqi,eps,dql ); // failed
      valbada2( nv,dqr,dqi,eps,dqr );

      wl= 0;
      wr= 0;
      for( ix=0;ix<3;ix++ )
     {
         //wl+= wn[ix]* ( xn[ix]- xql[ix*nql+iql] );
         //wr+= wn[ix]* ( xqr[ix*nqr+iqr] -xn[ix] );
         wl+= wn[ix]* ( xn[ix]- xql[ADDR(ix,iql,nql)] );
         wr+= wn[ix]* ( xqr[ADDR(ix,iqr,nqr)] -xn[ix] );
     }
      for( iv=0;iv<nv;iv++ )
     {
         dql[iv]*=( wl);
         dqr[iv]*=(-wr);
     }
  }

#endif
