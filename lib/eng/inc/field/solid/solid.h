#  ifndef _SOLID_
#  define _SOLID_

#  include <field/field.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Sun Oct 24 13:42:33 BST 2010
// Changes History
// Next Change(s)  -
// Purpose         generic solid class

   enum solid_t { solid_bad= -1, solid_elastic };

   class cSolid: public cField
  {
      protected:
//       Int                                         nv;
      public:
                                                cSolid();
         virtual                               ~cSolid();
         virtual void                             props( Int n, string *l ){};
         virtual Int                            gettype(){ return solid_bad; };
         virtual Int                              getnv(){ return nv; };
         virtual void                             sizes( cCosystem *coo ){ nv= coo->getnx(); };

         virtual void                              stressb1( Int, Int, Int *,Real *[], Real *[], Real *[], Real *[] ){};
         virtual void                              stressb2( Int, Int, Int *,Real *[], Real *[], Real *[], Real *[] ){};
         virtual void                              stressb3( Int, Int, Int *,Real *[], Real *[], Real *[] ){};
                                                   

         virtual void                              stress1( Int, Int, Int *,Real *[], Real *[] ){};
         virtual void                              stress2( Int, Int, Int *,Real *[], Real *[] ){};
         virtual void                              stress3( Int, Int, Int *,Real *[], Real *[] ){};

  };

   class cElastic: public cSolid
  {
      protected:
         Int                                      nm;
         string                                  *lbl;
         Real                                    *e,*nu,*rho;

         virtual void                             clear();
      public:
                                                cElastic();
         virtual                               ~cElastic();
         virtual void                             props( Int n, string *l );
         virtual Int                            gettype(){ return solid_elastic; }

         virtual void                              stressb1( Int, Int, Int *,Real *[], Real *[], Real *[], Real *[] );
         virtual void                              stressb2( Int, Int, Int *,Real *[], Real *[], Real *[], Real *[] );
         virtual void                              stressb3( Int, Int, Int *,Real *[], Real *[], Real *[] ); 
 
         virtual void                              stress1( Int, Int, Int *,Real *[], Real *[] );
         virtual void                              stress2( Int, Int, Int *,Real *[], Real *[] );
         virtual void                              stress3( Int, Int, Int *,Real *[], Real *[] ); 

         virtual void                              pickle( size_t *len, pickle_t *buf );
         virtual void                            unpickle( size_t *len, pickle_t  buf );
  };

   cSolid *newsolid( Int );

#  endif
