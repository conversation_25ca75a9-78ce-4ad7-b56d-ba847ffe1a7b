#ifndef _FIELD_
#define _FIELD_

#  define MxNVs 10

#  include <const.h>
#  include <cmath>
#  include <pickle/proto.h>
#  include <tab/proto.h>
#  include <cosystem/cosystem.h>

   class cField: public cPickle, public cTabSrc
  {
      protected:
         Real            unit[MxNVs],deflt[MxNVs];
         Int             ilv[MxNVs];
         Int             nvk,nv;

      public:

	//! Default constructor
         cField(){};
         virtual ~cField(){};
         virtual void nondim( Int, Int, Real *[], Int * ){};
         virtual void nondim( Int, Int, cAu3xView<Real>& , Int * ){};

         virtual void redim( Int, Int, Real *[] ){};
         virtual void redim( Int, Int, cAu3xView<Real>& ){};

         virtual Int getnv(){};
         virtual Int units( Int i){ return unit[i]; };
  };

#endif
