#  ifndef _LOOKUP_
#  define _LOOKUP_

#  include <cstdlib>
#  include <cmath>
#  include <cprec.h>
#  include <const.h>

   struct lookup_t
  {
      Real       s0,s1;
      bool       inside;
      bool       good;
  };

   template <typename type> void allc3( Int l, Int m, Int n, type ****var )
  {
      Int i,j,k;      

    (*var)= new type**[n];

      for( j=0;j<n;j++ )
     {
       (*var)[j]= new type*[m];
         for( i=0;i<m;i++ )
        {
          (*var)[j][i]= new type[l];
        }
     }
  };

   template <typename type> void del3( Int l, Int m, Int n, type ****var )
  {
      Int i,j,k;      
      for( j=0;j<n;j++ )
     {
         for( i=0;i<m;i++ )
        {
            delete[] (*var)[j][i]; (*var)[j][i]= NULL;
        }
         delete[] (*var)[j]; (*var)[j]= NULL;
     }
      delete[] (*var); (*var)=NULL;
  };

   class cLookup
  {
       protected:
          Int             nv,n1,n2;
          Real           *sx;
          Real           *sv;

          Real          ***x;
          Real          ***v;
       public:
          cLookup();
          virtual ~cLookup();

          void build( Int l, Int n, Int m, Real ***y, Real ***q );
          void interp( Real *s, Real *v, Real *dv0, Real *dv1 );
  };

#  endif
