#  ifndef _Q2D_
#  define _Q2D_

#  include <cmath>
#  include <cprec.h>
#  include <cassert>
#  include <utils/proto.h>
#  include <sort/proto.h>
#  include <lin/vect.h>
#  include <quad/q2dutils.h>
#  include <geo/3d/tri3d.h>

#  include <fstream>
#  include <iostream>
#  include <string>

/**@ingroup engineering
  *@{
 **/


/**@defgroup q2d General quad grids
    A set of abstractions to manipulate general quadrilateral grids in two and three space dimensions.
   @todo   Idiosyncracies in the general structure of the code to be removed: the stitch line reconstruction
           is cumberson and does not use the multi-block nature of the grid. Extend to ijk multiblocks.
   <AUTHOR>
   @date  Tue Jun  5 10:14:58 BST 2012
  *@{
  */

#  define MXNG 100      /**< Maximum number of boundary groups */

   struct q2d_t
  {
      Int                np;
      Int                ne;
      Int                ng;
      Int               nbg[MXNG];
      Int               npg[MXNG];
      Real              *xp[3];
      Real              *yp[2];
      Int              *iep[4];
      Int              *ipg[MXNG];
      Int              *ipb[MXNG][2];
      Int              *ieb[MXNG][2];
      string           bgnm[MXNG];
      periodic_t        prd[MXNG];
      smooth_t          smt[MXNG];
      Int               npr;
      Int              *ipr[2];

  };

   class cQ2d
  {

      protected:
         static const Int  MXNB=200;      /**< Maximum boundary size                                          **/
         static const Int  MXNP=40000;    /**< Maximum node count.                                            **/
         static const Int  MXNE=40000;    /**< Maximum element count.                                         **/

         Real             tolp;           /**< Tolerance for point insertion.                                            **/

         Int                np;           /**< Node count.                                                   **/
         Int                ne;           /**< Element count.                                                **/
         Int                ng;           /**< Boundary group count                                          **/
         Int               nbg[MXNG];     /**< Edge count on each boundary group.                            **/
         Int               npg[MXNG];     /**< Node count on each boundary group.                            **/
         Real              *xp[3];        /**< Three-dimensional node coordinates                            **/
         Real              *yp[2];        /**< Two-dimensional node coordinates.                             **/
         Int              *iep[4];        /**< Element-node connectiviy.                                     **/
         Int              *ipg[MXNG];     /**< Boundary node list for each boundary group.                   **/
         Int              *ipb[MXNG][2];  /**< Boundary edge-node list for each boundary group.              **/
         Int              *ieb[MXNG][2];  /**< Boundary edge-element for each boundary group. ieb[j][0][i]   
                                               is the element attached to boundary edge i on group j. ieb[j][1][i]
                                               is the side of that element corresponding to the boundary edge.
                                                                                                             **/ 
         string           bgnm[MXNG];     /**< Boundary group identifiers.                                   **/
         periodic_t        prd[MXNG];     /**< Boundary group periodic flags. No more than two periodic groups 
                                               are allowed at one time                                       **/
         smooth_t          smt[MXNG];     /**< Boundary group smooting flags.                                **/
         Real             ptch;           /**< Domain pitch                                                  **/
         Int               npr;           /**< Periodic nodes count.                                         **/
         Int               ipg0;          /**< Left periodic boundary index                                  **/
         Int               ipg1;          /**< Right periodic boundary index.                                **/
         Int              *ipr[2];        /**< Periodic node index                                           **/

/** Copy constructor. The existing grid is copied into var.
   @param                 var             Copy target.
   @brief                                 Copy.
 **/         
         virtual void     copy( cQ2d  *var );

/** Sort out boundary points. Assume the boundary edges are valid and generate lists of points on each boundary.
   @brief                                 Boundary point lists.
 **/         
         virtual void     bpts();

/** Check that the boundary representation is consistent with the element topology.
   @brief                                 Boundary check.
 **/         
         virtual void   checkb();

/** Add the quadrilateral grid var to the existing grid. It is assumed that the grids have matching points on
    parts of their boundaries. No other overlap is allowed. add() increases the point and element counts for the
    existing grid and includes the new nodes and elments from var. The boundary groups are also updated. If var
    contains boundary groups with the same identifier as existing groups, these are merged, otherwise new boundary
    groups are formed with the same identifiers as those in var. Matching boundary edges are removed. If all the edges 
    a group are removed the group itself is removed. The collapsed edges are concatenated in mappable chains and
    reported in svar. The numbering of the nodes and elements from var in the updated grid is reported in indx and iedx.
   @param                 var             The grid to be added.
   @param                 svar            Collapsed boundaries parsed in mappable chains.
   @param                 indx            Node identites from var in the updated grid.
   @param                 iedx            Element identites from var in the updated grid.
   @brief Add grid.
 **/
         virtual void add( cQ2d *var, q2dstitch_t *svar, Int *indx, Int *iedx );

/** Parse a boundary in mappable chanis. The boundary is described by its edges (isp) and by the quads attached to it 
    (ise). The number of mappable chains is reported in m. The end index of each chain is reported in lstc. The chain 
    orderings is reported in iprm. The boundary is scanned in its natural direction if idr is 0, in the opposite 
    direction if idr is 1.
   @param                 idr              Direction flag. 
   @param                 isp              Edge point connectivity.
   @param                 ise              Edge element connectivity. isp[0][i] is the element attached to the edge i,
                                           ise[1][i] is the element side corresponding to edge i.
   @param                 m                Number of mappable chains. 
   @param                 lstc             The end index of each chain.
   @param                 iprm             The chains ordering.
   @brief Mappable chains on boundaries.
 **/ 
         virtual void breaks( Int idr, Int *isp[], Int *ise[], Int *m, Int *lstc, Int *iprm );

/** Parse a stitch data structure in mappable chains. The stitch structure var is modified directly to represent
    a chain of mappable chains.
   @param                 var              The stitch data structure.
   @brief Mappable chains on stitch lines.
 **/ 
         virtual void breaks( q2dstitch_t *var );


/** Build periodic node lists.
 **/
         virtual void periodic();

      public:

         bool debug; /**< Debug control flag **/


         cQ2d();
         virtual ~cQ2d();

         virtual void tec( string );
         virtual void gnuplot( string );
         virtual void gnuplotb( string s );

         virtual void reset();
         virtual void add( cQ2d * );
         virtual void translate( Real * );

         virtual void copy( cQ2d ** );
         virtual bool same( cQ2d * );

         virtual void dump();
         virtual void finalize();

         virtual void rename( string, string );

/** Export the entire content of the object to a grid data structure.
   @param                   var            The grid data structure.
 **/
         virtual void exprt( q2d_t *var );

         virtual bool mapped(){ return false; };
 
//       virtual void smooth(){};

/** Locate in the grid the closest point to x
   @param                  x         Point coordinates.
   @return                           Closest node index.
 **/
         virtual Int  locate( Real *x );
  };

/**
  *@}
  */


/**@defgroup q2dmblock Multiblock quad grids
  *@{
  */


/**@defgroup q2dblk Blocks
  *@{
  */

/** Block description for multiblock quad grids **/
   struct q2dmblk_t
  {
      Int         ni;                       /** i-extent of the block. Cell count                                     **/
      Int         nj;                       /** j-extent of the block. Cell count                                     **/
      Int       **ij;                       /** ij references to global numbering                                     **/
      Real      dsrc[4];                    /** Source strengths for boundary attraction                              **/
      Real      **zp[2];                    /** Source distributions                                                  **/
  };

/** Initialize a block data structure.
   @param       data                            Block data structure.
  */
   void init( q2dmblk_t *data );

/** Destroy a block data structure.
   @param       data                            Block data structure.
  */
   void destroy( q2dmblk_t *data );

/** Copy a block data structure.
   @param       var                             Copy source.
   @param       data                            Copy target.
  */
   void copy( q2dmblk_t *var, q2dmblk_t *data );

/**
  *@}
  */

/**@defgroup q2dstitch Stitch lines
  *@{
  */

/** Multiblock stitch and boundary lines **/

   struct q2dmstitch_t
  {

         Int              n;                 /**< Number of segments in the stitch line                               **/
         Int             *i0;                /**< Nodes along the stitch line                                         **/
         Int             *i1;                /**< Left neighbours along the stitch line                               **/
         Int             *i2;                /**< Right neighbours along the stitch line                              **/
         Int             *i3;                /**< Periodic images along the stitch line                               **/
         bool            prd;                /**< Periodic flag                                                       **/
         smooth_t        smt;                /**< Smooth flag                                                         **/
         bool            bnd;                /**< Boundary flag                                                       **/
         Real           ys0[2];              /**< Reference point for sliding smoothing                               **/
         Real           ls0[2];              /**< Refrence direction for sliding smoothing                            **/
  };

/** Initialize a multiblock stitch line data structure.
   @param                          var          Stitch line data structure.
  */ 
   void init( q2dmstitch_t *var );

/** Destroy a multiblock stitch line data structure.
   @param                          var          Stitch line data structure.
  */
   void destroy( q2dmstitch_t * );

/** Build a multiblock stitch line data structure from a subset of a stitch line data structure
    The stitch node neighbours are determined using the information in the quad grid data structure.
   @param                          var          Stitch line data structure.
   @param                          ist          Start index along the stitch line.
   @param                          ent          End index along the stitch line.
   @param                          svar         Stitch line data structure.
   @param                          data         Quad grid data structure.
 **/
   void build( q2dmstitch_t *var, Int, Int , q2dstitch_t *svar, q2d_t *data );

/** Build a multiblock stitch line data structure from a subset of a boundary group.
    The stitch node neighbours are determined using the information in the quad grid data structure.
   @param                          var          Stitch line data structure.
   @param                          ig           Source boundary group.
   @param                          ist          Start index along the group.
   @param                          ien          End index along the group.
   @param                          data         Quad grid data structure.
 **/
   void build( q2dmstitch_t *var,    Int ig, Int ist, Int ien, q2d_t *data );

/** Copy multiblock stitch lines.
   @param                          var          Copy source.
   @param                          data         Copy target.
 **/
   void  copy( q2dmstitch_t *var, q2dmstitch_t *data );

/**
  *@}
  */

/**@defgroup q2dbrnch Branch points
  *@{
  */
/** Multiblock branch points **/

   struct q2dmbrnch_t
  {
         Int               n;                     /**< Branch points count **/
         Int             *nd;                     /**< Neighbours count for each branch point **/
         Int             *id;                     /**< Node index of each branch point **/
         Int            **jd;                     /**< Neighbour node index for each branch point **/
         Int            *ipr;                     /**< Periodic match for each branch point **/
         Int           **jpr;                     /**< Periodic neighbours for each branch point **/
         bool           *bnd;                     /**< Boundary flag for each branch point **/
  };

/** Initialize a multiblock branch point data structure.
   @param                   var              Multiblock data structure.
  */
   void init( q2dmbrnch_t *var );

/** Destroy a multiblock branch point data structure.
   @param                   var              Multiblock data structure.
  */
   void destroy( q2dmbrnch_t *var );

/** Reset a multiblock branch point data structure.
   @param                   var              Multiblock data structure.
  */
   void reset( q2dmbrnch_t *var );

/** Inset or update a branch point corresponding to node ip. The remaining vertices of element ie are added
    to the list of neighbours. The function returns the position of the branch point in the internal store.
   @param                obj         Multi block branch point data structure.
   @param                ip          Node.
   @param                ie          Element.
   @param                var         Mesh data structure.
   @return                           The position of the branch point in the internal store.
 **/
   Int             add( q2dmbrnch_t *obj, Int ip, Int ie, q2d_t *var ); 

/** Add node j to the list of neighbours of branch point i.
    to the list of neighbours. The function returns the position of the branch point in the internal store.
   @param                obj         Multi block branch point data structure.
   @param                i           Branch point.
   @param                j           Neighbour.
 **/
   void            add( q2dmbrnch_t *obj, Int i, Int j );

/** Add branch points corresponding to the end points of mappable chains in a stitch data structures.
   @param                obj         Multi block branch point data structure.
   @param            var          Stitch data structure.
   @param            data         Grid data structure.
 **/
   void build( q2dmbrnch_t *obj, q2dstitch_t *var, q2d_t * );

/** Add branch points corresponding to the end points of mappable chains.
   @param                obj         Multi block branch point data structure.
   @param            isp          Mappable chain edge-node connectivity.  
   @param            iep          Mappable chain element-node connectivity.  
   @param            m            Mappable chain count.
   @param            lprm         Mappable chain end index.
   @param            data         Grid data structure.
 **/
   void build( q2dmbrnch_t *obj, Int *isp[], Int *ise[], Int m, Int *lprm, q2d_t *data );

/** Copy constructor. var is assigned the same values as the object invoking the method.
   @param                obj         Multi block branch point data structure.
   @param             var          Copy target.
 **/
   void copy( q2dmbrnch_t *obj, q2dmbrnch_t *var );

/** Return an integer with the position of the branch point corresponding to node i in the internal store.
    If node i is not a branch point return -1.
   @param                obj         Multi block branch point data structure.
   @param                 i        Node
   @return                         The index of the corresponding branch point. -1 if the node is not a branch point
 **/
   Int  isbrnch( q2dmbrnch_t *obj, Int i );

/** Update the branch points using periodic information.
   @param                obj         Multi block branch point data structure.
   @param                 data        Quad grid data structure.
 **/
   void periodic( q2dmbrnch_t *obj, q2d_t *data );

/**
  *@}
  */

/** Single block quadrilateral grids **/

   class cQ2dBlk: public cQ2d
  {
      protected:
          
         Int                     ni;
         Int                     nj;

         Int                    *ij[MXNB];
         Int                   *ije[MXNB];

         Real                  dsrc[4];

         virtual Int           ibnd( Int, Int );
         virtual Int           jbnd( Int, Int );

         virtual void          bpts();
         virtual void          copy( cQ2dBlk  * );
         virtual void          addb( Int, q2dbnd_t *b );
         virtual void          copy( Int, Int, Int, Int, cQ2dBlk  * );
         void                 touch(    Int, q2dbnd_t * );
      public:

                       cQ2dBlk();
         virtual      ~cQ2dBlk();
         virtual bool mapped(){ return true; };

         virtual void                 reset();

         void                 build( q2dbnd_t *b0, q2dbnd_t *b1, q2dbnd_t *b2, q2dbnd_t *b3 );
         void                 touch( string, q2dbnd_t * );
         virtual void          copy( cQ2d ** );
         virtual void       istitch( q2dstitch_t *);

         virtual void          icut( Int, cQ2dBlk *, cQ2dBlk * );
         virtual void          jcut( Int, cQ2dBlk *, cQ2dBlk * );

         virtual void         exprt( Int *iprm, q2dmblk_t *data );
  };




/** Multi block quadrilateral grids **/

   class cQ2dMBlk: public cQ2d
  {
      protected:

         static const Int        MXNBK=200; /**< Maximum number of blocks                                             **/

         Real                     rlx;          /**< Elliptic smoothing relaxation                                    **/

         Int                     nblk;          /**< Number of blocks                                                 **/
         q2dmblk_t               blk[MXNBK];    /**< Blocks                                                           **/

         Int                     nstc;          /**< Number of stitch and boundary lines                              **/
         q2dmstitch_t             stc[MXNBK];   /**< Stitch and boundary lines                                        **/
         q2dmbrnch_t              bpt;          /**< Branch points                                                    **/

         Int                     *ipe;          /**< Reference triangle for each node in an associated surface 
                                                     triangulation                                                    **/


/** Copy constructor.
   @param                         var                Copy target.
  */
         virtual void      copy( cQ2dMBlk  *var );



/** Update the multi-block grids stitch line using the information in the stitch line data structure var.
   @param                         var                Stitch line data structure.
  */
         virtual void   stitch( q2dstitch_t *var );



/** Perform elliptic smoothing on the part of the grid belonging to block b. The edges of the block are not altered.
   @param                       i             Index of the block to be smoothed.
  */
         virtual void   smoothb( Int i );



/** Perform elliptic smoothing on the part of the grid belonging to block b. The edges of the block are not altered.
   @param                       i             Index of the block to be smoothed.
   @param                       var           Surface triangulation.
  */
         virtual void   smoothb( Int i, tri3d_t *var );

/** Perform elliptic smoothing on stitch lines formed from existing boundaries.
   @param                       i             Index of the block to be smoothed.
  */
         virtual void   smoothl( Int i );

/** Perform elliptic smoothing on the part of the grid belonging to the stitch line s. The end points of the 
    stitch line are not altered.
   @param                       i             Index of the Stitch line to be smoothed
  */
         virtual void   smooths( Int i );


/** Perform local smoothing on the branch points.
    stitch line are not altered.
  */
         virtual void   smootht();


/** Compute source terms for elliptic smoothing associated with sources on boundaries of block i.
   @param                       i             Block index
  */
         virtual void   src( Int i );



      public:
                                cQ2dMBlk();
         virtual               ~cQ2dMBlk();


/** Copy constructor.
   @param                           var             Copy target.
  */
         virtual void               copy( cQ2d  **var );


/** Add a single block to the grid and update information pertaining boundaries and stitch lines.
   @param                           var             Single block to be added.
  */
         virtual void                add( cQ2dBlk *var );


/** Finalize the construction of boundary stitches and branch points. The grid is ready to use after calling finalize.
  */
         virtual void           finalize();


/** Reset the grid.
  */
         virtual void              reset();


/** Perform elliptic smoothing in two space dimensions.
  */
         virtual void smooth( );

/** Perform elliptic smoothing in three-space using the surface triangulation var to perform local mapping.
   @param        var           Surface triangulation. Input.
  */
         virtual void smooth( tri3d_t *var );


/** Perform initial projection of a surface quad grid onto a surface triangulation. The three-dimensional coordinates
    of each node in the quad grid are updated with the coordinates of its projection on the surface underlying the 
    surface grid  and the two-dimensional coordinates become the parametric coordinates on that surface.
   @param        var           Surface triangulation. Input.
  */
         virtual void fit( tri3d_t *var );


  };

/**
  *@}
  */

/**
  *@}
  */

#   endif
