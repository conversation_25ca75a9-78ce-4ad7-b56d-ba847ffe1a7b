#  ifndef _JM28BUTILS_
#  define _JM28BUTILS_

#  include <cprec.h>
#  include <cassert>
#  include <utils/proto.h>
#  include <lin/vect.h>
#  include <cmath>

/**@ingroup q2d
  *@{
  */

/**@defgroup q2dbutil Utilities for boundaries and stitch lines on multiblock quad grids
  *@{
  */

/** Periodic boundaries. **/
   enum periodic_t { not_prd=0, left_prd, right_prd };
   enum smooth_t   { no_smt=0, sld_smt, orth_smt };

/** Initial data structure for boundary groups **/
   struct q2dbnd_t
  {
      Real           tol;                    /**< tolerance for point insertion.                                    **/
      Int              n;                    /**< Node count.                                                       **/
      Real            *x[3];                 /**< Node coordinates.                                                 **/
      Real            *s;                    /**< Node curvilinear coordinates (unused)                             **/
      smooth_t       smt;                    /**< Smoothing flag                                                    **/
      periodic_t     prd;                    /**< Periodic state.                                                   **/
      string         bnm;                    /**< Group identifier                                                  **/
      Real          dsrc;                    /**< Source for attraction                                             **/
  };

/** Initialize a boundary data structure 
   @param               var             Boundary data structure.
 **/
   void           init( q2dbnd_t *var );
/** Destroy a boundary data structure 
   @param               var             Boundary data structure.
 **/
   void        destroy( q2dbnd_t *var );
/** Reset a boundary data structure 
   @param               var             Boundary data structure.
 **/
   void          reset( q2dbnd_t *var );

/** Reset a boundary data structure and adds a straignt boundary with n edges of equal length between the points
    x0 and x1
   @param               n               Edge count.
   @param               x0              Start node.
   @param               x1              End node.
   @param               var             Boundary data structure.
 **/
   void          build( Int n,            Real *x0, Real *x1, q2dbnd_t *var );

/** Reset a boundary data structure and adds ien-ist+1 points from the list x0.
   @param               ist             Start node.
   @param               ien             End node.
   @param               x0              Coordinates.
   @param               var             Boundary data structure.
 **/
   void          build( Int ist, Int ien, Real *x0[],         q2dbnd_t *var );

/** Adds a straignt boundary with n edges of equal length between the points x0 and x1.
   @param               n               Edge count.
   @param               x0              Start node.
   @param               x1              End node.
   @param               var             Boundary data structure.
 **/
   void            add( Int n,            Real *x0, Real *x1, q2dbnd_t *var );

/** Adds ien-ist+1 points from the list x0.
   @param               ist             Start node.
   @param               ien             End node.
   @param               x0              Coordinates.
   @param               var             Boundary data structure.
 **/
   void            add( Int ist, Int ien, Real *x0[],         q2dbnd_t *var );

/** Set the properties for the boundary data structure 
   @param               smt             Smoothing flag
   @param               prd             Periodic flag
   @param               name            Boundary name
   @param               src             Attraction source (<=0 implies no source )
   @param               var             Boundary data structure.
 **/
   void     properties( smooth_t smt, periodic_t prd, string name, Real src, q2dbnd_t *var );


/** Write the boundary data structure var to the file s in a format suitable for gnuplot.
   @param               s               File name.
   @param               var             Boundary data structure.
 **/
   void        gnuplot( string s, q2dbnd_t *var );

/** Stitch data structure **/
   struct q2dstitch_t
  {
      Int                n;           /**< Edge count in the stitch data structure                           **/
      Int                m;           /**< Number of indepdendent chains.                                    **/
      smooth_t         smt;           /**< Smoothing flag inherited from the original block boundary         **/
      Int           *istc0[5];        /**< Left side of the stitch.
                                           istc0[0][i] is the first node of left-edge i,
                                           istc0[1][i] is the second node of left-edge i,
                                           istc0[2][i] is the element attached to left-edge i,
                                           istc0[3][i] is the corresponding side of that element.            
                                           istc0[4][i] is the original boundary group of that edge           **/
      Int           *istc1[5];        /**< Right side of the stitch.
                                           istc1[0][i] is the first node of right-edge i,
                                           istc1[1][i] is the second node of right-edge i,
                                           istc1[2][i] is the element attached to right-edge i,
                                           istc1[3][i] is the corresponding side of that element. 
                                           istc1[4][i] is the original boundary group of that edge.          **/
      Int            *lstc;           /**< End index of each independent chain.                              **/
  };

/** Initialize the stitch data structure var.
   @param                var             Stitch data structure.
 **/
   void           init( q2dstitch_t *var );

/** Destroy the stitch data structure var.
   @param                var             Stitch data structure.
 **/
   void        destroy( q2dstitch_t *var );

/** Sort the right side of the stitch data structure to match the left side. 
   @param                var             Stitch data structure.
 **/
   void           pair( q2dstitch_t *var );

/** Renumber the nodes and elements in the stitch data structure according the the node and element index arrays indx 
    and iedx. 
   @param                indx            Node index.
   @param                iedx            Element index.
   @param                var             Stitch data structure.
 **/
   void        permute( Int *indx, Int *iedx, q2dstitch_t *var );

/** Concatenate the stitch data structure. At the end of concatenate() the stitch data structure is composed of a
    number of chains. These are not necessarily mappable.
   @param                var             Stitch data structure.
 **/
   void    concatenate( q2dstitch_t *var );

/**
  *@}
  */

/**
  *@}
  */
#   endif
