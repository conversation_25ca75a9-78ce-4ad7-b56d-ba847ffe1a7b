#ifndef _JANAF_

#  define _JANAF_

#  include <string>
#  include <iostream>
#  include <cmath>
#  include <fstream>
#  include <sstream>
#  include <limits>

#  include <cprec.h>
#  include <const.h>
#  include <txtfle.h>

#  define   MxNel 10
#  define   MxNsp 100

//   extern "C" void janafrd( Int *, char *, Int *, char *, Int *, Int *, char *[], Int *, Real *,
//                            Real *, Real * );

//   extern "C" void janafrdtest( Int *, char *, Int *, char *, char * );

   class cJanaf
  {
      protected:

// elemental species

         Int        nel;
         string     cel[MxNel];
         Real       wel[MxNel];

// molecular species

         Int        nsp;
         string     csp[MxNsp];

// elemental compositions
         Int        nelsp[MxNsp], melsp[MxNel][MxNsp], ielsp[MxNel][MxNsp];

// species weights, gas constants and polynomials
         Real       wsp[MxNsp], rsp[3][MxNsp], asp[2][7][MxNsp];

         void readth( string );
         
         // Read thermodynamic data for a specific species from JANAF file
         bool janafrd(const string& filepath, const string& species, 
                     Int* numElements, Int* elementLengths, char** elementSymbols, 
                     Int* elementMultiplicities, Real* tempRanges, 
                     Real* highTCoeffs, Real* lowTCoeffs);
         
      public:
         cJanaf();
         virtual ~cJanaf();
         void read( string );
         void getWps( Int *, Real * );
         void getCps( Real, Real * );
         void getHps( Real, Real * );
         void getSps( Real, Real * );

         void eval( Real t, Real *cp, Real *h, Real *s );
         void deval( Real t, Real dt, Real *dcp, Real *dh, Real *ds );
         void show_janaf_gas();

  };

#endif
