#  ifndef _JGAS_
#  define _JGAS_

#  include <utils/proto.h>
#  include <cstdlib>
#  include <iostream>
#  include <gas/janaf.h>
#  include <gas/idgas.h>

   class cJGas: public cIdGas, public cJanaf
  {
      protected:
         Int         nrc;
         Real zrc[5],xsp[5][MxNsp],ssp[5][MxNsp];
         void z2sx( Real, Real *, Real *, Real * );
         void dz2sx( Real z, Real dz, Real wg, Real *ssp0, Real *xsp0, Real *dwg, Real *dssp0, Real *dxsp0 );


      public:

         cJGas();
         virtual ~cJGas();
         void props( string  );
         virtual Int getnsp(){return nsp;};

         virtual Int getnv(){ return 5; };
         virtual Int getnaux(){ return 6+5*nsp; };

         virtual void auxv( Real *, Real * );
         virtual void dauxv( Real *, Real *, Real *, Real * );
         virtual void dauxvz( Real *q, Real *aux, Real dz, Real *daux );

         virtual void prrhs( Real *q, Real *aux, Real *w, Real f, Real *rhs );
         virtual void prdrhsq( Real *q, Real *aux, Real *w, Real *dq, Real *daux, Real f, Real *drhs );
         virtual void prdrhsw( Real *q, Real *aux, Real *w, Real *dw, Real f, Real *drhs );
         virtual void dpr( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux );

         virtual void dhcrhs( Real *q, Real *aux, Real *w, Real f, Real *rhs );
         virtual void dhcdrhsq( Real *q, Real *aux, Real *w, Real *dq, Real *daux, Real f, Real *drhs );
         virtual void dhcdrhsw( Real *q, Real *aux, Real *w, Real *dw, Real f, Real *drhs );
         virtual void ddhc( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux );

         virtual void dherhs( Real *q, Real *aux, Real *w, Real f, Real *rhs );
         virtual void dhedrhsq( Real *q, Real *aux, Real *w, Real *dq, Real *daux, Real f, Real *drhs );
         virtual void dhedrhsw( Real *q, Real *aux, Real *w, Real *dw, Real f, Real *drhs );
         virtual void ddhe( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux );

         virtual void mixrhs( Real *q, Real *aux, Real *w, Real f, Real *rhs );
         virtual void mixdrhsq( Real *q, Real *aux, Real *w, Real *dq, Real *daux, Real f, Real *drhs );
         virtual void mixdrhsw( Real *q, Real *aux, Real *w, Real *dw, Real f, Real *drhs );
         virtual void dmix( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux );

         virtual void marhs( Real *q, Real *aux, Real *w, Real f, Real *rhs );
         virtual void madrhsq( Real *q, Real *aux, Real *w, Real *dq, Real *daux, Real f, Real *drhs );
         virtual void madrhsw( Real *q, Real *aux, Real *w, Real *dw, Real f, Real *drhs );
         virtual void dma( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux );

         virtual void verhs( Real *q, Real *aux, Real *w, Real f, Real *rhs );
         virtual void vedrhsq( Real *q, Real *aux, Real *w, Real *dq, Real *daux, Real f, Real *drhs );
         virtual void vedrhsw( Real *q, Real *aux, Real *w, Real *dw, Real f, Real *drhs );
         virtual void dve( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux );

         virtual void mfarhs( Real *q, Real *aux, Real *w, Real f, Real *rhs );
         virtual void mfadrhsq( Real *q, Real *aux, Real *w, Real *dq, Real *daux, Real f, Real *drhs );
         virtual void mfadrhsw( Real *q, Real *aux, Real *w, Real *dw, Real f, Real *drhs );
         virtual void dmfa( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux );

         virtual void mfrhs( Real *q, Real *aux, Real *w, Real f, Real *rhs );
         virtual void mfdrhsq( Real *q, Real *aux, Real *w, Real *dq, Real *daux, Real f, Real *drhs );
         virtual void mfdrhsw( Real *q, Real *aux, Real *w, Real *dw, Real f, Real *drhs );
         virtual void dmf( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux );

         virtual void shrhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs ){cout<<"forbidden\n";exit(0);};
         virtual void shdrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs ){cout<<"forbidden\n";exit(0);};
         virtual void shdrhsw( Real  *q, Real  *aux, Real  *w, Real *dw, Real fct, Real *drhs ){cout<<"forbidden\n";exit(0);};
         virtual void dsh( Real  *q, Real  *aux, Real  *w, Real *dq, Real *drhs, Real *daux ){cout<<"forbidden\n";exit(0);};

  };
   

#  endif
