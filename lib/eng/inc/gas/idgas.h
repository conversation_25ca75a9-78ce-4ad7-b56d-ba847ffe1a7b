#  ifndef _IDGAS_
#  define _IDGAS_

#  include <cprec.h>
#  include <const.h>
#  include <cmath>

#  define   MXVARS 1000

   class cIdGas
  {
      private:
         Real            rg,cp,cv,gam;
      protected:
         Real            tref,pref;
      public:
         cIdGas();
         virtual ~cIdGas();

         virtual Int getnv(){ return 4; };
         virtual Int getnaux(){ return 4; };

         virtual void auxv( Real *q, Real *aux );
         virtual void dauxv( Real *q, Real *aux, Real *dq, Real *daux );

         virtual void prrhs( Real *q, Real *aux, Real *w, Real f, Real *rhs );
         virtual void prdrhsq( Real *q, Real *aux, Real *w, Real *dq, Real *daux, Real f, Real *drhs );
         virtual void prdrhsw( Real *q, Real *aux, Real *w, Real *dw, Real f, Real *drhs );
         virtual void dpr( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux );

         virtual void dhcrhs( Real *q, Real *aux, Real *w, Real f, Real *rhs );
         virtual void dhcdrhsq( Real *q, Real *aux, Real *w, Real *dq, Real *daux, Real f, Real *drhs );
         virtual void dhcdrhsw( Real *q, Real *aux, Real *w, Real *dw, Real f, Real *drhs );
         virtual void ddhc( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux );

         virtual void dherhs( Real *q, Real *aux, Real *w, Real f, Real *rhs );
         virtual void dhedrhsq( Real *q, Real *aux, Real *w, Real *dq, Real *daux, Real f, Real *drhs );
         virtual void dhedrhsw( Real *q, Real *aux, Real *w, Real *dw, Real f, Real *drhs );
         virtual void ddhe( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux );

         virtual void mfrhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs );
         virtual void mfdrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs );
         virtual void mfdrhsw( Real  *q, Real  *aux, Real  *w, Real *dw, Real fct, Real *drhs );
         virtual void dmf( Real  *q, Real  *aux, Real  *w, Real *drhs, Real *dq, Real *daux );

         virtual void mfarhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs );
         virtual void mfadrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs );
         virtual void mfadrhsw( Real  *q, Real  *aux, Real  *w, Real *dw, Real fct, Real *drhs );
         virtual void dmfa( Real  *q, Real  *aux, Real  *w, Real *drhs, Real *dq, Real *daux );

         virtual void mfbrhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs );
         virtual void mfbdrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs );
         virtual void mfbdrhsw( Real  *q, Real  *aux, Real  *w, Real *dw, Real fct, Real *drhs );
         virtual void dmfb( Real  *q, Real  *aux, Real  *w, Real *dq, Real *drhs, Real *daux );


         virtual void verhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs );
         virtual void vedrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs );
         virtual void vedrhsw( Real  *q, Real  *aux, Real  *w, Real *dw, Real fct, Real *drhs );
         virtual void dve( Real  *q, Real  *aux, Real  *w, Real *drhs, Real *dq, Real *daux );

         virtual void marhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs );
         virtual void madrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs );
         virtual void madrhsw( Real  *q, Real  *aux, Real  *w, Real *dw, Real fct, Real *drhs );
         virtual void dma( Real  *q, Real  *aux, Real  *w, Real *drhs, Real *dq, Real *daux );

         virtual void shrhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs );
         virtual void shdrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs );
         virtual void shdrhsw( Real  *q, Real  *aux, Real  *w, Real *dw, Real fct, Real *drhs );
         virtual void dsh( Real  *q, Real  *aux, Real  *w, Real *drhs, Real *dq, Real *daux );

// temporary
         virtual void vorhs( Int, Int, Real *q[], Real *aux[], Real *w[], Real fct, Real *rhs[] );
         virtual void vodrhsq( Int, Int, Real *q[], Real *aux[], Real *w[], Real *dq[], Real *daux[], Real fct, Real *drhs[] );
         virtual void vodrhsw( Int, Int, Real *q[], Real *aux[], Real *w[], Real *dw[], Real fct, Real *drhs[] );
         virtual void dvo( Int, Int, Real *q[], Real *aux[], Real *w[], Real *drhs[], Real *dq[], Real *daux[] );

// Artyom's thingies
         virtual void           sta2to( Real *qi, Real *auxi, Real *q0, Real *aux0 );
         virtual void           achnge( Real *qi, Real *auxi, Real *qo, Real *auxo, Real A1, Real A2 );
         virtual void           mbeta( Real *qi, Real *auxi, Real *qo, Real *auxo, Real beta, Real *wi );
         virtual void           irpm( Real *qi, Real *auxi, Real *qo, Real *auxo, Real u0, Real beta, Real V1 );
         virtual void           chdir0b( Real *qi, Real *auxi, Real *qo, Real *auxo, Real *wi, Real *wo, Real beta );
  };

   void pm( cIdGas *gas, Real *q, Real *aux, Int n, Real da );
#  endif
