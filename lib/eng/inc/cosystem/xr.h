#  ifndef _COSYSTEM_XR_
#  define _COSYSTEM_XR_

#  include <cosystem/cosystem.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Feb 16 18:16:54 GMT 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         abstract coordinate system

/*! abstract coordinate systems */

   class cXrCosystem:public cCosystem
  {
      protected:
      public:
	//! Default constructor
         cXrCosystem(){};
         virtual ~cXrCosystem(){};
         virtual cosystem_e gettype(){ return cosystem_xr; };

	/*!
	 * \param Nx an Int
	 * \return no return type
	 */
         virtual void validate( Int );

	/*!
	 * Not defined yet
	 */
         virtual void coffset( Int , Real, Real * ){};

	/*!
	 * Not defined yet
	 */
         virtual void voffset( Int , Int , Int , Int *, Real , Real *[] ){};

         virtual void voffset( Int , Int , Real, Int *, Real *[], Int *, Real *[] ){};

	/*!
	 * Not defined yet
	 */	
         virtual void voffset( Int , Int , Int , Real , Real *[] ){};

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param xp[] an array of pointers to Real
	 * \param xdp[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void frame( Int , Int , Real , Real *[], Real *[] );
         virtual void frame( Int , Int , Real , cAu3xView<Real>& , cAu3xView<Real>& );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param q[] an array of pointers to Real
	 * \param a[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void accel( Int , Int , Real , Real *[], Real *[], Real *[] );
         virtual void daccel( Int , Int , Real , Real *[], Real *[], Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void bcoor( Int , Int , Real *[], Real *[] );
         virtual void bcoor( Int ips, Int ipe, cAu3xView<Real>& x, cAu3xView<Real>& bx );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param ipx[] an array of pointers to Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void bcoor( Int , Int , Int *[], Real *[], Real *[] );
         virtual void cvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void ccoor( Int , Int , Int *[], Real *[], Real *[] );

        //! coordinate-system specific vector transformation for averaging
	/*!
	 * \param ips starting index for array subrange
	 * \param ipe last index for array subrange
	 * \param ipq[] subset to process
	 * \param x[] coordinates
	 * \param q[]  vector
	 * \param bq[]  transformed vector
	 * \return no return type
	 */
         virtual void bvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void bvel( Int ibs, Int ibe, cAu3xView<Int>& ibq, cAu3xView<Real>& xb, cAu3xView<Real>& q, cAu3xView<Real>& qwrk );

         virtual void zvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void zvel( Int iqs, Int iqe, Int *[], cAu3xView<Real>& x , cAu3xView<Real>& bq, cAu3xView<Real> &q );
         virtual void zvel( Int iqs, Int iqe, cAu3xView<Real>& x , cAu3xView<Real>& bq, cAu3xView<Real> &q );
         virtual void yvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );

         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[], Real *[], Real *[] ){cout << "forbidden\n"; std::exit(0); };
         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[], Real *[] );
         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[] );
  };

#  endif
