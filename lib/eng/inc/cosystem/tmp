#  ifndef _COSYSTEM_
#  define _COSYSTEM_

#  include <math.h>
#  include <string>
#  include <sstream>
#  include <iostream>
#  include <fstream>

#  include <cprec.h>
#  include <const.h>
#  include <txtfle.h>
#  include <utils.h>
#  include <blade.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Feb 16 18:16:54 GMT 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         abstract coordinate system

/*! abstract coordinate systems */

   enum cCosystemTypes{ badCosystem=-1, annCosystem, cartCosystem, xrCosystem, mthetaCosystem, cartCosystem2 };

   class cCosystem *newcosystem( Int );

   class cCosystem
  {
      protected:
         Int          nx;
         Int          nvel;
         bool         valid;
      public:
	//! Default constructor
         cCosystem();
         virtual cCosystemTypes gettype(){ return badCosystem; };

         virtual void read( ifstream * );
         virtual void write( ofstream * );

        //! Return the number of velocity variables required in this coordinate system
	/*!
	 * \return return an Int
	 */
         virtual Int  getNvel(){ return nvel; };

        //! Return the number of coordinate variables required in this coordinate system
	/*!
	 * \return return an Int
	 */
         virtual Int  getNx(){ return nx; };

        //! obtain coordinate system information from a text file
	/*!
	 * \param info a pointer to cTxtfile
	 * \return no return type
	 */
         //virtual void getInfo( cTxtfle * );

        /*! validate the coordinate system. validate() compares the number of coordinates required by
            this coordinate system with the argument Nx. Usually Nx is the number of coordinates available
            in the grid geometry.*/
	/*!
	 * \param Nx an Int
	 * \return no return type
	 */
         virtual void validate( Int ){ cout << "CANNOT USE SIMPLE COSYSTEM\n"; std::exit(0);};

        //! check that the coordinate system is validated
	/*!
	 * \return true if the coordinate system has been validated against the grid information
	 */
         virtual bool validated(){return valid;};

        //!Shift coordinates by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param np an Int
	 * \param x a pointer to Real
	 * \return no return type
	 */
         virtual void coffset( Int , Real, Real * ){};

        //!Shift vectors by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv  first periodic variable
	 * \param ipp list of entries to offset
	 * \param f   sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int , Int , Int , Int *, Real , Real *[] ){};

        //!Shift vectors by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv  first periodic variable
	 * \param ipp list of entries to offset
	 * \param f   sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int , Int , Real, Int *, Real *[], Int *, Real *[] ){};
         virtual void coffset( Int , Int , Real, Int *, Real *[], Int *, Real *[] ){};

        //!Shift a vector by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv first periodic variable
	 * \param f sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int , Int , Int , Real , Real *[] ){};

	/*!
	 * \param iss an Int
	 * \param ise an Int
	 * \param npc an Int
	 * \param pisp an array of pointers to Int
	 * \param pxp an array of pointers to Real
	 * \param pQ[] a Real array
	 * \param pwrk[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void  xatl( Int , Int , Int , Int *[], Real *[], Real [], Real *[] );

	/*!
	 * \param iss an Int
	 * \param ise an Int
	 * \param npc an Int
	 * \param pisp[] an array of pointers to Int
	 * \param pxp[] an array of pointers to Real
	 * \param pdQdy[] an array of pointers to Real
	 * \param pwrk[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[] );

	/*!
	 * \param iss an Int
	 * \param ise an Int
	 * \param npc an Int
	 * \param pisp[] an array of pointers to Int
	 * \param pxp[] an array of pointers to Real
	 * \param pdQdy[] an array of pointers to Real
	 * \param pwrk0[] an array of pointers to Real
	 * \param pwrk1[] an array of pointers to Real
	 * \return no return type
	*/	
         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[], Real *[] );


	/*!
	 * \param iss an Int
	 * \param ise an Int
	 * \param npc an Int
	 * \param pisp[] an array of pointers to Int
	 * \param pxp[] an array of pointers to Real
	 * \param pdQdy[] an array of pointers to Real
	 * \param pwrk0[] an array of pointers to Real
	 * \param pwrk1[] an array of pointers to Real
	 * \param pwrk2[] an array of pointers to Real
	 * \return no return type
	*/	
         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[], Real *[], Real *[] );


	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param xp[] an array of pointers to Real
	 * \param xdp[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void frame( Int , Int , Real , Real *[], Real *[] ){};

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param q[] an array of pointers to Real
	 * \param a[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void accel( Int , Int , Real , Real *[], Real *[], Real *[] ){};
         virtual void daccel( Int , Int , Real , Real *[], Real *[], Real *[], Real *[] ){};

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void bcoor( Int , Int , Real *[], Real *[] );// cout << "THIS IS THE METHOD I WANT\n";std::exit(0); };
         virtual void ccoor( Int , Int , Int *[], Real *[], Real *[] ){ cout << "WHY DOES IT GET HERE?????\n";};

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param ipx[] an array of pointers to Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void bcoor( Int , Int , Int *[], Real *[], Real *[] ){};

        //! coordinate-system specific vector transformation for averaging
	/*!
	 * \param ips starting index for array subrange
	 * \param ipe last index for array subrange
	 * \param ipq[] subset to process
	 * \param x[] coordinates
	 * \param q[]  vector
	 * \param bq[]  transformed vector
	 * \return no return type
	 */
         virtual void bvel( Int , Int , Int *[], Real *[], Real *[], Real *[] ){};
         virtual void zvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void yvel( Int , Int , Int *[], Real *[], Real *[], Real *[] ){};
         virtual void cvel( Int , Int , Int *[], Real *[], Real *[], Real *[] ){};
  };

   class cCartCosystem: public cCosystem
  {
      protected:
         Real         ptch;
      public:
	//! Default constructor
         cCartCosystem();
         virtual cCosystemTypes gettype(){ return cartCosystem; };
         virtual void pitch( Real p ){ ptch= p;};

         virtual void read( ifstream * );
         virtual void write( ofstream * );

        /*! validate the coordinate system. validate() compares the number of coordinates required by
            this coordinate system with the argument Nx. Usually Nx is the number of coordinates available
            in the grid geometry.*/
	/*!
	 * \param Nx an Int
	 * \return no return type
	 */
         virtual void validate( Int );

        //!Shift coordinates by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param np an Int
	 * \param x a pointer to Real
	 * \return no return type
	 */
         virtual void coffset( Int , Real, Real * );

        //!Shift vectors by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv  first periodic variable
	 * \param ipp list of entries to offset
	 * \param f   sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int , Int , Int , Int *, Real , Real *[] );

        //!Shift vectors by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv  first periodic variable
	 * \param ipp list of entries to offset
	 * \param f   sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int , Int , Real, Int *, Real *[], Int *, Real *[] );
         virtual void coffset( Int , Int , Real, Int *, Real *[], Int *, Real *[] );

        //!Shift a vector by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv first periodic variable
	 * \param f sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int , Int , Int , Real , Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param xp[] an array of pointers to Real
	 * \param xdp[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void frame( Int , Int , Real , Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param q[] an array of pointers to Real
	 * \param a[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void accel( Int , Int , Real , Real *[], Real *[], Real *[] );
         virtual void daccel( Int , Int , Real , Real *[], Real *[], Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void bcoor( Int , Int , Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param ipx[] an array of pointers to Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void bcoor( Int , Int , Int *[], Real *[], Real *[] );

        //! coordinate-system specific vector transformation for averaging
	/*!
	 * \param ips starting index for array subrange
	 * \param ipe last index for array subrange
	 * \param ipq[] subset to process
	 * \param x[] coordinates
	 * \param q[]  vector
	 * \param bq[]  transformed vector
	 * \return no return type
	 */
         virtual void bvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void cvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void zvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void yvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
  };

   class cAnnCosystem: public cCosystem
  {
      protected:
         Real         ptch;
         Int          gsct,asct;
      public:
	//! Default constructor
         cAnnCosystem();
         virtual cCosystemTypes gettype(){ return annCosystem; };

         virtual void read( ifstream * );
         virtual void write( ofstream * );
         virtual void sectors( Int, Int );

        /*! validate the coordinate system. validate() compares the number of coordinates required by
            this coordinate system with the argument Nx. Usually Nx is the number of coordinates available
            in the grid geometry.*/
	/*!
	 * \param Nx an Int
	 * \return no return type
	 */
         virtual void validate( Int );

        //!Shift coordinates by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param np an Int
	 * \param x a pointer to Real
	 * \return no return type
	 */
         virtual void coffset( Int , Real, Real * );

        //!Shift vectors by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv  first periodic variable
	 * \param ipp list of entries to offset
	 * \param f   sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int , Int , Int , Int *, Real , Real *[] );

        //!Shift vectors by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv  first periodic variable
	 * \param ipp list of entries to offset
	 * \param f   sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int , Int , Real, Int *, Real *[], Int *, Real *[] );
         virtual void coffset( Int , Int , Real, Int *, Real *[], Int *, Real *[] );

        //!Shift a vector by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv first periodic variable
	 * \param f sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int , Int , Int , Real , Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param xp[] an array of pointers to Real
	 * \param xdp[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void frame( Int , Int , Real , Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param q[] an array of pointers to Real
	 * \param a[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void accel( Int , Int , Real , Real *[], Real *[], Real *[] );
         virtual void daccel( Int , Int , Real , Real *[], Real *[], Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void bcoor( Int , Int , Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param ipx[] an array of pointers to Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void bcoor( Int , Int , Int *[], Real *[], Real *[] );
         virtual void ccoor( Int , Int , Int *[], Real *[], Real *[] );

        //! coordinate-system specific vector transformation for averaging
	/*!
	 * \param ips starting index for array subrange
	 * \param ipe last index for array subrange
	 * \param ipq[] subset to process
	 * \param x[] coordinates
	 * \param q[]  vector
	 * \param bq[]  transformed vector
	 * \return no return type
	 */
         virtual void bvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void cvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void zvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void yvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
  };

   class cXrCosystem:public cCosystem
  {
      protected:
      public:
	//! Default constructor
         cXrCosystem();
         virtual cCosystemTypes gettype(){ return xrCosystem; };

	/*!
	 * \param Nx an Int
	 * \return no return type
	 */
         virtual void validate( Int );

	/*!
	 * Not defined yet
	 */
         virtual void coffset( Int , Real, Real * ){};

	/*!
	 * Not defined yet
	 */
         virtual void voffset( Int , Int , Int , Int *, Real , Real *[] ){};

         virtual void voffset( Int , Int , Real, Int *, Real *[], Int *, Real *[] ){};

	/*!
	 * Not defined yet
	 */	
         virtual void voffset( Int , Int , Int , Real , Real *[] ){};

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param xp[] an array of pointers to Real
	 * \param xdp[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void frame( Int , Int , Real , Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param q[] an array of pointers to Real
	 * \param a[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void accel( Int , Int , Real , Real *[], Real *[], Real *[] );
         virtual void daccel( Int , Int , Real , Real *[], Real *[], Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void bcoor( Int , Int , Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param ipx[] an array of pointers to Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void bcoor( Int , Int , Int *[], Real *[], Real *[] );
         virtual void cvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void ccoor( Int , Int , Int *[], Real *[], Real *[] );

        //! coordinate-system specific vector transformation for averaging
	/*!
	 * \param ips starting index for array subrange
	 * \param ipe last index for array subrange
	 * \param ipq[] subset to process
	 * \param x[] coordinates
	 * \param q[]  vector
	 * \param bq[]  transformed vector
	 * \return no return type
	 */
         virtual void bvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void zvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void yvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );

         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[], Real *[], Real *[] ){cout << "forbidden\n"; std::exit(0); };
         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[], Real *[] );
         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[] );
  };

   class cMthetaCosystem:public cCosystem
  {
      protected:

         Real            ptch;
         cSpline        *ssf;

      public:
	//! Default constructor
         cMthetaCosystem();

	//! builds an m'-theta coordinate system from a section definition
         virtual void section( cSection * );
         virtual cCosystemTypes gettype(){ return mthetaCosystem; };

	/*!
	 * \param Nx an Int
	 * \return no return type
	 */	
         virtual void validate( Int );

	/*!
	 * \param np an Int
	 * \param x a pointer to Real
	 * \return no return type
	 */
         virtual void coffset( Int , Real, Real * );

	/*!
	 * Not defined yet
	 */
         virtual void voffset( Int , Int , Int , Int *, Real , Real *[] ){};
         virtual void voffset( Int , Int , Real, Int *, Real *[], Int *, Real *[] ){};

	/*!
	 * Not defined yet
	 */	 
         virtual void voffset( Int , Int , Int , Real , Real *[] ){};

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param q[] an array of pointers to Real
	 * \param a[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void accel( Int , Int , Real , Real *[], Real *[], Real *[] );
         virtual void daccel( Int , Int , Real , Real *[], Real *[], Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param xp[] an array of pointers to Real
	 * \param xdp[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void frame( Int , Int , Real , Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void bcoor( Int , Int , Real *[], Real *[] );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param ipx[] a Real
	 * \param x[] an array of pointers to Int
	 * \param bx[]  an array of pointers to Real
	 * \return no return type
	 */
         virtual void bcoor( Int , Int , Int *[], Real *[], Real *[] );

        //! coordinate-system specific vector transformation for averaging
	/*!
	 * \param ips starting index for array subrange
	 * \param ipe last index for array subrange
	 * \param ipq[] subset to process
	 * \param x[] coordinates
	 * \param q[]  vector
	 * \param bq[]  transformed vector
	 * \return no return type
	 */
         virtual void bvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );

         virtual void dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[] );
         virtual void dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[], Real *[] );
  };

   class cCartCosystem2: public cCartCosystem
  {
      public:
         virtual cCosystemTypes gettype(){ return cartCosystem2; };
         virtual void validate( Int );
  };


#endif
