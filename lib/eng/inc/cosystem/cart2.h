#  ifndef _COSYSTEM_CART2_
#  define _COSYSTEM_CART2_

#  include <cosystem/cart.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Feb 16 18:16:54 GMT 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         abstract coordinate system


   class cCartCosystem2: public cCartCosystem
  {
      public:
         virtual cosystem_e gettype(){ return cosystem_cart2; };
         virtual void validate( Int );
         virtual void voffset_z( Int , Int , Real, Real, Int *, Real *[], Real *[], Int *, Real *[], Real *[] );

  };


#endif
