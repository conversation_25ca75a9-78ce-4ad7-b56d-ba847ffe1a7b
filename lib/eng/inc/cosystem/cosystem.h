#  ifndef _COSYSTEM_
#  define _COSYSTEM_

#  include <complex>
#  include <cprec.h>
#  include <const.h>
#  include <cmath>
#  include <lin/vect.h>
#  include <utils/proto.h>
#  include <pickle/proto.h>
#  include <tab/proto.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Feb 16 18:16:54 GMT 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         abstract coordinate system

/**@ingroup engineering
  *@{
 **/

/**@defgroup coordinates Coordinate Systems
  *@{
 **/

/*! abstract coordinate systems */

   enum cosystem_e{ cosystem_bad=-1, cosystem_ann, cosystem_cart, cosystem_xr, cosystem_mtheta, cosystem_cart2,
                    cosystem_num };
   
/** Strings identifying coordinate systems in input and output data*/
   static const string cosystem_s[5]={"annular","cartesian","xr","mtheta","cart2"};

/** Function to construct coordinate systems based on integer switch*/
   class cCosystem *newcosystem( Int );

   class cCosystem: public cPickle, public cTabSrc
  {
      protected:
         Int          nx;
         Int          nvel,nv;
         bool         valid;

      public:
	//! Default constructor
         cCosystem();
         virtual ~cCosystem();
         virtual cosystem_e gettype(){ return cosystem_bad; };
         virtual bool periodic(){ return false; };

         virtual void pickle( size_t *len, pickle_t *buf );
         virtual void unpickle( size_t *len, pickle_t buf );

         virtual void setnx(Int var){ nx = var; };
         virtual void setnvel(Int var){ nvel = var; };
         virtual void setnv(Int var){ nv = var; };
         virtual Int getnx(){ return nx; };
         virtual Int getnvel(){ return nvel; };

         virtual void get( cTabData *tab );
         virtual void set( cTabData *tab );

        /*! validate the coordinate system. validate() compares the number of coordinates required by
            this coordinate system with the argument Nx. Usually Nx is the number of coordinates available
            in the grid geometry.*/
	/*!
	 * \param Nx an Int
	 * \return no return type
	 */
         virtual void validate( Int ){ cout << "CANNOT USE SIMPLE COSYSTEM\n"; std::exit(0);};

        //! check that the coordinate system is validated
	/*!
	 * \return true if the coordinate system has been validated against the grid information
	 */
         virtual bool validated(){return valid;};

         virtual void coffset( Real f, Real *x ){};


        //!Shift coordinates by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param np an Int
	 * \param x a pointer to Real
	 * \return no return type
	 */
         virtual void coffset( Int , Real, Real * ){};

        //!Shift vectors by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv  first periodic variable
	 * \param ipp list of entries to offset
	 * \param f   sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int ips, Int ipe, Int iv, Int *ipp, Real f, Real *v[] ){};
         virtual void voffset(Real f, Real *var ){};


        //!Shift vectors by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv  first periodic variable
	 * \param ipp list of entries to offset
	 * \param f   sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int , Int , Real, Int *, Real *[], Int *, Real *[] ){};
         virtual void voffset( Int is, Int ie, Real f, Int *isrc, Real *sxsrc, Int *idst, Real *sxdst, Int nqsrc, Int nqdst ){};
         virtual void voffsetgpu( Int is, Int ie, Real f, Int *isrc, Real *sxsrc, Int *idst, Real *sxdst, Int nqsrc, Int nqdst ){};
         virtual void voffset( Int is, Int ie, Real f, cAu3xView<Int>& isrc, cAu3xView<Real>& xsrc, cAu3xView<Int>& idst, cAu3xView<Real>& xdst ){};


         virtual void coffset( Int , Int , Real, Int *, Real *[], Int *, Real *[] ){};
         virtual void coffset( Int is, Int ie, Real f, Int *isrc, Real *sxsrc, Int *idst, Real *sxdst, Int nqsrc, Int nqdst ){};
         virtual void coffsetgpu( Int is, Int ie, Real f, Int *isrc, Real *sxsrc, Int *idst, Real *sxdst, Int nqsrc, Int nqdst ){};
         virtual void coffset( Int is, Int ie, Real f, cAu3xView<Int>& isrc, cAu3xView<Real>& xsrc, cAu3xView<Int>& idst, cAu3xView<Real>& xdst ){};

         virtual void voffset_z( Int , Int , Real, Real, Int *, Real *[], Real *[], Int *, Real *[], Real *[] ){};

        //!Shift a vector by the coordinate system periodicity pitch
        /*!voffset shifts a vector by the periodic pitch of the coordinate system*/
	/*!
	 * \param ips starting index
	 * \param ipe ending index
	 * \param iv first periodic variable
	 * \param f sign flag
	 * \param v[] vector to offset
	 * \return no return type
	 */
         virtual void voffset( Int ips, Int ipe, Int iv, Real f, Real *v[] ){};

        /** toffset shifts a vector by a given offset.
            This can be used, as an example, to update the position
            of points on sliding planes. The exact nature of the transformation
            is determined by the coordinate system.
           @brief Shift a vector by a given offset.
	   @param ips starting index
	   @param ipe ending index
	   @param iv  variable corresponding to first vector component.
	   @param f   shift
	   @param v[] vector to offset
	 */
         virtual void toffset( Int ips, Int ipe, Int iv, Real f, Real *v[] ){};
         virtual void toffset( Int ips, Int ipe, Int iv, Real f, Real *sv, Int n ){};
         virtual void toffsetgpu( Int ips, Int ipe, Int iv, Real f, Real *sv, Int n, Int nv ){};
         virtual void toffset( Int ips, Int ipe, Int iv, Real f, cAu3xView<Real>& v ){};

        #pragma acc routine seq
         virtual void toffset( Int ip, Int iv, Real f, Real *sv, Int n ){};


	/*!
	 * \param iss an Int
	 * \param ise an Int
	 * \param npc an Int
	 * \param pisp an array of pointers to Int
	 * \param pxp an array of pointers to Real
	 * \param pQ[] a Real array
	 * \param pwrk[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void  xatl( Int , Int , Int , Int *[], Real *[], Real [], Real *[] );
         virtual void  xatl( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pwrk );
         virtual void  xatl_gpu( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pwrk );

	/*!
	 * \param iss an Int
	 * \param ise an Int
	 * \param npc an Int
	 * \param pisp[] an array of pointers to Int
	 * \param pxp[] an array of pointers to Real
	 * \param pdQdy[] an array of pointers to Real
	 * \param pwrk[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[] );
         virtual void  dxdl( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pdQdy, cAu3xView<Real>& pwrk );

	/*!
	 * \param iss an Int
	 * \param ise an Int
	 * \param npc an Int
	 * \param pisp[] an array of pointers to Int
	 * \param pxp[] an array of pointers to Real
	 * \param pdQdy[] an array of pointers to Real
	 * \param pwrk0[] an array of pointers to Real
	 * \param pwrk1[] an array of pointers to Real
	 * \return no return type
	*/	
         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[], Real *[] );
         virtual void  dxdl( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pdQdy, cAu3xView<Real>& pwrk0, cAu3xView<Real>& pwrk1 );
         virtual void  dxdl_gpu( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pdQdy, cAu3xView<Real>& pwrk0, cAu3xView<Real>& pwrk1 );

	/*!
	 * \param iss an Int
	 * \param ise an Int
	 * \param npc an Int
	 * \param pisp[] an array of pointers to Int
	 * \param pxp[] an array of pointers to Real
	 * \param pdQdy[] an array of pointers to Real
	 * \param pwrk0[] an array of pointers to Real
	 * \param pwrk1[] an array of pointers to Real
	 * \param pwrk2[] an array of pointers to Real
	 * \return no return type
	*/	
         virtual void  dxdl( Int , Int , Int , Int *[], Real *[], Real *, Real *[], Real *[], Real *[], Real *[] );
         virtual void  dxdl( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pdQdy, cAu3xView<Real>& pwrk0, cAu3xView<Real>& pwrk1, cAu3xView<Real>& pwrk2 );
         virtual void  dxdl_gpu( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pdQdy, cAu3xView<Real>& pwrk0, cAu3xView<Real>& pwrk1, cAu3xView<Real>& pwrk2 );

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param xp[] an array of pointers to Real
	 * \param xdp[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void frame( Int , Int , Real , Real *[], Real *[] ){};
         virtual void frame( Int , Int , Real , cAu3xView<Real>& , cAu3xView<Real>& ){};

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param omega a Real
	 * \param q[] an array of pointers to Real
	 * \param a[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void accel( Int , Int , Real , Real *[], Real *[], Real *[] ){};
         virtual void accel( Int ips, Int ipe, Real omega, Real *sxq, Real *sq, Real *sa, Int nq ){};


         virtual void daccel( Int , Int , Real , Real *[], Real *[], Real *[], Real *[] ){};
         virtual void daccel( Int ips, Int ipe, Real omega, Real *sxq, Real *sq, Real *sdq, Real *sa, Int nq ){};

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void bcoor( Int , Int , Real *[], Real *[] );// cout << "THIS IS THE METHOD I WANT\n";std::exit(0); };
         virtual void bcoor( Int ips, Int ipe, cAu3xView<Real>& x, cAu3xView<Real>& bx );// cout << "THIS IS THE METHOD I WANT\n";std::exit(0); };
         virtual void bcoor( Int , Int , Int *[], cAu3xView<Real>& x, cAu3xView<Real>& bx ){};
         virtual void ccoor( Int , Int , Int *[], Real *[], Real *[] ){ cout << "WHY DOES IT GET HERE?????\n";};

	/*!
	 * \param ips an Int
	 * \param ipe an Int
	 * \param ipx[] an array of pointers to Int
	 * \param x[] an array of pointers to Real
	 * \param bx[] an array of pointers to Real
	 * \return no return type
	*/
         virtual void bcoor( Int , Int , Int *[], Real *[], Real *[] ){};

        //! coordinate-system specific vector transformation for averaging
	/*!
	 * \param ips starting index for array subrange
	 * \param ipe last index for array subrange
	 * \param ipq[] subset to process
	 * \param x[] coordinates
	 * \param q[]  vector
	 * \param bq[]  transformed vector
	 * \return no return type
	 */
         virtual void bvel( Int , Int , Int *[], Real *[], Real *[], Real *[] ){};
         virtual void bvel( Int ibs, Int ibe, cAu3xView<Int>& ibq, cAu3xView<Real>& xb, cAu3xView<Real>& q, cAu3xView<Real>& qwrk ){};

         virtual void zvel( Int , Int , Int *[], Real *[], Real *[], Real *[] );
         virtual void zvel( Int iqs, Int iqe, Int *[], cAu3xView<Real>& x , cAu3xView<Real>& bq, cAu3xView<Real> &q );
         virtual void zvel( Int iqs, Int iqe, cAu3xView<Real>& x , cAu3xView<Real>& bq, cAu3xView<Real> &q );
         virtual void yvel( Int , Int , Int *[], Real *[], Real *[], Real *[] ){};
         virtual void cvel( Int , Int , Int *[], Real *[], Real *[], Real *[] ){};
         virtual void bvel( Int ib, Int *ibq[], Real *xb[], Real *q[], Real *bq ) {};

         virtual void jacoffset(Int ics, Int ice, Real f, Int nv, cJacBlk * jacblk) {};

  };

/**
  *@}
 **/
/**
  *@}
 **/
#endif
