#  ifndef _Q3D
#  define _Q3D_

#  define Q3DMXNP   100000
#  define Q3DMXNE   100000
#  define Q3DMXNB     1000
#  define Q3DMXNG     1000
#  define Q3DMXNN       10
#  define Q3DMXNBK     100
#  define Q3DMXNBR     100

#  include <cstdarg>
#  include <cprec.h>
#  include <cosystem/cosystems.h>
#  include <utils/proto.h>
#  include <lin/vect.h>
#  include <sort/proto.h>
#  include <cassert>
#  include <iostream>
#  include <fstream>

/**@ingroup engineering
  *@{
 **/

/**@defgroup q3d Multiblock hexahedral grids
  *@{
 **/

/** Boundary periodicity **/
   enum q3dprd_t{ not_periodic= -1, left_periodic, right_periodic };

/** Boundary smoothing **/
   enum q3dsmt_t{ not_smooth= -1, yes_smooth };
  

/** Single blocks inside multiblock hexahedral grids **/
   struct q3dblk_t
  {
      Int           ni;                   /**< I-extent of the block                                                  **/
      Int           nj;                   /**< J-extent of the block                                                  **/
      Int           nk;                   /**< K-extent of the block                                                  **/
      Int       ***ijk;                  /**< IJK ordering for the nodes in the block. ijk[k][j][i] is the global
                                            index of the node at i,j,k                                                **/
      string       lbl;                  /**< Block label                                                             **/
  };

/** Stitch surfaces inside multiblock hexahedral grids **/
   struct q3dstcs_t
  {
      
      Int         iprm;
      Int           ni;
      Int           nj;
      Int        iblk0;
      Int        iblk1;
      Int        jblk0;
      Int        jblk1;

      Int      **iqdx0;
      Int      **jqdx0;
      Int      **kqdx0;

      Int      **iqdx1;
      Int      **jqdx1;
      Int      **kqdx1;

      Int      **indx0;
      Int      **indx1;
      Int      **indx2;
      Int      **indx3;

      string       lbl;
  };

   struct q3dstcl_t
  {
      Int        n;
      Int      *in;
      q3dsmt_t  smt;
  };

/** Boundary surfaces inside multiblock hexahedral grids **/
   struct q3dbnds_t
  {
      q3dprd_t     prd;
      q3dsmt_t     smt;
      Int         iblk;
      Int         jblk;
      Int           ni;
      Int           nj;
      Int       **iqdx;
      Int       **jqdx;
      Int       **kqdx;

      Int      **indx0;
      Int      **indx1;
      Int      **indx3;

      string       lbl;
      Real          *y[2];
  };

/** Boundary surface specifications inside multiblock hexahedral grids.
    This data structure is only used when building blocks. **/
   struct q3dbnd_t
  {
      q3dprd_t   prd;
      q3dsmt_t   smt;
      string     lbl;                                          /** Boundary label **/
      Int        ni;                                           /** Boundary surface I-extent **/
      Int        nj;                                           /** Boundary surface J-extent **/
      Real    ***y;
      Real    ***x;
  };

/** Initialize a boundary surface description
   @param             var                   Boundary surface.
 **/
         void init( Int n0, Int n1, q3dbnd_t **var );

/** Initialize a boundary surface description
   @param             var                   Boundary surface.
 **/
         void copy( q3dbnd_t *data, Int ist, Int ien, Int jst, Int jen, q3dbnd_t **var );

/** Destroy a boundary surface description
   @param             var                   Boundary surface.
 **/
         void destroy( q3dbnd_t **var );

// replace with vararg?//
         void build( Int n0, Int n1, Int i0, Int i1, Real **xh, Real **xt, q3dbnd_t **var );

/** Stitch points inside multiblock hexahedral grids **/
   struct q3dstcp_t
  {
      Int         n;
      Int       *nn;
      Int       *in;
      Int       *hn;
      Int      **jn;
      Int      **kn;
  };

/** Multiblock hexahedral grids **/

   class cQ3dMBlk
  {

      protected:

         cCosystem  *coo;              /**< Coordinate system                                                         **/

         Int        nblk;              /**< Number of blocks                                                          **/
         q3dblk_t   *blk[Q3DMXNBK];    /**< Blocks                                                                    **/
 
         Int        nsts;              /**< Number of stitch surfaces                                                 **/
         q3dstcs_t  *sts[Q3DMXNBK];    /**< Stitch surfaces                                                           **/

         Int        nbnd;              /**< Number of stitch surfaces                                                 **/
         q3dbnds_t  *bnd[Q3DMXNBK];    /**< Stitch surfaces                                                           **/

         Int        nstl;              /**< Number of stitch lines                                                    **/
         q3dstcl_t  *stl[Q3DMXNBK];    /**< Stitch lines                                                              **/

         q3dstcp_t   stp          ;    /**< Stitch ponts                                                              **/

         Int          ng;              /**< Number of boundary groups                                                 **/
         Int         ngb[Q3DMXNG];     /**< Number of surfaces associated to each boundary group                      **/
         Int        *igb[Q3DMXNG];     /**< Surfaces associated to each boundary group                                **/
         string     glbl[Q3DMXNG];     /**< Boundary group labels                                                     **/
         q3dprd_t   gprd[Q3DMXNG];     /**< Boundary group periodic flag                                              **/
         q3dsmt_t   gsmt[Q3DMXNG];     /**< Boundary group smooth flag                                                **/
 

         Int          np;              /**< Number of nodes                                                           **/
         Real        *xp[3];           /**< Physical coordinates of the nodes                                         **/
//       Real       ptch;              /**< Domain pitch                                                              **/


/** Initialize a block.
   @param             var                   Block.
 **/
         virtual void init( Int n0, Int n1, Int n2, q3dblk_t **var );

/** Initialize a boundary line.
   @param             var                   boundary line.
 **/
         virtual void init( q3dstcl_t **var );

/** Destroy a boundary line.
   @param             var                   boundary line.
 **/
         virtual void destroy( q3dstcl_t **var );



/** Initialize a stitch surface
   @param             var                   Stitch surface.
 **/
         virtual void init( Int n0, Int n1, q3dstcs_t **var );




/** Initialize a boundary surface
   @param             var                   Boundary surface.
 **/
         virtual void init( Int n0, Int n1, q3dbnds_t **var );


/** Initialize a stitch point
   @param             var                   Stitch point.
 **/
         virtual void init( q3dstcp_t *var );


/** Initialize a block.
   @param             var                   Block.
 **/
         virtual void destroy( q3dblk_t  **var );
         virtual void copy( q3dblk_t *data, q3dblk_t **var );
         virtual void renumber( q3dblk_t *var, Int * );


/** Initialize a stitch surface
   @param             var                   Stitch surface.
 **/
         virtual void destroy( q3dstcs_t **var );


/** Initialize a boundary surface
   @param             var                   Boundary surface.
 **/
         virtual void destroy( q3dbnds_t **var );
         virtual void renumber( q3dbnds_t *var, Int *indx );


/** Initialize a stitch point
   @param             var                   Stitch point.
 **/
         virtual void destroy( q3dstcp_t *var );
         void add( Int i0, Int j0, Int k0, Int h0, q3dstcp_t *var );
         void add( q3dstcp_t *var, q3dstcs_t *data );


/** Retrieve the index of the four nodes on face m,n of boundary surface l.
   @param                     l                Boundary surface. Input.
   @param                     m                Face i-index. Input.
   @param                     n                Face j-index. Input.
   @param                     idx              Node indeces.
 **/
         virtual void face( q3dbnds_t *var, Int m, Int n, Int *idx );

/** Retrieve the index of the four nodes opposite the face m,n of boundary surface l.
   @param                     l                Boundary surface. Input.
   @param                     m                Face i-index. Input.
   @param                     n                Face j-index. Input.
   @param                     idx              Node indeces.
 **/
         virtual void antiface( q3dbnds_t *var, Int m, Int n, Int *idx );

/** Split a boundary surface according to a boolean mask.
   @param                     msk              Boolean mask.
   @param                     b                Boundary surface.
   @param                    *n0               Number of new active boundary surfaces.
   @param                    *b0               Active boundary surfaces.
   @param                    *n1               Number of new active boundary surfaces.
   @param                    *b1               Active boundary surfaces.
   @return                                     true if the surface is split.
 **/
         virtual bool split( bool *msk, q3dbnds_t *b, Int *n0, q3dbnds_t **b0, Int *n1, q3dbnds_t **b1 );

         virtual void split( Int , q3dbnds_t **bl, q3dbnds_t **br, Int *, q3dbnds_t **var );

/** Split a boundary surface according to a boolean mask.
   @param                     msk              Boolean mask.
   @param                     b                Boundary surface.
   @param                    *n0               Number of new active boundary surfaces.
   @param                    *b0               Active boundary surfaces.
   @param                    *n1               Number of new active boundary surfaces.
   @param                    *b1               Active boundary surfaces.
   @return                                     true if the surface is split.
 **/
//       virtual bool split( q3dbnds_t *b, Int *n0, q3dbnds_t **b0, Int *n1, q3dbnds_t **b1 );

/** Fill in the node arrays for a boundary surface.
   @param                     b                Boundary surface.
 **/
         virtual void nodes( q3dbnds_t *b );

/** Copy a subrange from a boundary surface onto another
   @param                     ist              I-start.
   @param                     ien              I-end.
   @param                     jst              J-start.
   @param                     jen              J-end.
   @param                     data             Source boundary surface.
   @param                     var              Copy target.
 **/
         virtual void copy( Int ist, Int ien, Int jst, Int jen, q3dbnds_t *data, q3dbnds_t **var );

/** Copy a boundary surface onto another
   @param                     data             Source boundary surface.
   @param                     var              Copy target.
 **/
         virtual void copy( q3dbnds_t *data, q3dbnds_t **var );


/** Make stitch surfaces from a set of discarded boundary surfaces 
   @param                   n                Number of candidate surfaces.
   @param                   b                Number of candidate surfaces.
 **/
         virtual void stitch( Int n, q3dbnds_t **data );

/** Make stitch surfaces from a set of discarded boundary surfaces 
   @param                   n                Number of candidate surfaces.
   @param                   b                Number of candidate surfaces.
 **/
//       virtual void build( q3dbnds_t *data, q3dstcs_t **var );

/** Make stitch surfaces from a set of discarded boundary surfaces 
   @param                   v1               First boundary surface.
   @param                   v2               Second boundary surface.
 **/
         virtual bool same( q3dbnds_t *v1, q3dbnds_t *v2 );

/** Make stitch surfaces from a pair of discarded boundary surfaces 
   @param                   b0               First boundary surface.
   @param                   b1               Second boundary surface.
   @param                   var              Stitch surface.
 **/
         virtual void build( q3dbnds_t *b0, q3dbnds_t *b1, q3dstcs_t **var );

/** Compare two faces 
   @param                   jn0              First face.
   @param                   jn1              First face.
 **/
         virtual Int same( Int *, Int * );

/** Tecplot dump for blocks
   @param                   f                Output stream.
   @param                   b                Block
 **/
         virtual void tec( ofstream *f, q3dblk_t *var );

/** Tecplot dump for boundary surfaces
   @param                   f                Output stream.
   @param                   b                Boundary surface
 **/
         virtual void tec( ofstream *f, q3dbnds_t *var );

/** Tecplot dump for stitch surfaces
   @param                   f                Output stream.
   @param                   b                Stitch surface
 **/
         virtual void tec( ofstream *f, q3dstcs_t *var );

/** Search points when adding a new block.*/
         virtual void search( cQ3dMBlk *var, Int *iwrk, bool *msk );

/** Collect points on boundary group ig */
         virtual void collect( Int ig, Int *n, Int *iwrk, Int *jwrk );
         virtual void collect( string s, Int *n, Int *iwrk, Int *jwrk );
         virtual void collect( Int *n, Int *iwrk, Int *jwrk );

         virtual void lines();

      public:

         cQ3dMBlk();                   
         virtual ~cQ3dMBlk();

/** Build a single block grid from its sizes and the coordinates of the corners.
   @param                        n0             i-extent of the block.
   @param                        n1             j-extent of the block.
   @param                        n2             k-extent of the block.
   @param                        x0             000 corner
   @param                        x1             100 corner
   @param                        x2             110 corner
   @param                        x3             010 corner
   @param                        x4             001 corner
   @param                        x5             101 corner
   @param                        x6             111 corner
   @param                        x7             011 corner
   @param                        b0             ::0 boundary
   @param                        b1             :0: boundary
   @param                        b2             1:: boundary
   @param                        b3             :1: boundary
   @param                        b4             0:: boundary
   @param                        b5             ::1 boundary
 **/
         virtual void build( Int n0, Int n1, Int n2,
                             Real *x0, Real *x1, Real *x2, Real *x3, Real *x4, Real *x5, Real *x6, Real *x7,
                             q3dbnd_t *b0, q3dbnd_t *b1, q3dbnd_t *b2, q3dbnd_t *b3, q3dbnd_t *b4, q3dbnd_t *b5 );

/** Initialize a block.
   @param             var                   Block.
 **/
         virtual void build( q3dbnd_t *b0, q3dbnd_t *b1, q3dbnd_t *b2, 
                             q3dbnd_t *b3, q3dbnd_t *b4, q3dbnd_t *b5, Int n );

/** Initialize a block.
   @param             var                   Block.
 **/
         virtual void build( q3dbnd_t *b0, q3dbnd_t *b1, q3dbnd_t *b2, 
                             q3dbnd_t *b3, q3dbnd_t *b4, q3dbnd_t *b5 );

/** Initialize a block.
   @param             var                   Block.
 **/
         virtual void build( q3dbnd_t *b0, q3dbnd_t *b1, q3dbnd_t *b2, 
                             q3dbnd_t *b3, q3dbnd_t *b4, q3dbnd_t *b5, Int n, Real **x0, Real **x1, ... );

/** Tecplot output.
   @param                        s              File name.
 **/
         virtual void tec( string s );
         virtual void tec( string b, string s );

/** Add a new block.
   @param                        var            Block.
 **/
         virtual void add( cQ3dMBlk *var );

/** Translate
   @param                        dx             Translation vector.
 **/
         virtual void translate( Real *dx );

/** Translate
   @param                        dx             Translation vector.
 **/
         virtual void rotate( Real *x0, Real *ax, Real t );

/** Extract
   @param                        dx             Translation vector.
 **/
         virtual void extract( string s, Int ist, Int ien, Int jst, Int jen, Int iprm, q3dbnd_t **var );

/** Smooth blocks */
         virtual void smooth( q3dblk_t *b, Real *dx[] );

/** Smooth stitch surfaces.*/
         virtual void smooth( q3dstcs_t *b, Real *dx[] );

/** Smooth boundary surfaces */
         virtual void smooth( q3dbnds_t *b, Real *dx[] );

/** Smooth stitch surfaces.*/
         virtual void smooth( q3dstcp_t *b, Real *dx[] );

/** Elliptic smoothing.*/
         virtual void smooth( );

/** Finalize multiblock assembly.*/
         virtual void finalize( );

/** Finalize periodic information.*/
         virtual void periodic( );

/** Finalize periodic information.*/
         virtual void groups( );



  };

/**
  *@}
 **/

/**
  *@}
 **/

#  endif
