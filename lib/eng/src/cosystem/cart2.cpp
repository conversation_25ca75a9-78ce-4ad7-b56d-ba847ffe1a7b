  using namespace std;

# include <cosystem/cart2.h>

   void cCartCosystem2::validate( Int Nx )
  { 
      nx= Nx;
      if( nx < 1 || nx > 3 )
     {  
         cout << "only 1/3 coordinates in cartesian system\n";
         std::exit(1); 
     };
      nvel= 3;
      valid=true;
  }

   void cCartCosystem2::voffset_z( Int is, Int ie, Real f, Real ibpa, Int *isrc, Real *xsrc_re[], Real *xsrc_im[],
                                                                      Int *idst, Real *xdst_re[], Real *xdst_im[])
  {
      Int ip,i0,i1;
      Real x_re,y_re;
      Real x_im,y_im;
      Real cth,sth;
      complex<Real> x, y, img(0,1), dt;

      for( ip=is;ip<ie;ip++ )
     {
         i0= ip;
         if( isrc ){ i0= isrc[ip]; };
         i1= ip;
         if( idst ){ i1= idst[ip]; };

         x_re= xsrc_re[0][i0];
         y_re= xsrc_re[1][i0];

         x_im= xsrc_im[0][i0];
         y_im= xsrc_im[1][i0];

         x = x_re + x_im * img;
         y = y_re + y_im * img;

         dt = cos(f*ibpa*pi2) + img*sin(f*ibpa*pi2);

         //phase change
         x *= dt;
         y *= dt;

         x_re= x.real();
         y_re= y.real();

         x_im= x.imag();
         y_im= y.imag();

         xdst_re[0][i1]= x_re;
         xdst_re[1][i1]= y_re;

         xdst_im[0][i1]= x_im;
         xdst_im[1][i1]= y_im;

     }
  }
