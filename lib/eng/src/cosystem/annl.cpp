  using namespace std;

# include <mem/proto.h>
# include <cosystem/ann.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Feb 16 18:35:44 GMT 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         cartesian coordinate syestem with annular pitch

   cAnnCosystem::cAnnCosystem()
  {
      asct= -1;
      gsct= -1;
      ptch=ZERO;
  }

   cAnnCosystem::~cAnnCosystem()
  {
      asct= -1;
      gsct= -1;
      ptch=ZERO;
  }

   void cAnnCosystem::pickle( size_t *len, pickle_t *buf )
  {
      cCosystem::pickle( len,buf );
      pckle( len,asct,buf );
      pckle( len,gsct,buf );
  }

   void cAnnCosystem::unpickle( size_t *len, pickle_t buf )
  {
      cCosystem::unpickle( len,buf );
      unpckle( len,&asct,buf );
      unpckle( len,&gsct,buf );
      ptch= pi2*(Real)gsct/(Real)asct;
  }

   void cAnnCosystem::get( cTabData *data )
  {
      cTabItem *tmp;
      cCosystem::get( data );
      tmp= new cTabItem( asct ); data->append( "assembly-sectors", tmp );
      tmp= new cTabItem( gsct ); data->append( "grid-sectors", tmp );
      data->set( "assembly-sectors", asct );
      data->set( "grid-sectors",     gsct );
  }

   void cAnnCosystem::set( cTabData *data )
  {
      cCosystem::set( data );
      data->get( "assembly-sectors", &asct );
      data->get( "grid-sectors", &gsct );
      if( asct > 0 && gsct > 0 )
     {
         ptch= pi2*(Real)gsct/(Real)asct;
     }
  }

   void cAnnCosystem::validate( Int Nx )
  { 
      nx= Nx;
      if( nx != 3 )
     {  
         cout << "annular coordinate system requires 3 dimensions\n";
         std::exit(1); 
     };
      nvel= nx;
      valid=true;
  }

   void cAnnCosystem::coffset( Int np, Real f, Real *x )
  {
      Int ip;
      Real y,z,cth,sth;
      Real *px[3];
      if( ptch != ZERO )
     {
         subv( nx,np, x, px );
         cth= cos(f*ptch);
         sth= sin(f*ptch);
         for( ip=0;ip<np;ip++ )
        {
            y= px[1][ip];
            z= px[2][ip];
            px[1][ip]= y*cth+ z*sth;
            px[2][ip]=-y*sth+ z*cth;
        }
     }
  }

   void cAnnCosystem::coffset( Real f, Real *x )
  {
      Int ip;
      Real y,z,cth,sth;
      if( ptch != ZERO )
     {
         cth= cos(f*ptch);
         sth= sin(f*ptch);
         y= x[1];
         z= x[2];
         x[1]= y*cth+ z*sth;
         x[2]=-y*sth+ z*cth;

     }
  }

   void cAnnCosystem::voffset( Int ips, Int ipe, Int iv, Int *ipp, Real f, Real *v[] )
  {
      Int ip,jp;
      Real y,z,cth,sth;
      if( ptch != ZERO )
     {
         cth= cos(f*ptch);
         sth= sin(f*ptch);
         for( jp=ips;jp<ipe;jp++ )
        {
            ip= ipp[jp];
            y=           v[iv][ip];
            z=           v[iv+1][ip];
            v[iv][ip]=   y*cth+ z*sth;
            v[iv+1][ip]=-y*sth+ z*cth;
        }
     }
  }

   void cAnnCosystem::voffset( Int ips, Int ipe, Int iv, Real f, Real *v[] )
  {
      Int ip,jp;
      Real y,z,cth,sth;
      if( ptch != ZERO )
     {
         cth= cos(f*ptch);
         sth= sin(f*ptch);
         for( jp=ips;jp<ipe;jp++ )
        {
            y= v[iv][jp];
            z= v[iv+1][jp];
            v[iv][jp]= y*cth+ z*sth;
            v[iv+1][jp]=-y*sth+ z*cth;
        }
     }
  }

   void cAnnCosystem::toffset( Int ips, Int ipe, Int iv, Real f, Real *v[] )
  {
      Int ip,jp;
      Real y,z,cth,sth;
      if( ptch != ZERO )
     {
         cth= cos(f);
         sth= sin(f);
         for( jp=ips;jp<ipe;jp++ )
        {
            y= v[iv][jp];
            z= v[iv+1][jp];
            v[iv][jp]= y*cth+ z*sth;
            v[iv+1][jp]=-y*sth+ z*cth;
        }
     }
  }

   void cAnnCosystem::toffset( Int ips, Int ipe, Int iv, Real f, Real *sv, Int n )
  {
      Int ip,jp;
      Real y,z,cth,sth;
      if( ptch != ZERO )
     {
         cth= cos(f);
         sth= sin(f);
         for( jp=ips;jp<ipe;jp++ )
        {
            //y= v[iv][jp];
            //z= v[iv+1][jp];
            y= sv[ADDR(iv,jp,n)];
            z= sv[ADDR(iv+1,jp,n)];
            //v[iv][jp]= y*cth+ z*sth;
            //v[iv+1][jp]=-y*sth+ z*cth;
            sv[ADDR(iv,jp,n)]= y*cth+ z*sth;
            sv[ADDR(iv+1,jp,n)]=-y*sth+ z*cth;
        }
     }
  }

   void cAnnCosystem::toffsetgpu( Int ips, Int ipe, Int iv, Real f, Real *sv, Int n, Int nv )
  {
      Int ip,jp;
      Real y,z,cth,sth;
      if( ptch != ZERO )
     {
         cth= cos(f);
         sth= sin(f);
        #pragma acc parallel loop\
          present(sv[0:n*nv]) \
          default(none)
         for( jp=ips;jp<ipe;jp++ )
        {
            //y= v[iv][jp];
            //z= v[iv+1][jp];
            y= sv[ADDR(iv,jp,n)];
            z= sv[ADDR(iv+1,jp,n)];
            //v[iv][jp]= y*cth+ z*sth;
            //v[iv+1][jp]=-y*sth+ z*cth;
            sv[ADDR(iv,jp,n)]= y*cth+ z*sth;
            sv[ADDR(iv+1,jp,n)]=-y*sth+ z*cth;
        }
     }
  }

   void cAnnCosystem::toffset( Int ips, Int ipe, Int iv, Real f, cAu3xView<Real>& v )
  {
      Int ip,jp;
      Real y,z,cth,sth;
      Int n;
      Real *sv;

      n = v.get_dim1();
      sv = v.get_data();
         
      if( ptch != ZERO )
     {      
         cth= cos(f);
         sth= sin(f);
        #pragma acc parallel loop\
          present(sv[0:n*nv]) \
          default(none)
         for( jp=ips;jp<ipe;jp++ )
        {   
            //y= v[iv][jp];
            //z= v[iv+1][jp];
            y= sv[ADDR(iv,jp,n)];
            z= sv[ADDR(iv+1,jp,n)];
            //v[iv][jp]= y*cth+ z*sth;
            //v[iv+1][jp]=-y*sth+ z*cth;
            sv[ADDR(iv,jp,n)]= y*cth+ z*sth;
            sv[ADDR(iv+1,jp,n)]=-y*sth+ z*cth;
        }
     }  
  } 

   void cAnnCosystem::toffset( Int jp, Int iv, Real f, Real *sv, Int n )
  {
      Real y,z,cth,sth;
      if( ptch != 0 )
     {
         cth= cos(f);
         sth= sin(f);
         y= sv[ADDR(iv,jp,n)];
         z= sv[ADDR(iv+1,jp,n)];
         sv[ADDR(iv,jp,n)]= y*cth+ z*sth;
         sv[ADDR(iv+1,jp,n)]=-y*sth+ z*cth;
     }
  }

   void cAnnCosystem::voffset( Int is, Int ie, Real f, Int *isrc, Real *xsrc[], Int *idst, Real *xdst[] )
  {
      Int ip,i0,i1;
      Real x,y,z,cth,sth;
      if( ptch != ZERO )
     {
         cth= cos(f*ptch);
         sth= sin(f*ptch);
         for( ip=is;ip<ie;ip++ )
        {
            i0= ip;
            if( isrc ){ i0= isrc[ip]; };
            i1= ip;
            if( idst ){ i1= idst[ip]; };
            x= xsrc[0][i0];
            y= xsrc[1][i0];
            z= xsrc[2][i0];
            xdst[0][i1]= x;
            xdst[1][i1]= y*cth+ z*sth;
            xdst[2][i1]=-y*sth+ z*cth;
        }
     }
  }

   void cAnnCosystem::voffset( Int is, Int ie, Real f, Int *isrc, Real *sxsrc, Int *idst, Real *sxdst, Int nqsrc, Int nqdst )
  {
      Int ip,i0,i1;
      Real x,y,z,cth,sth;
      if( ptch != ZERO )
     {
         cth= cos(f*ptch);
         sth= sin(f*ptch);
         for( ip=is;ip<ie;ip++ )
        {
            i0= isrc[ip];
            i1= ip;
            x= sxsrc[ADDR(0,i0,nqsrc)];
            y= sxsrc[ADDR(1,i0,nqsrc)];
            z= sxsrc[ADDR(2,i0,nqsrc)];
            sxdst[ADDR(0,i1,nqdst)]= x;
            sxdst[ADDR(1,i1,nqdst)]= y*cth+ z*sth;
            sxdst[ADDR(2,i1,nqdst)]=-y*sth+ z*cth;
        }
     }
  }

   void cAnnCosystem::voffsetgpu( Int is, Int ie, Real f, Int *isrc, Real *sxsrc, Int *idst, Real *sxdst, Int nqsrc, Int nqdst )
  {
      Int ip,i0,i1,iv;
      Real x,y,z,cth,sth;
      if( ptch != ZERO )
     {
         cth= cos(f*ptch);
         sth= sin(f*ptch);

        //nqsrc: nq, nqdst:nprq
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present (isrc[0:nqdst],sxsrc[0:nv*nqsrc],sxdst[0:nv*nqdst],this) \
         default(none)
         for( ip=is;ip<ie;ip++ )
        {
            i0= isrc[ip];
            i1= ip;
            x= sxsrc[ADDR(0,i0,nqsrc)];
            y= sxsrc[ADDR(1,i0,nqsrc)];
            z= sxsrc[ADDR(2,i0,nqsrc)];
            sxdst[ADDR(0,i1,nqdst)]= x;
            sxdst[ADDR(1,i1,nqdst)]= y*cth+ z*sth;
            sxdst[ADDR(2,i1,nqdst)]=-y*sth+ z*cth;

            for( iv=nvel;iv<nv;iv++ )
           {
               sxdst[ADDR(iv,i1,nqdst)]= sxsrc[ADDR(iv,i0,nqsrc)];
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cAnnCosystem::voffset( Int is, Int ie, Real f, cAu3xView<Int>& isrc_view, cAu3xView<Real>& xsrc, cAu3xView<Int>& idst_view, cAu3xView<Real>& xdst )
  {
      Int ip,i0,i1,iv;
      Real x,y,z,cth,sth;

      Int nqdst, nqsrc;
      Int *isrc;
      Real *sxsrc, *sxdst;

      nqsrc = xsrc.get_dim1();
      nqdst = xdst.get_dim1();

      isrc = isrc_view.get_data();
      sxsrc = xsrc.get_data();
      sxdst = xdst.get_data();

      if( ptch != ZERO )
     {
         cth= cos(f*ptch);
         sth= sin(f*ptch);

        //nqsrc: nq, nqdst:nprq
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present (isrc[0:nqdst],sxsrc[0:nv*nqsrc],sxdst[0:nv*nqdst],this) \
         default(none)
         for( ip=is;ip<ie;ip++ )
        {
            i0= isrc[ip];
            i1= ip;
            x= sxsrc[ADDR(0,i0,nqsrc)];
            y= sxsrc[ADDR(1,i0,nqsrc)];
            z= sxsrc[ADDR(2,i0,nqsrc)];
            sxdst[ADDR(0,i1,nqdst)]= x;
            sxdst[ADDR(1,i1,nqdst)]= y*cth+ z*sth;
            sxdst[ADDR(2,i1,nqdst)]=-y*sth+ z*cth;

            for( iv=nvel;iv<nv;iv++ )
           {
               sxdst[ADDR(iv,i1,nqdst)]= sxsrc[ADDR(iv,i0,nqsrc)];
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cAnnCosystem::voffset(Real f, Real *var )
  {
      Real cth,sth,x,y,z; 
      //if( ptch != 0 )
     {
         cth= cos(f*ptch);
         sth= sin(f*ptch);

         x= var[0];
         y= var[1];
         z= var[2];
         var[0]= x;
         var[1]= y*cth+ z*sth;
         var[2]=-y*sth+ z*cth;
     }
  }

   void cAnnCosystem::voffset_z( Int is, Int ie, Real f, Real ibpa, Int *isrc, Real *xsrc_re[], Real *xsrc_im[], 
                                                                    Int *idst, Real *xdst_re[], Real *xdst_im[])
  {
      Int ip,i0,i1;
      Real x_re,y_re,z_re;
      Real x_im,y_im,z_im;
      Real cth,sth;
      complex<Real> x, y, z, img(0,1), dt;

      if( ptch != ZERO )
     {
         cth= cos(f*ptch);
         sth= sin(f*ptch);
         for( ip=is;ip<ie;ip++ )
        {
            i0= ip;
            if( isrc ){ i0= isrc[ip]; };
            i1= ip;
            if( idst ){ i1= idst[ip]; };

            x_re= xsrc_re[0][i0];
            y_re= xsrc_re[1][i0];
            z_re= xsrc_re[2][i0];

            x_im= xsrc_im[0][i0];
            y_im= xsrc_im[1][i0];
            z_im= xsrc_im[2][i0];

            x = x_re + x_im * img;
            y = y_re + y_im * img;
            z = z_re + z_im * img;

            dt = cos(f*ibpa*pi2) + img*sin(f*ibpa*pi2);

            //phase change
            x *= dt;
            y *= dt;
            z *= dt;

            x_re= x.real();
            y_re= y.real();
            z_re= z.real();

            x_im= x.imag();
            y_im= y.imag();
            z_im= z.imag();

            //rotate velocity vector
            xdst_re[0][i1]= x_re;
            xdst_re[1][i1]= y_re*cth+ z_re*sth;
            xdst_re[2][i1]=-y_re*sth+ z_re*cth;

            xdst_im[0][i1]= x_im;
            xdst_im[1][i1]= y_im*cth+ z_im*sth;
            xdst_im[2][i1]=-y_im*sth+ z_im*cth;

            
        }
     }
  }

   void cAnnCosystem::coffset( Int is, Int ie, Real f, Int *isrc, Real *xsrc[], Int *idst, Real *xdst[] )
  {
       voffset( is,ie, f, isrc,xsrc, idst,xdst );
  }

   void cAnnCosystem::coffset( Int is, Int ie, Real f, Int *isrc, Real *sxsrc, Int *idst, Real *sxdst, Int nqsrc, Int nqdst )
  {
       voffset( is,ie, f, isrc,sxsrc, idst,sxdst, nqsrc,nqdst );
  }

   void cAnnCosystem::coffsetgpu( Int is, Int ie, Real f, Int *isrc, Real *sxsrc, Int *idst, Real *sxdst, Int nqsrc, Int nqdst )
  {
      Int ip,i0,i1,iv;
      Real x,y,z,cth,sth;
      if( ptch != ZERO )
     {
         cth= cos(f*ptch);
         sth= sin(f*ptch);
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present (isrc[0:nqdst],sxsrc[0:nx*nqsrc],sxdst[0:nx*nqdst],this)\
         default(none)
         for( ip=is;ip<ie;ip++ )
        {
            i0= isrc[ip];
            i1= ip;
            x= sxsrc[ADDR(0,i0,nqsrc)];
            y= sxsrc[ADDR(1,i0,nqsrc)];
            z= sxsrc[ADDR(2,i0,nqsrc)];
            sxdst[ADDR(0,i1,nqdst)]= x;
            sxdst[ADDR(1,i1,nqdst)]= y*cth+ z*sth;
            sxdst[ADDR(2,i1,nqdst)]=-y*sth+ z*cth;
        }
        #pragma acc exit data delete(this)
     }
  }

   void cAnnCosystem::coffset( Int is, Int ie, Real f, cAu3xView<Int>& isrc_view, cAu3xView<Real>& xsrc, cAu3xView<Int>& idst_view, cAu3xView<Real>& xdst )
  {
      Int ip,i0,i1,iv;
      Real x,y,z,cth,sth;

      Int nqdst, nqsrc;
      Int *isrc;
      Real *sxsrc, *sxdst; 

      nqsrc = xsrc.get_dim1();
      nqdst = xdst.get_dim1();
         
      isrc = isrc_view.get_data(); 
      sxsrc = xsrc.get_data();  
      sxdst = xdst.get_data();

      if( ptch != ZERO )
     {
         cth= cos(f*ptch);
         sth= sin(f*ptch);
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present (isrc[0:nqdst],sxsrc[0:nx*nqsrc],sxdst[0:nx*nqdst],this)\
         default(none)
         for( ip=is;ip<ie;ip++ )
        {
            i0= isrc[ip];
            i1= ip;
            x= sxsrc[ADDR(0,i0,nqsrc)];
            y= sxsrc[ADDR(1,i0,nqsrc)];
            z= sxsrc[ADDR(2,i0,nqsrc)];
            sxdst[ADDR(0,i1,nqdst)]= x;
            sxdst[ADDR(1,i1,nqdst)]= y*cth+ z*sth;
            sxdst[ADDR(2,i1,nqdst)]=-y*sth+ z*cth;
        }
        #pragma acc exit data delete(this)
     }
  }

   void cAnnCosystem::frame( Int ips, Int ipe, Real omega, Real *xp[], Real *xdp[] )
  {
      Int   ip;
      Real   y,z;
      if( omega != ZERO )
     {
         for( ip=ips;ip<ipe;ip++ )
        {
            y= xp[1][ip]; 
            z= xp[2][ip]; 
            xdp[1][ip]+= z*omega;
            xdp[2][ip]-= y*omega;
        }
     }
  }

   void cAnnCosystem::frame( Int ips, Int ipe, Real omega, cAu3xView<Real>& xp, cAu3xView<Real>& xdp )
  {
      Int   ip;
      Real   y,z;
      Int np;
      Real *sxp, *sxdp;
      if( omega != ZERO ) 
     {
         np = xp.get_dim1();
         sxp = xp.get_data();
         sxdp = xdp.get_data();
         
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sxp[0:nx*np],sxdp[0:nvel*np],this)\
         default(none)
         for( ip=ips;ip<ipe;ip++ )
        {
            //y= xp(1,ip);
            //z= xp(2,ip);
            y= sxp[ADDR(1,ip,np)];
            z= sxp[ADDR(2,ip,np)];
            //xdp(1,ip)+= z*omega;
            //xdp(2,ip)-= y*omega;
            sxdp[ADDR(1,ip,np)]+= z*omega;
            sxdp[ADDR(2,ip,np)]-= y*omega;
        }
        #pragma acc exit data delete(this)

     }
  }

   void cAnnCosystem::accel( Int ips, Int ipe, Real omega, Real *xq[], Real *q[], Real *a[] )
  {
      Int   ip;
      Real  uy,uz;
      if( omega != ZERO )
     {
         for( ip=ips;ip<ipe;ip++ )
        {
            uy= q[1][ip]; 
            uz= q[2][ip]; 
            a[0][ip]=     ZERO;
            a[1][ip]=-uz*omega;
            a[2][ip]= uy*omega;
        }
     }
  }
   void cAnnCosystem::accel( Int ips, Int ipe, Real omega, Real *sxq, Real *sq, Real *sa, Int nq )
  {
      Int   ip;
      Real  uy,uz;
      if( omega != ZERO )
     {
         for( ip=ips;ip<ipe;ip++ )
        {
            //uy= q[1][ip]; 
            //uz= q[2][ip]; 
            uy= sq[ADDR(1,ip,nq)]; 
            uz= sq[ADDR(2,ip,nq)]; 
            sa[ADDR(0,ip,nq)]=     ZERO;
            sa[ADDR(1,ip,nq)]=-uz*omega;
            sa[ADDR(2,ip,nq)]= uy*omega;
        }
     }
  }
   void cAnnCosystem::daccel( Int ips, Int ipe, Real omega, Real *xq[], Real *q[], Real *dq[], Real *a[] )
  {
      Int   ip;
      Real  uy,uz;
      if( omega != ZERO )
     {
         for( ip=ips;ip<ipe;ip++ )
        {
            uy= dq[1][ip]; 
            uz= dq[2][ip]; 
            a[0][ip]=     ZERO;
            a[1][ip]=-uz*omega;
            a[2][ip]= uy*omega;
        }
     }
  }
   void cAnnCosystem::daccel( Int ips, Int ipe, Real omega, Real *sxq, Real *sq, Real *sdq, Real *sa, Int nq )
  {
      Int   ip;
      Real  uy,uz;
      if( omega != ZERO )
     {
         for( ip=ips;ip<ipe;ip++ )
        {
            uy= sdq[ADDR(1,ip,nq)]; 
            uz= sdq[ADDR(2,ip,nq)]; 
            sa[ADDR(0,ip,nq)]=     ZERO;
            sa[ADDR(1,ip,nq)]=-uz*omega;
            sa[ADDR(2,ip,nq)]= uy*omega;
        }
     }
  }

   void cAnnCosystem::bcoor( Int ips, Int ipe, Real *x[], Real *bx[] )
  {
      Int ip,ix;
      Real x0,x1,x2,r;
      for( ip=ips;ip<ipe;ip++ )
     {
         x0= x[0][ip];
         x1= x[1][ip];
         x2= x[2][ip];
         r= x1*x1+ x2*x2;
         r= sqrt(r);
         bx[0][ip]= x0;
         bx[1][ip]= r;
         bx[2][ip]= atan2(x1,x2);
     }
  }

   void cAnnCosystem::bvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *q[], Real *bq[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= ipq[0][jp];
         x0= x[0][jp];
         x1= x[1][jp];
         x2= x[2][jp];
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r;
         sth= x1/r;
         bq[0][jp]= q[0][ip];
         Real tmp= q[1][ip];
         bq[1][jp]= sth* tmp +cth*q[2][ip];
         bq[2][jp]= cth* tmp -sth*q[2][ip];
     }
  }

   void cAnnCosystem::bvel( Int ips, Int ipe, cAu3xView<Int>& ipq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& bq )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;

      Int n_ipq, n_x, n_q, n_bq, nv;
      Int *sipq;
      Real *sx, *sq, *sbq;

      n_ipq = ipq.get_dim0();
      n_x   = x.get_dim1();
      n_q   = q.get_dim1();
      n_bq  = bq.get_dim1();
      nv    = q.get_dim0();

      sipq = ipq.get_data();
      sx   = x.get_data();
      sq   = q.get_data();
      sbq  = bq.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop\
      present(sipq[0:n_ipq],sx[0:nx*n_x], sq[0:nv*n_q], sbq[0:nv*n_bq]) \
      default(none)
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= sipq[jp];
         x0= sx[ADDR(0,jp,n_x)];
         x1= sx[ADDR(1,jp,n_x)];
         x2= sx[ADDR(2,jp,n_x)];
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r;
         sth= x1/r;
         sbq[ADDR(0,jp,n_bq)]= sq[ADDR(0,ip,n_q)];
         Real tmp= sq[ADDR(1,ip,n_q)];
         sbq[ADDR(1,jp,n_bq)]= sth* tmp +cth*sq[ADDR(2,ip,n_q)];
         sbq[ADDR(2,jp,n_bq)]= cth* tmp -sth*sq[ADDR(2,ip,n_q)];
     }
     #pragma acc exit data delete(this)
  }

   void cAnnCosystem::bvel( Int ib, Int *ibq[], Real *xb[], Real *q[], Real *bq )
  {
      Int iq;
      Real x0, x1, x2, r, cth, sth, tmp;

      iq= ibq[0][ib];
      x0= xb[0][ib];
      x1= xb[1][ib];
      x2= xb[2][ib];
      r= x1*x1+ x2*x2;
      r= sqrt(r)+small;
      cth= x2/r;
      sth= x1/r;
      bq[0]= q[0][iq];
      tmp= q[1][iq];
      bq[1]= sth* tmp +cth*q[2][iq];
      bq[2]= cth* tmp -sth*q[2][iq];
  }

   void cAnnCosystem::zvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipq ) ip= ipq[0][jp];
         x0= x[0][jp];
         x1= x[1][jp];
         x2= x[2][jp];
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r;
         sth= x1/r;
         q[0][ip]= bq[0][jp];
         Real tmp= bq[1][jp];
         q[1][ip]= sth*tmp +cth*bq[2][jp];
         q[2][ip]= cth*tmp -sth*bq[2][jp];
     }
  }

   void cAnnCosystem::zvel( Int ips, Int ipe, Int *ipq[], cAu3xView<Real>& x, cAu3xView<Real>& bq, cAu3xView<Real>& q )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipq ) ip= ipq[0][jp];
         x0= x(0,jp);
         x1= x(1,jp);
         x2= x(2,jp);
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r;
         sth= x1/r;
         q(0,ip)= bq(0,jp);
         Real tmp= bq(1,jp);
         q(1,ip)= sth*tmp +cth*bq(2,jp);
         q(2,ip)= cth*tmp -sth*bq(2,jp);
     }
  }

   void cAnnCosystem::zvel( Int ips, Int ipe, cAu3xView<Real>& x, cAu3xView<Real>& bq, cAu3xView<Real>& q )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      Int n_x, n_bq, n_q;
      Real *sx, *sbq, *sq;

      n_x = x.get_dim1();
      n_bq= bq.get_dim1();
      n_q = q.get_dim1();

      sx = x.get_data();
      sbq= bq.get_data();
      sq = q.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop\
      present(sx[0:nx*n_x], sq[0:nv*n_q], sbq[0:nv*n_bq],this) \
      default(none)
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         x0= sx[ADDR(0,jp,n_x)];
         x1= sx[ADDR(1,jp,n_x)];
         x2= sx[ADDR(2,jp,n_x)];
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r;
         sth= x1/r;
         sq[ADDR(0,ip,n_q)]= sbq[ADDR(0,jp, n_bq)];
         Real tmp= sbq[ADDR(1,jp,n_bq)];
         sq[ADDR(1,ip,n_q)]= sth*tmp +cth*sbq[ADDR(2,jp, n_bq)];
         sq[ADDR(2,ip,n_q)]= cth*tmp -sth*sbq[ADDR(2,jp, n_bq)];
     }
     #pragma acc exit data delete(this)
  }

   void cAnnCosystem::cvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipq ) ip= ipq[0][jp];
         q[0][ip]= bq[0][jp];
         q[1][ip]= bq[1][jp];
         q[2][ip]= bq[2][jp];
     }
  }
   void cAnnCosystem::yvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipq ) ip= ipq[0][jp];
         q[0][ip]= bq[0][jp];
         q[1][ip]= bq[1][jp];
         q[2][ip]= bq[2][jp];
     }
  }

   void cAnnCosystem::bcoor( Int ips, Int ipe, Int *ipx[], Real *x[], Real *bx[] )
  {
      Int ip,jp,ix;
      Real x0,x1,x2,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= ipx[0][jp];
         x0= x[0][ip];
         x1= x[1][ip];
         x2= x[2][ip];
         r= x1*x1+ x2*x2;
         r= sqrt(r);
         bx[0][jp]= x0;
         bx[1][jp]= r;
         bx[2][jp]= atan2(x1,x2); //deprecated
     }
  }

   void cAnnCosystem::bcoor( Int ips, Int ipe, Int *ipx[], cAu3xView<Real>& x, cAu3xView<Real>& bx )
  {   
      Int ip,jp,ix;
      Real x0,x1,x2,r;
      for( jp=ips;jp<ipe;jp++ )
     {   
         ip= ipx[0][jp];
         x0= x(0,ip);
         x1= x(1,ip);
         x2= x(2,ip);
         r= x1*x1+ x2*x2;
         r= sqrt(r);
         bx(0,jp)= x0;
         bx(1,jp)= r;
         bx(2,jp)= atan2(x1,x2); //deprecated
     }
  }

   void cAnnCosystem::bcoor( Int ips, Int ipe, cAu3xView<Real>& x, cAu3xView<Real>& bx )
  {
      Int ip,ix;
      Real x0,x1,x2,r;

      Int nq, nbb;
      Real *sx, *sbx;

      nq  = x.get_dim1();
      nbb = bx.get_dim1();  
      sx  = x.get_data();
      sbx = bx.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop\
      present(sx[0:nx*nq],sbx[0:nx*nbb],this) \
      default(none)
      for( ip=ips;ip<ipe;ip++ )
     {
         x0= sx[ADDR(0,ip,nq)];
         x1= sx[ADDR(1,ip,nq)];
         x2= sx[ADDR(2,ip,nq)];
         r= x1*x1+ x2*x2;
         r= sqrt(r);
         sbx[ADDR(0,ip,nbb)]= x0;
         sbx[ADDR(1,ip,nbb)]= r;
         sbx[ADDR(2,ip,nbb)]= atan2(x1,x2);
     }
     #pragma acc exit data delete(this)
  }

   void cAnnCosystem::ccoor( Int ips, Int ipe, Int *ipx[], Real *x[], Real *bx[] )
  {
      Int ip,jp,ix;
      Real x0,x1,x2,r;
//    cout << "ANNULAR COSYSTEM CCORR<========================================\n";
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipx )
        { ip= ipx[0][jp]; };
         bx[0][jp]= x[0][ip];
         bx[1][jp]= x[1][ip];
         bx[2][jp]= x[2][ip];
     }
  }

   void cAnnCosystem::jacoffset(Int ics, Int ice, Real f, Int nv, cJacBlk * jacblk)
  {
      Int ic, iv, jv;
      Real d1, d2, d1_new, d2_new;
      Real cth, sth;

      cth= cos(f*ptch);
      sth= sin(f*ptch);
      for(ic=ics; ic<ice; ic++)
     {
         for(iv=0; iv<nv; iv++)
        {
            d1 = jacblk[ic].jac[iv][2];
            d2 = jacblk[ic].jac[iv][3];

            d1_new= d1*cth+ d2*sth;
            d2_new=-d1*sth+ d2*cth;

            jacblk[ic].jac[iv][2] =  d1_new;
            jacblk[ic].jac[iv][3] =  d2_new;
        }

         for(iv=0; iv<nv; iv++)
        {
            d1 = jacblk[ic].jac[2][iv];
            d2 = jacblk[ic].jac[3][iv];

            d1_new= d1*cth+ d2*sth;
            d2_new=-d1*sth+ d2*cth;

            jacblk[ic].jac[2][iv] = d1_new;
            jacblk[ic].jac[3][iv] = d2_new;
        }
     }
  }

