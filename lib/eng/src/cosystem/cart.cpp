  using namespace std;

# include <cosystem/cart.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Feb 16 18:52:26 GMT 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         cartesian coordinate system with linear pitch

   cCartCosystem::cCartCosystem()
  {
      ptch=ZERO;
  }

   cCartCosystem::~cCartCosystem()
  {
      ptch=ZERO;
  }

   void cCartCosystem::pickle( size_t *len, pickle_t *buf )
  {
      cCosystem::pickle( len,buf );
      pckle( len, ptch, buf );
  }

   void cCartCosystem::unpickle( size_t *len, pickle_t buf )
  {
      cCosystem::unpickle( len,buf );
      unpckle( len, &ptch, buf );
  }

   void cCartCosystem::get( cTabData *data )
  {
      cTabItem *tmp;
      cCosystem::get( data );
      tmp= new cTabItem( ptch ); data->append( "pitch", tmp );
      data->set( "pitch", ptch );
  }

   void cCartCosystem::set( cTabData *data )
  {
      cCosystem::set( data );
      data->get( "pitch", &ptch );
  }

   void cCartCosystem::validate( Int Nx )
  { 
      nx= Nx;
      if( nx < 1 || nx > 3 )
     {  
         cout << "only 1/3 coordinates in cartesian system\n";
         std::exit(1); 
     };
      nvel= nx;
      valid=true;
  }

   void cCartCosystem::coffset( Int np, Real f, Real *x )
  {
      Int ip;
      Real y,z,cth,sth;
      Real *px[3];

      cout << "coordinate system is cartesian\n";

      if( ptch != 0 )
     {
         subv( nx,np, x, px );
         for( ip=0;ip<np;ip++ )
        {
            px[1][ip]+= f*ptch;
        }
     }
  }

   void cCartCosystem::voffset( Int ips, Int ipe, Int iv, Int *ipp, Real f, Real *v[] ){}
   void cCartCosystem::voffset( Int ips, Int ipe, Int iv, Real f, Real *v[] ){}

   void cCartCosystem::voffset( Int is, Int ie, Real f, Int *isrc, Real *xsrc[], Int *idst, Real *xdst[] )
  {
      Int ip,i0,i1;
      Real x,y,z,cth,sth;
      if( nvel == 3 )
     {
         for( ip=is;ip<ie;ip++ )
        {
            i0= ip;
            if( isrc ){ i0= isrc[ip]; };
            i1= ip;
            if( idst ){ i1= idst[ip]; };
            xdst[0][i1]= xsrc[0][i0];
            xdst[1][i1]= xsrc[1][i0];
            xdst[2][i1]= xsrc[2][i0];
        }
     }
      else
     {
         if( nvel == 2 )
        {
            for( ip=is;ip<ie;ip++ )
           {
               i0= ip;
               if( isrc ){ i0= isrc[ip]; };
               i1= ip;
               if( idst ){ i1= idst[ip]; };
               xdst[0][i1]= xsrc[0][i0];
               xdst[1][i1]= xsrc[1][i0];
           }
        }
     }
  }

   void cCartCosystem::coffset( Int is, Int ie, Real f, Int *isrc, Real *xsrc[], Int *idst, Real *xdst[] )
  {
      Int ip,i0,i1;
      Real x,y,z,cth,sth;
      if( nvel == 3 )
     {
         for( ip=is;ip<ie;ip++ )
        {
            i0= ip;
            if( isrc ){ i0= isrc[ip]; };
            i1= ip;
            if( idst ){ i1= idst[ip]; };
            xdst[0][i1]= xsrc[0][i0];
            //xdst[1][i1]= xsrc[1][i0]+f;
            xdst[1][i1]= xsrc[1][i0]+f*ptch;
            xdst[2][i1]= xsrc[2][i0];
        }
     }
      else
     {
         if( nvel == 2 )
        {
            for( ip=is;ip<ie;ip++ )
           {
               i0= ip;
               if( isrc ){ i0= isrc[ip]; };
               i1= ip;
               if( idst ){ i1= idst[ip]; };
               xdst[0][i1]= xsrc[0][i0];
               //xdst[1][i1]= xsrc[1][i0]+f;
               xdst[1][i1]= xsrc[1][i0]+f*ptch;
           }
        }
     }
  }

   void cCartCosystem::frame( Int ips, Int ipe, Real omega, Real *xp[], Real *xdp[] )
  {
      Int   ip;
      Real   y,z;
      for( ip=ips;ip<ipe;ip++ )
     {
         xdp[1][ip]+= omega;    
     }
  }

   void cCartCosystem::frame( Int ips, Int ipe, Real omega, cAu3xView<Real>& xp, cAu3xView<Real>& xdp )
  {
      Int   ip;
      Real   y,z;
      Int np;
      Real *sxdp;

      np = xp.get_dim1();
      sxdp = xdp.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop \
      present(sxdp[0:nvel*np],this)\
      default(none)
      for( ip=ips;ip<ipe;ip++ )
     {
         //xdp(1,ip)+= omega;
         sxdp[ADDR(1,ip,np)]+= omega;
     }
     #pragma acc exit data delete(this)
  }

   void cCartCosystem::accel( Int ips, Int ipe, Real omega, Real *xq[], Real *q[], Real *a[] )
  {
      setv( ips,ipe, nx,ZERO, a );
  }
   void cCartCosystem::daccel( Int ips, Int ipe, Real omega, Real *xq[], Real *q[], Real *dq[], Real *a[] )
  {
      setv( ips,ipe, nx,ZERO, a );
  }

   void cCartCosystem::bcoor( Int ips, Int ipe, Real *x[], Real *bx[] )
  {
      Int ip,ix;
      Real x0,x1,x2,r;
      for( ix=0;ix<nx;ix++ ) 
     {
         for( ip=ips;ip<ipe;ip++ )
        {
            bx[ix][ip]= x[ix][ip];
        }
     }
  }

   void cCartCosystem::bvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *q[], Real *bq[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( ix=0;ix<nvel;ix++ ) 
     {
         for( jp=ips;jp<ipe;jp++ )
        {
            ip= ipq[0][jp];
            bq[ix][jp]= q[ix][ip];
        }
     }
  }

   void cCartCosystem::bvel( Int ips, Int ipe, cAu3xView<Int>& ipq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& bq )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;

      Int n_ipq, n_x, n_q, n_bq, nv;
      Int *sipq;
      Real *sx, *sq, *sbq;
        
      n_ipq = ipq.get_dim0();
      n_x   = x.get_dim1();
      n_q   = q.get_dim1();
      n_bq  = bq.get_dim1();
      nv    = q.get_dim0();

      sipq = ipq.get_data();
      sx   = x.get_data();
      sq   = q.get_data();
      sbq  = bq.get_data();
 
     #pragma acc enter data copyin(this)
     #pragma acc parallel loop\
      present(sipq[0:n_ipq],sx[0:nx*n_x], sq[0:nv*n_q], sbq[0:nv*n_bq],this) \
      default(none)
      for( jp=ips;jp<ipe;jp++ )
     {
         for( ix=0;ix<nvel;ix++ ) 
        {
            ip= sipq[jp];
            sbq[ADDR(ix,jp,n_bq)]= sq[ADDR(ix,ip,n_q)];
        }
     }
     #pragma acc exit data delete(this)
  }

   void cCartCosystem::cvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( ix=0;ix<nvel;ix++ ) 
     {
         for( jp=ips;jp<ipe;jp++ )
        {
            ip= jp;
            if( ipq ){ ip= ipq[0][jp]; };
            q[ix][ip]= bq[ix][jp];
        }
     }
  }

   void cCartCosystem::yvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( ix=0;ix<nvel;ix++ ) 
     {
         for( jp=ips;jp<ipe;jp++ )
        {
            ip= jp;
            if( ipq ){ ip= ipq[0][jp]; };
            q[ix][ip]= bq[ix][jp];
        }
     }
  }

   void cCartCosystem::zvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( ix=0;ix<nvel;ix++ ) 
     {
         for( jp=ips;jp<ipe;jp++ )
        {
            ip= jp;
            if( ipq ){ ip= ipq[0][jp]; };
            q[ix][ip]= bq[ix][jp];
        }
     }
  }

   void cCartCosystem::zvel( Int ips, Int ipe, Int *ipq[], cAu3xView<Real>& x, cAu3xView<Real>& bq, cAu3xView<Real>& q )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( ix=0;ix<nvel;ix++ ) 
     {
         for( jp=ips;jp<ipe;jp++ )
        {
            ip= jp;
            if( ipq ){ ip= ipq[0][jp]; };
            q(ix,ip)= bq(ix,jp);
        }
     }
  }

   void cCartCosystem::zvel( Int ips, Int ipe, cAu3xView<Real>& x, cAu3xView<Real>& bq, cAu3xView<Real>& q )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;

     Int n_x, n_bq, n_q;
      Real *sx, *sbq, *sq;

      n_x = x.get_dim1();
      n_bq= bq.get_dim1();
      n_q = q.get_dim1();

      sx = x.get_data();
      sbq= bq.get_data();
      sq = q.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop\
      present(sx[0:nx*n_x], sq[0:nv*n_q], sbq[0:nv*n_bq],this) \
      default(none)
      for( ix=0;ix<nvel;ix++ ) 
     {
         for( jp=ips;jp<ipe;jp++ )
        {
            ip= jp;
            sq[ADDR(ix,ip,n_q)]= sbq[ADDR(ix,jp,n_bq)];
        }
     }
     #pragma acc exit data delete(this)
  }

   void cCartCosystem::bcoor( Int ips, Int ipe, Int *ipx[], Real *x[], Real *bx[] )
  {
      Int ip,jp,ix;
      Real x0,x1,x2,r;
      for( ix=0;ix<nx;ix++ ) 
     {
         for( jp=ips;jp<ipe;jp++ )
        {
            ip= ipx[0][jp];
            bx[ix][jp]= x[ix][ip];
        }
     }
  }

   void cCartCosystem::bcoor( Int ips, Int ipe, cAu3xView<Real>& x, cAu3xView<Real>& bx )
  {
      Int ip,ix;
      Real x0,x1,x2,r;

      Int nq, nbb;
      Real *sx, *sbx;
  
      nq  = x.get_dim1();
      nbb = bx.get_dim1();  
      sx  = x.get_data();
      sbx = bx.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop\
      present(sx[0:nx*nq],sbx[0:nx*nbb],this) \
      default(none)
      for( ip=ips;ip<ipe;ip++ )
     {
         for( ix=0;ix<nx;ix++ )
        {
            sbx[ADDR(ix,ip,nbb)]= sx[ADDR(ix,ip,nq)];
        }   
     }
     #pragma acc exit data delete(this)
  }

   void cCartCosystem::bcoor( Int ips, Int ipe, Int *ipx[], cAu3xView<Real>& x, cAu3xView<Real>& bx )
  {   
      Int ip,jp,ix;
      Real x0,x1,x2,r;
      for( ix=0;ix<nx;ix++ )
     {   
         for( jp=ips;jp<ipe;jp++ )
        {   
            ip= ipx[0][jp];
            bx(ix,jp)= x(ix,ip);
        }
     }
  }

   void cCartCosystem::voffset_z( Int is, Int ie, Real f, Real ibpa, Int *isrc, Real *xsrc_re[], Real *xsrc_im[],
                                                                     Int *idst, Real *xdst_re[], Real *xdst_im[])
  {
      Int ip,i0,i1;
      Real x_re,y_re;
      Real x_im,y_im;
      Real cth,sth, t;
      complex<Real> x, y, img(0,1), dt;

      if(nvel==2)
     {
        for( ip=is;ip<ie;ip++ )
       {
           i0= ip;
           if( isrc ){ i0= isrc[ip]; };
           i1= ip;
           if( idst ){ i1= idst[ip]; };
  
           x_re= xsrc_re[0][i0];
           x_im= xsrc_im[0][i0];

           y_re= xsrc_re[1][i0];
           y_im= xsrc_im[1][i0];
  
           x = x_re + x_im * img;
           y = y_re + y_im * img;

           t = f*ibpa*pi2;
           dt = cos(t) + img*sin(t);

       //    cout << dt << " " << x_re << " " << x_im << " ---> ";
           //phase change
           x *= dt;
           y *= dt;

           x_re= x.real();
           y_re= y.real();

           x_im= x.imag();
           y_im= y.imag();

           xdst_re[0][i1]= x_re;
           xdst_re[1][i1]= y_re;

           xdst_im[0][i1]= x_im;
           xdst_im[1][i1]= y_im;
  
       } 
     }
      else
     {
         cout << "Can not do this yet\n";
         assert(0);
     }
  }

