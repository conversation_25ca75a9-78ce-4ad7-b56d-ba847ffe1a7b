   using namespace std;

#  include <cosystem/cosystem.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Feb 16 18:14:49 GMT 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         abstract coordinate system


   cCosystem::cCosystem()
  {
      nx=-1;
      nvel=-1;
      valid=false;
  }

   cCosystem::~cCosystem()
  {
      nx=-1;
      nvel=-1;
      valid=false;
  }

   void cCosystem::pickle( size_t *len, pickle_t *buf )
  {
      pckle( len,nx,buf );
      pckle( len,nvel,buf );
      pckle( len,valid,buf );
  }
   void cCosystem::unpickle( size_t *len, pickle_t buf )
  {
      unpckle( len,&nx,buf );
      unpckle( len,&nvel,buf );
      unpckle( len,&valid,buf );
  }

   void cCosystem::get( cTabData *data )
  {
      cTabItem *tmp;
      tmp= new cTabItem( nx ); data->append( "coordinates", tmp );
      tmp= new cTabItem( nvel ); data->append( "velocities", tmp );
      data->set( "coordinates", nx );
      data->set( "velocities", nvel );
  }

   void cCosystem::set( cTabData *data )
  {
      data->get( "coordinates", &nx );
      data->get( "velocities", &nvel );
  }

   void cCosystem::xatl( Int iss, Int ise, Int npc, Int *pisp[], Real *pxp[], Real pQ[], Real *pwrk[] )
  {
      Int ip,jp,ix,is;
      for( ix=0;ix<nx;ix++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            for( is=iss;is<ise;is++ )
           {
               ip= pisp[jp][is];        
               pwrk[ix][is]+= pxp[ix][ip]* pQ[jp];
           }
        }     
     }
  }

   void cCosystem::xatl( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pwrk )
  {
      Int ip,jp,ix,is;

      Int npxp, npwrk, npQ;
      Real *spxp, *spwrk;
      Real *spQ;

      Int npisp, *spisp;

      npxp = pxp.get_dim1();
      npwrk = pwrk.get_dim1();
      npisp = pisp.get_dim1();
      npQ = pQ.get_dim0();

      spxp = pxp.get_data();
      spwrk = pwrk.get_data();
      spisp = pisp.get_data();
      spQ = pQ.get_data(); 

      for( ix=0;ix<nx;ix++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            for( is=iss;is<ise;is++ )
           {
               //ip= pisp[jp][is];
               ip= spisp[ADDR(jp,is,npisp)];
               //spwrk[ADDR(ix,is,npwrk)]+= spxp[ADDR(ix,ip,npxp)]* pQ[jp];
               spwrk[ADDR(ix,is,npwrk)]+= spxp[ADDR(ix,ip,npxp)]* spQ[jp];
           }
        }
     }
  }

   void cCosystem::xatl_gpu( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pwrk )
  {
      Int ip,jp,ix,is;

      Int npxp, npwrk, npQ;
      Real *spxp, *spwrk;
      Real *spQ;

      Int npisp, *spisp;

      npxp = pxp.get_dim1();
      npwrk = pwrk.get_dim1();
      npisp = pisp.get_dim1();
      npQ = pQ.get_dim0();

      spxp = pxp.get_data();
      spwrk = pwrk.get_data();
      spisp = pisp.get_data();
      spQ = pQ.get_data(); 

      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       present(spisp[0:npc*npisp],spwrk[0:npwrk*nx],spxp[0:npxp*nx],spQ[0:npc],this) \
       default(none)
      for( is=iss;is<ise;is++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            for( ix=0;ix<nx;ix++ )
           {
               //ip= pisp[jp][is];
               ip= spisp[ADDR(jp,is,npisp)];
               //spwrk[ADDR(ix,is,npwrk)]+= spxp[ADDR(ix,ip,npxp)]* pQ[jp];
               spwrk[ADDR(ix,is,npwrk)]+= spxp[ADDR(ix,ip,npxp)]* spQ[jp];
           }
        }
     }
      #pragma acc exit data delete(this)
  }

   void cCosystem::dxdl( Int iss, Int ise, Int npc, Int *pisp[], Real *pxp[], Real *pQ, Real *pdQdy[], Real *pwrk[] )
  {
      Int ip,jp,ix,is;
      for( ix=0;ix<nx;ix++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            for( is=iss;is<ise;is++ )
           {
               ip= pisp[jp][is];        
               pwrk[ix][is]+= pxp[ix][ip]* pdQdy[0][jp];
           }
        }     
     }
  }

   void cCosystem::dxdl( Int iss, Int ise, Int npc, Int *pisp[], Real *pxp[], Real *pQ, Real *pdQdy[], Real *pwrk0[], Real *pwrk1[] )
  {
      Int ip,jp,ix,is;
      for( ix=0;ix<nx;ix++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            for( is=iss;is<ise;is++ )
           {
               ip= pisp[jp][is];        
               pwrk0[ix][is]+= pxp[ix][ip]* pdQdy[0][jp];
               pwrk1[ix][is]+= pxp[ix][ip]* pdQdy[1][jp];
           }
        }     
     }
  }
   void cCosystem::dxdl( Int iss, Int ise, Int npc, Int *pisp[], Real *pxp[], Real *pQ, Real *pdQdy[], Real *pwrk0[], Real *pwrk1[], Real *pwrk2[] )
  {
      Int ip,jp,ix,is;
      for( ix=0;ix<nx;ix++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            for( is=iss;is<ise;is++ )
           {
               ip= pisp[jp][is];        
               pwrk0[ix][is]+= pxp[ix][ip]* pdQdy[0][jp];
               pwrk1[ix][is]+= pxp[ix][ip]* pdQdy[1][jp];
               pwrk2[ix][is]+= pxp[ix][ip]* pdQdy[2][jp];
           }
        }     
     }
  }

   void cCosystem::dxdl( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pdQdy, cAu3xView<Real>& pwrk )
  {
      Int ip,jp,ix,is;

      Int npxp, npwrk, npdQdy;
      Real *spxp, *spwrk, *spdQdy;
      Int npisp, *spisp;

      npxp = pxp.get_dim1();
      npwrk = pwrk.get_dim1();
      npisp = pisp.get_dim1();
      npdQdy = pdQdy.get_dim1();

      spxp = pxp.get_data(); 
      spwrk = pwrk.get_data();
      spisp = pisp.get_data();
      spdQdy = pdQdy.get_data();

      for( ix=0;ix<nx;ix++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            for( is=iss;is<ise;is++ )
           {
               //ip= pisp[jp][is];
               ip= spisp[ADDR(jp,is,npisp)];
               //spwrk[ADDR(ix,is,npwrk)]+= spxp[ADDR(ix,ip,npxp)]* pdQdy[0][jp];
               spwrk[ADDR(ix,is,npwrk)]+= spxp[ADDR(ix,ip,npxp)]* spdQdy[ADDR(0,jp,npdQdy)];
           }
        }
     }
  }

   void cCosystem::dxdl( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pdQdy, cAu3xView<Real>& pwrk0, cAu3xView<Real>& pwrk1 )
  {
      Int ip,jp,ix,is;

      Int npxp, npwrk0, npwrk1, npdQdy;
      Real *spxp, *spwrk0, *spwrk1, *spdQdy;
      Int npisp, *spisp;

      npxp = pxp.get_dim1();
      npwrk0 = pwrk0.get_dim1();
      npwrk1 = pwrk1.get_dim1();
      npisp = pisp.get_dim1();
      npdQdy = pdQdy.get_dim1();

      spxp = pxp.get_data(); 
      spwrk0 = pwrk0.get_data();
      spwrk1 = pwrk1.get_data();
      spisp = pisp.get_data();
      spdQdy = pdQdy.get_data();

      for( ix=0;ix<nx;ix++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            for( is=iss;is<ise;is++ )
           {
               //ip= pisp[jp][is];
               ip= spisp[ADDR(jp,is,npisp)];
               //spwrk0[ADDR(ix,is,npwrk0)]+= spxp[ADDR(ix,ip,npxp)]* pdQdy[0][jp];
               //spwrk1[ADDR(ix,is,npwrk1)]+= spxp[ADDR(ix,ip,npxp)]* pdQdy[1][jp];
               spwrk0[ADDR(ix,is,npwrk0)]+= spxp[ADDR(ix,ip,npxp)]* spdQdy[ADDR(0,jp,npdQdy)];
               spwrk1[ADDR(ix,is,npwrk1)]+= spxp[ADDR(ix,ip,npxp)]* spdQdy[ADDR(1,jp,npdQdy)];
           }
        }
     }
  }

   void cCosystem::dxdl_gpu( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pdQdy, cAu3xView<Real>& pwrk0, cAu3xView<Real>& pwrk1 )
  {
      Int ip,jp,ix,is;

      Int npxp, npwrk0, npwrk1, npdQdy;
      Real *spxp, *spwrk0, *spwrk1, *spdQdy;
      Int npisp, *spisp;

      npxp = pxp.get_dim1();
      npwrk0 = pwrk0.get_dim1();
      npwrk1 = pwrk1.get_dim1();
      npisp = pisp.get_dim1();
      npdQdy = pdQdy.get_dim1();

      spxp = pxp.get_data(); 
      spwrk0 = pwrk0.get_data();
      spwrk1 = pwrk1.get_data();
      spisp = pisp.get_data();
      spdQdy = pdQdy.get_data();

      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       present(spisp[0:npc*npisp],spwrk0[0:npwrk0*nx],spwrk1[0:npwrk1*nx],spxp[0:npxp*nx],spdQdy[0:npdQdy*3],this) \
       default(none)
      for( is=iss;is<ise;is++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            for( ix=0;ix<nx;ix++ )
           {
               //ip= pisp[jp][is];
               ip= spisp[ADDR(jp,is,npisp)];
               //spwrk0[ADDR(ix,is,npwrk0)]+= spxp[ADDR(ix,ip,npxp)]* pdQdy[0][jp];
               //spwrk1[ADDR(ix,is,npwrk1)]+= spxp[ADDR(ix,ip,npxp)]* pdQdy[1][jp];
               spwrk0[ADDR(ix,is,npwrk0)]+= spxp[ADDR(ix,ip,npxp)]* spdQdy[ADDR(0,jp,npdQdy)];
               spwrk1[ADDR(ix,is,npwrk1)]+= spxp[ADDR(ix,ip,npxp)]* spdQdy[ADDR(1,jp,npdQdy)];
           }
        }
     }
      #pragma acc exit data copyout(this)
  }

   void cCosystem::dxdl( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pdQdy, cAu3xView<Real>& pwrk0, cAu3xView<Real>& pwrk1, cAu3xView<Real>& pwrk2 )
  {
      Int ip,jp,ix,is;

      Int npxp, npwrk0, npwrk1, npwrk2, npdQdy;
      Real *spxp, *spwrk0, *spwrk1, *spwrk2, *spdQdy;
      Int npisp, *spisp;

      npxp = pxp.get_dim1();
      npwrk0 = pwrk0.get_dim1();
      npwrk1 = pwrk1.get_dim1();
      npwrk2 = pwrk2.get_dim1();
      npisp = pisp.get_dim1();
      npdQdy = pdQdy.get_dim1();

      spxp = pxp.get_data(); 
      spwrk0 = pwrk0.get_data();
      spwrk1 = pwrk1.get_data();
      spwrk2 = pwrk2.get_data();
      spisp = pisp.get_data();
      spdQdy = pdQdy.get_data();

      for( ix=0;ix<nx;ix++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            for( is=iss;is<ise;is++ )
           {
               //ip= pisp[jp][is];
               ip= spisp[ADDR(jp,is,npisp)];
               //spwrk0[ADDR(ix,is,npwrk0)]+= spxp[ADDR(ix,ip,npxp)]* pdQdy[0][jp];
               //spwrk1[ADDR(ix,is,npwrk1)]+= spxp[ADDR(ix,ip,npxp)]* pdQdy[1][jp];
               //spwrk2[ADDR(ix,is,npwrk2)]+= spxp[ADDR(ix,ip,npxp)]* pdQdy[2][jp];
               spwrk0[ADDR(ix,is,npwrk0)]+= spxp[ADDR(ix,ip,npxp)]* spdQdy[ADDR(0,jp,npdQdy)];
               spwrk1[ADDR(ix,is,npwrk1)]+= spxp[ADDR(ix,ip,npxp)]* spdQdy[ADDR(1,jp,npdQdy)];
               spwrk2[ADDR(ix,is,npwrk2)]+= spxp[ADDR(ix,ip,npxp)]* spdQdy[ADDR(2,jp,npdQdy)];
           }
        }
     }
  }
   void cCosystem::dxdl_gpu( Int iss, Int ise, Int npc, cAu3xView<Int>& pisp, cAu3xView<Real>& pxp, cAu3xView<Real>& pQ, cAu3xView<Real>& pdQdy, cAu3xView<Real>& pwrk0, cAu3xView<Real>& pwrk1, cAu3xView<Real>& pwrk2 )
  {
      Int ip,jp,ix,is;

      Int npxp, npwrk0, npwrk1, npwrk2, npdQdy;
      Real *spxp, *spwrk0, *spwrk1, *spwrk2, *spdQdy;
      Int npisp, *spisp;

      npxp = pxp.get_dim1();
      npwrk0 = pwrk0.get_dim1();
      npwrk1 = pwrk1.get_dim1();
      npwrk2 = pwrk2.get_dim1();
      npisp = pisp.get_dim1();
      npdQdy = pdQdy.get_dim1();

      spxp = pxp.get_data(); 
      spwrk0 = pwrk0.get_data();
      spwrk1 = pwrk1.get_data();
      spwrk2 = pwrk2.get_data();
      spisp = pisp.get_data();
      spdQdy = pdQdy.get_data();

      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       present(spisp[0:npc*npisp],spwrk0[0:npwrk0*nx],spwrk1[0:npwrk1*nx],spwrk2[0:npwrk2*nx],spxp[0:npxp*nx],spdQdy[0:npdQdy*3],this) \
       default(none)
      for( is=iss;is<ise;is++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            for( ix=0;ix<nx;ix++ )
           {
               //ip= pisp[jp][is];
               ip= spisp[ADDR(jp,is,npisp)];
               //spwrk0[ADDR(ix,is,npwrk0)]+= spxp[ADDR(ix,ip,npxp)]* pdQdy[0][jp];
               //spwrk1[ADDR(ix,is,npwrk1)]+= spxp[ADDR(ix,ip,npxp)]* pdQdy[1][jp];
               //spwrk2[ADDR(ix,is,npwrk2)]+= spxp[ADDR(ix,ip,npxp)]* pdQdy[2][jp];
               spwrk0[ADDR(ix,is,npwrk0)]+= spxp[ADDR(ix,ip,npxp)]* spdQdy[ADDR(0,jp,npdQdy)];
               spwrk1[ADDR(ix,is,npwrk1)]+= spxp[ADDR(ix,ip,npxp)]* spdQdy[ADDR(1,jp,npdQdy)];
               spwrk2[ADDR(ix,is,npwrk2)]+= spxp[ADDR(ix,ip,npxp)]* spdQdy[ADDR(2,jp,npdQdy)];
           }
        }
     }
      #pragma acc exit data delete(this)
  }

   void cCosystem::bcoor( Int ips, Int ipe, Real *x[], Real *bx[] )
  {
      Int ip,ix;
      Real x0,x1,x2,r;
      for( ip=ips;ip<ipe;ip++ )
     {
         x0= x[0][ip];
         x1= x[1][ip];
         x2= x[2][ip];
         r= x1*x1+ x2*x2;
         r= sqrt(r);
         bx[0][ip]= x0;
         bx[1][ip]= r;
         bx[2][ip]= atan2(x1,x2);
     }
  }

   void cCosystem::bcoor( Int ips, Int ipe, cAu3xView<Real>& x, cAu3xView<Real>& bx )
  {
      Int ip,ix;
      Real x0,x1,x2,r;

      Int nq, nbb;
      Real *sx, *sbx;
 
      nq  = x.get_dim1();
      nbb = bx.get_dim1(); 
      sx  = x.get_data();
      sbx = bx.get_data();

     #pragma acc parallel loop\
      present(sx[0:nx*nq],sbx[0:nx*nbb]) \
      default(none)
      for( ip=ips;ip<ipe;ip++ )
     {
         x0= sx[ADDR(0,ip,nq)];
         x1= sx[ADDR(1,ip,nq)];
         x2= sx[ADDR(2,ip,nq)];
         r= x1*x1+ x2*x2;
         r= sqrt(r);
         sbx[ADDR(0,ip,nbb)]= x0;
         sbx[ADDR(1,ip,nbb)]= r;
         sbx[ADDR(2,ip,nbb)]= atan2(x1,x2); 
     }
  }

   void cCosystem::zvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipq ) ip= ipq[0][jp];
         x0= x[0][jp];
         x1= x[1][jp];
         x2= x[2][jp];
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r;
         sth= x1/r;
         q[0][ip]= bq[0][jp];
         Real tmp= bq[1][jp];
         q[1][ip]= sth*tmp +cth*bq[2][jp];
         q[2][ip]= cth*tmp -sth*bq[2][jp];
     }
  }

   void cCosystem::zvel( Int ips, Int ipe, Int *ipq[], cAu3xView<Real>& x, cAu3xView<Real>& bq, cAu3xView<Real>& q )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r; 
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipq ) ip= ipq[0][jp];
         x0= x(0,jp);
         x1= x(1,jp);
         x2= x(2,jp);
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r;
         sth= x1/r;
         q(0,ip)= bq(0,jp);
         Real tmp= bq(1,jp);
         q(1,ip)= sth*tmp +cth*bq(2,jp);
         q(2,ip)= cth*tmp -sth*bq(2,jp);
     }
  }

   void cCosystem::zvel( Int ips, Int ipe, cAu3xView<Real>& x, cAu3xView<Real>& bq, cAu3xView<Real>& q )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      Int n_x, n_bq, n_q;
      Real *sx, *sbq, *sq;

      n_x = x.get_dim1();
      n_bq= bq.get_dim1();
      n_q = q.get_dim1();

      sx = x.get_data();
      sbq= bq.get_data();
      sq = q.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop\
      present(sx[0:nx*n_x], sq[0:nv*n_q], sbq[0:nv*n_bq],this) \
      default(none)
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         x0= sx[ADDR(0,jp,n_x)];
         x1= sx[ADDR(1,jp,n_x)];
         x2= sx[ADDR(2,jp,n_x)];
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r;
         sth= x1/r;
         sq[ADDR(0,ip,n_q)]= sbq[ADDR(0,jp, n_bq)];
         Real tmp= sbq[ADDR(1,jp,n_bq)];
         sq[ADDR(1,ip,n_q)]= sth*tmp +cth*sbq[ADDR(2,jp, n_bq)];
         sq[ADDR(2,ip,n_q)]= cth*tmp -sth*sbq[ADDR(2,jp, n_bq)];
     }
     #pragma acc exit data delete(this)
  }

