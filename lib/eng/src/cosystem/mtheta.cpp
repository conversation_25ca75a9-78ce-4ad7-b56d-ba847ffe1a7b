   using namespace std;

#  include <cosystem/mtheta.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Feb 16 18:56:03 GMT 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         streamsurface coordinate system

   cMthetaCosystem::cMthetaCosystem()
  {
      nx= 2;
//    ssf= NULL;
  }

/* void cMthetaCosystem::section( cSection *sec )
  {

      Real *m,*r,*d;
      Real *wrk;
      Real *ms[6];
      Real  r1,d1,dm1,dm,dr,dx;

      Int   i,np,nb;

      nb= sec->getnb();
      ptch= pi2/(Real)nb;
      np= sec->getNp();
      m=  sec->getMp(); // m'
      r=  sec->getRp();
      d=  sec->getDp();

      wrk= new Real[6*np];
      subv( 6,np, wrk,ms );
      ms[0][0]= 0.;     // x ( should start from a given value )
      ms[1][0]= r[0];
      ms[2][0]= d[0];
      ms[3][0]= 0.;     // S dm (the parametrisation  is against m' )
      ms[4][0]= 0.;     // S d dm
      ms[5][0]= 0.;     // S rd dm
      for( i=1;i<np;i++ )
     {
         r1= 0.5*( r[i]+r[i-1] );
         d1= 0.5*( d[i]+d[i-1] );

         dm1= m[i]-m[i-1];

         dm= r1*dm1;         
         dr= r[i]-r[i-1];
         dx= dm*dm- dr*dr;
         dx= sqrt(dx);    // assume the streamsurface never turns back
 
         ms[0][i]= ms[0][i-1]+ dx;
         ms[1][i]= r[i];
         ms[2][i]= d[i];
         ms[3][i]= ms[3][i-1]+ dm;
         ms[4][i]= ms[4][i-1]+ d1*dm;
         ms[5][i]= ms[5][i-1]+ r1*d1*dm;
     }

      ssf= new cSpline( 0,np,m, 6,wrk );
      delete[] wrk;
  }*/

   void cMthetaCosystem::validate( Int Nx )
  { 
      cout << "mtheta cosystem quarantined\n";
      exit(0);
      nx= Nx;
      if( nx != 2 )
     {
         cout << "only than 2 coordinates in mtheta\n";
         std::exit(1); 
     };
      nvel= 2;
      valid=true;
  }

   void cMthetaCosystem::coffset( Int np, Real f, Real *x )
  {
      Int ip;
      Real *px[3];
      if( ptch != 0 )
     {
         subv( nx,np, x, px );
         for( ip=0;ip<np;ip++ )
        {
            px[1][ip]+= f*ptch;
        }
     }
  }

   void cMthetaCosystem::frame( Int ips, Int ipe, Real omega, Real *xp[], Real *xdp[] )
  {
      Int   ip;
      cout << "NOT READY YET\n";
      std::exit(0);
  }

   void cMthetaCosystem::frame( Int ips, Int ipe, Real omega, cAu3xView<Real>& xp, cAu3xView<Real>& xdp )
  {
      Int   ip;
      cout << "NOT READY YET\n";
      std::exit(0);
  }

   void cMthetaCosystem::accel( Int ips, Int ipe, Real omega, Real *xq[], Real *q[], Real *a[] )
  {
      Int   ip;
      cout << "NOT READY YET\n";
      std::exit(0);
  }

   void cMthetaCosystem::daccel( Int ips, Int ipe, Real omega, Real *xq[], Real *q[], Real *dq[], Real *a[] )
  {
      Int   ip;
      cout << "NOT READY YET\n";
      std::exit(0);
  }

   void cMthetaCosystem::bcoor( Int ips, Int ipe, Real *x[], Real *bx[] )
  {
      cout << "CANT DO THIS YET\n";
      std::exit(0);
  }
   void cMthetaCosystem::bcoor( Int ips, Int ipe, Int *ipx[], Real *x[], Real *bx[] )
  {
      cout << "CANT DO THIS YET\n";
      std::exit(0);
  }
   void cMthetaCosystem::bvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *q[], Real *bq[] )
  {
      cout << "CANT DO THIS YET\n";
      std::exit(0);
  }
   void cMthetaCosystem::bvel( Int ips, Int ipe, cAu3xView<Int>& ipq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& bq )
  {
      cout << "CANT DO THIS YET\n";
      std::exit(0);
  }

   void cMthetaCosystem::dxdl( Int iss, Int ise, Int npc, Int *pisp[], Real *pxp[], Real *pQ, Real *pdQdy[], Real *pwrk[] )
  {
      Int ip,jp,ix,is;
      Real r,d,t,dm,m;
      Real wrk[6],dwrk[6];

      Real *swrk;
      swrk= new Real[ise];
      for( is=iss;is<ise;is++ )
     {
         swrk[is]= 0.;
     }

      for( jp=0;jp<npc;jp++ )
     {
         for( is=iss;is<ise;is++ )
        {
            ip= pisp[jp][is];        
            m= pxp[0][ip];
            t= pxp[1][ip];
//          ssf->interp( m, wrk,dwrk ); 
            r = wrk[1];
            d = wrk[2];
            swrk[is]+= pQ[jp]*r*d;
        }
     }

      for( jp=0;jp<npc;jp++ )
     {
         for( is=iss;is<ise;is++ )
        {
            ip= pisp[jp][is];        
            m= pxp[0][ip];
            t= pxp[1][ip];
//          ssf->interp( m, wrk,dwrk ); 
            dm= wrk[4];
//          pwrk[0][is]+= dm*         pdQdy[0][jp];
//          pwrk[1][is]+= swrk[is]*t* pdQdy[0][jp];
            pwrk[0][is]+= m* pdQdy[0][jp];
            pwrk[1][is]+= t* pdQdy[0][jp];
        }
     }
      delete[] swrk;
  }

   void cMthetaCosystem::dxdl( Int iss, Int ise, Int npc, Int *pisp[], Real *pxp[], Real *pQ, Real *pdQdy[], 
                               Real *pwrk0[], Real *pwrk1[] )
  {
      Int ip,jp,ix,is;
      Real r,d,t,dm,m;
      Real wrk[6],dwrk[6];

      Real *swrk;
      swrk= new Real[ise];
      for( is=iss;is<ise;is++ )
     {
         swrk[is]= 0.;
     }

      for( jp=0;jp<npc;jp++ )
     {
         for( is=iss;is<ise;is++ )
        {
            ip= pisp[jp][is];        
            m= pxp[0][ip];
            t= pxp[1][ip];
//          ssf->interp( m, wrk,dwrk ); 
            r = wrk[1];
            d = wrk[2];
            swrk[is]+= pQ[jp]*r*d;
        }
     }

      for( jp=0;jp<npc;jp++ )
     {
         for( is=iss;is<ise;is++ )
        {
            ip= pisp[jp][is];        
            m= pxp[0][ip];
            t= pxp[1][ip];
//          ssf->interp( m, wrk,dwrk ); 
            dm= wrk[4];
//          pwrk[0][is]+= dm*         pdQdy[0][jp];
//          pwrk[1][is]+= swrk[is]*t* pdQdy[0][jp];
            pwrk0[0][is]+= m* pdQdy[0][jp];
            pwrk0[1][is]+= t* pdQdy[0][jp];
            pwrk1[0][is]+= m* pdQdy[1][jp];
            pwrk1[1][is]+= t* pdQdy[1][jp];
        }
     }
      delete[] swrk;
  }

