using namespace std;


# include <cosystem/xr.h>

   void cXrCosystem::validate( Int Nx )
  { 
      nx= Nx;
      nvel=3;
      if( nx != 2 )
     { 
         cout << "only 2 coordinates in xr\n";
         std::exit(1); 
     };
      valid=true;
  }

   void cXrCosystem::frame( Int ips, Int ipe, Real omega, Real *xp[], Real *xdp[] )
  {
      Int   ip;
      Real  r;
      for( ip=ips;ip<ipe;ip++ )
     {
         r= xp[1][ip]; 
         xdp[2][ip]+= r*omega;
     }
  }

   void cXrCosystem::frame( Int ips, Int ipe, Real omega, cAu3xView<Real>& xp, cAu3xView<Real>& xdp )
  {
      Int   ip;
      Real  r;
      for( ip=ips;ip<ipe;ip++ )
     {
         r= xp(1,ip);
         xdp(2,ip)+= r*omega;
     }
  }

   void cXrCosystem::accel( Int ips, Int ipe, Real omega, Real *xq[], Real *q[], Real *a[] )
  {
      Int   ip;
      Real  r,ur,ut;
      for( ip=ips;ip<ipe;ip++ )
     {
         r= xq[1][ip];
         ur= q[1][ip];
         ut= q[2][ip];
         a[0][ip]= 0.;
         a[1][ip]= ut*ut/r;
         a[2][ip]=-ur*ut/r;
     }
  }

   void cXrCosystem::daccel( Int ips, Int ipe, Real omega, Real *xq[], Real *q[], Real *dq[], Real *a[] )
  {
      Int   ip;
      Real  r,ur,ut,dur,dut;
      for( ip=ips;ip<ipe;ip++ )
     {
         r= xq[1][ip];
         ur= q[1][ip];
         ut= q[2][ip];
         dur= dq[1][ip];
         dut= dq[2][ip];
         a[0][ip]= 0.;
         a[1][ip]= 2.*dut*ut/r;
         a[2][ip]=-(dur*ut+ ur*dut)/r;
     }
  }

   void cXrCosystem::bcoor( Int ips, Int ipe, Real *x[], Real *bx[] )
  {
      Int ip;
      for( ip=ips;ip<ipe;ip++ )
     {
         bx[0][ip]= x[0][ip];
         bx[1][ip]= x[1][ip];
     }
  }

   void cXrCosystem::bcoor( Int ips, Int ipe, Int *ipx[], Real *x[], Real *bx[] )
  {
      Int ip,jp;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= ipx[0][jp]; 
         bx[0][jp]= x[0][ip];
         bx[1][jp]= x[1][ip];
     }
  }

   void cXrCosystem::bcoor( Int ips, Int ipe, cAu3xView<Real>& x, cAu3xView<Real>& bx )
  {
      Int ip;
      for( ip=ips;ip<ipe;ip++ )
     {
         bx(0,ip)= x(0,ip);
         bx(1,ip)= x(1,ip);
     }
  }

   void cXrCosystem::dxdl( Int iss, Int ise, Int npc, Int *pisp[], Real *pxp[], Real *pQ, Real *pdQdy[], Real *pwrk[] )
  {
      Int ip,jp,ix,is,kp;
      Real r;
      for( jp=0;jp<npc;jp++ )
     {
         for( is=iss;is<ise;is++ )
        {
            ip= pisp[jp][is];        
            r=0;
            for( kp=0;kp<npc;kp++ )
           {
               r+= pQ[kp]*pxp[1][pisp[kp][is]];
           }
            pwrk[0][is]+= pi2*r*pxp[0][ip]* pdQdy[0][jp];
            pwrk[1][is]+= pi2*r*pxp[1][ip]* pdQdy[0][jp];
        }
     }
  }

   void cXrCosystem::dxdl( Int iss, Int ise, Int npc, Int *pisp[], Real *pxp[], Real *pQ, Real *pdQdy[], Real *pwrk0[], Real *pwrk1[] )
  {
      Int ip,jp,is,kp;
      Real r;
      for( jp=0;jp<npc;jp++ )
     {
         for( is=iss;is<ise;is++ )
        {
            ip= pisp[jp][is];        
            r=0;
            for( kp=0;kp<npc;kp++ )
           {
               r+= pQ[kp]*pxp[1][pisp[kp][is]];
           }
            pwrk0[0][is]+=       pxp[0][ip]* pdQdy[0][jp];
            pwrk1[0][is]+=       pxp[0][ip]* pdQdy[1][jp];
            pwrk0[1][is]+= pi2*r*pxp[1][ip]* pdQdy[0][jp];
            pwrk1[1][is]+= pi2*r*pxp[1][ip]* pdQdy[1][jp];
        }
     }
  }

   void cXrCosystem::cvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipq ) ip= ipq[0][jp];
         x0= x[0][jp];
         x1= x[1][jp];
         x2= x[2][jp];
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r;
         sth= x1/r;
         q[0][ip]= bq[0][jp];
         Real tmp= bq[1][jp];
         q[1][ip]= sth*tmp +cth*bq[2][jp];
         q[2][ip]= cth*tmp -sth*bq[2][jp];
     }
  }

   void cXrCosystem::yvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipq ) ip= ipq[0][jp];
         x0= x[0][jp];
         x1= x[1][jp];
         x2= x[2][jp];
         r= x1*x1+ x2*x2;
         r= sqrt(r)+small;
         cth= x2/r;
         sth= x1/r;
         q[0][ip]= bq[0][jp];
         Real tmp= bq[1][jp];
         q[1][ip]= sth*tmp +cth*bq[2][jp];
         q[2][ip]= cth*tmp -sth*bq[2][jp];
     }
  }

   void cXrCosystem::bvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *q[], Real *bq[] )
  {
      Int jp,ip;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= ipq[0][jp]; 
         bq[0][jp]= q[0][ip];
         bq[1][jp]= q[1][ip];
         bq[2][jp]= q[2][ip];
     }
  }

   void cXrCosystem::bvel( Int ips, Int ipe, cAu3xView<Int>& ipq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& bq )
  {
      Int jp,ip;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= ipq(jp); 
         bq(0,jp)= q(0,ip);
         bq(1,jp)= q(1,ip);
         bq(2,jp)= q(2,ip);
     }
  }

   void cXrCosystem::zvel( Int ips, Int ipe, Int *ipq[], Real *x[], Real *bq[], Real *q[] )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipq ) ip= ipq[0][jp];
         q[0][ip]= bq[0][jp];
         q[1][ip]= bq[1][jp];
         q[2][ip]= bq[2][jp];
     }
  }

   void cXrCosystem::zvel( Int ips, Int ipe, Int *ipq[], cAu3xView<Real>& x, cAu3xView<Real>& bq, cAu3xView<Real>& q )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipq ) ip= ipq[0][jp];
         q(0,ip)= bq(0,jp);
         q(1,ip)= bq(1,jp);
         q(2,ip)= bq(2,jp);
     }
  }

   void cXrCosystem::zvel( Int ips, Int ipe, cAu3xView<Real>& x, cAu3xView<Real>& bq, cAu3xView<Real>& q )
  {
      Int jp,ip,ix;
      Real x0,x1,x2,cth,sth,r;

      Int n_x, n_bq, n_q;
      Real *sx, *sbq, *sq;

      n_x = x.get_dim1();
      n_bq= bq.get_dim1();
      n_q = q.get_dim1();

      sx = x.get_data();
      sbq= bq.get_data();
      sq = q.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop\
      present(sx[0:nx*n_x], sq[0:nv*n_q], sbq[0:nv*n_bq],this) \
      default(none)
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         sq[ADDR(0,ip,n_q)]= sbq[ADDR(0,jp,n_bq)];
         sq[ADDR(1,ip,n_q)]= sbq[ADDR(1,jp,n_bq)];
         sq[ADDR(2,ip,n_q)]= sbq[ADDR(2,jp,n_bq)];
     }
     #pragma acc exit data delete(this)

  }

   void cXrCosystem::ccoor( Int ips, Int ipe, Int *ipx[], Real *x[], Real *bx[] )
  {
      Int ip,jp,ix;
      Real x0,x1,x2,r;
//    cout << "CALL XRCOSYSTEM CCOOR <==============================\n";
      for( jp=ips;jp<ipe;jp++ )
     {
         ip= jp;
         if( ipx )
        { ip= ipx[0][jp]; };
         bx[0][jp]= x[0][ip];
         bx[2][jp]= x[1][ip];
         bx[1][jp]= 0.;
     }
  }
