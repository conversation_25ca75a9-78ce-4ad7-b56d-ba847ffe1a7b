   using namespace std;

#  include <cosystem/cosystems.h>

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Tue Feb 16 18:14:49 GMT 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         abstract coordinate system


   cCosystem *newcosystem( Int ik )
  {
      cCosystem *tmp=NULL;
      switch(ik)
     {
         case(cosystem_cart):
        { 
            tmp= new cCartCosystem(); 
            break; 
        };
         case(cosystem_ann):
        { 
            tmp= new cAnnCosystem(); 
            break; 
        };
         case(cosystem_xr):
        { 
            tmp= new cXrCosystem(); 
            break; 
        };
         case(cosystem_mtheta):
        { 
            tmp= new cMthetaCosystem(); 
            break; 
        };
         case(cosystem_cart2):
        { 
            tmp= new cCartCosystem2(); 
            break; 
        };
     }
      return tmp;
  }

