   using namespace std;

#  include <iostream>
#  include <cstdlib>
#  include <lin/small.h>

   void luf2( Real *v0, Real *v1, Real *w0, Real *w1, Real *det )
  {

      Real d;

    (*det)= 0;
      idv2( v0,w0 ); w0[2]= 1./norminf2( w0 ); sclv2( w0[2],w0 );
      idv2( v1,w1 ); w1[2]= 1./norminf2( w1 ); sclv2( w1[2],w1 );

      d= w0[0];
      if( fabs( d ) > small )
     {
         w0[1]/= -w0[0];
         w1[1]+=  w0[1]*w1[0];

         d*= w1[1];
         if( fabs( d ) > small )
        {

            d/= w0[2];
            d/= w1[2];

          (*det)= d;

        }
     }
  }

   void luf2( Real *v0, Real *v1, Real *w0, Real *w1, Int *ipiv, Real *det )
  {

      Real d;
      Int  i0,i1;

      i0=0;
      i1=1;
      ipiv[0]=-1;
      ipiv[1]=-1;

    (*det)= 0;
      idv2( v0,w0 ); w0[2]= 1./norminf2( w0 ); sclv2( w0[2],w0 );
      idv2( v1,w1 ); w1[2]= 1./norminf2( w1 ); sclv2( w1[2],w1 );

      if( fabs( w0[i1] ) > fabs( w0[i0] ) ){ swap( &i0,&i1 ); };
      d= w0[i0];
      if( fabs( d ) > small )
     {
         w0[i1]/= -w0[i0];
         w1[i1]+=  w0[i1]*w1[i0];

         d*= w1[i1];
         if( fabs( d ) > small )
        {

            d/= w0[2];
            d/= w1[2];

          (*det)= d;

            ipiv[0]=i0;
            ipiv[1]=i1;

        }
     }
  }

   void lus2( Real *w0, Real *w1, Real *x, Real *r )
  {

      idv2(  r,x  ); 

      x[1]+= x[0]*w0[1];
      x[1]/= w1[1];

      x[0]-= x[1]*w1[0];
      x[0]/= w0[0];

      x[0]*= w0[2];
      x[1]*= w1[2];

  }

   void lus2( Real *w0, Real *w1, Int *ipiv, Real *x, Real *r )
  {
      Int     i0,i1;

      i0= ipiv[0];
      i1= ipiv[1];

      x[0]= r[i0];
      x[1]= r[i1];

      x[1]+= x[0]*w0[i1];
      x[1]/= w1[i1];

      x[0]-= x[1]*w1[i0];
      x[0]/= w0[i0];

      x[0]*= w0[2];
      x[1]*= w1[2];

  }

   void lus2t( Real *w0, Real *w1, Real *x, Real *r )
  {

      idv2(  r,x  ); 

      x[0]*= w0[2];
      x[1]*= w1[2];

      x[0]/= w0[0];
      x[1]-= w1[0]*x[0];

      x[1]/= w1[1];
      x[0]+= x[1]*w1[0];

  }

   void lus2t( Real *w0, Real *w1, Int *ipiv, Real *x, Real *r )
  {

      Int i0,i1;

      i0= ipiv[0];
      i1= ipiv[1];

      x[i0]= r[0];
      x[i1]= r[1];

      x[i0]*= w0[2];
      x[i1]*= w1[2];

      x[i0]/= w0[i0];
      x[i1]-= w1[i0]*x[i0];

      x[i1]/= w1[i1];
      x[i0]+= x[i1]*w1[i0];

  }

   void luf3( Real *v0, Real *v1, Real *v2, Real *w0, Real *w1, Real *w2, Real *det )
  {

      Real d;

      cout << "WARNING: USE NON-PIVOTING LUF/LUS AT OWN RISK\n";

    (*det)= 0;
      idv3( v0,w0 ); w0[3]= 1./norminf3( w0 ); sclv3( w0[3],w0 );
      idv3( v1,w1 ); w1[3]= 1./norminf3( w1 ); sclv3( w1[3],w1 );
      idv3( v2,w2 ); w2[3]= 1./norminf3( w2 ); sclv3( w2[3],w2 );

      d= w0[0];
      if( fabs( d ) > small )
     {
         w0[1]/= -w0[0];
         w1[1]+=  w0[1]*w1[0];
         w2[1]+=  w0[1]*w2[0];

         w0[2]/= -w0[0];
         w1[2]+=  w0[2]*w1[0];
         w2[2]+=  w0[2]*w2[0];

         d*= w1[1];
         if( fabs( d ) > small )
        {
            w1[2]/= -w1[1];
            w2[2]+=  w1[2]*w2[1];

            d*= w2[2];
            if( fabs( d ) > small )
           {
               d/= w0[3];
               d/= w1[3];
               d/= w2[3];

             (*det)= d;

           }
        }
     }
  }

   void luf3( Real *v0, Real *v1, Real *v2, Real *w0, Real *w1, Real *w2, Int *ipiv, Real *det )
  {

      Real d;
      Int  i0,i1,i2;

      i0=0;
      i1=1;
      i2=2;

      ipiv[0]=-1;
      ipiv[1]=-1;
      ipiv[2]=-1;

    (*det)= 0;
      idv3( v0,w0 ); w0[3]= 1./norminf3( w0 ); sclv3( w0[3],w0 );
      idv3( v1,w1 ); w1[3]= 1./norminf3( w1 ); sclv3( w1[3],w1 );
      idv3( v2,w2 ); w2[3]= 1./norminf3( w2 ); sclv3( w2[3],w2 );

      if( fabs( w0[i1] ) > fabs( w0[i0] ) ){ swap( &i0,&i1 ); };
      if( fabs( w0[i2] ) > fabs( w0[i0] ) ){ swap( &i0,&i2 ); };
      d= w0[i0];
      if( fabs( d ) > small )
     {
         w0[i1]/= -w0[i0];
         w1[i1]+=  w0[i1]*w1[i0];
         w2[i1]+=  w0[i1]*w2[i0];

         w0[i2]/= -w0[i0];
         w1[i2]+=  w0[i2]*w1[i0];
         w2[i2]+=  w0[i2]*w2[i0];

         if( fabs( w1[i2] ) > fabs( w1[i1] ) ){ swap( &i1,&i2 ); };
         d*= w1[i1];
         if( fabs( d ) > small )
        {
            w1[i2]/= -w1[i1];
            w2[i2]+=  w1[i2]*w2[i1];

            d*= w2[i2];
            if( fabs( d ) > small )
           {
               d/= w0[3];
               d/= w1[3];
               d/= w2[3];

             (*det)= d;

               ipiv[0]= i0;
               ipiv[1]= i1;
               ipiv[2]= i2;

           }
        }
     }
  }

   void lus3( Real *w0, Real *w1, Real *w2, Real *x, Real *r )
  {
      cout << "WARNING: USE NON-PIVOTING LUF/LUS AT OWN RISK\n";
      idv3(  r,x  ); 
      x[1]+= x[0]*w0[1];
      x[2]+= x[0]*w0[2];

      x[2]+= x[1]*w1[2];

      x[2]/= w2[2];

      x[1]-= x[2]*w2[1];
      x[0]-= x[2]*w2[0];

      x[1]/= w1[1];

      x[0]-= x[1]*w1[0];
      x[0]/= w0[0];

      x[0]*= w0[3];
      x[1]*= w1[3];
      x[2]*= w2[3];

  }

   void lus3( Real *w0, Real *w1, Real *w2, Int *ipiv, Real *x, Real *r )
  {
      Int i0,i1,i2;

      i0= ipiv[0];
      i1= ipiv[1];
      i2= ipiv[2];

      x[0]= r[i0];
      x[1]= r[i1];
      x[2]= r[i2];

      x[1]+= x[0]*w0[i1];

      x[2]+= x[0]*w0[i2];
      x[2]+= x[1]*w1[i2];

      x[2]/= w2[i2];

      x[1]-= x[2]*w2[i1];
      x[0]-= x[2]*w2[i0];

      x[1]/= w1[i1];

      x[0]-= x[1]*w1[i0];
      x[0]/= w0[i0];

      x[0]*= w0[3];
      x[1]*= w1[3];
      x[2]*= w2[3];

  }

   void lus3t( Real *w0, Real *w1, Real *w2, Real *x, Real *r )
  {
      cout << "WARNING: USE NON-PIVOTING LUF/LUS AT OWN RISK\n";
      idv3(  r,x  ); 

      x[0]*= w0[3];
      x[1]*= w1[3];
      x[2]*= w2[3];

      x[0]/= w0[0];
      x[1]-= w1[0]*x[0];
      x[2]-= w2[0]*x[0];

      x[1]/= w1[1];
      x[2]-= w2[1]*x[1];

      x[2]/= w2[2];

      x[1]+= w1[2]*x[2];
      x[0]+= w0[2]*x[2];

      x[0]+= w0[1]*x[1];

  }

   void lus3t( Real *w0, Real *w1, Real *w2, Int *ipiv, Real *x, Real *r )
  {
      Int  i0,i1,i2;

      i0=ipiv[0];
      i1=ipiv[1];
      i2=ipiv[2];

      x[i0]= r[0];
      x[i1]= r[1];
      x[i2]= r[2];

      x[i0]*= w0[3];
      x[i1]*= w1[3];
      x[i2]*= w2[3];
   
      x[i0]/= w0[i0];
      x[i1]-= w1[i0]*x[i0];
      x[i2]-= w2[i0]*x[i0];

      x[i1]/= w1[i1];
      x[i2]-= w2[i1]*x[i1];

      x[i2]/= w2[i2];

      x[i1]+= w1[i2]*x[i2];
      x[i0]+= w0[i2]*x[i2];

      x[i0]+= w0[i1]*x[i1];
   
  }

   void qrf23( Real *v0, Real *v1, Real *q0, Real *q1, Real *r )
  {
      idv3( v0,q0 );
      r[0]= norm23( q0 ); sclv3( 1./r[0],q0 );
      r[1]= dot3( q0,v1 );

      q1[0]= v1[0]- r[1]*q0[0];
      q1[1]= v1[1]- r[1]*q0[1];
      q1[2]= v1[2]- r[1]*q0[2];
      r[2]= norm23( q1 ); sclv3( 1./r[2],q1 );   

  }

   void qrs23( Real *q0, Real *q1, Real *r, Real *x, Real *b )
  {
      x[1]= dot3( b,q1 );
      x[1]/= r[2];
      x[0]= dot3( b,q0 );
      x[0]-= r[1]*x[1];
      x[0]/= r[0];
  }


   void jac2( Real *a, Real *c, Real *s )
  {
      Real a00,a01,a11;
      Real t,w;

      a00= a[0];
      a01= a[1];
      a11= a[2];

     *c= 1.;
     *s= 0.;
      if( fabs(a01) > small )
     {
         t= a00-a11;
         t/= a01;
         t*= 0.5;

         w= t*t;
         w+= 1;
         w= sqrt(w);

         if( t > 0 ){ w= -w; };
         t+= w;

        *c= 1+t*t;
        *c= sqrt(*c);
        *c= 1./(*c);

        *s= t*(*c);
     }
 
  }

   void sye2( Real *a, Real *q0, Real *q1, Real *d )
  {
      Real c,s;
      Real b[2];

      jac2( a,&c,&s );

      b[0]= c*a[0]- s*a[1];
      b[1]= c*a[1]- s*a[2];
      d[0]= c*b[0]- s*b[1];

      b[0]= s*a[0]+ c*a[1];
      b[1]= s*a[1]+ c*a[2];
      d[1]= s*b[0]+ c*b[1];

      if( d[0] < d[1] )
     {
         swap( d+0,d+1 ); 
         s= -s;
         swap( &s,&c );
     }
      q0[0]= c;
      q0[1]=-s;
      q1[0]= s;
      q1[1]= c;
  }
