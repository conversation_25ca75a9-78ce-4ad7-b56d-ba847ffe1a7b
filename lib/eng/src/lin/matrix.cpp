using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Fri Jul  3 12:57:08 BST 2009
// Changes History -
// Next Change(s)  -
// Purpose         LU factorisation - development program for AU3X

#  include <mem/proto.h>
#  include <lin/matrix.h>

   void getrf( Int n, Int *iprm[], Real *a )
  {
      Int             i,j,k;

// factorisation

      for( i=0;i<n;i++ )
     {
         for( k=i+1;k<n;k++ )
        {
            a[iprm[i][k]]= a[iprm[i][k]]/a[iprm[i][i]];
        } 
         for( j=i+1;j<n;j++ )
        {
            for( k=i+1;k<n;k++ )
           {
               a[iprm[j][k]]-= a[iprm[j][i]]*a[iprm[i][k]];
           }
        }
     }
     
  }

   void getrs( Int n, Int *iprm[], Real *a, Real *b )
  {
      Int             i,j;

// forward sweep

      for( j=0;j<n;j++ )
     {
         for( i=j+1;i<n;i++ )
        {
            b[i]-= a[iprm[j][i]]*b[j];
        }
     }

// backward sweep

      for( j=n-1;j>=0;j-- )
     {
         b[j]/= a[iprm[j][j]];
         for( i=0;i<j;i++ )
        {
            b[i]-= a[iprm[j][i]]*b[j];
        }
     }
  }

//   void getrf( Int iprm[3][3], Real *a )
//  {
//      Int             i,j,k;
//      Int             n = 3;
//// factorisation
//
//      for( i=0;i<n;i++ )
//     {
//         for( k=i+1;k<n;k++ )
//        {
//            a[iprm[i][k]]= a[iprm[i][k]]/a[iprm[i][i]];
//        } 
//         for( j=i+1;j<n;j++ )
//        {
//            for( k=i+1;k<n;k++ )
//           {
//               a[iprm[j][k]]-= a[iprm[j][i]]*a[iprm[i][k]];
//           }
//        }
//     }
//     
//  }
//
//   void getrs( Int iprm[3][3], Real *a, Real *b )
//  {
//      Int             i,j;
//      Int             n = 3;
//
//// forward sweep
//
//      for( j=0;j<n;j++ )
//     {
//         for( i=j+1;i<n;i++ )
//        {
//            b[i]-= a[iprm[j][i]]*b[j];
//        }
//     }
//
//// backward sweep
//
//      for( j=n-1;j>=0;j-- )
//     {
//         b[j]/= a[iprm[j][j]];
//         for( i=0;i<j;i++ )
//        {
//            b[i]-= a[iprm[j][i]]*b[j];
//        }
//     }
//  }

   void getrf( Int iqs, Int iqe, Int n, Int *iprm[], Real *a[] )
  {
      Int             i,j,k;
      Int             ii,ik,jk,ji;
      Int             iq;

// factorisation

      if( iqe > iqs )
     {
         for( i=0;i<n;i++ )
        {
            ii= iprm[i][i];
            for( k=i+1;k<n;k++ )
           {
               ik= iprm[i][k];
               for( iq=iqs;iq<iqe;iq++ )
              {
                  a[ik][iq]= a[ik][iq]/a[ii][iq];
              }
           } 
            for( j=i+1;j<n;j++ )
           {
               for( k=i+1;k<n;k++ )
              {
                  jk= iprm[j][k];
                  ji= iprm[j][i];
                  ik= iprm[i][k];
                  for( iq=iqs;iq<iqe;iq++ )
                 {
                     a[jk][iq]-= a[ji][iq]*a[ik][iq];
                 }
              }
           }
        }
     }
  }

   void getrs( Int iqs, Int iqe, Int n, Int *iprm[], Real *a[], Real *b[] )
  {
      Int             i,j;
      Int             jj,ji;
      Int             iq;

// forward sweep

      if( iqe > iqs )
     {
         for( j=0;j<n;j++ )
        {
            for( i=j+1;i<n;i++ )
           {
               ji= iprm[j][i];
               for( iq=iqs;iq<iqe;iq++ )
              {
                  b[i][iq]-= a[ji][iq]*b[j][iq];
              }
           }
        }
   
// backward sweep

         for( j=n-1;j>=0;j-- )
        {
            jj= iprm[j][j];
            for( iq=iqs;iq<iqe;iq++ )
           {
               b[j][iq]/= a[jj][iq];
           }
            for( i=0;i<j;i++ )
           {
               ji= iprm[j][i];
               for( iq=iqs;iq<iqe;iq++ )
              {
                  b[i][iq]-= a[ji][iq]*b[j][iq];
              }
           }
        }
     }
  }

   void getrf( Int iqs, Int iqe, Int n, Real *a0, Int nq )
  {
// factorisation

      if( iqe > iqs )
     {
          #pragma acc parallel loop \
          present(a0[0:n*n*nq]) \
          default(none)
          for( Int iq=iqs;iq<iqe;iq++ )
         {
             a0[1*nq+iq]= a0[1*nq+iq]/a0[0*nq+iq];
             a0[2*nq+iq]= a0[2*nq+iq]/a0[0*nq+iq];
             a0[4*nq+iq]-=a0[3*nq+iq]*a0[1*nq+iq];
             a0[5*nq+iq]-=a0[3*nq+iq]*a0[2*nq+iq];
             a0[7*nq+iq]-=a0[6*nq+iq]*a0[1*nq+iq];
             a0[8*nq+iq]-=a0[6*nq+iq]*a0[2*nq+iq];
             a0[5*nq+iq]= a0[5*nq+iq]/a0[4*nq+iq];
             a0[8*nq+iq]-=a0[7*nq+iq]*a0[5*nq+iq];
        
        }
     }
  }

   void getrs( Int iqs, Int iqe, Int n, Real *a, Real *b, Int nv, Int nq )
  {
      Int             iq,i,j,iv;
      Int             jj,ji;

// forward sweep

      if( iqe > iqs )
     {
         #pragma acc parallel loop \
         present(a[0:n*n*nq],b[0:n*nv*nq])
         for( iq=iqs;iq<iqe;iq++ )
        {
           // for(iv=0; iv<nv; iv++)
           //{
           //   // for( j=0;j<n;j++ )
           //   //{
           //   //    for( i=j+1;i<n;i++ )
           //   //   {
           //   //       b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
           //   //   }
           //   //}
           //}
            iv=0;
            j=0;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            j=0;i=j+2;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            j=1;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            iv=1;
            j=0;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            j=0;i=j+2;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            j=1;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            iv=2;
            j=0;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            j=0;i=j+2;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            j=1;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            iv=3;
            j=0;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            j=0;i=j+2;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            j=1;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            iv=4;
            j=0;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            j=0;i=j+2;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            j=1;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
            for(iv=5; iv<nv; iv++)
           {
               j=0;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
               j=0;i=j+2;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
               j=1;i=j+1;  b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
           }

            for(iv=0; iv<nv; iv++)
           {
//               for( j=n-1;j>=0;j-- )
//              {
//   //               jj= iprm[j][j];
//                  b[ADDR(iv,j,iq,nq)]/= a[ADDR(j,j,iq,nq)];
//                  for( i=0;i<j;i++ )
//                 {
//  //                   ji= iprm[j][i];
//                     b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
//                 }
//              }
               j=2;
               b[ADDR(iv,j,iq,nq)]/= a[ADDR(j,j,iq,nq)];
               j=2; i=0;
               b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
               j=2; i=1;
               b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];

               j=1;
               b[ADDR(iv,j,iq,nq)]/= a[ADDR(j,j,iq,nq)];
               j=1; i=0;
               b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];

               j=0;
               b[ADDR(iv,j,iq,nq)]/= a[ADDR(j,j,iq,nq)];
           }
        }
   
//// backward sweep
//
//         for( iq=iqs;iq<iqe;iq++ )
//        {
//            for(iv=0; iv<nv; iv++)
//           {
////               for( j=n-1;j>=0;j-- )
////              {
////   //               jj= iprm[j][j];
////                  b[ADDR(iv,j,iq,nq)]/= a[ADDR(j,j,iq,nq)];
////                  for( i=0;i<j;i++ )
////                 {
////  //                   ji= iprm[j][i];
////                     b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
////                 }
////              }
//               j=2;
//               b[ADDR(iv,j,iq,nq)]/= a[ADDR(j,j,iq,nq)];
//               j=2; i=0;
//               b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
//               j=2; i=1;
//               b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
//
//               j=1;
//               b[ADDR(iv,j,iq,nq)]/= a[ADDR(j,j,iq,nq)];
//               j=1; i=0;
//               b[ADDR(iv,i,iq,nq)]-= a[ADDR(j,i,iq,nq)]*b[ADDR(iv,j,iq,nq)];
//
//               j=0;
//               b[ADDR(iv,j,iq,nq)]/= a[ADDR(j,j,iq,nq)];
//           }
//        }
     }
  }
