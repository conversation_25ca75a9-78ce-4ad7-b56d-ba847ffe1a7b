   using namespace std;

#  include <iostream>
#  include <cstdlib>
#  include <lin/vect.h>

   Real norm12( Real *x )
  {
      Real val;
      val=  fabs( x[0] );
      val+= fabs( x[1] );
      return val ;
  }

   Real norminf2( Real *x )
  {
      Real val;
      val= abs( x[0] );
      val= max( abs( x[1] ), val );
      return val ;
  }

   Real norminf3( Real *x )
  {
      Real val=0;
      Real y1,y2,y3;
      y1= x[0];
      y2= x[1];
      y3= x[2]; //cout << y3 << "\n";
      val=      abs(x[0]);
      val= max( abs(x[1]), val );
      val= max( abs(x[2]), val );
      return val ;
  }

   Real norm13( Real *x )
  {
      Real val;
      val= abs( x[0] );
      val+= abs( x[1] );
      val+= abs( x[2] );
      return val ;
  }

   void sub2( Real *y1, Real *y2, Real *dy )
  { 
      dy[0]= y1[0]-y2[0]; 
      dy[1]= y1[1]-y2[1]; 
  };
   void add2( Real *y1, Real *y2, Real *dy )
  { 
      dy[0]= y1[0]+y2[0]; 
      dy[1]= y1[1]+y2[1]; 
  };

   void idv2( Real *y1, Real *y2 )
  { 
      y2[0]=y1[0]; y2[1]=y1[1]; 
  };

   void rotv2( Real *xr, Real cth, Real sth, Real *x )
  {
      Real dx[2];
      dx[0]= x[0]-xr[0];
      dx[1]= x[1]-xr[1];
      x[0]= xr[0]+ cth*dx[0]- sth*dx[1];
      x[1]= xr[1]+ sth*dx[0]+ cth*dx[1];
  }

   void rotv2( Int ist, Int ien, Real *xr, Real cth, Real sth, Real *x[] )
  {
      Int i;
      Real dx[2];
      for( i=ist;i<ien;i++ )
     {
         dx[0]= x[0][i]-xr[0];
         dx[1]= x[1][i]-xr[1];
         x[0][i]= xr[0]+ cth*dx[0]- sth*dx[1];
         x[1][i]= xr[1]+ sth*dx[0]+ cth*dx[1];
     }    
  }

   void rotv2( Real *xr, Real dt, Real *x )
  {
      Real cth,sth;
      cth= cos(dt);
      sth= sin(dt);
      rotv2( xr,cth,sth, x );
  }

   void rotv2( Int ist, Int ien, Real *xr, Real dt, Real *x[] )
  {
      Real cth,sth;
      cth= cos(dt);
      sth= sin(dt);
      rotv2( ist,ien, xr,cth,sth, x );
  }
 
   void mirv2( Real *xr, Real *l0, Real *x )
  {
      Real t,l2,l[2],d[2],n[2]; 
      l2= l0[0]*l0[0]+ l0[1]*l0[1]; 
      if( l2 > small )
     {
         l2= sqrt(l2);
         l[0]= l0[0]/l2;
         l[1]= l0[1]/l2;
         d[0]= x[0]-xr[0];
         d[1]= x[1]-xr[1];
         t= d[0]*l[0]+ d[1]*l[1];
         n[0]= d[0]- t*l[0];
         n[1]= d[1]- t*l[1];
         x[0]= xr[0]+ t*l[0]- n[0];
         x[1]= xr[1]+ t*l[1]- n[1];
     }
  }

   void mirv2( Int ist, Int ien, Real *xr, Real *l0, Real *x[] )
  {
      Int i;
      Real t,l2,l[2],d[2],n[2]; 
      l2= l0[0]*l0[0]+ l0[1]*l0[1]; 
      if( l2 > small )
     {
         l2= sqrt(l2);
         l[0]= l0[0]/l2;
         l[1]= l0[1]/l2;
         for( i=ist;i<ien;i++ )
        {
            d[0]= x[0][i]-xr[0];
            d[1]= x[1][i]-xr[1];
            t= d[0]*l[0]+ d[1]*l[1];
            n[0]= d[0]- t*l[0];
            n[1]= d[1]- t*l[1];
            x[0][i]= xr[0]+ t*l[0]- n[0];
            x[1][i]= xr[1]+ t*l[1]- n[1];
        }
     }
  }

   Real dot2( Real *v0, Real *v1 )
  {
      Real val;
      val= v0[0]*v1[0]+ v0[1]*v1[1];
      return val;
  }

   Real vec2( Real *v0, Real *v1 )
  {
      Real val;
      val= v0[0]*v1[1]- v0[1]*v1[0];
      return val;
  }

   void mirv2( Real *l0, Real *x )
  {
      Real l,y[2],n0[2];
      l= l0[0]*l0[0]+ l0[1]*l0[1];
      l= sqrt(l);
      if( l > small )
     {
         l0[0]/= l;
         l0[1]/= l;
         idv2( l0,n0 );
         rot90( n0 );
         y[0]= dot2( l0,x );
         y[1]= dot2( n0,x );
         x[0]= l0[0]*y[0]-n0[0]*y[1];
         x[1]= l0[1]*y[0]-n0[1]*y[1];
     }
  }

   void rotv2( Real cdt, Real sdt, Real *x )
  {
      Real tmp;
      tmp= x[0];
      x[0]= cdt*tmp- sdt*x[1];
      x[1]= sdt*tmp+ cdt*x[1];
  }

   void rotv2( Real dt, Real *x )
  {
      rotv2( cos(dt),sin(dt), x );
  }

   void sclv2( Real f, Real *v )
  {
      v[0]*= f;
      v[1]*= f;
  }

   void sclv3( Real f, Real *v )
  {
      v[0]*= f;
      v[1]*= f;
      v[2]*= f;
  }

   void sclv2( Real *x0, Real f, Real *x )
  {
      Real dx[2]; 
      dx[0]= x[0]-x0[0];
      dx[1]= x[1]-x0[1];
      x[0]= x0[0]+ f*dx[0];
      x[1]= x0[1]+ f*dx[1];
  }

   void sclv2( Int ist, Int ien, Real f, Real *v[] )
  {
      Int i;
      for( i=ist;i<ien;i++ )
     {
         v[0][i]*= f;
         v[1][i]*= f;
     }
  }

   void sclv2( Int ist, Int ien, Real *x0, Real f, Real *x[] )
  {
      Int i;
      Real dx[2]; 
      for( i=ist;i<ien;i++ )
     {
         dx[0]= x[0][i]-x0[0];
         dx[1]= x[1][i]-x0[1];
         x[0][i]= x0[0]+ f*dx[0];
         x[1][i]= x0[1]+ f*dx[1];
     }
  }


   void norml( Int n, Real *x )
  {
      Int i;
      Real l=0;
      for( i=0;i<n;i++ )
     {
         l+= x[i]*x[i];
     }
      l= sqrt(l);
      if( l > small )
     {
         for( i=0;i<n;i++ )
        {
            x[i]/= l;
        }
     }
  }

   void rot90( Real *x )
  {
      Real tmp;
      tmp= x[0];
      x[0]= -x[1];
      x[1]= tmp;
  }
   void rot180( Real *x )
  {
      x[0]= -x[0];
      x[1]= -x[1];
  }

   void rot270( Real *x )
  {
      Real tmp;
      tmp= x[1];
      x[1]= -x[0];
      x[0]= tmp;
  }

   void scale( Int n, Real *x, Real l )
  {
      Int i;
      for( i=0;i<n;i++ )
     {
         x[i]*= l;
     }
  }

   void vsum( Int n, Real *x, Real *y, Real *z )
  {
      Int i;
      for( i=0;i<n;i++ )
     {
         z[i]= x[i]+ y[i];
     }
   
  }
   void identv( Int n, Int *ival )
  {
      Int i;
      for( i=0;i<n;i++ )
     {
         ival[i]=i;
     }
  }

   Real norm1( Int n, Real *x )
  {
      Int  i;
      Real val=0;
      for( i=0;i<n;i++ )
     {
         val+= fabs( x[i] );
     }
      return( val );
  }

   Real norm2( Int n, Real *x )
  {
      Int  i;
      Real val=0;
      for( i=0;i<n;i++ )
     {
         val+= x[i]*x[i];
     }
      return( val );
  }

   Real norminf( Int n, Real *x )
  {
      Int  i;
      Real val=-1;
      for( i=0;i<n;i++ )
     {
         val= fmax( val,fabs(x[i]) );
     }
      return( val );
  }

   Real vangl2( Int n, Real *x[], Real *y )
  {
      Int    i;
      Real   dx0[2],dx1[2];
      Real   dth,th1,th0;
      Real   val=0;

//    cout << "vangle for "<<n<<" values\n";
      for( i=1;i<n;i++ )
     {
         dx0[0]= x[0][i-1]-y[0]; dx0[1]= x[1][i-1]-y[1];
         dx1[0]= x[0][i]-  y[0]; dx1[1]= x[1][i]-  y[1];

         th0= atan2( dx0[1],dx0[0] );
         th1= atan2( dx1[1],dx1[0] );
         dth= th1- th0;
         if( dth >  pi ){ dth-= pi2; }
         if( dth < -pi ){ dth+= pi2; }
         val+= dth;
     }

      return val ;
  }

   Real vangl2( Real *x0, Real *x1, Real *y )
  {
      Real   dx0[2],dx1[2];
      Real   dth,th1,th0;
      Real   val=0;

      dx0[0]= x0[0]-y[0]; dx0[1]= x0[1]-y[1];
      dx1[0]= x1[0]-y[0]; dx1[1]= x1[1]-y[1];

      th0= atan2( dx0[1],dx0[0] );
      th1= atan2( dx1[1],dx1[0] );
      dth= th1- th0;
      if( dth >  pi ){ dth-= pi2; }
      if( dth < -pi ){ dth+= pi2; }
      val= dth;

      return val ;
  }

   Real varea( Int n, Real *x[], Real *y )
  {
      Int    i;
      Real   dx0[2],dx1[2];
      Real   da;
      Real   val=0;

      for( i=1;i<n;i++ )
     {
         dx0[0]= x[0][i-1]-y[0]; dx0[1]= x[1][i-1]-y[1];
         dx1[0]= x[0][i]-  y[0]; dx1[1]= x[1][i]-  y[1];

         da= dx0[0]*dx1[1]- dx1[0]*dx0[1];
         da*= 0.5;
  
         val+= da;
     }

      return val ;
  }

   Real dot3( Real *v0, Real *v1 )
  {
      Real val;
      val= v0[0]*v1[0];
      val+= v0[1]*v1[1];
      val+= v0[2]*v1[2];
      return val;
  }

   Real dot( Int n, Real *v0, Real *v1 )
  {
      Real val;
      val= v0[0]*v1[0];
      for( Int i=1;i<n;i++ )
     {
         val+= v0[i]*v1[i];
     }
      return val;
  }
   void sub( Int n, Real *y1, Real *y2, Real *dy )
  { 
      for( Int i=0;i<n;i++ )
     {
         dy[i]= y1[i]-y2[i]; 
     }
  };

   void vec3( Real *v0, Real *v1, Real *v )
  {
      v[0]= v0[1]*v1[2]- v0[2]*v1[1];
      v[1]= v0[2]*v1[0]- v0[0]*v1[2];
      v[2]= v0[0]*v1[1]- v0[1]*v1[0];
  }

   Real norm22( Real *v )
  {
      Real d;
      d=  v[0]*v[0];
      d+= v[1]*v[1];
      d= sqrt(d);
      return d;
  }

   Real norm23( Real *v )
  {
      Real d;
      d=  v[0]*v[0];
      d+= v[1]*v[1];
      d+= v[2]*v[2];
      d= sqrt(d);
      return d;
  }

   void idv3( Real *y1, Real *y2 )
  { 
      y2[0]=y1[0];
      y2[1]=y1[1]; 
      y2[2]=y1[2]; 
  };

   void sub3( Real *y1, Real *y2, Real *dy )
  { 
      dy[0]= y1[0]-y2[0]; 
      dy[1]= y1[1]-y2[1]; 
      dy[2]= y1[2]-y2[2]; 
  };

   void add3( Real *y1, Real *y2, Real *dy )
  { 
      dy[0]= y1[0]+y2[0]; 
      dy[1]= y1[1]+y2[1]; 
      dy[2]= y1[2]+y2[2]; 
  };

   void orth2( Real *v0, Real *v1, Real *w )
  {
      Real v[2];
      Real d;
      d= dot2( v0,v1 );
      idv2( v0,v );
      sclv2( d,v );
      sub2( v1,v, w );
  }

   void orth3( Real *v0, Real *v1, Real *w )
  {
      Real v[3];
      Real d;
      d= dot3( v0,v1 );
      idv3( v0,v );
      sclv3( d,v );
      sub3( v1,v, w );
  }

   Real dist22( Real *x0, Real *x1 )
  {
      Real dx[2];
      Real val;
      sub2( x0,x1,dx );
      val= norm22( dx );
      return val;
  }

   Real dist23( Real *x0, Real *x1 )
  {
      Real dx[3];
      Real val;
      sub3( x0,x1,dx );
      val= norm23( dx );
      return val;
  }

   Real distinf2( Real *x0, Real *x1 )
  {
      Real dx[2];
      Real val;
      sub2( x0,x1,dx );
      val= norminf2( dx );
      return val;
  }

   Real distinf3( Real *x0, Real *x1 )
  {
      Real dx[3];
      Real val;
      sub3( x0,x1,dx );
      val= norminf3( dx );
      return val;
  }

   Int ptinlst2( Real *y, Int n, Real *x[], Real tol )
  {
     Int i;
     Real y1[2];
     Real d;
     Int  val;

     val= -1;
     for(i=0; i<n; i++)
    {
       line2( i,x, y1 );
       d= distinf2( y,y1 ); 
       if(d<tol)
      {
         val= i;
         break;
      }
    }
     return val;
  }

   Int ptinlst3( Real *y, Int n, Real *x[], Real tol )
  {
     Int i;
     Real y1[3];
     Real d;
     Int  val;

     val= -1;
     for(i=0; i<n; i++)
    {
       line3( i,x, y1 );
       d= distinf3( y,y1 ); 
       if(d<tol)
      {
         val= i;
         break;
      }
    }
     return val;
  }

   void shear2(Real *x0, Real *sh[2], Real *x)
  {
      Int      i;
      Real     dx0[2], dx1[2] ;

      dx0[0]= x[0]-x0[0];
      dx0[1]= x[1]-x0[1];

      dx1[0]= sh[0][0]*dx0[0]+ sh[0][1]*dx0[1];
      dx1[1]= sh[1][0]*dx0[0]+ sh[1][1]*dx0[1];

      x[0]= x0[0]+ dx1[0];
      x[1]= x0[1]+ dx1[1];
  }

