//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

//    Author          <PERSON> <<EMAIL>>
//    Created         Sat Feb  9 14:18:54 GMT 2008
//    Changes history -
//    Next change(s)  ( work in progress )
//    Purpose         tridiagonal LDU factorisation
//    Arguments       -

using namespace std;

#  include <iostream>
#  include <cstdlib>
#  include <utils/proto.h>
#  include <lin/thomas.h>


#  define  TOL 1.e-10

   void thmf( Int n, Real *am, Real *a, Real *ap )
  {         

// local symbols

      Real            d;
      Int             i;

// forward sweep

      i=0;
      if( fabs(a[i]) > TOL )
     {
         a[i]= 1./a[i];
     }
      else
     {
         cout << "null diagonal entry on line "<<i<<"\n";
         exit(0);
     }

      for( i=1;i<n;i++ )
     {
         d= am[i]*a[i-1];
         a[i]= a[i]- d*ap[i-1];
         am[i]= d;
         if( fabs(a[i]) > TOL )
        {
            a[i]= 1./a[i];
        }
         else
        {
            cout << "null diagonal entry on line "<<i<<"\n";
            exit(0);
        }
     }

// backward sweep

      for( i=n-1;i>0;i-- )
     {
         ap[i-1]= a[i]*ap[i-1];
     }
  }

   void thms( Int n, Real *am, Real *a, Real *ap, Int ldb, Int nb, Real *sb )
  {

// local symbols
      Int                 i,j;
      Real              **b;

// executable statements
      b= new Real*[ldb];   
      subv( ldb,nb, sb,b );

      for( j=0;j<ldb;j++ )
     {
// forward sweep
         for( i=1;i<n;i++ )
        {
            b[j][i]-= am[i]*b[j][i-1];
        }
// backward sweep
         for( i=n-2;i>=0;i-- )
        {
            b[j][i]-= ap[i]*b[j][i+1];
        }
// diagonal
         for( i=0;i<n;i++ )
        {
            b[j][i]*= a[i];
        }
     }
      delete[] b;
  }

   void thmst( Int n, Real *am, Real *a, Real *ap, Int ldb, Int nb,Real *sb )
  {

// local symbols
      Int                 i,j;

// diagonal
      Real              **b;

// executable statements

      b= new Real*[ldb];   
      subv( ldb,nb, sb,b );

      for( j=0;j<ldb;j++ )
     {
// diagonal
         for( i=0;i<n;i++ )
        {
            b[j][i]*= a[i];
        }
// backward sweep
         for( i=1;i<n;i++ )
        {
            b[j][i]-= ap[i-1]*b[j][i-1];
        }
// forward sweep
         for( i=n-2;i>=0;i-- )
        {
            b[j][i]-= am[i+1]*b[j][i+1];
        }
     }
      delete[] b;
  }

   void thomasp( Int n, Real *am, Real *a, Real *ap, Int ldb, Int nb, Real *sb, Real *wrk, Real *wrk2 )
  {

// local symbols

      Int             i,j;
// diagonal
      Real              **b;

// executable statements

      b= new Real*[ldb];   
      subv( ldb,nb, sb,b );

// executable statements



      thmf( n-1,am,a,ap );


      wrk[0]= ap[n-1];
      wrk[n-2]=am[n-1];
      wrk2[0]= am[0];
      wrk2[n-2]=ap[n-2];

      thmst( n-1,am,a,ap, 1,n,wrk);

      a[n-1]= a[n-1]- wrk[0]*wrk2[0]- wrk[n-2]*wrk2[n-2];

      if( fabs(a[n-1])> TOL )
     {
         a[n-1]= 1./a[n-1];
     }
      else
     {
         cout << "null diagonal entry on line "<<n-1<<"\n";
         exit(0);
     }

      wrk2[0]*=   a[n-1];
      wrk2[n-2]*= a[n-1];

      for( j=0;j<ldb;j++ )
     {
         for( i=0;i<n-1;i++ )
        {
            b[j][n-1]-= wrk[i]*b[j][i];
        }
         for( i=0;i<n-1;i++ )
        {
            b[j][i]-= wrk2[i]*b[j][n-1];
        }
     }


      thms(n-1, am,a,ap, ldb,nb,sb );
    
      for( j=0;j<ldb;j++ )
     {
         b[j][n-1]*= a[n-1];
     }

/*    cout <<"# after thmf\n";
      for( i=0;i<n;i++ )
     {
         cout << am[i]<<" "<<a[i]<<" "<<ap[i]<<" "<<b[0][i]<<" "<<b[1][i]<<"\n";
     }
      exit(0);*/


      delete[] b;
  }

   void thomas( Int n, Real *am, Real *a, Real *ap, Int ldb,Int nb, Real *b )
  {

      thmf( n, am,a,ap );
      thms( n, am,a,ap, ldb,nb,b );
  }
