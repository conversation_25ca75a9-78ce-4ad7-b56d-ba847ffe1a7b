   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Wed Oct 27 13:58:03 BST 2010
// Changes History -
// Next Change(s)  -
// Purpose         

#  include <lin/lins.h>
#  include <cmath>

   cLins::cLins(){};
   cLins::~cLins(){};

   void cLins::cg( Real *x[], Real *p[], Real *ap[], Real *r[], Real *z[], Real *r2 )
  {
      Real         alpha,beta,r20;

      adot( p,1.,ap );
      alpha= (*r2)/dot( p,ap );

      saxpy( (Real)1.,x, alpha, p );
      saxpy( (Real)1.,r,-alpha, ap );
      prec( r,z );

      r20= *r2;
    (*r2)= dot( r,z );

      beta= (*r2)/r20;

      saxpy( beta,p,(Real)1.,z );
         
  }

   void cLins::mgscm( Int n, Realv *(v), Real *w, Realv u )
  {
      Int i;
      for( i=0;i<n;i++ )
     {
         w[i]= dot( v[i],u );
//       saxpy( 1.,u, -w[i],v[i], u );
         saxpy( 1.,u, -w[i],v[i] );//hopefully it means the same
     }
      w[n]= dot(u,u);
      w[n]= sqrt(w[n]);
      scalv( 1./w[n],u );

  }

   void cLins::fom( Int n, Realv x, Realv b, Realv *v, Real *h[], Realv wrk )
  {

      Real  beta;

      if( n == 0 )
     {
         copy( b,wrk );
         adot( x,-1,wrk );
         setv( 0,v[0] );
         prec( v[0],wrk );
//       mgscm( 0,v,r+0,v[0] );
         assert( false );
     }
      else
     {
         setv( 0.,v[n] );
         setv( 0.,wrk );
         adot( v[n-1],1.,wrk );
         prec( v[n],wrk );
         mgscm( n,v, h[n-1], v[n] );

     }


/*    project( v );

      for( j=0;j<nkr;j++ )
     {
         saxpy( 1.,x, v->r[j],v->v[j], x );
     }

      destroy( w );
      delete w;*/
     
  }
