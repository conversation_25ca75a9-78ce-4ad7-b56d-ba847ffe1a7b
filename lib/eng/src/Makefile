include ../../Makefile.in

CSRC=   cosystem/cosystem.cpp \
	cosystem/annl.cpp \
	cosystem/cart2.cpp \
	cosystem/cart.cpp \
	cosystem/mtheta.cpp \
	cosystem/xr.cpp \
	cosystem/cosystems.cpp \
	field/gas/newgas.cpp \
	field/gas/gas.cpp \
	field/gas/mfroegas.cpp \
	field/gas/janafgas.cpp \
	field/gas/mfjanafgas.cpp \
	field/gas/mfreactingas.cpp \
	field/gas/heatconduction.cpp \
	field/gas/mfroegas_cht.cpp \
	field/gas/mfausmupgas.cpp \
	field/visc/newvisc.cpp \
	field/visc/cebeci.cpp \
	field/visc/cebecilowre.cpp \
	field/visc/komega.cpp \
	field/visc/komegalowre.cpp \
	field/visc/laminar.cpp \
	field/solid/solid.cpp \
	field/solid/elastic.cpp \
	gas/janaf.cpp \
	topo/graph.cpp \
	topo/ugraph.cpp \
	topo/dgraph.cpp \
	topo/conn.cpp \
	topo/cpack.cpp \
	topo/topo.cpp \
	topo/invert.cpp \
	topo/match.cpp \
	lin/lins.cpp \
	lin/vect.cpp \
	lin/small.cpp \
	lin/matrix.cpp \
	lin/thomas.cpp \
	lookup/lookup.cpp

#geo/3d/polyset3d.cpp \
#geo/3d/mface.cpp \
#geo/3d/sweep1.cpp \

COBJ= $(CSRC:.cpp=.o)

OBJS= $(COBJ) 

BINS= ../libeng.so

$(BINS): $(OBJS)
	$(PCCMP) -shared $(OBJS) -o $@
#ar -rs $(BINS) $(OBJS)

.cpp.o:
	$(PCCMP) $(COPT) $(SYSI) $(ENGI) -o $@ -c $<

clean:
	rm -f $(OBJS) $(BINS)
