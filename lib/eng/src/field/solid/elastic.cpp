   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Sun Oct 24 13:42:33 BST 2010
// Changes History
// Next Change(s)  -
// Purpose         generic solid class

#  include <field/solid/solid.h>
#  include <fstream>

   cElastic::cElastic()
  {
      nm= 0;
      e=NULL;
      nu=NULL;
      rho=NULL;
      lbl=NULL;
  }

   void cElastic::clear()
  {
      nm= 0;
      delete[] e; e=NULL;
      delete[] nu; nu=NULL;
      delete[] rho; rho=NULL;
      delete[] lbl; lbl=NULL;
  }
   cElastic::~cElastic()
  {
      clear();
  }
   
   void cElastic::props( Int n, string *s )
  {
      ifstream fle;
      string   fnme;
      string   sdum;
      Int      im;
      Real     se,snu,srho;
      bool    *found;
      bool     val;

      clear();
      nm= n;
      e= new Real[nm];
      nu= new Real[nm];
      rho= new Real[nm];
      found= new bool[nm];
      lbl= new string[nm];
      setv( (Int)0,nm, false,found );

      fnme= share;
      fnme+= "/solid/elastic/props.dat";
      fle.open( fnme.c_str() );
      while( fle.good() )
     {
         fle >> sdum >> se >> snu >> srho ;
         im= inlst( sdum, n,s );
         if( im != -1 )
        {
            lbl[im]= sdum;
            e[im]=  se;
            nu[im]= snu;
            rho[im]=srho;
            found[im]= true;
        }
     }
      fle.close();

      val= true;
      for( im=0;im<nm;im++ )
     {
         val= val && found[im];
     }
      delete[] found; found=NULL;
      if( !val )
     {
         cout << "could not find material properties\n";
         exit(1);
     }
  }
   
   void cElastic::stressb1( Int is, Int ie, Int *im, Real *s[], Real *wi[], Real *wl[], Real *t[] )
  {
      Int i,jm;
      Real        d11;
       
      for(  i=is;i<ie;i++ )
     {
         jm=im[i];
         d11= e[jm];
         t[0][i]= s[0][i]*d11*wi[0][i]/wl[0][i];
     }
  }

   void cElastic::stressb2( Int is, Int ie, Int *im, Real *s[], Real *wi[], Real *wl[], Real *t[] )
  {
      Int i,jm;
      Real        d11;
       
      for(  i=is;i<ie;i++ )
     {
          jm= im[i];
          d11= e[jm];
          t[0][i]= ( 2-(1./3.) )*s[0][i]*d11*wi[0][i]*wi[0][i]*wi[0][i]/( wl[0][i]*12.0*(1-nu[jm]*nu[jm]) );
     }
  }

   void cElastic::stressb3( Int is, Int ie, Int *im, Real *s[], Real *wi[], Real *t[] )
  { 
      Int i,jm;
      Real        th,d11,d12,d33;   
      
      for(i=is; i<ie; i++)
     {
        jm= im[i];
        th= wi[0][i]/sqrt( ( 1.-nu[jm]*nu[jm] ) ); // Calladine
        
        d11= e[jm]*wi[0][i]*th*th/12.0;
        d12= nu[jm];
        d33= 2.*(1.-nu[jm]); // 2 is from Id
      
        d12*= d11;
        d33*= d11; 

        t[0][i]= s[0][i]*d11+ s[1][i]*d12;
        t[1][i]= s[0][i]*d12+ s[1][i]*d11;
        t[2][i]=                           s[2][i]*d33; 
     }
  }

   void cElastic::stress1( Int is, Int ie, Int *im, Real *s[], Real *t[] )
  {
      Int i,jm;
      Real        d11;
      for(  i=is;i<ie;i++ )
     {
         jm= im[i];
         d11= e[jm];
         t[0][i]= s[0][i]*d11;
     }
  }



   void cElastic::stress2( Int is, Int ie, Int *im, Real *s[], Real *t[] )
  {
      Int i,jm;
      Real        d11,d12,d33;      

      for(  i=is;i<ie;i++ )
     {
         jm= im[i];
         d12= nu[jm];
         d33= 0.5*(1.-nu[jm]);
         d11= e[jm]/( 1.-nu[jm]*nu[jm] );
         d12*= d11;
         d33*= d11;
         t[0][i]= s[0][i]*d11+ s[1][i]*d12;
         t[1][i]= s[0][i]*d12+ s[1][i]*d11;
         t[2][i]=                           s[2][i]*d33;
     }
  }


   void cElastic::stress3( Int is, Int ie, Int *im, Real *s[], Real *t[] )
  {
      Int i,jm;
      Real        d11,d12,d13,d33;      
      
      for(  i=is;i<ie;i++ )
     {
         jm= im[i];
         d11= e[jm] *(1-nu[jm])/((1+nu[jm])*(1-2*nu[jm]));
         d12= e[jm]*nu[jm]/((1+nu[jm])*(1-2*nu[jm]));
         d13= d12;
         d33= 0.5*e[jm]/(1+nu[jm]);
         t[0][i]= s[0][i]*d11+ s[1][i]*d12+ s[2][i]*d12;
         t[1][i]= s[0][i]*d12+ s[1][i]*d11+ s[2][i]*d12;
         t[2][i]= s[0][i]*d12+ s[1][i]*d12+ s[2][i]*d11 ;

         t[3][i]=                                        s[3][i]*d33;
         t[4][i]=                                                    s[4][i]*d33;
         t[5][i]=                                                              s[5][i]*d33;
     }
  } 

   void cElastic::pickle( size_t *len, pickle_t *buf )
  {
      pckle( len, nm,lbl, buf );
      pckle( len, nm,e, buf );
      pckle( len, nm,nu, buf );
      pckle( len, nm,rho, buf );
  }

   void cElastic::unpickle( size_t *len, pickle_t buf )
  {
      Int tmp;
      clear();

      unpckle( len, &tmp,&lbl, buf ); nm= tmp;
      unpckle( len, &tmp,&e, buf ); 
      unpckle( len, &tmp,&nu, buf );
      unpckle( len, &tmp,&rho, buf );

      for( Int i=0;i<nm;i++ )
     {
         cout << lbl[i]<<" "<<e[i]<<" "<<nu[i]<<" "<<rho[i]<<"\n";
     }
  }
