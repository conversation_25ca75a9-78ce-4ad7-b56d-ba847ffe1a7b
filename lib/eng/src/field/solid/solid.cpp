   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Sun Oct 24 13:42:33 BST 2010
// Changes History
// Next Change(s)  -
// Purpose         generic solid class

#  include <field/solid/solid.h>

   cSolid::cSolid()
  {
  }

   cSolid::~cSolid()
  {
  }

   cSolid *newsolid( Int ist )
  {
      cSolid *val=NULL;
      switch( ist )
     {
         case( solid_elastic ):
        {
            val= new cElastic();
            break;
        }
         default:
        {
            cout << "unknown solid type\n";
            exit(1);
        }
     }
      return val;
  }

/* void cSolid::setcoo( cCosystem *c )
  {
      coo= c;
      nv= coo->getNvel();
      cout << "SOLID HAS "<<nv<<" DISPLACEMENTS\n";
  }*/
