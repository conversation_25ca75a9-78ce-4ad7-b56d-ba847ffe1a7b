using namespace std;

#  include <field/visc.h>

   void cCebeciLowRe::maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       ds,s,l;
      Real       mu,rho,kappa,mut,kappat,cp;
      Real       lmax=0.005;
      //Real       lmax=0.0072447256138495853;
    
      if( iqe > iqs )
     {
         maux33( iqs,iqe, xq,q,dst,dqdx,aux, lmixmax );//,auxb );//,igdst,idst );
     }
  }

   void cCebeciLowRe::maux( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq )
  {
      if( iqe > iqs )
     {
         maux33( iqs,iqe, sxq,sq,sdst,sdqdx,saux,lmixmax,nq );//,auxb );//,igdst,idst );
     }
  }

   void cCebeciLowRe::maux( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux )
  {
      if( iqe > iqs )
     {
         maux33( iqs,iqe, xq,q,dst,dqdx,aux );//,auxb );//,igdst,idst );
     }
  }

   void cCebeciLowRe::maux33( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       ds,s,l;
      Real       mu,rho,kappa,mut,kappat,cp;
      Real       lmax=0.005;
      //Real       lmax=0.0072447256138495853;
      Int        ig,iqb;
      Real       utau,yp;
      Real       ap0=26.;
   
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            s=0;
            ds= dqdx[0][1][iq]+ dqdx[1][0][iq]; s+= ds*ds; 
            ds= dqdx[0][2][iq]+ dqdx[2][0][iq]; s+= ds*ds; 
            ds= dqdx[1][2][iq]+ dqdx[2][1][iq]; s+= ds*ds; 
            s= sqrt(s);
            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            l= fmin( dst[0][iq],lmixmax );
            l*= karm;

            //Low Reynolds correction
 /*         ig=igdst[0][iq];
            iqb=idst[0][iq];
            utau=auxb[ig][0][iqb];
            utau=fabs(utau);
            yp=rho*dst[0][iq]*utau/mu;*/
            yp= dst[1][iq];
            l*= 1.0-exp(-yp/ap0);

            mut= rho*s*l*l;
            mut= fmin( mut,1000*mu );
            kappat= cp*mut;   // unit turbulent Prandtl number
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;
        }
     }
  }

   void cCebeciLowRe::maux33( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq )//, Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       ds,s,l;
      Real       mu,rho,kappa,mut,kappat,cp;
      Real       lmax=0.005;
      //Real       lmax=0.0072447256138495853;
      Int        ig,iqb;
      Real       utau,yp;
      Real       ap0=26.;
   
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         firstprivate(lmax,ap0) \
         present(sxq[0:nx*nq],sq[0:nv*nq],sdst[0:2*nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            s=0;
            //ds= dqdx[0][1][iq]+ dqdx[1][0][iq]; s+= ds*ds; 
            //ds= dqdx[0][2][iq]+ dqdx[2][0][iq]; s+= ds*ds; 
            //ds= dqdx[1][2][iq]+ dqdx[2][1][iq]; s+= ds*ds; 
            ds= sdqdx[ADDR(0,1,iq,nq)]+ sdqdx[ADDR(1,0,iq,nq)]; s+= ds*ds; 
            ds= sdqdx[ADDR(0,2,iq,nq)]+ sdqdx[ADDR(2,0,iq,nq)]; s+= ds*ds; 
            ds= sdqdx[ADDR(1,2,iq,nq)]+ sdqdx[ADDR(2,1,iq,nq)]; s+= ds*ds; 
            s= sqrt(s);
            //cp= aux[naux0-3][iq];
            //mu= aux[naux0-2][iq];
            cp= saux[ADDR(naux0-3,iq,nq)];
            mu= saux[ADDR(naux0-2,iq,nq)];
            //kappa= aux[naux0-1][iq];
            kappa= saux[ADDR(naux0-1,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //l= fmin( dst[0][iq],lmixmax );
            l= fmin( sdst[ADDR(0,iq,nq)],lmixmax );
            l*= karm;

            //Low Reynolds correction
 /*         ig=igdst[0][iq];
            iqb=idst[0][iq];
            utau=auxb[ig][0][iqb];
            utau=fabs(utau);
            yp=rho*dst[0][iq]*utau/mu;*/
            //yp= dst[1][iq];
            yp= sdst[ADDR(1,iq,nq)];
            l*= 1.0-exp(-yp/ap0);

            mut= rho*s*l*l;
            mut= fmin( mut,1000*mu );
            kappat= cp*mut;   // unit turbulent Prandtl number
            //aux[naux-2][iq]= mu+ mut;
            //aux[naux-1][iq]= kappa+ kappat;
            saux[ADDR(naux-2,iq,nq)]= mu+ mut;
            saux[ADDR(naux-1,iq,nq)]= kappa+ kappat;
        }
        #pragma acc exit data delete(this)
     }
  }

   void cCebeciLowRe::maux33( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux )//, Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       ds,s,l;
      Real       mu,rho,kappa,mut,kappat,cp;
      Real       lmax=0.05;
      //Real       lmax=0.0072447256138495853;
      Int        ig,iqb;
      Real       utau,yp;
      Real       ap0=26.;
  
      Int nq;
      Real *sxq, *sq, *sdst, *sdqdx, *saux;

      nq = q.get_dim1();

      sxq   = xq.get_data();
      sq    = q.get_data();
      sdst  = dst.get_data();
      sdqdx = dqdx.get_data();
      saux  = aux.get_data();
 
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         firstprivate(lmax,ap0) \
         present(sxq[0:nx*nq],sq[0:nv*nq],sdst[0:2*nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            s=0;
            //ds= dqdx[0][1][iq]+ dqdx[1][0][iq]; s+= ds*ds; 
            //ds= dqdx[0][2][iq]+ dqdx[2][0][iq]; s+= ds*ds; 
            //ds= dqdx[1][2][iq]+ dqdx[2][1][iq]; s+= ds*ds; 
            ds= sdqdx[ADDR(0,1,iq,nq)]+ sdqdx[ADDR(1,0,iq,nq)]; s+= ds*ds; 
            ds= sdqdx[ADDR(0,2,iq,nq)]+ sdqdx[ADDR(2,0,iq,nq)]; s+= ds*ds; 
            ds= sdqdx[ADDR(1,2,iq,nq)]+ sdqdx[ADDR(2,1,iq,nq)]; s+= ds*ds; 
            s= sqrt(s);
            //cp= aux[naux0-3][iq];
            //mu= aux[naux0-2][iq];
            cp= saux[ADDR(naux0-3,iq,nq)];
            mu= saux[ADDR(naux0-2,iq,nq)];
            //kappa= aux[naux0-1][iq];
            kappa= saux[ADDR(naux0-1,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //l= fmin( dst[0][iq],lmixmax );
            l= fmin( sdst[ADDR(0,iq,nq)],lmax );
            l*= karm;

            //Low Reynolds correction
 /*         ig=igdst[0][iq];
            iqb=idst[0][iq];
            utau=auxb[ig][0][iqb];
            utau=fabs(utau);
            yp=rho*dst[0][iq]*utau/mu;*/
            //yp= dst[1][iq];
            yp= sdst[ADDR(1,iq,nq)];
            l*= 1.0-exp(-yp/ap0);

            mut= rho*s*l*l;
            mut= fmin( mut,1000*mu );
            kappat= cp*mut;   // unit turbulent Prandtl number
            //aux[naux-2][iq]= mu+ mut;
            //aux[naux-1][iq]= kappa+ kappat;
            saux[ADDR(naux-2,iq,nq)]= mu+ mut;
            saux[ADDR(naux-1,iq,nq)]= kappa+ kappat;
        }
        #pragma acc exit data delete(this)
     }
  }

   //void cCebeciLowRe::mwflx33( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
   //                                        Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
   //                                                   Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cCebeciLowRe::mwflx33( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,  
                                                Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr, 
                                                Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            tw[MxNVs];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;
      Int             nql,nqr;

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          firstprivate(umin) \
          private(f,ut,tw) \
          present(            sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
                  icqr[0:nfc],sxr[0:nx*nq], sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
                  swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
          default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic];

            //mu= auxr[naux0-2][iqr];
            //mut= auxr[naux-2][iqr];
            //rho= auxr[0][iqr];
            mu= sauxr[ADDR(naux0-2,iqr,nqr)];
            mut= sauxr[ADDR(naux-2,iqr,nqr)];
            rho= sauxr[ADDR(0,iqr,nqr)];

// distance
            //d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            //d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            //d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
            d=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxl[ADDR(0,iql,nql)] );
            d+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxl[ADDR(1,iql,nql)] );
            d+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxl[ADDR(2,iql,nql)] );

// tangential velocity

            //ut[0]= qr[0][iqr]-ql[0][iql];
            //ut[1]= qr[1][iqr]-ql[1][iql];
            //ut[2]= qr[2][iqr]-ql[2][iql];
            ut[0]= sqr[ADDR(0,iqr,nqr)]-sql[ADDR(0,iql,nql)];
            ut[1]= sqr[ADDR(1,iqr,nqr)]-sql[ADDR(1,iql,nql)];
            ut[2]= sqr[ADDR(2,iqr,nqr)]-sql[ADDR(2,iql,nql)];

            //un=  ut[0]*wc[0][ic];
            //un+= ut[1]*wc[1][ic];
            //un+= ut[2]*wc[2][ic];
            un=  ut[0]*swc[ADDR(0,ic,nfc)];
            un+= ut[1]*swc[ADDR(1,ic,nfc)];
            un+= ut[2]*swc[ADDR(2,ic,nfc)];

            //ut[0]-= wc[0][ic]*un;
            //ut[1]-= wc[1][ic]*un;
            //ut[2]-= wc[2][ic]*un;
            ut[0]-= swc[ADDR(0,ic,nfc)]*un;
            ut[1]-= swc[ADDR(1,ic,nfc)]*un;
            ut[2]-= swc[ADDR(2,ic,nfc)]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            //utau= auxl[0][iql];
            utau= sauxl[ADDR(0,iql,nql)];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               #pragma acc loop seq
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
           }
            else
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            //f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            //f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            //f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];
            f[1]= -rho*ut[0]*fabs(ut[0])*swc[ADDR(3,ic,nfc)];
            f[2]= -rho*ut[1]*fabs(ut[1])*swc[ADDR(3,ic,nfc)];
            f[3]= -rho*ut[2]*fabs(ut[2])*swc[ADDR(3,ic,nfc)];

            //f[4]=  f[1]*ql[0][iql];
            //f[4]+= f[2]*ql[1][iql];
            //f[4]+= f[3]*ql[2][iql];
            f[4]=  f[1]*sql[ADDR(0,iql,nql)];
            f[4]+= f[2]*sql[ADDR(1,iql,nql)];
            f[4]+= f[3]*sql[ADDR(2,iql,nql)];

            //rhsl[1][iql]-= f[1];
            //rhsl[2][iql]-= f[2];
            //rhsl[3][iql]-= f[3];
            //rhsl[4][iql]-= f[4];
            #pragma acc atomic
            srhsl[ADDR_(1,iql,nql)]-= f[1];
            #pragma acc atomic
            srhsl[ADDR_(2,iql,nql)]-= f[2];
            #pragma acc atomic
            srhsl[ADDR_(3,iql,nql)]-= f[3];
            #pragma acc atomic
            srhsl[ADDR_(4,iql,nql)]-= f[4];

            //rhsr[1][iqr]+= f[1];
            //rhsr[2][iqr]+= f[2];
            //rhsr[3][iqr]+= f[3];
            //rhsr[4][iqr]+= f[4];
            #pragma acc atomic
            srhsr[ADDR_(1,iqr,nqr)]+= f[1];
            #pragma acc atomic
            srhsr[ADDR_(2,iqr,nqr)]+= f[2];
            #pragma acc atomic
            srhsr[ADDR_(3,iqr,nqr)]+= f[3];
            #pragma acc atomic
            srhsr[ADDR_(4,iqr,nqr)]+= f[4];

            //auxc[nauxf-1][ic]+= utau*wc[3][ic];
            //auxl[0][iql]= utau;
            //auxl[1][iql]= rho*utau/mu;
            #pragma acc atomic
            sauxc[ADDR_((nauxf-1),ic,nfc)]+= utau*swc[ADDR_(3,ic,nfc)];

            sauxl[ADDR(0,iql,nql)]= utau;
            sauxl[ADDR(1,iql,nql)]= rho*utau/mu;
        }
         #pragma acc exit data delete(this)
     }
  }

   void cCebeciLowRe::mwflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            tw[MxNVs];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;
      Int             nql,nqr;

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *srhsl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sxl   = xl.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();;

      icqr  = icqr_view.get_data();
      sxr   = xr.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      srhsr = rhsr.get_data();;

      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          firstprivate(umin) \
          private(f,ut,tw) \
          present(            sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
                  icqr[0:nfc],sxr[0:nx*nq], sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
                  swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
          default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic];

            //mu= auxr[naux0-2][iqr];
            //mut= auxr[naux-2][iqr];
            //rho= auxr[0][iqr];
            mu= sauxr[ADDR(naux0-2,iqr,nqr)];
            mut= sauxr[ADDR(naux-2,iqr,nqr)];
            rho= sauxr[ADDR(0,iqr,nqr)];

// distance
            //d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            //d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            //d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
            d=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxl[ADDR(0,iql,nql)] );
            d+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxl[ADDR(1,iql,nql)] );
            d+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxl[ADDR(2,iql,nql)] );

// tangential velocity

            //ut[0]= qr[0][iqr]-ql[0][iql];
            //ut[1]= qr[1][iqr]-ql[1][iql];
            //ut[2]= qr[2][iqr]-ql[2][iql];
            ut[0]= sqr[ADDR(0,iqr,nqr)]-sql[ADDR(0,iql,nql)];
            ut[1]= sqr[ADDR(1,iqr,nqr)]-sql[ADDR(1,iql,nql)];
            ut[2]= sqr[ADDR(2,iqr,nqr)]-sql[ADDR(2,iql,nql)];

            //un=  ut[0]*wc[0][ic];
            //un+= ut[1]*wc[1][ic];
            //un+= ut[2]*wc[2][ic];
            un=  ut[0]*swc[ADDR(0,ic,nfc)];
            un+= ut[1]*swc[ADDR(1,ic,nfc)];
            un+= ut[2]*swc[ADDR(2,ic,nfc)];

            //ut[0]-= wc[0][ic]*un;
            //ut[1]-= wc[1][ic]*un;
            //ut[2]-= wc[2][ic]*un;
            ut[0]-= swc[ADDR(0,ic,nfc)]*un;
            ut[1]-= swc[ADDR(1,ic,nfc)]*un;
            ut[2]-= swc[ADDR(2,ic,nfc)]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            //utau= auxl[0][iql];
            utau= sauxl[ADDR(0,iql,nql)];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               #pragma acc loop seq
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
           }
            else
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            //f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            //f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            //f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];
            f[1]= -rho*ut[0]*fabs(ut[0])*swc[ADDR(3,ic,nfc)];
            f[2]= -rho*ut[1]*fabs(ut[1])*swc[ADDR(3,ic,nfc)];
            f[3]= -rho*ut[2]*fabs(ut[2])*swc[ADDR(3,ic,nfc)];

            //f[4]=  f[1]*ql[0][iql];
            //f[4]+= f[2]*ql[1][iql];
            //f[4]+= f[3]*ql[2][iql];
            f[4]=  f[1]*sql[ADDR(0,iql,nql)];
            f[4]+= f[2]*sql[ADDR(1,iql,nql)];
            f[4]+= f[3]*sql[ADDR(2,iql,nql)];

            //rhsl[1][iql]-= f[1];
            //rhsl[2][iql]-= f[2];
            //rhsl[3][iql]-= f[3];
            //rhsl[4][iql]-= f[4];
            #pragma acc atomic
            srhsl[ADDR_(1,iql,nql)]-= f[1];
            #pragma acc atomic
            srhsl[ADDR_(2,iql,nql)]-= f[2];
            #pragma acc atomic
            srhsl[ADDR_(3,iql,nql)]-= f[3];
            #pragma acc atomic
            srhsl[ADDR_(4,iql,nql)]-= f[4];

            //rhsr[1][iqr]+= f[1];
            //rhsr[2][iqr]+= f[2];
            //rhsr[3][iqr]+= f[3];
            //rhsr[4][iqr]+= f[4];
            #pragma acc atomic
            srhsr[ADDR_(1,iqr,nqr)]+= f[1];
            #pragma acc atomic
            srhsr[ADDR_(2,iqr,nqr)]+= f[2];
            #pragma acc atomic
            srhsr[ADDR_(3,iqr,nqr)]+= f[3];
            #pragma acc atomic
            srhsr[ADDR_(4,iqr,nqr)]+= f[4];

            //auxc[nauxf-1][ic]+= utau*wc[3][ic];
            //auxl[0][iql]= utau;
            //auxl[1][iql]= rho*utau/mu;
            #pragma acc atomic
            sauxc[ADDR_((nauxf-1),ic,nfc)]+= utau*swc[ADDR_(3,ic,nfc)];

            sauxl[ADDR(0,iql,nql)]= utau;
            sauxl[ADDR(1,iql,nql)]= rho*utau/mu;
        }
         #pragma acc exit data delete(this)
     }
  }

   //void cCebeciLowRe::yplus( Int ist, Int ien, Int *iqd, Real *xq[], Real *q[], Real *aux[], Int *iqb, Real *qb[], Real *auxb[], Real *dst[] )
   void cCebeciLowRe::yplus( Int ist, Int ien, Int *siqd, Real *sxq, Real *sq, Real *saux, Int *siqb, Real *sqb, Real *sauxb, Real *sdst, Int nbb, Int nq, Int ndst )
  {
      Int id,iq,ib;
      Real d,utau,mu,yp,rho;
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop \
       present(siqd[0:2*ndst],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],siqb[0:ndst],sqb[0:nv*nbb],sauxb[0:naux*nbb],sdst[0:2*nq],this)\
       default(none)

      for( id=ist;id<ien;id++ )
     {
         iq= siqd[id];
         ib= siqb[id];
         //d= dst[0][iq];
         d= sdst[ADDR(0,iq,nq)];
         //mu= aux[naux0-2][iq];
         mu= saux[ADDR(naux0-2,iq,nq)];
         //rho= aux[0][iq];
         rho= saux[ADDR(0,iq,nq)];
         //utau=auxb[0][ib];
         utau=sauxb[ADDR(0,ib,nbb)];
         utau=fabs(utau);
         yp=rho*d*utau/mu;
//       aux[naux-4][iq]= yp;
         //dst[1][iq]= yp;
         sdst[ADDR(1,iq,nq)]= yp;
     }
      #pragma acc exit data delete(this)
  }

   void cCebeciLowRe::yplus( Int ist, Int ien, cAu3xView<Int>& iqd, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Int>& iqb, cAu3xView<Real>& qb, 
                             cAu3xView<Real>& auxb, cAu3xView<Real>& dst )
  {
      Int id,iq,ib;
      Real d,utau,mu,yp,rho;

      Int nbb, nq, ndst;
      Int *siqd;
      Real *sxq, *sq, *saux;
      Int *siqb;
      Real *sqb, *sauxb, *sdst;

      nbb = qb.get_dim1();
      nq = q.get_dim1();
      ndst = iqd.get_dim1();

      siqd  = iqd.get_data();
      sxq   = xq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      siqb  = iqb.get_data();
      sqb   = qb.get_data();
      sauxb = auxb.get_data();
      sdst  = dst.get_data();

      #pragma acc enter data copyin(this)
      #pragma acc parallel loop \
       present(siqd[0:2*ndst],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],siqb[0:ndst],sqb[0:nv*nbb],sauxb[0:naux*nbb],sdst[0:2*nq],this)\
       default(none)

      for( id=ist;id<ien;id++ )
     {
         iq= siqd[id];
         ib= siqb[id];
         //d= dst[0][iq];
         d= sdst[ADDR(0,iq,nq)];
         //mu= aux[naux0-2][iq];
         mu= saux[ADDR(naux0-2,iq,nq)];
         //rho= aux[0][iq];
         rho= saux[ADDR(0,iq,nq)];
         //utau=auxb[0][ib];
         utau=sauxb[ADDR(0,ib,nbb)];
         utau=fabs(utau);
         yp=rho*d*utau/mu;
//       aux[naux-4][iq]= yp;
         //dst[1][iq]= yp;
         sdst[ADDR(1,iq,nq)]= yp;
     }
      #pragma acc exit data delete(this)
  }
