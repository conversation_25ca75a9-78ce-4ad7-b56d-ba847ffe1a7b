using namespace std;

#  include <field/visc.h>
   cSpalarthighre::cSpalarthighre()
  {
      karm=0.41;
      b=5.1;
      sigma=2./3.;
      cv1=7.1;;
      cv13=cv1*cv1*cv1;
      cb1=0.1335;
      cb2=0.622;
      cw1= cb1/(karm*karm)+(1+cb2)/sigma;
      cw2=0.3;
      cw3=2.;
      cw36=64.;

//      cout << " spalart hight res================\n";
  };

   void cSpalarthighre::maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], 
                              Real lmixmax )
  {
      Int        iq,ix,jx;
      Real       fv1,chi,chi3,nu,mu,rho,kappa,mut,kappat,cp,nut;

      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            nu= mu/rho;
            q[nv0][iq]= fmax( q[nv0][iq],small );
            //q[nv0][iq]= fmin( q[nv0][iq],800*mu/rho );    // MAURO the limit was 1500
            q[nv0][iq]= fmin( q[nv0][iq],5000*mu/rho );    // MAURO the limit was 1500
            //q[nv0][iq]= fmin( q[nv0][iq],100*mu/rho );
            nut= q[nv0][iq];
            mut= rho*nut;
            kappat= cp*mut;
            aux[naux-3][iq]= 0.;
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;
        }
     }
  }


   void cSpalarthighre::srhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real nuy,utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2;
      Real omg[3], sr[3], srmag;

//      cout << " spalart hight res srhs ================\n";

      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            if( utau >= 0 )
           {
// stress tensor

               //magnitude of vorticity
               omg[0]= dqdx[0][1][iq]- dqdx[1][0][iq];
               omg[1]= dqdx[2][0][iq]- dqdx[0][2][iq];
               omg[2]= dqdx[1][2][iq]- dqdx[2][1][iq];
               s=  omg[0]*omg[0];
               s+= omg[1]*omg[1];
               s+= omg[2]*omg[2];
               s= sqrt( s );

               //strain rate magnitude
               //sr[0]= dqdx[0][1][iq] + dqdx[1][0][iq];
               //sr[1]= dqdx[2][0][iq] + dqdx[0][2][iq];
               //sr[2]= dqdx[1][2][iq] + dqdx[2][1][iq];
               //srmag = 2*dqdx[0][0][iq]*dqdx[0][0][iq];
               //srmag+= 2*dqdx[1][1][iq]*dqdx[1][1][iq];
               //srmag+= 2*dqdx[2][2][iq]*dqdx[2][2][iq];
               //cross terms are ignored to avoid sudden change of nut behind a shock?
               //srmag = sr[0]*sr[0];
               //srmag+= sr[1]*sr[1];
               //srmag+= sr[2]*sr[2];
               //srmag = sqrt(srmag);

               //rotation correction
               //s = s + fmin(0, srmag - s );

               ky= karm*y;
               ky2=ky*ky;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               nuy= nut/y;
               nuy*= nuy;
               if( nut > 1.e-12 )
              {
                  rhs[nv0][iq]+= wq[0][iq]*rho*( cb1*s*nut- (cw1*fw)*nuy );
              }

           }
            else
           {
               rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*nut;
           }
        }
     }
  }

   void cSpalarthighre::dsrhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
                        Real *wq[], Real *res[], Real *lhs[] )
  {
      Int iq;
      Real nuy,utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2,drho,dnut;
      Real omg[3], sr[3], srmag;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            drho= dq[0][iq];
            dnut=daux[nv0][iq];
            if( utau >=  0 )
           {
               //vorticity magnitude
               omg[0]= dqdx[0][1][iq]- dqdx[1][0][iq];
               omg[1]= dqdx[2][0][iq]- dqdx[0][2][iq];
               omg[2]= dqdx[1][2][iq]- dqdx[2][1][iq];
               s=  omg[0]*omg[0];
               s+= omg[1]*omg[1];
               s+= omg[2]*omg[2];
               s= sqrt( s );

               //strain rate magnitude
               //sr[0]= dqdx[0][1][iq] + dqdx[1][0][iq];
               //sr[1]= dqdx[2][0][iq] + dqdx[0][2][iq];
               //sr[2]= dqdx[1][2][iq] + dqdx[2][1][iq];
               //srmag = 2*dqdx[0][0][iq]*dqdx[0][0][iq];
               //srmag+= 2*dqdx[1][1][iq]*dqdx[1][1][iq];
               //srmag+= 2*dqdx[2][2][iq]*dqdx[2][2][iq];
               //cross terms are ignored to avoid sudden change of nut behind a shock?
               //srmag = sr[0]*sr[0];
               //srmag+= sr[1]*sr[1];
               //srmag+= sr[2]*sr[2];
               //srmag = sqrt(srmag);

               //rotation correction
               //s = s + fmin(0, srmag - s );

               ky= karm*y;
               ky2=ky*ky;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               nuy= nut/y;
               nuy*= nuy;
               if( nut > 1.e-12 )
              {
                  res[nv0][iq]+= wq[0][iq]*drho*( cb1*s*nut- (cw1*fw)*nuy )+ 
                                 wq[0][iq]*rho*dnut*( cb1*s- 2*(cw1*fw)*nut/(y*y) );
              }

           }
            else
           {
               res[nv0][iq]= - lhs[nlhs-1][iq]*rho*nut;
           }
        }
     }
  }

   void cSpalarthighre::slhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[] )
  {
      Int iq;
      Real utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2;
      Real omg[3], sr[3], srmag;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            if( utau >=  0 )
           {
               //vorticity magnitude
               omg[0]= dqdx[0][1][iq] - dqdx[1][0][iq];
               omg[1]= dqdx[2][0][iq] - dqdx[0][2][iq];
               omg[2]= dqdx[1][2][iq] - dqdx[2][1][iq];
               s= omg[0]*omg[0];
               s+= omg[1]*omg[1];
               s+= omg[2]*omg[2];
               s= sqrt( s );

               //strain rate magnitude
               //sr[0]= dqdx[0][1][iq] + dqdx[1][0][iq];
               //sr[1]= dqdx[2][0][iq] + dqdx[0][2][iq];
               //sr[2]= dqdx[1][2][iq] + dqdx[2][1][iq];
               //srmag = sr[0]*sr[0];
               //srmag+= sr[1]*sr[1];
               //srmag+= sr[2]*sr[2];
               //srmag = sqrt(srmag);

               //rotation correction
               //s = s + fmin(0, srmag - s );

               ky= karm*y;
               ky2=ky*ky;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               lhs[nlhs0-1][iq]+= wq[0][iq]*rho*( cb1*s+ 2*(cw1*fw)*nut/(y*y) );
           }
            else
           {
               lhs[nlhs0-1][iq]= 0;
           }
        }
     }
  }

