using namespace std;

#  include <field/visc.h>

   void reynoldstress( Int nx, Real rho, Real k, Real omega, Real dqdx[3][3], Real stbeta, Real mu0, Real tau[3][3], 
                       Real a0[3][3], Real *b );
   void dreynoldstress( Int nx, Real rho, Real k, Real omega, Real drho, Real dk, Real dqdx[3][3], Real ddqdx[3][3], 
                        Real stbeta, Real mu0, Real a[3][3], Real *b, Real dtau[3][3] );

   cSBslEarsm::cSBslEarsm()
  {
      sigmak1=0.5;
      sigmaw1=0.5;
      beta1=0.075;
      a1=0.31;
      stbeta=0.09;
      karm=0.41;
      sigmak2=1.0;
      sigmaw2=0.856;
      beta2=0.0828;
      b=5.1;
      gam1=beta1/stbeta-sigmaw1*karm*karm/sqrt(stbeta);
      gam2=beta2/stbeta-sigmaw2*karm*karm/sqrt(stbeta);
  }

   void reynoldstress( Int nx, Real rho, Real k, Real omega, Real dqdx[3][3], Real stbeta, Real mu0, Real tau[3][3], 
                       Real a0[3][3], Real *b )
  {
      const Real  A1=1.245;
      const Real  C1=1.8;
      Int ix, jx, kx, lx;
      Real t1[3][3], t3[3][3], t4[3][3], t6[3][3], a[3][3], delta[3][3];
      Real sij[3][3], wij[3][3];
      Real h_s, h_w, h_sw;   
      Real b1, b3, b4, b6, Q, Q1, C1p, tscale, N;
     
//turbulence time scale
      tscale = fmax(1./(stbeta*omega), 6.*sqrt(mu0/(stbeta*rho*fmax(k,1e-16)*omega)));
//      cout << omega << " " << rho << " " << k << " " << omega << " tscale \n";

// vorticity rate tensor
      for(ix=0; ix<nx; ix++)
     {
         for(jx=0; jx<nx; jx++)
        {
            wij[ix][jx]= dqdx[ix][jx] - dqdx[jx][ix]; 

            if(ix==jx) delta[ix][jx] = 1.;
            else       delta[ix][jx] = 0.;

            wij[ix][jx] *= 0.5*tscale;
     //       cout << wij[ix][jx] << " ";
        }
     }
     // cout << " " << tscale << "\n";

// strain rate tensor
       for(ix=0; ix<nx; ix++)
      {
          for(jx=0; jx<nx; jx++)
         {
             sij[ix][jx]= dqdx[ix][jx] + dqdx[jx][ix]; 

             if(ix==jx)
            {
                for(kx=0; kx<nx; kx++)
               { 
                   sij[ix][jx] += (-2./3.)*dqdx[kx][kx];
               }
            } 
             sij[ix][jx] *= 0.5*tscale;
     //        cout << sij[ix][jx] << " ";
         }
      }
     //  cout << " " << tscale << "\n";
       
       h_s=0;
       h_w=0;
       for(ix=0; ix<nx; ix++)
      {
          for(jx=0; jx<nx; jx++)
         {
             h_s += sij[ix][jx]*sij[jx][ix];                            
             h_w += wij[ix][jx]*wij[jx][ix];                            
         }
      } 

       h_sw=0;
       for(ix=0; ix<nx; ix++)
      {
          for(jx=0; jx<nx; jx++)
         {
             for(kx=0; kx<nx; kx++)
            {
                h_sw += sij[ix][kx]*wij[kx][jx]*wij[jx][ix];    
            }    
         }
      }

// tensor basis coefficient
       C1p = (C1-1.)*9./4.;
       N = C1p + sqrt(2*stbeta*h_s)*9./4.;
       Q = (N*N - 2*h_w)/A1;
       Q1 = (2*N*N-h_w)*Q/6.;
       if(Q>=0.)  Q += small;
       else       Q -= small;
       if(Q1>=0.) Q1+= small;
       else       Q1-= small;
       b1 = -1. * N/Q;
       b3 = -1. * 2.*h_sw/(N*Q1);
       b4 = -1. * 1./Q;
       b6 = -1. * N/Q1;
       b[1] = b1;
       b[3] = b3;
       b[4] = b4;
       b[6] = b6;

       //cout << b1 << " " << b3 << " " << b4 << " " << b6 << "\n";
// tensor component basis  
       for(ix=0; ix<nx; ix++)
      {
          for(jx=0; jx<nx; jx++)
         {
             t1[ix][jx] = sij[ix][jx];
             t3[ix][jx] = 0;
             t4[ix][jx] = 0;
             for(kx=0; kx<nx; kx++)
            {
               t3[ix][jx] += wij[ix][kx]*wij[kx][jx] - h_w*delta[ix][jx]*1./3.;
               t4[ix][jx] += sij[ix][kx]*wij[kx][jx] - wij[ix][kx]*sij[kx][jx];
            }
             t6[ix][jx] = 0;
             for(kx=0; kx<nx; kx++)
            {
                for(lx=0; lx<nx; lx++)
               {
                   t6[ix][jx] += sij[ix][kx]*wij[kx][lx]*wij[lx][jx] + wij[ix][kx]*wij[kx][lx]*sij[lx][jx];
               }
            }
             t6[ix][jx] += (-1.)*delta[ix][jx]*h_sw*2./3.;
             t6[ix][jx] += (-1.)*h_w*sij[ix][jx];

             //a[ix][jx] = b1*t1[ix][jx] + b3*t3[ix][jx] + b4*t4[ix][jx] + b6*t6[ix][jx];
             a[ix][jx] = b1*t1[ix][jx];
             a0[ix][jx] = a[ix][jx];

             //tau[ix][jx] = (-1.)*rho*k*(a[ix][jx] + delta[ix][jx]*2./3.);
             tau[ix][jx] = (rho*k/omega)*sij[ix][jx];

             //cout << tau[ix][jx] << " ";
         } 
      }
       //cout << "\n";
  }

   void dreynoldstress( Int nx, Real rho, Real k, Real omega, Real drho, Real dk, Real dqdx[3][3], Real ddqdx[3][3], 
                        Real stbeta, Real mu0, Real a[3][3], Real *b, Real dtau[3][3] )
  {
      cout << " get here???????????\n";
      Int ix, jx, kx, lx;
      Real dt1[3][3], dt3[3][3], dt4[3][3], dt6[3][3], da[3][3], delta[3][3];
      Real sij[3][3], wij[3][3];
      Real dsij[3][3], dwij[3][3];
      Real dh_w, dh_sw, h_w;   
      Real b1, b3, b4, b6;
      Real tscale;    
 
//turbulence time scale
      tscale = fmax(1./(stbeta*omega), 6.*sqrt(mu0/(stbeta*rho*fmax(k,1e-16)*omega)));

// vorticity rate tensor
      for(ix=0; ix<nx; ix++)
     {
         for(jx=0; jx<nx; jx++)
        {
            wij[ix][jx]= dqdx[ix][jx] - dqdx[jx][ix]; 
            dwij[ix][jx]= ddqdx[ix][jx] - ddqdx[jx][ix]; 

            if(ix==jx) delta[ix][jx] = 1.;
            else       delta[ix][jx] = 0.;

            wij[ix][jx] *= 0.5*tscale;
            dwij[ix][jx] *= 0.5*tscale;
        }
     }

// strain rate tensor
      for(ix=0; ix<nx; ix++)
     {
         for(jx=0; jx<nx; jx++)
        {
            sij[ix][jx]= dqdx[ix][jx] + dqdx[jx][ix]; 
            dsij[ix][jx]= ddqdx[ix][jx] + ddqdx[jx][ix]; 

            if(ix==jx)
           {
               for(kx=0; kx<nx; kx++)
              { 
                  sij[ix][jx]  += (-2./3.)*dqdx[kx][kx];
                  dsij[ix][jx] += (-2./3.)*ddqdx[kx][kx];
              }
           } 
            sij[ix][jx] *= 0.5*tscale;
            dsij[ix][jx] *= 0.5*tscale;
        }
     }

      
      //dh_s=0;
      dh_w=0;
      h_w =0;
      for(ix=0; ix<nx; ix++)
     {
         for(jx=0; jx<nx; jx++)
        {
            //dh_s += sij[ix][jx]*sij[jx][ix];                            
            dh_w += dwij[ix][jx]*wij[jx][ix] + wij[ix][jx]*dwij[jx][ix];                            
            h_w  += wij[ix][jx]*wij[jx][ix];                            
        }
     } 

      dh_sw=0;
      for(ix=0; ix<nx; ix++)
     {
         for(jx=0; jx<nx; jx++)
        {
            for(kx=0; kx<nx; kx++)
           {
               //h_sw += sij[ix][kx]*wij[kx][jx]*wij[jx][ix];    
               dh_sw += dsij[ix][kx] * wij[kx][jx] * wij[jx][ix] +   
                         sij[ix][kx] *dwij[kx][jx] * wij[jx][ix] +    
                         sij[ix][kx] * wij[kx][jx] *dwij[jx][ix];    
           }    
        }
     }


//tensor coefficient
      b1 = b[1];
      b3 = b[3];
      b4 = b[4];
      b6 = b[6];

// tensor component basis  
      //dT1
       for(ix=0; ix<nx; ix++)
      {
          for(jx=0; jx<nx; jx++)
         {
             dt1[ix][jx] = dsij[ix][jx];
         }
      }

       //dT3
       for(ix=0; ix<nx; ix++)
      {
          for(jx=0; jx<nx; jx++)
         {
             for(kx=0; kx<nx; kx++)
            {   
                dt3[ix][jx] = dwij[ix][kx]*wij[kx][jx] + wij[ix][kx]*dwij[kx][jx] - dh_w*delta[ix][jx]*1./3.;
            }
         }
      }

       //dT4
       for(ix=0; ix<nx; ix++)
      {
          for(jx=0; jx<nx; jx++)
         {
             for(kx=0; kx<nx; kx++)
            {   
                dt4[ix][jx] = dsij[ix][kx]*sij[kx][jx] + sij[ix][kx]*dsij[kx][jx] - 
                             (dwij[ix][kx]*wij[kx][jx] + wij[ix][kx]*dwij[ix][jx]);
            }
         }
      }

       //dT6
       for(ix=0; ix<nx; ix++)
      {
          for(jx=0; jx<nx; jx++)
         {
             for(kx=0; kx<nx; kx++)
            {  
                for(lx=0; lx<nx; lx++)
               {
                   Real tmpa, tmpb, tmpc, tmpd; 
                   tmpa = dsij[ix][kx] * wij[kx][lx] * wij[lx][jx] + 
                           sij[ix][kx] *dwij[kx][lx] * wij[lx][jx] + 
                           sij[ix][kx] * wij[kx][lx] *dwij[lx][jx];

                   tmpb = dwij[ix][kx] * wij[kx][lx] * sij[lx][jx] + 
                           wij[ix][kx] *dwij[kx][lx] * sij[lx][jx] + 
                           wij[ix][kx] * wij[kx][lx] *dsij[lx][jx];

                   tmpc = delta[ix][jx] * dh_sw;
 
                   tmpd = dh_w*sij[ix][jx] + h_w*dsij[ix][jx];

                   dt6[ix][jx] = tmpa + tmpb - 2.*tmpc/3. - tmpd;
                                 
               }
            }
         }
      }

       //assume b1~b6 are constant
       for(ix=0; ix<nx; ix++)
      {
          for(jx=0; jx<nx; jx++)
         {
             da[ix][jx] = b1*dt1[ix][jx] + b3*dt3[ix][jx] + b4*dt4[ix][jx] + b6*dt6[ix][jx]; 
         }
      }

       //compute dtau
       for(ix=0; ix<nx; ix++)
      {
          for(jx=0; jx<nx; jx++)
         {
             dtau[ix][jx] = k*a[ix][jx]*drho + rho*a[ix][jx]*dk + rho*k*da[ix][jx] + rho*delta[ix][jx]*dk*2./3. + 
                            k*delta[ix][jx]*drho*2./3.;
             dtau[ix][jx] *= -1.;
         } 
      }
  }

   void cSBslEarsm::maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[],  
                          Real lmixmax )
  {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,omega,k;
      Real       sij[2][2];
      Real       s;
      Real       d;
      Real       r1,r2;
      Real       cdkw;
      Real       arg1;
      Real       f1;

 
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {

// strain rate tensors
            s = 0;
            for(ix=0; ix<nx; ix++)
           {
               for(jx=0; jx<nx; jx++)
              {
                  sij[ix][jx]= 0.5*( dqdx[ix][jx][iq]+ dqdx[jx][ix][iq] ); 
                  s += sij[ix][jx] * sij[ix][jx];
              }
           }
            s=  sqrt(2*s);

            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            d= dst[0][iq];

            //q[nv0][iq] = fmax(q[nv0][iq], 1e-16);
            //k= q[nv0][iq];
            q[nv0+1][iq] = fmax(q[nv0+1][iq], 1e-3);
            omega= q[nv0+1][iq];

            r1=sqrt(k)/(stbeta*omega*d);
            r2=500.*mu/(rho*d*d*omega);
            cdkw = 0;
            for(ix=0; ix<nx; ix++)
           {
               cdkw += dqdx[nv0][ix][iq]*dqdx[nv0+1][ix][iq];
           }
            cdkw= 2.*rho*sigmaw2*cdkw/omega;
            arg1= max(r1,r2);
            arg1= min(arg1,4.*rho*sigmaw2*k/(max(cdkw,1e-10)*d*d));
            f1= tanh(arg1*arg1*arg1*arg1);


            //this part is same as k-omega 
            mut= rho*k/omega;
            mut= fmin( mut,10000.*mu );
            k= omega*mut/rho;
            q[nv0][iq]= k;
 

            kappat= cp*mut;   // unit turbulent Prandtl number
            aux[naux-5][iq]= cdkw;
            aux[naux-4][iq]= f1;
            aux[naux-3][iq]= 0.;
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;
        }
     }
  }

   /*void cSBslEarsm::srhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Int *idst[],
                          Int *igdst[], Real *wq[], Real *rhs[], Real *lhs[] )

  {
      Int iq,ix, jx;
      Real utau,rho,k,omega,y,sqrtb,mu0,prodk, mut, mu;
      Real tau[3][3];
      Real f1;
      Real gam,beta;
      Real cdkw;
      Real prodkt;
      Real tmpdqdx[3][3], a[3][3], b[20];

      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(stbeta);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut = mu-mu0;
            f1= aux[naux-4][iq];
            gam=f1*gam1+(1.-f1)*gam2;
            beta=f1*beta1+(1.-f1)*beta2;
            cdkw= aux[naux-5][iq];

            for(ix=0; ix<nx; ix++)
           {
               for(jx=0; jx<nx; jx++)
              {
                  tmpdqdx[ix][jx] = dqdx[ix][jx][iq];    
              }
           }
            
            if(  utau == 0 )
            //if( utau <= 0 )
           {

               reynoldstress( nx, rho, k, omega, tmpdqdx, stbeta, mu0, tau, a, b );

//production term
               prodk = 0;
               for(ix=0; ix<nx; ix++)
              {
                  for(jx=0; jx<nx; jx++)
                 {
                     prodk += tau[ix][jx]*dqdx[ix][jx][iq];
                 }
              }
               prodkt= min(prodk,10.*stbeta*rho*k*omega);
               rhs[nv0][iq]+= wq[0][iq]*( prodkt-stbeta*rho*k*omega );
               //rhs[nv0+1][iq]+= wq[0][iq]*( gam/max(mut/rho,1e-16)*prodkt-beta*rho*omega*omega+(1.-f1)*cdkw );
               //rhs[nv0+1][iq] +=  wq[0][iq]*(gam/(fmax(k/omega,1e-16)*prodkt)
               //                 - beta*rho*omega*omega 
               //                 + (1.-f1)*cdkw );
               rhs[nv0+1][iq]+= wq[0][iq]*( gam/max(mut,1e-16)*prodkt-beta*rho*omega*omega+(1.-f1)*cdkw );

           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*rhs[0][iq];
              }
               else
              {
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(6*mu0/(rho*beta*y*y)-omega)+ omega*rhs[0][iq];
              }
           }
        }
     }
  }*/

   void cSBslEarsm::srhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Int *idst[],
                          Int *igdst[], Real *wq[], Real *rhs[], Real *lhs[] )

  {
      Int iq,iv,ix;
      Real utau,rho,k,omega,y,sqrtb,mu,mu0,mut,prodk,div;
      Real tau[3][3];
      Real sij[3][3];
      Real f1;
      Real gam,beta;
      Real cdkw;
      Real prodkt;
      Real cw=1.0;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(stbeta);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut=mu-mu0;
            f1= aux[naux-4][iq];
            gam=f1*gam1+(1.-f1)*gam2;
            beta=f1*beta1+(1.-f1)*beta2;
            cdkw= aux[naux-5][iq];
            
            if(  utau == 0 )
            //if( utau <= 0 )
           {
// strain rate tensor

               sij[0][0]= 0.5*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               sij[1][0]= 0.5*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               sij[0][1]= sij[1][0];
               sij[1][1]= 0.5*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
   
// stress tensor
               tau[0][0]= 2.*mut*sij[0][0]; 
               tau[1][0]= 2.*mut*sij[1][0]; 

               tau[0][1]=  tau[1][0];
               tau[1][1]= 2.*mut*sij[1][1]; 

               div=  2./3.*mut*dqdx[0][0][iq];
               div+= 2./3.*mut*dqdx[1][1][iq];

               tau[0][0]-= div;
               tau[1][1]-= div;

               tau[0][0]-= 2./3.*rho*k;
               tau[1][1]-= 2./3.*rho*k;

               prodk=  tau[0][0]*dqdx[0][0][iq];
               prodk+= tau[1][0]*dqdx[1][0][iq];
               prodk+= tau[0][1]*dqdx[0][1][iq];
               prodk+= tau[1][1]*dqdx[1][1][iq];

               prodkt= min(prodk,10.*stbeta*rho*k*omega);
               rhs[nv0][iq]+= wq[0][iq]*( prodkt-stbeta*rho*k*omega );
               //rhs[nv0+1][iq]+= wq[0][iq]*( gam*rho*s2-beta*rho*omega*omega+(1.-f1)*cdkw );
               //rhs[nv0+1][iq]+= wq[0][iq]*( gam*fmin(1./max(mut/rho,1e-16)*prodk, 10*stbeta*rho*omega*omega)-beta*rho*omega*omega+(1.-f1)*cdkw );
               rhs[nv0+1][iq]+= wq[0][iq]*( gam/max(mut/rho,1e-16)*prodkt-beta*rho*omega*omega+(1.-f1)*cdkw );
 
           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*rhs[0][iq];
              }
               else
              {
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(6*mu0/(rho*beta*y*y)-omega)+ omega*rhs[0][iq];
              }
           }
        }
     }
  }

   /*void cSBslEarsm::mflx( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], 
                          Real *rhsl[], Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[],
                          Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl;
      Real            dqnr[MxNVs],dqtr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa;
      Real            mu0, mut, f1, sigmak,sigmaw, k, omega, a[3][3], b[20];

// temporary location: move to viscous behaviour class

      //cout << "mflx ================= \n";
      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

            //cout << iql << " " << iqr << "\n";

// distance of DOF positions from face centre
            wr = 0;
            wl = 0;
            for(ix=0; ix<nx; ix++)
           {
               wr +=  wc[ix][ic]*( xc[ix][ic] - xl[ix][iql] );
               wl +=  wc[ix][ic]*( xr[ix][iqr]- xc[ix][ic] );
           }
            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            for( iv=0;iv<nv;iv++ )
           {
               dqnl[iv] = 0;
               dqnr[iv] = 0;
               for(ix=0; ix<nx; ix++)
              {
                  dqnl[iv] +=  dqdxl[iv][ix][iql]*wc[ix][ic];
                  dqnr[iv] +=  dqdxr[iv][ix][iqr]*wc[ix][ic];
              }
               dqn[iv]= ( qr[iv][iqr]-ql[iv][iql] )/w;
               q[iv]  = wl*ql[iv][iql]+ wr*qr[iv][iqr];
           }

// tangential gradients

            for( iv=0;iv<nv;iv++ )
           {
               for(ix=0; ix<nx; ix++)
              {
                  dqtl= dqdxl[iv][ix][iql]- wc[ix][ic]*dqnl[iv];
                  dqtr= dqdxr[iv][ix][iqr]- wc[ix][ic]*dqnr[iv];
                  dqt[iv][ix]= wl*dqtl+ wr*dqtr;
              }
           }

// reconstruct gradient
            for( iv=0;iv<nv;iv++ )
           {
               for(ix=0; ix<nx; ix++)
              {
                  dqdx[iv][ix]= dqn[iv]*wc[ix][ic]+ dqt[iv][ix];
              }
           }

// stress tensor
            mu0=   wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu=    wl*auxl[naux-2 ][iql]+ wr*auxr[naux-2 ][iqr];
            kappa= wl*auxl[naux-1 ][iql]+ wr*auxr[naux-1 ][iqr];
            rho=   wl*auxl[     0 ][iql]+ wr*auxr[     0 ][iqr];
            k=     wl*ql[nv0      ][iql]+ wr*qr[nv0      ][iqr];
            omega= wl*ql[nv0+1    ][iql]+ wr*qr[nv0+1    ][iqr];

            //cout << iql << " " << iqr << " ==== \n";
            reynoldstress( nx, rho, k, omega, dqdx, stbeta, mu0, tau, a, b );
            //for(ix=0; ix<nx; ix++)
           //{
           //    for(int jx=0; jx<nx; jx++)
           //   {
           //       cout << tau[ix][jx] << " ";
           //   }
           //}
           //    cout << "\n";
            

// viscous fluxes
            if(nx==3)
           {
               taun[1]=  tau[0][0]*wc[0][ic];
               taun[1]+= tau[0][1]*wc[1][ic];
               taun[1]+= tau[0][2]*wc[2][ic];
   
               taun[2]=  tau[1][0]*wc[0][ic];
               taun[2]+= tau[1][1]*wc[1][ic];
               taun[2]+= tau[1][2]*wc[2][ic];
   
               taun[3]=  tau[2][0]*wc[0][ic];
               taun[3]+= tau[2][1]*wc[1][ic];
               taun[3]+= tau[2][2]*wc[2][ic];


               taun[4]= -kappa*dqn[3];
               taun[4]+= taun[1]*q[0];
               taun[4]+= taun[2]*q[1];
               taun[4]+= taun[3]*q[2];


           }
            else if(nx==2)
           {
               taun[1]=  tau[0][0]*wc[0][ic];
               taun[1]+= tau[0][1]*wc[1][ic];

               taun[2]=  tau[1][0]*wc[0][ic];
               taun[2]+= tau[1][1]*wc[1][ic];
   
               taun[3]= -kappa*dqn[2];
               taun[3]+= taun[1]*q[0];
               taun[3]+= taun[2]*q[1];
           }

            mut= mu-mu0;
            f1= wl*auxl[naux-4][iql] +wr*auxr[naux-4][iqr];
            sigmak=f1*sigmak1+(1.-f1)*sigmak2;
            sigmaw=f1*sigmaw1+(1.-f1)*sigmaw2;

            taun[nv0]= -(mu0+ sigmak*mut)*dqn[nv0];
            taun[nv0+1]= -(mu0+ sigmaw*mut)*dqn[nv0+1];

// accumulate
            for( iv=1;iv<nv;iv++ )
           {
               rhsr[iv][iqr]+= taun[iv]*wc[nx][ic];
               rhsl[iv][iql]-= taun[iv]*wc[nx][ic];
           }
            auxc[nauxf-1][ic]+= wc[nx][ic]*mu/(rho*w);
        }
     }
  }*/

   void cSBslEarsm::dmflx(Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[],Real *dauxl[], 
                          Real *resl[], Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], 
                          Real *resr[], Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      assert(0);
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa;
      Real            mu0, mut, f1, sigmak,sigmaw, k, omega, drho, dk, a[3][3], b[20];

     //dql: variation of conservative variables
     //dauxl: variation of primitive variables

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre
            wl = 0.;
            wr = 0.;
            for(ix=0; ix<nx; ix++)
           {
               wr +=  wc[ix][ic]*( xc[ix][ic]- xl[ix][iql] );
               wl +=  wc[ix][ic]*( xr[ix][iqr]- xc[ix][ic] );
           }
            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv;iv++ )
           {
               ddqn[iv]= ( dauxr[iv][iqr]- dauxl[iv][iql] )/w;
               dqn[iv]=  ( qr[iv][iqr]-       ql[iv][iql] )/w;

               q[iv]=  wl*   ql[iv][iql]+ wr*   qr[iv][iqr];
               dq[iv]= wl*dauxl[iv][iql]+ wr*dauxr[iv][iqr];
           }
            drho = wl*dql[0][iql] + wr*dql[0][iqr];
            dk   = dq[nv0];

            for( iv=0;iv<nv;iv++ )
           {
               for(ix=0; ix<nx; ix++)
              {
                  dqdx[iv][ix]= dqn[iv]*wc[ix][ic];
                  ddqdx[iv][ix]= ddqn[iv]*wc[ix][ic];
              }
           }

// stress tensor
            mu0=   wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu=    wl*auxl[naux-2 ][iql]+ wr*auxr[naux-2 ][iqr];
            kappa= wl*auxl[naux-1 ][iql]+ wr*auxr[naux-1 ][iqr];
            rho=   wl*auxl[     0 ][iql]+ wr*auxr[     0 ][iqr];
            k=     wl*ql[nv0       ][iql]+ wr*qr[nv0       ][iqr];
            omega= wl*ql[nv0+1     ][iql]+ wr*qr[nv0+1     ][iqr];

            reynoldstress( nx, rho, k, omega, dqdx, stbeta, mu0, tau, a, b );
            dreynoldstress( nx, rho, k, omega, drho, dk, dqdx, ddqdx, stbeta, mu0, a, b, dtau );

// viscous flux
            if(nx==3)
           {
               taun[1]=    tau[0][0]*wc[0][ic];
               taun[1]+=   tau[0][1]*wc[1][ic];
               taun[1]+=   tau[0][2]*wc[2][ic];
   
               taun[2]=    tau[1][0]*wc[0][ic];
               taun[2]+=   tau[1][1]*wc[1][ic];
               taun[2]+=   tau[1][2]*wc[2][ic];
   
               taun[3]=    tau[2][0]*wc[0][ic];
               taun[3]+=   tau[2][1]*wc[1][ic];
               taun[3]+=   tau[2][2]*wc[2][ic];
   
               dtaun[1]=   dtau[0][0]*wc[0][ic];
               dtaun[1]+=  dtau[0][1]*wc[1][ic];
               dtaun[1]+=  dtau[0][2]*wc[2][ic];
   
               dtaun[2]=   dtau[1][0]*wc[0][ic];
               dtaun[2]+=  dtau[1][1]*wc[1][ic];
               dtaun[2]+=  dtau[1][2]*wc[2][ic];
   
               dtaun[3]=   dtau[2][0]*wc[0][ic];
               dtaun[3]+=  dtau[2][1]*wc[1][ic];
               dtaun[3]+=  dtau[2][2]*wc[2][ic];
   
               dtaun[4]= -kappa*ddqn[3];
               dtaun[4]+= taun[1]*dq[0]+ dtaun[1]*q[0];
               dtaun[4]+= taun[2]*dq[1]+ dtaun[2]*q[1];
               dtaun[4]+= taun[3]*dq[2]+ dtaun[3]*q[2];


           }
            else if(nx==2)
           {
               taun[1]=    tau[0][0]*wc[0][ic];
               taun[1]+=   tau[0][1]*wc[1][ic];
   
               taun[2]=    tau[1][0]*wc[0][ic];
               taun[2]+=   tau[1][1]*wc[1][ic];
   
               dtaun[1]=   dtau[0][0]*wc[0][ic];
               dtaun[1]+=  dtau[0][1]*wc[1][ic];
   
               dtaun[2]=   dtau[1][0]*wc[0][ic];
               dtaun[2]+=  dtau[1][1]*wc[1][ic];
   
               dtaun[3]= -kappa*ddqn[2];
               dtaun[3]+= taun[1]*dq[0]+ dtaun[1]*q[0];
               dtaun[3]+= taun[2]*dq[1]+ dtaun[2]*q[1];
           }          

// diffusion for k and omega
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            f1= wl*auxl[naux-4][iql] +wr*auxr[naux-4][iqr];
            sigmak=f1*sigmak1+(1.-f1)*sigmak2;
            sigmaw=f1*sigmaw1+(1.-f1)*sigmaw2;

            dtaun[nv0]= -(mu0+ sigmak*mut)*ddqn[nv0];
            dtaun[nv0+1]= -(mu0+ sigmaw*mut)*ddqn[nv0+1];
 
// accumulate

            for( iv=1;iv<nv;iv++ )
           {
               resr[iv][iqr]+= dtaun[iv]*wc[nx][ic];
               resl[iv][iql]-= dtaun[iv]*wc[nx][ic];
           }
        }
     }
  }

   void cSBslEarsm::mflx22( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], 
                            Real *rhsl[], Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[],
                            Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      assert(0);
  }

   void cSBslEarsm::mflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], 
                            Real *rhsl[], Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[],
                            Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      assert(0);
  }

   void cSBslEarsm::mflx23( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], 
                            Real *rhsl[], Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[],
                            Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      assert(0);
  }

   void cSBslEarsm::srhs22( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[],
                            Real *rhs[], Real *lhs[] )
  {
      assert(0);
  }

   void cSBslEarsm::srhs23( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[],
                            Real *rhs[], Real *lhs[] )
  {
      assert(0);
  }

   void cSBslEarsm::srhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[],
                            Real *rhs[], Real *lhs[] )
  {
      assert(0);
  }

   void cSBslEarsm::maux22( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      assert(0);
  }

   void cSBslEarsm::maux23( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      assert(0);
  }

   void cSBslEarsm::maux33( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      assert(0);
  }

   void cSBslEarsm::dmflx22(Int ics,Int ice,Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[],Real *dauxl[], 
                          Real *resl[], Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], 
                          Real *resr[], Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
     assert(0);
  }

   void cSBslEarsm::dmflx23(Int ics,Int ice,Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[],Real *dauxl[], 
                          Real *resl[], Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], 
                          Real *resr[], Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
     assert(0);
  }

   void cSBslEarsm::dmflx33(Int ics,Int ice,Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[],Real *dauxl[], 
                          Real *resl[], Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], 
                          Real *resr[], Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
     assert(0);
  }

   void cSBslEarsm::mflx( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                      Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;
      Real            f1,sigmak,sigmaw;

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];

            dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];

            dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];

            dqnl[nv0+1]=  dqdxl[nv0+1][0][iql]*wc[0][ic];
            dqnl[nv0+1]+= dqdxl[nv0+1][1][iql]*wc[1][ic];

            dqnr[nv0+1]=  dqdxr[nv0+1][0][iqr]*wc[0][ic];
            dqnr[nv0+1]+= dqdxr[nv0+1][1][iqr]*wc[1][ic];

            dqn[nv0+1]= ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;
            q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];

// tangential gradients

            dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][0][iql]- wc[0][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][0][iqr]- wc[0][ic]*dqnr[nv0+1];
            dqt[nv0+1][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][1][iql]- wc[1][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][1][iqr]- wc[1][ic]*dqnr[nv0+1];
            dqt[nv0+1][1]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];

            dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic]+ dqt[nv0+1][0];
            dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic]+ dqt[nv0+1][1];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            f1= wl*auxl[naux-4][iql] +wr*auxr[naux-4][iqr];
            sigmak=f1*sigmak1+(1.-f1)*sigmak2;
            sigmaw=f1*sigmaw1+(1.-f1)*sigmaw2;

            taun[nv0]= -(mu0+ sigmak*mut)*dqn[nv0];
            taun[nv0+1]= -(mu0+ sigmaw*mut)*dqn[nv0+1];
// accumulate
            rhsr[nv0][iqr]+=   taun[nv0]*  wc[2][ic];
            rhsr[nv0+1][iqr]+= taun[nv0+1]*wc[2][ic];

            rhsl[nv0][iql]-=   taun[nv0]*  wc[2][ic];
            rhsl[nv0+1][iql]-= taun[nv0+1]*wc[2][ic];

        }
     }
  }

/*      Int iq,iv,ix;
      Real utau,rho,k,omega,y,sqrtb,mu,mu0,mut,prodk,div;
      Real tau[3][3];
      Real sij[3][3];
      Real f1;
      Real gam,beta;
      Real cdkw;
      Real prodkt;
      Real cw=1.0;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(stbeta);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut=mu-mu0;
            f1= aux[naux-4][iq];
            gam=f1*gam1+(1.-f1)*gam2;
            beta=f1*beta1+(1.-f1)*beta2;
            cdkw= aux[naux-5][iq];
            
            if(  utau == 0 )
            //if( utau <= 0 )
           {
// strain rate tensor

               sij[0][0]= 0.5*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               sij[1][0]= 0.5*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               sij[0][1]= sij[1][0];
               sij[1][1]= 0.5*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
   
// stress tensor
               tau[0][0]= 2.*mut*sij[0][0]; 
               tau[1][0]= 2.*mut*sij[1][0]; 

               tau[0][1]=  tau[1][0];
               tau[1][1]= 2.*mut*sij[1][1]; 

               div=  2./3.*mut*dqdx[0][0][iq];
               div+= 2./3.*mut*dqdx[1][1][iq];

               tau[0][0]-= div;
               tau[1][1]-= div;

               tau[0][0]-= 2./3.*rho*k;
               tau[1][1]-= 2./3.*rho*k;

               prodk=  tau[0][0]*dqdx[0][0][iq];
               prodk+= tau[1][0]*dqdx[1][0][iq];
               prodk+= tau[0][1]*dqdx[0][1][iq];
               prodk+= tau[1][1]*dqdx[1][1][iq];

               prodkt= min(prodk,10.*stbeta*rho*k*omega);
               rhs[nv0][iq]+= wq[0][iq]*( prodkt-stbeta*rho*k*omega );
               //rhs[nv0+1][iq]+= wq[0][iq]*( gam*rho*s2-beta*rho*omega*omega+(1.-f1)*cdkw );
               //rhs[nv0+1][iq]+= wq[0][iq]*( gam*fmin(1./max(mut/rho,1e-16)*prodk, 10*stbeta*rho*omega*omega)-beta*rho*omega*omega+(1.-f1)*cdkw );
               rhs[nv0+1][iq]+= wq[0][iq]*( gam/max(mut/rho,1e-16)*prodkt-beta*rho*omega*omega+(1.-f1)*cdkw );
 
           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*rhs[0][iq];
              }
               else
              {
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(6*mu0/(rho*beta*y*y)-omega)+ omega*rhs[0][iq];
              }
           }
        }
     }*/
