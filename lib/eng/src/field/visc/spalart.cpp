using namespace std;

#  include <field/visc.h>

const Real prodfct = 1.0;

   cSpalart::cSpalart()
  {
      karm=0.41;
      b=5.1;
      sigma=2./3.;
      cv1=7.1;;
      cv13=cv1*cv1*cv1;
      cb1=0.1335;
      cb2=0.622;
      cw1= cb1/(karm*karm)+(1+cb2)/sigma;
      cw2=0.3;
      cw3=2.;
      cw36=64.;
  };
   void cSpalart::setvrs( Int Nx, Int Nvel, Int *Nv, Int *Naux, Int *Nauxf, Int *Nlhs )
  {
      nx= Nx;
      nvel= Nvel;

     
      nv0=*(Nv);
    (*Nv)+=1;
      nv= *Nv;

      nauxf0=*Nauxf;
    (*Nauxf)+=1;
      nauxf= *Nauxf;

      nlhs0=*Nlhs;
    (*Nlhs)+=1;
      nlhs= *Nlhs;

      naux0=*Naux; 
    (*Naux)+= 3;
      naux=(*Naux);

  };
   void cSpalart::nondim( Int iqs, Int iqe, Real *q[], Int *idone )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
         if( idone[nv0] != 1 )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[nv0][iq]=1.e-4;
           }
        }
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[nv0][iq]/= 100.;
        }
     }
  }

   void cSpalart::redim( Int iqs, Int iqe, Real *q[] )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[nv0][iq]*= 100.;
        }
     }
  }

   void cSpalart::maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       fv1,chi,chi3,nu,mu,rho,kappa,mut,kappat,cp,nut;
    
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            nu= mu/rho;
            q[nv0][iq]= fmax( q[nv0][iq],small );
            //q[nv0][iq]= fmin( q[nv0][iq],800*mu/rho );    // MAURO the limit was 1500
            //q[nv0][iq]= fmin( q[nv0][iq],1500*mu/rho );    // MAURO the limit was 1500
            //q[nv0][iq]= fmin( q[nv0][iq],2000*mu/rho );    // MAURO the limit was 1500
            //q[nv0][iq]= fmin( q[nv0][iq],5000*mu/rho );    // MAURO the limit was 1500
            //q[nv0][iq]= fmin( q[nv0][iq],7500*mu/rho );    // MAURO the limit was 1500
            q[nv0][iq]= fmin( q[nv0][iq],10000*mu/rho );    // MAURO the limit was 1500
            //q[nv0][iq]= fmin( q[nv0][iq],100*mu/rho );
            nut= q[nv0][iq];
            chi= nut/nu;
            chi3= chi*chi*chi;
            fv1= chi3/(chi3+cv13);
            mut= rho*fv1*nut;
            kappat= cp*mut;   
            aux[naux-3][iq]= 0.;
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;
        }
     }
  }

   void cSpalart::mwflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                          Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                     Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      if( ice > ics )
     {

         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               mwflx22( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
           }
            else
           {
               if( nvel == 3 )
              {
                  mwflx23( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
           }
        }
     }
  }


   void cSpalart::mwflx22( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                       Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );

// tangential velocity
            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            utau= auxl[0][iql];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               auxr[naux-3][iqr]=-1;
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[2][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[2][ic];

            f[3]=  f[1]*ql[0][iql];
            f[3]+= f[2]*ql[1][iql];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[2][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;
        }
     }
  }
   void cSpalart::mwflx23( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                    Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

// distance
            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );

// tangential velocity
            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];
            ut[2]= qr[2][iqr]- ql[2][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];
            utg= sqrt(utg);
            utg=fmax(utg,umin);

            utau= auxl[0][iql];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               auxr[naux-3][iqr]=-1;
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[2][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[2][ic];
            f[3]= -rho*ut[2]*fabs(ut[2])*wc[2][ic];

            f[4]=  f[1]*ql[0][iql];
            f[4]+= f[2]*ql[1][iql];
            f[4]+= f[3]*ql[2][iql];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];
            rhsl[4][iql]-= f[4];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];
            rhsr[4][iqr]+= f[4];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[2][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;

        }
     }
  }
   void cSpalart::mwflx33( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                    Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

// distance
            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );

// tangential velocity
            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];
            ut[2]= qr[2][iqr]- ql[2][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];
            un+= ut[2]*wc[2][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;
            ut[2]-= wc[2][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];
            utg= sqrt(utg);
            utg=fmax(utg,umin);

            utau= auxl[0][iql];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               auxr[naux-3][iqr]=-1;
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];

            f[4]=  f[1]*ql[0][iql];
            f[4]+= f[2]*ql[1][iql];
            f[4]+= f[3]*ql[2][iql];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];
            rhsl[4][iql]-= f[4];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];
            rhsr[4][iqr]+= f[4];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[3][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;
        }
     }
  }

   void cSpalart::vlhs( Int iqs, Int iqe, Real cfl, Real *wq[], Real *lhs[] )
  {
      Int             ia,ja,iq;
      Real            utau;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            lhs[nlhs0-1][iq]+= lhs[nlhs-1][iq]; 
        }
     }
  }

   void cSpalart::invdg( Int iqs, Int iqe, Real *lhs[], Real *res[] )
  {
      Int iv,iq;
      if( iqe > iqs )
     {

         for( iq=iqs;iq<iqe;iq++ )
        {
            res[nv0][iq]/=   lhs[nlhs0-1][iq]; 
        }
     }
  }
   void cSpalart::ilhs( Int ics, Int ice, Int *icql, Real *ql[], Real *auxl[], Real *lhsl[], 
                                      Int *icqr, Real *qr[], Real *auxr[], Real *lhsr[], 
                                                 Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Int             ia,ja,iql,iqr,ic;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        { 

            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };
       
            lhsl[nlhs0-1][iql]+= auxc[nauxf-1][ic];
            lhsr[nlhs0-1][iqr]+= auxc[nauxf-1][ic];
        }
     }
  }

   void cSpalart::srhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Int *idst[], Int *igdst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real nuy,utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2;
      Real omg[3];

      if( iqe > iqs )
     {
         if( nx == 2 )
        {
            if( nvel == 2 ) 
           {
               srhs22( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
           }
            else
           {
               if( nvel == 3 )
              {
                  srhs23( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               if( nvel == 3 )
              {
                  srhs33( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
              }
           }
        }
     }
  }
   void cSpalart::srhs22( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real nuy,utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2;
      Real omg[3];

      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            if( utau >= 0 )
           {
// stress tensor
               omg[0]= dqdx[0][1][iq]+ dqdx[1][0][iq];
               s= omg[0]*omg[0];
               s= sqrt( s );
               chi= nut/nu;
               chi3= chi*chi*chi;
               fv1= chi3/( chi3+cv13 );
               fv2= 1-chi/(1+chi*fv1);
               ky= karm*y;
               ky2=ky*ky;
               s+= fv2*nut/ky2;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               nuy= nut/y;
               nuy*= nuy;
               if( nut > 1.e-12 )
              {
                  rhs[nv0][iq]+= wq[0][iq]*rho*( cb1*s*nut- (cw1*fw)*nuy );
              }

           }
            else
           {
               rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*nut;
           }
        }
     }
  }
   void cSpalart::srhs23( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real nuy,utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2;
      Real omg[3];

      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            if( utau >= 0 )
           {
// stress tensor
               omg[0]= dqdx[0][1][iq]+ dqdx[1][0][iq];
               omg[1]= dqdx[2][0][iq];
               omg[2]= dqdx[2][1][iq];
               s=  omg[0]*omg[0];
               s+= omg[1]*omg[1];
               s+= omg[2]*omg[2];
               s= sqrt( s );
               chi= nut/nu;
               chi3= chi*chi*chi;
               fv1= chi3/( chi3+cv13 );
               fv2= 1-chi/(1+chi*fv1);
               ky= karm*y;
               ky2=ky*ky;
               s+= fv2*nut/ky2;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               nuy= nut/y;
               nuy*= nuy;
               if( nut > 1.e-12 )
              {
                  rhs[nv0][iq]+= wq[0][iq]*rho*( cb1*s*nut- (cw1*fw)*nuy );
              }

           }
            else
           {
               rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*nut;
           }
        }
     }
  }
   void cSpalart::srhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real nuy,utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2;
      Real omg[3];

      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            if( utau >= 0 )
           {
// stress tensor
               omg[0]= dqdx[0][1][iq]- dqdx[1][0][iq];
               omg[1]= dqdx[2][0][iq]- dqdx[0][2][iq];
               omg[2]= dqdx[1][2][iq]- dqdx[2][1][iq];
               s=  omg[0]*omg[0];
               s+= omg[1]*omg[1];
               s+= omg[2]*omg[2];
               s= sqrt( s );
               chi= nut/nu;
               chi3= chi*chi*chi;
               fv1= chi3/( chi3+cv13 );
               fv2= 1-chi/(1+chi*fv1);
               ky= karm*y;
               ky2=ky*ky;
               s+= fv2*nut/ky2;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               nuy= nut/y;
               nuy*= nuy;
               if( nut > 1.e-12 )
              {
                  rhs[nv0][iq]+= wq[0][iq]*rho*( prodfct*cb1*s*nut- (cw1*fw)*nuy );
              }

           }
            else
           {
               rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*nut;
           }
        }
     }
  }

   void cSpalart::dsrhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
                        Real *wq[], Real *res[], Real *lhs[] )
  {
      Int iq;
      Real nuy,utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2,drho,dnut;
      Real omg[3];
      if( iqe > iqs )
     {
         if( nx == 2 )
        {
            if( nvel == 2 ) 
           {
               dsrhs22( iqs,iqe,  cfl, q,aux,dq,daux,dqdx, dst,wq,res, lhs );
           }
            else
           {
               if( nvel == 3 )
              {
                  dsrhs23( iqs,iqe,  cfl, q,aux,dq,daux,dqdx, dst,wq,res, lhs );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               if( nvel == 3 )
              {
                  dsrhs33( iqs,iqe,  cfl, q,aux,dq,daux,dqdx, dst,wq,res, lhs );
              }
           }
        }

     }
  }
   void cSpalart::dsrhs22( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
                        Real *wq[], Real *res[], Real *lhs[] )
  {
      Int iq;
      Real nuy,utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2,drho,dnut;
      Real omg[3];
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            drho= dq[0][iq];
            dnut=daux[nv0][iq];
            if( utau >=  0 )
           {
               omg[0]= dqdx[0][1][iq]+ dqdx[1][0][iq];
               s= omg[0]*omg[0];
               s= sqrt( s );
               chi= nut/nu;
               chi3= chi*chi*chi;
               fv1= chi3/( chi3+cv13 );
               fv2= 1-chi/(1+chi*fv1);
               ky= karm*y;
               ky2=ky*ky;
               s+= fv2*nut/ky2;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               nuy= nut/y;
               nuy*= nuy;
               if( nut > 1.e-12 )
              {
                  res[nv0][iq]+= wq[0][iq]*drho*( cb1*s*nut- (cw1*fw)*nuy )+ 
                                 wq[0][iq]*rho*dnut*( cb1*s- 2*(cw1*fw)*nut/(y*y) );
              }

           }
            else
           {
               res[nv0][iq]= - lhs[nlhs-1][iq]*rho*nut;
           }
        }
     }
  }
   void cSpalart::dsrhs23( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
                        Real *wq[], Real *res[], Real *lhs[] )
  {
      Int iq;
      Real nuy,utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2,drho,dnut;
      Real omg[3];
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            drho= dq[0][iq];
            dnut=daux[nv0][iq];
            if( utau >=  0 )
           {
               omg[0]= dqdx[0][1][iq]+ dqdx[1][0][iq];
               omg[1]= dqdx[2][0][iq];
               omg[2]= dqdx[2][1][iq];
               s=  omg[0]*omg[0];
               s+= omg[1]*omg[1];
               s+= omg[2]*omg[2];
               s= sqrt( omg[0]*omg[0] );
               chi= nut/nu;
               chi3= chi*chi*chi;
               fv1= chi3/( chi3+cv13 );
               fv2= 1-chi/(1+chi*fv1);
               ky= karm*y;
               ky2=ky*ky;
               s+= fv2*nut/ky2;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               nuy= nut/y;
               nuy*= nuy;
               if( nut > 1.e-12 )
              {
                  res[nv0][iq]+= wq[0][iq]*drho*( cb1*s*nut- (cw1*fw)*nuy )+ 
                                 wq[0][iq]*rho*dnut*( cb1*s- 2*(cw1*fw)*nut/(y*y) );
              }
           }
            else
           {
               res[nv0][iq]= - lhs[nlhs-1][iq]*rho*nut;
           }
        }
     }
  }
   void cSpalart::dsrhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
                        Real *wq[], Real *res[], Real *lhs[] )
  {
      Int iq;
      Real nuy,utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2,drho,dnut;
      Real omg[3];
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            drho= dq[0][iq];
            dnut=daux[nv0][iq];
            if( utau >=  0 )
           {
               omg[0]= dqdx[0][1][iq]- dqdx[1][0][iq];
               omg[1]= dqdx[2][0][iq]- dqdx[0][2][iq];
               omg[2]= dqdx[1][2][iq]- dqdx[2][1][iq];
               s=  omg[0]*omg[0];
               s+= omg[1]*omg[1];
               s+= omg[2]*omg[2];
               s= sqrt( s );
               chi= nut/nu;
               chi3= chi*chi*chi;
               fv1= chi3/( chi3+cv13 );
               fv2= 1-chi/(1+chi*fv1);
               ky= karm*y;
               ky2=ky*ky;
               s+= fv2*nut/ky2;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               nuy= nut/y;
               nuy*= nuy;
               if( nut > 1.e-12 )
              {
                  res[nv0][iq]+= wq[0][iq]*drho*( prodfct*cb1*s*nut- (cw1*fw)*nuy )+ 
                                 wq[0][iq]*rho*dnut*( prodfct*cb1*s- 2*(cw1*fw)*nut/(y*y) );
              }

           }
            else
           {
               res[nv0][iq]= - lhs[nlhs-1][iq]*rho*nut;
           }
        }
     }
  }

   void cSpalart::slhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[] )
  {
      Int iq;
      Real utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2;
      Real omg[3];
      if( iqe > iqs )
     {
         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               slhs22( iqs, iqe, cfl, q, aux, dqdx, dst, wq, lhs );
           }
            else
           {
               if( nvel == 3 )
              {
                  slhs23( iqs, iqe, cfl, q, aux, dqdx, dst, wq, lhs );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               if( nvel == 3 )
              {
                  slhs33( iqs, iqe, cfl, q, aux, dqdx, dst, wq, lhs );
              }
           }
        }
     }
  }
   void cSpalart::slhs22( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[] )
  {
      Int iq;
      Real utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2;
      Real omg[3];
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            if( utau >=  0 )
           {
               omg[0]= dqdx[0][1][iq] + dqdx[1][0][iq];
               s= omg[0]*omg[0];
               s= sqrt( s );
               chi= nut/nu;
               chi3= chi*chi*chi;
               fv1= chi3/( chi3+cv13 );
               fv2= 1-chi/(1+chi*fv1);
               ky= karm*y;
               ky2=ky*ky;
               s+= fv2*nut/ky2;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               lhs[nlhs0-1][iq]+= wq[0][iq]*rho*( cb1*s+ 2*(cw1*fw)*nut/(y*y) );
           }
            else
           {
               lhs[nlhs0-1][iq]= 0;
           }
        }
     }
  }

   void cSpalart::slhs23( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[] )
  {
      Int iq;
      Real utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2;
      Real omg[3];
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            if( utau >=  0 )
           {
               omg[0]= dqdx[0][1][iq]+ dqdx[1][0][iq];
               omg[1]= dqdx[2][0][iq];
               omg[2]= dqdx[2][1][iq];
               s= omg[0]*omg[0];
               s+= omg[1]*omg[1];
               s+= omg[2]*omg[2];
               s= sqrt( s );
               chi= nut/nu;
               chi3= chi*chi*chi;
               fv1= chi3/( chi3+cv13 );
               fv2= 1-chi/(1+chi*fv1);
               ky= karm*y;
               ky2=ky*ky;
               s+= fv2*nut/ky2;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               lhs[nlhs0-1][iq]+= wq[0][iq]*rho*( cb1*s+ 2*(cw1*fw)*nut/(y*y) );
           }
            else
           {
               lhs[nlhs0-1][iq]= 0;
           }
        }
     }
  }
   void cSpalart::slhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[] )
  {
      Int iq;
      Real utau,rho,nut,nu,mu,mut,mu0,y,s,g,ky,ky2,chi,chi3,g6,fw,fv1,fv2;
      Real omg[3];
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            nut= q[nv0][iq];
            y= dst[0][iq];
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut= mu-mu0;
            nu= mu0/rho;
            if( utau >=  0 )
           {
               omg[0]= dqdx[0][1][iq] - dqdx[1][0][iq];
               omg[1]= dqdx[2][0][iq] - dqdx[0][2][iq];
               omg[2]= dqdx[1][2][iq] - dqdx[2][1][iq];
               s= omg[0]*omg[0];
               s+= omg[1]*omg[1];
               s+= omg[2]*omg[2];
               s= sqrt( s );
               chi= nut/nu;
               chi3= chi*chi*chi;
               fv1= chi3/( chi3+cv13 );
               fv2= 1-chi/(1+chi*fv1);
               ky= karm*y;
               ky2=ky*ky;
               s+= fv2*nut/ky2;
               g= nut/( s*ky2 );
               g= g*( 1+ cw2*( g*g*g*g*g- 1 ) );
               g6= g*g*g;
               g6*= g6;
               fw= ( 1+cw36 )/( g6+cw36 );
               fw= g*pow( fw,1./6. );
              
               lhs[nlhs0-1][iq]+= wq[0][iq]*rho*( prodfct*cb1*s+ 2*(cw1*fw)*nut/(y*y) );
           }
            else
           {
               lhs[nlhs0-1][iq]= 0;
           }
        }
     }
  }

   void cSpalart::mflx( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                        Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               mflx22( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr, xc,wc,wxdc, auxc );
           }
            else
           {
               if( nvel == 3 )
              {
                  mflx23( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr, xc,wc,wxdc, auxc );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               if( nvel == 3 )
              {
                  mflx33( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr, xc,wc,wxdc, auxc );
              }
           }
        }
            
     }
  }
   void cSpalart::mflx22( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            
            dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];

            dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];

            dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];

            dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

// reconstruct gradient

            dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];

// stress tensor

            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            taun[nv0]= -(mu0+ (1+cb2)*rho*q[nv0])*dqn[nv0]/sigma;

            rhsr[nv0][iqr]+= taun[nv0]*wc[2][ic];
            rhsl[nv0][iql]-= taun[nv0]*wc[2][ic];

// "anti-diffusion"

            rhsr[nv0][iqr]+= cb2/sigma*auxr[0][iqr]*qr[nv0][iqr]*dqn[nv0]*wc[2][ic];
            rhsl[nv0][iql]-= cb2/sigma*auxl[0][iql]*ql[nv0][iql]*dqn[nv0]*wc[2][ic];

        }
     }
  }
   void cSpalart::mflx23( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                        Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];

            dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];

            dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];

// tangential gradients

            dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            taun[nv0]= -(mu0+ (1+cb2)*rho*q[nv0])*dqn[nv0]/sigma;

            rhsr[nv0][iqr]+= taun[nv0]*wc[2][ic];

            rhsl[nv0][iql]-= taun[nv0]*wc[2][ic];

            rhsr[nv0][iqr]+= cb2/sigma*auxr[0][iqr]*qr[nv0][iqr]*dqn[nv0]*wc[2][ic];
            rhsl[nv0][iql]-= cb2/sigma*auxl[0][iql]*ql[nv0][iql]*dqn[nv0]*wc[2][ic];

        }
     }
  }

   void cSpalart::mflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                        Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];
            dqnl[nv0]+= dqdxl[nv0][2][iql]*wc[2][ic];

            dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];
            dqnr[nv0]+= dqdxr[nv0][2][iqr]*wc[2][ic];

            dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];

// tangential gradients

            dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][2][iql]- wc[2][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][2][iqr]- wc[2][ic]*dqnr[nv0];
            dqt[nv0][2]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];
            dqdx[nv0][2]= dqn[nv0]*wc[2][ic]+ dqt[nv0][2];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

// diffusion 
            taun[nv0]= -(mu0+ (1+cb2)*rho*q[nv0])*dqn[nv0]/sigma;
// accumulate
            rhsr[nv0][iqr]+= taun[nv0]*wc[3][ic];

            rhsl[nv0][iql]-= taun[nv0]*wc[3][ic];

// "anti-diffusion"
            rhsr[nv0][iqr]+= cb2/sigma*auxr[0][iqr]*qr[nv0][iqr]*dqn[nv0]*wc[3][ic];
            rhsl[nv0][iql]-= cb2/sigma*auxl[0][iql]*ql[nv0][iql]*dqn[nv0]*wc[3][ic];

        }
     }
  }

   void cSpalart::dmflx( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa;

      if( ice > ics )
     { 
         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               dmflx22( ics,ice, icql,xl,ql,auxl,dql,dauxl,resl, icqr,xr,qr,auxr,dqr,dauxr,resr, xc,wc,wxdc,auxc );
           }
            else
           {
               if( nvel == 3 )
              {
                  dmflx23( ics,ice, icql,xl,ql,auxl,dql,dauxl,resl, icqr,xr,qr,auxr,dqr,dauxr,resr, xc,wc,wxdc,auxc );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               if( nvel == 3 )
              {
                  dmflx33( ics,ice, icql,xl,ql,auxl,dql,dauxl,resl, icqr,xr,qr,auxr,dqr,dauxr,resr, xc,wc,wxdc,auxc );
              }
           }
        }
     }
  }

   void cSpalart::dmflx22( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div,ddiv;

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            ddqn[nv0]= ( dauxr[nv0][iqr]-dauxl[nv0][iql] )/w;
            dqn[nv0]=  ( qr[nv0][iqr]-ql[nv0][iql] )/w;

            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];
            dq[nv0]= wl*dauxl[nv0][iql]+ wr*dauxr[nv0][iqr];

            dqdx[nv0][0]= dqn[nv0]*wc[0][ic];
            ddqdx[nv0][0]= ddqn[nv0]*wc[0][ic];

            dqdx[nv0][1]= dqn[nv0]*wc[1][ic];
            ddqdx[nv0][1]= ddqn[nv0]*wc[1][ic];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            dtaun[nv0]= -(mu0+ (1+cb2)*rho*q[nv0])*ddqn[nv0]/sigma;
// accumulate
            resr[nv0][iqr]+= dtaun[nv0]*wc[2][ic];

            resl[nv0][iql]-= dtaun[nv0]*wc[2][ic];

            resr[nv0][iqr]+= cb2/sigma*auxr[0][iqr]*qr[nv0][iqr]*ddqn[nv0]*wc[2][ic];
            resl[nv0][iql]-= cb2/sigma*auxl[0][iql]*ql[nv0][iql]*ddqn[nv0]*wc[2][ic];
        }
     }
  }
   void cSpalart::dmflx23( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div,ddiv;

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            ddqn[nv0]= ( dauxr[nv0][iqr]-dauxl[nv0][iql] )/w;
            dqn[nv0]=  ( qr[nv0][iqr]-ql[nv0][iql] )/w;

            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];
            dq[nv0]= wl*dauxl[nv0][iql]+ wr*dauxr[nv0][iqr];

            dqdx[nv0][0]= dqn[nv0]*wc[0][ic];
            ddqdx[nv0][0]= ddqn[nv0]*wc[0][ic];

            dqdx[nv0][1]= dqn[nv0]*wc[1][ic];
            ddqdx[nv0][1]= ddqn[nv0]*wc[1][ic];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            dtaun[nv0]= -(mu0+ (1+cb2)*rho*q[nv0])*ddqn[nv0]/sigma;

            resr[nv0][iqr]+= dtaun[nv0]*wc[2][ic];

            resl[nv0][iql]-= dtaun[nv0]*wc[2][ic];

            resr[nv0][iqr]+= cb2/sigma*auxr[0][iqr]*qr[nv0][iqr]*ddqn[nv0]*wc[2][ic];
            resl[nv0][iql]-= cb2/sigma*auxl[0][iql]*ql[nv0][iql]*ddqn[nv0]*wc[2][ic];
        }
     }
  }
   void cSpalart::dmflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div,ddiv;

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            ddqn[nv0]= ( dauxr[nv0][iqr]-dauxl[nv0][iql] )/w;
            dqn[nv0]=  ( qr[nv0][iqr]-ql[nv0][iql] )/w;

            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];
            dq[nv0]= wl*dauxl[nv0][iql]+ wr*dauxr[nv0][iqr];

            dqdx[nv0][0]= dqn[nv0]*wc[0][ic];
            ddqdx[nv0][0]= ddqn[nv0]*wc[0][ic];

            dqdx[nv0][1]= dqn[nv0]*wc[1][ic];
            ddqdx[nv0][1]= ddqn[nv0]*wc[1][ic];

            dqdx[nv0][2]= dqn[nv0]*wc[2][ic];
            ddqdx[nv0][2]= ddqn[nv0]*wc[2][ic];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            dtaun[nv0]= -(mu0+ (1+cb2)*rho*q[nv0])*ddqn[nv0]/sigma;

// accumulate
            resr[nv0][iqr]+= dtaun[nv0]*wc[3][ic];

            resl[nv0][iql]-= dtaun[nv0]*wc[3][ic];

            resr[nv0][iqr]+= cb2/sigma*auxr[0][iqr]*qr[nv0][iqr]*ddqn[nv0]*wc[3][ic];
            resl[nv0][iql]-= cb2/sigma*auxl[0][iql]*ql[nv0][iql]*ddqn[nv0]*wc[3][ic];
        }
     }
  }

   void cSpalart::dvar( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dU[], Real *dq[] )
  {
      Int iq; 
      Real ro,dro;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            ro= aux[0][iq];
            dro= dU[0][iq];
            dq[nv0][iq]=   ( dU[nv0][iq]-   q[nv0][iq]*dro   )/ro;
            dq[nv0][iq]=   fmax( dq[nv0][iq],-0.9*q[nv0][iq] );
        }
     } 
  }

   void cSpalart::mwflxisoth( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                          Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                     Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      if( ice > ics )
     {

         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               mwflxisoth22( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
           }
            else
           {
               if( nvel == 3 )
              {
                  mwflxisoth23( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               mwflxisoth33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
           }
        }
     }
  }

   void cSpalart::mwflxisoth22( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                       Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );

// tangential velocity
            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            utau= auxl[0][iql];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               auxr[naux-3][iqr]=-1;
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[2][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[2][ic];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[2][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;
        }
     }
  }
   void cSpalart::mwflxisoth23( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                    Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

// distance
            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );

// tangential velocity
            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];
            ut[2]= qr[2][iqr]- ql[2][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];
            utg= sqrt(utg);
            utg=fmax(utg,umin);

            utau= auxl[0][iql];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               auxr[naux-3][iqr]=-1;
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[2][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[2][ic];
            f[3]= -rho*ut[2]*fabs(ut[2])*wc[2][ic];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[2][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;

        }
     }
  }
   void cSpalart::mwflxisoth33( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                    Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

// distance
            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );

// tangential velocity
            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];
            ut[2]= qr[2][iqr]- ql[2][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];
            un+= ut[2]*wc[2][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;
            ut[2]-= wc[2][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];
            utg= sqrt(utg);
            utg=fmax(utg,umin);

            utau= auxl[0][iql];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               auxr[naux-3][iqr]=-1;
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[3][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;
        }
     }
  }
