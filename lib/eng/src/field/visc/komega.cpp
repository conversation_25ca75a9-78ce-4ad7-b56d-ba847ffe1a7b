using namespace std;

#  include <field/visc.h>

   cKomega::cKomega()
  {
      stbeta=0.09;
      beta=0.075;
      stsigma=0.5;
      sigma=0.5;
      karm=0.41;
      b=5.1;
      alpha= 5./9.;
  };

   void cKomega::setvrs( Int Nx, Int Nvel, Int *Nv, Int *Naux, Int *Nauxf, Int *Nlhs )
  {
      nx= Nx;
      nvel= Nvel;

     
      nv0=*(Nv);
    (*Nv)+=2;
      nv= *Nv;

      nauxf0=*Nauxf;
    (*Nauxf)+=2;
      nauxf= *Nauxf;

      nlhs0=*Nlhs;
    (*Nlhs)+=2;
      nlhs= *Nlhs;

      naux0=*Naux; 
    (*Naux)+= 3;
      naux=(*Naux);

      karm=0.41;
      b= 5.1;
  };
   void cKomega::nondim( Int iqs, Int iqe, Real *q[], Int *idone )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
         if( idone[nv0] != 1 )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[nv0][iq]=4;
           }
        }
         if( idone[nv0+1] != 1 )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[nv0+1][iq]= 44444.;
           }
        }
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[nv0][iq]/= 10000.;
            q[nv0+1][iq]/= 100.;
        }
     }
  }

   void cKomega::nondim( Int iqs, Int iqe, cAu3xView<Real>& q, Int *idone )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)

        Int nq;
        Real *sq;
           
        nq = q.get_dim1();
        sq = q.get_data();

//         if( idone[nv0] != 1 )
//        {
//
//           #pragma acc parallel loop \
//            present (sq[0:nv*nq],idone[0:nv],this)\
//            default(none)
//            for( iq=iqs;iq<iqe;iq++ )
//           {
//               //q(nv0,iq)=4;
//               sq[ADDR(nv0,iq,nq)]=4;
//           }
//        }
//         if( idone[nv0+1] != 1 )
//        {
//           #pragma acc parallel loop \
//            present (sq[0:nv*nq],idone[0:nv],this)\
//            default(none)
//            for( iq=iqs;iq<iqe;iq++ )
//           {
//               //q(nv0+1,iq)= 44444.;
//               sq[ADDR(nv0+1,iq,nq)]= 44444.;
//           }
//        }
        #pragma acc parallel loop \
         present (sq[0:nv*nq],idone[0:nv],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //q(nv0,iq)/= 10000.;
            //q(nv0+1,iq)/= 100.;
            sq[ADDR(nv0,iq,nq)]/= 10000.;
            sq[ADDR(nv0+1,iq,nq)]/= 100.;
        }

        #pragma acc exit data copyout(this)
     }
  }


   void cKomega::redim( Int iqs, Int iqe, Real *q[] )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[nv0][iq]*= 10000.;
            q[nv0+1][iq]*= 100.;
        }
     }
  }

   void cKomega::redim( Int iqs, Int iqe, cAu3xView<Real>& q )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
         #pragma acc enter data copyin(this)

         Int nq;
         Real *sq;
            
         nq = q.get_dim1();
         sq = q.get_data();

        #pragma acc parallel loop \
         present (sq[0:nv*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //q(nv0,iq)*= 10000.;
            //q(nv0+1,iq)*= 100.;
            sq[ADDR(nv0,iq,nq)]*= 10000.;
            sq[ADDR(nv0+1,iq,nq)]*= 100.;
        }

         #pragma acc exit data copyout(this)
     }
  }

   void cKomega::redim_cpu( Int iqs, Int iqe, cAu3xView<Real>& q )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
         Int nq;
         Real *sq;
            
         nq = q.get_dim1();
         sq = q.get_data();

         for( iq=iqs;iq<iqe;iq++ )
        {
            //q(nv0,iq)*= 10000.;
            //q(nv0+1,iq)*= 100.;
            sq[ADDR(nv0,iq,nq)]*= 10000.;
            sq[ADDR(nv0+1,iq,nq)]*= 100.;
        }
     }
  }

   void cKomega::maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,omega,k;
    
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            mut= rho*k/omega;
            //mut= fmin( mut,2000.*mu );
            mut= fmin( mut,10000.*mu );
            k= omega*mut/rho;
            q[nv0][iq]= k;
            kappat= cp*mut;   // unit turbulent Prandtl number
            aux[naux-3][iq]= 0.;
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;
        }
     }
  }

   void cKomega::maux( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,omega,k;
    
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sxq[0:nx*nq],sq[0:nv*nq],sdst[0:2*nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //cp= aux[naux0-3][iq];
            //mu= aux[naux0-2][iq];
            cp= saux[ADDR(naux0-3,iq,nq)];
            mu= saux[ADDR(naux0-2,iq,nq)];
            //kappa= aux[naux0-1][iq];
            kappa= saux[ADDR(naux0-1,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            //omega= q[nv0+1][iq];
            k= sq[ADDR(nv0,iq,nq)];
            omega= sq[ADDR(nv0+1,iq,nq)];
            mut= rho*k/omega;
            //mut= fmin( mut,2000.*mu );
            mut= fmin( mut,10000.*mu );
            k= omega*mut/rho;
            //q[nv0][iq]= k;
            sq[ADDR(nv0,iq,nq)]= k;
            kappat= cp*mut;   // unit turbulent Prandtl number
            //aux[naux-3][iq]= 0.;
            //aux[naux-2][iq]= mu+ mut;
            //aux[naux-1][iq]= kappa+ kappat;
            saux[ADDR(naux-3,iq,nq)]= 0.;
            saux[ADDR(naux-2,iq,nq)]= mu+ mut;
            saux[ADDR(naux-1,iq,nq)]= kappa+ kappat;
        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::maux( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux )
  {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,omega,k;
   
      Int nq;
      Real *sxq, *sq, *sdst, *sdqdx, *saux;

      nq = q.get_dim1();
      sxq   = xq.get_data();
      sq    = q.get_data();
      sdst  = dst.get_data();
      sdqdx = dqdx.get_data();
      saux  = aux.get_data();
 
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sxq[0:nx*nq],sq[0:nv*nq],sdst[0:2*nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //cp= aux[naux0-3][iq];
            //mu= aux[naux0-2][iq];
            cp= saux[ADDR(naux0-3,iq,nq)];
            mu= saux[ADDR(naux0-2,iq,nq)];
            //kappa= aux[naux0-1][iq];
            kappa= saux[ADDR(naux0-1,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            //omega= q[nv0+1][iq];
            k= sq[ADDR(nv0,iq,nq)];
            omega= sq[ADDR(nv0+1,iq,nq)];
            mut= rho*k/omega;
            //mut= fmin( mut,2000.*mu );
            mut= fmin( mut,10000.*mu );
            k= omega*mut/rho;
            //q[nv0][iq]= k;
            sq[ADDR(nv0,iq,nq)]= k;
            kappat= cp*mut;   // unit turbulent Prandtl number
            //aux[naux-3][iq]= 0.;
            //aux[naux-2][iq]= mu+ mut;
            //aux[naux-1][iq]= kappa+ kappat;
            saux[ADDR(naux-3,iq,nq)]= 0.;
            saux[ADDR(naux-2,iq,nq)]= mu+ mut;
            saux[ADDR(naux-1,iq,nq)]= kappa+ kappat;
        }
        #pragma acc exit data delete(this)
     }
  }

   //void cKomega::mwflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
   //                                      Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
   //                                                 Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cKomega::mwflx( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,  
                                         Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr, 
                                                    Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr ) 
  {
      if( ice > ics )
     {
         //mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
         mwflx33( ics,ice, icql,sxl,sql,sauxl,srhsl,  icqr,sxr,sqr,sauxr,srhsr, swc,swxdc,sauxc,nql,nqr );
     }
  }

   void cKomega::mwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                         cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                         cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( ice > ics )
     {
         //mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
         mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
     }
  }

   //void cKomega::mwflx33( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
   //                                        Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
   //                                                   Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cKomega::mwflx33( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,  
                                           Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr, 
                                                      Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;
      Int             nql,nqr;

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          firstprivate(umin) \
          private(f,ut) \
          present(            sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
                  icqr[0:nfc],sxr[0:nx*nq], sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
                  swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
          default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic];

            //mu= auxr[naux0-2][iqr];
            //mut= auxr[naux-2][iqr];
            //rho= auxr[0][iqr];
            mu= sauxr[ADDR(naux0-2,iqr,nqr)];
            mut= sauxr[ADDR(naux-2,iqr,nqr)];
            rho= sauxr[ADDR(0,iqr,nqr)];

            //d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            //d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            //d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
            d=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxl[ADDR(0,iql,nql)] );
            d+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxl[ADDR(1,iql,nql)] );
            d+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxl[ADDR(2,iql,nql)] );

// tangential velocity
            //ut[0]= qr[0][iqr]- ql[0][iql];
            //ut[1]= qr[1][iqr]- ql[1][iql];
            //ut[2]= qr[2][iqr]- ql[2][iql];
            ut[0]= sqr[ADDR(0,iqr,nqr)]- sql[ADDR(0,iql,nql)];
            ut[1]= sqr[ADDR(1,iqr,nqr)]- sql[ADDR(1,iql,nql)];
            ut[2]= sqr[ADDR(2,iqr,nqr)]- sql[ADDR(2,iql,nql)];

            //un=  ut[0]*wc[0][ic];
            //un+= ut[1]*wc[1][ic];
            //un+= ut[2]*wc[2][ic];
            un=  ut[0]*swc[ADDR(0,ic,nfc)];
            un+= ut[1]*swc[ADDR(1,ic,nfc)];
            un+= ut[2]*swc[ADDR(2,ic,nfc)];

            //ut[0]-= wc[0][ic]*un;
            //ut[1]-= wc[1][ic]*un;
            //ut[2]-= wc[2][ic]*un;
            ut[0]-= swc[ADDR(0,ic,nfc)]*un;
            ut[1]-= swc[ADDR(1,ic,nfc)]*un;
            ut[2]-= swc[ADDR(2,ic,nfc)]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];
           
            utg= sqrt(utg);
            utg=fmax(utg,umin);
            //utau= auxl[0][iql];
            utau= sauxl[ADDR(0,iql,nql)];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               #pragma acc loop seq
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
               //auxr[naux-3][iqr]= utau;
               sauxr[ADDR(naux-3,iqr,nqr)]= utau;
           }
            else
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
               //auxr[naux-3][iqr]=-utau;
               sauxr[ADDR(naux-3,iqr,nqr)]=-utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;
            //f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            //f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            //f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];
            f[1]= -rho*ut[0]*fabs(ut[0])*swc[ADDR(3,ic,nfc)];
            f[2]= -rho*ut[1]*fabs(ut[1])*swc[ADDR(3,ic,nfc)];
            f[3]= -rho*ut[2]*fabs(ut[2])*swc[ADDR(3,ic,nfc)];
            //f[4]=  f[1]*ql[0][iql];
            //f[4]+= f[2]*ql[1][iql];
            //f[4]+= f[3]*ql[2][iql];
            f[4]=  f[1]*sql[ADDR(0,iql,nql)];
            f[4]+= f[2]*sql[ADDR(1,iql,nql)];
            f[4]+= f[3]*sql[ADDR(2,iql,nql)];

            //rhsl[1][iql]-= f[1];
            //rhsl[2][iql]-= f[2];
            //rhsl[3][iql]-= f[3];
            //rhsl[4][iql]-= f[4];
            #pragma acc atomic
            srhsl[ADDR_(1,iql,nql)]-= f[1];
            #pragma acc atomic
            srhsl[ADDR_(2,iql,nql)]-= f[2];
            #pragma acc atomic
            srhsl[ADDR_(3,iql,nql)]-= f[3];
            #pragma acc atomic
            srhsl[ADDR_(4,iql,nql)]-= f[4];

            //rhsr[1][iqr]+= f[1];
            //rhsr[2][iqr]+= f[2];
            //rhsr[3][iqr]+= f[3];
            //rhsr[4][iqr]+= f[4];
            #pragma acc atomic
            srhsr[ADDR_(1,iqr,nqr)]+= f[1];
            #pragma acc atomic
            srhsr[ADDR_(2,iqr,nqr)]+= f[2];
            #pragma acc atomic
            srhsr[ADDR_(3,iqr,nqr)]+= f[3];
            #pragma acc atomic
            srhsr[ADDR_(4,iqr,nqr)]+= f[4];

            //auxc[nauxf-1][ic]+= mut/(rho*d)*wc[3][ic];
            //auxl[0][iql]= utau;
            //auxl[1][iql]= rho*utau/mu;
            #pragma acc atomic
            sauxc[ADDR_((nauxf-1),ic,nfc)]+= mut/(rho*d)*swc[ADDR_(3,ic,nfc)];

            sauxl[ADDR(0,iql,nql)]= utau;
            sauxl[ADDR(1,iql,nql)]= rho*utau/mu;
        }
         #pragma acc exit data delete(this)
     }
  }

   void cKomega::mwflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                           cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                           cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;
      Int             nql,nqr;

      Int nq, nfc;
      Int *icql;
      Real *sxl, *sql, *sauxl, *srhsl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nq = qr.get_dim1();
      nfc = wc.get_dim1();

      icql  = icql_view.get_data();
      sxl   = xl.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();;

      icqr  = icqr_view.get_data();
      sxr   = xr.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      srhsr = rhsr.get_data();;

      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          firstprivate(umin) \
          private(f,ut) \
          present(            sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
                  icqr[0:nfc],sxr[0:nx*nq], sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
                  swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
          default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic];

            //mu= auxr[naux0-2][iqr];
            //mut= auxr[naux-2][iqr];
            //rho= auxr[0][iqr];
            mu= sauxr[ADDR(naux0-2,iqr,nqr)];
            mut= sauxr[ADDR(naux-2,iqr,nqr)];
            rho= sauxr[ADDR(0,iqr,nqr)];

            //d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            //d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            //d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
            d=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxl[ADDR(0,iql,nql)] );
            d+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxl[ADDR(1,iql,nql)] );
            d+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxl[ADDR(2,iql,nql)] );

// tangential velocity
            //ut[0]= qr[0][iqr]- ql[0][iql];
            //ut[1]= qr[1][iqr]- ql[1][iql];
            //ut[2]= qr[2][iqr]- ql[2][iql];
            ut[0]= sqr[ADDR(0,iqr,nqr)]- sql[ADDR(0,iql,nql)];
            ut[1]= sqr[ADDR(1,iqr,nqr)]- sql[ADDR(1,iql,nql)];
            ut[2]= sqr[ADDR(2,iqr,nqr)]- sql[ADDR(2,iql,nql)];

            //un=  ut[0]*wc[0][ic];
            //un+= ut[1]*wc[1][ic];
            //un+= ut[2]*wc[2][ic];
            un=  ut[0]*swc[ADDR(0,ic,nfc)];
            un+= ut[1]*swc[ADDR(1,ic,nfc)];
            un+= ut[2]*swc[ADDR(2,ic,nfc)];

            //ut[0]-= wc[0][ic]*un;
            //ut[1]-= wc[1][ic]*un;
            //ut[2]-= wc[2][ic]*un;
            ut[0]-= swc[ADDR(0,ic,nfc)]*un;
            ut[1]-= swc[ADDR(1,ic,nfc)]*un;
            ut[2]-= swc[ADDR(2,ic,nfc)]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];
           
            utg= sqrt(utg);
            utg=fmax(utg,umin);
            //utau= auxl[0][iql];
            utau= sauxl[ADDR(0,iql,nql)];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               #pragma acc loop seq
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
               //auxr[naux-3][iqr]= utau;
               sauxr[ADDR(naux-3,iqr,nqr)]= utau;
           }
            else
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
               //auxr[naux-3][iqr]=-utau;
               sauxr[ADDR(naux-3,iqr,nqr)]=-utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;
            //f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            //f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            //f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];
            f[1]= -rho*ut[0]*fabs(ut[0])*swc[ADDR(3,ic,nfc)];
            f[2]= -rho*ut[1]*fabs(ut[1])*swc[ADDR(3,ic,nfc)];
            f[3]= -rho*ut[2]*fabs(ut[2])*swc[ADDR(3,ic,nfc)];
            //f[4]=  f[1]*ql[0][iql];
            //f[4]+= f[2]*ql[1][iql];
            //f[4]+= f[3]*ql[2][iql];
            f[4]=  f[1]*sql[ADDR(0,iql,nql)];
            f[4]+= f[2]*sql[ADDR(1,iql,nql)];
            f[4]+= f[3]*sql[ADDR(2,iql,nql)];

            //rhsl[1][iql]-= f[1];
            //rhsl[2][iql]-= f[2];
            //rhsl[3][iql]-= f[3];
            //rhsl[4][iql]-= f[4];
            #pragma acc atomic
            srhsl[ADDR_(1,iql,nql)]-= f[1];
            #pragma acc atomic
            srhsl[ADDR_(2,iql,nql)]-= f[2];
            #pragma acc atomic
            srhsl[ADDR_(3,iql,nql)]-= f[3];
            #pragma acc atomic
            srhsl[ADDR_(4,iql,nql)]-= f[4];

            //rhsr[1][iqr]+= f[1];
            //rhsr[2][iqr]+= f[2];
            //rhsr[3][iqr]+= f[3];
            //rhsr[4][iqr]+= f[4];
            #pragma acc atomic
            srhsr[ADDR_(1,iqr,nqr)]+= f[1];
            #pragma acc atomic
            srhsr[ADDR_(2,iqr,nqr)]+= f[2];
            #pragma acc atomic
            srhsr[ADDR_(3,iqr,nqr)]+= f[3];
            #pragma acc atomic
            srhsr[ADDR_(4,iqr,nqr)]+= f[4];

            //auxc[nauxf-1][ic]+= mut/(rho*d)*wc[3][ic];
            //auxl[0][iql]= utau;
            //auxl[1][iql]= rho*utau/mu;
            #pragma acc atomic
            sauxc[ADDR_((nauxf-1),ic,nfc)]+= mut/(rho*d)*swc[ADDR_(3,ic,nfc)];

            sauxl[ADDR(0,iql,nql)]= utau;
            sauxl[ADDR(1,iql,nql)]= rho*utau/mu;
        }
         #pragma acc exit data delete(this)
     }
  }
   //void cKomega::vlhs( Int iqs, Int iqe, Real cfl, Real *wq[], Real *lhs[] )
   void cKomega::vlhs( Int iqs, Int iqe, Real cfl, Real *swq, Real *slhs, Int nq )
  {
      Int             ia,ja,iq;
      Real            utau;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(swq[0:(nx+1)*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //lhs[nlhs0-1][iq]+= lhs[nlhs-1][iq]; 
            //lhs[nlhs0][iq]+=   lhs[nlhs-1][iq]; 
            slhs[ADDR(nlhs0-1,iq,nq)]+= slhs[ADDR(nlhs-1,iq,nq)]; 
            slhs[ADDR(nlhs0,iq,nq)]+=   slhs[ADDR(nlhs-1,iq,nq)]; 
        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::vlhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& wq, cAu3xView<Real>& lhs )
  {
      Int             ia,ja,iq;
      Real            utau;

      Int nq;
      Real *swq, *slhs;
    
      nq   = wq.get_dim1();
      swq  = wq.get_data();
      slhs = lhs.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(swq[0:(nx+1)*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //lhs[nlhs0-1][iq]+= lhs[nlhs-1][iq]; 
            //lhs[nlhs0][iq]+=   lhs[nlhs-1][iq]; 
            slhs[ADDR(nlhs0-1,iq,nq)]+= slhs[ADDR(nlhs-1,iq,nq)]; 
            slhs[ADDR(nlhs0,iq,nq)]+=   slhs[ADDR(nlhs-1,iq,nq)]; 
        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::invdg( Int iqs, Int iqe, Real *slhs, Real *sres, Int nq )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present(sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //res[nv0][iq]/=   lhs[nlhs0-1][iq]; 
            //res[nv0+1][iq]/= lhs[nlhs0][iq]; 
            sres[ADDR(nv0,iq,nq)]/=   slhs[ADDR(nlhs0-1,iq,nq)]; 
            sres[ADDR(nv0+1,iq,nq)]/= slhs[ADDR(nlhs0,iq,nq)]; 
        }
        #pragma acc exit data copyout(this)
     }
  }

   void cKomega::invdg( Int iqs, Int iqe, cAu3xView<Real>& lhs, cAu3xView<Real>& res )
  {
      Int iv,iq;

      Int nq;
      Real *slhs; Real *sres;

      slhs = lhs.get_data();
      sres = res.get_data();

      nq = lhs.get_dim1();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present(sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //res[nv0][iq]/=   lhs[nlhs0-1][iq]; 
            //res[nv0+1][iq]/= lhs[nlhs0][iq]; 
            sres[ADDR(nv0,iq,nq)]/=   slhs[ADDR(nlhs0-1,iq,nq)]; 
            sres[ADDR(nv0+1,iq,nq)]/= slhs[ADDR(nlhs0,iq,nq)]; 
        }
        #pragma acc exit data copyout(this)
     }
  }

   void cKomega::ilhs( Int ics, Int ice, Int *icql, Real *ql[], Real *auxl[], Real *lhsl[], 
                                      Int *icqr, Real *qr[], Real *auxr[], Real *lhsr[], 
                                                 Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Int             ia,ja,iql,iqr,ic;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        { 

            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };
       
            lhsl[nlhs0-1][iql]+= auxc[nauxf-1][ic];
            lhsl[nlhs0][iql]+=   auxc[nauxf-1][ic];
            lhsr[nlhs0-1][iqr]+= auxc[nauxf-1][ic];
            lhsr[nlhs0][iqr]+=   auxc[nauxf-1][ic];
        }
     }
  }

   void cKomega::ilhs( Int ics, Int ice, Int *sicql, Real *sql, Real *sauxl, Real *slhsl, 
                                         Int *sicqr, Real *sqr, Real *sauxr, Real *slhsr, 
                                         Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Int             nql,nqr,iql,iqr,ic;

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(             sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
                 sicqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        { 

            iql= ic;
            iqr= sicqr[ADDR(0,ic,nfc)];
       
            //lhsl[nlhs0-1][iql]+= auxc[nauxf-1][ic];
            //lhsl[nlhs0][iql]+=   auxc[nauxf-1][ic];
            //lhsr[nlhs0-1][iqr]+= auxc[nauxf-1][ic];
            //lhsr[nlhs0][iqr]+=   auxc[nauxf-1][ic];
            #pragma acc atomic
            slhsl[ADDR_((nlhs0-1),iql,nql)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhsl[ADDR_(nlhs0,    iql,nql)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhsr[ADDR_((nlhs0-1),iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhsr[ADDR_(nlhs0,    iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::ilhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                         cAu3xView<Int>& icqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                         cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             nql,nqr,iql,iqr,ic;

      Int nfc, nq;
      Int *sicql; Real *sql; Real *sauxl; Real *slhsl;
      Int *sicqr; Real *sqr; Real *sauxr; Real *slhsr;
      Real *swc; Real *swxdc; Real *sauxc;

      sicql = icql.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      slhsl = lhsl.get_data();;
  
      sicqr = icqr.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      slhsr = lhsr.get_data();;

      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(             sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
                 sicqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        { 

            iql= ic;
            iqr= sicqr[ADDR(0,ic,nfc)];
       
            //lhsl[nlhs0-1][iql]+= auxc[nauxf-1][ic];
            //lhsl[nlhs0][iql]+=   auxc[nauxf-1][ic];
            //lhsr[nlhs0-1][iqr]+= auxc[nauxf-1][ic];
            //lhsr[nlhs0][iqr]+=   auxc[nauxf-1][ic];
            #pragma acc atomic
            slhsl[ADDR_((nlhs0-1),iql,nql)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhsl[ADDR_(nlhs0,    iql,nql)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhsr[ADDR_((nlhs0-1),iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhsr[ADDR_(nlhs0,    iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::ilhs( Int ics, Int ice, Int *sicq, Real *sq, Real *saux, Real *slhs, 
                       Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Int             iql,iqr,ic;

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],slhs[0:nlhs*nq], \
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this )\
         default(none)
         for( ic=ics;ic<ice;ic++ )
        { 

            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];
       
            //lhsl[nlhs0-1][iql]+= auxc[nauxf-1][ic];
            //lhsl[nlhs0][iql]+=   auxc[nauxf-1][ic];
            //lhsr[nlhs0-1][iqr]+= auxc[nauxf-1][ic];
            //lhsr[nlhs0][iqr]+=   auxc[nauxf-1][ic];
            #pragma acc atomic
            slhs[ADDR_((nlhs0-1),iql,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhs[ADDR_(nlhs0,    iql,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhs[ADDR_((nlhs0-1),iqr,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhs[ADDR_(nlhs0,    iqr,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::ilhs( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& lhs,
                       cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             iql,iqr,ic;

      Int nq, nfc;
      Int *sicq; Real *sq; Real *saux; Real *slhs;
      Real *swc; Real *swxdc; Real *sauxc;
    
      sicq  = icq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      slhs  = lhs.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();
  
      nq = q.get_dim1();
      nfc = icq.get_dim1();

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],slhs[0:nlhs*nq], \
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this )\
         default(none)
         for( ic=ics;ic<ice;ic++ )
        { 

            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];
       
            //lhsl[nlhs0-1][iql]+= auxc[nauxf-1][ic];
            //lhsl[nlhs0][iql]+=   auxc[nauxf-1][ic];
            //lhsr[nlhs0-1][iqr]+= auxc[nauxf-1][ic];
            //lhsr[nlhs0][iqr]+=   auxc[nauxf-1][ic];
            #pragma acc atomic
            slhs[ADDR_((nlhs0-1),iql,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhs[ADDR_(nlhs0,    iql,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhs[ADDR_((nlhs0-1),iqr,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhs[ADDR_(nlhs0,    iqr,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
        }
        #pragma acc exit data delete(this)
     }
  }

   //void cKomega::srhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Int *idst[], Int *igdst[], Real *wq[], Real *rhs[], Real *lhs[] )
   void cKomega::srhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Int *sidst, Int *sigdst, Real *swq, Real *srhs, Real *slhs, Int nq )
  {
      if( iqe > iqs )
     {
         //srhs33( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
         srhs33( iqs,iqe, cfl, sq,saux,sdqdx,sdst,swq,srhs,slhs,nq );
     }
  }

   void cKomega::srhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& rhs,
                       cAu3xView<Real>& lhs )
  {
      if( iqe > iqs )
     {
         //srhs33( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
         srhs33( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
     }
  }

   //void cKomega::srhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
   void cKomega::srhs33( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *srhs, Real *slhs, Int nq )
  {
      Int iq,iv,ix;
      Real utau,rho,k,omega,y,sqrtb,mu,mu0,s,div;
      Real tau[3][3];
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(tau) \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
                 sdst[0:2*nq],swq[0:(nx+1)*nq],srhs[0:nv*nq],slhs[0:nlhs*nq],this )\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu0= aux[naux0-2][iq];
            //mu= aux[naux-2][iq];
            mu0= saux[ADDR(naux0-2,iq,nq)];
            mu= saux[ADDR(naux-2,iq,nq)];
            if( utau == 0 )
           {
// stress tensor

               //tau[0][0]= -mu*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               //tau[1][0]= -mu*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               //tau[2][0]= -mu*( dqdx[0][2][iq]+ dqdx[2][0][iq] ); 
               tau[0][0]= -mu*( sdqdx[ADDR(0,0,iq,nq)]+ sdqdx[ADDR(0,0,iq,nq)] ); 
               tau[1][0]= -mu*( sdqdx[ADDR(0,1,iq,nq)]+ sdqdx[ADDR(1,0,iq,nq)] ); 
               tau[2][0]= -mu*( sdqdx[ADDR(0,2,iq,nq)]+ sdqdx[ADDR(2,0,iq,nq)] ); 
               tau[0][1]=  tau[1][0];
               //tau[1][1]= -mu*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
               //tau[2][1]= -mu*( dqdx[1][2][iq]+ dqdx[2][1][iq] ); 
               tau[1][1]= -mu*( sdqdx[ADDR(1,1,iq,nq)]+ sdqdx[ADDR(1,1,iq,nq)] ); 
               tau[2][1]= -mu*( sdqdx[ADDR(1,2,iq,nq)]+ sdqdx[ADDR(2,1,iq,nq)] ); 
               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               //tau[2][2]= -mu*( dqdx[2][2][iq]+ dqdx[2][2][iq] ); 
               tau[2][2]= -mu*( sdqdx[ADDR(2,2,iq,nq)]+ sdqdx[ADDR(2,2,iq,nq)] ); 

               //div=  2./3.*mu*dqdx[0][0][iq];
               //div+= 2./3.*mu*dqdx[1][1][iq];
               //div+= 2./3.*mu*dqdx[2][2][iq];
               div=  2./3.*mu*sdqdx[ADDR(0,0,iq,nq)];
               div+= 2./3.*mu*sdqdx[ADDR(1,1,iq,nq)];
               div+= 2./3.*mu*sdqdx[ADDR(2,2,iq,nq)];

               tau[0][0]+= div;
               tau[1][1]+= div;
               tau[2][2]+= div;

               //s=  tau[0][0]*dqdx[0][0][iq];
               //s+= tau[1][0]*dqdx[1][0][iq];
               //s+= tau[2][0]*dqdx[2][0][iq];
               //s+= tau[0][1]*dqdx[0][1][iq];
               //s+= tau[1][1]*dqdx[1][1][iq];
               //s+= tau[2][1]*dqdx[2][1][iq];
               //s+= tau[0][2]*dqdx[0][2][iq];
               //s+= tau[1][2]*dqdx[1][2][iq];
               //s+= tau[2][2]*dqdx[2][2][iq];
               s=  tau[0][0]*sdqdx[ADDR(0,0,iq,nq)];
               s+= tau[1][0]*sdqdx[ADDR(1,0,iq,nq)];
               s+= tau[2][0]*sdqdx[ADDR(2,0,iq,nq)];
               s+= tau[0][1]*sdqdx[ADDR(0,1,iq,nq)];
               s+= tau[1][1]*sdqdx[ADDR(1,1,iq,nq)];
               s+= tau[2][1]*sdqdx[ADDR(2,1,iq,nq)];
               s+= tau[0][2]*sdqdx[ADDR(0,2,iq,nq)];
               s+= tau[1][2]*sdqdx[ADDR(1,2,iq,nq)];
               s+= tau[2][2]*sdqdx[ADDR(2,2,iq,nq)];

               //rhs[nv0][iq]-= wq[0][iq]*( stbeta*rho*k*omega+ s );
               //rhs[nv0+1][iq]-= wq[0][iq]*( beta*rho*fabs(omega)*omega+ s*alpha/fmax(0.01*mu0/rho,k/omega ) );
               srhs[ADDR(nv0,iq,nq)]-= swq[ADDR(0,iq,nq)]*( stbeta*rho*k*omega+ s );
               srhs[ADDR(nv0+1,iq,nq)]-= swq[ADDR(0,iq,nq)]*( beta*rho*fabs(omega)*omega+ s*alpha/fmax(0.01*mu0/rho,k/omega ) );
           }
            else
           {
               if( utau > 0 )
              {
                  //rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  //rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*rhs[0][iq];
                  srhs[ADDR(nv0,iq,nq)]= slhs[ADDR(nlhs-1,iq,nq)]* rho*(utau*utau/sqrtb- k)+ k*srhs[ADDR(0,iq,nq)];
                  srhs[ADDR(nv0+1,iq,nq)]=  slhs[ADDR(nlhs-1,iq,nq)]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*srhs[ADDR(0,iq,nq)];
              }
               else
              {
                  //rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  srhs[ADDR(nv0,iq,nq)]= - slhs[ADDR(nlhs-1,iq,nq)]*rho*k;
//                rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*(mu/(beta*y*y)-rho*omega);
              }
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::srhs33( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& rhs, cAu3xView<Real>& lhs )
  {
      Int iq,iv,ix;
      Real utau,rho,k,omega,y,sqrtb,mu,mu0,s,div;
      Real tau[3][3];

      Int nq;
      Real *sq; Real *saux; Real *sdqdx; Real *sdst; Real *swq; Real* srhs; Real *slhs;

      nq = q.get_dim1();

      sq    = q.get_data();
      saux  = aux.get_data(); 
      sdqdx = dqdx.get_data();
      sdst  = dst.get_data();
      swq   = wq.get_data();
      srhs  = rhs.get_data();
      slhs  = lhs.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(tau) \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
                 sdst[0:2*nq],swq[0:(nx+1)*nq],srhs[0:nv*nq],slhs[0:nlhs*nq],this )\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu0= aux[naux0-2][iq];
            //mu= aux[naux-2][iq];
            mu0= saux[ADDR(naux0-2,iq,nq)];
            mu= saux[ADDR(naux-2,iq,nq)];
            if( utau == 0 )
           {
// stress tensor

               //tau[0][0]= -mu*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               //tau[1][0]= -mu*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               //tau[2][0]= -mu*( dqdx[0][2][iq]+ dqdx[2][0][iq] ); 
               tau[0][0]= -mu*( sdqdx[ADDR(0,0,iq,nq)]+ sdqdx[ADDR(0,0,iq,nq)] ); 
               tau[1][0]= -mu*( sdqdx[ADDR(0,1,iq,nq)]+ sdqdx[ADDR(1,0,iq,nq)] ); 
               tau[2][0]= -mu*( sdqdx[ADDR(0,2,iq,nq)]+ sdqdx[ADDR(2,0,iq,nq)] ); 
               tau[0][1]=  tau[1][0];
               //tau[1][1]= -mu*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
               //tau[2][1]= -mu*( dqdx[1][2][iq]+ dqdx[2][1][iq] ); 
               tau[1][1]= -mu*( sdqdx[ADDR(1,1,iq,nq)]+ sdqdx[ADDR(1,1,iq,nq)] ); 
               tau[2][1]= -mu*( sdqdx[ADDR(1,2,iq,nq)]+ sdqdx[ADDR(2,1,iq,nq)] ); 
               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               //tau[2][2]= -mu*( dqdx[2][2][iq]+ dqdx[2][2][iq] ); 
               tau[2][2]= -mu*( sdqdx[ADDR(2,2,iq,nq)]+ sdqdx[ADDR(2,2,iq,nq)] ); 

               //div=  2./3.*mu*dqdx[0][0][iq];
               //div+= 2./3.*mu*dqdx[1][1][iq];
               //div+= 2./3.*mu*dqdx[2][2][iq];
               div=  2./3.*mu*sdqdx[ADDR(0,0,iq,nq)];
               div+= 2./3.*mu*sdqdx[ADDR(1,1,iq,nq)];
               div+= 2./3.*mu*sdqdx[ADDR(2,2,iq,nq)];

               tau[0][0]+= div;
               tau[1][1]+= div;
               tau[2][2]+= div;

               //s=  tau[0][0]*dqdx[0][0][iq];
               //s+= tau[1][0]*dqdx[1][0][iq];
               //s+= tau[2][0]*dqdx[2][0][iq];
               //s+= tau[0][1]*dqdx[0][1][iq];
               //s+= tau[1][1]*dqdx[1][1][iq];
               //s+= tau[2][1]*dqdx[2][1][iq];
               //s+= tau[0][2]*dqdx[0][2][iq];
               //s+= tau[1][2]*dqdx[1][2][iq];
               //s+= tau[2][2]*dqdx[2][2][iq];
               s=  tau[0][0]*sdqdx[ADDR(0,0,iq,nq)];
               s+= tau[1][0]*sdqdx[ADDR(1,0,iq,nq)];
               s+= tau[2][0]*sdqdx[ADDR(2,0,iq,nq)];
               s+= tau[0][1]*sdqdx[ADDR(0,1,iq,nq)];
               s+= tau[1][1]*sdqdx[ADDR(1,1,iq,nq)];
               s+= tau[2][1]*sdqdx[ADDR(2,1,iq,nq)];
               s+= tau[0][2]*sdqdx[ADDR(0,2,iq,nq)];
               s+= tau[1][2]*sdqdx[ADDR(1,2,iq,nq)];
               s+= tau[2][2]*sdqdx[ADDR(2,2,iq,nq)];

               //rhs[nv0][iq]-= wq[0][iq]*( stbeta*rho*k*omega+ s );
               //rhs[nv0+1][iq]-= wq[0][iq]*( beta*rho*fabs(omega)*omega+ s*alpha/fmax(0.01*mu0/rho,k/omega ) );
               srhs[ADDR(nv0,iq,nq)]-= swq[ADDR(0,iq,nq)]*( stbeta*rho*k*omega+ s );
               srhs[ADDR(nv0+1,iq,nq)]-= swq[ADDR(0,iq,nq)]*( beta*rho*fabs(omega)*omega+ s*alpha/fmax(0.01*mu0/rho,k/omega ) );
           }
            else
           {
               if( utau > 0 )
              {
                  //rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  //rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*rhs[0][iq];
                  srhs[ADDR(nv0,iq,nq)]= slhs[ADDR(nlhs-1,iq,nq)]* rho*(utau*utau/sqrtb- k)+ k*srhs[ADDR(0,iq,nq)];
                  srhs[ADDR(nv0+1,iq,nq)]=  slhs[ADDR(nlhs-1,iq,nq)]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*srhs[ADDR(0,iq,nq)];
              }
               else
              {
                  //rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  srhs[ADDR(nv0,iq,nq)]= - slhs[ADDR(nlhs-1,iq,nq)]*rho*k;
//                rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*(mu/(beta*y*y)-rho*omega);
              }
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   //void cKomega::dsrhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
   //                     Real *wq[], Real *res[], Real *lhs[] )
   void cKomega::dsrhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sdqdx, Real *sdst, 
                        Real *swq, Real *sres, Real *slhs, Int nq )
  {
      Int iq;
      Real utau,rho,k,omega,y,sqrtb,mu,drho;
      Real dk,domega;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //drho= dq[0][iq];
            drho= sdq[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //dk= daux[nv0][iq];
            dk= sdaux[ADDR(nv0,iq,nq)];
            //domega= daux[nv0+1][iq];
            domega= sdaux[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu= aux[naux0-2][iq];
            mu= saux[ADDR(naux0-2,iq,nq)];
            if( utau == 0 )
           {
               //res[nv0][iq]-= wq[0][iq]*stbeta*(rho*omega*dk+ drho*omega*k+ rho*domega*k );
               //res[nv0+1][iq]-= wq[0][iq]*beta*( 2*rho*omega*domega+ omega*omega*drho );
               sres[ADDR(nv0,iq,nq)]-= swq[ADDR(0,iq,nq)]*stbeta*(rho*omega*dk+ drho*omega*k+ rho*domega*k );
               sres[ADDR(nv0+1,iq,nq)]-= swq[ADDR(0,iq,nq)]*beta*( 2*rho*omega*domega+ omega*omega*drho );
           }
            else
           {
               if( utau > 0 )
              {
                  //res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  //res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*domega;
                  sres[ADDR(nv0,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*dk;
                  sres[ADDR(nv0+1,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*domega;
              }
               else
              {
                  //res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  sres[ADDR(nv0,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*dk;
//                res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*domega; //this line was commented out originally
              }
           }
        }
        #pragma acc exit data copyout(this)
     }
  }

   void cKomega::dsrhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                        cAu3xView<Real>& wq, cAu3xView<Real>& res, cAu3xView<Real>& lhs )
  {
      Int iq;
      Real utau,rho,k,omega,y,sqrtb,mu,drho;
      Real dk,domega;

      Int nq;
      Real *sq, *saux, *sdq, *sdaux, *sdqdx, *sdst, *swq, *sres, *slhs;

      nq    = q.get_dim1();
      sq    = q.get_data();
      saux  = aux.get_data();
      sdq   = dq.get_data();
      sdaux = daux.get_data();
      sdqdx = dqdx.get_data();
      sdst  = dst.get_data();
      swq   = wq.get_data();
      sres  = res.get_data();
      slhs  = lhs.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //drho= dq[0][iq];
            drho= sdq[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //dk= daux[nv0][iq];
            dk= sdaux[ADDR(nv0,iq,nq)];
            //domega= daux[nv0+1][iq];
            domega= sdaux[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu= aux[naux0-2][iq];
            mu= saux[ADDR(naux0-2,iq,nq)];
            if( utau == 0 )
           {
               //res[nv0][iq]-= wq[0][iq]*stbeta*(rho*omega*dk+ drho*omega*k+ rho*domega*k );
               //res[nv0+1][iq]-= wq[0][iq]*beta*( 2*rho*omega*domega+ omega*omega*drho );
               sres[ADDR(nv0,iq,nq)]-= swq[ADDR(0,iq,nq)]*stbeta*(rho*omega*dk+ drho*omega*k+ rho*domega*k );
               sres[ADDR(nv0+1,iq,nq)]-= swq[ADDR(0,iq,nq)]*beta*( 2*rho*omega*domega+ omega*omega*drho );
           }
            else
           {
               if( utau > 0 )
              {
                  //res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  //res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*domega;
                  sres[ADDR(nv0,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*dk;
                  sres[ADDR(nv0+1,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*domega;
              }
               else
              {
                  //res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  sres[ADDR(nv0,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*dk;
//                res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*domega; //this line was commented out originally
              }
           }
        }
        #pragma acc exit data copyout(this)
     }
  }

   //void cKomega::slhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[] )
   void cKomega::slhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *slhsa, Int nq )
  {
      Int iq;
      Real utau,rho,k,omega,y,sqrtb,mu;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],slhsa[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu= aux[naux0-2][iq];
            mu= saux[ADDR(naux0-2,iq,nq)];
            if( utau == 0 )
           {
               //lhs[nlhs0-1][iq]+= wq[0][iq]*stbeta*rho*omega;
               //lhs[nlhs0][iq]+= 2*wq[0][iq]*beta*rho*omega;
               slhsa[ADDR(nlhs0-1,iq,nq)]+= swq[ADDR(0,iq,nq)]*stbeta*rho*omega;
               slhsa[ADDR(nlhs0,iq,nq)]+= 2*swq[ADDR(0,iq,nq)]*beta*rho*omega;
           }
            else
           {
               //lhs[nlhs0-1][iq]= 0.;
               //lhs[nlhs0][iq]= 0.;
               slhsa[ADDR(nlhs0-1,iq,nq)]= 0.;
               slhsa[ADDR(nlhs0,iq,nq)]= 0.;
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa )
  {
      Int iq;
      Real utau,rho,k,omega,y,sqrtb,mu;

      Int nq;
      Real *sq, *saux, *sdqdx, *sdst, *swq, *slhsa;
    
      nq    = q.get_dim1();

      sq    = q.get_data();
      saux  = aux.get_data();
      sdqdx = dqdx.get_data();
      sdst  = dst.get_data();
      swq   = wq.get_data(); 
      slhsa = lhsa.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],slhsa[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu= aux[naux0-2][iq];
            mu= saux[ADDR(naux0-2,iq,nq)];
            if( utau == 0 )
           {
               //lhs[nlhs0-1][iq]+= wq[0][iq]*stbeta*rho*omega;
               //lhs[nlhs0][iq]+= 2*wq[0][iq]*beta*rho*omega;
               slhsa[ADDR(nlhs0-1,iq,nq)]+= swq[ADDR(0,iq,nq)]*stbeta*rho*omega;
               slhsa[ADDR(nlhs0,iq,nq)]+= 2*swq[ADDR(0,iq,nq)]*beta*rho*omega;
           }
            else
           {
               //lhs[nlhs0-1][iq]= 0.;
               //lhs[nlhs0][iq]= 0.;
               slhsa[ADDR(nlhs0-1,iq,nq)]= 0.;
               slhsa[ADDR(nlhs0,iq,nq)]= 0.;
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   //void cKomega::mflx( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
   //                                      Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
   //                                                 Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cKomega::mflx( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdqdxl, Real *srhsl,
                                         Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqdxr, Real *srhsr, 
                                                    Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      if( ice > ics )
     {
         mflx33( ics,ice, icql,sxl,sql,sauxl,sdqdxl,srhsl, icqr,sxr,sqr,sauxr,sdqdxr,srhsr, sxc,swc,swxdc, sauxc, nfc, nq );
     }
  }

   void cKomega::mflx( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& rhsl,
                                         cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& rhsr,
                                         cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( ice > ics )
     {
         mflx33( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr, xc,wc,wxdc, auxc );
     }
  }

   //void cKomega::mflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
   //                                        Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
   //                                                   Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cKomega::mflx33( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdqdxl, Real *srhsl,
                                           Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqdxr, Real *srhsr, 
                                           Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;
      Int             nql, nqr;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         firstprivate(nql,nqr) \
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxl,dqdxr)\
         present(sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],sdqdxl[0:nv*nx*nfc],srhsl[0:nv*nfc],\
                 icqr[0:nfc],sxr[0:nx*nq],sqr[0:nv*nq],sauxr[0:naux*nq],sdqdxr[0:nv*nx*nq],srhsr[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)

         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];

            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
            wr=  wn[0]*( xn[0]- sxl[ADDR(0,iql,nql)] );
            wr+= wn[1]*( xn[1]- sxl[ADDR(1,iql,nql)] );
            wr+= wn[2]*( xn[2]- sxl[ADDR(2,iql,nql)] );

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients
            dqdxl[nv0][0]=  sdqdxl[ADDR(nv0,0,iql,nql)];
            dqdxl[nv0][1]=  sdqdxl[ADDR(nv0,1,iql,nql)];
            dqdxl[nv0][2]=  sdqdxl[ADDR(nv0,2,iql,nql)];

            dqdxr[nv0][0]=  sdqdxr[ADDR(nv0,0,iqr,nqr)];
            dqdxr[nv0][1]=  sdqdxr[ADDR(nv0,1,iqr,nqr)];
            dqdxr[nv0][2]=  sdqdxr[ADDR(nv0,2,iqr,nqr)];

            //dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            //dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];
            //dqnl[nv0]+= dqdxl[nv0][2][iql]*wc[2][ic];
            dqnl[nv0]=  dqdxl[nv0][0]*wn[0];
            dqnl[nv0]+= dqdxl[nv0][1]*wn[1];
            dqnl[nv0]+= dqdxl[nv0][2]*wn[2];

            //dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            //dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];
            //dqnr[nv0]+= dqdxr[nv0][2][iqr]*wc[2][ic];
            dqnr[nv0]=  dqdxr[nv0][0]*wn[0];
            dqnr[nv0]+= dqdxr[nv0][1]*wn[1];
            dqnr[nv0]+= dqdxr[nv0][2]*wn[2];

            //dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            //q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];
            dqn[nv0]= ( sqr[ADDR(nv0,iqr,nqr)]-sql[ADDR(nv0,iql,nql)] )/w;
            q[nv0]= wl*sql[ADDR(nv0,iql,nql)]+ wr*sqr[ADDR(nv0,iqr,nqr)];

            //dqnl[nv0+1]=  dqdxl[nv0+1][0][iql]*wc[0][ic];
            //dqnl[nv0+1]+= dqdxl[nv0+1][1][iql]*wc[1][ic];
            //dqnl[nv0+1]+= dqdxl[nv0+1][2][iql]*wc[2][ic];
            dqnl[nv0+1]=  dqdxl[nv0+1][0]*wn[0];
            dqnl[nv0+1]+= dqdxl[nv0+1][1]*wn[1];
            dqnl[nv0+1]+= dqdxl[nv0+1][2]*wn[2];

            //dqnr[nv0+1]=  dqdxr[nv0+1][0][iqr]*wc[0][ic];
            //dqnr[nv0+1]+= dqdxr[nv0+1][1][iqr]*wc[1][ic];
            //dqnr[nv0+1]+= dqdxr[nv0+1][2][iqr]*wc[2][ic];
            dqnr[nv0+1]=  dqdxr[nv0+1][0]*wn[0];
            dqnr[nv0+1]+= dqdxr[nv0+1][1]*wn[1];
            dqnr[nv0+1]+= dqdxr[nv0+1][2]*wn[2];

            //dqn[nv0+1]= ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;
            //q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];
            dqn[nv0+1]= ( sqr[ADDR(nv0+1,iqr,nqr)]-sql[ADDR(nv0+1,iql,nql)] )/w;
            q[nv0+1]= wl*sql[ADDR(nv0+1,iql,nql)]+ wr*sqr[ADDR(nv0+1,iqr,nqr)];

// tangential gradients

            //dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            //dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][0]- wn[0]*dqnl[nv0];
            dqtr= dqdxr[nv0][0]- wn[0]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            //dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][1]- wn[1]*dqnl[nv0];
            dqtr= dqdxr[nv0][1]- wn[1]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdxl[nv0][2][iql]- wc[2][ic]*dqnl[nv0];
            //dqtr= dqdxr[nv0][2][iqr]- wc[2][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][2]- wn[2]*dqnl[nv0];
            dqtr= dqdxr[nv0][2]- wn[2]*dqnr[nv0];
            dqt[nv0][2]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdxl[nv0+1][0][iql]- wc[0][ic]*dqnl[nv0+1];
            //dqtr= dqdxr[nv0+1][0][iqr]- wc[0][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][0]- wn[0]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][0]- wn[0]*dqnr[nv0+1];
            dqt[nv0+1][0]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdxl[nv0+1][1][iql]- wc[1][ic]*dqnl[nv0+1];
            //dqtr= dqdxr[nv0+1][1][iqr]- wc[1][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][1]- wn[1]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][1]- wn[1]*dqnr[nv0+1];
            dqt[nv0+1][1]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdxl[nv0+1][2][iql]- wc[2][ic]*dqnl[nv0+1];
            //dqtr= dqdxr[nv0+1][2][iqr]- wc[2][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][2]- wn[2]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][2]- wn[2]*dqnr[nv0+1];
            dqt[nv0+1][2]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            //dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            //dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];
            //dqdx[nv0][2]= dqn[nv0]*wc[2][ic]+ dqt[nv0][2];
            dqdx[nv0][0]= dqn[nv0]*wn[0]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wn[1]+ dqt[nv0][1];
            dqdx[nv0][2]= dqn[nv0]*wn[2]+ dqt[nv0][2];

            //dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic]+ dqt[nv0+1][0];
            //dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic]+ dqt[nv0+1][1];
            //dqdx[nv0+1][2]= dqn[nv0+1]*wc[2][ic]+ dqt[nv0+1][2];
            dqdx[nv0+1][0]= dqn[nv0+1]*wn[0]+ dqt[nv0+1][0];
            dqdx[nv0+1][1]= dqn[nv0+1]*wn[1]+ dqt[nv0+1][1];
            dqdx[nv0+1][2]= dqn[nv0+1]*wn[2]+ dqt[nv0+1][2];

// stress tensor
            //mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            //mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mu0= wl*sauxl[ADDR(naux0-2,iql,nql)]+ wr*sauxr[ADDR(naux0-2,iqr,nqr)];
            mu= wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
            mut= mu-mu0;
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];
            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
            rho= wl*sauxl[ADDR(0,iql,nql)]+ wr*sauxr[ADDR(0,iqr,nqr)];

            taun[nv0]= -(mu0+ stsigma*mut)*dqn[nv0];
            taun[nv0+1]= -(mu0+ sigma*mut)*dqn[nv0+1];
// accumulate
            //rhsr[nv0][iqr]+=   taun[nv0]*  wc[3][ic];
            //rhsr[nv0+1][iqr]+= taun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            srhsr[ADDR_(nv0,iqr,nqr)]+=  taun[nv0]*  wn[3];
            #pragma acc atomic
            srhsr[ADDR_((nv0+1),iqr,nqr)]+= taun[nv0+1]*wn[3];

            //rhsl[nv0][iql]-=   taun[nv0]*  wc[3][ic];
            //rhsl[nv0+1][iql]-= taun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            srhsl[ADDR_(nv0,iql,nql)]-=   taun[nv0]*  wn[3];
            #pragma acc atomic
            srhsl[ADDR_((nv0+1),iql,nql)]-= taun[nv0+1]*wn[3];

        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::mflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl0, cAu3xView<Real>& rhsl,
                                           cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr0, cAu3xView<Real>& rhsr,
                                           cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;
      Int             nql, nqr;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdqdxl, *srhsl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqdxr, *srhsr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sxl    = xl.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdqdxl = dqdxl0.get_data();
      srhsl  = rhsl.get_data();
      icqr   = icqr_view.get_data();
      sxr    = xr.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqdxr = dqdxr0.get_data();
      srhsr  = rhsr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         firstprivate(nql,nqr) \
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxl,dqdxr)\
         present(sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],sdqdxl[0:nv*nx*nfc],srhsl[0:nv*nfc],\
                 icqr[0:nfc],sxr[0:nx*nq],sqr[0:nv*nq],sauxr[0:naux*nq],sdqdxr[0:nv*nx*nq],srhsr[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)

         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];

            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
            wr=  wn[0]*( xn[0]- sxl[ADDR(0,iql,nql)] );
            wr+= wn[1]*( xn[1]- sxl[ADDR(1,iql,nql)] );
            wr+= wn[2]*( xn[2]- sxl[ADDR(2,iql,nql)] );

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients
            dqdxl[nv0][0]=  sdqdxl[ADDR(nv0,0,iql,nql)];
            dqdxl[nv0][1]=  sdqdxl[ADDR(nv0,1,iql,nql)];
            dqdxl[nv0][2]=  sdqdxl[ADDR(nv0,2,iql,nql)];

            dqdxr[nv0][0]=  sdqdxr[ADDR(nv0,0,iqr,nqr)];
            dqdxr[nv0][1]=  sdqdxr[ADDR(nv0,1,iqr,nqr)];
            dqdxr[nv0][2]=  sdqdxr[ADDR(nv0,2,iqr,nqr)];

            //dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            //dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];
            //dqnl[nv0]+= dqdxl[nv0][2][iql]*wc[2][ic];
            dqnl[nv0]=  dqdxl[nv0][0]*wn[0];
            dqnl[nv0]+= dqdxl[nv0][1]*wn[1];
            dqnl[nv0]+= dqdxl[nv0][2]*wn[2];

            //dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            //dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];
            //dqnr[nv0]+= dqdxr[nv0][2][iqr]*wc[2][ic];
            dqnr[nv0]=  dqdxr[nv0][0]*wn[0];
            dqnr[nv0]+= dqdxr[nv0][1]*wn[1];
            dqnr[nv0]+= dqdxr[nv0][2]*wn[2];

            //dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            //q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];
            dqn[nv0]= ( sqr[ADDR(nv0,iqr,nqr)]-sql[ADDR(nv0,iql,nql)] )/w;
            q[nv0]= wl*sql[ADDR(nv0,iql,nql)]+ wr*sqr[ADDR(nv0,iqr,nqr)];

            //dqnl[nv0+1]=  dqdxl[nv0+1][0][iql]*wc[0][ic];
            //dqnl[nv0+1]+= dqdxl[nv0+1][1][iql]*wc[1][ic];
            //dqnl[nv0+1]+= dqdxl[nv0+1][2][iql]*wc[2][ic];
            dqnl[nv0+1]=  dqdxl[nv0+1][0]*wn[0];
            dqnl[nv0+1]+= dqdxl[nv0+1][1]*wn[1];
            dqnl[nv0+1]+= dqdxl[nv0+1][2]*wn[2];

            //dqnr[nv0+1]=  dqdxr[nv0+1][0][iqr]*wc[0][ic];
            //dqnr[nv0+1]+= dqdxr[nv0+1][1][iqr]*wc[1][ic];
            //dqnr[nv0+1]+= dqdxr[nv0+1][2][iqr]*wc[2][ic];
            dqnr[nv0+1]=  dqdxr[nv0+1][0]*wn[0];
            dqnr[nv0+1]+= dqdxr[nv0+1][1]*wn[1];
            dqnr[nv0+1]+= dqdxr[nv0+1][2]*wn[2];

            //dqn[nv0+1]= ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;
            //q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];
            dqn[nv0+1]= ( sqr[ADDR(nv0+1,iqr,nqr)]-sql[ADDR(nv0+1,iql,nql)] )/w;
            q[nv0+1]= wl*sql[ADDR(nv0+1,iql,nql)]+ wr*sqr[ADDR(nv0+1,iqr,nqr)];

// tangential gradients

            //dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            //dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][0]- wn[0]*dqnl[nv0];
            dqtr= dqdxr[nv0][0]- wn[0]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            //dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][1]- wn[1]*dqnl[nv0];
            dqtr= dqdxr[nv0][1]- wn[1]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdxl[nv0][2][iql]- wc[2][ic]*dqnl[nv0];
            //dqtr= dqdxr[nv0][2][iqr]- wc[2][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][2]- wn[2]*dqnl[nv0];
            dqtr= dqdxr[nv0][2]- wn[2]*dqnr[nv0];
            dqt[nv0][2]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdxl[nv0+1][0][iql]- wc[0][ic]*dqnl[nv0+1];
            //dqtr= dqdxr[nv0+1][0][iqr]- wc[0][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][0]- wn[0]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][0]- wn[0]*dqnr[nv0+1];
            dqt[nv0+1][0]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdxl[nv0+1][1][iql]- wc[1][ic]*dqnl[nv0+1];
            //dqtr= dqdxr[nv0+1][1][iqr]- wc[1][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][1]- wn[1]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][1]- wn[1]*dqnr[nv0+1];
            dqt[nv0+1][1]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdxl[nv0+1][2][iql]- wc[2][ic]*dqnl[nv0+1];
            //dqtr= dqdxr[nv0+1][2][iqr]- wc[2][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][2]- wn[2]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][2]- wn[2]*dqnr[nv0+1];
            dqt[nv0+1][2]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            //dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            //dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];
            //dqdx[nv0][2]= dqn[nv0]*wc[2][ic]+ dqt[nv0][2];
            dqdx[nv0][0]= dqn[nv0]*wn[0]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wn[1]+ dqt[nv0][1];
            dqdx[nv0][2]= dqn[nv0]*wn[2]+ dqt[nv0][2];

            //dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic]+ dqt[nv0+1][0];
            //dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic]+ dqt[nv0+1][1];
            //dqdx[nv0+1][2]= dqn[nv0+1]*wc[2][ic]+ dqt[nv0+1][2];
            dqdx[nv0+1][0]= dqn[nv0+1]*wn[0]+ dqt[nv0+1][0];
            dqdx[nv0+1][1]= dqn[nv0+1]*wn[1]+ dqt[nv0+1][1];
            dqdx[nv0+1][2]= dqn[nv0+1]*wn[2]+ dqt[nv0+1][2];

// stress tensor
            //mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            //mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mu0= wl*sauxl[ADDR(naux0-2,iql,nql)]+ wr*sauxr[ADDR(naux0-2,iqr,nqr)];
            mu= wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
            mut= mu-mu0;
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];
            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
            rho= wl*sauxl[ADDR(0,iql,nql)]+ wr*sauxr[ADDR(0,iqr,nqr)];

            taun[nv0]= -(mu0+ stsigma*mut)*dqn[nv0];
            taun[nv0+1]= -(mu0+ sigma*mut)*dqn[nv0+1];
// accumulate
            //rhsr[nv0][iqr]+=   taun[nv0]*  wc[3][ic];
            //rhsr[nv0+1][iqr]+= taun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            srhsr[ADDR_(nv0,iqr,nqr)]+=  taun[nv0]*  wn[3];
            #pragma acc atomic
            srhsr[ADDR_((nv0+1),iqr,nqr)]+= taun[nv0+1]*wn[3];

            //rhsl[nv0][iql]-=   taun[nv0]*  wc[3][ic];
            //rhsl[nv0+1][iql]-= taun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            srhsl[ADDR_(nv0,iql,nql)]-=   taun[nv0]*  wn[3];
            #pragma acc atomic
            srhsl[ADDR_((nv0+1),iql,nql)]-= taun[nv0+1]*wn[3];

        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::mflx( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdqdx, Real *srhs,
                       Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      if( ice > ics )
     {
         mflx33( ics,ice, sicq,sx,sq,saux,sdqdx,srhs,sxc,swc,swxdc, sauxc, nfc, nq );
     }
  }

   void cKomega::mflx( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& rhs,
                       cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( ice > ics )
     {
         mflx33( ics,ice, icq,x,q,aux,dqdx,rhs,xc,wc,wxdc, auxc );
     }
  }

   //void cKomega::mflx33( Int ics, Int ice, Int *icq[2], Real *x[], Real *q0[], Real *aux[], Real **dqdx0[], Real *rhs[],
   //                                                     Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cKomega::mflx33( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdqdx, Real *srhs,
                         Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxl,dqdxr)\
         present(sicq[0:nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nv*nx*nq], srhs[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)

         for( ic=ics;ic<ice;ic++ )
        {
            
            //iql= icq[0][ic]; 
            //iqr= icq[1][ic]; 
            iql= sicq[ADDR(0,ic,nfc)]; 
            iqr= sicq[ADDR(1,ic,nfc)]; 

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];

            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients
            dqdxl[nv0][0] =  sdqdx[ADDR(nv0,0,iql,nq)];
            dqdxl[nv0][1] =  sdqdx[ADDR(nv0,1,iql,nq)];
            dqdxl[nv0][2] =  sdqdx[ADDR(nv0,2,iql,nq)];

            dqdxr[nv0][0] =  sdqdx[ADDR(nv0,0,iqr,nq)];
            dqdxr[nv0][1] =  sdqdx[ADDR(nv0,1,iqr,nq)];
            dqdxr[nv0][2] =  sdqdx[ADDR(nv0,2,iqr,nq)];

            //dqnl[nv0]=  dqdx0[nv0][0][iql]*wc[0][ic];
            //dqnl[nv0]+= dqdx0[nv0][1][iql]*wc[1][ic];
            //dqnl[nv0]+= dqdx0[nv0][2][iql]*wc[2][ic];
            dqnl[nv0]=  dqdxl[nv0][0]*wn[0];
            dqnl[nv0]+= dqdxl[nv0][1]*wn[1];
            dqnl[nv0]+= dqdxl[nv0][2]*wn[2];

            //dqnr[nv0]=  dqdx0[nv0][0][iqr]*wc[0][ic];
            //dqnr[nv0]+= dqdx0[nv0][1][iqr]*wc[1][ic];
            //dqnr[nv0]+= dqdx0[nv0][2][iqr]*wc[2][ic];
            dqnr[nv0]=  dqdxr[nv0][0]*wn[0];
            dqnr[nv0]+= dqdxr[nv0][1]*wn[1];
            dqnr[nv0]+= dqdxr[nv0][2]*wn[2];

            //dqn[nv0]= ( q0[nv0][iqr]-q0[nv0][iql] )/w;
            //q[nv0]= wl*q0[nv0][iql]+ wr*q0[nv0][iqr];
            dqn[nv0]= ( sq[ADDR(nv0,iqr,nq)]-sq[ADDR(nv0,iql,nq)] )/w;
            q[nv0]= wl*sq[ADDR(nv0,iql,nq)]+ wr*sq[ADDR(nv0,iqr,nq)];

            dqdxl[nv0+1][0] =  sdqdx[ADDR(nv0+1,0,iql,nq)];
            dqdxl[nv0+1][1] =  sdqdx[ADDR(nv0+1,1,iql,nq)];
            dqdxl[nv0+1][2] =  sdqdx[ADDR(nv0+1,2,iql,nq)];

            dqdxr[nv0+1][0] =  sdqdx[ADDR(nv0+1,0,iqr,nq)];
            dqdxr[nv0+1][1] =  sdqdx[ADDR(nv0+1,1,iqr,nq)];
            dqdxr[nv0+1][2] =  sdqdx[ADDR(nv0+1,2,iqr,nq)];

            //dqnl[nv0+1]=  dqdx0[nv0+1][0][iql]*wc[0][ic];
            //dqnl[nv0+1]+= dqdx0[nv0+1][1][iql]*wc[1][ic];
            //dqnl[nv0+1]+= dqdx0[nv0+1][2][iql]*wc[2][ic];
            dqnl[nv0+1]=  dqdxl[nv0+1][0]*wn[0];
            dqnl[nv0+1]+= dqdxl[nv0+1][1]*wn[1];
            dqnl[nv0+1]+= dqdxl[nv0+1][2]*wn[2];

            //dqnr[nv0+1]=  dqdx0[nv0+1][0][iqr]*wc[0][ic];
            //dqnr[nv0+1]+= dqdx0[nv0+1][1][iqr]*wc[1][ic];
            //dqnr[nv0+1]+= dqdx0[nv0+1][2][iqr]*wc[2][ic];
            dqnr[nv0+1]=  dqdxr[nv0+1][0]*wn[0];
            dqnr[nv0+1]+= dqdxr[nv0+1][1]*wn[1];
            dqnr[nv0+1]+= dqdxr[nv0+1][2]*wn[2];

            //dqn[nv0+1]= ( q0[nv0+1][iqr]-q0[nv0+1][iql] )/w;
            //q[nv0+1]= wl*q0[nv0+1][iql]+ wr*q0[nv0+1][iqr];
            dqn[nv0+1]= ( sq[ADDR(nv0+1,iqr,nq)]-sq[ADDR(nv0+1,iql,nq)] )/w;
            q[nv0+1]= wl*sq[ADDR(nv0+1,iql,nq)]+ wr*sq[ADDR(nv0+1,iqr,nq)];

// tangential gradients

            //dqtl= dqdx0[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            //dqtr= dqdx0[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][0]- wn[0]*dqnl[nv0];
            dqtr= dqdxr[nv0][0]- wn[0]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdx0[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            //dqtr= dqdx0[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][1]- wn[1]*dqnl[nv0];
            dqtr= dqdxr[nv0][1]- wn[1]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdx0[nv0][2][iql]- wc[2][ic]*dqnl[nv0];
            //dqtr= dqdx0[nv0][2][iqr]- wc[2][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][2]- wn[2]*dqnl[nv0];
            dqtr= dqdxr[nv0][2]- wn[2]*dqnr[nv0];
            dqt[nv0][2]= wl*dqtl+ wr*dqtr; 
            //dqtl= dqdx0[nv0+1][0][iql]- wc[0][ic]*dqnl[nv0+1];
            //dqtr= dqdx0[nv0+1][0][iqr]- wc[0][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][0]- wn[0]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][0]- wn[0]*dqnr[nv0+1];
            dqt[nv0+1][0]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdx0[nv0+1][1][iql]- wc[1][ic]*dqnl[nv0+1];
            //dqtr= dqdx0[nv0+1][1][iqr]- wc[1][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][1]- wn[1]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][1]- wn[1]*dqnr[nv0+1];
            dqt[nv0+1][1]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdx0[nv0+1][2][iql]- wc[2][ic]*dqnl[nv0+1];
            //dqtr= dqdx0[nv0+1][2][iqr]- wc[2][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][2]- wn[2]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][2]- wn[2]*dqnr[nv0+1];
            dqt[nv0+1][2]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            //dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            //dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];
            //dqdx[nv0][2]= dqn[nv0]*wc[2][ic]+ dqt[nv0][2];
            dqdx[nv0][0]= dqn[nv0]*wn[0]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wn[1]+ dqt[nv0][1];
            dqdx[nv0][2]= dqn[nv0]*wn[2]+ dqt[nv0][2];

            //dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic]+ dqt[nv0+1][0];
            //dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic]+ dqt[nv0+1][1];
            //dqdx[nv0+1][2]= dqn[nv0+1]*wc[2][ic]+ dqt[nv0+1][2];
            dqdx[nv0+1][0]= dqn[nv0+1]*wn[0]+ dqt[nv0+1][0];
            dqdx[nv0+1][1]= dqn[nv0+1]*wn[1]+ dqt[nv0+1][1];
            dqdx[nv0+1][2]= dqn[nv0+1]*wn[2]+ dqt[nv0+1][2];

// stress tensor
            //mu0= wl*aux[naux0-2][iql]+ wr*aux[naux0-2][iqr];
            //mu= wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
            mu0= wl*saux[ADDR(naux0-2,iql,nq)]+ wr*saux[ADDR(naux0-2,iqr,nq)];
            mu= wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
            mut= mu-mu0;
            //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
            //rho= wl*aux[0][iql]+ wr*aux[0][iqr];
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho= wl*saux[ADDR(0,iql,nq)]+ wr*saux[ADDR(0,iqr,nq)];

            taun[nv0]= -(mu0+ stsigma*mut)*dqn[nv0];
            taun[nv0+1]= -(mu0+ sigma*mut)*dqn[nv0+1];
// accumulate
            //rhs[nv0][iqr]+=   taun[nv0]*  wc[3][ic];
            //rhs[nv0+1][iqr]+= taun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            srhs[ADDR_(nv0,iqr,nq)]+=   taun[nv0]*  wn[3];
            #pragma acc atomic
            srhs[ADDR_((nv0+1),iqr,nq)]+= taun[nv0+1]*wn[3];

            //rhs[nv0][iql]-=   taun[nv0]*  wc[3][ic];
            //rhs[nv0+1][iql]-= taun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            srhs[ADDR_(nv0,iql,nq)]-=   taun[nv0]*  wn[3];
            #pragma acc atomic
            srhs[ADDR_((nv0+1),iql,nq)]-= taun[nv0+1]*wn[3];

        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::mflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx0, cAu3xView<Real>& rhs,
                         cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];

      Int nq, nfc;

      nfc = xc.get_dim1();
      nq  = q0.get_dim1();

      Int *sicq;
      Real *sx, *sq, *saux, *sdqdx, *srhs, *sxc, *swc, *swxdc, *sauxc;

      sicq = icq.get_data();
      sx = x.get_data();
      sq = q0.get_data();
      saux = aux.get_data();
      sdqdx = dqdx0.get_data();
      srhs = rhs.get_data();
      sxc = xc.get_data();
      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxl,dqdxr)\
         present(sicq[0:nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nv*nx*nq], srhs[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)

         for( ic=ics;ic<ice;ic++ )
        {
            
            //iql= icq[0][ic]; 
            //iqr= icq[1][ic]; 
            iql= sicq[ADDR(0,ic,nfc)]; 
            iqr= sicq[ADDR(1,ic,nfc)]; 

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];

            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients
            dqdxl[nv0][0] =  sdqdx[ADDR(nv0,0,iql,nq)];
            dqdxl[nv0][1] =  sdqdx[ADDR(nv0,1,iql,nq)];
            dqdxl[nv0][2] =  sdqdx[ADDR(nv0,2,iql,nq)];

            dqdxr[nv0][0] =  sdqdx[ADDR(nv0,0,iqr,nq)];
            dqdxr[nv0][1] =  sdqdx[ADDR(nv0,1,iqr,nq)];
            dqdxr[nv0][2] =  sdqdx[ADDR(nv0,2,iqr,nq)];

            //dqnl[nv0]=  dqdx0[nv0][0][iql]*wc[0][ic];
            //dqnl[nv0]+= dqdx0[nv0][1][iql]*wc[1][ic];
            //dqnl[nv0]+= dqdx0[nv0][2][iql]*wc[2][ic];
            dqnl[nv0]=  dqdxl[nv0][0]*wn[0];
            dqnl[nv0]+= dqdxl[nv0][1]*wn[1];
            dqnl[nv0]+= dqdxl[nv0][2]*wn[2];

            //dqnr[nv0]=  dqdx0[nv0][0][iqr]*wc[0][ic];
            //dqnr[nv0]+= dqdx0[nv0][1][iqr]*wc[1][ic];
            //dqnr[nv0]+= dqdx0[nv0][2][iqr]*wc[2][ic];
            dqnr[nv0]=  dqdxr[nv0][0]*wn[0];
            dqnr[nv0]+= dqdxr[nv0][1]*wn[1];
            dqnr[nv0]+= dqdxr[nv0][2]*wn[2];

            //dqn[nv0]= ( q0[nv0][iqr]-q0[nv0][iql] )/w;
            //q[nv0]= wl*q0[nv0][iql]+ wr*q0[nv0][iqr];
            dqn[nv0]= ( sq[ADDR(nv0,iqr,nq)]-sq[ADDR(nv0,iql,nq)] )/w;
            q[nv0]= wl*sq[ADDR(nv0,iql,nq)]+ wr*sq[ADDR(nv0,iqr,nq)];

            dqdxl[nv0+1][0] =  sdqdx[ADDR(nv0+1,0,iql,nq)];
            dqdxl[nv0+1][1] =  sdqdx[ADDR(nv0+1,1,iql,nq)];
            dqdxl[nv0+1][2] =  sdqdx[ADDR(nv0+1,2,iql,nq)];

            dqdxr[nv0+1][0] =  sdqdx[ADDR(nv0+1,0,iqr,nq)];
            dqdxr[nv0+1][1] =  sdqdx[ADDR(nv0+1,1,iqr,nq)];
            dqdxr[nv0+1][2] =  sdqdx[ADDR(nv0+1,2,iqr,nq)];

            //dqnl[nv0+1]=  dqdx0[nv0+1][0][iql]*wc[0][ic];
            //dqnl[nv0+1]+= dqdx0[nv0+1][1][iql]*wc[1][ic];
            //dqnl[nv0+1]+= dqdx0[nv0+1][2][iql]*wc[2][ic];
            dqnl[nv0+1]=  dqdxl[nv0+1][0]*wn[0];
            dqnl[nv0+1]+= dqdxl[nv0+1][1]*wn[1];
            dqnl[nv0+1]+= dqdxl[nv0+1][2]*wn[2];

            //dqnr[nv0+1]=  dqdx0[nv0+1][0][iqr]*wc[0][ic];
            //dqnr[nv0+1]+= dqdx0[nv0+1][1][iqr]*wc[1][ic];
            //dqnr[nv0+1]+= dqdx0[nv0+1][2][iqr]*wc[2][ic];
            dqnr[nv0+1]=  dqdxr[nv0+1][0]*wn[0];
            dqnr[nv0+1]+= dqdxr[nv0+1][1]*wn[1];
            dqnr[nv0+1]+= dqdxr[nv0+1][2]*wn[2];

            //dqn[nv0+1]= ( q0[nv0+1][iqr]-q0[nv0+1][iql] )/w;
            //q[nv0+1]= wl*q0[nv0+1][iql]+ wr*q0[nv0+1][iqr];
            dqn[nv0+1]= ( sq[ADDR(nv0+1,iqr,nq)]-sq[ADDR(nv0+1,iql,nq)] )/w;
            q[nv0+1]= wl*sq[ADDR(nv0+1,iql,nq)]+ wr*sq[ADDR(nv0+1,iqr,nq)];

// tangential gradients

            //dqtl= dqdx0[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            //dqtr= dqdx0[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][0]- wn[0]*dqnl[nv0];
            dqtr= dqdxr[nv0][0]- wn[0]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdx0[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            //dqtr= dqdx0[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][1]- wn[1]*dqnl[nv0];
            dqtr= dqdxr[nv0][1]- wn[1]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdx0[nv0][2][iql]- wc[2][ic]*dqnl[nv0];
            //dqtr= dqdx0[nv0][2][iqr]- wc[2][ic]*dqnr[nv0];
            dqtl= dqdxl[nv0][2]- wn[2]*dqnl[nv0];
            dqtr= dqdxr[nv0][2]- wn[2]*dqnr[nv0];
            dqt[nv0][2]= wl*dqtl+ wr*dqtr; 
            //dqtl= dqdx0[nv0+1][0][iql]- wc[0][ic]*dqnl[nv0+1];
            //dqtr= dqdx0[nv0+1][0][iqr]- wc[0][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][0]- wn[0]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][0]- wn[0]*dqnr[nv0+1];
            dqt[nv0+1][0]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdx0[nv0+1][1][iql]- wc[1][ic]*dqnl[nv0+1];
            //dqtr= dqdx0[nv0+1][1][iqr]- wc[1][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][1]- wn[1]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][1]- wn[1]*dqnr[nv0+1];
            dqt[nv0+1][1]= wl*dqtl+ wr*dqtr;

            //dqtl= dqdx0[nv0+1][2][iql]- wc[2][ic]*dqnl[nv0+1];
            //dqtr= dqdx0[nv0+1][2][iqr]- wc[2][ic]*dqnr[nv0+1];
            dqtl= dqdxl[nv0+1][2]- wn[2]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][2]- wn[2]*dqnr[nv0+1];
            dqt[nv0+1][2]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            //dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            //dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];
            //dqdx[nv0][2]= dqn[nv0]*wc[2][ic]+ dqt[nv0][2];
            dqdx[nv0][0]= dqn[nv0]*wn[0]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wn[1]+ dqt[nv0][1];
            dqdx[nv0][2]= dqn[nv0]*wn[2]+ dqt[nv0][2];

            //dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic]+ dqt[nv0+1][0];
            //dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic]+ dqt[nv0+1][1];
            //dqdx[nv0+1][2]= dqn[nv0+1]*wc[2][ic]+ dqt[nv0+1][2];
            dqdx[nv0+1][0]= dqn[nv0+1]*wn[0]+ dqt[nv0+1][0];
            dqdx[nv0+1][1]= dqn[nv0+1]*wn[1]+ dqt[nv0+1][1];
            dqdx[nv0+1][2]= dqn[nv0+1]*wn[2]+ dqt[nv0+1][2];

// stress tensor
            //mu0= wl*aux[naux0-2][iql]+ wr*aux[naux0-2][iqr];
            //mu= wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
            mu0= wl*saux[ADDR(naux0-2,iql,nq)]+ wr*saux[ADDR(naux0-2,iqr,nq)];
            mu= wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
            mut= mu-mu0;
            //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
            //rho= wl*aux[0][iql]+ wr*aux[0][iqr];
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho= wl*saux[ADDR(0,iql,nq)]+ wr*saux[ADDR(0,iqr,nq)];

            taun[nv0]= -(mu0+ stsigma*mut)*dqn[nv0];
            taun[nv0+1]= -(mu0+ sigma*mut)*dqn[nv0+1];
// accumulate
            //rhs[nv0][iqr]+=   taun[nv0]*  wc[3][ic];
            //rhs[nv0+1][iqr]+= taun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            srhs[ADDR_(nv0,iqr,nq)]+=   taun[nv0]*  wn[3];
            #pragma acc atomic
            srhs[ADDR_((nv0+1),iqr,nq)]+= taun[nv0+1]*wn[3];

            //rhs[nv0][iql]-=   taun[nv0]*  wc[3][ic];
            //rhs[nv0+1][iql]-= taun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            srhs[ADDR_(nv0,iql,nq)]-=   taun[nv0]*  wn[3];
            #pragma acc atomic
            srhs[ADDR_((nv0+1),iql,nq)]-= taun[nv0+1]*wn[3];

        }
        #pragma acc exit data delete(this)
     }
  }

   //void cKomega::dmflx( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
   //                                      Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
   //                                               Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cKomega::dmflx( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                          Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr, 
                                          Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa;

      if( ice > ics )
     { 
         dmflx33( ics,ice, icql,sxl,sql,sauxl,sdql,sdauxl,sresl, 
                           icqr,sxr,sqr,sauxr,sdqr,sdauxr,sresr,
                           sxc,swc,swxdc,sauxc,nfc,nq );
     }
  }

   void cKomega::dmflx( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                          cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                          cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( ice > ics )
     { 
         dmflx33( ics,ice, icql,xl,ql,auxl,dql,dauxl,resl, 
                           icqr,xr,qr,auxr,dqr,dauxr,resr,
                           xc,wc,wxdc,auxc );
     }
  }

   //void cKomega::dmflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
   //                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
   //                                                    Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cKomega::dmflx33( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                            Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr, 
                                            Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div,ddiv;
      Int             nql, nqr;

      if( ice > ics )
     { 
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         firstprivate(nql,nqr)\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun)\
         present (sxl[0:nx*nql],sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
                  icqr[0:nfc],sxr[0:nx*nqr],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
                  sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)

         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            iqr= icqr[ic];

// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
            wr=  swc[ADDR(0,ic,nfc)]*( sxc[ADDR(0,ic,nfc)]- sxl[ADDR(0,iql,nql)] );
            wr+= swc[ADDR(1,ic,nfc)]*( sxc[ADDR(1,ic,nfc)]- sxl[ADDR(1,iql,nql)] );
            wr+= swc[ADDR(2,ic,nfc)]*( sxc[ADDR(2,ic,nfc)]- sxl[ADDR(2,iql,nql)] );

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxc[ADDR(0,ic,nfc)] );
            wl+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxc[ADDR(1,ic,nfc)] );
            wl+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxc[ADDR(2,ic,nfc)] );
           
            w= wl+wr;
            wl/= w; 
            wr/= w; 

            //ddqn[nv0]= ( dauxr[nv0][iqr]-dauxl[nv0][iql] )/w;
            //dqn[nv0]=  ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            ddqn[nv0]= ( sdauxr[ADDR(nv0,iqr,nqr)]-sdauxl[ADDR(nv0,iql,nql)] )/w;
            dqn[nv0]=  ( sqr[ADDR(nv0,iqr,nqr)]-sql[ADDR(nv0,iql,nql)] )/w;

            //q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];
            //dq[nv0]= wl*dauxl[nv0][iql]+ wr*dauxr[nv0][iqr];
            q[nv0]= wl*sql[ADDR(nv0,iql,nql)]+ wr*sqr[ADDR(nv0,iqr,nqr)];
            dq[nv0]= wl*sdauxl[ADDR(nv0,iql,nql)]+ wr*sdauxr[ADDR(nv0,iqr,nqr)];

            //ddqn[nv0+1]= ( dauxr[nv0+1][iqr]-dauxl[nv0+1][iql] )/w;
            //dqn[nv0+1]=  ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;
            ddqn[nv0+1]= ( sdauxr[ADDR(nv0+1,iqr,nqr)]-sdauxl[ADDR(nv0+1,iql,nql)] )/w;
            dqn[nv0+1]=  ( sqr[ADDR(nv0+1,iqr,nqr)]-sql[ADDR(nv0+1,iql,nql)] )/w;

            //q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];
            //dq[nv0+1]= wl*dauxl[nv0+1][iql]+ wr*dauxr[nv0+1][iqr];
            q[nv0+1]= wl*sql[ADDR(nv0+1,iql,nql)]+ wr*sqr[ADDR(nv0+1,iqr,nqr)];
            dq[nv0+1]= wl*sdauxl[ADDR(nv0+1,iql,nql)]+ wr*sdauxr[ADDR(nv0+1,iqr,nqr)];

            //dqdx[nv0][0]= dqn[nv0]*wc[0][ic];
            //dqdx[nv0][1]= dqn[nv0]*wc[1][ic];
            //dqdx[nv0][2]= dqn[nv0]*wc[2][ic];
            dqdx[nv0][0]= dqn[nv0]*swc[ADDR(0,ic,nfc)];
            dqdx[nv0][1]= dqn[nv0]*swc[ADDR(1,ic,nfc)];
            dqdx[nv0][2]= dqn[nv0]*swc[ADDR(2,ic,nfc)];

            //ddqdx[nv0][0]= ddqn[nv0]*wc[0][ic];
            //ddqdx[nv0][1]= ddqn[nv0]*wc[1][ic];
            //ddqdx[nv0][2]= ddqn[nv0]*wc[2][ic];
            ddqdx[nv0][0]= ddqn[nv0]*swc[ADDR(0,ic,nfc)];
            ddqdx[nv0][1]= ddqn[nv0]*swc[ADDR(1,ic,nfc)];
            ddqdx[nv0][2]= ddqn[nv0]*swc[ADDR(2,ic,nfc)];

            //dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic];
            //dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic];
            //dqdx[nv0+1][2]= dqn[nv0+1]*wc[2][ic];
            dqdx[nv0+1][0]= dqn[nv0+1]*swc[ADDR(0,ic,nfc)];
            dqdx[nv0+1][1]= dqn[nv0+1]*swc[ADDR(1,ic,nfc)];
            dqdx[nv0+1][2]= dqn[nv0+1]*swc[ADDR(2,ic,nfc)];

            //ddqdx[nv0+1][0]= ddqn[nv0+1]*wc[0][ic];
            //ddqdx[nv0+1][1]= ddqn[nv0+1]*wc[1][ic];
            //ddqdx[nv0+1][2]= ddqn[nv0+1]*wc[2][ic];
            ddqdx[nv0+1][0]= ddqn[nv0+1]*swc[ADDR(0,ic,nfc)];
            ddqdx[nv0+1][1]= ddqn[nv0+1]*swc[ADDR(1,ic,nfc)];
            ddqdx[nv0+1][2]= ddqn[nv0+1]*swc[ADDR(2,ic,nfc)];

// stress tensor
            //mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            //mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mu0= wl*sauxl[ADDR(naux0-2,iql,nql)]+ wr*sauxr[ADDR(naux0-2,iqr,nqr)];
            mu= wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
            mut= mu-mu0;
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];
            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
            rho= wl*sauxl[ADDR(0,iql,nql)]+ wr*sauxr[ADDR(0,iqr,nqr)];

// diffusion for k and omega
            dtaun[nv0]= -(mu0+ stsigma*mut)*ddqn[nv0];
            dtaun[nv0+1]= -(mu0+ sigma*mut)*ddqn[nv0+1];
           
// accumulate

            //resr[nv0][iqr]+=   dtaun[nv0]*wc[3][ic];
            //resr[nv0+1][iqr]+= dtaun[nv0+1]*wc[3][ic];
            sresr[ADDR_(nv0,iqr,nqr)]+=   dtaun[nv0]*swc[ADDR_(3,ic,nfc)];
            sresr[ADDR_((nv0+1),iqr,nqr)]+= dtaun[nv0+1]*swc[ADDR_(3,ic,nfc)];

            //resl[nv0][iql]-=   dtaun[nv0]*wc[3][ic];
            //resl[nv0+1][iql]-= dtaun[nv0+1]*wc[3][ic];
            sresl[ADDR_(nv0,iql,nql)]-=   dtaun[nv0]*swc[ADDR_(3,ic,nfc)];
            sresl[ADDR_((nv0+1),iql,nql)]-= dtaun[nv0+1]*swc[ADDR_(3,ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::dmflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                            cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                            cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div,ddiv;
      Int             nql, nqr;

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sxl    = xl.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdql   = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl  = resl.get_data();
      icqr   = icqr_view.get_data();
      sxr    = xr.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqr   = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr  = resr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      if( ice > ics )
     { 
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         firstprivate(nql,nqr)\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun)\
         present (sxl[0:nx*nql],sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
                  icqr[0:nfc],sxr[0:nx*nqr],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
                  sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)

         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            iqr= icqr[ic];

// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
            wr=  swc[ADDR(0,ic,nfc)]*( sxc[ADDR(0,ic,nfc)]- sxl[ADDR(0,iql,nql)] );
            wr+= swc[ADDR(1,ic,nfc)]*( sxc[ADDR(1,ic,nfc)]- sxl[ADDR(1,iql,nql)] );
            wr+= swc[ADDR(2,ic,nfc)]*( sxc[ADDR(2,ic,nfc)]- sxl[ADDR(2,iql,nql)] );

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxc[ADDR(0,ic,nfc)] );
            wl+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxc[ADDR(1,ic,nfc)] );
            wl+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxc[ADDR(2,ic,nfc)] );
           
            w= wl+wr;
            wl/= w; 
            wr/= w; 

            //ddqn[nv0]= ( dauxr[nv0][iqr]-dauxl[nv0][iql] )/w;
            //dqn[nv0]=  ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            ddqn[nv0]= ( sdauxr[ADDR(nv0,iqr,nqr)]-sdauxl[ADDR(nv0,iql,nql)] )/w;
            dqn[nv0]=  ( sqr[ADDR(nv0,iqr,nqr)]-sql[ADDR(nv0,iql,nql)] )/w;

            //q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];
            //dq[nv0]= wl*dauxl[nv0][iql]+ wr*dauxr[nv0][iqr];
            q[nv0]= wl*sql[ADDR(nv0,iql,nql)]+ wr*sqr[ADDR(nv0,iqr,nqr)];
            dq[nv0]= wl*sdauxl[ADDR(nv0,iql,nql)]+ wr*sdauxr[ADDR(nv0,iqr,nqr)];

            //ddqn[nv0+1]= ( dauxr[nv0+1][iqr]-dauxl[nv0+1][iql] )/w;
            //dqn[nv0+1]=  ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;
            ddqn[nv0+1]= ( sdauxr[ADDR(nv0+1,iqr,nqr)]-sdauxl[ADDR(nv0+1,iql,nql)] )/w;
            dqn[nv0+1]=  ( sqr[ADDR(nv0+1,iqr,nqr)]-sql[ADDR(nv0+1,iql,nql)] )/w;

            //q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];
            //dq[nv0+1]= wl*dauxl[nv0+1][iql]+ wr*dauxr[nv0+1][iqr];
            q[nv0+1]= wl*sql[ADDR(nv0+1,iql,nql)]+ wr*sqr[ADDR(nv0+1,iqr,nqr)];
            dq[nv0+1]= wl*sdauxl[ADDR(nv0+1,iql,nql)]+ wr*sdauxr[ADDR(nv0+1,iqr,nqr)];

            //dqdx[nv0][0]= dqn[nv0]*wc[0][ic];
            //dqdx[nv0][1]= dqn[nv0]*wc[1][ic];
            //dqdx[nv0][2]= dqn[nv0]*wc[2][ic];
            dqdx[nv0][0]= dqn[nv0]*swc[ADDR(0,ic,nfc)];
            dqdx[nv0][1]= dqn[nv0]*swc[ADDR(1,ic,nfc)];
            dqdx[nv0][2]= dqn[nv0]*swc[ADDR(2,ic,nfc)];

            //ddqdx[nv0][0]= ddqn[nv0]*wc[0][ic];
            //ddqdx[nv0][1]= ddqn[nv0]*wc[1][ic];
            //ddqdx[nv0][2]= ddqn[nv0]*wc[2][ic];
            ddqdx[nv0][0]= ddqn[nv0]*swc[ADDR(0,ic,nfc)];
            ddqdx[nv0][1]= ddqn[nv0]*swc[ADDR(1,ic,nfc)];
            ddqdx[nv0][2]= ddqn[nv0]*swc[ADDR(2,ic,nfc)];

            //dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic];
            //dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic];
            //dqdx[nv0+1][2]= dqn[nv0+1]*wc[2][ic];
            dqdx[nv0+1][0]= dqn[nv0+1]*swc[ADDR(0,ic,nfc)];
            dqdx[nv0+1][1]= dqn[nv0+1]*swc[ADDR(1,ic,nfc)];
            dqdx[nv0+1][2]= dqn[nv0+1]*swc[ADDR(2,ic,nfc)];

            //ddqdx[nv0+1][0]= ddqn[nv0+1]*wc[0][ic];
            //ddqdx[nv0+1][1]= ddqn[nv0+1]*wc[1][ic];
            //ddqdx[nv0+1][2]= ddqn[nv0+1]*wc[2][ic];
            ddqdx[nv0+1][0]= ddqn[nv0+1]*swc[ADDR(0,ic,nfc)];
            ddqdx[nv0+1][1]= ddqn[nv0+1]*swc[ADDR(1,ic,nfc)];
            ddqdx[nv0+1][2]= ddqn[nv0+1]*swc[ADDR(2,ic,nfc)];

// stress tensor
            //mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            //mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mu0= wl*sauxl[ADDR(naux0-2,iql,nql)]+ wr*sauxr[ADDR(naux0-2,iqr,nqr)];
            mu= wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
            mut= mu-mu0;
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];
            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
            rho= wl*sauxl[ADDR(0,iql,nql)]+ wr*sauxr[ADDR(0,iqr,nqr)];

// diffusion for k and omega
            dtaun[nv0]= -(mu0+ stsigma*mut)*ddqn[nv0];
            dtaun[nv0+1]= -(mu0+ sigma*mut)*ddqn[nv0+1];
           
// accumulate

            //resr[nv0][iqr]+=   dtaun[nv0]*wc[3][ic];
            //resr[nv0+1][iqr]+= dtaun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            sresr[ADDR_(nv0,iqr,nqr)]+=   dtaun[nv0]*swc[ADDR_(3,ic,nfc)];
            #pragma acc atomic
            sresr[ADDR_((nv0+1),iqr,nqr)]+= dtaun[nv0+1]*swc[ADDR_(3,ic,nfc)];

            //resl[nv0][iql]-=   dtaun[nv0]*wc[3][ic];
            //resl[nv0+1][iql]-= dtaun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            sresl[ADDR_(nv0,iql,nql)]-=   dtaun[nv0]*swc[ADDR_(3,ic,nfc)];
            #pragma acc atomic
            sresl[ADDR_((nv0+1),iql,nql)]-= dtaun[nv0+1]*swc[ADDR_(3,ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::dmflx( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sres,
                        Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      if( ice > ics )
     { 
         dmflx33( ics,ice, sicq,sx,sq,saux,sdq,sdaux,sres,sxc,swc,swxdc,sauxc,nfc,nq );
     }
  }

   void cKomega::dmflx( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                        cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( ice > ics )
     { 
         dmflx33( ics,ice, icq,x,q,aux,dq,daux,res,xc,wc,wxdc,auxc );
     }
  }

   //void cKomega::dmflx33( Int ics, Int ice, Int *icq[2], Real *x[], Real *q0[], Real *aux[], Real *dq0[], Real *daux[], Real *res[],
   //                       Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cKomega::dmflx33( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sres,
                          Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div,ddiv;

      if( ice > ics )
     { 
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun)\
         present (sicq[0:2*nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],\
                  sres[0:nv*nq],sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];

// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  swc[ADDR(0,ic,nfc)]*( sxc[ADDR(0,ic,nfc)]- sx[ADDR(0,iql,nq)] );
            wr+= swc[ADDR(1,ic,nfc)]*( sxc[ADDR(1,ic,nfc)]- sx[ADDR(1,iql,nq)] );
            wr+= swc[ADDR(2,ic,nfc)]*( sxc[ADDR(2,ic,nfc)]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  swc[ADDR(0,ic,nfc)]*( sx[ADDR(0,iqr,nq)]- sxc[ADDR(0,ic,nfc)] );
            wl+= swc[ADDR(1,ic,nfc)]*( sx[ADDR(1,iqr,nq)]- sxc[ADDR(1,ic,nfc)] );
            wl+= swc[ADDR(2,ic,nfc)]*( sx[ADDR(2,iqr,nq)]- sxc[ADDR(2,ic,nfc)] );
           
            w= wl+wr;
            wl/= w; 
            wr/= w; 

            //ddqn[nv0]= ( daux[nv0][iqr]-daux[nv0][iql] )/w;
            //dqn[nv0]=  ( q0[nv0][iqr]-q0[nv0][iql] )/w;
            ddqn[nv0]= ( sdaux[ADDR(nv0,iqr,nq)]-sdaux[ADDR(nv0,iql,nq)] )/w;
            dqn[nv0]=  ( sq[ADDR(nv0,iqr,nq)]-sq[ADDR(nv0,iql,nq)] )/w;

            //q[nv0]= wl*q0[nv0][iql]+ wr*q0[nv0][iqr];
            //dq[nv0]= wl*daux[nv0][iql]+ wr*daux[nv0][iqr];
            q[nv0]= wl*sq[ADDR(nv0,iql,nq)]+ wr*sq[ADDR(nv0,iqr,nq)];
            dq[nv0]= wl*sdaux[ADDR(nv0,iql,nq)]+ wr*sdaux[ADDR(nv0,iqr,nq)];

            //ddqn[nv0+1]= ( daux[nv0+1][iqr]-daux[nv0+1][iql] )/w;
            //dqn[nv0+1]=  ( q0[nv0+1][iqr]-q0[nv0+1][iql] )/w;
            ddqn[nv0+1]= ( sdaux[ADDR(nv0+1,iqr,nq)]-sdaux[ADDR(nv0+1,iql,nq)] )/w;
            dqn[nv0+1]=  ( sq[ADDR(nv0+1,iqr,nq)]-sq[ADDR(nv0+1,iql,nq)] )/w;

            //q[nv0+1]= wl*q0[nv0+1][iql]+ wr*q0[nv0+1][iqr];
            //dq[nv0+1]= wl*daux[nv0+1][iql]+ wr*daux[nv0+1][iqr];
            q[nv0+1]= wl*sq[ADDR(nv0+1,iql,nq)]+ wr*sq[ADDR(nv0+1,iqr,nq)];
            dq[nv0+1]= wl*sdaux[ADDR(nv0+1,iql,nq)]+ wr*sdaux[ADDR(nv0+1,iqr,nq)];

            //dqdx[nv0][0]= dqn[nv0]*wc[0][ic];
            //dqdx[nv0][1]= dqn[nv0]*wc[1][ic];
            //dqdx[nv0][2]= dqn[nv0]*wc[2][ic];
            dqdx[nv0][0]= dqn[nv0]*swc[ADDR(0,ic,nfc)];
            dqdx[nv0][1]= dqn[nv0]*swc[ADDR(1,ic,nfc)];
            dqdx[nv0][2]= dqn[nv0]*swc[ADDR(2,ic,nfc)];

            //ddqdx[nv0][0]= ddqn[nv0]*wc[0][ic];
            //ddqdx[nv0][1]= ddqn[nv0]*wc[1][ic];
            //ddqdx[nv0][2]= ddqn[nv0]*wc[2][ic];
            ddqdx[nv0][0]= ddqn[nv0]*swc[ADDR(0,ic,nfc)];
            ddqdx[nv0][1]= ddqn[nv0]*swc[ADDR(1,ic,nfc)];
            ddqdx[nv0][2]= ddqn[nv0]*swc[ADDR(2,ic,nfc)];

            //dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic];
            //dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic];
            //dqdx[nv0+1][2]= dqn[nv0+1]*wc[2][ic];
            dqdx[nv0+1][0]= dqn[nv0+1]*swc[ADDR(0,ic,nfc)];
            dqdx[nv0+1][1]= dqn[nv0+1]*swc[ADDR(1,ic,nfc)];
            dqdx[nv0+1][2]= dqn[nv0+1]*swc[ADDR(2,ic,nfc)];

            //ddqdx[nv0+1][0]= ddqn[nv0+1]*wc[0][ic];
            //ddqdx[nv0+1][1]= ddqn[nv0+1]*wc[1][ic];
            //ddqdx[nv0+1][2]= ddqn[nv0+1]*wc[2][ic];
            ddqdx[nv0+1][0]= ddqn[nv0+1]*swc[ADDR(0,ic,nfc)];
            ddqdx[nv0+1][1]= ddqn[nv0+1]*swc[ADDR(1,ic,nfc)];
            ddqdx[nv0+1][2]= ddqn[nv0+1]*swc[ADDR(2,ic,nfc)];

// stress tensor
            //mu0= wl*aux[naux0-2][iql]+ wr*aux[naux0-2][iqr];
            //mu= wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
            mu0= wl*saux[ADDR(naux0-2,iql,nq)]+ wr*saux[ADDR(naux0-2,iqr,nq)];
            mu= wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
            mut= mu-mu0;
            //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
            //rho= wl*aux[0][iql]+ wr*aux[0][iqr];
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho= wl*saux[ADDR(0,iql,nq)]+ wr*saux[ADDR(0,iqr,nq)];

// diffusion for k and omega
            dtaun[nv0]= -(mu0+ stsigma*mut)*ddqn[nv0];
            dtaun[nv0+1]= -(mu0+ sigma*mut)*ddqn[nv0+1];
           
// accumulate

            //res[nv0][iqr]+=   dtaun[nv0]*wc[3][ic];
            //res[nv0+1][iqr]+= dtaun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            sres[ADDR_(nv0,iqr,nq)]+=   dtaun[nv0]*swc[ADDR_(3,ic,nfc)];
            #pragma acc atomic
            sres[ADDR_((nv0+1),iqr,nq)]+= dtaun[nv0+1]*swc[ADDR_(3,ic,nfc)];

            //res[nv0][iql]-=   dtaun[nv0]*wc[3][ic];
            //res[nv0+1][iql]-= dtaun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            sres[ADDR_(nv0,iql,nq)]-=   dtaun[nv0]*swc[ADDR_(3,ic,nfc)];
            #pragma acc atomic
            sres[ADDR_((nv0+1),iql,nq)]-= dtaun[nv0+1]*swc[ADDR_(3,ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::dmflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                          cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div,ddiv;

      Int nfc, nq;
      Int *sicq;
      Real *sx, *sq, *saux, *sdq, *sdaux, *sres, *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = q0.get_dim1();

      sicq  = icq.get_data();
      sx    = x.get_data();
      sq    = q0.get_data();
      saux  = aux.get_data();
      sdq   = dq0.get_data();
      sdaux = daux.get_data();
      sres  = res.get_data();
      sxc   = xc.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     { 
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun)\
         present (sicq[0:2*nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],\
                  sres[0:nv*nq],sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];

// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  swc[ADDR(0,ic,nfc)]*( sxc[ADDR(0,ic,nfc)]- sx[ADDR(0,iql,nq)] );
            wr+= swc[ADDR(1,ic,nfc)]*( sxc[ADDR(1,ic,nfc)]- sx[ADDR(1,iql,nq)] );
            wr+= swc[ADDR(2,ic,nfc)]*( sxc[ADDR(2,ic,nfc)]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  swc[ADDR(0,ic,nfc)]*( sx[ADDR(0,iqr,nq)]- sxc[ADDR(0,ic,nfc)] );
            wl+= swc[ADDR(1,ic,nfc)]*( sx[ADDR(1,iqr,nq)]- sxc[ADDR(1,ic,nfc)] );
            wl+= swc[ADDR(2,ic,nfc)]*( sx[ADDR(2,iqr,nq)]- sxc[ADDR(2,ic,nfc)] );
           
            w= wl+wr;
            wl/= w; 
            wr/= w; 

            //ddqn[nv0]= ( daux[nv0][iqr]-daux[nv0][iql] )/w;
            //dqn[nv0]=  ( q0[nv0][iqr]-q0[nv0][iql] )/w;
            ddqn[nv0]= ( sdaux[ADDR(nv0,iqr,nq)]-sdaux[ADDR(nv0,iql,nq)] )/w;
            dqn[nv0]=  ( sq[ADDR(nv0,iqr,nq)]-sq[ADDR(nv0,iql,nq)] )/w;

            //q[nv0]= wl*q0[nv0][iql]+ wr*q0[nv0][iqr];
            //dq[nv0]= wl*daux[nv0][iql]+ wr*daux[nv0][iqr];
            q[nv0]= wl*sq[ADDR(nv0,iql,nq)]+ wr*sq[ADDR(nv0,iqr,nq)];
            dq[nv0]= wl*sdaux[ADDR(nv0,iql,nq)]+ wr*sdaux[ADDR(nv0,iqr,nq)];

            //ddqn[nv0+1]= ( daux[nv0+1][iqr]-daux[nv0+1][iql] )/w;
            //dqn[nv0+1]=  ( q0[nv0+1][iqr]-q0[nv0+1][iql] )/w;
            ddqn[nv0+1]= ( sdaux[ADDR(nv0+1,iqr,nq)]-sdaux[ADDR(nv0+1,iql,nq)] )/w;
            dqn[nv0+1]=  ( sq[ADDR(nv0+1,iqr,nq)]-sq[ADDR(nv0+1,iql,nq)] )/w;

            //q[nv0+1]= wl*q0[nv0+1][iql]+ wr*q0[nv0+1][iqr];
            //dq[nv0+1]= wl*daux[nv0+1][iql]+ wr*daux[nv0+1][iqr];
            q[nv0+1]= wl*sq[ADDR(nv0+1,iql,nq)]+ wr*sq[ADDR(nv0+1,iqr,nq)];
            dq[nv0+1]= wl*sdaux[ADDR(nv0+1,iql,nq)]+ wr*sdaux[ADDR(nv0+1,iqr,nq)];

            //dqdx[nv0][0]= dqn[nv0]*wc[0][ic];
            //dqdx[nv0][1]= dqn[nv0]*wc[1][ic];
            //dqdx[nv0][2]= dqn[nv0]*wc[2][ic];
            dqdx[nv0][0]= dqn[nv0]*swc[ADDR(0,ic,nfc)];
            dqdx[nv0][1]= dqn[nv0]*swc[ADDR(1,ic,nfc)];
            dqdx[nv0][2]= dqn[nv0]*swc[ADDR(2,ic,nfc)];

            //ddqdx[nv0][0]= ddqn[nv0]*wc[0][ic];
            //ddqdx[nv0][1]= ddqn[nv0]*wc[1][ic];
            //ddqdx[nv0][2]= ddqn[nv0]*wc[2][ic];
            ddqdx[nv0][0]= ddqn[nv0]*swc[ADDR(0,ic,nfc)];
            ddqdx[nv0][1]= ddqn[nv0]*swc[ADDR(1,ic,nfc)];
            ddqdx[nv0][2]= ddqn[nv0]*swc[ADDR(2,ic,nfc)];

            //dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic];
            //dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic];
            //dqdx[nv0+1][2]= dqn[nv0+1]*wc[2][ic];
            dqdx[nv0+1][0]= dqn[nv0+1]*swc[ADDR(0,ic,nfc)];
            dqdx[nv0+1][1]= dqn[nv0+1]*swc[ADDR(1,ic,nfc)];
            dqdx[nv0+1][2]= dqn[nv0+1]*swc[ADDR(2,ic,nfc)];

            //ddqdx[nv0+1][0]= ddqn[nv0+1]*wc[0][ic];
            //ddqdx[nv0+1][1]= ddqn[nv0+1]*wc[1][ic];
            //ddqdx[nv0+1][2]= ddqn[nv0+1]*wc[2][ic];
            ddqdx[nv0+1][0]= ddqn[nv0+1]*swc[ADDR(0,ic,nfc)];
            ddqdx[nv0+1][1]= ddqn[nv0+1]*swc[ADDR(1,ic,nfc)];
            ddqdx[nv0+1][2]= ddqn[nv0+1]*swc[ADDR(2,ic,nfc)];

// stress tensor
            //mu0= wl*aux[naux0-2][iql]+ wr*aux[naux0-2][iqr];
            //mu= wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
            mu0= wl*saux[ADDR(naux0-2,iql,nq)]+ wr*saux[ADDR(naux0-2,iqr,nq)];
            mu= wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
            mut= mu-mu0;
            //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
            //rho= wl*aux[0][iql]+ wr*aux[0][iqr];
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho= wl*saux[ADDR(0,iql,nq)]+ wr*saux[ADDR(0,iqr,nq)];

// diffusion for k and omega
            dtaun[nv0]= -(mu0+ stsigma*mut)*ddqn[nv0];
            dtaun[nv0+1]= -(mu0+ sigma*mut)*ddqn[nv0+1];
           
// accumulate

            //res[nv0][iqr]+=   dtaun[nv0]*wc[3][ic];
            //res[nv0+1][iqr]+= dtaun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            sres[ADDR_(nv0,iqr,nq)]+=   dtaun[nv0]*swc[ADDR_(3,ic,nfc)];
            #pragma acc atomic
            sres[ADDR_((nv0+1),iqr,nq)]+= dtaun[nv0+1]*swc[ADDR_(3,ic,nfc)];

            //res[nv0][iql]-=   dtaun[nv0]*wc[3][ic];
            //res[nv0+1][iql]-= dtaun[nv0+1]*wc[3][ic];
            #pragma acc atomic
            sres[ADDR_(nv0,iql,nq)]-=   dtaun[nv0]*swc[ADDR_(3,ic,nfc)];
            #pragma acc atomic
            sres[ADDR_((nv0+1),iql,nq)]-= dtaun[nv0+1]*swc[ADDR_(3,ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomega::dvar( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dU[], Real *dq[] )
  {
      Int iq; 
      Real ro,dro;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            ro= aux[0][iq];
            dro= dU[0][iq];
            dq[nv0][iq]=   ( dU[nv0][iq]-   q[nv0][iq]*dro   )/ro;
            dq[nv0+1][iq]= ( dU[nv0+1][iq]- q[nv0+1][iq]*dro )/ro;
            dq[nv0][iq]=   fmax( dq[nv0  ][iq],-0.95*q[nv0  ][iq] );
            dq[nv0+1][iq]= fmax( dq[nv0+1][iq],-0.95*q[nv0+1][iq] );
        }
     } 
  }

   void cKomega::dvar( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq )
  {
      Int iq; 
      Real ro,dro;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            ro= aux[ADDR(0,iq,nq)];
            dro= dU[ADDR(0,iq,nq)];
            dq[ADDR(nv0,iq,nq)]=   ( dU[ADDR(nv0,iq,nq)]-   q[ADDR(nv0,iq,nq)]*dro   )/ro;
            dq[ADDR(nv0+1,iq,nq)]= ( dU[ADDR(nv0+1,iq,nq)]- q[ADDR(nv0+1,iq,nq)]*dro )/ro;
            dq[ADDR(nv0,iq,nq)]=   fmax( dq[ADDR(nv0  ,iq,nq)],-0.95*q[ADDR(nv0  ,iq,nq)] );
            dq[ADDR(nv0+1,iq,nq)]= fmax( dq[ADDR(nv0+1,iq,nq)],-0.95*q[ADDR(nv0+1,iq,nq)] );
        }
     } 
  }

   void cKomega::dvargpu( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq )
  {
      Int iq; 
      Real ro,dro;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(q[0:nv*nq],aux[0:naux*nq],dU[0:nv*nq],dq[0:nv*nq],this) \
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            ro= aux[ADDR(0,iq,nq)];
            dro= dU[ADDR(0,iq,nq)];
            dq[ADDR(nv0,iq,nq)]=   ( dU[ADDR(nv0,iq,nq)]-   q[ADDR(nv0,iq,nq)]*dro   )/ro;
            dq[ADDR(nv0+1,iq,nq)]= ( dU[ADDR(nv0+1,iq,nq)]- q[ADDR(nv0+1,iq,nq)]*dro )/ro;
            dq[ADDR(nv0,iq,nq)]=   fmax( dq[ADDR(nv0  ,iq,nq)],-0.95*q[ADDR(nv0  ,iq,nq)] );
            dq[ADDR(nv0+1,iq,nq)]= fmax( dq[ADDR(nv0+1,iq,nq)],-0.95*q[ADDR(nv0+1,iq,nq)] );
        }
        #pragma acc exit data delete(this)
     } 
  }

   void cKomega::dvar( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view )
  {
      Int iq; 
      Real ro,dro;

      Int nq;
      Real *q, *aux, *dU, *dq;

      nq = q_view.get_dim1();

      q = q_view.get_data();
      aux = aux_view.get_data();
      dU = dU_view.get_data();
      dq = dq_view.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(q[0:nv*nq],aux[0:naux*nq],dU[0:nv*nq],dq[0:nv*nq],this) \
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            ro= aux[ADDR(0,iq,nq)];
            dro= dU[ADDR(0,iq,nq)]; 
            dq[ADDR(nv0,iq,nq)]=   ( dU[ADDR(nv0,iq,nq)]-   q[ADDR(nv0,iq,nq)]*dro   )/ro;
            dq[ADDR(nv0+1,iq,nq)]= ( dU[ADDR(nv0+1,iq,nq)]- q[ADDR(nv0+1,iq,nq)]*dro )/ro;
            dq[ADDR(nv0,iq,nq)]=   fmax( dq[ADDR(nv0  ,iq,nq)],-0.95*q[ADDR(nv0  ,iq,nq)] );
            dq[ADDR(nv0+1,iq,nq)]= fmax( dq[ADDR(nv0+1,iq,nq)],-0.95*q[ADDR(nv0+1,iq,nq)] );
        }
        #pragma acc exit data delete(this)
     }
  }

