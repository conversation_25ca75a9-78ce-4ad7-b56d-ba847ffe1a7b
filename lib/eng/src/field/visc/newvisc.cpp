   using namespace std;

#  include <field/visc.h>

   cVisc *newvisc( Int ityp )
  {
      cVisc *val= NULL;
      switch( ityp )
     {
         case(laminar_visc):
        {
            val= new cLaminar();
            break;
        }
         case(cebeci_visc):
        {
            val= new cCebeci();
            break;
        }
         case(cebecilowre_visc):
        {
            val= new cCebeciLowRe();
            break;
        }
         case(komega_visc):
        {
            val= new cKomega();
            break;
        }
         case(komegalowre_visc):
        {
            val= new cKomegaLowRe();
            break;
        }
         default:
        {
            val= new cVisc();
            break;
        }
     }
      return val;
  }
