using namespace std;

#  include <field/visc.h>
   cLaminar::cLaminar(){};
   void cLaminar::setvrs( Int Nx, Int Nvel, Int *Nv, Int *Naux, Int *Nauxf, Int *Nlhs )
  {
      nx= Nx;
      nvel= Nvel;

      nv= *Nv;
      nauxf= *Nauxf;
      nlhs= *Nlhs;
      naux= *Naux;
  };

   //void cLaminar::mwflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
   //                                       Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
   //                                                  Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cLaminar::mwflx( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,  
                                          Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr, 
                                                     Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr ) 
  {
      if( ice > ics )
     {
         //mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
         mwflx33( ics,ice, icql,sxl,sql,sauxl,srhsl,  icqr,sxr,sqr,sauxr,srhsr, swc,swxdc,sauxc,nql,nqr );
     }
  }

   void cLaminar::mwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                          cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                          cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( ice > ics )
     {
         //mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
         mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
     }
  }

   //void cLaminar::mwflx33( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
   //                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
   //                                                    Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cLaminar::mwflx33( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,  
                                            Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr, 
                                                       Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,utau;
      Int             ia,ic,iql,iqr;
      Int             nql, nqr;

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          private(f,ut) \
          present(            sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
                  icqr[0:nfc],sxr[0:nx*nq], sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
                  swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
          default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic];

            //mu= auxr[naux-2][iqr];
            //rho= auxr[0][iqr];
            mu= sauxr[ADDR(naux-2,iqr,nqr)];
            rho= sauxr[ADDR(0,iqr,nqr)];

            //d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            //d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            //d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
            d=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxl[ADDR(0,iql,nql)] );
            d+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxl[ADDR(1,iql,nql)] );
            d+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxl[ADDR(2,iql,nql)] );
 
            //ut[0]= qr[0][iqr]- ql[0][iql];
            //ut[1]= qr[1][iqr]- ql[1][iql];
            //ut[2]= qr[2][iqr]- ql[2][iql];
            ut[0]= sqr[ADDR(0,iqr,nqr)]- sql[ADDR(0,iql,nql)];
            ut[1]= sqr[ADDR(1,iqr,nqr)]- sql[ADDR(1,iql,nql)];
            ut[2]= sqr[ADDR(2,iqr,nqr)]- sql[ADDR(2,iql,nql)];

            //un=  ut[0]*wc[0][ic];
            //un+= ut[1]*wc[1][ic];
            //un+= ut[2]*wc[2][ic];
            un=  ut[0]*swc[ADDR(0,ic,nfc)];
            un+= ut[1]*swc[ADDR(1,ic,nfc)];
            un+= ut[2]*swc[ADDR(2,ic,nfc)];

            //ut[0]-= wc[0][ic]*un;
            //ut[1]-= wc[1][ic]*un;
            //ut[2]-= wc[2][ic]*un;
            ut[0]-= swc[ADDR(0,ic,nfc)]*un;
            ut[1]-= swc[ADDR(1,ic,nfc)]*un;
            ut[2]-= swc[ADDR(2,ic,nfc)]*un;

            utau=  ut[0]*ut[0];
            utau+= ut[1]*ut[1];
            utau+= ut[2]*ut[2];
            utau=mu*sqrt(utau)/d;
            utau= sqrt(utau/rho);

            //f[1]= -mu*ut[0]/d*wc[3][ic];
            //f[2]= -mu*ut[1]/d*wc[3][ic];
            //f[3]= -mu*ut[2]/d*wc[3][ic];
            f[1]= -mu*ut[0]/d*swc[ADDR(3,ic,nfc)];
            f[2]= -mu*ut[1]/d*swc[ADDR(3,ic,nfc)];
            f[3]= -mu*ut[2]/d*swc[ADDR(3,ic,nfc)];

            //f[4]=  f[1]*ql[0][iql];
            //f[4]+= f[2]*ql[1][iql];
            //f[4]+= f[3]*ql[2][iql];
            f[4]=  f[1]*sql[ADDR(0,iql,nql)];
            f[4]+= f[2]*sql[ADDR(1,iql,nql)];
            f[4]+= f[3]*sql[ADDR(2,iql,nql)];

            //rhsl[1][iql]-= f[1];
            //rhsl[2][iql]-= f[2];
            //rhsl[3][iql]-= f[3];
            //rhsl[4][iql]-= f[4];
            #pragma acc atomic
            srhsl[ADDR_(1,iql,nql)]-= f[1];
            #pragma acc atomic
            srhsl[ADDR_(2,iql,nql)]-= f[2];
            #pragma acc atomic
            srhsl[ADDR_(3,iql,nql)]-= f[3];
            #pragma acc atomic
            srhsl[ADDR_(4,iql,nql)]-= f[4];

            //rhsr[1][iqr]+= f[1];
            //rhsr[2][iqr]+= f[2];
            //rhsr[3][iqr]+= f[3];
            //rhsr[4][iqr]+= f[4];
            #pragma acc atomic
            srhsr[ADDR_(1,iqr,nqr)]+= f[1];
            #pragma acc atomic
            srhsr[ADDR_(2,iqr,nqr)]+= f[2];
            #pragma acc atomic
            srhsr[ADDR_(3,iqr,nqr)]+= f[3];
            #pragma acc atomic
            srhsr[ADDR_(4,iqr,nqr)]+= f[4];

            //auxc[nauxf-1][ic]+= mu/(rho*d)*wc[3][ic];
            #pragma acc atomic
            sauxc[ADDR_((nauxf-1),ic,nfc)]+= mu/(rho*d)*swc[ADDR_(3,ic,nfc)];
        }
         #pragma acc exit data delete(this)
     }
  }

   void cLaminar::mwflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                            cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,utau;
      Int             ia,ic,iql,iqr;
      Int             nql, nqr;

      Int nq, nfc;
      Int *icql;
      Real *sxl, *sql, *sauxl, *srhsl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sxl   = xl.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();;

      icqr  = icqr_view.get_data();
      sxr   = xr.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      srhsr = rhsr.get_data();;

      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          private(f,ut) \
          present(            sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
                  icqr[0:nfc],sxr[0:nx*nq], sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
                  swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
          default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic];

            //mu= auxr[naux-2][iqr];
            //rho= auxr[0][iqr];
            mu= sauxr[ADDR(naux-2,iqr,nqr)];
            rho= sauxr[ADDR(0,iqr,nqr)];

            //d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            //d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            //d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
            d=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxl[ADDR(0,iql,nql)] );
            d+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxl[ADDR(1,iql,nql)] );
            d+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxl[ADDR(2,iql,nql)] );
 
            //ut[0]= qr[0][iqr]- ql[0][iql];
            //ut[1]= qr[1][iqr]- ql[1][iql];
            //ut[2]= qr[2][iqr]- ql[2][iql];
            ut[0]= sqr[ADDR(0,iqr,nqr)]- sql[ADDR(0,iql,nql)];
            ut[1]= sqr[ADDR(1,iqr,nqr)]- sql[ADDR(1,iql,nql)];
            ut[2]= sqr[ADDR(2,iqr,nqr)]- sql[ADDR(2,iql,nql)];

            //un=  ut[0]*wc[0][ic];
            //un+= ut[1]*wc[1][ic];
            //un+= ut[2]*wc[2][ic];
            un=  ut[0]*swc[ADDR(0,ic,nfc)];
            un+= ut[1]*swc[ADDR(1,ic,nfc)];
            un+= ut[2]*swc[ADDR(2,ic,nfc)];

            //ut[0]-= wc[0][ic]*un;
            //ut[1]-= wc[1][ic]*un;
            //ut[2]-= wc[2][ic]*un;
            ut[0]-= swc[ADDR(0,ic,nfc)]*un;
            ut[1]-= swc[ADDR(1,ic,nfc)]*un;
            ut[2]-= swc[ADDR(2,ic,nfc)]*un;

            utau=  ut[0]*ut[0];
            utau+= ut[1]*ut[1];
            utau+= ut[2]*ut[2];
            utau=mu*sqrt(utau)/d;
            utau= sqrt(utau/rho);

            //f[1]= -mu*ut[0]/d*wc[3][ic];
            //f[2]= -mu*ut[1]/d*wc[3][ic];
            //f[3]= -mu*ut[2]/d*wc[3][ic];
            f[1]= -mu*ut[0]/d*swc[ADDR(3,ic,nfc)];
            f[2]= -mu*ut[1]/d*swc[ADDR(3,ic,nfc)];
            f[3]= -mu*ut[2]/d*swc[ADDR(3,ic,nfc)];

            //f[4]=  f[1]*ql[0][iql];
            //f[4]+= f[2]*ql[1][iql];
            //f[4]+= f[3]*ql[2][iql];
            f[4]=  f[1]*sql[ADDR(0,iql,nql)];
            f[4]+= f[2]*sql[ADDR(1,iql,nql)];
            f[4]+= f[3]*sql[ADDR(2,iql,nql)];

            //rhsl[1][iql]-= f[1];
            //rhsl[2][iql]-= f[2];
            //rhsl[3][iql]-= f[3];
            //rhsl[4][iql]-= f[4];
            #pragma acc atomic
            srhsl[ADDR_(1,iql,nql)]-= f[1];
            #pragma acc atomic
            srhsl[ADDR_(2,iql,nql)]-= f[2];
            #pragma acc atomic
            srhsl[ADDR_(3,iql,nql)]-= f[3];
            #pragma acc atomic
            srhsl[ADDR_(4,iql,nql)]-= f[4];

            //rhsr[1][iqr]+= f[1];
            //rhsr[2][iqr]+= f[2];
            //rhsr[3][iqr]+= f[3];
            //rhsr[4][iqr]+= f[4];
            #pragma acc atomic
            srhsr[ADDR_(1,iqr,nqr)]+= f[1];
            #pragma acc atomic
            srhsr[ADDR_(2,iqr,nqr)]+= f[2];
            #pragma acc atomic
            srhsr[ADDR_(3,iqr,nqr)]+= f[3];
            #pragma acc atomic
            srhsr[ADDR_(4,iqr,nqr)]+= f[4];

            //auxc[nauxf-1][ic]+= mu/(rho*d)*wc[3][ic];
            #pragma acc atomic
            sauxc[ADDR_((nauxf-1),ic,nfc)]+= mu/(rho*d)*swc[ADDR_(3,ic,nfc)];
        }
         #pragma acc exit data delete(this)
     }
  }


   void cLaminar::dmwflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            dut[3],ut[3];
      Real            d,dun,un,rho,mu;
      Int             ia,ic,iql,iqr;
      if( ice > ics )
     {
         dmwflx33( ics,ice, icql,xl,ql,auxl,dql,dauxl,resl, icqr,xr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
     }
  }

   void cLaminar::dmwflx33( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                                        Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            dut[3],ut[3];
      Real            d,dun,un,rho,mu;
      Int             ia,ic,iql,iqr;
      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux-2][iqr];
            rho= auxr[0][iqr];
 
            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );

            dut[0]= dauxr[0][iqr];
            dut[1]= dauxr[1][iqr];
            dut[2]= dauxr[2][iqr];

            dun=  dut[0]*wc[0][ic];
            dun+= dut[1]*wc[1][ic];
            dun+= dut[2]*wc[2][ic];

            dut[0]-= wc[0][ic]*dun;
            dut[1]-= wc[1][ic]*dun;
            dut[2]-= wc[2][ic]*dun;

            f[1]= -mu*dut[0]/d*wc[3][ic];
            f[2]= -mu*dut[1]/d*wc[3][ic];
            f[3]= -mu*dut[2]/d*wc[3][ic];

            f[4]=  f[1]*ql[0][iql];
            f[4]+= f[2]*ql[1][iql];
            f[4]+= f[3]*ql[2][iql];

            resl[1][iql]-= f[1];
            resl[2][iql]-= f[2];
            resl[3][iql]-= f[3];
            resl[4][iql]-= f[4];

            resr[1][iqr]+= f[1];
            resr[2][iqr]+= f[2];
            resr[3][iqr]+= f[3];
            resr[4][iqr]+= f[4];

        }
     }
  }

   void cLaminar::dmwflx_z( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *zl_re[], Real *zl_im[], Real *rhsl_re[], Real *rhsl_im[],  
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *zr_re[], Real *zr_im[], Real *rhsr_re[], Real *rhsr_im[], 
                                             Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      if( ice > ics )
     {
         dmwflx33_z( ics,ice, icql,xl,ql,auxl,zl_re,zl_im,rhsl_re,rhsl_im,icqr,xr,qr,auxr,zr_re,zr_im,rhsr_re, rhsr_im,wc,wxdc,auxc );
     }
  }

   void cLaminar::dmwflx33_z( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *zl_re[], Real *zl_im[], Real *rhsl_re[], Real *rhsl_im[],  
                                               Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *zr_re[], Real *zr_im[], Real *rhsr_re[], Real *rhsr_im[], 
                                               Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3], ut_re[3], ut_im[3], un_re, un_im;
      Real            tw[MxNVs];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;
      Real            df_re[MxNVs], df_im[MxNVs];
      Real            drho, dt, dp, t, p;
      Real karm=0.41;
      Real b=5.1;
      Real rg= 287./10000.;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

// distance
            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );

// tangential velocity

            ut[0]= qr[0][iqr]-ql[0][iql];
            ut[1]= qr[1][iqr]-ql[1][iql];
            ut[2]= qr[2][iqr]-ql[2][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];
            un+= ut[2]*wc[2][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;
            ut[2]-= wc[2][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            utau= auxl[0][iql];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
           }
            else
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];

            f[4]=  f[1]*ql[0][iql];
            f[4]+= f[2]*ql[1][iql];
            f[4]+= f[3]*ql[2][iql];

//perturbation
            ut_re[0]= zr_re[0][iqr]-zl_re[0][iql];
            ut_re[1]= zr_re[1][iqr]-zl_re[1][iql];
            ut_re[2]= zr_re[2][iqr]-zl_re[2][iql];

            un_re=  ut_re[0]*wc[0][ic];
            un_re+= ut_re[1]*wc[1][ic];
            un_re+= ut_re[2]*wc[2][ic];

            ut_re[0]-= wc[0][ic]*un_re;
            ut_re[1]-= wc[1][ic]*un_re;
            ut_re[2]-= wc[2][ic]*un_re;

            ut_re[0]/= up;
            ut_re[1]/= up;
            ut_re[2]/= up;

            ut_im[0]= zr_im[0][iqr]-zl_im[0][iql];
            ut_im[1]= zr_im[1][iqr]-zl_im[1][iql];
            ut_im[2]= zr_im[2][iqr]-zl_im[2][iql];

            un_im=  ut_im[0]*wc[0][ic];
            un_im+= ut_im[1]*wc[1][ic];
            un_im+= ut_im[2]*wc[2][ic];

            ut_im[0]-= wc[0][ic]*un_im;
            ut_im[1]-= wc[1][ic]*un_im;
            ut_im[2]-= wc[2][ic]*un_im;

            ut_im[0]/= up;
            ut_im[1]/= up;
            ut_im[2]/= up;

            //utau is frozen
            t = qr[3][iqr];
            p = qr[4][iqr];

            dt = zr_re[3][iqr];
            dp = zr_re[4][iqr];
            drho = (1/rg)*( -dt*p/(t*t) + dp/t );
            //df_re[1]= -drho*ut[0]*fabs(ut[0])*wc[3][ic];
            //df_re[2]= -drho*ut[1]*fabs(ut[1])*wc[3][ic];
            //df_re[3]= -drho*ut[2]*fabs(ut[2])*wc[3][ic];
            df_re[1]= -(drho*ut[0]*fabs(ut[0]) + 2*rho*fabs(ut[0])*ut_re[0])*wc[3][ic];
            df_re[2]= -(drho*ut[1]*fabs(ut[1]) + 2*rho*fabs(ut[1])*ut_re[1])*wc[3][ic];
            df_re[3]= -(drho*ut[2]*fabs(ut[2]) + 2*rho*fabs(ut[2])*ut_re[2])*wc[3][ic];
            df_re[4]=  df_re[1]*ql[0][iql] + f[1]*zl_re[0][iql];
            df_re[4]+= df_re[2]*ql[1][iql] + f[2]*zl_re[1][iql];
            df_re[4]+= df_re[3]*ql[2][iql] + f[3]*zl_re[2][iql];


            rhsl_re[1][iql]-= df_re[1];
            rhsl_re[2][iql]-= df_re[2];
            rhsl_re[3][iql]-= df_re[3];
            rhsl_re[4][iql]-= df_re[4];
            rhsr_re[1][iqr]+= df_re[1];
            rhsr_re[2][iqr]+= df_re[2];
            rhsr_re[3][iqr]+= df_re[3];
            rhsr_re[4][iqr]+= df_re[4];

            dt = zr_im[3][iqr];
            dp = zr_im[4][iqr];
            drho = (1/rg)*( -dt*p/(t*t) + dp/t );
            //df_im[1]= -drho*ut[0]*fabs(ut[0])*wc[3][ic];
            //df_im[2]= -drho*ut[1]*fabs(ut[1])*wc[3][ic];
            //df_im[3]= -drho*ut[2]*fabs(ut[2])*wc[3][ic];
            df_im[1]= -(drho*ut[0]*fabs(ut[0]) + 2*rho*fabs(ut[0])*ut_im[0])*wc[3][ic];
            df_im[2]= -(drho*ut[1]*fabs(ut[1]) + 2*rho*fabs(ut[1])*ut_im[1])*wc[3][ic];
            df_im[3]= -(drho*ut[2]*fabs(ut[2]) + 2*rho*fabs(ut[2])*ut_im[2])*wc[3][ic];
            df_im[4]=  df_im[1]*ql[0][iql] + f[1]*zl_im[0][iql];
            df_im[4]+= df_im[2]*ql[1][iql] + f[2]*zl_im[1][iql];
            df_im[4]+= df_im[3]*ql[2][iql] + f[3]*zl_im[2][iql];
             

            rhsl_im[1][iql]-= df_im[1];
            rhsl_im[2][iql]-= df_im[2];
            rhsl_im[3][iql]-= df_im[3];
            rhsl_im[4][iql]-= df_im[4];
            rhsr_im[1][iqr]+= df_im[1];
            rhsr_im[2][iqr]+= df_im[2];
            rhsr_im[3][iqr]+= df_im[3];
            rhsr_im[4][iqr]+= df_im[4];
        }
     }
  }

