using namespace std;

#  include <field/visc.h>

   cKepsilonLowRe::cKepsilonLowRe()
  {
      karm=0.41;
      b=5.1;
      cmu=0.09;
      sigmak=1;
      sigmae=1.3;
      ce1=1.35;
      ce2=1.8;
  };
   void cKepsilonLowRe::setvrs( Int Nx, Int Nvel, Int *Nv, Int *Naux, Int *Nauxf, Int *Nlhs )
  {
      nx= Nx;
      nvel= Nvel;

     
      nv0=*(Nv);
    (*Nv)+=2;
      nv= *Nv;

      nauxf0=*Nauxf;
    (*Nauxf)+=2;
      nauxf= *Nauxf;

      nlhs0=*Nlhs;
    (*Nlhs)+=2;
      nlhs= *Nlhs;

      naux0=*Naux; 
    (*Naux)+= 10;
      naux=(*Naux);

      karm=0.41;
      b= 5.1;
  };

   void cKepsilonLowRe::maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
   //void cKepsilonLowRe::maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Int *iqb, Real *auxb[],Int *iqd, Real *xb[]  )
   {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,k,epsilon;
 //     Int        ig,iqb,iql;    MAURO
 //     Int        ig,iql;        MAURO
      Int        ib,id,iql,iq1; //MAURO
      Real       utau,d,yp;
      Real       yp1;  //MAURO
      Real       fmu,f1,f2;
      Real       rt,ry;
      Real       prode,epsw;
      Real       epsilon_bc;

      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            if(k<0)
            {
             cout<<"Negative k: "<<iq<<" "<<k<<endl;
            }
            epsilon= q[nv0+1][iq];
            if(epsilon<0)
            {
//            cout<<"Negative epsilon: "<<iq<<" "<<epsilon<<endl;
            }
///*
            d=dst[0][iq];
            mu= aux[naux0-2][iq];
            rho= aux[0][iq];
            if (aux[naux-4][iq]==0){     // MAURO ::: This affect only for the first iteration
                 yp = 1;}
            else{
                 utau=aux[naux-4][iq];
                 utau=fabs(utau);
                 yp=rho*d*utau/mu;
           //      aux[naux-4][iq]= yp;
                 }
            ry=rho*sqrt(k)*d/mu;
            rt=rho*k*k/(mu*fmax(epsilon,1e-16));
            rt=fmax(rt,1);//
            rt=fmax(rt,0.1*karm/cmu*ry);//MAURO Fix END
           // yp=rho*d*utau/mu;//*/
     //       cout << "iq , yp= "<< iq << " "  << yp << "\n"; // MAURO debug
            fmu=1.0-exp(-0.0115*yp);
            f1=1.0;
            f2=1.0-0.22*exp(-(rt/6)*(rt/6));
            epsw=2*mu/rho*k/d/d;
            prode=-2*mu/rho*epsilon/d/d*exp(-0.5*yp);
            epsilon_bc=0.;
            //fmu=1.0;
            //f1=1.0;
            //f2=1.0;
            //epsw=0;
            //prode=0;

            mut= cmu*fmu*rho*k*k/fmax(epsilon,1e-16);
       //     mut= fmin( mut,2000.*mu );   //MAURO
            mut= fmin( mut,8000.*mu );
            k= sqrt( epsilon*mut/( cmu*fmu*rho ) );    // MAURO : here blows upo because of fmu=0 because of yp wrong calculation
            q[nv0][iq]= k;

            kappat= cp*mut;   // unit turbulent Prandtl number
            aux[naux-3][iq]= 0.;
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;

            aux[naux-4][iq]=yp;
            aux[naux-5][iq]=fmu;
            aux[naux-6][iq]=f1;
            aux[naux-7][iq]=f2;
            aux[naux-8][iq]=epsw;
            aux[naux-9][iq]=prode;
            aux[naux-10][iq]=epsilon_bc;
            
        }
     }
  }

   void cKepsilonLowRe::mwflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;

      if( ice > ics )
     {
         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               mwflx22( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
           }
            else
           {
               if( nvel == 3 )
              {
                  mwflx23( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               if( nvel == 3 )
              {
                  mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
              }
           }
        }
     }
  }
   void cKepsilonLowRe::mwflx22( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                        Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

// distance
            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
 
// tangential velocity

            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            utau= auxl[0][iql];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11 )
           {
     //          cout << "WALL FUNCT";
             	up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
               auxr[naux-3][iqr]=-utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[2][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[2][ic];
            f[3]=  f[1]*ql[0][iql];
            f[3]+= f[2]*ql[1][iql];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[2][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;

        }
     }
  }

   void cKepsilonLowRe::mwflx23( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                        Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );

            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];
            ut[2]= qr[2][iqr]- ql[2][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            utau= auxl[0][iql];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
               auxr[naux-3][iqr]=-utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[2][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[2][ic];
            f[3]= -rho*ut[2]*fabs(ut[2])*wc[2][ic];

            f[4]=  f[1]*ql[0][iql];
            f[4]+= f[2]*ql[1][iql];
            f[4]+= f[3]*ql[2][iql];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];
            rhsl[4][iql]-= f[4];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];
            rhsr[4][iqr]+= f[4];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[2][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;

        }
     }
  }

   void cKepsilonLowRe::mwflx33( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                        Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

// distance
            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
 
// tangential velocity

            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];
            ut[2]= qr[2][iqr]- ql[2][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];
            un+= ut[2]*wc[2][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;
            ut[2]-= wc[2][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            utau= auxl[0][iql];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
               auxr[naux-3][iqr]=-utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];
            f[4]=  f[1]*ql[0][iql];
            f[4]+= f[2]*ql[1][iql];
            f[4]+= f[3]*ql[2][iql];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];
            rhsl[4][iql]-= f[4];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];
            rhsr[4][iqr]+= f[4];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[3][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;

        }
     }
  }

   void cKepsilonLowRe::srhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Int *idst[], Int *igdst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,epsilon,y,sqrtb,mu,mu0,s,k1;
      Real tau[3][3];
      if( iqe > iqs )
     {
         if( nx == 2 )
        {
            if( nvel == 2  )
           {
               srhs22( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
           }
            else
           {
               if( nvel == 3 )
              {
                  srhs23( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               srhs33( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
           }
        }
     }
  }

   void cKepsilonLowRe::srhs22( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,epsilon,y,sqrtb,mu,mu0,mut,s,k1;
      Real tau[3][3],div;
      Real f1,f2;
      Real epsilon_bc;
      Real prode,epsw;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            k1= k+1.e-16;
            epsilon= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(cmu);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut=mu-mu0;
            f1=aux[naux-6][iq];
            f2=aux[naux-7][iq];
            epsw=aux[naux-8][iq];
            prode=aux[naux-9][iq];
            epsilon_bc=aux[naux-10][iq];
            if( utau == 0 )
           {
               tau[0][0]= -mut*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               tau[1][0]= -mut*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 

               tau[0][1]=  tau[1][0];
               tau[1][1]= -mut*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 

               div=  2./3.*mut*dqdx[0][0][iq];
               div+= 2./3.*mut*dqdx[1][1][iq];

               tau[0][0]+= div;
               tau[1][1]+= div;

               tau[0][0]+= 2./3.*rho*k;
               tau[1][1]+= 2./3.*rho*k;

               s=  tau[0][0]*dqdx[0][0][iq];
               s+= tau[1][0]*dqdx[1][0][iq];
               s+= tau[0][1]*dqdx[0][1][iq];
               s+= tau[1][1]*dqdx[1][1][iq];

               rhs[nv0][iq]-= wq[0][iq]*( rho*(epsilon+epsw)+ s );
               k1= fmax( 0.01*mu0/rho,k/epsilon );
               //k1=k1/epsilon;
               rhs[nv0+1][iq]-= wq[0][iq]*( s*ce1*f1/k1+ ce2*f2*rho*epsilon/k1-rho*prode );
           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  //rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*utau/(sqrtb*karm*y)-epsilon)+ epsilon*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*utau/(karm*y)-epsilon)+ epsilon*rhs[0][iq];
              }
               else
              {
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k+k*rhs[0][iq];
                  rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(epsilon_bc-epsilon)+ epsilon*rhs[0][iq];
                  //rhs[nv0][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*a*y*y-k)+ k*rhs[0][iq];
                  //rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(epsilon_bc+c*pow(rho*utau*utau/mu,3)*y*y-epsilon)+ epsilon*rhs[0][iq];
              }
           }
        }
     }
  }

   void cKepsilonLowRe::srhs23( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,epsilon,y,sqrtb,mu,mu0,mut,s,k1;
      Real tau[3][3],div;
      Real f1,f2;
      Real epsilon_bc;
      Real prode,epsw;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            k1= k+1.e-16;
            epsilon= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(cmu);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut=mu-mu0;
            f1=aux[naux-6][iq];
            f2=aux[naux-7][iq];
            epsw=aux[naux-8][iq];
            prode=aux[naux-9][iq];
            epsilon_bc=aux[naux-10][iq];
            if( utau == 0 )
           {
// stress tensor
               tau[0][0]= -mut*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               tau[1][0]= -mut*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               tau[2][0]= -mut*(                 dqdx[2][0][iq] ); 

               tau[0][1]=  tau[1][0];
               tau[1][1]= -mut*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
               tau[2][1]= -mut*(                 dqdx[2][1][iq] ); 

               div=  2./3.*mut*dqdx[0][0][iq];
               div+= 2./3.*mut*dqdx[1][1][iq];

               tau[0][0]+= div;
               tau[1][1]+= div;

               tau[0][0]+= 2./3.*rho*k;
               tau[1][1]+= 2./3.*rho*k;

               s=  tau[0][0]*dqdx[0][0][iq];
               s+= tau[1][0]*dqdx[1][0][iq];
               s+= tau[2][0]*dqdx[2][0][iq];

               s+= tau[0][1]*dqdx[0][1][iq];
               s+= tau[1][1]*dqdx[1][1][iq];
               s+= tau[2][1]*dqdx[2][1][iq];

               rhs[nv0][iq]-= wq[0][iq]*( rho*epsilon+ s );
               k1= fmax( 0.01*mu0/rho,k/epsilon );
               //k1=k1/epsilon;
               rhs[nv0+1][iq]-= wq[0][iq]*( s*ce1*f1/k1+ ce2*f2*rho*epsilon/k1-rho*prode );
           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  //rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*utau/(sqrtb*karm*y)-epsilon)+ epsilon*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*utau/(karm*y)-epsilon)+ epsilon*rhs[0][iq];
              }
               else
              {
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k+k*rhs[0][iq];
                  rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(epsilon_bc-epsilon)+ epsilon*rhs[0][iq];
                  //rhs[nv0][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*a*y*y-k)+ k*rhs[0][iq];
                  //rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(epsilon_bc+c*pow(rho*utau*utau/mu,3)*y*y-epsilon)+ epsilon*rhs[0][iq];
              }
           }
        }
     }
  }

   void cKepsilonLowRe::srhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,epsilon,y,sqrtb,mu,mu0,mut,s,k1;
      Real tau[3][3],div;
      Real f1,f2;
      Real epsilon_bc;
      Real prode,epsw;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            k1= k+1.e-16;
            epsilon= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(cmu);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut=mu-mu0;
            f1=aux[naux-6][iq];
            f2=aux[naux-7][iq];
            epsw=aux[naux-8][iq];
            prode=aux[naux-9][iq];
            epsilon_bc=aux[naux-10][iq];
            if( utau == 0 )
           {

               tau[0][0]= -mut*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               tau[1][0]= -mut*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               tau[2][0]= -mut*( dqdx[0][2][iq]+ dqdx[2][0][iq] ); 

               tau[0][1]=  tau[1][0];
               tau[1][1]= -mut*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
               tau[2][1]= -mut*( dqdx[1][2][iq]+ dqdx[2][1][iq] ); 

               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               tau[2][2]= -mut*( dqdx[2][2][iq]+ dqdx[2][2][iq] ); 

               div=  2./3.*mut*dqdx[0][0][iq];
               div+= 2./3.*mut*dqdx[1][1][iq];
               div+= 2./3.*mut*dqdx[2][2][iq];

               tau[0][0]+= div;
               tau[1][1]+= div;
               tau[2][2]+= div;

               tau[0][0]+= 2./3.*rho*k;
               tau[1][1]+= 2./3.*rho*k;
               tau[2][2]+= 2./3.*rho*k;

               s=  tau[0][0]*dqdx[0][0][iq];
               s+= tau[1][0]*dqdx[1][0][iq];
               s+= tau[2][0]*dqdx[2][0][iq];

               s+= tau[0][1]*dqdx[0][1][iq];
               s+= tau[1][1]*dqdx[1][1][iq];
               s+= tau[2][1]*dqdx[2][1][iq];

               s+= tau[0][2]*dqdx[0][2][iq];
               s+= tau[1][2]*dqdx[1][2][iq];
               s+= tau[2][2]*dqdx[2][2][iq];
 
               rhs[nv0][iq]-= wq[0][iq]*( rho*epsilon+ s );
               k1= fmax( 0.01*mu0/rho,k/epsilon );
               //k1=k1/epsilon;
               rhs[nv0+1][iq]-= wq[0][iq]*( s*ce1*f1/k1+ ce2*f2*rho*epsilon/k1-rho*prode );
           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  //rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*utau/(sqrtb*karm*y)-epsilon)+ epsilon*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*utau/(karm*y)-epsilon)+ epsilon*rhs[0][iq];
              }
               else
              {
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k+ k*rhs[0][iq];
                  rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(epsilon_bc-epsilon)+ epsilon*rhs[0][iq];
                  //rhs[nv0][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*a*y*y-k)+ k*rhs[0][iq];
                  //rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(epsilon_bc+c*pow(rho*utau*utau/mu,3)*y*y-epsilon)+ epsilon*rhs[0][iq];
              }
           }
        }
     }
  }

   void cKepsilonLowRe::dsrhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
                        Real *wq[], Real *res[], Real *lhs[] )
  {
      Int iq;
      Real utau,rho,k,epsilon,y,sqrtb,mu,drho,mu0;
      Real dk,depsilon;
      Real f2;
      Real k1;
      Real yp;
      //Real rt,df2;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            mu0= aux[naux0-2][iq];
            rho= aux[0][iq];
            drho= dq[0][iq];
            k= q[nv0][iq];
            k1= k+1.e-16;
            epsilon= q[nv0+1][iq];
            dk= daux[nv0][iq];
            depsilon= daux[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(cmu);
            mu= aux[naux0-2][iq];
            f2=aux[naux-7][iq];
            yp=aux[naux-4][iq];
            k1= fmax( 0.01*mu/rho,k/epsilon );
            //rt=rho*k*k/(mu*epsilon);
            //df2=-2*rt*rt*exp(-rt*rt)*depsilon/epsilon;
            if( utau == 0 )
           {
               res[nv0][iq]-= wq[0][iq]*(1/k1+2*mu/rho/y/y)*(rho*dk+drho*k);
               //res[nv0][iq]-= wq[0][iq]*(rho*depsilon+drho*epsilon);
               //res[nv0+1][iq]-= wq[0][iq]*( ce2*f2*dq[nv0+1][iq]/fmax(0.01*mu0/rho,k/epsilon) );
               res[nv0+1][iq]-= wq[0][iq]*(ce2*f2/k1+2*mu/rho/y/y*exp(-0.5*yp))*(rho*depsilon+drho*epsilon);
           }
            else
           {
               if( utau > 0 )
              {
                  res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*depsilon;
              }
               else
              {
                  res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*depsilon;
                  //res[nv0+1][iq]= 0.;
              }
           }
        }
     }
  }

   void cKepsilonLowRe::slhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[] )
  {
      Int iq;
      Real utau,rho,k,epsilon,y,sqrtb,mu;
      Real f2;
      Real yp;
      Real k1;
      //Real rt,df2;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            epsilon= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(cmu);
            mu= aux[naux0-2][iq];
            f2=aux[naux-7][iq];
            yp=aux[naux-4][iq];
            k1= fmax( 0.01*mu/rho,k/epsilon );
            //rt=rho*k*k/(mu*epsilon);
            //df2=2*rt*exp(-rt*rt)*(-rt/epsilon);
            if( utau == 0 )
           {
               lhs[nlhs0-1][iq]+= wq[0][iq]*(1/k1+2*mu/rho/y/y);
               lhs[nlhs0][iq]+= wq[0][iq]*(ce2*f2/k1+2*mu/rho/y/y*exp(-0.5*yp));
           }
            else
           {
               lhs[nlhs0-1][iq]= 0.;
               lhs[nlhs0][iq]= 0.;
           }
        }
     }
  }
   void cKepsilonLowRe::mflx( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                        Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               mflx22( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr, xc,wc,wxdc, auxc );
           }
            else
           {
               if( nvel == 3 )
              {
                  mflx23( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr, xc,wc,wxdc, auxc );
              }
           }
        }
         else 
        {
            if( nx == 3 )
           {
               mflx33( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr, xc,wc,wxdc, auxc );
           }
        }
     }
  }

   void cKepsilonLowRe::mflx22( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                      Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            ddqn[MxNVs]; //Second derivative
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;
      Real            utau;

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];

            dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];

            dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];

            dqnl[nv0+1]=  dqdxl[nv0+1][0][iql]*wc[0][ic];
            dqnl[nv0+1]+= dqdxl[nv0+1][1][iql]*wc[1][ic];

            dqnr[nv0+1]=  dqdxr[nv0+1][0][iqr]*wc[0][ic];
            dqnr[nv0+1]+= dqdxr[nv0+1][1][iqr]*wc[1][ic];

            dqn[nv0+1]= ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;
            q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];

// tangential gradients

            dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][0][iql]- wc[0][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][0][iqr]- wc[0][ic]*dqnr[nv0+1];
            dqt[nv0+1][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][1][iql]- wc[1][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][1][iqr]- wc[1][ic]*dqnr[nv0+1];
            dqt[nv0+1][1]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];

            dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic]+ dqt[nv0+1][0];
            dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic]+ dqt[nv0+1][1];

// second derivative
            ddqn[nv0]= (dqnr[nv0]-dqnl[nv0])/w;
            ddqn[nv0+1]= (dqnr[nv0+1]-dqnl[nv0+1])/w;

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            taun[nv0]= -(mu0+ mut/sigmak)*dqn[nv0];
            taun[nv0+1]= -(mu0+ mut/sigmae)*dqn[nv0+1];
// accumulate
            rhsr[nv0][iqr]+=   taun[nv0]*  wc[2][ic];
            rhsr[nv0+1][iqr]+= taun[nv0+1]*wc[2][ic];

            rhsl[nv0][iql]-=   taun[nv0]*  wc[2][ic];
            rhsl[nv0+1][iql]-= taun[nv0+1]*wc[2][ic];

        }
     }
  }

   void cKepsilonLowRe::mflx23( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                      Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            ddqn[MxNVs]; //Second derivative
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;
      Real            utau;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];

            dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];

            dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];

            dqnl[nv0+1]=  dqdxl[nv0+1][0][iql]*wc[0][ic];
            dqnl[nv0+1]+= dqdxl[nv0+1][1][iql]*wc[1][ic];

            dqnr[nv0+1]=  dqdxr[nv0+1][0][iqr]*wc[0][ic];
            dqnr[nv0+1]+= dqdxr[nv0+1][1][iqr]*wc[1][ic];

            dqn[nv0+1]= ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;
            q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];

// tangential gradients

            dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][0][iql]- wc[0][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][0][iqr]- wc[0][ic]*dqnr[nv0+1];
            dqt[nv0+1][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][1][iql]- wc[1][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][1][iqr]- wc[1][ic]*dqnr[nv0+1];
            dqt[nv0+1][1]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];

            dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic]+ dqt[nv0+1][0];
            dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic]+ dqt[nv0+1][1];

// second derivative
            ddqn[nv0]= (dqnr[nv0]-dqnl[nv0])/w;
            ddqn[nv0+1]= (dqnr[nv0+1]-dqnl[nv0+1])/w;

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            taun[nv0]= -(mu0+ mut/sigmak)*dqn[nv0];
            taun[nv0+1]= -(mu0+ mut/sigmae)*dqn[nv0+1];
// accumulate
            rhsr[nv0][iqr]+=   taun[nv0]*  wc[2][ic];
            rhsr[nv0+1][iqr]+= taun[nv0+1]*wc[2][ic];

            rhsl[nv0][iql]-=   taun[nv0]*  wc[2][ic];
            rhsl[nv0+1][iql]-= taun[nv0+1]*wc[2][ic];

        }
     }
  }

   void cKepsilonLowRe::mflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                      Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            ddqn[MxNVs]; //Second derivative
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;
      Real            utau;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];
            dqnl[nv0]+= dqdxl[nv0][2][iql]*wc[2][ic];

            dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];
            dqnr[nv0]+= dqdxr[nv0][2][iqr]*wc[2][ic];

            dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];

            dqnl[nv0+1]=  dqdxl[nv0+1][0][iql]*wc[0][ic];
            dqnl[nv0+1]+= dqdxl[nv0+1][1][iql]*wc[1][ic];
            dqnl[nv0+1]+= dqdxl[nv0+1][2][iql]*wc[2][ic];

            dqnr[nv0+1]=  dqdxr[nv0+1][0][iqr]*wc[0][ic];
            dqnr[nv0+1]+= dqdxr[nv0+1][1][iqr]*wc[1][ic];
            dqnr[nv0+1]+= dqdxr[nv0+1][2][iqr]*wc[2][ic];

            dqn[nv0+1]= ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;
            q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];

// tangential gradients

            dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][2][iql]- wc[2][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][2][iqr]- wc[2][ic]*dqnr[nv0];
            dqt[nv0][2]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][0][iql]- wc[0][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][0][iqr]- wc[0][ic]*dqnr[nv0+1];
            dqt[nv0+1][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][1][iql]- wc[1][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][1][iqr]- wc[1][ic]*dqnr[nv0+1];
            dqt[nv0+1][1]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][2][iql]- wc[2][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][2][iqr]- wc[2][ic]*dqnr[nv0+1];
            dqt[nv0+1][2]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];
            dqdx[nv0][2]= dqn[nv0]*wc[2][ic]+ dqt[nv0][2];

            dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic]+ dqt[nv0+1][0];
            dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic]+ dqt[nv0+1][1];
            dqdx[nv0+1][2]= dqn[nv0+1]*wc[2][ic]+ dqt[nv0+1][2];

// second derivative
            ddqn[nv0]= (dqnr[nv0]-dqnl[nv0])/w;
            ddqn[nv0+1]= (dqnr[nv0+1]-dqnl[nv0+1])/w;

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            taun[nv0]= -(mu0+ mut/sigmak)*dqn[nv0];
            taun[nv0+1]= -(mu0+ mut/sigmae)*dqn[nv0+1];
// accumulate
            rhsr[nv0][iqr]+=   taun[nv0]*  wc[3][ic];
            rhsr[nv0+1][iqr]+= taun[nv0+1]*wc[3][ic];

            rhsl[nv0][iql]-=   taun[nv0]*  wc[3][ic];
            rhsl[nv0+1][iql]-= taun[nv0+1]*wc[3][ic];

        }
     }
  }

   void cKepsilonLowRe::mwflxisoth( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;

      if( ice > ics )
     {
         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               mwflx22( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
           }
            else
           {
               if( nvel == 3 )
              {
                  mwflx23( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               if( nvel == 3 )
              {
                  mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
              }
           }
        }
     }
  }

   void cKepsilonLowRe::yplus( Int ist, Int ien, Int *iqd, Real *xq[], Real *q[], Real *aux[], Int *iqb, Real *qb[], Real *auxb[], Real *dst[] )
  {
      Int id,iq,ib;
      Real d,utau,mu,rho,yp;
      for( id=ist;id<ien;id++ )
     {

    	 iq= iqd[id]; // Non serve
         ib= iqb[id];
     //    cout << "ist, ien, iq, ib = " <<ist << ","<< ien << " "<< iq << " " << ib <<"\n";
         d= dst[0][iq];
         mu= aux[naux0-2][iq];
         rho= aux[0][iq];
         utau=auxb[0][ib];
         utau=fabs(utau);
         yp=rho*d*utau/mu;
         aux[naux-4][iq]= yp;
     }
  }
