using namespace std;

#  include <field/visc.h>

   void cKomegaLowRe::setvrs( Int Nx, Int Nvel, Int *Nv, Int *Naux, Int *Nauxf, Int *Nlhs )
  {
      nx= Nx;
      nvel= Nvel;

     
      nv0=*(Nv);
    (*Nv)+=2;
      nv= *Nv;

      nauxf0=*Nauxf;
    (*Nauxf)+=2;
      nauxf= *Nauxf;

      nlhs0=*Nlhs;
    (*Nlhs)+=2;
      nlhs= *Nlhs;

      naux0=*Naux; 
    (*Naux)+= 4;
      naux=(*Naux);

      karm=0.41;
      b= 5.1;
  };

   void cKomegaLowRe::maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,omega,k;
      Int        ig,iqb;
      Real       d,utau,yp;
    
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];

/*          d= dst[0][iq];
            ig=igdst[0][iq];
            iqb=idst[0][iq];
            utau=auxb[ig][0][iqb];
            utau=fabs(utau);
            yp=rho*d*utau/mu;*/
            yp= dst[1][iq];
            mut= rho*k/omega;
            //mut= fmin( mut,2000.*mu );
            //mut= fmin( mut,10000.*mu );
            mut= fmin( mut,10000.*mu );
            k= omega*mut/rho;
            q[nv0][iq]= k;
            kappat= cp*mut;   // unit turbulent Prandtl number
            aux[naux-4][iq]= yp;
            aux[naux-3][iq]= 0.;
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;
        }
     }
  }

   void cKomegaLowRe::maux( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq )
  {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,omega,k;
      Int        ig,iqb;
      Real       d,utau,yp;
    
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sxq[0:nx*nq],sq[0:nv*nq],sdst[0:2*nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //cp= aux[naux0-3][iq];
            //mu= aux[naux0-2][iq];
            //kappa= aux[naux0-1][iq];
            //rho= aux[0][iq];
            //k= q[nv0][iq];
            //omega= q[nv0+1][iq];
            cp   = saux[ADDR(naux0-3,iq,nq)];
            mu   = saux[ADDR(naux0-2,iq,nq)];
            kappa= saux[ADDR(naux0-1,iq,nq)];
            rho  = saux[ADDR(0,iq,nq)];
            k    = sq[ADDR(nv0,iq,nq)];
            omega= sq[ADDR(nv0+1,iq,nq)];

/*          d= dst[0][iq];
            ig=igdst[0][iq];
            iqb=idst[0][iq];
            utau=auxb[ig][0][iqb];
            utau=fabs(utau);
            yp=rho*d*utau/mu;*/
            //yp= dst[1][iq];
            yp= sdst[ADDR(1,iq,nq)];
            mut= rho*k/omega;
            //mut= fmin( mut,2000.*mu );
            //mut= fmin( mut,10000.*mu );
            mut= fmin( mut,10000.*mu );
            k= omega*mut/rho;
            //q[nv0][iq]= k;
            sq[ADDR(nv0,iq,nq)]= k;
            kappat= cp*mut;   // unit turbulent Prandtl number
            //aux[naux-4][iq]= yp;
            //aux[naux-3][iq]= 0.;
            //aux[naux-2][iq]= mu+ mut;
            //aux[naux-1][iq]= kappa+ kappat;
            saux[ADDR(naux-4,iq,nq)]= yp;
            saux[ADDR(naux-3,iq,nq)]= 0.;
            saux[ADDR(naux-2,iq,nq)]= mu+ mut;
            saux[ADDR(naux-1,iq,nq)]= kappa+ kappat;
        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomegaLowRe::maux( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux )
  {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,omega,k;
      Int        ig,iqb;
      Real       d,utau,yp;

      Int nq;
      Real *sxq, *sq, *sdst, *sdqdx, *saux;

      nq = xq.get_dim1();

      sxq   = xq.get_data();
      sq    = q.get_data();
      sdst  = dst.get_data();
      sdqdx = dqdx.get_data();
      saux  = aux.get_data();
    
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sxq[0:nx*nq],sq[0:nv*nq],sdst[0:2*nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //cp= aux[naux0-3][iq];
            //mu= aux[naux0-2][iq];
            //kappa= aux[naux0-1][iq];
            //rho= aux[0][iq];
            //k= q[nv0][iq];
            //omega= q[nv0+1][iq];
            cp   = saux[ADDR(naux0-3,iq,nq)];
            mu   = saux[ADDR(naux0-2,iq,nq)];
            kappa= saux[ADDR(naux0-1,iq,nq)];
            rho  = saux[ADDR(0,iq,nq)];
            k    = sq[ADDR(nv0,iq,nq)];
            omega= sq[ADDR(nv0+1,iq,nq)];

/*          d= dst[0][iq];
            ig=igdst[0][iq];
            iqb=idst[0][iq];
            utau=auxb[ig][0][iqb];
            utau=fabs(utau);
            yp=rho*d*utau/mu;*/
            //yp= dst[1][iq];
            yp= sdst[ADDR(1,iq,nq)];
            mut= rho*k/omega;
            //mut= fmin( mut,2000.*mu );
            //mut= fmin( mut,10000.*mu );
            mut= fmin( mut,10000.*mu );
            k= omega*mut/rho;
            //q[nv0][iq]= k;
            sq[ADDR(nv0,iq,nq)]= k;
            kappat= cp*mut;   // unit turbulent Prandtl number
            //aux[naux-4][iq]= yp;
            //aux[naux-3][iq]= 0.;
            //aux[naux-2][iq]= mu+ mut;
            //aux[naux-1][iq]= kappa+ kappat;
            saux[ADDR(naux-4,iq,nq)]= yp;
            saux[ADDR(naux-3,iq,nq)]= 0.;
            saux[ADDR(naux-2,iq,nq)]= mu+ mut;
            saux[ADDR(naux-1,iq,nq)]= kappa+ kappat;
        }
        #pragma acc exit data delete(this)
     }
  }

   //void cKomegaLowRe::srhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
   void cKomegaLowRe::srhs33( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *srhs, Real *slhs, Int nq )
  {
      Int iq,iv,ix;
      Real utau,rho,k,omega,y,sqrtb,mu,mu0,mut,s,div;
      Real tau[3][3],dqdx[MxNVs][3];
      Real yp;
      Real sign, st;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(tau,dqdx) \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
                 sdst[0:2*nq],swq[0:(nx+1)*nq],srhs[0:nv*nq],slhs[0:nlhs*nq],this )\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu0= aux[naux0-2][iq];
            //mu= aux[naux-2][iq];
            mu0= saux[ADDR(naux0-2,iq,nq)];
            mu= saux[ADDR(naux-2,iq,nq)];
            mut=mu-mu0;
            //yp= aux[naux-4][iq];
            yp= saux[ADDR(naux-4,iq,nq)];
            if( utau == 0 )
           {
// stress tensor
               dqdx[0][0] = sdqdx[ADDR(0,0,iq,nq)];
               dqdx[1][0] = sdqdx[ADDR(1,0,iq,nq)];
               dqdx[2][0] = sdqdx[ADDR(2,0,iq,nq)];
               dqdx[0][1] = sdqdx[ADDR(0,1,iq,nq)];
               dqdx[1][1] = sdqdx[ADDR(1,1,iq,nq)];
               dqdx[2][1] = sdqdx[ADDR(2,1,iq,nq)];
               dqdx[0][2] = sdqdx[ADDR(0,2,iq,nq)];
               dqdx[1][2] = sdqdx[ADDR(1,2,iq,nq)];
               dqdx[2][2] = sdqdx[ADDR(2,2,iq,nq)];

               //tau[0][0]= -mut*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               //tau[1][0]= -mut*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               //tau[2][0]= -mut*( dqdx[0][2][iq]+ dqdx[2][0][iq] ); 
               tau[0][0]= -mut*( dqdx[0][0]+ dqdx[0][0] ); 
               tau[1][0]= -mut*( dqdx[0][1]+ dqdx[1][0] ); 
               tau[2][0]= -mut*( dqdx[0][2]+ dqdx[2][0] ); 
               tau[0][1]=  tau[1][0];
               //tau[1][1]= -mut*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
               //tau[2][1]= -mut*( dqdx[1][2][iq]+ dqdx[2][1][iq] ); 
               tau[1][1]= -mut*( dqdx[1][1]+ dqdx[1][1] ); 
               tau[2][1]= -mut*( dqdx[1][2]+ dqdx[2][1] ); 
               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               //tau[2][2]= -mut*( dqdx[2][2][iq]+ dqdx[2][2][iq] ); 
               tau[2][2]= -mut*( dqdx[2][2]+ dqdx[2][2] ); 

               //div=  2./3.*mut*dqdx[0][0][iq];
               //div+= 2./3.*mut*dqdx[1][1][iq];
               //div+= 2./3.*mut*dqdx[2][2][iq];
               div=  2./3.*mut*dqdx[0][0];
               div+= 2./3.*mut*dqdx[1][1];
               div+= 2./3.*mut*dqdx[2][2];

               tau[0][0]+= div;
               tau[1][1]+= div;
               tau[2][2]+= div;

               //s=  tau[0][0]*dqdx[0][0][iq];
               //s+= tau[1][0]*dqdx[1][0][iq];
               //s+= tau[2][0]*dqdx[2][0][iq];
               //s+= tau[0][1]*dqdx[0][1][iq];
               //s+= tau[1][1]*dqdx[1][1][iq];
               //s+= tau[2][1]*dqdx[2][1][iq];
               //s+= tau[0][2]*dqdx[0][2][iq];
               //s+= tau[1][2]*dqdx[1][2][iq];
               //s+= tau[2][2]*dqdx[2][2][iq];
               s=  tau[0][0]*dqdx[0][0];
               s+= tau[1][0]*dqdx[1][0];
               s+= tau[2][0]*dqdx[2][0];
               s+= tau[0][1]*dqdx[0][1];
               s+= tau[1][1]*dqdx[1][1];
               s+= tau[2][1]*dqdx[2][1];
               s+= tau[0][2]*dqdx[0][2];
               s+= tau[1][2]*dqdx[1][2];
               s+= tau[2][2]*dqdx[2][2];

                    if(s>0.) sign= 1;
               else if(s<0.) sign=-1;
               else          sign= 0;
               st = min(fabs(s),(Real)20.*stbeta*rho*k*omega);
               st *= sign;

               //rhs[nv0][iq]-= wq[0][iq]*( stbeta*rho*k*omega+ st );
               //rhs[nv0+1][iq]-= wq[0][iq]*( beta*rho*fabs(omega)*omega+ s*alpha/fmax(0.01*mu0/rho,k/omega ) );
               srhs[ADDR(nv0,iq,nq)]-= swq[ADDR(0,iq,nq)]*( stbeta*rho*k*omega+ st );
               srhs[ADDR(nv0+1,iq,nq)]-= swq[ADDR(0,iq,nq)]*( beta*rho*fabs(omega)*omega+ s*alpha/fmax(0.01*mu0/rho,k/omega ) );
           }
            else
           {
               if( utau > 0 )
              {
                  //rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  //rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*rhs[0][iq];
                  srhs[ADDR(nv0,iq,nq)]= slhs[ADDR(nlhs-1,iq,nq)]* rho*(utau*utau/sqrtb- k)+ k*srhs[ADDR(0,iq,nq)];
                  srhs[ADDR(nv0+1,iq,nq)]=  slhs[ADDR(nlhs-1,iq,nq)]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*srhs[ADDR(0,iq,nq)];
              }
               else
              {
                  //rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  //rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(6*mu0/(rho*beta*y*y)-omega)+ omega*rhs[0][iq];
                  srhs[ADDR(nv0,iq,nq)]= - slhs[ADDR(nlhs-1,iq,nq)]*rho*k;
                  srhs[ADDR(nv0+1,iq,nq)]= slhs[ADDR(nlhs-1,iq,nq)]*rho*(6*mu0/(rho*beta*y*y)-omega)+ omega*srhs[ADDR(0,iq,nq)];
//                rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*(mu/(beta*y*y)-rho*omega);
              }
           }
           // if(yp<5)
           //{
           //    rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(6*mu0/(rho*beta*y*y)-omega)+ omega*rhs[0][iq];
           //}
        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomegaLowRe::srhs33( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx0, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& rhs, cAu3xView<Real>& lhs )
  {
      Int iq,iv,ix;
      Real utau,rho,k,omega,y,sqrtb,mu,mu0,mut,s,div;
      Real tau[3][3],dqdx[MxNVs][3];
      Real yp;
      Real sign, st;

      Int nq;
      Real *sq, *saux, *sdqdx, *sdst, *swq, *srhs, *slhs;

      nq = q.get_dim1();

      sq    = q.get_data();
      saux  = aux.get_data(); 
      sdqdx = dqdx0.get_data(); 
      sdst  = dst.get_data(); 
      swq   = wq.get_data();
      srhs  = rhs.get_data();
      slhs  = lhs.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(tau,dqdx) \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
                 sdst[0:2*nq],swq[0:(nx+1)*nq],srhs[0:nv*nq],slhs[0:nlhs*nq],this )\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu0= aux[naux0-2][iq];
            //mu= aux[naux-2][iq];
            mu0= saux[ADDR(naux0-2,iq,nq)];
            mu= saux[ADDR(naux-2,iq,nq)];
            mut=mu-mu0;
            //yp= aux[naux-4][iq];
            yp= saux[ADDR(naux-4,iq,nq)];
            if( utau == 0 )
           {
// stress tensor
               dqdx[0][0] = sdqdx[ADDR(0,0,iq,nq)];
               dqdx[1][0] = sdqdx[ADDR(1,0,iq,nq)];
               dqdx[2][0] = sdqdx[ADDR(2,0,iq,nq)];
               dqdx[0][1] = sdqdx[ADDR(0,1,iq,nq)];
               dqdx[1][1] = sdqdx[ADDR(1,1,iq,nq)];
               dqdx[2][1] = sdqdx[ADDR(2,1,iq,nq)];
               dqdx[0][2] = sdqdx[ADDR(0,2,iq,nq)];
               dqdx[1][2] = sdqdx[ADDR(1,2,iq,nq)];
               dqdx[2][2] = sdqdx[ADDR(2,2,iq,nq)];

               //tau[0][0]= -mut*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               //tau[1][0]= -mut*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               //tau[2][0]= -mut*( dqdx[0][2][iq]+ dqdx[2][0][iq] ); 
               tau[0][0]= -mut*( dqdx[0][0]+ dqdx[0][0] ); 
               tau[1][0]= -mut*( dqdx[0][1]+ dqdx[1][0] ); 
               tau[2][0]= -mut*( dqdx[0][2]+ dqdx[2][0] ); 
               tau[0][1]=  tau[1][0];
               //tau[1][1]= -mut*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
               //tau[2][1]= -mut*( dqdx[1][2][iq]+ dqdx[2][1][iq] ); 
               tau[1][1]= -mut*( dqdx[1][1]+ dqdx[1][1] ); 
               tau[2][1]= -mut*( dqdx[1][2]+ dqdx[2][1] ); 
               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               //tau[2][2]= -mut*( dqdx[2][2][iq]+ dqdx[2][2][iq] ); 
               tau[2][2]= -mut*( dqdx[2][2]+ dqdx[2][2] ); 

               //div=  2./3.*mut*dqdx[0][0][iq];
               //div+= 2./3.*mut*dqdx[1][1][iq];
               //div+= 2./3.*mut*dqdx[2][2][iq];
               div=  2./3.*mut*dqdx[0][0];
               div+= 2./3.*mut*dqdx[1][1];
               div+= 2./3.*mut*dqdx[2][2];

               tau[0][0]+= div;
               tau[1][1]+= div;
               tau[2][2]+= div;

               //s=  tau[0][0]*dqdx[0][0][iq];
               //s+= tau[1][0]*dqdx[1][0][iq];
               //s+= tau[2][0]*dqdx[2][0][iq];
               //s+= tau[0][1]*dqdx[0][1][iq];
               //s+= tau[1][1]*dqdx[1][1][iq];
               //s+= tau[2][1]*dqdx[2][1][iq];
               //s+= tau[0][2]*dqdx[0][2][iq];
               //s+= tau[1][2]*dqdx[1][2][iq];
               //s+= tau[2][2]*dqdx[2][2][iq];
               s=  tau[0][0]*dqdx[0][0];
               s+= tau[1][0]*dqdx[1][0];
               s+= tau[2][0]*dqdx[2][0];
               s+= tau[0][1]*dqdx[0][1];
               s+= tau[1][1]*dqdx[1][1];
               s+= tau[2][1]*dqdx[2][1];
               s+= tau[0][2]*dqdx[0][2];
               s+= tau[1][2]*dqdx[1][2];
               s+= tau[2][2]*dqdx[2][2];

                    if(s>0.) sign= 1;
               else if(s<0.) sign=-1;
               else          sign= 0;
               st = min(fabs(s),(Real)20.*stbeta*rho*k*omega);
               st *= sign;

               //rhs[nv0][iq]-= wq[0][iq]*( stbeta*rho*k*omega+ st );
               //rhs[nv0+1][iq]-= wq[0][iq]*( beta*rho*fabs(omega)*omega+ s*alpha/fmax(0.01*mu0/rho,k/omega ) );
               srhs[ADDR(nv0,iq,nq)]-= swq[ADDR(0,iq,nq)]*( stbeta*rho*k*omega+ st );
               srhs[ADDR(nv0+1,iq,nq)]-= swq[ADDR(0,iq,nq)]*( beta*rho*fabs(omega)*omega+ s*alpha/fmax(0.01*mu0/rho,k/omega ) );
           }
            else
           {
               if( utau > 0 )
              {
                  //rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  //rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*rhs[0][iq];
                  srhs[ADDR(nv0,iq,nq)]= slhs[ADDR(nlhs-1,iq,nq)]* rho*(utau*utau/sqrtb- k)+ k*srhs[ADDR(0,iq,nq)];
                  srhs[ADDR(nv0+1,iq,nq)]=  slhs[ADDR(nlhs-1,iq,nq)]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*srhs[ADDR(0,iq,nq)];
              }
               else
              {
                  //rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  //rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(6*mu0/(rho*beta*y*y)-omega)+ omega*rhs[0][iq];
                  srhs[ADDR(nv0,iq,nq)]= - slhs[ADDR(nlhs-1,iq,nq)]*rho*k;
                  srhs[ADDR(nv0+1,iq,nq)]= slhs[ADDR(nlhs-1,iq,nq)]*rho*(6*mu0/(rho*beta*y*y)-omega)+ omega*srhs[ADDR(0,iq,nq)];
//                rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*(mu/(beta*y*y)-rho*omega);
              }
           }
           // if(yp<5)
           //{
           //    rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(6*mu0/(rho*beta*y*y)-omega)+ omega*rhs[0][iq];
           //}
        }
        #pragma acc exit data delete(this)
     }
  }

   //void cKomegaLowRe::dsrhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
   //                     Real *wq[], Real *res[], Real *lhs[] )
   void cKomegaLowRe::dsrhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sdqdx, Real *sdst, 
                        Real *swq, Real *sres, Real *slhs, Int nq )
  {
      Int iq;
      Real utau,rho,k,omega,y,sqrtb,mu,drho;
      Real dk,domega;
      Real yp;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //drho= dq[0][iq];
            drho= sdq[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //dk= daux[nv0][iq];
            dk= sdaux[ADDR(nv0,iq,nq)];
            //domega= daux[nv0+1][iq];
            domega= sdaux[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu= aux[naux0-2][iq];
            mu= saux[ADDR(naux0-2,iq,nq)];
            //yp= aux[naux-4][iq];
            yp= saux[ADDR(naux-4,iq,nq)];
            if( utau == 0 )
           {
               //res[nv0][iq]-= wq[0][iq]*stbeta*(rho*omega*dk+ drho*omega*k+ rho*domega*k );
               //res[nv0+1][iq]-= wq[0][iq]*beta*( 2*rho*omega*domega+ omega*omega*drho );
               sres[ADDR(nv0,iq,nq)]-= swq[ADDR(0,iq,nq)]*stbeta*(rho*omega*dk+ drho*omega*k+ rho*domega*k );
               sres[ADDR(nv0+1,iq,nq)]-= swq[ADDR(0,iq,nq)]*beta*( 2*rho*omega*domega+ omega*omega*drho );
           }
            else
           {
               if( utau > 0 )
              {
                  //res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  //res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*domega;
                  sres[ADDR(nv0,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*dk;
                  sres[ADDR(nv0+1,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*domega;
              }
               else
              {
                  //res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  //res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*domega;
                  sres[ADDR(nv0,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*dk;
                  sres[ADDR(nv0+1,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*domega;
              }
           }
           // if(yp<5)
           //{
           //    res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*domega;
           //}
        }
        #pragma acc exit data delete(this)
     }
  }

   void cKomegaLowRe::dsrhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                        cAu3xView<Real>& wq, cAu3xView<Real>& res, cAu3xView<Real>& lhs )
  {
      Int iq;
      Real utau,rho,k,omega,y,sqrtb,mu,drho;
      Real dk,domega;
      Real yp;

      Int nq;
      Real *sq, *saux, *sdq, *sdaux, *sdqdx, *sdst, *swq, *sres, *slhs;

      nq    = q.get_dim1(); 
      sq    = q.get_data();
      saux  = aux.get_data();
      sdq   = dq.get_data(); 
      sdaux = daux.get_data();
      sdqdx = dqdx.get_data();
      sdst  = dst.get_data();
      swq   = wq.get_data();
      sres  = res.get_data();
      slhs  = lhs.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //drho= dq[0][iq];
            drho= sdq[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //dk= daux[nv0][iq];
            dk= sdaux[ADDR(nv0,iq,nq)];
            //domega= daux[nv0+1][iq];
            domega= sdaux[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu= aux[naux0-2][iq];
            mu= saux[ADDR(naux0-2,iq,nq)];
            //yp= aux[naux-4][iq];
            yp= saux[ADDR(naux-4,iq,nq)];
            if( utau == 0 )
           {
               //res[nv0][iq]-= wq[0][iq]*stbeta*(rho*omega*dk+ drho*omega*k+ rho*domega*k );
               //res[nv0+1][iq]-= wq[0][iq]*beta*( 2*rho*omega*domega+ omega*omega*drho );
               sres[ADDR(nv0,iq,nq)]-= swq[ADDR(0,iq,nq)]*stbeta*(rho*omega*dk+ drho*omega*k+ rho*domega*k );
               sres[ADDR(nv0+1,iq,nq)]-= swq[ADDR(0,iq,nq)]*beta*( 2*rho*omega*domega+ omega*omega*drho );
           }
            else
           {
               if( utau > 0 )
              {
                  //res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  //res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*domega;
                  sres[ADDR(nv0,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*dk;
                  sres[ADDR(nv0+1,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*domega;
              }
               else
              {
                  //res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  //res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*domega;
                  sres[ADDR(nv0,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*dk;
                  sres[ADDR(nv0+1,iq,nq)]= -slhs[ADDR(nlhs-1,iq,nq)]*rho*domega;
              }
           }
           // if(yp<5)
           //{
           //    res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*domega;
           //}
        }
        #pragma acc exit data delete(this)
     }
  }

   //void cKomegaLowRe::slhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[] )
   void cKomegaLowRe::slhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *slhsa, Int nq )
  {
      Int iq;
      Real utau,rho,k,omega,y,sqrtb,mu;
      Real yp;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],slhsa[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu= aux[naux0-2][iq];
            //yp= aux[naux-4][iq];
            mu= saux[ADDR(naux0-2,iq,nq)];
            yp= saux[ADDR(naux-4,iq,nq)];
            if( utau == 0 )
           {
               //lhs[nlhs0-1][iq]+= wq[0][iq]*stbeta*rho*omega;
               //lhs[nlhs0][iq]+= 2*wq[0][iq]*beta*rho*omega;
               slhsa[ADDR(nlhs0-1,iq,nq)]+= swq[ADDR(0,iq,nq)]*stbeta*rho*omega;
               slhsa[ADDR(nlhs0,iq,nq)]+= 2*swq[ADDR(0,iq,nq)]*beta*rho*omega;
           }
            else
           {
               //lhs[nlhs0-1][iq]= 0.;
               //lhs[nlhs0][iq]= 0.;
               slhsa[ADDR(nlhs0-1,iq,nq)]= 0.;
               slhsa[ADDR(nlhs0,iq,nq)]= 0.;
           }
        }
        #pragma acc exit data delete(this)
        // if(yp<5)
        //{
        //    lhs[nlhs0][iq]= 0.;
        //}
     }
  }

   void cKomegaLowRe::slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa )
  {
      Int iq;
      Real utau,rho,k,omega,y,sqrtb,mu;
      Real yp;

      Int nq;
      Real *sq, *saux, *sdqdx, *sdst, *swq, *slhsa;

      nq = q.get_dim1();

      sq    = q.get_data();
      saux  = aux.get_data();
      sdqdx = dqdx.get_data();
      sdst  = dst.get_data();
      swq   = wq.get_data();
      slhsa = lhsa.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],slhsa[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //utau= aux[naux-3][iq];
            utau= saux[ADDR(naux-3,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //k= q[nv0][iq];
            k= sq[ADDR(nv0,iq,nq)];
            //omega= q[nv0+1][iq];
            omega= sq[ADDR(nv0+1,iq,nq)];
            //y= dst[0][iq];
            y= sdst[ADDR(0,iq,nq)];
            sqrtb= sqrt(stbeta);
            //mu= aux[naux0-2][iq];
            //yp= aux[naux-4][iq];
            mu= saux[ADDR(naux0-2,iq,nq)];
            yp= saux[ADDR(naux-4,iq,nq)];
            if( utau == 0 )
           {
               //lhs[nlhs0-1][iq]+= wq[0][iq]*stbeta*rho*omega;
               //lhs[nlhs0][iq]+= 2*wq[0][iq]*beta*rho*omega;
               slhsa[ADDR(nlhs0-1,iq,nq)]+= swq[ADDR(0,iq,nq)]*stbeta*rho*omega;
               slhsa[ADDR(nlhs0,iq,nq)]+= 2*swq[ADDR(0,iq,nq)]*beta*rho*omega;
           }
            else
           {
               //lhs[nlhs0-1][iq]= 0.;
               //lhs[nlhs0][iq]= 0.;
               slhsa[ADDR(nlhs0-1,iq,nq)]= 0.;
               slhsa[ADDR(nlhs0,iq,nq)]= 0.;
           }
        }
        #pragma acc exit data delete(this)
        // if(yp<5)
        //{
        //    lhs[nlhs0][iq]= 0.;
        //}
     }
  }
   //void cKomegaLowRe::yplus( Int ist, Int ien, Int *iqd, Real *xq[], Real *q[], Real *aux[], Int *iqb, Real *qb[], Real *auxb[], Real *dst[] )
   void cKomegaLowRe::yplus( Int ist, Int ien, Int *siqd, Real *sxq, Real *sq, Real *saux, Int *siqb, Real *sqb, Real *sauxb, Real *sdst, Int nbb, Int nq, Int ndst )
  {
      Int id,iq,ib;
      Real d,utau,mu,yp,rho;
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop \
       present(siqd[0:2*ndst],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],siqb[0:ndst],sqb[0:nv*nbb],sauxb[0:naux*nbb],sdst[0:2*nq],this)\
       default(none)

      for( id=ist;id<ien;id++ )
     {
         iq= siqd[id];
         ib= siqb[id];
         //d= dst[0][iq];
         d= sdst[ADDR(0,iq,nq)];
         //mu= aux[naux0-2][iq];
         mu= saux[ADDR(naux0-2,iq,nq)];
         //rho= aux[0][iq];
         rho= saux[ADDR(0,iq,nq)];
         //utau=auxb[0][ib];
         utau=sauxb[ADDR(0,ib,nbb)];
         utau=fabs(utau);
         yp=rho*d*utau/mu;
         //aux[naux-4][iq]= yp;
         saux[ADDR(naux-4,iq,nq)]= yp;
     }
      #pragma acc exit data delete(this)
  }

   void cKomegaLowRe::yplus( Int ist, Int ien, cAu3xView<Int>& iqd, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Int>& iqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& dst )
  {
      Int id,iq,ib;
      Real d,utau,mu,yp,rho;

      Int nbb, nq, ndst;
      Int *siqd;
      Real *sxq, *sq, *saux;
      Int *siqb;
      Real *sqb, *sauxb, *sdst;

      nbb = qb.get_dim1();
      nq = q.get_dim1();
      ndst = iqd.get_dim1();

      siqd  = iqd.get_data();
      sxq   = xq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      siqb  = iqb.get_data();
      sqb   = qb.get_data();
      sauxb = auxb.get_data();
      sdst  = dst.get_data();

      #pragma acc enter data copyin(this)
      #pragma acc parallel loop \
       present(siqd[0:2*ndst],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],siqb[0:ndst],sqb[0:nv*nbb],sauxb[0:naux*nbb],sdst[0:2*nq],this)\
       default(none)

      for( id=ist;id<ien;id++ )
     {
         iq= siqd[id];
         ib= siqb[id];
         //d= dst[0][iq];
         d= sdst[ADDR(0,iq,nq)];
         //mu= aux[naux0-2][iq];
         mu= saux[ADDR(naux0-2,iq,nq)];
         //rho= aux[0][iq];
         rho= saux[ADDR(0,iq,nq)];
         //utau=auxb[0][ib];
         utau=sauxb[ADDR(0,ib,nbb)];
         utau=fabs(utau);
         yp=rho*d*utau/mu;
         //aux[naux-4][iq]= yp;
         saux[ADDR(naux-4,iq,nq)]= yp;
     }
      #pragma acc exit data delete(this)
  }
