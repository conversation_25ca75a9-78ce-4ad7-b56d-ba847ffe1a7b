using namespace std;

#  include <field/visc.h>

   cCebeci::cCebeci(){};
   void cCebeci::setvrs( Int Nx, Int Nvel, Int *Nv, Int *Naux, Int *Nauxf, Int *Nlhs )
  {
      nx= Nx;
      nvel= Nvel;

      nv= *Nv;
      nv0=nv;
      nauxf= *Nauxf;
      nlhs= *Nlhs;
      naux0=*Naux; 
    (*Naux)+= 2;
      naux=(*Naux);
      karm=0.41;
      b= 5.1;
  };

   void cCebeci::maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       ds,s,l;
      Real       mu,rho,kappa,mut,kappat,cp;
      Real       lmax=0.1;
      //Real       lmax=0.0072447256138495853;
    
      if( iqe > iqs )
     {
         maux33( iqs,iqe, xq,q,dst,dqdx,aux, lmixmax );
     }
  }

   void cCebeci::maux( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq )
  {
      if( iqe > iqs )
     {
         maux33( iqs,iqe, sxq,sq,sdst,sdqdx,saux,lmixmax,nq );
     }
  }

   void cCebeci::maux( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux )
  {
      if( iqe > iqs )
     {
         maux33( iqs,iqe, xq,q,dst,dqdx,aux );
     }
  }

   void cCebeci::maux33( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )
  {
      Int        iq,ix,jx;
      Real       ds,s,l;
      Real       mu,rho,kappa,mut,kappat,cp;
      Real       lmax=0.1;
      //Real       lmax=0.0072447256138495853;
    
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            s=0;
            ds= dqdx[0][1][iq]+ dqdx[1][0][iq]; s+= ds*ds; 
            ds= dqdx[0][2][iq]+ dqdx[2][0][iq]; s+= ds*ds; 
            ds= dqdx[1][2][iq]+ dqdx[2][1][iq]; s+= ds*ds; 
            s= sqrt(s);
            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            l= fmin( dst[0][iq],lmixmax );
            l*= karm;
            mut= rho*s*l*l;
            kappat= cp*mut;   // unit turbulent Prandtl number
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;
        }
     }
  }

   void cCebeci::maux33( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq )
  {
      Int        iq,ix,jx;
      Real       ds,s,l;
      Real       mu,rho,kappa,mut,kappat,cp;
      Real       lmax=0.1;
      //Real       lmax=0.0072447256138495853;
    
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         firstprivate(lmax) \
         present(sxq[0:nx*nq],sq[0:nv*nq],sdst[0:2*nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            s=0;
            //ds= dqdx[0][1][iq]+ dqdx[1][0][iq]; s+= ds*ds; 
            //ds= dqdx[0][2][iq]+ dqdx[2][0][iq]; s+= ds*ds; 
            //ds= dqdx[1][2][iq]+ dqdx[2][1][iq]; s+= ds*ds; 
            ds= sdqdx[ADDR(0,1,iq,nq)]+ sdqdx[ADDR(1,0,iq,nq)]; s+= ds*ds; 
            ds= sdqdx[ADDR(0,2,iq,nq)]+ sdqdx[ADDR(2,0,iq,nq)]; s+= ds*ds; 
            ds= sdqdx[ADDR(1,2,iq,nq)]+ sdqdx[ADDR(2,1,iq,nq)]; s+= ds*ds; 
            s= sqrt(s);
            //cp= aux[naux0-3][iq];
            //mu= aux[naux0-2][iq];
            cp= saux[ADDR(naux0-3,iq,nq)];
            mu= saux[ADDR(naux0-2,iq,nq)];
            //kappa= aux[naux0-1][iq];
            kappa= saux[ADDR(naux0-1,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //l= fmin( dst[0][iq],lmixmax );
            l= fmin( sdst[ADDR(0,iq,nq)],lmixmax );
            l*= karm;
            mut= rho*s*l*l;
            kappat= cp*mut;   // unit turbulent Prandtl number
            //aux[naux-2][iq]= mu+ mut;
            //aux[naux-1][iq]= kappa+ kappat;
            saux[ADDR(naux-2,iq,nq)]= mu+ mut;
            saux[ADDR(naux-1,iq,nq)]= kappa+ kappat;
        }
        #pragma acc exit data delete(this)
     }
  }

   void cCebeci::maux33( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux )
  {
      Int        iq,ix,jx;
      Real       ds,s,l;
      Real       mu,rho,kappa,mut,kappat,cp;
      Real       lmax=0.1;
      //Real       lmax=0.0072447256138495853;
   
      Int nq;
      Real *sxq; Real *sq; Real *sdst; Real *sdqdx; Real *saux;

      nq = q.get_dim1();

      sxq   = xq.get_data();
      sq    = q.get_data();
      sdst  = dst.get_data();
      sdqdx = dqdx.get_data();
      saux  = aux.get_data();
 
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         firstprivate(lmax) \
         present(sxq[0:nx*nq],sq[0:nv*nq],sdst[0:2*nq],sdqdx[0:nx*nv*nq],saux[0:naux*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            s=0;
            //ds= dqdx[0][1][iq]+ dqdx[1][0][iq]; s+= ds*ds; 
            //ds= dqdx[0][2][iq]+ dqdx[2][0][iq]; s+= ds*ds; 
            //ds= dqdx[1][2][iq]+ dqdx[2][1][iq]; s+= ds*ds; 
            ds= sdqdx[ADDR(0,1,iq,nq)]+ sdqdx[ADDR(1,0,iq,nq)]; s+= ds*ds; 
            ds= sdqdx[ADDR(0,2,iq,nq)]+ sdqdx[ADDR(2,0,iq,nq)]; s+= ds*ds; 
            ds= sdqdx[ADDR(1,2,iq,nq)]+ sdqdx[ADDR(2,1,iq,nq)]; s+= ds*ds; 
            s= sqrt(s);
            //cp= aux[naux0-3][iq];
            //mu= aux[naux0-2][iq];
            cp= saux[ADDR(naux0-3,iq,nq)];
            mu= saux[ADDR(naux0-2,iq,nq)];
            //kappa= aux[naux0-1][iq];
            kappa= saux[ADDR(naux0-1,iq,nq)];
            //rho= aux[0][iq];
            rho= saux[ADDR(0,iq,nq)];
            //l= fmin( dst[0][iq],lmixmax );
            l= fmin( sdst[ADDR(0,iq,nq)],lmax );
            l*= karm;
            mut= rho*s*l*l;
            kappat= cp*mut;   // unit turbulent Prandtl number
            //aux[naux-2][iq]= mu+ mut;
            //aux[naux-1][iq]= kappa+ kappat;
            saux[ADDR(naux-2,iq,nq)]= mu+ mut;
            saux[ADDR(naux-1,iq,nq)]= kappa+ kappat;
        }
        #pragma acc exit data delete(this)
     }
  }

   //void cCebeci::mwflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
   //                                      Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
   //                                                 Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cCebeci::mwflx( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,  
                                         Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr, 
                                                    Real *swc, Real *swxdc, Real *sauxc,Int nql,Int nqr ) 
  {
      if( ice > ics )
     {
         //mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
         mwflx33( ics,ice, icql,sxl,sql,sauxl,srhsl,  icqr,sxr,sqr,sauxr,srhsr, swc,swxdc,sauxc,nql,nqr );
     }
  }

   void cCebeci::mwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,  
                                         cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                         cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( ice > ics )
     {
         //mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
         mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
     }
  }

   //void cCebeci::mwflx33( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
   //                                        Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
   //                                                   Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cCebeci::mwflx33( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,  
                                           Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr, 
                                                      Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            tw[MxNVs];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;
      Int             nfc;

      if( ice > ics )
     {
         nfc = nql;
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic];

            //mu= auxr[naux0-2][iqr];
            //mut= auxr[naux-2][iqr];
            //rho= auxr[0][iqr];
            mu= sauxr[ADDR(naux0-2,iqr,nqr)];
            mut= sauxr[ADDR(naux-2,iqr,nqr)];
            rho= sauxr[ADDR(0,iqr,nqr)];

// distance
            //d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            //d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            //d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
            d=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxl[ADDR(0,iql,nql)] );
            d+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxl[ADDR(1,iql,nql)] );
            d+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxl[ADDR(2,iql,nql)] );

// tangential velocity

            //ut[0]= qr[0][iqr]-ql[0][iql];
            //ut[1]= qr[1][iqr]-ql[1][iql];
            //ut[2]= qr[2][iqr]-ql[2][iql];
            ut[0]= sqr[ADDR(0,iqr,nqr)]-sql[ADDR(0,iql,nql)];
            ut[1]= sqr[ADDR(1,iqr,nqr)]-sql[ADDR(1,iql,nql)];
            ut[2]= sqr[ADDR(2,iqr,nqr)]-sql[ADDR(2,iql,nql)];

            //un=  ut[0]*wc[0][ic];
            //un+= ut[1]*wc[1][ic];
            //un+= ut[2]*wc[2][ic];
            un=  ut[0]*swc[ADDR(0,ic,nfc)];
            un+= ut[1]*swc[ADDR(1,ic,nfc)];
            un+= ut[2]*swc[ADDR(2,ic,nfc)];

            //ut[0]-= wc[0][ic]*un;
            //ut[1]-= wc[1][ic]*un;
            //ut[2]-= wc[2][ic]*un;
            ut[0]-= swc[ADDR(0,ic,nfc)]*un;
            ut[1]-= swc[ADDR(1,ic,nfc)]*un;
            ut[2]-= swc[ADDR(2,ic,nfc)]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];

            utg= sqrt(utg);
            utg= fmax(utg,umin);
            re= rho*utg*d/mu;
            re= log(re)+karm*b;
            //utau= auxl[0][iql];
            utau= sauxl[ADDR(0,iql,nql)];
            if( utau <= 0 ){ utau=utg/20; };
            up= utg/utau;
            yp= rho*utau*d/mu;
            for( it=0;it<5;it++ )
           {
               dup= up*(-karm*up-log(up)+re)/(up*karm+1);
               up+= 0.9*dup;
           }
            utau= utg/up;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            //f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            //f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            //f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];
            f[1]= -rho*ut[0]*fabs(ut[0])*swc[ADDR(3,ic,nfc)];
            f[2]= -rho*ut[1]*fabs(ut[1])*swc[ADDR(3,ic,nfc)];
            f[3]= -rho*ut[2]*fabs(ut[2])*swc[ADDR(3,ic,nfc)];

            //f[4]=  f[1]*ql[0][iql];
            //f[4]+= f[2]*ql[1][iql];
            //f[4]+= f[3]*ql[2][iql];
            f[4]=  f[1]*sql[ADDR(0,iql,nql)];
            f[4]+= f[2]*sql[ADDR(1,iql,nql)];
            f[4]+= f[3]*sql[ADDR(2,iql,nql)];

            //rhsl[1][iql]-= f[1];
            //rhsl[2][iql]-= f[2];
            //rhsl[3][iql]-= f[3];
            //rhsl[4][iql]-= f[4];
            srhsl[ADDR(1,iql,nql)]-= f[1];
            srhsl[ADDR(2,iql,nql)]-= f[2];
            srhsl[ADDR(3,iql,nql)]-= f[3];
            srhsl[ADDR(4,iql,nql)]-= f[4];

            //rhsr[1][iqr]+= f[1];
            //rhsr[2][iqr]+= f[2];
            //rhsr[3][iqr]+= f[3];
            //rhsr[4][iqr]+= f[4];
            srhsr[ADDR(1,iqr,nqr)]+= f[1];
            srhsr[ADDR(2,iqr,nqr)]+= f[2];
            srhsr[ADDR(3,iqr,nqr)]+= f[3];
            srhsr[ADDR(4,iqr,nqr)]+= f[4];

            //auxc[nauxf-1][ic]+= utau*wc[3][ic];
            //auxl[0][iql]= utau;
            //auxl[1][iql]= rho*utau/mu;
            sauxc[ADDR(nauxf-1,ic,nfc)]+= utau*swc[ADDR(3,ic,nfc)];
            sauxl[ADDR(0,iql,nql)]= utau;
            sauxl[ADDR(1,iql,nql)]= rho*utau/mu;
        }
     }
  }

   void cCebeci::mwflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                           cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                           cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      cout << "void cCebeci::mwflx33 not used anymore\n";
      assert(0);
//      Real            f[MxNVs];
//      Real            ut[3];
//      Real            tw[MxNVs];
//      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
//      Int             it,ia,ic,iql,iqr;
//      Real            umin=1e-10;
//      Int             nfc;
//
//      if( ice > ics )
//     {
//         nfc = nql;
//         for( ic=ics;ic<ice;ic++ )
//        {
//            iql= ic;
//            iqr= icqr[ic];
//
//            //mu= auxr[naux0-2][iqr];
//            //mut= auxr[naux-2][iqr];
//            //rho= auxr[0][iqr];
//            mu= sauxr[ADDR(naux0-2,iqr,nqr)];
//            mut= sauxr[ADDR(naux-2,iqr,nqr)];
//            rho= sauxr[ADDR(0,iqr,nqr)];
//
//// distance
//            //d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
//            //d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
//            //d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
//            d=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxl[ADDR(0,iql,nql)] );
//            d+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxl[ADDR(1,iql,nql)] );
//            d+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxl[ADDR(2,iql,nql)] );
//
//// tangential velocity
//
//            //ut[0]= qr[0][iqr]-ql[0][iql];
//            //ut[1]= qr[1][iqr]-ql[1][iql];
//            //ut[2]= qr[2][iqr]-ql[2][iql];
//            ut[0]= sqr[ADDR(0,iqr,nqr)]-sql[ADDR(0,iql,nql)];
//            ut[1]= sqr[ADDR(1,iqr,nqr)]-sql[ADDR(1,iql,nql)];
//            ut[2]= sqr[ADDR(2,iqr,nqr)]-sql[ADDR(2,iql,nql)];
//
//            //un=  ut[0]*wc[0][ic];
//            //un+= ut[1]*wc[1][ic];
//            //un+= ut[2]*wc[2][ic];
//            un=  ut[0]*swc[ADDR(0,ic,nfc)];
//            un+= ut[1]*swc[ADDR(1,ic,nfc)];
//            un+= ut[2]*swc[ADDR(2,ic,nfc)];
//
//            //ut[0]-= wc[0][ic]*un;
//            //ut[1]-= wc[1][ic]*un;
//            //ut[2]-= wc[2][ic]*un;
//            ut[0]-= swc[ADDR(0,ic,nfc)]*un;
//            ut[1]-= swc[ADDR(1,ic,nfc)]*un;
//            ut[2]-= swc[ADDR(2,ic,nfc)]*un;
//
//            utg=  ut[0]*ut[0];
//            utg+= ut[1]*ut[1];
//            utg+= ut[2]*ut[2];
//
//            utg= sqrt(utg);
//            utg= fmax(utg,umin);
//            re= rho*utg*d/mu;
//            re= log(re)+karm*b;
//            //utau= auxl[0][iql];
//            utau= sauxl[ADDR(0,iql,nql)];
//            if( utau <= 0 ){ utau=utg/20; };
//            up= utg/utau;
//            yp= rho*utau*d/mu;
//            for( it=0;it<5;it++ )
//           {
//               dup= up*(-karm*up-log(up)+re)/(up*karm+1);
//               up+= 0.9*dup;
//           }
//            utau= utg/up;
//
//            ut[0]= ut[0]/up;
//            ut[1]= ut[1]/up;
//            ut[2]= ut[2]/up;
//
//            //f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
//            //f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
//            //f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];
//            f[1]= -rho*ut[0]*fabs(ut[0])*swc[ADDR(3,ic,nfc)];
//            f[2]= -rho*ut[1]*fabs(ut[1])*swc[ADDR(3,ic,nfc)];
//            f[3]= -rho*ut[2]*fabs(ut[2])*swc[ADDR(3,ic,nfc)];
//
//            //f[4]=  f[1]*ql[0][iql];
//            //f[4]+= f[2]*ql[1][iql];
//            //f[4]+= f[3]*ql[2][iql];
//            f[4]=  f[1]*sql[ADDR(0,iql,nql)];
//            f[4]+= f[2]*sql[ADDR(1,iql,nql)];
//            f[4]+= f[3]*sql[ADDR(2,iql,nql)];
//
//            //rhsl[1][iql]-= f[1];
//            //rhsl[2][iql]-= f[2];
//            //rhsl[3][iql]-= f[3];
//            //rhsl[4][iql]-= f[4];
//            srhsl[ADDR(1,iql,nql)]-= f[1];
//            srhsl[ADDR(2,iql,nql)]-= f[2];
//            srhsl[ADDR(3,iql,nql)]-= f[3];
//            srhsl[ADDR(4,iql,nql)]-= f[4];
//
//            //rhsr[1][iqr]+= f[1];
//            //rhsr[2][iqr]+= f[2];
//            //rhsr[3][iqr]+= f[3];
//            //rhsr[4][iqr]+= f[4];
//            srhsr[ADDR(1,iqr,nqr)]+= f[1];
//            srhsr[ADDR(2,iqr,nqr)]+= f[2];
//            srhsr[ADDR(3,iqr,nqr)]+= f[3];
//            srhsr[ADDR(4,iqr,nqr)]+= f[4];
//
//            //auxc[nauxf-1][ic]+= utau*wc[3][ic];
//            //auxl[0][iql]= utau;
//            //auxl[1][iql]= rho*utau/mu;
//            sauxc[ADDR(nauxf-1,ic,nfc)]+= utau*swc[ADDR(3,ic,nfc)];
//            sauxl[ADDR(0,iql,nql)]= utau;
//            sauxl[ADDR(1,iql,nql)]= rho*utau/mu;
//        }
//     }
  }

   //void cCebeci::dmwflx_new( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
   //                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
   //                                           Real *wc[], Real *wxdc[], Real *auxc[] )
   void cCebeci::dmwflx_new( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                              Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                              Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      Real            f[MxNVs];
      Real            ut[3], dut[3], dun;
      Real            tw[MxNVs];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;
      Real            df[MxNVs];
      Real            drho, dt, dp, t, p;
      Real rg= 287./10000.;
      Int  nql,nqr;

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(f,ut,dut,tw,df) \
         present(            sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],sdql[0:nv*nfc],sdauxl[0:nv*nfc],sresl[0:nv*nfc],\
                 icqr[0:nfc],sxr[0:nx*nq], sqr[0:nv*nq], sauxr[0:naux*nq], sdqr[0:nv*nq], sdauxr[0:nv*nq], sresr[0:nv*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic];

            //mu= auxr[naux0-2][iqr];
            //rho= auxr[0][iqr];
            mu= sauxr[ADDR(naux0-2,iqr,nqr)];
            rho= sauxr[ADDR(0,iqr,nqr)];

// distance
            //d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            //d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            //d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
            d=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxl[ADDR(0,iql,nql)] );
            d+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxl[ADDR(1,iql,nql)] );
            d+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxl[ADDR(2,iql,nql)] );

// tangential velocity

            //ut[0]= qr[0][iqr]-ql[0][iql];
            //ut[1]= qr[1][iqr]-ql[1][iql];
            //ut[2]= qr[2][iqr]-ql[2][iql];
            ut[0]= sqr[ADDR(0,iqr,nqr)]-sql[ADDR(0,iql,nql)];
            ut[1]= sqr[ADDR(1,iqr,nqr)]-sql[ADDR(1,iql,nql)];
            ut[2]= sqr[ADDR(2,iqr,nqr)]-sql[ADDR(2,iql,nql)];

            //un=  ut[0]*wc[0][ic];
            //un+= ut[1]*wc[1][ic];
            //un+= ut[2]*wc[2][ic];
            un=  ut[0]*swc[ADDR(0,ic,nfc)];
            un+= ut[1]*swc[ADDR(1,ic,nfc)];
            un+= ut[2]*swc[ADDR(2,ic,nfc)];

            //ut[0]-= wc[0][ic]*un;
            //ut[1]-= wc[1][ic]*un;
            //ut[2]-= wc[2][ic]*un;
            ut[0]-= swc[ADDR(0,ic,nfc)]*un;
            ut[1]-= swc[ADDR(1,ic,nfc)]*un;
            ut[2]-= swc[ADDR(2,ic,nfc)]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            //utau= auxl[0][iql];
            utau= sauxl[ADDR(0,iql,nql)];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               #pragma acc loop seq
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
           }
            else
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            //f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            //f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            //f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];
            f[1]= -rho*ut[0]*fabs(ut[0])*swc[ADDR(3,ic,nfc)];
            f[2]= -rho*ut[1]*fabs(ut[1])*swc[ADDR(3,ic,nfc)];
            f[3]= -rho*ut[2]*fabs(ut[2])*swc[ADDR(3,ic,nfc)];

            //f[4]=  f[1]*ql[0][iql];
            //f[4]+= f[2]*ql[1][iql];
            //f[4]+= f[3]*ql[2][iql];
            f[4]=  f[1]*sql[ADDR(0,iql,nql)];
            f[4]+= f[2]*sql[ADDR(1,iql,nql)];
            f[4]+= f[3]*sql[ADDR(2,iql,nql)];

//perturbation
            //dut[0]= dauxr[0][iqr]-dauxl[0][iql];
            //dut[1]= dauxr[1][iqr]-dauxl[1][iql];
            //dut[2]= dauxr[2][iqr]-dauxl[2][iql];
            dut[0]= sdauxr[ADDR(0,iqr,nqr)]-sdauxl[ADDR(0,iql,nql)];
            dut[1]= sdauxr[ADDR(1,iqr,nqr)]-sdauxl[ADDR(1,iql,nql)];
            dut[2]= sdauxr[ADDR(2,iqr,nqr)]-sdauxl[ADDR(2,iql,nql)];

            //dun=  dut[0]*wc[0][ic];
            //dun+= dut[1]*wc[1][ic];
            //dun+= dut[2]*wc[2][ic];
            dun=  dut[0]*swc[ADDR(0,ic,nfc)];
            dun+= dut[1]*swc[ADDR(1,ic,nfc)];
            dun+= dut[2]*swc[ADDR(2,ic,nfc)];

            //dut[0]-= wc[0][ic]*dun;
            //dut[1]-= wc[1][ic]*dun;
            //dut[2]-= wc[2][ic]*dun;
            dut[0]-= swc[ADDR(0,ic,nfc)]*dun;
            dut[1]-= swc[ADDR(1,ic,nfc)]*dun;
            dut[2]-= swc[ADDR(2,ic,nfc)]*dun;

            dut[0]/= up;
            dut[1]/= up;
            dut[2]/= up;

            //u+ is frozen
            //t = qr[3][iqr];
            //p = qr[4][iqr];
            t = sqr[ADDR(3,iqr,nqr)];
            p = sqr[ADDR(4,iqr,nqr)];

            //dt = dauxr[3][iqr];
            //dp = dauxr[4][iqr];
            dt = sdauxr[ADDR(3,iqr,nqr)];
            dp = sdauxr[ADDR(4,iqr,nqr)];
            drho = (1/rg)*( -dt*p/(t*t) + dp/t );
            //df[1]= -(drho*ut[0]*fabs(ut[0]) + 2*rho*fabs(ut[0])*dut[0])*wc[3][ic];
            //df[2]= -(drho*ut[1]*fabs(ut[1]) + 2*rho*fabs(ut[1])*dut[1])*wc[3][ic];
            //df[3]= -(drho*ut[2]*fabs(ut[2]) + 2*rho*fabs(ut[2])*dut[2])*wc[3][ic];
            df[1]= -(drho*ut[0]*fabs(ut[0]) + 2*rho*fabs(ut[0])*dut[0])*swc[ADDR(3,ic,nfc)];
            df[2]= -(drho*ut[1]*fabs(ut[1]) + 2*rho*fabs(ut[1])*dut[1])*swc[ADDR(3,ic,nfc)];
            df[3]= -(drho*ut[2]*fabs(ut[2]) + 2*rho*fabs(ut[2])*dut[2])*swc[ADDR(3,ic,nfc)];
            //df[4]=  df[1]*ql[0][iql] + f[1]*dauxl[0][iql];
            //df[4]+= df[2]*ql[1][iql] + f[2]*dauxl[1][iql];
            //df[4]+= df[3]*ql[2][iql] + f[3]*dauxl[2][iql];
            df[4]=  df[1]*sql[ADDR(0,iql,nql)] + f[1]*sdauxl[ADDR(0,iql,nql)];
            df[4]+= df[2]*sql[ADDR(1,iql,nql)] + f[2]*sdauxl[ADDR(1,iql,nql)];
            df[4]+= df[3]*sql[ADDR(2,iql,nql)] + f[3]*sdauxl[ADDR(2,iql,nql)];

            #pragma acc atomic
            sresl[ADDR_(1,iql,nql)]-= df[1];
            #pragma acc atomic
            sresl[ADDR_(2,iql,nql)]-= df[2];
            #pragma acc atomic
            sresl[ADDR_(3,iql,nql)]-= df[3];
            #pragma acc atomic
            sresl[ADDR_(4,iql,nql)]-= df[4];

            #pragma acc atomic
            sresr[ADDR_(1,iqr,nqr)]+= df[1];
            #pragma acc atomic
            sresr[ADDR_(2,iqr,nqr)]+= df[2];
            #pragma acc atomic
            sresr[ADDR_(3,iqr,nqr)]+= df[3];
            #pragma acc atomic
            sresr[ADDR_(4,iqr,nqr)]+= df[4];

        }
        #pragma acc exit data delete(this)
      //cout << "dmwflx_new \n";
     }
  }

   void cCebeci::dmwflx_new( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                              cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            f[MxNVs];
      Real            ut[3], dut[3], dun;
      Real            tw[MxNVs];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;
      Real            df[MxNVs];
      Real            drho, dt, dp, t, p;
      Real rg= 287./10000.;
      Int  nql,nqr;

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sxl   = xl.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      sdql  = dql.get_data();
      sdauxl= dauxl.get_data();
      sresl = resl.get_data();

      icqr  = icqr_view.get_data();
      sxr   = xr.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      sdqr  = dqr.get_data();
      sdauxr= dauxr.get_data();
      sresr = resr.get_data();

      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(f,ut,dut,tw,df) \
         present(            sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],sdql[0:nv*nfc],sdauxl[0:nv*nfc],sresl[0:nv*nfc],\
                 icqr[0:nfc],sxr[0:nx*nq], sqr[0:nv*nq], sauxr[0:naux*nq], sdqr[0:nv*nq], sdauxr[0:nv*nq], sresr[0:nv*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic];

            //mu= auxr[naux0-2][iqr];
            //rho= auxr[0][iqr];
            mu= sauxr[ADDR(naux0-2,iqr,nqr)];
            rho= sauxr[ADDR(0,iqr,nqr)];

// distance
            //d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            //d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            //d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
            d=  swc[ADDR(0,ic,nfc)]*( sxr[ADDR(0,iqr,nqr)]- sxl[ADDR(0,iql,nql)] );
            d+= swc[ADDR(1,ic,nfc)]*( sxr[ADDR(1,iqr,nqr)]- sxl[ADDR(1,iql,nql)] );
            d+= swc[ADDR(2,ic,nfc)]*( sxr[ADDR(2,iqr,nqr)]- sxl[ADDR(2,iql,nql)] );

// tangential velocity

            //ut[0]= qr[0][iqr]-ql[0][iql];
            //ut[1]= qr[1][iqr]-ql[1][iql];
            //ut[2]= qr[2][iqr]-ql[2][iql];
            ut[0]= sqr[ADDR(0,iqr,nqr)]-sql[ADDR(0,iql,nql)];
            ut[1]= sqr[ADDR(1,iqr,nqr)]-sql[ADDR(1,iql,nql)];
            ut[2]= sqr[ADDR(2,iqr,nqr)]-sql[ADDR(2,iql,nql)];

            //un=  ut[0]*wc[0][ic];
            //un+= ut[1]*wc[1][ic];
            //un+= ut[2]*wc[2][ic];
            un=  ut[0]*swc[ADDR(0,ic,nfc)];
            un+= ut[1]*swc[ADDR(1,ic,nfc)];
            un+= ut[2]*swc[ADDR(2,ic,nfc)];

            //ut[0]-= wc[0][ic]*un;
            //ut[1]-= wc[1][ic]*un;
            //ut[2]-= wc[2][ic]*un;
            ut[0]-= swc[ADDR(0,ic,nfc)]*un;
            ut[1]-= swc[ADDR(1,ic,nfc)]*un;
            ut[2]-= swc[ADDR(2,ic,nfc)]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            //utau= auxl[0][iql];
            utau= sauxl[ADDR(0,iql,nql)];
            if( utau <= 0 )
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
           }
            yp= rho*utau*d/mu;
            if( yp >= 11. )
           {
               up= utg/utau;
               re= rho*utg*d/mu;
               re= log(re)+karm*b;
               #pragma acc loop seq
               for( it=0;it<10;it++ )
              {
                  dup= up*(-karm*up-log(up)+re)/(up*karm+1);
                  up+= 0.9*dup;
              }
               utau= utg/up;
           }
            else
           {
               utau= mu*utg/d;
               utau= sqrt(utau/rho );
               up= utg/utau;
           }
            yp= rho*utau*d/mu;

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            //f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            //f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            //f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];
            f[1]= -rho*ut[0]*fabs(ut[0])*swc[ADDR(3,ic,nfc)];
            f[2]= -rho*ut[1]*fabs(ut[1])*swc[ADDR(3,ic,nfc)];
            f[3]= -rho*ut[2]*fabs(ut[2])*swc[ADDR(3,ic,nfc)];

            //f[4]=  f[1]*ql[0][iql];
            //f[4]+= f[2]*ql[1][iql];
            //f[4]+= f[3]*ql[2][iql];
            f[4]=  f[1]*sql[ADDR(0,iql,nql)];
            f[4]+= f[2]*sql[ADDR(1,iql,nql)];
            f[4]+= f[3]*sql[ADDR(2,iql,nql)];

//perturbation
            //dut[0]= dauxr[0][iqr]-dauxl[0][iql];
            //dut[1]= dauxr[1][iqr]-dauxl[1][iql];
            //dut[2]= dauxr[2][iqr]-dauxl[2][iql];
            dut[0]= sdauxr[ADDR(0,iqr,nqr)]-sdauxl[ADDR(0,iql,nql)];
            dut[1]= sdauxr[ADDR(1,iqr,nqr)]-sdauxl[ADDR(1,iql,nql)];
            dut[2]= sdauxr[ADDR(2,iqr,nqr)]-sdauxl[ADDR(2,iql,nql)];

            //dun=  dut[0]*wc[0][ic];
            //dun+= dut[1]*wc[1][ic];
            //dun+= dut[2]*wc[2][ic];
            dun=  dut[0]*swc[ADDR(0,ic,nfc)];
            dun+= dut[1]*swc[ADDR(1,ic,nfc)];
            dun+= dut[2]*swc[ADDR(2,ic,nfc)];

            //dut[0]-= wc[0][ic]*dun;
            //dut[1]-= wc[1][ic]*dun;
            //dut[2]-= wc[2][ic]*dun;
            dut[0]-= swc[ADDR(0,ic,nfc)]*dun;
            dut[1]-= swc[ADDR(1,ic,nfc)]*dun;
            dut[2]-= swc[ADDR(2,ic,nfc)]*dun;

            dut[0]/= up;
            dut[1]/= up;
            dut[2]/= up;

            //u+ is frozen
            //t = qr[3][iqr];
            //p = qr[4][iqr];
            t = sqr[ADDR(3,iqr,nqr)];
            p = sqr[ADDR(4,iqr,nqr)];

            //dt = dauxr[3][iqr];
            //dp = dauxr[4][iqr];
            dt = sdauxr[ADDR(3,iqr,nqr)];
            dp = sdauxr[ADDR(4,iqr,nqr)];
            drho = (1/rg)*( -dt*p/(t*t) + dp/t );
            //df[1]= -(drho*ut[0]*fabs(ut[0]) + 2*rho*fabs(ut[0])*dut[0])*wc[3][ic];
            //df[2]= -(drho*ut[1]*fabs(ut[1]) + 2*rho*fabs(ut[1])*dut[1])*wc[3][ic];
            //df[3]= -(drho*ut[2]*fabs(ut[2]) + 2*rho*fabs(ut[2])*dut[2])*wc[3][ic];
            df[1]= -(drho*ut[0]*fabs(ut[0]) + 2*rho*fabs(ut[0])*dut[0])*swc[ADDR(3,ic,nfc)];
            df[2]= -(drho*ut[1]*fabs(ut[1]) + 2*rho*fabs(ut[1])*dut[1])*swc[ADDR(3,ic,nfc)];
            df[3]= -(drho*ut[2]*fabs(ut[2]) + 2*rho*fabs(ut[2])*dut[2])*swc[ADDR(3,ic,nfc)];
            //df[4]=  df[1]*ql[0][iql] + f[1]*dauxl[0][iql];
            //df[4]+= df[2]*ql[1][iql] + f[2]*dauxl[1][iql];
            //df[4]+= df[3]*ql[2][iql] + f[3]*dauxl[2][iql];
            df[4]=  df[1]*sql[ADDR(0,iql,nql)] + f[1]*sdauxl[ADDR(0,iql,nql)];
            df[4]+= df[2]*sql[ADDR(1,iql,nql)] + f[2]*sdauxl[ADDR(1,iql,nql)];
            df[4]+= df[3]*sql[ADDR(2,iql,nql)] + f[3]*sdauxl[ADDR(2,iql,nql)];

            #pragma acc atomic
            sresl[ADDR_(1,iql,nql)]-= df[1];
            #pragma acc atomic
            sresl[ADDR_(2,iql,nql)]-= df[2];
            #pragma acc atomic
            sresl[ADDR_(3,iql,nql)]-= df[3];
            #pragma acc atomic
            sresl[ADDR_(4,iql,nql)]-= df[4];

            #pragma acc atomic
            sresr[ADDR_(1,iqr,nqr)]+= df[1];
            #pragma acc atomic
            sresr[ADDR_(2,iqr,nqr)]+= df[2];
            #pragma acc atomic
            sresr[ADDR_(3,iqr,nqr)]+= df[3];
            #pragma acc atomic
            sresr[ADDR_(4,iqr,nqr)]+= df[4];

        }
        #pragma acc exit data delete(this)
      //cout << "dmwflx_new \n";
     }
  }
