using namespace std;

#  include <field/visc.h>

   cKepsilon::cKepsilon()
  {
      karm=0.41;
      b=5.1;
      cmu=0.09;
      sigmak=1;
      sigmae=1.3;
      ce1=1.44;
      ce2=1.92;
  };
   void cKepsilon::setvrs( Int Nx, Int Nvel, Int *Nv, Int *Naux, Int *Nauxf, Int *Nlhs )
  {
      nx= Nx;
      nvel= Nvel;

     
      nv0=*(Nv);
    (*Nv)+=2;
      nv= *Nv;

      nauxf0=*Nauxf;
    (*Nauxf)+=2;
      nauxf= *Nauxf;

      nlhs0=*Nlhs;
    (*Nlhs)+=2;
      nlhs= *Nlhs;

      naux0=*Naux; 
    (*Naux)+= 3;
      naux=(*Naux);

      karm=0.41;
      b= 5.1;
  };
   void cKepsilon::nondim( Int iqs, Int iqe, Real *q[], Int *idone )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
         if( idone[nv0] != 1 )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[nv0][iq]=4;
           }
        }
         if( idone[nv0+1] != 1 )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[nv0+1][iq]= 4*44444.;
           }
        }
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[nv0][iq]/= 10000.;
            q[nv0+1][iq]/= 1000000.;
        }
     }
  }

   void cKepsilon::redim( Int iqs, Int iqe, Real *q[] )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[nv0][iq]*= 10000.;
            q[nv0+1][iq]*= 1000000.;
        }
     }
  }

   void cKepsilon::maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,k,epsilon;
    
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            epsilon= q[nv0+1][iq];
            mut= cmu*rho*k*k/epsilon;
            mut= fmin( mut,2000.*mu );      // MAURO    mut= fmin( mut,2000.*mu );
            //mut=mu;
            //cout << epsilon;
            k= sqrt( epsilon*mut/( cmu*rho ) );
            q[nv0][iq]= k;

            kappat= cp*mut;   // unit turbulent Prandtl number
            aux[naux-3][iq]= 0.;
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;
        }
     }
  }

   void cKepsilon::mwflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;

      if( ice > ics )
     {
         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               mwflx22( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
           }
            else
           {
               if( nvel == 3 )
              {
                  mwflx23( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               if( nvel == 3 )
              {
                  mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
              }
           }
        }
     }
  }
   void cKepsilon::mwflx22( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                        Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

// distance
            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
 
// tangential velocity

            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            re= rho*utg*d/mu;
            re= log(re)+karm*b;
            utau= auxl[0][iql];
            if( utau <= 0 ){ utau=utg/20; };
            up= utg/utau;
            yp= rho*utau*d/mu;
            for( it=0;it<10;it++ )
           {
               dup= up*(-karm*up-log(up)+re)/(up*karm+1);
               up+= 0.9*dup;
           }
            utau= utg/up;
            yp= rho*utau*d/mu;
            if( yp > 12 )
           {
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               auxr[naux-3][iqr]=-1;
           }

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[2][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[2][ic];
            f[3]=  f[1]*ql[0][iql];
            f[3]+= f[2]*ql[1][iql];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[2][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;
        }
     }
  }

   void cKepsilon::mwflx23( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                        Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;
      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );

            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];
            ut[2]= qr[2][iqr]- ql[2][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            re= rho*utg*d/mu;
            re= log(re)+karm*b;
            utau= auxl[0][iql];
            if( utau <= 0 ){ utau=utg/20; };
            up= utg/utau;
            yp= rho*utau*d/mu;
            for( it=0;it<10;it++ )
           {
               dup= up*(-karm*up-log(up)+re)/(up*karm+1);
               up+= 0.9*dup;
           }
            utau= utg/up;
            yp= rho*utau*d/mu;
            auxr[naux-3][iqr]= utau;
/*          if( yp > 12 )
           {
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               auxr[naux-3][iqr]=-1;
           }*/

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[2][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[2][ic];
            f[3]= -rho*ut[2]*fabs(ut[2])*wc[2][ic];

            f[4]=  f[1]*ql[0][iql];
            f[4]+= f[2]*ql[1][iql];
            f[4]+= f[3]*ql[2][iql];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];
            rhsl[4][iql]-= f[4];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];
            rhsr[4][iqr]+= f[4];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[2][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;
        }
     }
  }

   void cKepsilon::mwflx33( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                        Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;
      Real            umin=1e-10;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

            mu= auxr[naux0-2][iqr];
            mut= auxr[naux-2][iqr];
            rho= auxr[0][iqr];

// distance
            d=  wc[0][ic]*( xr[0][iqr]- xl[0][iql] );
            d+= wc[1][ic]*( xr[1][iqr]- xl[1][iql] );
            d+= wc[2][ic]*( xr[2][iqr]- xl[2][iql] );
 
// tangential velocity

            ut[0]= qr[0][iqr]- ql[0][iql];
            ut[1]= qr[1][iqr]- ql[1][iql];
            ut[2]= qr[2][iqr]- ql[2][iql];

            un=  ut[0]*wc[0][ic];
            un+= ut[1]*wc[1][ic];
            un+= ut[2]*wc[2][ic];

            ut[0]-= wc[0][ic]*un;
            ut[1]-= wc[1][ic]*un;
            ut[2]-= wc[2][ic]*un;

            utg=  ut[0]*ut[0];
            utg+= ut[1]*ut[1];
            utg+= ut[2]*ut[2];

            utg= sqrt(utg);
            utg=fmax(utg,umin);
            re= rho*utg*d/mu;
            re= log(re)+karm*b;
            utau= auxl[0][iql];
            if( utau <= 0 ){ utau=utg/20; };
            up= utg/utau;
            yp= rho*utau*d/mu;
            for( it=0;it<10;it++ )
           {
               dup= up*(-karm*up-log(up)+re)/(up*karm+1);
               up+= 0.9*dup;
           }
            utau= utg/up;
            yp= rho*utau*d/mu;
            if( yp > 12 )
           {
               auxr[naux-3][iqr]= utau;
           }
            else
           {
               auxr[naux-3][iqr]=-1;
           }

            ut[0]= ut[0]/up;
            ut[1]= ut[1]/up;
            ut[2]= ut[2]/up;

            f[1]= -rho*ut[0]*fabs(ut[0])*wc[3][ic];
            f[2]= -rho*ut[1]*fabs(ut[1])*wc[3][ic];
            f[3]= -rho*ut[2]*fabs(ut[2])*wc[3][ic];
            f[4]=  f[1]*ql[0][iql];
            f[4]+= f[2]*ql[1][iql];
            f[4]+= f[3]*ql[2][iql];

            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];
            rhsl[4][iql]-= f[4];

            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];
            rhsr[4][iqr]+= f[4];

            auxc[nauxf-1][ic]+= mut/(rho*d)*wc[3][ic];
            auxl[0][iql]= utau;
            auxl[1][iql]= rho*utau/mu;
        }
     }
  }
   void cKepsilon::vlhs( Int iqs, Int iqe, Real cfl, Real *wq[], Real *lhs[] )
  {
      Int             ia,ja,iq;
      Real            utau;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            lhs[nlhs0-1][iq]+= lhs[nlhs-1][iq]; 
            lhs[nlhs0][iq]+=   lhs[nlhs-1][iq]; 
        }
     }
  }

   void cKepsilon::invdg( Int iqs, Int iqe, Real *lhs[], Real *res[] )
  {
      Int iv,iq;
      if( iqe > iqs )
     {

         for( iq=iqs;iq<iqe;iq++ )
        {
            res[nv0][iq]/=   lhs[nlhs0-1][iq]; 
            res[nv0+1][iq]/= lhs[nlhs0][iq]; 
        }
     }
  }
   void cKepsilon::ilhs( Int ics, Int ice, Int *icql, Real *ql[], Real *auxl[], Real *lhsl[], 
                                      Int *icqr, Real *qr[], Real *auxr[], Real *lhsr[], 
                                                 Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Int             ia,ja,iql,iqr,ic;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        { 

            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };
       
            lhsl[nlhs0-1][iql]+= auxc[nauxf-1][ic];
            lhsl[nlhs0][iql]+=   auxc[nauxf-1][ic];
            lhsr[nlhs0-1][iqr]+= auxc[nauxf-1][ic];
            lhsr[nlhs0][iqr]+=   auxc[nauxf-1][ic];
        }
     }
  }

   void cKepsilon::srhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Int *idst[], Int *igdst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,epsilon,y,sqrtb,mu,mu0,s,k1;
      Real tau[3][3];
      if( iqe > iqs )
     {
         if( nx == 2 )
        {
            if( nvel == 2  )
           {
               srhs22( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
           }
            else
           {
               if( nvel == 3 )
              {
                  srhs23( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               srhs33( iqs,iqe, cfl, q,aux,dqdx,dst,wq,rhs,lhs );
           }
        }
     }
  }

   void cKepsilon::srhs22( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,epsilon,y,sqrtb,mu,mu0,s,k1;
      Real tau[3][3],div;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            k1= k+1.e-16;
            epsilon= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(cmu);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            if( utau == 0 )
           {
               tau[0][0]= -mu*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               tau[1][0]= -mu*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 

               tau[0][1]=  tau[1][0];
               tau[1][1]= -mu*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 

               div=  2./3.*mu*dqdx[0][0][iq];
               div+= 2./3.*mu*dqdx[1][1][iq];

               tau[0][0]+= div;
               tau[1][1]+= div;

               s=  tau[0][0]*dqdx[0][0][iq];
               s+= tau[1][0]*dqdx[1][0][iq];
               s+= tau[0][1]*dqdx[0][1][iq];
               s+= tau[1][1]*dqdx[1][1][iq];

               rhs[nv0][iq]-= wq[0][iq]*( rho*epsilon+ s );
               k1= fmax( 0.01*mu0/rho,k/epsilon );    
               rhs[nv0+1][iq]-= wq[0][iq]*( s*ce1/k1+ ce2*rho*epsilon/k1 );
           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*utau/(karm*y)-epsilon)+ epsilon*rhs[0][iq];
              }
               else
              {
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  rhs[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*epsilon;
              }
           }
        }
     }
  }

   void cKepsilon::srhs23( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,epsilon,y,sqrtb,mu,mu0,s,k1;
      Real tau[3][3],div;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            k1= k+1.e-16;
            epsilon= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(cmu);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            if( utau == 0 )
           {
// stress tensor
               tau[0][0]= -mu*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               tau[1][0]= -mu*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               tau[2][0]= -mu*(                 dqdx[2][0][iq] ); 

               tau[0][1]=  tau[1][0];
               tau[1][1]= -mu*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
               tau[2][1]= -mu*(                 dqdx[2][1][iq] ); 

               div=  2./3.*mu*dqdx[0][0][iq];
               div+= 2./3.*mu*dqdx[1][1][iq];

               tau[0][0]+= div;
               tau[1][1]+= div;

               s=  tau[0][0]*dqdx[0][0][iq];
               s+= tau[1][0]*dqdx[1][0][iq];
               s+= tau[2][0]*dqdx[2][0][iq];

               s+= tau[0][1]*dqdx[0][1][iq];
               s+= tau[1][1]*dqdx[1][1][iq];
               s+= tau[2][1]*dqdx[2][1][iq];

               rhs[nv0][iq]-= wq[0][iq]*( rho*epsilon+ s );
               k1= fmax( 0.01*mu0/rho,k/epsilon );
               rhs[nv0+1][iq]-= wq[0][iq]*( s*ce1/k1+ ce2*rho*epsilon/k1 );
           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*utau/(karm*y)-epsilon)+ epsilon*rhs[0][iq];
              }
               else
              {
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  rhs[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*epsilon;
              }
           }
        }
     }
  }

   void cKepsilon::srhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,epsilon,y,sqrtb,mu,mu0,s,k1;
      Real tau[3][3],div;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            k1= k+1.e-16;
            epsilon= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(cmu);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            if( utau == 0 )
           {

               tau[0][0]= -mu*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               tau[1][0]= -mu*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               tau[2][0]= -mu*( dqdx[0][2][iq]+ dqdx[2][0][iq] ); 

               tau[0][1]=  tau[1][0];
               tau[1][1]= -mu*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
               tau[2][1]= -mu*( dqdx[1][2][iq]+ dqdx[2][1][iq] ); 

               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               tau[2][2]= -mu*( dqdx[2][2][iq]+ dqdx[2][2][iq] ); 

               div=  2./3.*mu*dqdx[0][0][iq];
               div+= 2./3.*mu*dqdx[1][1][iq];
               div+= 2./3.*mu*dqdx[2][2][iq];

               tau[0][0]+= div;
               tau[1][1]+= div;
               tau[2][2]+= div;

               s=  tau[0][0]*dqdx[0][0][iq];
               s+= tau[1][0]*dqdx[1][0][iq];
               s+= tau[2][0]*dqdx[2][0][iq];

               s+= tau[0][1]*dqdx[0][1][iq];
               s+= tau[1][1]*dqdx[1][1][iq];
               s+= tau[2][1]*dqdx[2][1][iq];

               s+= tau[0][2]*dqdx[0][2][iq];
               s+= tau[1][2]*dqdx[1][2][iq];
               s+= tau[2][2]*dqdx[2][2][iq];
 
               rhs[nv0][iq]-= wq[0][iq]*( rho*epsilon+ s );
               k1= fmax( 0.01*mu0/rho,k/epsilon );
               rhs[nv0+1][iq]-= wq[0][iq]*( s*ce1/k1+ ce2*rho*epsilon/k1 );
           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau*utau*utau/(karm*y)-epsilon)+ epsilon*rhs[0][iq];
              }
               else
              {
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  rhs[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*epsilon;
              }
           }
        }
     }
  }

   void cKepsilon::dsrhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
                        Real *wq[], Real *res[], Real *lhs[] )
  {
      Int iq;
      Real utau,rho,k,epsilon,y,sqrtb,mu,drho,mu0;
      Real dk,depsilon;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            mu0= aux[naux0-2][iq];
            rho= aux[0][iq];
            drho= dq[0][iq];
            k= q[nv0][iq];
            epsilon= q[nv0+1][iq];
            dk= daux[nv0][iq];
            depsilon= daux[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(cmu);
            mu= aux[naux0-2][iq];
            if( utau == 0 )
           {
               res[nv0][iq]-= wq[0][iq]*dq[nv0+1][iq];
               res[nv0+1][iq]-= wq[0][iq]*( ce2*dq[nv0+1][iq]/fmax(0.01*mu0/rho,k/epsilon) );
           }
            else
           {
               if( utau > 0 )
              {
                  res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*depsilon;
              }
               else
              {
                  res[nv0][iq]= -lhs[nlhs-1][iq]*rho*dk;
                  res[nv0+1][iq]= -lhs[nlhs-1][iq]*rho*depsilon;
              }
           }
        }
     }
  }
   void cKepsilon::slhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[] )
  {
      Int iq;
      Real utau,rho,k,epsilon,y,sqrtb,mu;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            epsilon= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(cmu);
            mu= aux[naux0-2][iq];
            if( utau == 0 )
           {
//             lhs[nlhs0-1][iq]+= wq[0][iq]*stbeta*rho*epsilon;
               lhs[nlhs0][iq]+= 2*wq[0][iq]*rho*epsilon/(k+1.e-9);
           }
            else
           {
//             lhs[nlhs0-1][iq]= 0.;
//             lhs[nlhs0][iq]= 0.;
           }
        }
     }
  }
   void cKepsilon::mflx( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                        Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               mflx22( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr, xc,wc,wxdc, auxc );
           }
            else
           {
               if( nvel == 3 )
              {
                  mflx23( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr, xc,wc,wxdc, auxc );
              }
           }
        }
         else 
        {
            if( nx == 3 )
           {
               mflx33( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr, xc,wc,wxdc, auxc );
           }
        }
     }
  }

   void cKepsilon::mflx22( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                      Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];

            dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];

            dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];

            dqnl[nv0+1]=  dqdxl[nv0+1][0][iql]*wc[0][ic];
            dqnl[nv0+1]+= dqdxl[nv0+1][1][iql]*wc[1][ic];

            dqnr[nv0+1]=  dqdxr[nv0+1][0][iqr]*wc[0][ic];
            dqnr[nv0+1]+= dqdxr[nv0+1][1][iqr]*wc[1][ic];

            dqn[nv0+1]= ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;
            q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];

// tangential gradients

            dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][0][iql]- wc[0][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][0][iqr]- wc[0][ic]*dqnr[nv0+1];
            dqt[nv0+1][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][1][iql]- wc[1][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][1][iqr]- wc[1][ic]*dqnr[nv0+1];
            dqt[nv0+1][1]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];

            dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic]+ dqt[nv0+1][0];
            dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic]+ dqt[nv0+1][1];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            taun[nv0]= -(mu0+ mut/sigmak)*dqn[nv0];
            taun[nv0+1]= -(mu0+ mut/sigmae)*dqn[nv0+1];
// accumulate
            rhsr[nv0][iqr]+=   taun[nv0]*  wc[2][ic];
            rhsr[nv0+1][iqr]+= taun[nv0+1]*wc[2][ic];

            rhsl[nv0][iql]-=   taun[nv0]*  wc[2][ic];
            rhsl[nv0+1][iql]-= taun[nv0+1]*wc[2][ic];

        }
     }
  }
   void cKepsilon::mflx23( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                      Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];

            dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];

            dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];

            dqnl[nv0+1]=  dqdxl[nv0+1][0][iql]*wc[0][ic];
            dqnl[nv0+1]+= dqdxl[nv0+1][1][iql]*wc[1][ic];

            dqnr[nv0+1]=  dqdxr[nv0+1][0][iqr]*wc[0][ic];
            dqnr[nv0+1]+= dqdxr[nv0+1][1][iqr]*wc[1][ic];

            dqn[nv0+1]= ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;
            q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];

// tangential gradients

            dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][0][iql]- wc[0][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][0][iqr]- wc[0][ic]*dqnr[nv0+1];
            dqt[nv0+1][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][1][iql]- wc[1][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][1][iqr]- wc[1][ic]*dqnr[nv0+1];
            dqt[nv0+1][1]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];

            dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic]+ dqt[nv0+1][0];
            dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic]+ dqt[nv0+1][1];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            taun[nv0]= -(mu0+ mut/sigmak)*dqn[nv0];
            taun[nv0+1]= -(mu0+ mut/sigmae)*dqn[nv0+1];
// accumulate
            rhsr[nv0][iqr]+=   taun[nv0]*  wc[2][ic];
            rhsr[nv0+1][iqr]+= taun[nv0+1]*wc[2][ic];

            rhsl[nv0][iql]-=   taun[nv0]*  wc[2][ic];
            rhsl[nv0+1][iql]-= taun[nv0+1]*wc[2][ic];

        }
     }
  }

   void cKepsilon::mflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                      Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            dqnl[nv0]=  dqdxl[nv0][0][iql]*wc[0][ic];
            dqnl[nv0]+= dqdxl[nv0][1][iql]*wc[1][ic];
            dqnl[nv0]+= dqdxl[nv0][2][iql]*wc[2][ic];

            dqnr[nv0]=  dqdxr[nv0][0][iqr]*wc[0][ic];
            dqnr[nv0]+= dqdxr[nv0][1][iqr]*wc[1][ic];
            dqnr[nv0]+= dqdxr[nv0][2][iqr]*wc[2][ic];

            dqn[nv0]= ( qr[nv0][iqr]-ql[nv0][iql] )/w;
            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];

            dqnl[nv0+1]=  dqdxl[nv0+1][0][iql]*wc[0][ic];
            dqnl[nv0+1]+= dqdxl[nv0+1][1][iql]*wc[1][ic];
            dqnl[nv0+1]+= dqdxl[nv0+1][2][iql]*wc[2][ic];

            dqnr[nv0+1]=  dqdxr[nv0+1][0][iqr]*wc[0][ic];
            dqnr[nv0+1]+= dqdxr[nv0+1][1][iqr]*wc[1][ic];
            dqnr[nv0+1]+= dqdxr[nv0+1][2][iqr]*wc[2][ic];

            dqn[nv0+1]= ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;
            q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];

// tangential gradients

            dqtl= dqdxl[nv0][0][iql]- wc[0][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][0][iqr]- wc[0][ic]*dqnr[nv0];
            dqt[nv0][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][1][iql]- wc[1][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][1][iqr]- wc[1][ic]*dqnr[nv0];
            dqt[nv0][1]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0][2][iql]- wc[2][ic]*dqnl[nv0];
            dqtr= dqdxr[nv0][2][iqr]- wc[2][ic]*dqnr[nv0];
            dqt[nv0][2]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][0][iql]- wc[0][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][0][iqr]- wc[0][ic]*dqnr[nv0+1];
            dqt[nv0+1][0]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][1][iql]- wc[1][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][1][iqr]- wc[1][ic]*dqnr[nv0+1];
            dqt[nv0+1][1]= wl*dqtl+ wr*dqtr;

            dqtl= dqdxl[nv0+1][2][iql]- wc[2][ic]*dqnl[nv0+1];
            dqtr= dqdxr[nv0+1][2][iqr]- wc[2][ic]*dqnr[nv0+1];
            dqt[nv0+1][2]= wl*dqtl+ wr*dqtr;

// reconstruct gradient
            dqdx[nv0][0]= dqn[nv0]*wc[0][ic]+ dqt[nv0][0];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic]+ dqt[nv0][1];
            dqdx[nv0][2]= dqn[nv0]*wc[2][ic]+ dqt[nv0][2];

            dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic]+ dqt[nv0+1][0];
            dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic]+ dqt[nv0+1][1];
            dqdx[nv0+1][2]= dqn[nv0+1]*wc[2][ic]+ dqt[nv0+1][2];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            taun[nv0]= -(mu0+ mut/sigmak)*dqn[nv0];
            taun[nv0+1]= -(mu0+ mut/sigmae)*dqn[nv0+1];
// accumulate
            rhsr[nv0][iqr]+=   taun[nv0]*  wc[3][ic];
            rhsr[nv0+1][iqr]+= taun[nv0+1]*wc[3][ic];

            rhsl[nv0][iql]-=   taun[nv0]*  wc[3][ic];
            rhsl[nv0+1][iql]-= taun[nv0+1]*wc[3][ic];

        }
     }
  }

   void cKepsilon::dmflx( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa;

      if( ice > ics )
     { 
         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               dmflx22( ics,ice, icql,xl,ql,auxl,dql,dauxl,resl, icqr,xr,qr,auxr,dqr,dauxr,resr, xc,wc,wxdc,auxc );
           }
            else
           {
               if( nvel == 3 )
              {
                  dmflx23( ics,ice, icql,xl,ql,auxl,dql,dauxl,resl, icqr,xr,qr,auxr,dqr,dauxr,resr, xc,wc,wxdc,auxc );
              }
           }

        }
         else
        {
            if( nx == 3 )
           {
               dmflx33( ics,ice, icql,xl,ql,auxl,dql,dauxl,resl, icqr,xr,qr,auxr,dqr,dauxr,resr, xc,wc,wxdc,auxc );
           }
        }
     }
  }

   void cKepsilon::dmflx22( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                       Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div,ddiv;

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
           
            w= wl+wr;
            wl/= w; 
            wr/= w; 

            ddqn[nv0]= ( dauxr[nv0][iqr]-dauxl[nv0][iql] )/w;
            dqn[nv0]=  ( qr[nv0][iqr]-ql[nv0][iql] )/w;

            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];
            dq[nv0]= wl*dauxl[nv0][iql]+ wr*dauxr[nv0][iqr];

            ddqn[nv0+1]= ( dauxr[nv0+1][iqr]-dauxl[nv0+1][iql] )/w;
            dqn[nv0+1]=  ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;

            q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];
            dq[nv0+1]= wl*dauxl[nv0+1][iql]+ wr*dauxr[nv0+1][iqr];

            dqdx[nv0][0]= dqn[nv0]*wc[0][ic];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic];

            ddqdx[nv0][0]= ddqn[nv0]*wc[0][ic];
            ddqdx[nv0][1]= ddqn[nv0]*wc[1][ic];

            dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic];
            dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic];

            ddqdx[nv0+1][0]= ddqn[nv0+1]*wc[0][ic];
            ddqdx[nv0+1][1]= ddqn[nv0+1]*wc[1][ic];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

// diffusion for k and epsilon
            dtaun[nv0]= -(mu0+ mut/sigmak)*ddqn[nv0];
            dtaun[nv0+1]= -(mu0+ mut/sigmae)*ddqn[nv0+1];
           
// accumulate

            resr[nv0][iqr]+=   dtaun[nv0]*wc[2][ic];
            resr[nv0+1][iqr]+= dtaun[nv0+1]*wc[2][ic];

            resl[nv0][iql]-=   dtaun[nv0]*wc[2][ic];
            resl[nv0+1][iql]-= dtaun[nv0+1]*wc[2][ic];

        }
     }
  }

   void cKepsilon::dmflx23( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                       Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div,ddiv;

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
           
            w= wl+wr;
            wl/= w; 
            wr/= w; 

            ddqn[nv0]= ( dauxr[nv0][iqr]-dauxl[nv0][iql] )/w;
            dqn[nv0]=  ( qr[nv0][iqr]-ql[nv0][iql] )/w;

            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];
            dq[nv0]= wl*dauxl[nv0][iql]+ wr*dauxr[nv0][iqr];

            ddqn[nv0+1]= ( dauxr[nv0+1][iqr]-dauxl[nv0+1][iql] )/w;
            dqn[nv0+1]=  ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;

            q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];
            dq[nv0+1]= wl*dauxl[nv0+1][iql]+ wr*dauxr[nv0+1][iqr];

            dqdx[nv0][0]= dqn[nv0]*wc[0][ic];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic];

            ddqdx[nv0][0]= ddqn[nv0]*wc[0][ic];
            ddqdx[nv0][1]= ddqn[nv0]*wc[1][ic];

            dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic];
            dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic];

            ddqdx[nv0+1][0]= ddqn[nv0+1]*wc[0][ic];
            ddqdx[nv0+1][1]= ddqn[nv0+1]*wc[1][ic];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

// diffusion for k and epsilon
            dtaun[nv0]= -(mu0+ mut/sigmak)*ddqn[nv0];
            dtaun[nv0+1]= -(mu0+ mut/sigmae)*ddqn[nv0+1];
           
// accumulate

            resr[nv0][iqr]+=   dtaun[nv0]*wc[2][ic];
            resr[nv0+1][iqr]+= dtaun[nv0+1]*wc[2][ic];

            resl[nv0][iql]-=   dtaun[nv0]*wc[2][ic];
            resl[nv0+1][iql]-= dtaun[nv0+1]*wc[2][ic];

        }
     }
  }

   void cKepsilon::dmflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                       Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,mut,mu0,kappa,div,ddiv;

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
           
            w= wl+wr;
            wl/= w; 
            wr/= w; 

            ddqn[nv0]= ( dauxr[nv0][iqr]-dauxl[nv0][iql] )/w;
            dqn[nv0]=  ( qr[nv0][iqr]-ql[nv0][iql] )/w;

            q[nv0]= wl*ql[nv0][iql]+ wr*qr[nv0][iqr];
            dq[nv0]= wl*dauxl[nv0][iql]+ wr*dauxr[nv0][iqr];

            ddqn[nv0+1]= ( dauxr[nv0+1][iqr]-dauxl[nv0+1][iql] )/w;
            dqn[nv0+1]=  ( qr[nv0+1][iqr]-ql[nv0+1][iql] )/w;

            q[nv0+1]= wl*ql[nv0+1][iql]+ wr*qr[nv0+1][iqr];
            dq[nv0+1]= wl*dauxl[nv0+1][iql]+ wr*dauxr[nv0+1][iqr];

            dqdx[nv0][0]= dqn[nv0]*wc[0][ic];
            dqdx[nv0][1]= dqn[nv0]*wc[1][ic];
            dqdx[nv0][2]= dqn[nv0]*wc[2][ic];

            ddqdx[nv0][0]= ddqn[nv0]*wc[0][ic];
            ddqdx[nv0][1]= ddqn[nv0]*wc[1][ic];
            ddqdx[nv0][2]= ddqn[nv0]*wc[2][ic];

            dqdx[nv0+1][0]= dqn[nv0+1]*wc[0][ic];
            dqdx[nv0+1][1]= dqn[nv0+1]*wc[1][ic];
            dqdx[nv0+1][2]= dqn[nv0+1]*wc[2][ic];

            ddqdx[nv0+1][0]= ddqn[nv0+1]*wc[0][ic];
            ddqdx[nv0+1][1]= ddqn[nv0+1]*wc[1][ic];
            ddqdx[nv0+1][2]= ddqn[nv0+1]*wc[2][ic];

// stress tensor
            mu0= wl*auxl[naux0-2][iql]+ wr*auxr[naux0-2][iqr];
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            mut= mu-mu0;
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

// diffusion for k and epsilon
            dtaun[nv0]= -(mu0+ mut/sigmak)*ddqn[nv0];
            dtaun[nv0+1]= -(mu0+ mut/sigmae)*ddqn[nv0+1];
           
// accumulate

            resr[nv0][iqr]+=   dtaun[nv0]*wc[3][ic];
            resr[nv0+1][iqr]+= dtaun[nv0+1]*wc[3][ic];

            resl[nv0][iql]-=   dtaun[nv0]*wc[3][ic];
            resl[nv0+1][iql]-= dtaun[nv0+1]*wc[3][ic];

        }
     }
  }

   void cKepsilon::dvar( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dU[], Real *dq[] )
  {
      Int iq; 
      Real ro,dro;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            ro= aux[0][iq];
            dro= dU[0][iq];
            dq[nv0][iq]=   ( dU[nv0][iq]-   q[nv0][iq]*dro   )/ro;
            dq[nv0+1][iq]= ( dU[nv0+1][iq]- q[nv0+1][iq]*dro )/ro;
            dq[nv0][iq]=   fmin( fmax( dq[nv0  ][iq],-0.8*q[nv0  ][iq] ), 0.8*q[nv0][iq] );
            dq[nv0+1][iq]= fmin( fmax( dq[nv0+1][iq],-0.8*q[nv0+1][iq] ), 0.8*q[nv0+1][iq] );
        }
     } 
  }

   void cKepsilon::mwflxisoth( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                           Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            f[MxNVs];
      Real            ut[3];
      Real            d,un,rho,mu,up,utg,re,dup,utau,yp,mut;
      Int             it,ia,ic,iql,iqr;

      if( ice > ics )
     {
         if( nx == 2 )
        {
            if( nvel == 2 )
           {
               mwflx22( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
           }
            else
           {
               if( nvel == 3 )
              {
                  mwflx23( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
              }
           }
        }
         else
        {
            if( nx == 3 )
           {
               if( nvel == 3 )
              {
                  mwflx33( ics,ice, icql,xl,ql,auxl,rhsl,  icqr,xr,qr,auxr,rhsr, wc,wxdc,auxc );
              }
           }
        }
     }
  }
