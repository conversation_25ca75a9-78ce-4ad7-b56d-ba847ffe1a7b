using namespace std;

#  include <field/visc.h>

   cKomegaSST2003::cKomegaSST2003()
  {
      sigmak1=0.85;
      //sigmak1=0.5;
      sigmaw1=0.5;
      beta1=0.075;
      a1=0.31; //original
      //a1=0.31*4.;
      stbeta=0.09;
      karm=0.41;
      sigmak2=1.0;
      sigmaw2=0.856;
      beta2=0.0828;
      b=5.1;
      gam1=5./9.;
      gam2=0.44;

  };


   void cKomegaSST2003::maux22( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,omega,k;
      Real       sij[3][3];
      Real       s;
      Real       d;
      Real       r1,r2;
      Real       cdkw;
      Real       arg1;
      Real       f1;
      Real       arg2;
      Real       f2;
      Real       eps=1e-16;
    
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
// strain rate tensor

            sij[0][0]= 0.5*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
            sij[1][0]= 0.5*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
            sij[0][1]= sij[1][0];
            sij[1][1]= 0.5*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 

            s=  sij[0][0]*sij[0][0];
            s+= sij[1][0]*sij[1][0];
            s+= sij[0][1]*sij[0][1];
            s+= sij[1][1]*sij[1][1];
            s=  sqrt(2*s);



            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            d= dst[0][iq];

            q[nv0+1][iq] = fmax(q[nv0+1][iq], 1e-3);
            omega= q[nv0+1][iq];

            r1=sqrt(k)/(stbeta*omega*d);
            r2=500.*mu/(rho*d*d*omega);
            cdkw=  dqdx[nv0][0][iq]*dqdx[nv0+1][0][iq];
            cdkw+= dqdx[nv0][1][iq]*dqdx[nv0+1][1][iq];
            cdkw= 2.*rho*sigmaw2*cdkw/omega;
            //cdkw=0.0;
            arg1= max(r1,r2);
            arg1= min(arg1,4.*rho*sigmaw2*k/(max(cdkw,1e-10)*d*d));
            //arg1= min(arg1,4.*rho*sigmaw2*k/(max(cdkw,1.)*d*d));
            //arg1= min(arg1,4.*rho*sigmaw2*k/(max(cdkw,100.)*d*d));
            arg1 = fmin(arg1, 10.);
            f1= tanh(arg1*arg1*arg1*arg1); //tanh(10000) is 1
            //f1=1.0;
            arg2 = max(2.*r1,r2);
            arg2 = fmin(arg2, 100.);
            f2= tanh(arg2*arg2);

            //mut= rho*a1*k/max(a1*omega,vort*f2);
            //mut= rho*a1*k/max(a1*omega,s*f2);
            mut= rho*a1*k/max(a1*omega,s*f2);

            //mut= rho*a1*k/max(a1*omega,vort*f2);
//            mut= rho*a1*k/max(a1*omega,s*f2);
            //mut= fmin( mut,10000.*mu );

            //mut= rho*k/omega;

            //mut= fmin( mut,2000.*mu );
            //k= omega*mut/rho;
            //q[nv0][iq]= k;

            kappat= cp*mut;   // unit turbulent Prandtl number
            aux[naux-5][iq]= cdkw;
            aux[naux-4][iq]= f1;
            aux[naux-3][iq]= 0.;
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;
        }
     }
  }

   void cKomegaSST2003::maux23( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,omega,k;
      Real       sij[3][3];
      Real       s;
      Real       d;
      Real       r1,r2;
      Real       cdkw;
      Real       arg1;
      Real       f1;
      Real       arg2;
      Real       f2;
      Real       eps=1e-16;
    
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
// strain rate tensor
            sij[0][0]= 0.5*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
            sij[1][0]= 0.5*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
            sij[2][0]= 0.5*(                 dqdx[2][0][iq] ); 

            sij[0][1]= sij[1][0];
            sij[1][1]= 0.5*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
            sij[2][1]= 0.5*(                 dqdx[2][1][iq] ); 

            s=  sij[0][0]*sij[0][0];
            s+= sij[1][0]*sij[1][0];
            s+= sij[2][0]*sij[2][0];
            s+= sij[0][1]*sij[0][1];
            s+= sij[1][1]*sij[1][1];
            s+= sij[2][1]*sij[2][1];
            s=  sqrt(2*s);

            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            d= dst[0][iq];

            r1=sqrt(k)/(stbeta*omega*d);
            r2=500.*mu/(rho*d*d*omega);
            cdkw=  dqdx[nv0][0][iq]*dqdx[nv0+1][0][iq];
            cdkw+= dqdx[nv0][1][iq]*dqdx[nv0+1][1][iq];
            cdkw= 2.*rho*sigmaw2*cdkw/omega;
            arg1= max(r1,r2);
            arg1= min(arg1,4.*rho*sigmaw2*k/(max(cdkw,1e-10)*d*d));
            f1= tanh(arg1*arg1*arg1*arg1);
            arg1 = fmin(arg1, 10.);
            //f1= 1.0;
            arg2 = max(2.*r1,r2);
            arg2 = fmin(arg2, 100.);
            f2= tanh(arg2*arg2);

            mut= rho*a1*k/max(a1*omega,s*f2);
            //mut= rho*k/omega;

            //mut= fmin( mut,2000.*mu );
            //k= omega*mut/rho;
            //q[nv0][iq]= k;

            kappat= cp*mut;   // unit turbulent Prandtl number
            aux[naux-5][iq]= cdkw;
            aux[naux-4][iq]= f1;
            aux[naux-3][iq]= 0.;
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;
        }
     }
  }

   void cKomegaSST2003::maux33( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      Int        iq,ix,jx;
      Real       mu,rho,kappa,mut,kappat,cp,omega,k;
      Real       sij[3][3];
      Real       s;
      Real       d;
      Real       r1,r2;
      Real       cdkw;
      Real       arg1;
      Real       f1;
      Real       arg2;
      Real       f2;
      Real       eps=1e-16;
    
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
// strain rate tensor

            sij[0][0]= 0.5*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
            sij[1][0]= 0.5*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
            sij[2][0]= 0.5*( dqdx[0][2][iq]+ dqdx[2][0][iq] ); 

            sij[0][1]= sij[1][0];
            sij[1][1]= 0.5*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
            sij[2][1]= 0.5*( dqdx[1][2][iq]+ dqdx[2][1][iq] ); 

            sij[0][2]= sij[2][0];
            sij[1][2]= sij[2][1];
            sij[2][2]= 0.5*( dqdx[2][2][iq]+ dqdx[2][2][iq] ); 


            s=  sij[0][0]*sij[0][0];
            s+= sij[1][0]*sij[1][0];
            s+= sij[2][0]*sij[2][0];
            s+= sij[0][1]*sij[0][1];
            s+= sij[1][1]*sij[1][1];
            s+= sij[2][1]*sij[2][1];
            s+= sij[0][2]*sij[0][2];
            s+= sij[1][2]*sij[1][2];
            s+= sij[2][2]*sij[2][2];
            s=  sqrt(2*s);

            cp= aux[naux0-3][iq];
            mu= aux[naux0-2][iq];
            kappa= aux[naux0-1][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            d= dst[0][iq];

            q[nv0+1][iq] = fmax(q[nv0+1][iq], 1e-3);
            omega= q[nv0+1][iq];

            r1=sqrt(k)/(stbeta*omega*d);
            r2=500.*mu/(rho*d*d*omega);
            cdkw=  dqdx[nv0][0][iq]*dqdx[nv0+1][0][iq];
            cdkw+= dqdx[nv0][1][iq]*dqdx[nv0+1][1][iq];
            cdkw+= dqdx[nv0][2][iq]*dqdx[nv0+1][2][iq];
            cdkw= 2.*rho*sigmaw2*cdkw/omega;
            arg1= max(r1,r2);
            //arg1= min(arg1,4.*rho*sigmaw2*k/(max(cdkw,1e-20)*d*d));
            arg1= min(arg1,4.*rho*sigmaw2*k/(max(cdkw,1e-10)*d*d));
            arg1 = fmin(arg1, 10.);
            f1= tanh(arg1*arg1*arg1*arg1);
            //f1=1.0
            arg2 = max(2.*r1,r2);
            arg2 = fmin(arg2, 100.);
            f2= tanh(arg2*arg2);

            //mut= rho*a1*k/max(a1*omega,vort*f2);
            mut= rho*a1*k/max(a1*omega,s*f2);


            //mut= rho*a1*k/max(a1*omega,vort*f2);
//            mut= rho*a1*k/max(a1*omega,s*f2);
            //mut= rho*k/omega;
            //mut= fmin( mut,10000.*mu );


            //mut= fmin( mut,2000.*mu );
            //k= omega*mut/rho;
            //q[nv0][iq]= k;

            kappat= cp*mut;   // unit turbulent Prandtl number
            aux[naux-5][iq]= cdkw;
            aux[naux-4][iq]= f1;
            aux[naux-3][iq]= 0.;
            aux[naux-2][iq]= mu+ mut;
            aux[naux-1][iq]= kappa+ kappat;
        }
     }
  }

   void cKomegaSST2003::srhs22( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,omega,y,sqrtb,mu,mu0,mut,prodk,div;
      Real tau[3][3];
      Real sij[3][3];
      Real f1;
      Real gam,beta;
      Real cdkw;
      Real prodkt;
      Real cw=1.0;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(stbeta);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut=mu-mu0;
            f1= aux[naux-4][iq];
            gam=f1*gam1+(1.-f1)*gam2;
            beta=f1*beta1+(1.-f1)*beta2;
            cdkw= aux[naux-5][iq];
            
            if(  utau == 0 )
            //if( utau <= 0 )
           {
// strain rate tensor

               sij[0][0]= 0.5*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               sij[1][0]= 0.5*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               sij[0][1]= sij[1][0];
               sij[1][1]= 0.5*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
   
// stress tensor
               tau[0][0]= 2.*mut*sij[0][0]; 
               tau[1][0]= 2.*mut*sij[1][0]; 

               tau[0][1]=  tau[1][0];
               tau[1][1]= 2.*mut*sij[1][1]; 

               div=  2./3.*mut*dqdx[0][0][iq];
               div+= 2./3.*mut*dqdx[1][1][iq];

               tau[0][0]-= div;
               tau[1][1]-= div;

               tau[0][0]-= 2./3.*rho*k;
               tau[1][1]-= 2./3.*rho*k;

               prodk=  tau[0][0]*dqdx[0][0][iq];
               prodk+= tau[1][0]*dqdx[1][0][iq];
               prodk+= tau[0][1]*dqdx[0][1][iq];
               prodk+= tau[1][1]*dqdx[1][1][iq];

               prodkt= min(prodk,10.*stbeta*rho*k*omega);
               rhs[nv0][iq]+= wq[0][iq]*( prodkt-stbeta*rho*k*omega );
               //rhs[nv0+1][iq]+= wq[0][iq]*( gam*rho*s2-beta*rho*omega*omega+(1.-f1)*cdkw );
               //rhs[nv0+1][iq]+= wq[0][iq]*( gam*fmin(1./max(mut/rho,1e-16)*prodk, 10*stbeta*rho*omega*omega)-beta*rho*omega*omega+(1.-f1)*cdkw );
               rhs[nv0+1][iq]+= wq[0][iq]*( gam/max(mut/rho,1e-16)*prodkt-beta*rho*omega*omega+(1.-f1)*cdkw );
 
           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*rhs[0][iq];
              }
               else
              {
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(6*mu0/(rho*beta*y*y)-omega)+ omega*rhs[0][iq];
              }
           }
        }
     }
  }
   void cKomegaSST2003::srhs23( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,omega,y,sqrtb,mu,mu0,mut,prodk,div;
      Real tau[3][3];
      Real sij[3][3];
      Real f1;
      Real gam,beta;
      Real cdkw;
      Real prodkt;
      Real cw=1.0;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(stbeta);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut=mu-mu0;
            f1= aux[naux-4][iq];
            gam=f1*gam1+(1.-f1)*gam2;
            beta=f1*beta1+(1.-f1)*beta2;
            cdkw= aux[naux-5][iq];

            //if( utau == 0 )
            if( utau <= 0 )
           {
// strain rate tensor
               sij[0][0]= 0.5*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               sij[1][0]= 0.5*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               sij[2][0]= 0.5*(                 dqdx[2][0][iq] ); 

               sij[0][1]= sij[1][0];
               sij[1][1]= 0.5*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
               sij[2][1]= 0.5*(                 dqdx[2][1][iq] ); 

// stress tensor
               tau[0][0]= 2.0*mut*sij[0][0]; 
               tau[1][0]= 2.0*mut*sij[1][0]; 
               tau[2][0]= 2.0*mut*sij[2][0]; 

               tau[0][1]=  tau[1][0];
               tau[1][1]= 2.0*mut*sij[1][1]; 
               tau[2][1]= 2.0*mut*sij[2][1]; 

               div=  2./3.*mut*dqdx[0][0][iq];
               div+= 2./3.*mut*dqdx[1][1][iq];

               tau[0][0]-= div;
               tau[1][1]-= div;

               tau[0][0]-= 2./3.*rho*k;
               tau[1][1]-= 2./3.*rho*k;

               prodk=  tau[0][0]*dqdx[0][0][iq];
               prodk+= tau[1][0]*dqdx[1][0][iq];
               prodk+= tau[2][0]*dqdx[2][0][iq];
               prodk+= tau[0][1]*dqdx[0][1][iq];
               prodk+= tau[1][1]*dqdx[1][1][iq];
               prodk+= tau[2][1]*dqdx[2][1][iq];
 
               prodkt= min(prodk,10.*stbeta*rho*k*omega);
               rhs[nv0][iq]+= wq[0][iq]*( prodkt-stbeta*rho*k*omega );

               prodkt= min(prodk,10.*stbeta*rho*omega*omega);
               //rhs[nv0+1][iq]+= wq[0][iq]*( gam*rho*s2-beta*rho*omega*omega+(1.-f1)*cdkw );
               rhs[nv0+1][iq]+= wq[0][iq]*( gam/max(mut/rho,1e-16)*prodkt-beta*rho*omega*omega+(1.-f1)*cdkw );
           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*rhs[0][iq];
                  //rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(w-omega)+ omega*rhs[0][iq];
              }
               else
              {
              //    rhs[nv0][iq]+= wq[0][iq]*( prodkt-stbeta*rho*k*omega );
              //    rhs[nv0+1][iq]+= wq[0][iq]*( gam/max(mut/rho,1e-16)*prodk-beta*rho*omega*omega+(1.-f1)*cdkw );
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(6*mu0/(rho*beta*y*y)-omega)+ omega*rhs[0][iq];
                  //rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(w-omega)+ omega*rhs[0][iq];
              }
           }
        }
     }
  }

   /*void cKomegaSST2003::srhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,omega,y,sqrtb,mu,mu0,mut,prodk,div;
      Real tau[3][3];
      Real sij[3][3];
      Real s2;
      Real f1;
      Real gam,beta;
      Real cdkw;
      Real prodkt;
      Real cw=1.0;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(stbeta);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut=mu-mu0;
            f1= aux[naux-4][iq];
            gam=f1*gam1+(1.-f1)*gam2;
            beta=f1*beta1+(1.-f1)*beta2;
            cdkw= aux[naux-5][iq];

            //if( utau == 0 )
            if( utau <= 0 )
           {
// strain rate tensor

               sij[0][0]= 0.5*( dqdx[0][0][iq]+ dqdx[0][0][iq] ); 
               sij[1][0]= 0.5*( dqdx[0][1][iq]+ dqdx[1][0][iq] ); 
               sij[2][0]= 0.5*( dqdx[0][2][iq]+ dqdx[2][0][iq] ); 

               sij[0][1]= sij[1][0];
               sij[1][1]= 0.5*( dqdx[1][1][iq]+ dqdx[1][1][iq] ); 
               sij[2][1]= 0.5*( dqdx[1][2][iq]+ dqdx[2][1][iq] ); 

               sij[0][2]= sij[2][0];
               sij[1][2]= sij[2][1];
               sij[2][2]= 0.5*( dqdx[2][2][iq]+ dqdx[2][2][iq] ); 

               s2=  sij[0][0]*sij[0][0];
               s2+= sij[1][0]*sij[1][0];
               s2+= sij[2][0]*sij[2][0];
               s2+= sij[0][1]*sij[0][1];
               s2+= sij[1][1]*sij[1][1];
               s2+= sij[2][1]*sij[2][1];
               s2+= sij[0][2]*sij[0][2];
               s2+= sij[1][2]*sij[1][2];
               s2+= sij[2][2]*sij[2][2];
               s2*= 2;

// stress tensor

               tau[0][0]= 2.0*mut*sij[0][0]; 
               tau[1][0]= 2.0*mut*sij[1][0]; 
               tau[2][0]= 2.0*mut*sij[2][0]; 

               tau[0][1]=  tau[1][0];
               tau[1][1]= 2.0*mut*sij[1][1]; 
               tau[2][1]= 2.0*mut*sij[2][1]; 

               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               tau[2][2]= 2.0*mut*sij[2][2]; 

               div=  2./3.*mut*dqdx[0][0][iq];
               div+= 2./3.*mut*dqdx[1][1][iq];
               div+= 2./3.*mut*dqdx[2][2][iq];

               tau[0][0]-= div;
               tau[1][1]-= div;
               tau[2][2]-= div;

               tau[0][0]-= 2./3.*rho*k;
               tau[1][1]-= 2./3.*rho*k;
               tau[2][2]-= 2./3.*rho*k;

               prodk=  tau[0][0]*dqdx[0][0][iq];
               prodk+= tau[1][0]*dqdx[1][0][iq];
               prodk+= tau[2][0]*dqdx[2][0][iq];
               prodk+= tau[0][1]*dqdx[0][1][iq];
               prodk+= tau[1][1]*dqdx[1][1][iq];
               prodk+= tau[2][1]*dqdx[2][1][iq];
               prodk+= tau[0][2]*dqdx[0][2][iq];
               prodk+= tau[1][2]*dqdx[1][2][iq];
               prodk+= tau[2][2]*dqdx[2][2][iq];
 
               prodkt= min(prodk,10.*stbeta*rho*k*omega);

               rhs[nv0][iq]+= wq[0][iq]*( prodkt-stbeta*rho*k*omega );
               //rhs[nv0+1][iq]+= wq[0][iq]*( gam*rho*s2-beta*rho*omega*omega+(1.-f1)*cdkw );
               rhs[nv0+1][iq]+= wq[0][iq]*( gam/max(mut/rho,1e-16)*prodkt-beta*rho*omega*omega+(1.-f1)*cdkw );
           }
            else
           {
              // if( utau > 0 )
              //{
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*rhs[0][iq];
              //}
              // else
              //{
              //    rhs[nv0][iq]+= wq[0][iq]*( prodkt-stbeta*rho*k*omega );
              //    rhs[nv0+1][iq]+= wq[0][iq]*( gam/max(mut/rho,1e-16)*prodk-beta*rho*omega*omega+(1.-f1)*cdkw );
              //}
           }
        }
     }
  }*/

   void cKomegaSST2003::srhs33( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int iq,iv,ix;
      Real utau,rho,k,omega,y,sqrtb,mu,mu0,mut,prodk,div;
      Real tau[3][3];
      Real sij[3][3];
      Real s2;
      Real f1;
      Real gam,beta;
      Real cdkw;
      Real prodkt;
      Real cw=1.0;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            utau= aux[naux-3][iq];
            rho= aux[0][iq];
            k= q[nv0][iq];
            omega= q[nv0+1][iq];
            y= dst[0][iq];
            sqrtb= sqrt(stbeta);
            mu0= aux[naux0-2][iq];
            mu= aux[naux-2][iq];
            mut=mu-mu0;
            f1= aux[naux-4][iq];
            gam=f1*gam1+(1.-f1)*gam2;
            beta=f1*beta1+(1.-f1)*beta2;
            cdkw= aux[naux-5][iq];

            if( utau == 0 )
            //if( utau <= 0 )
           {
// strain rate tensor

               sij[0][0]= 0.5*( dqdx[0][0][iq]+ dqdx[0][0][iq] );
               sij[1][0]= 0.5*( dqdx[0][1][iq]+ dqdx[1][0][iq] );
               sij[2][0]= 0.5*( dqdx[0][2][iq]+ dqdx[2][0][iq] );

               sij[0][1]= sij[1][0];
               sij[1][1]= 0.5*( dqdx[1][1][iq]+ dqdx[1][1][iq] );
               sij[2][1]= 0.5*( dqdx[1][2][iq]+ dqdx[2][1][iq] );

               sij[0][2]= sij[2][0];
               sij[1][2]= sij[2][1];
               sij[2][2]= 0.5*( dqdx[2][2][iq]+ dqdx[2][2][iq] );

               //s2=  sij[0][0]*sij[0][0];
               //s2+= sij[1][0]*sij[1][0];
               //s2+= sij[2][0]*sij[2][0];
               //s2+= sij[0][1]*sij[0][1];
               //s2+= sij[1][1]*sij[1][1];
               //s2+= sij[2][1]*sij[2][1];
               //s2+= sij[0][2]*sij[0][2];
               //s2+= sij[1][2]*sij[1][2];
               //s2+= sij[2][2]*sij[2][2];
               //s2*= 2;

// stress tensor

               tau[0][0]= 2.0*mut*sij[0][0];
               tau[1][0]= 2.0*mut*sij[1][0];
               tau[2][0]= 2.0*mut*sij[2][0];

               tau[0][1]=  tau[1][0];
               tau[1][1]= 2.0*mut*sij[1][1];
               tau[2][1]= 2.0*mut*sij[2][1];

               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               tau[2][2]= 2.0*mut*sij[2][2];

               div=  2./3.*mut*dqdx[0][0][iq];
               div+= 2./3.*mut*dqdx[1][1][iq];
               div+= 2./3.*mut*dqdx[2][2][iq];

               tau[0][0]-= div;
               tau[1][1]-= div;
               tau[2][2]-= div;

               tau[0][0]-= 2./3.*rho*k;
               tau[1][1]-= 2./3.*rho*k;
               tau[2][2]-= 2./3.*rho*k;

               prodk=  tau[0][0]*dqdx[0][0][iq];
               prodk+= tau[1][0]*dqdx[1][0][iq];
               prodk+= tau[2][0]*dqdx[2][0][iq];
               prodk+= tau[0][1]*dqdx[0][1][iq];
               prodk+= tau[1][1]*dqdx[1][1][iq];
               prodk+= tau[2][1]*dqdx[2][1][iq];
               prodk+= tau[0][2]*dqdx[0][2][iq];
               prodk+= tau[1][2]*dqdx[1][2][iq];
               prodk+= tau[2][2]*dqdx[2][2][iq];

               prodkt= min(prodk,10.*stbeta*rho*k*omega);
               rhs[nv0][iq]+= wq[0][iq]*( prodkt-stbeta*rho*k*omega );
               //rhs[nv0+1][iq]+= wq[0][iq]*( gam*rho*s2-beta*rho*omega*omega+(1.-f1)*cdkw );
               //rhs[nv0+1][iq]+= wq[0][iq]*( gam*fmin(1./max(mut/rho,1e-16)*prodk, 10*stbeta*rho*omega*omega)-beta*rho*omega*omega+(1.-f1)*cdkw );
               rhs[nv0+1][iq]+= wq[0][iq]*( gam/max(mut/rho,1e-16)*prodkt-beta*rho*omega*omega+(1.-f1)*cdkw );
           }
            else
           {
               if( utau > 0 )
              {
                  rhs[nv0][iq]= lhs[nlhs-1][iq]* rho*(utau*utau/sqrtb- k)+ k*rhs[0][iq];
                  rhs[nv0+1][iq]=  lhs[nlhs-1][iq]*rho*(utau/(sqrtb*karm*y)-omega)+ omega*rhs[0][iq];
              }
               else
              {
              //    rhs[nv0][iq]+= wq[0][iq]*( prodkt-stbeta*rho*k*omega );
              //    rhs[nv0+1][iq]+= wq[0][iq]*( gam/max(mut/rho,1e-16)*prodk-beta*rho*omega*omega+(1.-f1)*cdkw );
                  rhs[nv0][iq]= - lhs[nlhs-1][iq]*rho*k;
                  rhs[nv0+1][iq]= lhs[nlhs-1][iq]*rho*(1*6*mu0/(rho*beta*y*y)-omega)+ omega*rhs[0][iq];
              }
           }
        }
     }
  }

