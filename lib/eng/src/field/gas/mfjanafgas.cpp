using namespace std;

#  include <field/gas.h>
#  include <fstream>

   cMfJanafGas::cMfJanafGas( cCosystem *Coo, cVisc *visc )
  {
      coo= Coo;
      vsc= visc;

      nvel=coo->getnvel(); 
      nx=coo->getnx(); 

      nvk=3;
      nv=2+nvel;
      naux=7;
      nauxf=8;
      nlhs= 2;

      nv0= nv;
      naux0= naux;
      nauxf0= nauxf;
      nlhs0= nlhs;

      vsc->setvrs( nx,nvel, &nv,&naux,&nauxf,&nlhs );

      ilv[0]=nvel;
      ilv[1]=ilv[0]+1;
      ilv[2]=ilv[1]+1;
      unit[0]= 100.;
      unit[1]=   1.;
      unit[2]= unit[0]*unit[0];
      deflt[0]=   0.;
      deflt[1]= 298.;
      deflt[2]= 100000./unit[2];
      rg= 287/unit[2];
      gam=1.4;
      eps= 0.05;
      coo->setnv(nv);

      props( );
  }

   void cMfJanafGas::props( )
  {
      Int il,isp;
/*    cout << "INFO FILE POINTER IS "<<info<<"\n";
      il= info->whereis( "cMfJanafGas",1);
      cout << "GAS INFO ON LINE "<<il<<"\n";
      cout << "PATH OFFSET IS "<<cpath<<"\n";*/
      string tmp= share;
      string jname=tmp+"/JANAF/";


      cJanaf::read( jname );

// gas composition & molecular weights
      string fname= jname+"/comps0.dat";
      ifstream fle( fname.c_str() );
     
      for( isp=0;isp<nsp;isp++ )
     {
         fle >> xsp[isp];
     }
      fle.close();

      Real w=0;
      for( isp=0;isp<nsp;isp++ )
     {
         w+= xsp[isp]*wsp[isp];
     }
      rg= runi/w/unit[2];

      cout << " mixture weight " << w << " runi " << runi << "\n" ;

      for( isp=0;isp<nsp;isp++ )
     {
         ssp[isp]= xsp[isp]/w;
     }
      cout << "GAS RG "<<rg<<"\n";
  }

   void cMfJanafGas::auxv2( Int iqs, Int iqe, Real *q[], Real *aux[] )
  {
// k,h,a

      Int  iq; 
      Int  iv;
      Int  isp,ir;
      Real h0,cv,rg1,cp;
      Real h0sp[MxNsp],cvsp[MxNsp];
      Real a0,a1,a2,a3,a4,a5,t,p;
      Real mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);


      if( iqe > iqs )
     {

         rg1= runi/unit[2];
      
         for( iq=iqs;iq<iqe;iq++ )
        {
            t= q[2][iq];
            p= q[3][iq];
// density
            aux[0][iq]= p/( rg*t );

// kinetic energy

            aux[1][iq]=  q[0][iq]*q[0][iq];
            aux[1][iq]+= q[1][iq]*q[1][iq];

// standard formation enthalpy and specific heat
            h0= 0;
            cv= 0;
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( t > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvsp[isp]= ( a0-1+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );
               cv+= cvsp[isp]*ssp[isp];

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               h0sp[isp]= a5+ t*( a0+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );
               h0+= h0sp[isp]*ssp[isp];

           }
         
            cv*= rg1;
            gam= (cv+rg)/cv;

            h0*= rg1;
            aux[1][iq]*= 0.5;
// speed of sound and total entalpy
            aux[2][iq]= gam*rg* t;
            aux[3][iq]= h0+ aux[1][iq];
            aux[2][iq]= sqrt( aux[2][iq] );
            aux[4][iq]= cv+rg;
            aux[5][iq]= mu;
            aux[6][iq]= kappa;

        }
     } 
  }

   void cMfJanafGas::auxv3( Int iqs, Int iqe, Real *q[], Real *aux[] )
  {
// k,h,a

      Int  iq; 
      Int  iv;
      Int  isp,ir;
      Real  h0,cv,rg1,cp;
      Real h0sp[MxNsp],cvsp[MxNsp];
      Real a0,a1,a2,a3,a4,a5,t,p;
      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);


      if( iqe > iqs )
     {

         rg1= runi/unit[2];
      
         for( iq=iqs;iq<iqe;iq++ )
        {
            t= q[3][iq];
            p= q[4][iq];

// density

            aux[0][iq]= p/( rg*t );

// kinetic energy

            aux[1][iq]=  q[0][iq]*q[0][iq];
            aux[1][iq]+= q[1][iq]*q[1][iq];
            aux[1][iq]+= q[2][iq]*q[2][iq];

// standard formation enthalpy and specific heat
            h0= 0;
            cv= 0;
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( t > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvsp[isp]= ( a0-1+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );
               cv+= cvsp[isp]*ssp[isp];

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               h0sp[isp]= a5+ t*( a0+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );
               h0+= h0sp[isp]*ssp[isp];

           }
         
            cv*= rg1;
            gam= (cv+rg)/cv;

            h0*= rg1;
            aux[1][iq]*= 0.5;
// speed of sound and total entalpy
            aux[2][iq]= gam*rg* t;
            aux[3][iq]= h0+ aux[1][iq];
            aux[2][iq]= sqrt( aux[2][iq] );
            aux[4][iq]= cv+rg;
            aux[5][iq]= mu;
            aux[6][iq]= kappa;

        }
     } 
  }

   void cMfJanafGas::auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch )
  {
// k,h,a

      Int  iq; 
      Int  iv;
      Int  isp,ir;
      Real  h0,cv,rg1,cp;
      Real h0sp[MxNsp],cvsp[MxNsp];
      Real a0,a1,a2,a3,a4,a5,t,p;
      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);


      if( iqe > iqs )
     {
         Int nq;
         Real *q, *aux;

         nq = q_view.get_dim1();

         q   = q_view.get_data();
         aux = aux_view.get_data();

         rg1= runi/unit[2];
      
         for( iq=iqs;iq<iqe;iq++ )
        {
            //t= q[3][iq];
            //p= q[4][iq];
            t= q[ADDR(3,iq,nq)];
            p= q[ADDR(4,iq,nq)];

// density

            //aux[0][iq]= p/( rg*t );
            aux[ADDR(0,iq,nq)]= p/( rg*t );

// kinetic energy

            //aux[1][iq]=  q[0][iq]*q[0][iq];
            //aux[1][iq]+= q[1][iq]*q[1][iq];
            //aux[1][iq]+= q[2][iq]*q[2][iq];
            aux[ADDR(1,iq,nq)]=  q[ADDR(0,iq,nq)]*q[ADDR(0,iq,nq)];
            aux[ADDR(1,iq,nq)]+= q[ADDR(1,iq,nq)]*q[ADDR(1,iq,nq)];
            aux[ADDR(1,iq,nq)]+= q[ADDR(2,iq,nq)]*q[ADDR(2,iq,nq)];

// standard formation enthalpy and specific heat
            h0= 0;
            cv= 0;
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( t > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvsp[isp]= ( a0-1+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );
               cv+= cvsp[isp]*ssp[isp];

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               h0sp[isp]= a5+ t*( a0+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );
               h0+= h0sp[isp]*ssp[isp];

           }
         
            cv*= rg1;
            gam= (cv+rg)/cv;
            h0*= rg1;

            //aux[1][iq]*= 0.5;
            aux[ADDR(1,iq,nq)]*= 0.5;
// speed of sound and total entalpy
            //aux[2][iq]= gam*rg* t;
            //aux[3][iq]= h0+ aux[1][iq];
            //aux[2][iq]= sqrt( aux[2][iq] );
            //aux[4][iq]= cv+rg;
            //aux[5][iq]= mu;
            //aux[6][iq]= kappa;
            aux[ADDR(2,iq,nq)]= gam*rg* t;
            aux[ADDR(3,iq,nq)]= h0+ aux[ADDR(1,iq,nq)];
            aux[ADDR(2,iq,nq)]= sqrt( aux[ADDR(2,iq,nq)] );
            aux[ADDR(4,iq,nq)]= cv+rg;
            aux[ADDR(5,iq,nq)]= mu;
            aux[ADDR(6,iq,nq)]= kappa;

        }
     } 
  }

   void cMfJanafGas::dvar2( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dU[], Real *dq[] )
  {
      Int iq,ia; 
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;

      if( iqe > iqs )
     {

         for( iq=iqs;iq<iqe;iq++ )
        {
            t=  q[2][iq];
            p=  q[3][iq];
            ro= aux[0][iq];
            h=  aux[3][iq];
            cv= aux[4][iq];
            cv-= rg;
            re= ro*h- p;
            e= re/ro;
      
            dro= dU[0][iq];
            dre= dU[3][iq];

            dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
            dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;

            dk=  q[0][iq]*dq[0][iq];
            dk+= q[1][iq]*dq[1][iq];

            dk*= ro;
            dt= ( dre- e*dro- dk )/( ro*cv );
            dp= p*( dro/ro+ dt/t );

            dq[nvel][iq]= dt;
            dq[nvel+1][iq]= dp;
        }
     } 
  }

   void cMfJanafGas::dvar3( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dU[], Real *dq[] )
  {
      Int iq,ia; 
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;

      if( iqe > iqs )
     {

         for( iq=iqs;iq<iqe;iq++ )
        {
            t=  q[3][iq];
            p=  q[4][iq];
            ro= aux[0][iq];
            h=  aux[3][iq];
            cv= aux[4][iq];
            cv-= rg;
            re= ro*h- p;
            e= re/ro;
      
            dro= dU[0][iq];
            dre= dU[4][iq];

            dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
            dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
            dq[2][iq]= ( dU[3][iq]- q[2][iq]*dro )/ro;

            dk=  q[0][iq]*dq[0][iq];
            dk+= q[1][iq]*dq[1][iq];
            dk+= q[2][iq]*dq[2][iq];

            dk*= ro;
            dt= ( dre- e*dro- dk )/( ro*cv );
            dp= p*( dro/ro+ dt/t );

            dq[3][iq]= dt;
            dq[4][iq]= dp;
        }
     } 
  }

   void cMfJanafGas::dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view )
  {
      Int iq,ia; 
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;

      Int nq;
      Real *q, *aux, *dU, *dq;

      nq = q_view.get_dim1();

      q = q_view.get_data();
      aux = aux_view.get_data();
      dU = dU_view.get_data();
      dq = dq_view.get_data();

      if( iqe > iqs )
     {

         for( iq=iqs;iq<iqe;iq++ )
        {
            //t=  q[3][iq];
            //p=  q[4][iq];
            t=  q[ADDR(3,iq,nq)];
            p=  q[ADDR(4,iq,nq)];
            //ro= aux[0][iq];
            //h=  aux[3][iq];
            //cv= aux[4][iq];
            ro= aux[ADDR(0,iq,nq)];
            h=  aux[ADDR(3,iq,nq)];
            cv= aux[ADDR(4,iq,nq)];
            cv-= rg;
            re= ro*h- p;
            e= re/ro;
     
 
            //dro= dU[0][iq];
            //dre= dU[4][iq];
            dro= dU[ADDR(0,iq,nq)];
            dre= dU[ADDR(4,iq,nq)];

            //dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
            //dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
            //dq[2][iq]= ( dU[3][iq]- q[2][iq]*dro )/ro;
            dq[ADDR(0,iq,nq)]= ( dU[ADDR(1,iq,nq)]- q[ADDR(0,iq,nq)]*dro )/ro;
            dq[ADDR(1,iq,nq)]= ( dU[ADDR(2,iq,nq)]- q[ADDR(1,iq,nq)]*dro )/ro;
            dq[ADDR(2,iq,nq)]= ( dU[ADDR(3,iq,nq)]- q[ADDR(2,iq,nq)]*dro )/ro;

            //dk=  q[0][iq]*dq[0][iq];
            //dk+= q[1][iq]*dq[1][iq];
            //dk+= q[2][iq]*dq[2][iq];
            dk=  q[ADDR(0,iq,nq)]*dq[ADDR(0,iq,nq)];
            dk+= q[ADDR(1,iq,nq)]*dq[ADDR(1,iq,nq)];
            dk+= q[ADDR(2,iq,nq)]*dq[ADDR(2,iq,nq)];

            dk*= ro;
            dt= ( dre- e*dro- dk )/( ro*cv );
            dp= p*( dro/ro+ dt/t );

            //dq[3][iq]= dt;
            //dq[4][iq]= dp;
            dq[ADDR(3,iq,nq)]= dt;
            dq[ADDR(4,iq,nq)]= dp;
        }
     } 
  }

   void cMfJanafGas::iflx22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
                                              Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                                         Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,epa,ka,qa[MxNVs],ua[3],ana[3],una,unaa,raa,cva,ta, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];
      Int             ia,ic,iql,iqr;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };

            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

 
// fluxes from the left
            tl=  ql[2][iql];
            pl=  ql[3][iql];
            rl=  auxl[0][iql];
            kl=  auxl[1][iql];
            al=  auxl[2][iql];
            hl=  auxl[3][iql];
            cvl= auxl[4][iql];
            cvl-= rg;
            e0l= hl-kl-rg*tl;
            unl=  wc[0][ic]*ql[0][iql];
            unl+= wc[1][ic]*ql[1][iql];

            ll1= unl-wxdc[0][ic];
            ll3= unl+ al;
            ll4= unl- al;

            fl[0]= ll1*rl;
            fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
            fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
            fl[3]= fl[0]*hl+ wxdc[0][ic]*pl;
            for( ia=4;ia<nv;ia++ )
           {  
               fl[ia]= fl[0]*ql[ia][iql];
           }

// fluxes from the right
            tr=  qr[2][iqr];
            pr=  qr[3][iqr];
            rr=  auxr[0][iqr];
            kr=  auxr[1][iqr];
            ar=  auxr[2][iqr];
            hr=  auxr[3][iqr];
            cvr= auxr[4][iqr];
            cvr-= rg;
            e0r= hr-kr-rg*tr;
            unr=  wc[0][ic]*qr[0][iqr];
            unr+= wc[1][ic]*qr[1][iqr];

            lr1= unr-wxdc[0][ic];
            lr3= unr+ ar;
            lr4= unr- ar;

            fr[0]= lr1 *rr;
            fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
            fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
            fr[3]= fr[0]*hr+ wxdc[0][ic]*pr;
            for( ia=4;ia<nv;ia++ )
           {  
               fr[ia]= fr[0]*qr[ia][iqr];
           }


// Roe averages
            ra= rr/rl;
            ra= sqrt(ra);
            wl= 1.+ra;
            wl= 1./wl;
            wr= 1.-wl;

            ra*= rl;
            if( fabs( tl-tr ) > 1.e-6 )
           {
               cva= (e0r-e0l)/(tr-tl);
           }
            else
           {
               cva= 0.5*(cvr+cvl);
           }
            gam= (cva+rg)/cva;

            ka= 0.;
            una= 0;
            qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
            qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
            qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
            qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
            for( ia=4;ia<nv;ia++ )
           {
               qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
           }
            ua[0]= qa[0];
            ua[1]= qa[1];

            ka=  ua[0]*ua[0];
            ka+= ua[1]*ua[1];

            una=  ua[0]* wc[0][ic];
            una+= ua[1]* wc[1][ic];

            ka*= 0.5; 

            ha= wl*hl+ wr*hr;
            ta= wl*tl+ wr*tr;
            epa=wl*e0l+wr*e0r;
            epa=epa-cva*ta;
            epa+=ka;
            a2a= (gam-1.)*( ha- epa );
            aa= sqrt( a2a );
            raa=ra*aa;

// eigenvalues with Harten's fix

            la1= una-wxdc[0][ic];
            la3= una+ aa;
            la4= una- aa;

            lmax= fabs(la1)+ aa;
            lmax= fmax( lmax, fabs(ll1)+ al );
            lmax= fmax( lmax, fabs(lr1)+ ar );

            le3= fmax( fmax( eps, la3-ll3), lr3-la3 );
            le4= fmax( fmax( eps, la4-ll4), lr4-la4 );

            la1= fabs(la1);
            la3= fabs(la3);
            la4= fabs(la4);

            if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
            if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

            auxc[0][ic]= wl;
            auxc[1][ic]= wr;
            auxc[2][ic]= ra;
            auxc[3][ic]= epa;
            auxc[4][ic]= la1;
            auxc[5][ic]= la3;
            auxc[6][ic]= la4;
            auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

            dr= rr- rl;
            du[0]= qr[0][iqr]- ql[0][iql];
            du[1]= qr[1][iqr]- ql[1][iql];
            dun= unr- unl;

            la3*=  0.5*ra/aa;
            la4*= -0.5*ra/aa;

            dp= pr-pl;
            dpa=dp/raa;
            dw1= la1*(dr-dp/a2a);
            dw3= la3*(dun+dpa);
            dw4= la4*(dun-dpa);
            dw2a= 0.;
            dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
            dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;

            dw2a=  dw2[0]*ua[0];
            dw2a+= dw2[1]*ua[1];

            ana[0]= aa*wc[0][ic];
            ana[1]= aa*wc[1][ic];

            for( ia=4;ia<nv;ia++ )
           {
               dw5[ia]= ra*la1*( qr[ia][iqr]- ql[ia][iql] );
           } 
 
            unaa=aa*una;

// Roe fluxes
            fa[0]= dw1+               dw3+                  dw4;
            fa[1]= dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
            fa[2]= dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
            fa[3]= dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );

            for( ia=4;ia<nv;ia++ ){ fa[ia]=  fa[0]*qa[ia]+ dw5[ia]; };

// assemble 
            f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[2][ic];
            f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[2][ic];
            f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[2][ic];
            f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[2][ic];

            rhsl[0][iql]-= f[0];
            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];

            rhsr[0][iqr]+= f[0];
            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];

            for( ia=4;ia<nv;ia++ )
           {
               f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[2][ic];
               rhsl[ia][iql]-= f[ia];
               rhsr[ia][iqr]+= f[ia];
           }
      
            auxc[nauxf-1][ic]*= wc[2][ic];

        }
     }
  }
   void cMfJanafGas::iflx23( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
                                     Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                     Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,epa,ka,qa[MxNVs],ua[3],ana[3],una,unaa,raa,cva,ta, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];
      Int             ia,ic,iql,iqr;
      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };

            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

 
// fluxes from the left
            tl=  ql[3][iql];
            pl=  ql[4][iql];
            rl=  auxl[0][iql];
            kl=  auxl[1][iql];
            al=  auxl[2][iql];
            hl=  auxl[3][iql];
            cvl= auxl[4][iql];
            cvl-= rg;
            e0l= hl-kl-rg*tl;
            unl=  wc[0][ic]*ql[0][iql];
            unl+= wc[1][ic]*ql[1][iql];

            ll1= unl-wxdc[0][ic];
            ll3= unl+ al;
            ll4= unl- al;

            fl[0]= ll1*rl;
            fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
            fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
            fl[3]= fl[0]*ql[2][iql];
            fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
            for( ia=5;ia<nv;ia++ )
           {  
               fl[ia]= fl[0]*ql[ia][iql];
           }

// fluxes from the right
            tr=  qr[3][iqr];
            pr=  qr[4][iqr];
            rr=  auxr[0][iqr];
            kr=  auxr[1][iqr];
            ar=  auxr[2][iqr];
            hr=  auxr[3][iqr];
            cvr= auxr[4][iqr];
            cvr-= rg;
            e0r= hr-kr-rg*tr;
            unr=  wc[0][ic]*qr[0][iqr];
            unr+= wc[1][ic]*qr[1][iqr];

            lr1= unr-wxdc[0][ic];
            lr3= unr+ ar;
            lr4= unr- ar;

            fr[0]= lr1 *rr;
            fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
            fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
            fr[3]= fr[0]*qr[2][iqr];
            fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
            for( ia=5;ia<nv;ia++ )
           {  
               fr[ia]= fr[0]*qr[ia][iqr];
           }


// Roe averages
            ra= rr/rl;
            ra= sqrt(ra);
            wl= 1.+ra;
            wl= 1./wl;
            wr= 1.-wl;

            ra*= rl;
            if( fabs( tl-tr ) > 1.e-6 )
           {
               cva= (e0r-e0l)/(tr-tl);
           }
            else
           {
               cva= 0.5*(cvr+cvl);
           }
            gam= (cva+rg)/cva;

            ka= 0.;
            una= 0;
            qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
            qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
            qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
            qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
            qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
            for( ia=5;ia<nv;ia++ )
           {
               qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
           }
            ua[0]= qa[0];
            ua[1]= qa[1];
            ua[2]= qa[1];

            ka=  ua[0]*ua[0];
            ka+= ua[1]*ua[1];
            ka+= ua[2]*ua[2];

            una=  ua[0]* wc[0][ic];
            una+= ua[1]* wc[1][ic];

            ka*= 0.5; 

            ha= wl*hl+ wr*hr;
            ta= wl*tl+ wr*tr;
            epa=wl*e0l+wr*e0r;
            epa=epa-cva*ta;
            epa+=ka;
            a2a= (gam-1.)*( ha- epa );
            aa= sqrt( a2a );
            raa=ra*aa;

// eigenvalues with Harten's fix

            la1= una-wxdc[0][ic];
            la3= una+ aa;
            la4= una- aa;

            lmax= fabs(la1)+ aa;
            lmax= fmax( lmax, fabs(ll1)+ al );
            lmax= fmax( lmax, fabs(lr1)+ ar );

            le3= fmax( fmax( eps, la3-ll3), lr3-la3 );
            le4= fmax( fmax( eps, la4-ll4), lr4-la4 );

            la1= fabs(la1);
            la3= fabs(la3);
            la4= fabs(la4);

            if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
            if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

            auxc[0][ic]= wl;
            auxc[1][ic]= wr;
            auxc[2][ic]= ra;
            auxc[3][ic]= epa;
            auxc[4][ic]= la1;
            auxc[5][ic]= la3;
            auxc[6][ic]= la4;
            auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

            dr= rr- rl;
            du[0]= qr[0][iqr]- ql[0][iql];
            du[1]= qr[1][iqr]- ql[1][iql];
            du[2]= qr[2][iqr]- ql[2][iql];
            dun= unr- unl;

            la3*=  0.5*ra/aa;
            la4*= -0.5*ra/aa;

            dp= pr-pl;
            dpa=dp/raa;
            dw1= la1*(dr-dp/a2a);
            dw3= la3*(dun+dpa);
            dw4= la4*(dun-dpa);
            dw2a= 0.;
            dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
            dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
            dw2[2]= du[2];                dw2[2]*= ra*la1;

            dw2a=  dw2[0]*ua[0];
            dw2a+= dw2[1]*ua[1];
            dw2a+= dw2[2]*ua[2];

            ana[0]= aa*wc[0][ic];
            ana[1]= aa*wc[1][ic];

            for( ia=4;ia<nv;ia++ )
           {
               dw5[ia]= ra*la1*( qr[ia][iqr]- ql[ia][iql] );
           } 
 
            unaa=aa*una;

// Roe fluxes
            fa[0]= dw1+                 dw3+                  dw4;
            fa[1]= dw1*ua[0]+   dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
            fa[2]= dw1*ua[1]+   dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
            fa[3]= fa[0]*ua[2]+ dw2[2];
            fa[4]= dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );

            for( ia=5;ia<nv;ia++ ){ fa[ia]=  fa[0]*qa[ia]+ dw5[ia]; };

// assemble 
            f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[2][ic];
            f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[2][ic];
            f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[2][ic];
            f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[2][ic];
            f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[2][ic];

            rhsl[0][iql]-= f[0];
            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];
            rhsl[4][iql]-= f[4];

            rhsr[0][iqr]+= f[0];
            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];
            rhsr[4][iqr]+= f[4];

            for( ia=5;ia<nv;ia++ )
           {
               f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[2][ic];
               rhsl[ia][iql]-= f[ia];
               rhsr[ia][iqr]+= f[ia];
           }
      
            auxc[nauxf-1][ic]*= wc[2][ic];

        }
     }
  }

   void cMfJanafGas::iflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
                                     Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                     Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,epa,ka,qa[MxNVs],ua[3],ana[3],una,unaa,raa,cva,ta, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];
      Int             ia,ic,iql,iqr;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };

            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

 
// fluxes from the left
            tl=  ql[3][iql];
            pl=  ql[4][iql];
            rl=  auxl[0][iql];
            kl=  auxl[1][iql];
            al=  auxl[2][iql];
            hl=  auxl[3][iql];
            cvl= auxl[4][iql];
            cvl-= rg;
            e0l= hl-kl-rg*tl;
            unl=  wc[0][ic]*ql[0][iql];
            unl+= wc[1][ic]*ql[1][iql];
            unl+= wc[2][ic]*ql[2][iql];

            ll1= unl-wxdc[0][ic];
            ll3= unl+ al;
            ll4= unl- al;

            fl[0]= ll1*rl;
            fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
            fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
            fl[3]= fl[0]*ql[2][iql]+ wc[2][ic]*pl;
            fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
            for( ia=5;ia<nv;ia++ )
           {  
               fl[ia]= fl[0]*ql[ia][iql];
           }

// fluxes from the right
            tr=  qr[3][iqr];
            pr=  qr[4][iqr];
            rr=  auxr[0][iqr];
            kr=  auxr[1][iqr];
            ar=  auxr[2][iqr];
            hr=  auxr[3][iqr];
            cvr= auxr[4][iqr];
            cvr-= rg;
            e0r= hr-kr-rg*tr;
            unr=  wc[0][ic]*qr[0][iqr];
            unr+= wc[1][ic]*qr[1][iqr];
            unr+= wc[2][ic]*qr[2][iqr];

            lr1= unr-wxdc[0][ic];
            lr3= unr+ ar;
            lr4= unr- ar;

            fr[0]= lr1 *rr;
            fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
            fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
            fr[3]= fr[0]*qr[2][iqr]+ wc[2][ic]*pr;
            fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
            for( ia=5;ia<nv;ia++ )
           {  
               fr[ia]= fr[0]*qr[ia][iqr];
           }


// Roe averages
            ra= rr/rl;
            ra= sqrt(ra);
            wl= 1.+ra;
            wl= 1./wl;
            wr= 1.-wl;

            ra*= rl;
            if( fabs( tl-tr ) > 1.e-6 )
           {
               cva= (e0r-e0l)/(tr-tl);
           }
            else
           {
               cva= 0.5*(cvr+cvl);
           }
            gam= (cva+rg)/cva;

            ka= 0.;
            una= 0;
            qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
            qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
            qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
            qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
            qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
            for( ia=5;ia<nv;ia++ )
           {
               qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
           }
            ua[0]= qa[0];
            ua[1]= qa[1];
            ua[2]= qa[2];

            ka=  ua[0]*ua[0];
            ka+= ua[1]*ua[1];
            ka+= ua[2]*ua[2];

            una=  ua[0]* wc[0][ic];
            una+= ua[1]* wc[1][ic];
            una+= ua[2]* wc[2][ic];

            ka*= 0.5; 

            ha= wl*hl+ wr*hr;
            ta= wl*tl+ wr*tr;
            epa=wl*e0l+wr*e0r;
            epa=epa-cva*ta;
            epa+=ka;
            a2a= (gam-1.)*( ha- epa );
            aa= sqrt( a2a );
            raa=ra*aa;

// eigenvalues with Harten's fix

            la1= una-wxdc[0][ic];
            la3= una+ aa;
            la4= una- aa;

            lmax= fabs(la1)+ aa;
            lmax= fmax( lmax, fabs(ll1)+ al );
            lmax= fmax( lmax, fabs(lr1)+ ar );

            le3= fmax( fmax( eps, la3-ll3), lr3-la3 );
            le4= fmax( fmax( eps, la4-ll4), lr4-la4 );

            la1= fabs(la1);
            la3= fabs(la3);
            la4= fabs(la4);

            if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
            if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

            auxc[0][ic]= wl;
            auxc[1][ic]= wr;
            auxc[2][ic]= ra;
            auxc[3][ic]= epa;
            auxc[4][ic]= la1;
            auxc[5][ic]= la3;
            auxc[6][ic]= la4;
            auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

            dr= rr- rl;
            du[0]= qr[0][iqr]- ql[0][iql];
            du[1]= qr[1][iqr]- ql[1][iql];
            du[2]= qr[2][iqr]- ql[2][iql];
            dun= unr- unl;

            la3*=  0.5*ra/aa;
            la4*= -0.5*ra/aa;

            dp= pr-pl;
            dpa=dp/raa;
            dw1= la1*(dr-dp/a2a);
            dw3= la3*(dun+dpa);
            dw4= la4*(dun-dpa);
            dw2a= 0.;
            dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
            dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
            dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;

            dw2a=  dw2[0]*ua[0];
            dw2a+= dw2[1]*ua[1];
            dw2a+= dw2[2]*ua[2];

            ana[0]= aa*wc[0][ic];
            ana[1]= aa*wc[1][ic];
            ana[2]= aa*wc[2][ic];

            for( ia=5;ia<nv;ia++ )
           {
               dw5[ia]= ra*la1*( qr[ia][iqr]- ql[ia][iql] );
           } 
 
            unaa=aa*una;

// Roe fluxes
            fa[0]= dw1+               dw3+                  dw4;
            fa[1]= dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
            fa[2]= dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
            fa[3]= dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] );
            fa[4]= dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );

            for( ia=5;ia<nv;ia++ ){ fa[ia]=  fa[0]*qa[ia]+ dw5[ia]; };

// assemble 
            f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
            f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
            f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
            f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
            f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];

            rhsl[0][iql]-= f[0];
            rhsl[1][iql]-= f[1];
            rhsl[2][iql]-= f[2];
            rhsl[3][iql]-= f[3];
            rhsl[4][iql]-= f[4];

            rhsr[0][iqr]+= f[0];
            rhsr[1][iqr]+= f[1];
            rhsr[2][iqr]+= f[2];
            rhsr[3][iqr]+= f[3];
            rhsr[4][iqr]+= f[4];

            for( ia=5;ia<nv;ia++ )
           {
               f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
               rhsl[ia][iql]-= f[ia];
               rhsr[ia][iqr]+= f[ia];
           }
      
            auxc[nauxf-1][ic]*= wc[3][ic];
        }
     }
  }

   void cMfJanafGas::iflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                              cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,epa,ka,qa[MxNVs],ua[3],ana[3],una,unaa,raa,cva,ta, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];
      Int             ia,ic,iql,iqr;

      Int             nql,nqr;

      Int nfc, nq;
      Int *icql;
      Real *sql, *sauxl, *srhsl;
      Int *icqr;
      Real *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      srhsr = rhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();


      nql = nfc;
      nqr = nq;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ADDR(0,ic,nfc)];

 
// fluxes from the left
            //tl=  ql[3][iql];
            //pl=  ql[4][iql];
            //rl=  auxl[0][iql];
            //kl=  auxl[1][iql];
            //al=  auxl[2][iql];
            //hl=  auxl[3][iql];
            //cvl= auxl[4][iql];
            tl=  sql[ADDR(3,iql,nql)];
            pl=  sql[ADDR(4,iql,nql)];
            rl=  sauxl[ADDR(0,iql,nql)];
            kl=  sauxl[ADDR(1,iql,nql)];
            al=  sauxl[ADDR(2,iql,nql)];
            hl=  sauxl[ADDR(3,iql,nql)];
            cvl= sauxl[ADDR(4,iql,nql)];
            cvl-= rg;
            e0l= hl-kl-rg*tl;
            //unl=  wc[0][ic]*ql[0][iql];
            //unl+= wc[1][ic]*ql[1][iql];
            //unl+= wc[2][ic]*ql[2][iql];
            unl=  swc[ADDR(0,ic,nfc)]*sql[ADDR(0,iql,nql)];
            unl+= swc[ADDR(1,ic,nfc)]*sql[ADDR(1,iql,nql)];
            unl+= swc[ADDR(2,ic,nfc)]*sql[ADDR(2,iql,nql)];

            //ll1= unl-wxdc[0][ic];
            ll1= unl-swxdc[ADDR(0,ic,nfc)];
            ll3= ll1+ al;
            ll4= ll1- al;

            fl[0]= ll1*rl;
            //fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
            //fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
            //fl[3]= fl[0]*ql[2][iql]+ wc[2][ic]*pl;
            //fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
            fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ swc[ADDR(0,ic,nfc)]*pl;
            fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ swc[ADDR(1,ic,nfc)]*pl;
            fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ swc[ADDR(2,ic,nfc)]*pl;
            fl[4]= fl[0]*hl+ swxdc[ADDR(0,ic,nfc)]*pl;
            for( ia=5;ia<nv;ia++ )
           {  
               //fl[ia]= fl[0]*ql[ia][iql];
               fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)];
           }

// fluxes from the right
            //tr=  qr[3][iqr];
            //pr=  qr[4][iqr];
            //rr=  auxr[0][iqr];
            //kr=  auxr[1][iqr];
            //ar=  auxr[2][iqr];
            //hr=  auxr[3][iqr];
            //cvr= auxr[4][iqr];
            tr=  sqr[ADDR(3,iqr,nqr)];
            pr=  sqr[ADDR(4,iqr,nqr)];
            rr=  sauxr[ADDR(0,iqr,nqr)];
            kr=  sauxr[ADDR(1,iqr,nqr)];
            ar=  sauxr[ADDR(2,iqr,nqr)];
            hr=  sauxr[ADDR(3,iqr,nqr)];
            cvr= sauxr[ADDR(4,iqr,nqr)];
            cvr-= rg;
            e0r= hr-kr-rg*tr;
            //unr=  wc[0][ic]*qr[0][iqr];
            //unr+= wc[1][ic]*qr[1][iqr];
            //unr+= wc[2][ic]*qr[2][iqr];
            unr=  swc[ADDR(0,ic,nfc)]*sqr[ADDR(0,iqr,nqr)];
            unr+= swc[ADDR(1,ic,nfc)]*sqr[ADDR(1,iqr,nqr)];
            unr+= swc[ADDR(2,ic,nfc)]*sqr[ADDR(2,iqr,nqr)];

            //lr1= unr-wxdc[0][ic];
            lr1= unr-swxdc[ADDR(0,ic,nfc)];
            lr3= lr1+ ar;
            lr4= lr1- ar;

            fr[0]= lr1 *rr;
            //fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
            //fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
            //fr[3]= fr[0]*qr[2][iqr]+ wc[2][ic]*pr;
            //fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
            fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ swc[ADDR(0,ic,nfc)]*pr;
            fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ swc[ADDR(1,ic,nfc)]*pr;
            fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ swc[ADDR(2,ic,nfc)]*pr;
            fr[4]= fr[0]*hr+ swxdc[ADDR(0,ic,nfc)]*pr;
            for( ia=5;ia<nv;ia++ )
           {  
               //fr[ia]= fr[0]*qr[ia][iqr];
               fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)];
           }


// Roe averages
            ra= rr/rl;
            ra= sqrt(ra);
            wl= 1.+ra;
            wl= 1./wl;
            wr= 1.-wl;

            ra*= rl;
            if( fabs( tl-tr ) > 1.e-6 )
           {
               cva= (e0r-e0l)/(tr-tl);
           }
            else
           {
               cva= 0.5*(cvr+cvl);
           }
            gam= (cva+rg)/cva;

            ka= 0.;
            una= 0;
            //qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
            //qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
            //qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
            //qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
            //qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
            qa[0]= wl*sql[ADDR(0,iql,nql)]+ wr*sqr[ADDR(0,iqr,nqr)];
            qa[1]= wl*sql[ADDR(1,iql,nql)]+ wr*sqr[ADDR(1,iqr,nqr)];
            qa[2]= wl*sql[ADDR(2,iql,nql)]+ wr*sqr[ADDR(2,iqr,nqr)];
            qa[3]= wl*sql[ADDR(3,iql,nql)]+ wr*sqr[ADDR(3,iqr,nqr)];
            qa[4]= wl*sql[ADDR(4,iql,nql)]+ wr*sqr[ADDR(4,iqr,nqr)];
            for( ia=5;ia<nv;ia++ )
           {
               //qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
               qa[ia]= wl*sql[ADDR(ia,iql,nql)]+ wr*sqr[ADDR(ia,iqr,nqr)];
           }
            ua[0]= qa[0];
            ua[1]= qa[1];
            ua[2]= qa[2];

            ka=  ua[0]*ua[0];
            ka+= ua[1]*ua[1];
            ka+= ua[2]*ua[2];

            //una=  ua[0]* wc[0][ic];
            //una+= ua[1]* wc[1][ic];
            //una+= ua[2]* wc[2][ic];
            una=  ua[0]* swc[ADDR(0,ic,nfc)];
            una+= ua[1]* swc[ADDR(1,ic,nfc)];
            una+= ua[2]* swc[ADDR(2,ic,nfc)];

            ka*= 0.5; 

            ha= wl*hl+ wr*hr;
            ta= wl*tl+ wr*tr;
            epa=wl*e0l+wr*e0r;
            epa=epa-cva*ta;
            epa+=ka;
            a2a= (gam-1.)*( ha- epa );
            aa= sqrt( a2a );
            raa=ra*aa;

// eigenvalues with Harten's fix

            //la1= una-wxdc[0][ic];
            la1= una-swxdc[ADDR(0,ic,nfc)];
            la3= la1+ aa;
            la4= la1- aa;

            lmax= fabs(la1)+ aa;
            lmax= fmax( lmax, fabs(ll1)+ al );
            lmax= fmax( lmax, fabs(lr1)+ ar );

            le3= fmax( fmax( eps, la3-ll3), lr3-la3 );
            le4= fmax( fmax( eps, la4-ll4), lr4-la4 );

            la1= fabs(la1);
            la3= fabs(la3);
            la4= fabs(la4);

            if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
            if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

            //auxc[0][ic]= wl;
            //auxc[1][ic]= wr;
            //auxc[2][ic]= ra;
            //auxc[3][ic]= epa;
            //auxc[4][ic]= la1;
            //auxc[5][ic]= la3;
            //auxc[6][ic]= la4;
            //auxc[nauxf-1][ic]= lmax;
            sauxc[ADDR(0,ic,nfc)]= wl;
            sauxc[ADDR(1,ic,nfc)]= wr;
            sauxc[ADDR(2,ic,nfc)]= ra;
            sauxc[ADDR(3,ic,nfc)]= epa;
            sauxc[ADDR(4,ic,nfc)]= la1;
            sauxc[ADDR(5,ic,nfc)]= la3;
            sauxc[ADDR(6,ic,nfc)]= la4;
            sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

            dr= rr- rl;
            //du[0]= qr[0][iqr]- ql[0][iql];
            //du[1]= qr[1][iqr]- ql[1][iql];
            //du[2]= qr[2][iqr]- ql[2][iql];
            du[0]= sqr[ADDR(0,iqr,nqr)]- sql[ADDR(0,iql,nql)];
            du[1]= sqr[ADDR(1,iqr,nqr)]- sql[ADDR(1,iql,nql)];
            du[2]= sqr[ADDR(2,iqr,nqr)]- sql[ADDR(2,iql,nql)];
            dun= unr- unl;

            la3*=  0.5*ra/aa;
            la4*= -0.5*ra/aa;

            dp= pr-pl;
            dpa=dp/raa;
            dw1= la1*(dr-dp/a2a);
            dw3= la3*(dun+dpa);
            dw4= la4*(dun-dpa);
            dw2a= 0.;
            //dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
            //dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
            //dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
            dw2[0]= du[0]- dun*swc[ADDR(0,ic,nfc)]; dw2[0]*= ra*la1;
            dw2[1]= du[1]- dun*swc[ADDR(1,ic,nfc)]; dw2[1]*= ra*la1;
            dw2[2]= du[2]- dun*swc[ADDR(2,ic,nfc)]; dw2[2]*= ra*la1;

            dw2a=  dw2[0]*ua[0];
            dw2a+= dw2[1]*ua[1];
            dw2a+= dw2[2]*ua[2];

            //ana[0]= aa*wc[0][ic];
            //ana[1]= aa*wc[1][ic];
            //ana[2]= aa*wc[2][ic];
            ana[0]= aa*swc[ADDR(0,ic,nfc)];
            ana[1]= aa*swc[ADDR(1,ic,nfc)];
            ana[2]= aa*swc[ADDR(2,ic,nfc)];

            for( ia=5;ia<nv;ia++ )
           {
               //dw5[ia]= ra*la1*( qr[ia][iqr]- ql[ia][iql] );
               dw5[ia]= ra*la1*( sqr[ADDR(ia,iqr,nqr)]- sql[ADDR(ia,iql,nql)] );
           } 
 
            unaa=aa*una;

// Roe fluxes
            fa[0]= dw1+               dw3+                  dw4;
            fa[1]= dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
            fa[2]= dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
            fa[3]= dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] );
            fa[4]= dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );

            for( ia=5;ia<nv;ia++ ){ fa[ia]=  fa[0]*qa[ia]+ dw5[ia]; };

// assemble 
            //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
            //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
            //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
            //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
            //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
            f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*swc[ADDR(3,ic,nfc)];
            f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*swc[ADDR(3,ic,nfc)];
            f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*swc[ADDR(3,ic,nfc)];
            f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*swc[ADDR(3,ic,nfc)];
            f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*swc[ADDR(3,ic,nfc)];

            //rhsl[0][iql]-= f[0];
            //rhsl[1][iql]-= f[1];
            //rhsl[2][iql]-= f[2];
            //rhsl[3][iql]-= f[3];
            //rhsl[4][iql]-= f[4];
            srhsl[ADDR(0,iql,nql)]-= f[0];
            srhsl[ADDR(1,iql,nql)]-= f[1];
            srhsl[ADDR(2,iql,nql)]-= f[2];
            srhsl[ADDR(3,iql,nql)]-= f[3];
            srhsl[ADDR(4,iql,nql)]-= f[4];

            //rhsr[0][iqr]+= f[0];
            //rhsr[1][iqr]+= f[1];
            //rhsr[2][iqr]+= f[2];
            //rhsr[3][iqr]+= f[3];
            //rhsr[4][iqr]+= f[4];
            srhsr[ADDR(0,iqr,nqr)]+= f[0];
            srhsr[ADDR(1,iqr,nqr)]+= f[1];
            srhsr[ADDR(2,iqr,nqr)]+= f[2];
            srhsr[ADDR(3,iqr,nqr)]+= f[3];
            srhsr[ADDR(4,iqr,nqr)]+= f[4];

            for( ia=5;ia<nv;ia++ )
           {
               //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
               f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*swc[ADDR(3,ic,nfc)];
               //rhsl[ia][iql]-= f[ia];
               //rhsr[ia][iqr]+= f[ia];
               srhsl[ADDR(ia,iql,nql)]-= f[ia];
               srhsr[ADDR(ia,iqr,nqr)]+= f[ia];
           }
      
            //auxc[nauxf-1][ic]*= wc[3][ic];
            sauxc[ADDR(nauxf-1,ic,nfc)]*= swc[ADDR(3,ic,nfc)];
        }
     }
  }

   void cMfJanafGas::diflx22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                               Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                                          Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            aa,a2a,ra,ha,ka,ua[3],ana[3],epa,una,unaa,raa, la1,la4,la3,fa[MxNVs],qa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,lmax,ta,pa,dw5[MxNVs];
      Real            wl,wr;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

// fluxes from the left

            pl= ql[3][iql];
            rl= auxl[0][iql];
            hl= auxl[3][iql];

            drl=    dql[0][iql];
            dpl=  dauxl[3][iql];
            drel=   dql[3][iql];

            unl=  -wxdc[0][ic];
            unl+=  wc[0][ic]*ql[0][iql]; 
            unl+=  wc[1][ic]*ql[1][iql]; 
            dunl=  wc[0][ic]*dauxl[0][iql]; 
            dunl+= wc[1][ic]*dauxl[1][iql]; 

            ml= unl*rl;
            fl[0]= drl*unl+ rl*dunl;
            fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+ dpl*wc[0][ic]; 
            fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+ dpl*wc[1][ic]; 
            fl[3]= dunl*rl*hl+ unl*( drel+ dpl )+ dpl*wxdc[0][ic];
            for( ia=4;ia<nv;ia++ )
           {
               fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql]; 
           }

// fluxes from the right

            pr= qr[3][iqr];
            rr= auxr[0][iqr];
            hr= auxr[3][iqr];

            drr=    dqr[0][iqr];
            dpr=  dauxr[3][iqr];
            drer=   dqr[3][iqr];

            unr=  -wxdc[0][ic];
            unr+=  wc[0][ic]*qr[0][iqr]; 
            unr+=  wc[1][ic]*qr[1][iqr]; 
            dunr=  wc[0][ic]*dauxr[0][iqr]; 
            dunr+= wc[1][ic]*dauxr[1][iqr]; 

            mr= unr*rr;
            fr[0]= drr*unr+ rr*dunr;
            fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
            fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
            fr[3]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
            for( ia=4;ia<nv;ia++ )
           {
               fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr]; 
           }


// retrieve Roe averages

            wl= auxc[0][ic];
            wr= auxc[1][ic];
            ra= auxc[2][ic];
            epa= auxc[3][ic];
            la1= auxc[4][ic];
            la3= auxc[5][ic];
            la4= auxc[6][ic];
            lmax= auxc[nauxf-1][ic];

            qa[0]= wl*ql[0][iql]+wr*qr[0][iqr]; 
            qa[1]= wl*ql[1][iql]+wr*qr[1][iqr]; 
            qa[2]= wl*ql[2][iql]+wr*qr[2][iqr]; 
            qa[3]= wl*ql[3][iql]+wr*qr[3][iqr]; 

            for( ia=4;ia<nv;ia++ )
           { 
               qa[ia]= wl*ql[ia][iql]+wr*qr[ia][iqr]; 
           }

            ha= wl*hl+ wr*hr;
            a2a= (gam-1.)*( ha- epa );
            aa= sqrt( a2a );
            raa=ra*aa;

            ua[0]= qa[0];
            ua[1]= qa[1];

// Left eigenvectors

            dr= drr- drl;
            dp= dpr- dpl;

            du[0]= dauxr[0][iqr]- dauxl[0][iql];
            du[1]= dauxr[1][iqr]- dauxl[1][iql];

            una=  ua[0]*wc[0][ic];
            una+= ua[1]*wc[1][ic];

            dun= dunr- dunl;

            la3*=  0.5*ra/aa;
            la4*= -0.5*ra/aa;

            dpa=dp/raa;
            dw1= la1*(dr-dp/a2a);
            dw3= la3*(dun+dpa);
            dw4= la4*(dun-dpa);

            dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
            dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;

            dw2a=  dw2[0]*ua[0];
            dw2a+= dw2[1]*ua[1];

            ana[0]= aa*wc[0][ic];
            ana[1]= aa*wc[1][ic];

            for( ia=4;ia<nv;ia++ )
           {
               dw5[ia]= ra*la1*( dauxr[ia][iqr]- dauxl[ia][iql] );
           }
 
            unaa=aa*una;

// Roe fluxes
            fa[0]=   dw1+               dw3+                  dw4;
            fa[1]=   dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
            fa[2]=   dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
            fa[3]=   dw1*epa+    dw2a+  dw3*( ha+ unaa)+      dw4*( ha- unaa );

            for( ia=4;ia<nv;ia++ ){ fa[ia]= fa[0]*qa[ia]+ dw5[ia]; }
           
// assemble 
            Real f[MxNVs];
            f[0]= 0.5*( fr[0]+ fl[0] -fa[0] )*wc[2][ic];
            f[1]= 0.5*( fr[1]+ fl[1] -fa[1] )*wc[2][ic];
            f[2]= 0.5*( fr[2]+ fl[2] -fa[2] )*wc[2][ic];
            f[3]= 0.5*( fr[3]+ fl[3] -fa[3] )*wc[2][ic];

            resl[0][iql]-= f[0];
            resl[1][iql]-= f[1];
            resl[2][iql]-= f[2];
            resl[3][iql]-= f[3];

            resr[0][iqr]+= f[0];
            resr[1][iqr]+= f[1];
            resr[2][iqr]+= f[2];
            resr[3][iqr]+= f[3];

            for( ia=4;ia<nv;ia++ )
           { 
               f[ia]= 0.5*( fr[ia]+ fl[ia] -fa[ia] )*wc[2][ic];
               resl[ia][iql]-= f[ia];
               resr[ia][iqr]+= f[ia];
           }
        }
     }
  }
   void cMfJanafGas::diflx23( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            aa,a2a,ra,ha,ka,ua[3],ana[3],epa,una,unaa,raa, la1,la4,la3,fa[MxNVs],qa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,lmax,ta,pa,dw5[MxNVs];
      Real            wl,wr;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

// fluxes from the left

            pl= ql[4][iql];
            rl= auxl[0][iql];
            hl= auxl[3][iql];

            drl=    dql[0][iql];
            dpl=  dauxl[4][iql];
            drel=   dql[4][iql];

            unl=  -wxdc[0][ic];
            unl+=  wc[0][ic]*ql[0][iql]; 
            unl+=  wc[1][ic]*ql[1][iql]; 
            dunl=  wc[0][ic]*dauxl[0][iql]; 
            dunl+= wc[1][ic]*dauxl[1][iql]; 

            ml= unl*rl;
            fl[0]= drl*unl+ rl*dunl;
            fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+ dpl*wc[0][ic]; 
            fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+ dpl*wc[1][ic]; 
            fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql];
            fl[4]= dunl*rl*hl+ unl*( drel+ dpl )+ dpl*wxdc[0][ic];
            for( ia=5;ia<nv;ia++ )
           {
               fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql]; 
           }

// fluxes from the right

            pr= qr[4][iqr];
            rr= auxr[0][iqr];
            hr= auxr[3][iqr];

            drr=    dqr[0][iqr];
            dpr=  dauxr[4][iqr];
            drer=   dqr[4][iqr];

            unr=  -wxdc[0][ic];
            unr+=  wc[0][ic]*qr[0][iqr]; 
            unr+=  wc[1][ic]*qr[1][iqr]; 
            dunr=  wc[0][ic]*dauxr[0][iqr]; 
            dunr+= wc[1][ic]*dauxr[1][iqr]; 

            mr= unr*rr;
            fr[0]= drr*unr+ rr*dunr;
            fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
            fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
            fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr];
            fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
            for( ia=5;ia<nv;ia++ )
           {
               fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr]; 
           }


// retrieve Roe averages

            wl= auxc[0][ic];
            wr= auxc[1][ic];
            ra= auxc[2][ic];
            epa= auxc[3][ic];
            la1= auxc[4][ic];
            la3= auxc[5][ic];
            la4= auxc[6][ic];
            lmax= auxc[nauxf-1][ic];

            qa[0]= wl*ql[0][iql]+wr*qr[0][iqr]; 
            qa[1]= wl*ql[1][iql]+wr*qr[1][iqr]; 
            qa[2]= wl*ql[2][iql]+wr*qr[2][iqr]; 
            qa[3]= wl*ql[3][iql]+wr*qr[3][iqr]; 
            qa[4]= wl*ql[4][iql]+wr*qr[4][iqr]; 

            for( ia=5;ia<nv;ia++ )
           { 
               qa[ia]= wl*ql[ia][iql]+wr*qr[ia][iqr]; 
           }
            ha= wl*hl+ wr*hr;
            a2a= (gam-1.)*( ha- epa );
            aa= sqrt( a2a );
            raa=ra*aa;

            ua[0]= qa[0];
            ua[1]= qa[1];
            ua[2]= qa[2];

// Left eigenvectors

            dr= drr- drl;
            dp= dpr- dpl;

            du[0]= dauxr[0][iqr]- dauxl[0][iql];
            du[1]= dauxr[1][iqr]- dauxl[1][iql];
            du[2]= dauxr[2][iqr]- dauxl[2][iql];

            una=  ua[0]*wc[0][ic];
            una+= ua[1]*wc[1][ic];

            dun= dunr- dunl;

            la3*=  0.5*ra/aa;
            la4*= -0.5*ra/aa;

            dpa=dp/raa;
            dw1= la1*(dr-dp/a2a);
            dw3= la3*(dun+dpa);
            dw4= la4*(dun-dpa);

            dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
            dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
            dw2[2]= du[2];                dw2[2]*= ra*la1;

            dw2a=  dw2[0]*ua[0];
            dw2a+= dw2[1]*ua[1];
            dw2a+= dw2[2]*ua[2];

            ana[0]= aa*wc[0][ic];
            ana[1]= aa*wc[1][ic];

            for( ia=5;ia<nv;ia++ )
           {
               dw5[ia]= ra*la1*( dauxr[ia][iqr]- dauxl[ia][iql] );
           }
 
            unaa=aa*una;

// Roe fluxes
            fa[0]=   dw1+                 dw3+                  dw4;
            fa[1]=   dw1*ua[0]+   dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
            fa[2]=   dw1*ua[1]+   dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
            fa[3]=   fa[0]*ua[2]+ dw2[2];
            fa[4]=   dw1*epa+     dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );

            for( ia=5;ia<nv;ia++ ){ fa[ia]= fa[0]*qa[ia]+ dw5[ia]; }
           
            Real f[MxNVs];
// assemble 
            f[0]= 0.5*( fr[0]+ fl[0] -fa[0] )*wc[2][ic];
            f[1]= 0.5*( fr[1]+ fl[1] -fa[1] )*wc[2][ic];
            f[2]= 0.5*( fr[2]+ fl[2] -fa[2] )*wc[2][ic];
            f[3]= 0.5*( fr[3]+ fl[3] -fa[3] )*wc[2][ic];
            f[4]= 0.5*( fr[4]+ fl[4] -fa[4] )*wc[2][ic];

            resl[0][iql]-= f[0];
            resl[1][iql]-= f[1];
            resl[2][iql]-= f[2];
            resl[3][iql]-= f[3];
            resl[4][iql]-= f[4];

            resr[0][iqr]+= f[0];
            resr[1][iqr]+= f[1];
            resr[2][iqr]+= f[2];
            resr[3][iqr]+= f[3];
            resr[4][iqr]+= f[4];

            for( ia=5;ia<nv;ia++ )
           { 
               f[ia]= 0.5*( fr[ia]+ fl[ia] -fa[ia] )*wc[2][ic];
               resl[ia][iql]-= f[ia];
               resr[ia][iqr]+= f[ia];
           }
        }
     }
  }

   void cMfJanafGas::diflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            aa,a2a,ra,ha,ka,ua[3],ana[3],epa,una,unaa,raa, la1,la4,la3,fa[MxNVs],qa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,lmax,ta,pa,dw5[MxNVs];
      Real            wl,wr;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };

// fluxes from the left

            pl= ql[4][iql];
            rl= auxl[0][iql];
            hl= auxl[3][iql];

            drl=    dql[0][iql];
            dpl=  dauxl[4][iql];
            drel=   dql[4][iql];

            unl=  -wxdc[0][ic];
            unl+=  wc[0][ic]*ql[0][iql]; 
            unl+=  wc[1][ic]*ql[1][iql]; 
            unl+=  wc[2][ic]*ql[2][iql]; 
            dunl=  wc[0][ic]*dauxl[0][iql]; 
            dunl+= wc[1][ic]*dauxl[1][iql]; 
            dunl+= wc[2][ic]*dauxl[2][iql]; 

            ml= unl*rl;
            fl[0]= drl*unl+ rl*dunl;
            fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+ dpl*wc[0][ic]; 
            fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+ dpl*wc[1][ic]; 
            fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql]+ dpl*wc[2][ic]; 
            fl[4]= dunl*rl*hl+ unl*( drel+ dpl )+ dpl*wxdc[0][ic];
            for( ia=5;ia<nv;ia++ )
           {
               fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql]; 
           }

// fluxes from the right

            pr= qr[4][iqr];
            rr= auxr[0][iqr];
            hr= auxr[3][iqr];

            drr=    dqr[0][iqr];
            dpr=  dauxr[4][iqr];
            drer=   dqr[4][iqr];

            unr=  -wxdc[0][ic];
            unr+=  wc[0][ic]*qr[0][iqr]; 
            unr+=  wc[1][ic]*qr[1][iqr]; 
            unr+=  wc[2][ic]*qr[2][iqr]; 
            dunr=  wc[0][ic]*dauxr[0][iqr]; 
            dunr+= wc[1][ic]*dauxr[1][iqr]; 
            dunr+= wc[2][ic]*dauxr[2][iqr]; 

            mr= unr*rr;
            fr[0]= drr*unr+ rr*dunr;
            fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
            fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
            fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
            fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
            for( ia=5;ia<nv;ia++ )
           {
               fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr]; 
           }


// retrieve Roe averages

            wl= auxc[0][ic];
            wr= auxc[1][ic];
            ra= auxc[2][ic];
            epa= auxc[3][ic];
            la1= auxc[4][ic];
            la3= auxc[5][ic];
            la4= auxc[6][ic];
            lmax= auxc[nauxf-1][ic];

            qa[0]= wl*ql[0][iql]+wr*qr[0][iqr]; 
            qa[1]= wl*ql[1][iql]+wr*qr[1][iqr]; 
            qa[2]= wl*ql[2][iql]+wr*qr[2][iqr]; 
            qa[3]= wl*ql[3][iql]+wr*qr[3][iqr]; 
            qa[4]= wl*ql[4][iql]+wr*qr[4][iqr]; 

            for( ia=5;ia<nv;ia++ )
           { 
               qa[ia]= wl*ql[ia][iql]+wr*qr[ia][iqr]; 
           }
            ha= wl*hl+ wr*hr;
            a2a= (gam-1.)*( ha- epa );
            aa= sqrt( a2a );
            raa=ra*aa;

            ua[0]= qa[0];
            ua[1]= qa[1];
            ua[2]= qa[2];

// Left eigenvectors

            dr= drr- drl;
            dp= dpr- dpl;

            du[0]= dauxr[0][iqr]- dauxl[0][iql];
            du[1]= dauxr[1][iqr]- dauxl[1][iql];
            du[2]= dauxr[2][iqr]- dauxl[2][iql];

            una=  ua[0]*wc[0][ic];
            una+= ua[1]*wc[1][ic];
            una+= ua[2]*wc[2][ic];

            dun= dunr- dunl;

            la3*=  0.5*ra/aa;
            la4*= -0.5*ra/aa;

            dpa=dp/raa;
            dw1= la1*(dr-dp/a2a);
            dw3= la3*(dun+dpa);
            dw4= la4*(dun-dpa);

            dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
            dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
            dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;

            dw2a=  dw2[0]*ua[0];
            dw2a+= dw2[1]*ua[1];
            dw2a+= dw2[2]*ua[2];

            ana[0]= aa*wc[0][ic];
            ana[1]= aa*wc[1][ic];
            ana[2]= aa*wc[2][ic];

            for( ia=5;ia<nv;ia++ )
           {
               dw5[ia]= ra*la1*( dauxr[ia][iqr]- dauxl[ia][iql] );
           }
 
            unaa=aa*una;

// Roe fluxes
            fa[0]=   dw1+               dw3+                  dw4;
            fa[1]=   dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
            fa[2]=   dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
            fa[3]=   dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] );
            fa[4]=   dw1*epa+    dw2a+  dw3*( ha+ unaa)+      dw4*( ha- unaa );

            for( ia=5;ia<nv;ia++ ){ fa[ia]= fa[0]*qa[ia]+ dw5[ia]; }
           
// assemble 
            Real f[MxNVs];
            f[0]= 0.5*( fr[0]+ fl[0] -fa[0] )*wc[3][ic];
            f[1]= 0.5*( fr[1]+ fl[1] -fa[1] )*wc[3][ic];
            f[2]= 0.5*( fr[2]+ fl[2] -fa[2] )*wc[3][ic];
            f[3]= 0.5*( fr[3]+ fl[3] -fa[3] )*wc[3][ic];
            f[4]= 0.5*( fr[4]+ fl[4] -fa[4] )*wc[3][ic];

            resl[0][iql]-= f[0];
            resl[1][iql]-= f[1];
            resl[2][iql]-= f[2];
            resl[3][iql]-= f[3];
            resl[4][iql]-= f[4];

            resr[0][iqr]+= f[0];
            resr[1][iqr]+= f[1];
            resr[2][iqr]+= f[2];
            resr[3][iqr]+= f[3];
            resr[4][iqr]+= f[4];

            for( ia=5;ia<nv;ia++ )
           { 
               f[ia]= 0.5*( fr[ia]+ fl[ia] -fa[ia] )*wc[3][ic];
               resl[ia][iql]-= f[ia];
               resr[ia][iqr]+= f[ia];
           }
        }
     }
  }

   void cMfJanafGas::diflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                               cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            aa,a2a,ra,ha,ka,ua[3],ana[3],epa,una,unaa,raa, la1,la4,la3,fa[MxNVs],qa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,lmax,ta,pa,dw5[MxNVs];
      Real            wl,wr;

      Int             nql, nqr;

      Int nfc, nq;
      Int *icql;
      Real *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *swc, *swxdc, *sauxc;
         
      nfc = wc.get_dim1();
      nq  = qr.get_dim1();
         
      icql   = icql_view.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdql   = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl  = resl.get_data();
      icqr   = icqr_view.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqr   = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr  = resr.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      nql = nfc;
      nqr = nq;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ADDR(0,ic,nfc)];

// fluxes from the left

            //pl= ql[4][iql];
            //rl= auxl[0][iql];
            //hl= auxl[3][iql];
            pl= sql[ADDR(4,iql,nql)];
            rl= sauxl[ADDR(0,iql,nql)];
            hl= sauxl[ADDR(3,iql,nql)];

            //drl=    dql[0][iql];
            //dpl=  dauxl[4][iql];
            //drel=   dql[4][iql];
            drl=    sdql[ADDR(0,iql,nql)];
            dpl=  sdauxl[ADDR(4,iql,nql)];
            drel=   sdql[ADDR(4,iql,nql)];

            //unl=  -wxdc[0][ic];
            //unl+=  wc[0][ic]*ql[0][iql]; 
            //unl+=  wc[1][ic]*ql[1][iql]; 
            //unl+=  wc[2][ic]*ql[2][iql]; 
            //dunl=  wc[0][ic]*dauxl[0][iql]; 
            //dunl+= wc[1][ic]*dauxl[1][iql]; 
            //dunl+= wc[2][ic]*dauxl[2][iql]; 
            unl=  -swxdc[ADDR(0,ic,nfc)];
            unl+=  swc[ADDR(0,ic,nfc)]*sql[ADDR(0,iql,nql)]; 
            unl+=  swc[ADDR(1,ic,nfc)]*sql[ADDR(1,iql,nql)]; 
            unl+=  swc[ADDR(2,ic,nfc)]*sql[ADDR(2,iql,nql)]; 
            dunl=  swc[ADDR(0,ic,nfc)]*sdauxl[ADDR(0,iql,nql)]; 
            dunl+= swc[ADDR(1,ic,nfc)]*sdauxl[ADDR(1,iql,nql)]; 
            dunl+= swc[ADDR(2,ic,nfc)]*sdauxl[ADDR(2,iql,nql)]; 

            ml= unl*rl;
            fl[0]= drl*unl+ rl*dunl;
            //fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+ dpl*wc[0][ic]; 
            //fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+ dpl*wc[1][ic]; 
            //fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql]+ dpl*wc[2][ic]; 
            //fl[4]= dunl*rl*hl+ unl*( drel+ dpl )+ dpl*wxdc[0][ic];
            fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ ml*sdauxl[ADDR(0,iql,nql)]+ dpl*swc[ADDR(0,ic,nfc)]; 
            fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ ml*sdauxl[ADDR(1,iql,nql)]+ dpl*swc[ADDR(1,ic,nfc)]; 
            fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ ml*sdauxl[ADDR(2,iql,nql)]+ dpl*swc[ADDR(2,ic,nfc)]; 
            fl[4]= dunl*rl*hl+ unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
            for( ia=5;ia<nv;ia++ )
           {
               //fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql]; 
               fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)]+ ml*sdauxl[ADDR(ia,iql,nql)]; 
           }

// fluxes from the right

            //pr= qr[4][iqr];
            //rr= auxr[0][iqr];
            //hr= auxr[3][iqr];
            pr= sqr[ADDR(4,iqr,nqr)];
            rr= sauxr[ADDR(0,iqr,nqr)];
            hr= sauxr[ADDR(3,iqr,nqr)];

            //drr=    dqr[0][iqr];
            //dpr=  dauxr[4][iqr];
            //drer=   dqr[4][iqr];
            drr=    sdqr[ADDR(0,iqr,nqr)];
            dpr=  sdauxr[ADDR(4,iqr,nqr)];
            drer=   sdqr[ADDR(4,iqr,nqr)];

            //unr=  -wxdc[0][ic];
            //unr+=  wc[0][ic]*qr[0][iqr]; 
            //unr+=  wc[1][ic]*qr[1][iqr]; 
            //unr+=  wc[2][ic]*qr[2][iqr]; 
            //dunr=  wc[0][ic]*dauxr[0][iqr]; 
            //dunr+= wc[1][ic]*dauxr[1][iqr]; 
            //dunr+= wc[2][ic]*dauxr[2][iqr]; 
            unr=  -swxdc[ADDR(0,ic,nfc)];
            unr+=  swc[ADDR(0,ic,nfc)]*sqr[ADDR(0,iqr,nqr)]; 
            unr+=  swc[ADDR(1,ic,nfc)]*sqr[ADDR(1,iqr,nqr)]; 
            unr+=  swc[ADDR(2,ic,nfc)]*sqr[ADDR(2,iqr,nqr)]; 
            dunr=  swc[ADDR(0,ic,nfc)]*sdauxr[ADDR(0,iqr,nqr)]; 
            dunr+= swc[ADDR(1,ic,nfc)]*sdauxr[ADDR(1,iqr,nqr)]; 
            dunr+= swc[ADDR(2,ic,nfc)]*sdauxr[ADDR(2,iqr,nqr)]; 

            mr= unr*rr;
            fr[0]= drr*unr+ rr*dunr;
            //fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
            //fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
            //fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
            //fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
            fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ mr*sdauxr[ADDR(0,iqr,nqr)]+ dpr*swc[ADDR(0,ic,nfc)]; 
            fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ mr*sdauxr[ADDR(1,iqr,nqr)]+ dpr*swc[ADDR(1,ic,nfc)]; 
            fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ mr*sdauxr[ADDR(2,iqr,nqr)]+ dpr*swc[ADDR(2,ic,nfc)]; 
            fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
            for( ia=5;ia<nv;ia++ )
           {
               //fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr]; 
               fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)]+ mr*sdauxr[ADDR(ia,iqr,nqr)]; 
           }


// retrieve Roe averages

            //wl= auxc[0][ic];
            //wr= auxc[1][ic];
            //ra= auxc[2][ic];
            //epa= auxc[3][ic];
            //la1= auxc[4][ic];
            //la3= auxc[5][ic];
            //la4= auxc[6][ic];
            //lmax= auxc[nauxf-1][ic];
            wl=  sauxc[ADDR(0,ic,nfc)];
            wr=  sauxc[ADDR(1,ic,nfc)];
            ra=  sauxc[ADDR(2,ic,nfc)];
            epa= sauxc[ADDR(3,ic,nfc)];
            la1= sauxc[ADDR(4,ic,nfc)];
            la3= sauxc[ADDR(5,ic,nfc)];
            la4= sauxc[ADDR(6,ic,nfc)];
            lmax= sauxc[ADDR(nauxf-1,ic,nfc)];

            //qa[0]= wl*ql[0][iql]+wr*qr[0][iqr]; 
            //qa[1]= wl*ql[1][iql]+wr*qr[1][iqr]; 
            //qa[2]= wl*ql[2][iql]+wr*qr[2][iqr]; 
            //qa[3]= wl*ql[3][iql]+wr*qr[3][iqr]; 
            //qa[4]= wl*ql[4][iql]+wr*qr[4][iqr]; 
            qa[0]= wl*sql[ADDR(0,iql,nql)]+wr*sqr[ADDR(0,iqr,nqr)]; 
            qa[1]= wl*sql[ADDR(1,iql,nql)]+wr*sqr[ADDR(1,iqr,nqr)]; 
            qa[2]= wl*sql[ADDR(2,iql,nql)]+wr*sqr[ADDR(2,iqr,nqr)]; 
            qa[3]= wl*sql[ADDR(3,iql,nql)]+wr*sqr[ADDR(3,iqr,nqr)]; 
            qa[4]= wl*sql[ADDR(4,iql,nql)]+wr*sqr[ADDR(4,iqr,nqr)]; 

            for( ia=5;ia<nv;ia++ )
           { 
               //qa[ia]= wl*ql[ia][iql]+wr*qr[ia][iqr]; 
               qa[ia]= wl*sql[ADDR(ia,iql,nql)]+wr*sqr[ADDR(ia,iqr,nqr)]; 
           }
            ha= wl*hl+ wr*hr;
            a2a= (gam-1.)*( ha- epa );
            aa= sqrt( a2a );
            raa=ra*aa;

            ua[0]= qa[0];
            ua[1]= qa[1];
            ua[2]= qa[2];

// Left eigenvectors

            dr= drr- drl;
            dp= dpr- dpl;

            //du[0]= dauxr[0][iqr]- dauxl[0][iql];
            //du[1]= dauxr[1][iqr]- dauxl[1][iql];
            //du[2]= dauxr[2][iqr]- dauxl[2][iql];
            du[0]= sdauxr[ADDR(0,iqr,nqr)]- sdauxl[ADDR(0,iql,nql)];
            du[1]= sdauxr[ADDR(1,iqr,nqr)]- sdauxl[ADDR(1,iql,nql)];
            du[2]= sdauxr[ADDR(2,iqr,nqr)]- sdauxl[ADDR(2,iql,nql)];

            //una=  ua[0]*wc[0][ic];
            //una+= ua[1]*wc[1][ic];
            //una+= ua[2]*wc[2][ic];
            una=  ua[0]*swc[ADDR(0,ic,nfc)];
            una+= ua[1]*swc[ADDR(1,ic,nfc)];
            una+= ua[2]*swc[ADDR(2,ic,nfc)];

            dun= dunr- dunl;

            la3*=  0.5*ra/aa;
            la4*= -0.5*ra/aa;

            dpa=dp/raa;
            dw1= la1*(dr-dp/a2a);
            dw3= la3*(dun+dpa);
            dw4= la4*(dun-dpa);

            //dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
            //dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
            //dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
            dw2[0]= du[0]- dun*swc[ADDR(0,ic,nfc)]; dw2[0]*= ra*la1;
            dw2[1]= du[1]- dun*swc[ADDR(1,ic,nfc)]; dw2[1]*= ra*la1;
            dw2[2]= du[2]- dun*swc[ADDR(2,ic,nfc)]; dw2[2]*= ra*la1;

            dw2a=  dw2[0]*ua[0];
            dw2a+= dw2[1]*ua[1];
            dw2a+= dw2[2]*ua[2];

            //ana[0]= aa*wc[0][ic];
            //ana[1]= aa*wc[1][ic];
            //ana[2]= aa*wc[2][ic];
            ana[0]= aa*swc[ADDR(0,ic,nfc)];
            ana[1]= aa*swc[ADDR(1,ic,nfc)];
            ana[2]= aa*swc[ADDR(2,ic,nfc)];

            for( ia=5;ia<nv;ia++ )
           {
               //dw5[ia]= ra*la1*( dauxr[ia][iqr]- dauxl[ia][iql] );
               dw5[ia]= ra*la1*( sdauxr[ADDR(ia,iqr,nqr)]- sdauxl[ADDR(ia,iql,nql)] );
           }
 
            unaa=aa*una;

// Roe fluxes
            fa[0]=   dw1+               dw3+                  dw4;
            fa[1]=   dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
            fa[2]=   dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
            fa[3]=   dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] );
            fa[4]=   dw1*epa+    dw2a+  dw3*( ha+ unaa)+      dw4*( ha- unaa );

            for( ia=5;ia<nv;ia++ ){ fa[ia]= fa[0]*qa[ia]+ dw5[ia]; }
           
// assemble 
            Real f[MxNVs];
            //f[0]= 0.5*( fr[0]+ fl[0] -fa[0] )*wc[3][ic];
            //f[1]= 0.5*( fr[1]+ fl[1] -fa[1] )*wc[3][ic];
            //f[2]= 0.5*( fr[2]+ fl[2] -fa[2] )*wc[3][ic];
            //f[3]= 0.5*( fr[3]+ fl[3] -fa[3] )*wc[3][ic];
            //f[4]= 0.5*( fr[4]+ fl[4] -fa[4] )*wc[3][ic];
            f[0]= 0.5*( fr[0]+ fl[0] -fa[0] )*swc[ADDR(3,ic,nfc)];
            f[1]= 0.5*( fr[1]+ fl[1] -fa[1] )*swc[ADDR(3,ic,nfc)];
            f[2]= 0.5*( fr[2]+ fl[2] -fa[2] )*swc[ADDR(3,ic,nfc)];
            f[3]= 0.5*( fr[3]+ fl[3] -fa[3] )*swc[ADDR(3,ic,nfc)];
            f[4]= 0.5*( fr[4]+ fl[4] -fa[4] )*swc[ADDR(3,ic,nfc)];

            //resl[0][iql]-= f[0];
            //resl[1][iql]-= f[1];
            //resl[2][iql]-= f[2];
            //resl[3][iql]-= f[3];
            //resl[4][iql]-= f[4];
            sresl[ADDR(0,iql,nql)]-= f[0];
            sresl[ADDR(1,iql,nql)]-= f[1];
            sresl[ADDR(2,iql,nql)]-= f[2];
            sresl[ADDR(3,iql,nql)]-= f[3];
            sresl[ADDR(4,iql,nql)]-= f[4];

            //resr[0][iqr]+= f[0];
            //resr[1][iqr]+= f[1];
            //resr[2][iqr]+= f[2];
            //resr[3][iqr]+= f[3];
            //resr[4][iqr]+= f[4];
            sresr[ADDR(0,iqr,nqr)]+= f[0];
            sresr[ADDR(1,iqr,nqr)]+= f[1];
            sresr[ADDR(2,iqr,nqr)]+= f[2];
            sresr[ADDR(3,iqr,nqr)]+= f[3];
            sresr[ADDR(4,iqr,nqr)]+= f[4];


            for( ia=5;ia<nv;ia++ )
           { 
               //f[ia]= 0.5*( fr[ia]+ fl[ia] -fa[ia] )*wc[3][ic];
               f[ia]= 0.5*( fr[ia]+ fl[ia] -fa[ia] )*swc[ADDR(3,ic,nfc)];
               //resl[ia][iql]-= f[ia];
               //resr[ia][iqr]+= f[ia];
               sresl[ADDR(ia,iql,nql)]-= f[ia];
               sresr[ADDR(ia,iqr,nqr)]+= f[ia];
               
           }
        }
     }
  }

   void cMfJanafGas::diflx33( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,iql,iqr;

      Real            wl,wr,aa,a2a,ra,ha,epa,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs],wn[4], ua[3], du[3];

      Int nfc, nq;    

      nfc = wc.get_dim1();
      nq  = q.get_dim1();

      Int *sicq;
      Real *sq, *saux, *sdq, *sdaux, *sres, *swc, *swxdc, *sauxc;

      sicq  = icq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      sdq   = dq0.get_data();
      sdaux = daux.get_data();
      sres  = res.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      firstprivate(cp), \
      private(fl,fr,qa,ana,fa,dw2,dq,dw5,f,wn, ua, du)\
      present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],\
               sres[0:nv*nq],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( Int ic=ics;ic<ice;ic++ )
     {
         //iql= icq[0][ic];
         //iqr= icq[1][ic];
         iql= sicq[ADDR(0,ic,nfc)];
         iqr= sicq[ADDR(1,ic,nfc)];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left

         //pl= q[4][iql];
         //rl= aux[0][iql];
         //hl= aux[3][iql];
         pl= sq[ADDR(4,iql,nq)];
         rl= saux[ADDR(0,iql,nq)];
         hl= saux[ADDR(3,iql,nq)];

         //drl=    dq0[0][iql];
         //dpl=  daux[4][iql];
         //drel=   dq0[4][iql];
         drl=    sdq[ADDR(0,iql,nq)];
         dpl=  sdaux[ADDR(4,iql,nq)];
         drel=   sdq[ADDR(4,iql,nq)];

         //unl=  wc[0][ic]*q[0][iql]; 
         //unl+= wc[1][ic]*q[1][iql]; 
         //unl+= wc[2][ic]*q[2][iql]; 
         //unl-= wxdc[0][ic];
         unl=  wn[0]*sq[ADDR(0,iql,nq)]; 
         unl+= wn[1]*sq[ADDR(1,iql,nq)]; 
         unl+= wn[2]*sq[ADDR(2,iql,nq)]; 
         unl-= swxdc[ADDR(0,ic,nfc)];

         //dunl=  wc[0][ic]*daux[0][iql]; 
         //dunl+= wc[1][ic]*daux[1][iql]; 
         //dunl+= wc[2][ic]*daux[2][iql]; 
         dunl=  wn[0]*sdaux[ADDR(0,iql,nq)]; 
         dunl+= wn[1]*sdaux[ADDR(1,iql,nq)]; 
         dunl+= wn[2]*sdaux[ADDR(2,iql,nq)]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         //fl[1]= fl[0]*q[0][iql]+ ml*daux[0][iql]+  dpl*wc[0][ic]; 
         //fl[2]= fl[0]*q[1][iql]+ ml*daux[1][iql]+  dpl*wc[1][ic]; 
         //fl[3]= fl[0]*q[2][iql]+ ml*daux[2][iql]+  dpl*wc[2][ic]; 
         //fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         fl[1]= fl[0]*sq[ADDR(0,iql,nq)]+ ml*sdaux[ADDR(0,iql,nq)]+  dpl*wn[0]; 
         fl[2]= fl[0]*sq[ADDR(1,iql,nq)]+ ml*sdaux[ADDR(1,iql,nq)]+  dpl*wn[1]; 
         fl[3]= fl[0]*sq[ADDR(2,iql,nq)]+ ml*sdaux[ADDR(2,iql,nq)]+  dpl*wn[2]; 
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fl[ia]= fl[0]*q[ia][iql]+ ml*daux[ia][iql];
            fl[ia]= fl[0]*sq[ADDR(ia,iql,nq)]+ ml*sdaux[ADDR(ia,iql,nq)];
        }

// fluxes from the right

         //pr= q[4][iqr];
         //rr= aux[0][iqr];
         //hr= aux[3][iqr];
         pr= sq[ADDR(4,iqr,nq)];
         rr= saux[ADDR(0,iqr,nq)];
         hr= saux[ADDR(3,iqr,nq)];

         //drr=    dq0[0][iqr];
         //dpr=  daux[4][iqr];
         //drer=   dq0[4][iqr];
         drr=    sdq[ADDR(0,iqr,nq)];
         dpr=  sdaux[ADDR(4,iqr,nq)];
         drer=   sdq[ADDR(4,iqr,nq)];

         //unr=  wc[0][ic]*q[0][iqr]; 
         //unr+= wc[1][ic]*q[1][iqr]; 
         //unr+= wc[2][ic]*q[2][iqr]; 
         //unr-= wxdc[0][ic];
         unr=  wn[0]*sq[ADDR(0,iqr,nq)]; 
         unr+= wn[1]*sq[ADDR(1,iqr,nq)]; 
         unr+= wn[2]*sq[ADDR(2,iqr,nq)]; 
         unr-= swxdc[ADDR(0,ic,nfc)];

         //dunr=  wc[0][ic]*daux[0][iqr]; 
         //dunr+= wc[1][ic]*daux[1][iqr]; 
         //dunr+= wc[2][ic]*daux[2][iqr]; 
         dunr=  wn[0]*sdaux[ADDR(0,iqr,nq)]; 
         dunr+= wn[1]*sdaux[ADDR(1,iqr,nq)]; 
         dunr+= wn[2]*sdaux[ADDR(2,iqr,nq)]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         //fr[1]= fr[0]*q[0][iqr]+ mr*daux[0][iqr]+ dpr*wc[0][ic]; 
         //fr[2]= fr[0]*q[1][iqr]+ mr*daux[1][iqr]+ dpr*wc[1][ic]; 
         //fr[3]= fr[0]*q[2][iqr]+ mr*daux[2][iqr]+ dpr*wc[2][ic]; 
         //fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         fr[1]= fr[0]*sq[ADDR(0,iqr,nq)]+ mr*sdaux[ADDR(0,iqr,nq)]+ dpr*wn[0]; 
         fr[2]= fr[0]*sq[ADDR(1,iqr,nq)]+ mr*sdaux[ADDR(1,iqr,nq)]+ dpr*wn[1]; 
         fr[3]= fr[0]*sq[ADDR(2,iqr,nq)]+ mr*sdaux[ADDR(2,iqr,nq)]+ dpr*wn[2]; 
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fr[ia]= fr[0]*q[ia][iqr]+ mr*daux[ia][iqr];
            fr[ia]= fr[0]*sq[ADDR(ia,iqr,nq)]+ mr*sdaux[ADDR(ia,iqr,nq)];
        }

// retrieve Roe averages

         //wl= auxc[0][ic];
         //wr= auxc[1][ic];
         //ra= auxc[2][ic];
         //epa= auxc[3][ic];
         //la1= auxc[4][ic];
         //la3= auxc[5][ic];
         //la4= auxc[6][ic];
         //lmax= auxc[nauxf-1][ic];
         wl=  sauxc[ADDR(0,ic,nfc)];
         wr=  sauxc[ADDR(1,ic,nfc)];
         ra=  sauxc[ADDR(2,ic,nfc)];
         epa= sauxc[ADDR(3,ic,nfc)];
         la1= sauxc[ADDR(4,ic,nfc)];
         la3= sauxc[ADDR(5,ic,nfc)];
         la4= sauxc[ADDR(6,ic,nfc)];

         //qa[0]= wl*ql[0][iql]+wr*qr[0][iqr]; 
         //qa[1]= wl*ql[1][iql]+wr*qr[1][iqr]; 
         //qa[2]= wl*ql[2][iql]+wr*qr[2][iqr]; 
         //qa[3]= wl*ql[3][iql]+wr*qr[3][iqr]; 
         //qa[4]= wl*ql[4][iql]+wr*qr[4][iqr]; 
         qa[0]= wl*sq[ADDR(0,iql,nq)]+wr*sq[ADDR(0,iqr,nq)]; 
         qa[1]= wl*sq[ADDR(1,iql,nq)]+wr*sq[ADDR(1,iqr,nq)]; 
         qa[2]= wl*sq[ADDR(2,iql,nq)]+wr*sq[ADDR(2,iqr,nq)]; 
         qa[3]= wl*sq[ADDR(3,iql,nq)]+wr*sq[ADDR(3,iqr,nq)]; 
         qa[4]= wl*sq[ADDR(4,iql,nq)]+wr*sq[ADDR(4,iqr,nq)]; 

         for( ia=5;ia<nv;ia++ )
        { 
            //qa[ia]= wl*ql[ia][iql]+wr*qr[ia][iqr]; 
            qa[ia]= wl*sq[ADDR(ia,iql,nq)]+wr*sq[ADDR(ia,iqr,nq)]; 
        }
         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- epa );
         aa= sqrt( a2a );
         raa=ra*aa;

         ua[0]= qa[0];
         ua[1]= qa[1];
         ua[2]= qa[2];

// Left eigenvectors

         dr= drr- drl;
         dp= dpr- dpl;

         //du[0]= dauxr[0][iqr]- dauxl[0][iql];
         //du[1]= dauxr[1][iqr]- dauxl[1][iql];
         //du[2]= dauxr[2][iqr]- dauxl[2][iql];
         du[0]= sdaux[ADDR(0,iqr,nq)]- sdaux[ADDR(0,iql,nq)];
         du[1]= sdaux[ADDR(1,iqr,nq)]- sdaux[ADDR(1,iql,nq)];
         du[2]= sdaux[ADDR(2,iqr,nq)]- sdaux[ADDR(2,iql,nq)];

         //una=  ua[0]*wc[0][ic];
         //una+= ua[1]*wc[1][ic];
         //una+= ua[2]*wc[2][ic];
         una=  ua[0]*swc[ADDR(0,ic,nfc)];
         una+= ua[1]*swc[ADDR(1,ic,nfc)];
         una+= ua[2]*swc[ADDR(2,ic,nfc)];

         dun= dunr- dunl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         //dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= du[0]- dun*swc[ADDR(0,ic,nfc)]; dw2[0]*= ra*la1;
         dw2[1]= du[1]- dun*swc[ADDR(1,ic,nfc)]; dw2[1]*= ra*la1;
         dw2[2]= du[2]- dun*swc[ADDR(2,ic,nfc)]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*ua[0];
         dw2a+= dw2[1]*ua[1];
         dw2a+= dw2[2]*ua[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*swc[ADDR(0,ic,nfc)];
         ana[1]= aa*swc[ADDR(1,ic,nfc)];
         ana[2]= aa*swc[ADDR(2,ic,nfc)];

         for( ia=5;ia<nv;ia++ )
        {
            //dw5[ia]= ra*la1*( dauxr[ia][iqr]- dauxl[ia][iql] );
            dw5[ia]= ra*la1*( sdaux[ADDR(ia,iqr,nq)]- sdaux[ADDR(ia,iql,nq)] );
        }

         unaa=aa*una;

// Roe fluxes
         fa[0]=   dw1+               dw3+                  dw4;
         fa[1]=   dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
         fa[2]=   dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
         fa[3]=   dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] );
         fa[4]=   dw1*epa+    dw2a+  dw3*( ha+ unaa)+      dw4*( ha- unaa );

         for( ia=5;ia<nv;ia++ ){ fa[ia]= fa[0]*qa[ia]+ dw5[ia]; }

// assemble 

         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //res[0][iql]-= f[0];
         //res[1][iql]-= f[1];
         //res[2][iql]-= f[2];
         //res[3][iql]-= f[3];
         //res[4][iql]-= f[4];
         #pragma acc atomic
         sres[ADDR_(0,iql,nq)]-= f[0];
         #pragma acc atomic
         sres[ADDR_(1,iql,nq)]-= f[1];
         #pragma acc atomic
         sres[ADDR_(2,iql,nq)]-= f[2];
         #pragma acc atomic
         sres[ADDR_(3,iql,nq)]-= f[3];
         #pragma acc atomic
         sres[ADDR_(4,iql,nq)]-= f[4];

         //res[0][iqr]+= f[0];
         //res[1][iqr]+= f[1];
         //res[2][iqr]+= f[2];
         //res[3][iqr]+= f[3];
         //res[4][iqr]+= f[4];
         #pragma acc atomic
         sres[ADDR_(0,iqr,nq)]+= f[0];
         #pragma acc atomic
         sres[ADDR_(1,iqr,nq)]+= f[1];
         #pragma acc atomic
         sres[ADDR_(2,iqr,nq)]+= f[2];
         #pragma acc atomic
         sres[ADDR_(3,iqr,nq)]+= f[3];
         #pragma acc atomic
         sres[ADDR_(4,iqr,nq)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //res[ia][iql]-= f[ia];
            //res[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic
            sres[ADDR_(ia,iql,nq)]-= f[ia];
            #pragma acc atomic
            sres[ADDR_(ia,iqr,nq)]+= f[ia];
        }
     }
     #pragma acc exit data delete (this)
  }

   void cMfJanafGas::diflxb22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] )
  {
       diflx22( ics,ice, icql,ql,auxl,dql,dauxl,resl, icqr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
  }

   void cMfJanafGas::diflxb23( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] )
  {
       diflx23( ics,ice, icql,ql,auxl,dql,dauxl,resl, icqr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
  }

   void cMfJanafGas::diflxb33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] )
  {
       diflx33( ics,ice, icql,ql,auxl,dql,dauxl,resl, icqr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
  }


   void cMfJanafGas::iflxmuscl22( Int ics,Int ice, Int *icql, Int idl, Real *xql[], Real *ql0[], Real *dxdxl[], Real **dqdxl[], Real *auxl0[], Real *rhsl[],
                                                Int *icqr, Int idr, Real *xqr[], Real *qr0[], Real *dxdxr[], Real **dqdxr[], Real *auxr0[], Real *rhsr[], 
                                                                    Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,fl[MxNVs],ql[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,fr[MxNVs],qr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,epa,ka,qa[MxNVs],ua[3],ana[3],una,unaa,raa,cva,ta, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];
      Int             ia,ic,iql,iqr,isp,ir;
      Real            rg1= runi/unit[2];
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Real            xn[3],wn[3];



      Real h0sp[MxNsp],cvsp[MxNsp];
      Real a0,a1,a2,a3,a4,a5;

      for( ic=ics;ic<ice;ic++ )
     {

         wn[0]= wc[0][ic];
         wn[1]= wc[1][ic];

         xn[0]= xc[0][ic];
         xn[1]= xc[1][ic];

         iql= ic;
         if( icql ){ iql= icql[ic]; }
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; }
         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );

         for( ia=0;ia<nv;ia++ )
        {
            ql[ia]= ql0[ia][iql]+ dql[ia];
            qr[ia]= qr0[ia][iqr]+ dqr[ia];
        }

// fluxes from the left
         tl=  ql[2];
         pl=  ql[3];

         auxl[0]= pl/( rg*tl );

         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];

         hl= 0;
         cvl= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tl > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvsp[isp]= ( a0-1+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            cvl+= cvsp[isp]*ssp[isp];

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            h0sp[isp]= a5+ tl*( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            hl+= h0sp[isp]*ssp[isp];

        }
         
         cvl*= rg1;
         gam= (cvl+rg)/cvl;

         hl*= rg1;
         auxl[1]*= 0.5;

         auxl[2]= gam*rg* tl;
         auxl[3]= hl+ auxl[1];
         auxl[2]= sqrt( auxl[2] );
         auxl[4]= cvl+rg;

         rl=  auxl[0];
         kl=  auxl[1];
         al=  auxl[2];
         hl=  auxl[3];
         cvl= auxl[4];
         cvl-= rg;
         e0l= hl-kl-rg*tl;
         unl=  wc[0][ic]*ql[0];
         unl+= wc[1][ic]*ql[1];

         ll1= unl-wxdc[0][ic];
         ll3= unl+ al;
         ll4= unl- al;

         fl[0]= ll1*rl;
         fl[1]= fl[0]*ql[0]+ wc[0][ic]*pl;
         fl[2]= fl[0]*ql[1]+ wc[1][ic]*pl;
         fl[3]= fl[0]*hl+ wxdc[0][ic]*pl;
         for( ia=4;ia<nv;ia++ )
        {  
            fl[ia]= fl[0]*ql[ia];
        }

// fluxes from the right
         tr=  qr[2];
         pr=  qr[3];

         tr=  qr[2];
         pr=  qr[3];

         auxr[0]= pr/( rg*tr );

         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];

         hr= 0;
         cvr= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tr > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvsp[isp]= ( a0-1+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            cvr+= cvsp[isp]*ssp[isp];

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            h0sp[isp]= a5+ tr*( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            hr+= h0sp[isp]*ssp[isp];

        }
         
         cvr*= rg1;
         gam= (cvr+rg)/cvr;

         hr*= rg1;
         auxr[1]*= 0.5;

         auxr[2]= gam*rg* tr;
         auxr[3]= hr+ auxr[1];
         auxr[2]= sqrt( auxr[2] );
         auxr[4]= cvr+rg;

         rr=  auxr[0];
         kr=  auxr[1];
         ar=  auxr[2];
         hr=  auxr[3];
         cvr= auxr[4];
         cvr-= rg;
         e0r= hr-kr-rg*tr;
         unr=  wc[0][ic]*qr[0];
         unr+= wc[1][ic]*qr[1];

         lr1= unr-wxdc[0][ic];
         lr3= unr+ ar;
         lr4= unr- ar;

         fr[0]= lr1 *rr;
         fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         fr[3]= fr[0]*hr+ wxdc[0][ic]*pr;
         for( ia=4;ia<nv;ia++ )
        {  
            fr[ia]= fr[0]*qr[ia];
        }


// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;
         if( fabs( tl-tr ) > 1.e-6 )
        {
            cva= (e0r-e0l)/(tr-tl);
        }
         else
        {
            cva= 0.5*(cvr+cvl);
        }
         gam= (cva+rg)/cva;

         ka= 0.;
         una= 0;
         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         for( ia=4;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }
         ua[0]= qa[0];
         ua[1]= qa[1];

         ka=  ua[0]*ua[0];
         ka+= ua[1]*ua[1];

         una=  ua[0]* wc[0][ic];
         una+= ua[1]* wc[1][ic];

         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         ta= wl*tl+ wr*tr;
         epa=wl*e0l+wr*e0r;
         epa=epa-cva*ta;
         epa+=ka;
         a2a= (gam-1.)*( ha- epa );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         la1= una-wxdc[0][ic];
         la3= una+ aa;
         la4= una- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 );
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 );

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);

         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         auxc[0][ic]= wl;
         auxc[1][ic]= wr;
         auxc[2][ic]= ra;
         auxc[3][ic]= epa;
         auxc[4][ic]= la1;
         auxc[5][ic]= la3;
         auxc[6][ic]= la4;
         auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

         dr= rr- rl;
         du[0]= qr[0]- ql[0];
         du[1]= qr[1]- ql[1];
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         dw2a= 0.;
         dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;

         dw2a=  dw2[0]*ua[0];
         dw2a+= dw2[1]*ua[1];

         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];

         for( ia=4;ia<nv;ia++ )
        {
            dw5[ia]= ra*la1*( qr[ia]- ql[ia] );
        } 
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
         fa[2]= dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
         fa[3]= dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );

         for( ia=4;ia<nv;ia++ ){ fa[ia]=  fa[0]*qa[ia]+ dw5[ia]; };

// assemble 
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[2][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[2][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[2][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[2][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];

         for( ia=4;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[2][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }
   
         auxc[nauxf-1][ic]*= wc[2][ic];

     }
  }

   void cMfJanafGas::iflxmuscl23( Int ics,Int ice, Int *icql, Int idl, Real *xql[], Real *ql0[], Real *dxdxl[], Real **dqdxl[], Real *auxl0[], Real *rhsl[],
                                                Int *icqr, Int idr, Real *xqr[], Real *qr0[], Real *dxdxr[], Real **dqdxr[], Real *auxr0[], Real *rhsr[], 
                                                                    Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,fl[MxNVs],ql[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,fr[MxNVs],qr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,epa,ka,qa[MxNVs],ua[3],ana[3],una,unaa,raa,cva,ta, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];
      Int             ia,ic,iql,iqr,isp,ir;
      Real            rg1= runi/unit[2];
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Real            xn[3],wn[3];



      Real h0sp[MxNsp],cvsp[MxNsp];
      Real a0,a1,a2,a3,a4,a5;

      for( ic=ics;ic<ice;ic++ )
     {

         wn[0]= wc[0][ic];
         wn[1]= wc[1][ic];

         xn[0]= xc[0][ic];
         xn[1]= xc[1][ic];

         iql= ic;
         if( icql ){ iql= icql[ic]; }
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; }
         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );

         for( ia=0;ia<nv;ia++ )
        {
            ql[ia]= ql0[ia][iql]+ dql[ia];
            qr[ia]= qr0[ia][iqr]+ dqr[ia];
        }

// fluxes from the left
         tl=  ql[3];
         pl=  ql[4];

         auxl[0]= pl/( rg*tl );

         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];

         hl= 0;
         cvl= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tl > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvsp[isp]= ( a0-1+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            cvl+= cvsp[isp]*ssp[isp];

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            h0sp[isp]= a5+ tl*( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            hl+= h0sp[isp]*ssp[isp];

        }
         
         cvl*= rg1;
         gam= (cvl+rg)/cvl;

         hl*= rg1;
         auxl[1]*= 0.5;

         auxl[2]= gam*rg* tl;
         auxl[3]= hl+ auxl[1];
         auxl[2]= sqrt( auxl[2] );
         auxl[4]= cvl+rg;

         rl=  auxl[0];
         kl=  auxl[1];
         al=  auxl[2];
         hl=  auxl[3];
         cvl= auxl[4];
         cvl-= rg;
         e0l= hl-kl-rg*tl;
         unl=  wc[0][ic]*ql[0];
         unl+= wc[1][ic]*ql[1];

         ll1= unl-wxdc[0][ic];
         ll3= unl+ al;
         ll4= unl- al;

         fl[0]= ll1*rl;
         fl[1]= fl[0]*ql[0]+ wc[0][ic]*pl;
         fl[2]= fl[0]*ql[1]+ wc[1][ic]*pl;
         fl[3]= fl[0]*ql[2];
         fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
         for( ia=5;ia<nv;ia++ )
        {  
            fl[ia]= fl[0]*ql[ia];
        }

// fluxes from the right

         tr=  qr[3];
         pr=  qr[4];

         auxr[0]= pr/( rg*tr );

         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];

         hr= 0;
         cvr= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tr > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvsp[isp]= ( a0-1+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            cvr+= cvsp[isp]*ssp[isp];

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            h0sp[isp]= a5+ tr*( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            hr+= h0sp[isp]*ssp[isp];

        }
         
         cvr*= rg1;
         gam= (cvr+rg)/cvr;

         hr*= rg1;
         auxr[1]*= 0.5;

         auxr[2]= gam*rg* tr;
         auxr[3]= hr+ auxr[1];
         auxr[2]= sqrt( auxr[2] );
         auxr[4]= cvr+rg;

         rr=  auxr[0];
         kr=  auxr[1];
         ar=  auxr[2];
         hr=  auxr[3];
         cvr= auxr[4];
         cvr-= rg;
         e0r= hr-kr-rg*tr;
         unr=  wc[0][ic]*qr[0];
         unr+= wc[1][ic]*qr[1];

         lr1= unr-wxdc[0][ic];
         lr3= unr+ ar;
         lr4= unr- ar;

         fr[0]= lr1 *rr;
         fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         fr[3]= fr[0]*qr[2];
         fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
         for( ia=5;ia<nv;ia++ )
        {  
            fr[ia]= fr[0]*qr[ia];
        }


// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;
         if( fabs( tl-tr ) > 1.e-6 )
        {
            cva= (e0r-e0l)/(tr-tl);
        }
         else
        {
            cva= 0.5*(cvr+cvl);
        }
         gam= (cva+rg)/cva;

         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }
         ua[0]= qa[0];
         ua[1]= qa[1];
         ua[2]= qa[2];

         ka=  ua[0]*ua[0];
         ka+= ua[1]*ua[1];
         ka+= ua[2]*ua[2];

         una=  ua[0]* wc[0][ic];
         una+= ua[1]* wc[1][ic];

         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         ta= wl*tl+ wr*tr;
         epa=wl*e0l+wr*e0r;
         epa=epa-cva*ta;
         epa+=ka;
         a2a= (gam-1.)*( ha- epa );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         la1= una-wxdc[0][ic];
         la3= una+ aa;
         la4= una- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 );
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 );

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);

         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         auxc[0][ic]= wl;
         auxc[1][ic]= wr;
         auxc[2][ic]= ra;
         auxc[3][ic]= epa;
         auxc[4][ic]= la1;
         auxc[5][ic]= la3;
         auxc[6][ic]= la4;
         auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

         dr= rr- rl;
         du[0]= qr[0]- ql[0];
         du[1]= qr[1]- ql[1];
         du[2]= qr[2]- ql[2];
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         dw2a= 0.;
         dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2[2]= du[2];                dw2[2]*= ra*la1;

         dw2a=  dw2[0]*ua[0];
         dw2a+= dw2[1]*ua[1];
         dw2a+= dw2[2]*ua[2];

         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= ra*la1*( qr[ia]- ql[ia] );
        } 
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+                 dw3+                  dw4;
         fa[1]= dw1*ua[0]+   dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
         fa[2]= dw1*ua[1]+   dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
         fa[3]= fa[0]*ua[2]+ dw2[2];
         fa[4]= dw1*epa+     dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );

         for( ia=5;ia<nv;ia++ ){ fa[ia]=  fa[0]*qa[ia]+ dw5[ia]; };

// assemble 
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[2][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[2][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[2][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[2][ic];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[2][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];
         rhsl[4][iql]-= f[4];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];
         rhsr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[2][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }
   
         auxc[nauxf-1][ic]*= wc[2][ic];

     }
  }

   void cMfJanafGas::iflxmuscl33( Int ics,Int ice, Int *icql, Int idl, Real *xql[], Real *ql0[], Real *dxdxl[], Real **dqdxl[], Real *auxl0[], Real *rhsl[],
                                                Int *icqr, Int idr, Real *xqr[], Real *qr0[], Real *dxdxr[], Real **dqdxr[], Real *auxr0[], Real *rhsr[], 
                                                                    Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,fl[MxNVs],ql[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,fr[MxNVs],qr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,epa,ka,qa[MxNVs],ua[3],ana[3],una,unaa,raa,cva,ta, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];
      Int             ia,ic,iql,iqr,isp,ir;
      Real            rg1= runi/unit[2];
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Real            xn[3],wn[3];



      Real h0sp[MxNsp],cvsp[MxNsp];
      Real a0,a1,a2,a3,a4,a5;

      for( ic=ics;ic<ice;ic++ )
     {

         wn[0]= wc[0][ic];
         wn[1]= wc[1][ic];
         wn[2]= wc[2][ic];

         xn[0]= xc[0][ic];
         xn[1]= xc[1][ic];
         xn[2]= xc[2][ic];

         iql= ic;
         if( icql ){ iql= icql[ic]; }
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; }
         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );

         for( ia=0;ia<nv;ia++ )
        {
            ql[ia]= ql0[ia][iql]+ dql[ia];
            qr[ia]= qr0[ia][iqr]+ dqr[ia];
        }

// fluxes from the left
         tl=  ql[3];
         pl=  ql[4];

         auxl[0]= pl/( rg*tl );

         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];

         hl= 0;
         cvl= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tl > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvsp[isp]= ( a0-1+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            cvl+= cvsp[isp]*ssp[isp];

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            h0sp[isp]= a5+ tl*( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            hl+= h0sp[isp]*ssp[isp];

        }
         
         cvl*= rg1;
         gam= (cvl+rg)/cvl;

         hl*= rg1;
         auxl[1]*= 0.5;

         auxl[2]= gam*rg* tl;
         auxl[3]= hl+ auxl[1];
         auxl[2]= sqrt( auxl[2] );
         auxl[4]= cvl+rg;

         rl=  auxl[0];
         kl=  auxl[1];
         al=  auxl[2];
         hl=  auxl[3];
         cvl= auxl[4];
         cvl-= rg;
         e0l= hl-kl-rg*tl;
         unl=  wc[0][ic]*ql[0];
         unl+= wc[1][ic]*ql[1];
         unl+= wc[2][ic]*ql[2];

         ll1= unl-wxdc[0][ic];
         ll3= unl+ al;
         ll4= unl- al;

         fl[0]= ll1*rl;
         fl[1]= fl[0]*ql[0]+ wc[0][ic]*pl;
         fl[2]= fl[0]*ql[1]+ wc[1][ic]*pl;
         fl[3]= fl[0]*ql[2]+ wc[2][ic]*pl;
         fl[4]= fl[0]*hl+    wxdc[0][ic]*pl;
         for( ia=5;ia<nv;ia++ )
        {  
            fl[ia]= fl[0]*ql[ia];
        }

// fluxes from the right
         tr=  qr[3];
         pr=  qr[4];

         auxr[0]= pr/( rg*tr );

         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];

         hr= 0;
         cvr= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tr > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvsp[isp]= ( a0-1+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            cvr+= cvsp[isp]*ssp[isp];

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            h0sp[isp]= a5+ tr*( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            hr+= h0sp[isp]*ssp[isp];

        }
         
         cvr*= rg1;
         gam= (cvr+rg)/cvr;

         hr*= rg1;
         auxr[1]*= 0.5;

         auxr[2]= gam*rg* tr;
         auxr[3]= hr+ auxr[1];
         auxr[2]= sqrt( auxr[2] );
         auxr[4]= cvr+rg;

         rr=  auxr[0];
         kr=  auxr[1];
         ar=  auxr[2];
         hr=  auxr[3];
         cvr= auxr[4];
         cvr-= rg;
         e0r= hr-kr-rg*tr;
         unr=  wc[0][ic]*qr[0];
         unr+= wc[1][ic]*qr[1];
         unr+= wc[2][ic]*qr[2];

         lr1= unr-wxdc[0][ic];
         lr3= unr+ ar;
         lr4= unr- ar;

         fr[0]= lr1 *rr;
         fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         fr[3]= fr[0]*qr[2]+ wc[2][ic]*pr;
         fr[4]= fr[0]*hr+    wxdc[0][ic]*pr;
         for( ia=5;ia<nv;ia++ )
        {  
            fr[ia]= fr[0]*qr[ia];
        }


// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;
         if( fabs( tl-tr ) > 1.e-6 )
        {
            cva= (e0r-e0l)/(tr-tl);
        }
         else
        {
            cva= 0.5*(cvr+cvl);
        }
         gam= (cva+rg)/cva;

         ka= 0.;
         una= 0;
         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }
         ua[0]= qa[0];
         ua[1]= qa[1];
         ua[2]= qa[2];

         ka=  ua[0]*ua[0];
         ka+= ua[1]*ua[1];
         ka+= ua[2]*ua[2];

         una=  ua[0]* wc[0][ic];
         una+= ua[1]* wc[1][ic];
         una+= ua[2]* wc[2][ic];

         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         ta= wl*tl+ wr*tr;
         epa=wl*e0l+wr*e0r;
         epa=epa-cva*ta;
         epa+=ka;
         a2a= (gam-1.)*( ha- epa );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         la1= una-wxdc[0][ic];
         la3= una+ aa;
         la4= una- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 );
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 );

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);

         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         auxc[0][ic]= wl;
         auxc[1][ic]= wr;
         auxc[2][ic]= ra;
         auxc[3][ic]= epa;
         auxc[4][ic]= la1;
         auxc[5][ic]= la3;
         auxc[6][ic]= la4;
         auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

         dr= rr- rl;
         du[0]= qr[0]- ql[0];
         du[1]= qr[1]- ql[1];
         du[2]= qr[2]- ql[2];
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         dw2a= 0.;
         dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*ua[0];
         dw2a+= dw2[1]*ua[1];
         dw2a+= dw2[2]*ua[2];

         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];
         ana[2]= aa*wc[2][ic];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= ra*la1*( qr[ia]- ql[ia] );
        } 
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
         fa[2]= dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
         fa[3]= dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] );
         fa[4]= dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );

         for( ia=5;ia<nv;ia++ ){ fa[ia]=  fa[0]*qa[ia]+ dw5[ia]; };

// assemble 
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];
         rhsl[4][iql]-= f[4];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];
         rhsr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }
   
         auxc[nauxf-1][ic]*= wc[3][ic];

     }
  }

   void cMfJanafGas::iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icql_view, Int idl, cAu3xView<Real>& xql, cAu3xView<Real>& ql0, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& auxl0, cAu3xView<Real>& rhsl,
                                                   cAu3xView<Int>& icqr_view, Int idr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr0, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& auxr0, cAu3xView<Real>& rhsr,
                                                   cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder )
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,fl[MxNVs],ql[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,fr[MxNVs],qr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,epa,ka,qa[MxNVs],ua[3],ana[3],una,unaa,raa,cva,ta, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];
      Int             ia,ic,iql,iqr,isp,ir;
      Real            rg1= runi/unit[2];
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Real            xn[3],wn[3];



      Real h0sp[MxNsp],cvsp[MxNsp];
      Real a0,a1,a2,a3,a4,a5;

      Int nfc, nq, nql, nqr;
      Int *icql;
      Real *sxql, *sql, *sdxdxl, *sdqdxl, *sauxl, *srhsl;
      Int *icqr;
      Real *sxqr, *sqr, *sdxdxr, *sdqdxr, *sauxr, *srhsr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr0.get_dim1();
      nql = nfc;
      nqr = nq;

      icql   = icql_view.get_data();
      sxql   = xql.get_data();
      sql    = ql0.get_data();
      sdxdxl = dxdxl.get_data();
      sdqdxl = dqdxl.get_data();
      sauxl  = auxl0.get_data();
      srhsl  = rhsl.get_data();
      icqr   = icqr_view.get_data();
      sxqr   = xqr.get_data();
      sqr    = qr0.get_data();
      sdxdxr = dxdxr.get_data();
      sdqdxr = dqdxr.get_data();
      sauxr  = auxr0.get_data();
      srhsr  = rhsr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      for( ic=ics;ic<ice;ic++ )
     {

         //wn[0]= wc[0][ic];
         //wn[1]= wc[1][ic];
         //wn[2]= wc[2][ic];
         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];

         //xn[0]= xc[0][ic];
         //xn[1]= xc[1][ic];
         //xn[2]= xc[2][ic];
         xn[0]= sxc[ADDR(0,ic,nfc)];
         xn[1]= sxc[ADDR(1,ic,nfc)];
         xn[2]= sxc[ADDR(2,ic,nfc)];

         iql= ic;
         iqr= icqr[ADDR(0,ic,nfc)];

//         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );
//
//         for( ia=0;ia<nv;ia++ )
//        {
//            ql[ia]= ql0[ia][iql]+ dql[ia];
//            qr[ia]= qr0[ia][iqr]+ dqr[ia];
//        }
         deltq( nv, iql, sxql, sql, sdxdxl, sdqdxl,
                    iqr, sxqr, sqr, sdxdxr, sdqdxr,
                    xn, wn,  dql0, dqr0, dql, dqr, nfc, nq );

         for( ia=0;ia<nv;ia++ )
        {
            ql[ia]= sql[ADDR(ia,iql,nql)]+ (iorder-1)*dql[ia];
            qr[ia]= sqr[ADDR(ia,iqr,nqr)]+ (iorder-1)*dqr[ia];
        }


// fluxes from the left
         tl=  ql[3];
         pl=  ql[4];

         auxl[0]= pl/( rg*tl );

         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];

         hl= 0;
         cvl= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tl > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvsp[isp]= ( a0-1+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            cvl+= cvsp[isp]*ssp[isp];

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            h0sp[isp]= a5+ tl*( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            hl+= h0sp[isp]*ssp[isp];

        }
         
         cvl*= rg1;
         gam= (cvl+rg)/cvl;

         hl*= rg1;
         auxl[1]*= 0.5;

         auxl[2]= gam*rg* tl;
         auxl[3]= hl+ auxl[1];
         auxl[2]= sqrt( auxl[2] );
         auxl[4]= cvl+rg;

         rl=  auxl[0];
         kl=  auxl[1];
         al=  auxl[2];
         hl=  auxl[3];
         cvl= auxl[4];
         cvl-= rg;
         e0l= hl-kl-rg*tl;
         //unl=  wc[0][ic]*ql[0];
         //unl+= wc[1][ic]*ql[1];
         //unl+= wc[2][ic]*ql[2];
         unl=  swc[ADDR(0,ic,nfc)]*ql[0];
         unl+= swc[ADDR(1,ic,nfc)]*ql[1];
         unl+= swc[ADDR(2,ic,nfc)]*ql[2];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0]+ wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1]+ wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2]+ wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+    wxdc[0][ic]*pl;
         fl[1]= fl[0]*ql[0]+ swc[ADDR(0,ic,nfc)]*pl;
         fl[2]= fl[0]*ql[1]+ swc[ADDR(1,ic,nfc)]*pl;
         fl[3]= fl[0]*ql[2]+ swc[ADDR(2,ic,nfc)]*pl;
         fl[4]= fl[0]*hl+    swxdc[ADDR(0,ic,nfc)]*pl;
         for( ia=5;ia<nv;ia++ )
        {  
            fl[ia]= fl[0]*ql[ia];
        }

// fluxes from the right
         tr=  qr[3];
         pr=  qr[4];

         auxr[0]= pr/( rg*tr );

         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];

         hr= 0;
         cvr= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tr > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvsp[isp]= ( a0-1+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            cvr+= cvsp[isp]*ssp[isp];

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            h0sp[isp]= a5+ tr*( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            hr+= h0sp[isp]*ssp[isp];

        }
         
         cvr*= rg1;
         gam= (cvr+rg)/cvr;

         hr*= rg1;
         auxr[1]*= 0.5;

         auxr[2]= gam*rg* tr;
         auxr[3]= hr+ auxr[1];
         auxr[2]= sqrt( auxr[2] );
         auxr[4]= cvr+rg;

         rr=  auxr[0];
         kr=  auxr[1];
         ar=  auxr[2];
         hr=  auxr[3];
         cvr= auxr[4];
         cvr-= rg;
         e0r= hr-kr-rg*tr;
         //unr=  wc[0][ic]*qr[0];
         //unr+= wc[1][ic]*qr[1];
         //unr+= wc[2][ic]*qr[2];
         unr=  swc[ADDR(0,ic,nfc)]*qr[0];
         unr+= swc[ADDR(1,ic,nfc)]*qr[1];
         unr+= swc[ADDR(2,ic,nfc)]*qr[2];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         //fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+    wxdc[0][ic]*pr;
         fr[1]= fr[0]*qr[0]+ swc[ADDR(0,ic,nfc)]*pr;
         fr[2]= fr[0]*qr[1]+ swc[ADDR(1,ic,nfc)]*pr;
         fr[3]= fr[0]*qr[2]+ swc[ADDR(2,ic,nfc)]*pr;
         fr[4]= fr[0]*hr+    swxdc[ADDR(0,ic,nfc)]*pr;
         for( ia=5;ia<nv;ia++ )
        {  
            fr[ia]= fr[0]*qr[ia];
        }


// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;
         if( fabs( tl-tr ) > 1.e-6 )
        {
            cva= (e0r-e0l)/(tr-tl);
        }
         else
        {
            cva= 0.5*(cvr+cvl);
        }
         gam= (cva+rg)/cva;

         ka= 0.;
         una= 0;
         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }
         ua[0]= qa[0];
         ua[1]= qa[1];
         ua[2]= qa[2];

         ka=  ua[0]*ua[0];
         ka+= ua[1]*ua[1];
         ka+= ua[2]*ua[2];

         //una=  ua[0]* wc[0][ic];
         //una+= ua[1]* wc[1][ic];
         //una+= ua[2]* wc[2][ic];
         una=  ua[0]* swc[ADDR(0,ic,nfc)];
         una+= ua[1]* swc[ADDR(1,ic,nfc)];
         una+= ua[2]* swc[ADDR(2,ic,nfc)];

         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         ta= wl*tl+ wr*tr;
         epa=wl*e0l+wr*e0r;
         epa=epa-cva*ta;
         epa+=ka;
         a2a= (gam-1.)*( ha- epa );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 );
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 );

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);

         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= epa;
         //auxc[4][ic]= la1;
         //auxc[5][ic]= la3;
         //auxc[6][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= epa;
         sauxc[ADDR(4,ic,nfc)]= la1;
         sauxc[ADDR(5,ic,nfc)]= la3;
         sauxc[ADDR(6,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         du[0]= qr[0]- ql[0];
         du[1]= qr[1]- ql[1];
         du[2]= qr[2]- ql[2];
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         dw2a= 0.;
         //dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= du[0]- dun*swc[ADDR(0,ic,nfc)]; dw2[0]*= ra*la1;
         dw2[1]= du[1]- dun*swc[ADDR(1,ic,nfc)]; dw2[1]*= ra*la1;
         dw2[2]= du[2]- dun*swc[ADDR(2,ic,nfc)]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*ua[0];
         dw2a+= dw2[1]*ua[1];
         dw2a+= dw2[2]*ua[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*swc[ADDR(0,ic,nfc)];
         ana[1]= aa*swc[ADDR(1,ic,nfc)];
         ana[2]= aa*swc[ADDR(2,ic,nfc)];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= ra*la1*( qr[ia]- ql[ia] );
        } 
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
         fa[2]= dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
         fa[3]= dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] );
         fa[4]= dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );

         for( ia=5;ia<nv;ia++ ){ fa[ia]=  fa[0]*qa[ia]+ dw5[ia]; };

// assemble 
         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*swc[ADDR(3,ic,nfc)];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*swc[ADDR(3,ic,nfc)];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*swc[ADDR(3,ic,nfc)];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*swc[ADDR(3,ic,nfc)];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*swc[ADDR(3,ic,nfc)];

         //rhsl[0][iql]-= f[0];
         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         srhsl[ADDR(0,iql,nql)]-= f[0];
         srhsl[ADDR(1,iql,nql)]-= f[1];
         srhsl[ADDR(2,iql,nql)]-= f[2];
         srhsl[ADDR(3,iql,nql)]-= f[3];
         srhsl[ADDR(4,iql,nql)]-= f[4];

         //rhsr[0][iqr]+= f[0];
         //rhsr[1][iqr]+= f[1];
         //rhsr[2][iqr]+= f[2];
         //rhsr[3][iqr]+= f[3];
         //rhsr[4][iqr]+= f[4];
         srhsr[ADDR(0,iqr,nqr)]+= f[0];
         srhsr[ADDR(1,iqr,nqr)]+= f[1];
         srhsr[ADDR(2,iqr,nqr)]+= f[2];
         srhsr[ADDR(3,iqr,nqr)]+= f[3];
         srhsr[ADDR(4,iqr,nqr)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*swc[ADDR(3,ic,nfc)];
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            srhsl[ADDR(ia,iql,nql)]-= f[ia];
            srhsr[ADDR(ia,iqr,nqr)]+= f[ia];
        }
   
         //auxc[nauxf-1][ic]*= wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]*= swc[ADDR(3,ic,nfc)];

     }
  }

   void cMfJanafGas::iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder )
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,fl[MxNVs],ql[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,fr[MxNVs],qr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,epa,ka,qa[MxNVs],ua[3],ana[3],una,unaa,raa,cva,ta, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];
      Int             ia,ic,iql,iqr,isp,ir;
      Real            rg1= runi/unit[2];
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Real            xn[3],wn[3];

      Real h0sp[MxNsp],cvsp[MxNsp];
      Real a0,a1,a2,a3,a4,a5;

      Int nfc, nq;
      Int *sicq;
      Real *sxq, *sq, *sdxdx, *sdqdx, *saux, *srhs;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = q.get_dim1();
         
      sicq  = icq.get_data();
      sxq   = xq.get_data();
      sq    = q.get_data();
      sdxdx = dxdx.get_data();
      sdqdx = dqdx.get_data();
      saux  = aux.get_data();
      srhs  = rhs.get_data();
      sxc   = xc.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();


      for( ic=ics;ic<ice;ic++ )
     {

         //wn[0]= wc[0][ic];
         //wn[1]= wc[1][ic];
         //wn[2]= wc[2][ic];
         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];

         //xn[0]= xc[0][ic];
         //xn[1]= xc[1][ic];
         //xn[2]= xc[2][ic];
         xn[0]= sxc[ADDR(0,ic,nfc)];
         xn[1]= sxc[ADDR(1,ic,nfc)];
         xn[2]= sxc[ADDR(2,ic,nfc)];

         iql = sicq[ADDR(0,ic,nfc)];
         iqr = sicq[ADDR(1,ic,nfc)];
//         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );
//
//         for( ia=0;ia<nv;ia++ )
//        {
//            ql[ia]= ql0[ia][iql]+ dql[ia];
//            qr[ia]= qr0[ia][iqr]+ dqr[ia];
//        }
         deltq( nv,iql,iqr,sxq,sq,sdxdx,sdqdx,xn,wn,dql0,dqr0,dql,dqr,nq );

         for( ia=0;ia<nv;ia++ )
        {
            ql[ia]= sq[ADDR(ia,iql,nq)]+ (iorder-1)*dql[ia];
            qr[ia]= sq[ADDR(ia,iqr,nq)]+ (iorder-1)*dqr[ia];
        }


// fluxes from the left
         tl=  ql[3];
         pl=  ql[4];

         auxl[0]= pl/( rg*tl );

         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];

         hl= 0;
         cvl= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tl > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvsp[isp]= ( a0-1+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            cvl+= cvsp[isp]*ssp[isp];

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            h0sp[isp]= a5+ tl*( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            hl+= h0sp[isp]*ssp[isp];

        }
         
         cvl*= rg1;
         gam= (cvl+rg)/cvl;

         hl*= rg1;
         auxl[1]*= 0.5;

         auxl[2]= gam*rg* tl;
         auxl[3]= hl+ auxl[1];
         auxl[2]= sqrt( auxl[2] );
         auxl[4]= cvl+rg;

         rl=  auxl[0];
         kl=  auxl[1];
         al=  auxl[2];
         hl=  auxl[3];
         cvl= auxl[4];
         cvl-= rg;
         e0l= hl-kl-rg*tl;
         //unl=  wc[0][ic]*ql[0];
         //unl+= wc[1][ic]*ql[1];
         //unl+= wc[2][ic]*ql[2];
         unl=  swc[ADDR(0,ic,nfc)]*ql[0];
         unl+= swc[ADDR(1,ic,nfc)]*ql[1];
         unl+= swc[ADDR(2,ic,nfc)]*ql[2];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0]+ wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1]+ wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2]+ wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+    wxdc[0][ic]*pl;
         fl[1]= fl[0]*ql[0]+ swc[ADDR(0,ic,nfc)]*pl;
         fl[2]= fl[0]*ql[1]+ swc[ADDR(1,ic,nfc)]*pl;
         fl[3]= fl[0]*ql[2]+ swc[ADDR(2,ic,nfc)]*pl;
         fl[4]= fl[0]*hl+    swxdc[ADDR(0,ic,nfc)]*pl;
         for( ia=5;ia<nv;ia++ )
        {  
            fl[ia]= fl[0]*ql[ia];
        }

// fluxes from the right
         tr=  qr[3];
         pr=  qr[4];

         auxr[0]= pr/( rg*tr );

         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];

         hr= 0;
         cvr= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tr > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvsp[isp]= ( a0-1+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            cvr+= cvsp[isp]*ssp[isp];

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            h0sp[isp]= a5+ tr*( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            hr+= h0sp[isp]*ssp[isp];

        }
         
         cvr*= rg1;
         gam= (cvr+rg)/cvr;

         hr*= rg1;
         auxr[1]*= 0.5;

         auxr[2]= gam*rg* tr;
         auxr[3]= hr+ auxr[1];
         auxr[2]= sqrt( auxr[2] );
         auxr[4]= cvr+rg;

         rr=  auxr[0];
         kr=  auxr[1];
         ar=  auxr[2];
         hr=  auxr[3];
         cvr= auxr[4];
         cvr-= rg;
         e0r= hr-kr-rg*tr;
         //unr=  wc[0][ic]*qr[0];
         //unr+= wc[1][ic]*qr[1];
         //unr+= wc[2][ic]*qr[2];
         unr=  swc[ADDR(0,ic,nfc)]*qr[0];
         unr+= swc[ADDR(1,ic,nfc)]*qr[1];
         unr+= swc[ADDR(2,ic,nfc)]*qr[2];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         //fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+    wxdc[0][ic]*pr;
         fr[1]= fr[0]*qr[0]+ swc[ADDR(0,ic,nfc)]*pr;
         fr[2]= fr[0]*qr[1]+ swc[ADDR(1,ic,nfc)]*pr;
         fr[3]= fr[0]*qr[2]+ swc[ADDR(2,ic,nfc)]*pr;
         fr[4]= fr[0]*hr+    swxdc[ADDR(0,ic,nfc)]*pr;
         for( ia=5;ia<nv;ia++ )
        {  
            fr[ia]= fr[0]*qr[ia];
        }


// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;
         if( fabs( tl-tr ) > 1.e-6 )
        {
            cva= (e0r-e0l)/(tr-tl);
        }
         else
        {
            cva= 0.5*(cvr+cvl);
        }
         gam= (cva+rg)/cva;

         ka= 0.;
         una= 0;
         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }
         ua[0]= qa[0];
         ua[1]= qa[1];
         ua[2]= qa[2];

         ka=  ua[0]*ua[0];
         ka+= ua[1]*ua[1];
         ka+= ua[2]*ua[2];

         //una=  ua[0]* wc[0][ic];
         //una+= ua[1]* wc[1][ic];
         //una+= ua[2]* wc[2][ic];
         una=  ua[0]* swc[ADDR(0,ic,nfc)];
         una+= ua[1]* swc[ADDR(1,ic,nfc)];
         una+= ua[2]* swc[ADDR(2,ic,nfc)];

         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         ta= wl*tl+ wr*tr;
         epa=wl*e0l+wr*e0r;
         epa=epa-cva*ta;
         epa+=ka;
         a2a= (gam-1.)*( ha- epa );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 );
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 );

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);

         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= epa;
         //auxc[4][ic]= la1;
         //auxc[5][ic]= la3;
         //auxc[6][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= epa;
         sauxc[ADDR(4,ic,nfc)]= la1;
         sauxc[ADDR(5,ic,nfc)]= la3;
         sauxc[ADDR(6,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         du[0]= qr[0]- ql[0];
         du[1]= qr[1]- ql[1];
         du[2]= qr[2]- ql[2];
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         dw2a= 0.;
         //dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= du[0]- dun*swc[ADDR(0,ic,nfc)]; dw2[0]*= ra*la1;
         dw2[1]= du[1]- dun*swc[ADDR(1,ic,nfc)]; dw2[1]*= ra*la1;
         dw2[2]= du[2]- dun*swc[ADDR(2,ic,nfc)]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*ua[0];
         dw2a+= dw2[1]*ua[1];
         dw2a+= dw2[2]*ua[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*swc[ADDR(0,ic,nfc)];
         ana[1]= aa*swc[ADDR(1,ic,nfc)];
         ana[2]= aa*swc[ADDR(2,ic,nfc)];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= ra*la1*( qr[ia]- ql[ia] );
        } 
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
         fa[2]= dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
         fa[3]= dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] );
         fa[4]= dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );

         for( ia=5;ia<nv;ia++ ){ fa[ia]=  fa[0]*qa[ia]+ dw5[ia]; };

// assemble 
         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*swc[ADDR(3,ic,nfc)];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*swc[ADDR(3,ic,nfc)];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*swc[ADDR(3,ic,nfc)];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*swc[ADDR(3,ic,nfc)];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*swc[ADDR(3,ic,nfc)];

         //rhsl[0][iql]-= f[0];
         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         srhs[ADDR(0,iql,nq)]-= f[0];
         srhs[ADDR(1,iql,nq)]-= f[1];
         srhs[ADDR(2,iql,nq)]-= f[2];
         srhs[ADDR(3,iql,nq)]-= f[3];
         srhs[ADDR(4,iql,nq)]-= f[4];

         //rhsr[0][iqr]+= f[0];
         //rhsr[1][iqr]+= f[1];
         //rhsr[2][iqr]+= f[2];
         //rhsr[3][iqr]+= f[3];
         //rhsr[4][iqr]+= f[4];
         srhs[ADDR(0,iqr,nq)]+= f[0];
         srhs[ADDR(1,iqr,nq)]+= f[1];
         srhs[ADDR(2,iqr,nq)]+= f[2];
         srhs[ADDR(3,iqr,nq)]+= f[3];
         srhs[ADDR(4,iqr,nq)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*swc[ADDR(3,ic,nfc)];
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            srhs[ADDR(ia,iql,nq)]-= f[ia];
            srhs[ADDR(ia,iqr,nq)]+= f[ia];
        }
   
         //auxc[nauxf-1][ic]*= wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]*= swc[ADDR(3,ic,nfc)];

     }
  }
