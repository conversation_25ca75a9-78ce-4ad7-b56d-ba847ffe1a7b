using namespace std;

# include <field/gas.h>
# include <mem/proto.h>
# include <field/grad.h>

   cHeatConduction::cHeatConduction( cCosystem *Coo, cVisc *visc )
  {

      coo= Coo;
      vsc= visc;
      nx=  coo->getnx();
      nvel=coo->getnvel(); 

      nvk=3;
      nv=2+nvel;
      naux=7;
      nauxf=7;
      nlhs= 2;

      nv0= nv;
      naux0= naux;
      nauxf0= nauxf;
      nlhs0= nlhs;

      vsc->setvrs( nx,nvel, &nv,&naux,&nauxf,&nlhs );
      ilv[0]=nvel;
      ilv[1]=ilv[0]+1;
      ilv[2]=ilv[1]+1;
      unit[0]= 100.;
      unit[1]=   1.;
      unit[2]= unit[0]*unit[0];
      deflt[0]=   0.;
      deflt[1]= 298.;
      deflt[2]= 100000./unit[2];
      //rg= 0;
      //gam=0;
      //eps= 0.05;
      coo->setnv(nv);
  }


   void cHeatConduction::ilhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                                 cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                                 cAu3xView<Real>& wc,  cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             nql,nqr,iql,iqr,ic;

      Int nfc, nq;
      Int *sicql;
      Real *sql, *sauxl, *slhsl;
      Int *sicqr;
      Real *sqr, *sauxr, *slhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      sicql = icql.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      slhsl = lhsl.get_data();
      sicqr = icqr.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      slhsr = lhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(             sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
                 sicqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        { 

            iql= ic;
            iqr= sicqr[ADDR(0,ic,nfc)]; 
       
            //lhsl[0][iql]+= auxc[nauxf-1][ic];
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhsl[ADDR_(0,iql,nql)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhsr[ADDR_(0,iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
  }

   void cHeatConduction::ilhs( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& lhs,
                                           cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             iql,iqr,ic;

      Int nfc, nq;
      Int *sicq;  
      Real *sq, *saux, *slhs, *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = q.get_dim1();

      sicq  = icq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      slhs  = lhs.get_data();
      swc   = wc.get_data(); 
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {

        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],slhs[0:nlhs*nq], \
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this )\
         default(none)
         for( ic=ics;ic<ice;ic++ )
        { 

            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];
       
            //lhsl[0][iql]+= auxc[nauxf-1][ic];
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhs[ADDR_(0,iql,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhs[ADDR_(0,iqr,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
  }

   void cHeatConduction::wlhs( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                                 cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                                 cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            gam1,kr;
      Real            dp1,dp2,dp3,dp4,dp5;
      Real            dfr[MxNVs][MxNVs];

      Int             ic,ia,iql,iqr,ja;
      Int             nql,nqr;

      Int nfc, nq;
      Int *icql;
      Real *sql, *sauxl, *slhsl;
      Int *icqr;
      Real *sqr, *sauxr, *slhsr;  
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      slhsl = lhsl.get_data(); 
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      slhsr = lhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(            sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
                 icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic]; 
      
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhsr[ADDR_(0,iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
  }

   void cHeatConduction::vlhs( Int iqs, Int iqe, Real dtm,Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                               cAu3xView<Real>& wq, cAu3xView<Real>& lhs, Real rord )
  {
      Int             ia,ja,iq;
      Real            w,tau;

      Int nq;
      Real *sq, *saux, *sdqdx, *sdst, *swq, *slhs;

      nq    = q.get_dim1();

      sq    = q.get_data();
      saux  = aux.get_data();
      sdqdx = dqdx.get_data();
      sdst  = dst.get_data();
      swq   = wq.get_data(); 
      slhs  = lhs.get_data();

      if( iqe > iqs )
     {
         w= 1./cfl;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //lhs[nlhs-1][iq]=  lhs[0][iq]*w;
            //if(rord>0) lhs[nlhs-1][iq]+= rord*wq[0][iq]/dtm;
            //lhs[0][iq]+=     lhs[nlhs-1][iq];
            slhs[ADDR(nlhs-1,iq,nq)]=  slhs[ADDR(0,iq,nq)]*w;
            if(rord>0) slhs[ADDR(nlhs-1,iq,nq)]+= rord*swq[ADDR(0,iq,nq)]/dtm;
            slhs[ADDR(0,iq,nq)]+=     slhs[ADDR(nlhs-1,iq,nq)];

        }
        #pragma acc exit data delete(this)
     }
      vsc->vlhs( iqs,iqe, cfl, wq,lhs );
  }

   void cHeatConduction::invdg( Int iqs, Int iqe, cAu3xView<Real>& lhs, cAu3xView<Real>& res )
  {
      Int iv,iq;

      Int nq;
      Real *slhs, *sres;

      nq = lhs.get_dim1();
      slhs = lhs.get_data();
      sres = res.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present(sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv0;iv++ )
           {
               sres[ADDR(iv,iq,nq)]/= slhs[ADDR(0,iq,nq)]; 
           }
        }
        #pragma acc exit data copyout(this)
     }
      //vsc->invdg( iqs,iqe, lhs,res );
      vsc->invdg( iqs,iqe, lhs,res );
  }

   void cHeatConduction::dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view )
  {
      Int iq,ia;
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cp,e;
      Int nq;
      Real *q, *aux, *dU, *dq;

      nq = q_view.get_dim1();

      q = q_view.get_data();
      aux = aux_view.get_data();
      dU = dU_view.get_data();
      dq = dq_view.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop \
      present(q[0:nv*nq],aux[0:naux*nq],dU[0:nv*nq],dq[0:nv*nq],this) \
      default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         ro= aux[ADDR(0,iq,nq)];
         cp= aux[ADDR(4,iq,nq)];
         dre= dU[ADDR(4,iq,nq)];
         dt= dre/( ro*cp );

         dq[ADDR(3,iq,nq)]= dt;
     }
     #pragma acc exit data delete(this)
  }

   void cHeatConduction::auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch )
  {
      Int iq;
      Int iv;
      Real kappa=236;
      Real rho=2710;
      Real cp = 902;

      kappa/= (unit[0]*unit[0]*unit[0]);
      cp/=unit[0]*unit[0];

      if(arch=="d")
     {
         Int nq;
         Real *q, *aux;

         nq = q_view.get_dim1();

         q   = q_view.get_data();
         aux = aux_view.get_data();

         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          present(q[0:nv*nq],aux[0:naux*nq],this) \
          default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
//    density
            aux[ADDR(0,iq,nq)]= rho;
//    kinetic energy
            aux[ADDR(1,iq,nq)]= 0.;
//    speed of sound and total entalpy
            aux[ADDR(2,iq,nq)]= 0.;
            aux[ADDR(3,iq,nq)]= cp*q[ADDR(3,iq,nq)];
            aux[ADDR(4,iq,nq)]= cp;
            aux[ADDR(5,iq,nq)]= 0.;
            aux[ADDR(6,iq,nq)]= kappa;
        }
         #pragma acc exit data delete(this)
     }
      else if(arch=="h")
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
//    density
            aux_view(0,iq)= rho;
//    kinetic energy
            aux_view(1,iq)= 0.;
//    speed of sound and total entalpy
            aux_view(2,iq)= 0.;
            aux_view(3,iq)= cp*q_view(3,iq);
            aux_view(4,iq)= cp;
            aux_view(5,iq)= 0.;
            aux_view(6,iq)= kappa;
        } 
     }
      else
     {
         cout << "unkown arch\n"; 
         assert(0);
     }
  }

   void cHeatConduction::cnsv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch )
  {      
      Int iq,ia;
      Int nq;
      Real *sq, *saux, *sqo;
        
      if(arch == "d")
     { 
         nq = q.get_dim1();
   
         sq   = q.get_data();
         saux = aux.get_data();
         sqo  = qo.get_data();
   
         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          present(sq[0:nv*nq],saux[0:naux*nq],sqo[0:nv*nq],this) \
          default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            // density
            sqo[ADDR(0,iq,nq)]= saux[ADDR(0,iq,nq)];
            sqo[ADDR(1,iq,nq)]= 0;
            sqo[ADDR(2,iq,nq)]= 0;
            sqo[ADDR(3,iq,nq)]= 0;
            sqo[ADDR(4,iq,nq)]= sqo[ADDR(0,iq,nq)]*saux[ADDR(3,iq,nq)];
        }
         #pragma acc exit data delete(this)
     }
      else if(arch == "h")
     { 
         for( iq=iqs;iq<iqe;iq++ )
        {
            // density
            qo(0,iq)= aux(0,iq);
            qo(1,iq)= 0;
            qo(2,iq)= 0;
            qo(3,iq)= 0;
            qo(4,iq)= qo(0,iq)*aux(3,iq);
        }
     }
      else
     {
         assert(0);
     }
  }

   void cHeatConduction::mflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl0, cAu3xView<Real>& rhsl,
                                                   cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr0, cAu3xView<Real>& rhsr,
                                                   cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div, cp;
      Int             nql, nqr;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdqdxl, *srhsl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqdxr, *srhsr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sxl    = xl.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdqdxl = dqdxl0.get_data();
      srhsl  = rhsl.get_data();
      icqr   = icqr_view.get_data();
      sxr    = xr.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqdxr = dqdxr0.get_data();
      srhsr  = rhsr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr) \
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxl,dqdxr)\
         present(sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],sdqdxl[0:nv*nx*nfc],srhsl[0:nv*nfc],\
                 icqr[0:nfc],sxr[0:nx*nq],sqr[0:nv*nq],sauxr[0:naux*nq],sdqdxr[0:nv*nx*nq],srhsr[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

            w= wl;
            wl = 0.5; 
            wr = 0.5; 

// normal gradients
            iv=3;
            dqn[iv]= ( sqr[ADDR(iv,iqr,nqr)]-sql[ADDR(iv,iql,nql)] )/w;


            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
            rho=   wl*sauxl[ADDR(     0,iql,nql)]+ wr*sauxr[ADDR(     0,iqr,nqr)];
            cp=    wl*sauxl[ADDR(     4,iql,nql)]+ wr*sauxr[ADDR(     4,iqr,nqr)];

            taun[4]= -kappa*dqn[3];

//            cout << sqr[ADDR(iv,iqr,nqr)] << " " << sql[ADDR(iv,iql,nql)] << " " << w << " " << kappa << " " << cp << " " << rho << " " << taun[4] << " " << wn[3] << " boundary \n";
// accumulate
            //for( iv=1;iv<nv0;iv++ )
            iv=4;
           {
               //rhsr[iv][iqr]+= taun[iv]*wc[3][ic];
               //rhsl[iv][iql]-= taun[iv]*wc[3][ic];
               #pragma acc atomic
               srhsr[ADDR_(iv,iqr,nqr)]+= taun[iv]*wn[3];
               //#pragma acc atomic
               srhsl[ADDR_(iv,iql,nql)]-= taun[iv]*wn[3];
           }
            //auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*kappa/(rho*cp*w);
            
        }
        #pragma acc exit data delete(this)
     }
  }

   void cHeatConduction::mflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx0, cAu3xView<Real>& rhs,
                                 cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ) 
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div, cp;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];

      Int nq, nfc;    
      
      nfc = xc.get_dim1();
      nq  = q0.get_dim1();

      Int *sicq;
      Real *sx, *sq, *saux, *sdqdx, *srhs, *sxc, *swc, *swxdc, *sauxc;

      sicq = icq.get_data();
      sx = x.get_data();
      sq = q0.get_data();
      saux = aux.get_data();
      sdqdx = dqdx0.get_data();
      srhs = rhs.get_data();
      sxc = xc.get_data();
      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxr,dqdxl)\
         present(sicq[0:nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nv*nx*nq], srhs[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)

         for( ic=ics;ic<ice;ic++ )
        {

            //iql= icq[0][ic]; 
            //iqr= icq[1][ic]; 
            iql= sicq[ADDR(0,ic,nfc)]; 
            iqr= sicq[ADDR(1,ic,nfc)]; 

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients
            iv=3;
            dqn[iv]= ( sq[ADDR(iv,iqr,nq)]-sq[ADDR(iv,iql,nq)] )/w;

// tangential gradients

// heat flux
            mu=    0;
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho=   wl*saux[ADDR(     0,iql,nq)]+ wr*saux[ADDR(     0,iqr,nq)];
            cp=    wl*saux[ADDR(     4,iql,nq)]+ wr*saux[ADDR(     4,iqr,nq)];

            taun[4]= -kappa*dqn[3];

//            cout << sq[ADDR(iv,iqr,nq)] << " " << sq[ADDR(iv,iql,nq)] << " " << w << " " << kappa << " " << cp << " " << rho << " " << taun[4] << " " << wn[3] << " internal\n";
// accumulate
            //for( iv=1;iv<nv0;iv++ )
            iv = 4;
           {
               //rhs[iv][iqr]+= taun[iv]*wc[3][ic];
               //rhs[iv][iql]-= taun[iv]*wc[3][ic];
               #pragma acc atomic
               srhs[ADDR_(iv,iqr,nq)]+= taun[iv]*wn[3];
               #pragma acc atomic
               srhs[ADDR_(iv,iql,nq)]-= taun[iv]*wn[3];
           }
            //auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*kappa/(rho*cp*w);
            
        }
        #pragma acc exit data delete(this)
     }
  }

   void cHeatConduction::dmflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                    cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                    cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv;
      Int             nql, nqr;

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sxl    = xl.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdql   = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl  = resl.get_data();
      icqr   = icqr_view.get_data();
      sxr    = xr.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqr   = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr  = resr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      if( ice > ics )
     { 
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr)\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun,wn,xn)\
         present (sxl[0:nx*nql],sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
                  icqr[0:nfc],sxr[0:nx*nqr],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
                  sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

            w= wl;
            wl = 0.5; 
            wr = 0.5; 

//linearized gradients
            iv = 3;
            ddqn[iv]= ( sdauxr[ADDR(iv,iqr,nqr)]- 0. )/w; //boundary value is fixed

            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];

//linearized heat flux
            dtaun[4]= -kappa*ddqn[3];
           
// accumulate

            //for( iv=1;iv<nv0;iv++ )
            iv=4;
           {
               //resr[iv][iqr]+= dtaun[iv]*wc[3][ic];
               //resl[iv][iql]-= dtaun[iv]*wc[3][ic];
               #pragma acc atomic
               sresr[ADDR_(iv,iqr,nqr)]+= dtaun[iv]*wn[3];
               //#pragma acc atomic
               //sresl[ADDR_(iv,iql,nql)]-= dtaun[iv]*wn[3];
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cHeatConduction::dmflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                                  cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv;

      Int nfc, nq;
      Int *sicq;      
      Real *sx, *sq, *saux, *sdq, *sdaux, *sres, *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = q0.get_dim1();

      sicq  = icq.get_data();
      sx    = x.get_data();
      sq    = q0.get_data();
      saux  = aux.get_data();
      sdq   = dq0.get_data();
      sdaux = daux.get_data();
      sres  = res.get_data();
      sxc   = xc.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun,wn,xn)\
         present (sicq[0:2*nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],\
                  sres[0:nv*nq],sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
 
         for( ic=ics;ic<ice;ic++ )
        {
            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            //for( iv=0;iv<nv0;iv++ )
            iv = 3;
            ddqn[iv]= ( sdaux[ADDR(iv,iqr,nq)]- sdaux[ADDR(iv,iql,nq)] )/w;

// linearized heat flux

            //mu=    wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
            //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
            //rho=   wl*aux[0][iql]+      wr*aux[0][iqr];
            mu=    wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho=   wl*saux[ADDR(0,iql,nq)]+      wr*saux[ADDR(0,iqr,nq)];

            dtaun[4]= -kappa*ddqn[3];
           
// accumulate

            //for( iv=1;iv<nv0;iv++ )
            iv = 4;
           {
               //res[iv][iqr]+= dtaun[iv]*wc[3][ic];
               //res[iv][iql]-= dtaun[iv]*wc[3][ic];
               #pragma acc atomic
               sres[ADDR_(iv,iqr,nq)]+= dtaun[iv]*wn[3];
               #pragma acc atomic
               sres[ADDR_(iv,iql,nq)]-= dtaun[iv]*wn[3];
           }
        }
        #pragma acc exit data delete(this)
     }
  }
