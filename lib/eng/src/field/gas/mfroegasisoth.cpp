using namespace std;

# include <field/gas.h>

   cMfRoeGasIsoth::cMfRoeGasIsoth( cCosystem *Coo, cVisc *visc )
  {

      coo= Coo;
      vsc= visc;
      nx=  coo->getnx();
      nvel=coo->getnvel(); 

      nvk=3;
      nv=1+nvel;
      naux=7;
      nauxf=7;
      nlhs= 2;

      nv0= nv;
      naux0= naux;
      nauxf0= nauxf;
      nlhs0= nlhs;

      vsc->setvrs( nx,nvel, &nv,&naux,&nauxf,&nlhs );
      ilv[0]=nvel;
      ilv[1]=ilv[0]+1;
      ilv[2]=ilv[1]+1;
      unit[0]= 100.;
      unit[1]=   1.;
      unit[2]= unit[0]*unit[0];
      deflt[0]=   0.;
      deflt[1]= 298.;
      deflt[2]= 100000./unit[2];
      rg= 287/unit[2];
      gam=1.4;
      eps= 0.05;
      tref=290.;

  }

   void cMfRoeGasIsoth::iflx22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];

      Int             ia,ic,iql,iqr;

      Real            da,dr,dl;
      Real            utr,utl,dut;

      for( ic=ics;ic<ice;ic++ )
     {

         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

 
// fluxes from the left

         pl= ql[2][iql];
         rl= auxl[0][iql];
         al= auxl[2][iql];
         hl= auxl[3][iql];
         dl= rg*tref;
         dl= sqrt(dl);

         unl=  wc[0][ic]*ql[0][iql];
         unl+= wc[1][ic]*ql[1][iql];

         utl= -wc[1][ic]*ql[0][iql];
         utl+= wc[0][ic]*ql[1][iql];

         ll1= unl-wxdc[0][ic];
         ll3= unl+ dl;
         ll4= unl- dl;

         fl[0]= ll1*rl;
         fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
         fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
         for( ia=3;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia][iql]; }

// fluxes from the right
         
         pr= qr[2][iqr];
         rr= auxr[0][iqr];
         ar= auxr[2][iqr];
         hr= auxr[3][iqr];
         dr= rg*tref;
         dr= sqrt(dr);

         unr=  wc[0][ic]*qr[0][iqr];
         unr+= wc[1][ic]*qr[1][iqr];

         utr= -wc[1][ic]*qr[0][iqr];
         utr+= wc[0][ic]*qr[1][iqr];

         lr1= unr-wxdc[0][ic];
         lr3= unr+ dr;
         lr4= unr- dr;

         fr[0]= lr1 *rr;
         fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
         fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
         for( ia=3;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia][iqr]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         for( ia=3;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
        }

         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );

// eigenvalues with Harten's fix

         da=rg*tref;
         da=sqrt(da);
         raa=ra*da;

         la1= una-wxdc[0][ic];
         la3= una+ da;
         la4= una- da;

         lmax= fabs(la1)+ da;
         lmax= fmax( lmax, fabs(ll1)+ dl );
         lmax= fmax( lmax, fabs(lr1)+ dr );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         auxc[0][ic]= wl;
         auxc[1][ic]= wr;
         auxc[2][ic]= ra;
         auxc[3][ic]= la1;
         auxc[4][ic]= la3;
         auxc[5][ic]= la4;
         auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0][iqr]- ql[0][iql];
         dq[1]= qr[1][iqr]- ql[1][iql];
         dq[2]= qr[2][iqr]- ql[2][iql];
         for( ia=3;ia<nv;ia++ )
        {
            dq[ia]= qr[ia][iqr]- ql[ia][iql];
        }
         dun= unr- unl;
         dut= utr- utl;

         la3*=  0.5*ra/da;
         la4*= -0.5*ra/da;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*dut;
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];

         ana[0]= da*wc[0][ic];
         ana[1]= da*wc[1][ic];

         for( ia=3;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=   dw1*0             + dw3                 + dw4;
         fa[1]=  -dw1*ra*wc[1][ic]  + dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]=   dw1*ra*wc[0][ic]  + dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         for( ia=3;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]+ dw5[ia]; }

// assemble 
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[2][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[2][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[2][ic];

         la1=una;
         la3=una+da;
         la4=una-da;
         la3*=  0.5*ra/da;
         la4*= -0.5*ra/da;
         dw1= la1*dut;
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         //cout<<"FLUX FR-FL0 "<<wc[0][ic]<<" "<<wc[1][ic]<<" "<<fr[0]-fl[0]<<" "<<dw1*0             + dw3                 + dw4<<endl;
         //cout<<"FLUX FR-FL1 "<<wc[0][ic]<<" "<<wc[1][ic]<<" "<<fr[1]-fl[1]<<" "<<dw1*-ra*wc[1][ic] + dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] )<<endl;
         //cout<<"FLUX FR-FL2 "<<wc[0][ic]<<" "<<wc[1][ic]<<" "<<fr[2]-fl[2]<<" "<<dw1*ra*wc[0][ic]  + dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] )<<endl;
         //cout<<endl;

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];

         for( ia=3;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[2][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }
         auxc[nauxf-1][ic]*= wc[2][ic];
     }
  }

   void cMfRoeGasIsoth::iflx23( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];

      Int             ia,ic,iql,iqr;

      for( ic=ics;ic<ice;ic++ )
     {

         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

 
// fluxes from the left

         pl= ql[4][iql];
         rl= auxl[0][iql];
         al= auxl[2][iql];
         hl= auxl[3][iql];

         unl=  wc[0][ic]*ql[0][iql];
         unl+= wc[1][ic]*ql[1][iql];

         ll1= unl-wxdc[0][ic];
         ll3= unl+ al;
         ll4= unl- al;

         fl[0]= ll1*rl;
         fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
         fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
         fl[3]= fl[0]*ql[2][iql];
         fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia][iql]; }

// fluxes from the right
         rr= auxr[0][iqr];
         pr= qr[4][iqr];
         rr= auxr[0][iqr];
         ar= auxr[2][iqr];
         hr= auxr[3][iqr];

         unr=  wc[0][ic]*qr[0][iqr];
         unr+= wc[1][ic]*qr[1][iqr];

         lr1= unr-wxdc[0][ic];
         lr3= unr+ ar;
         lr4= unr- ar;

         fr[0]= lr1 *rr;
         fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
         fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
         fr[3]= fr[0]*qr[2][iqr];
         fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia][iqr]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
         qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
        }

         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         la1= una-wxdc[0][ic];
         la3= una+ aa;
         la4= una- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         auxc[0][ic]= wl;
         auxc[1][ic]= wr;
         auxc[2][ic]= ra;
         auxc[3][ic]= la1;
         auxc[4][ic]= la3;
         auxc[5][ic]= la4;
         auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0][iqr]- ql[0][iql];
         dq[1]= qr[1][iqr]- ql[1][iql];
         dq[2]= qr[2][iqr]- ql[2][iql];
         dq[3]= qr[3][iqr]- ql[3][iql];
         dq[4]= qr[4][iqr]- ql[4][iql];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= qr[ia][iqr]- ql[ia][iql];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2[2]= dq[2];                dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=   dw1+               dw3+                  dw4;
         fa[1]=   dw1*qa[0]+   dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]=   dw1*qa[1]+   dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]=   fa[0]*qa[2]+ dw2[2];
         fa[4]=   dw1*ka+    dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; }

// assemble 
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[2][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[2][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[2][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[2][ic];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[2][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];
         rhsl[4][iql]-= f[4];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];
         rhsr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[2][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }
         auxc[nauxf-1][ic]*= wc[2][ic];
     }
  }
   void cMfRoeGasIsoth::iflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];

      Int             ia,ic,iql,iqr;

      for( ic=ics;ic<ice;ic++ )
     {

         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

 
// fluxes from the left

         pl= ql[4][iql];
         rl= auxl[0][iql];
         al= auxl[2][iql];
         hl= auxl[3][iql];

         unl=  wc[0][ic]*ql[0][iql];
         unl+= wc[1][ic]*ql[1][iql];
         unl+= wc[2][ic]*ql[2][iql];

         ll1= unl-wxdc[0][ic];
         ll3= unl+ al;
         ll4= unl- al;

         fl[0]= ll1*rl;
         fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
         fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
         fl[3]= fl[0]*ql[2][iql]+ wc[2][ic]*pl;
         fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia][iql]; }

// fluxes from the right
         rr= auxr[0][iqr];
         pr= qr[4][iqr];
         rr= auxr[0][iqr];
         ar= auxr[2][iqr];
         hr= auxr[3][iqr];

         unr=  wc[0][ic]*qr[0][iqr];
         unr+= wc[1][ic]*qr[1][iqr];
         unr+= wc[2][ic]*qr[2][iqr];

         lr1= unr-wxdc[0][ic];
         lr3= unr+ ar;
         lr4= unr- ar;

         fr[0]= lr1 *rr;
         fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
         fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
         fr[3]= fr[0]*qr[2][iqr]+ wc[2][ic]*pr;
         fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia][iqr]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
         qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
        }

         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];
         una+= qa[2]* wc[2][ic];

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         la1= una-wxdc[0][ic];
         la3= una+ aa;
         la4= una- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         auxc[0][ic]= wl;
         auxc[1][ic]= wr;
         auxc[2][ic]= ra;
         auxc[3][ic]= la1;
         auxc[4][ic]= la3;
         auxc[5][ic]= la4;
         auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0][iqr]- ql[0][iql];
         dq[1]= qr[1][iqr]- ql[1][iql];
         dq[2]= qr[2][iqr]- ql[2][iql];
         dq[3]= qr[3][iqr]- ql[3][iql];
         dq[4]= qr[4][iqr]- ql[4][iql];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= qr[ia][iqr]- ql[ia][iql];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];
         ana[2]= aa*wc[2][ic];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=   dw1+               dw3+                  dw4;
         fa[1]=   dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]=   dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]=   dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]=   dw1*ka+    dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; }

// assemble 
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];
         rhsl[4][iql]-= f[4];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];
         rhsr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }
         auxc[nauxf-1][ic]*= wc[3][ic];
     }
  }

   void cMfRoeGasIsoth::dvar2( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dU[], Real *dq[] )
  {
      Int iq,ia; 
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;
      cv= rg/(gam-1.);

      for( iq=iqs;iq<iqe;iq++ )
     {
         ro= aux[0][iq];
         dro= dU[0][iq];

         dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
         dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
         dq[2][iq]= rg*tref*dro;
         //cout<<"PSRO "<<q[2][iq]/(rg*ro)<<endl;
     }
  }

   void cMfRoeGasIsoth::dvar3( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dU[], Real *dq[] )
  {
      Int iq,ia; 
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;
      cv= rg/(gam-1.);

      for( iq=iqs;iq<iqe;iq++ )
     {
         ro= aux[0][iq];
         dro= dU[0][iq];

         dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
         dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
         dq[2][iq]= ( dU[3][iq]- q[2][iq]*dro )/ro;
         dq[3][iq]= rg*tref*dro;
     }
  }

   void cMfRoeGasIsoth::auxv2( Int iqs, Int iqe, Real *q[], Real *aux[] )
  {
      Int iq; 
      Int iv;
      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);
      Real cp=rg*gam/(gam-1);
      
      for( iq=iqs;iq<iqe;iq++ )
     {
// density
         aux[0][iq]= q[2][iq]/( rg*tref );
// kinetic energy
         aux[1][iq]= q[0][iq]*q[0][iq];
         aux[1][iq]+= q[1][iq]*q[1][iq];
         aux[1][iq]*= 0.5;
// speed of sound and total entalpy
         aux[2][iq]= gam*rg* tref; 
         aux[3][iq]= aux[2][iq]/(gam-1)+ aux[1][iq];
         aux[2][iq]= sqrt( aux[2][iq] );
         aux[4][iq]= cp;
         aux[5][iq]= mu;
         aux[6][iq]= kappa;
     } 
  }

   void cMfRoeGasIsoth::auxv3( Int iqs, Int iqe, Real *q[], Real *aux[] )
  {
      Int iq; 
      Int iv;
      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);
      Real cp=rg*gam/(gam-1);
      
      for( iq=iqs;iq<iqe;iq++ )
     {
// density
         aux[0][iq]= q[3][iq]/( rg*tref );
// kinetic energy
         aux[1][iq]=  q[0][iq]*q[0][iq];
         aux[1][iq]+= q[1][iq]*q[1][iq];
         aux[1][iq]+= q[2][iq]*q[2][iq];
         aux[1][iq]*= 0.5;
// speed of sound and total entalpy
         aux[2][iq]= gam*rg* tref; 
         aux[3][iq]= aux[2][iq]/(gam-1)+ aux[1][iq];
         aux[2][iq]= sqrt( aux[2][iq] );
         aux[4][iq]= cp;
         aux[5][iq]= mu;
         aux[6][iq]= kappa;
     } 
  }

   void cMfRoeGasIsoth::cnsv2( Int iqs, Int iqe, Real *q[], Real *aux[], Real *qo[] )
  {

      Int iq,ia; 

      for( iq=iqs;iq<iqe;iq++ )
     {
// density
         qo[0][iq]= aux[0][iq];
         qo[1][iq]= qo[0][iq]*q[0][iq];
         qo[2][iq]= qo[0][iq]*q[1][iq];
         for( ia=3;ia<nv;ia++ )
        {
            qo[ia][iq]= qo[0][iq]*q[ia][iq];
        }
     } 
  }

   void cMfRoeGasIsoth::cnsv3( Int iqs, Int iqe, Real *q[], Real *aux[], Real *qo[] )
  {

      Int iq,ia; 

      for( iq=iqs;iq<iqe;iq++ )
     {
// density
         qo[0][iq]= aux[0][iq];
         qo[1][iq]= qo[0][iq]*q[0][iq];
         qo[2][iq]= qo[0][iq]*q[1][iq];
         qo[3][iq]= qo[0][iq]*q[2][iq];
         for( ia=4;ia<nv;ia++ )
        {
            qo[ia][iq]= qo[0][iq]*q[ia][iq];
        }
     } 
  }

   void cMfRoeGasIsoth::wflx22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],  
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real f[MxNVs];
      Int  ic,ia,iql,iqr;
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };
 
// fluxes from the left
         f[1]=    wc[0][ic]* qr[2][iqr]* wc[2][ic];
         f[2]=    wc[1][ic]* qr[2][iqr]* wc[2][ic];
         auxc[nauxf-1][ic]= ( auxr[2][iqr]+fabs(wxdc[0][ic]) )*wc[2][ic];

         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];

         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];

     }
  }
   void cMfRoeGasIsoth::wflx23( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],  
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real f[MxNVs];
      Int  ic,ia,iql,iqr;
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };
 
// fluxes from the left
         f[1]=    wc[0][ic]* qr[4][iqr]* wc[2][ic];
         f[2]=    wc[1][ic]* qr[4][iqr]* wc[2][ic];
         f[4]=  wxdc[0][ic]* qr[4][iqr]* wc[2][ic];
         auxc[nauxf-1][ic]= ( auxr[2][iqr]+fabs(wxdc[0][ic]) )*wc[2][ic];

         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[4][iql]-= f[4];

         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[4][iqr]+= f[4];

     }
  }

   void cMfRoeGasIsoth::wflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],  
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real f[MxNVs];
      Int  ic,ia,iql,iqr;
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

// fluxes from the left
         f[1]=    wc[0][ic]* qr[4][iqr]* wc[3][ic];
         f[2]=    wc[1][ic]* qr[4][iqr]* wc[3][ic];
         f[3]=    wc[2][ic]* qr[4][iqr]* wc[3][ic];
         f[4]=  wxdc[0][ic]* qr[4][iqr]* wc[3][ic];
         auxc[nauxf-1][ic]= ( auxr[2][iqr]+fabs(wxdc[0][ic]) )*wc[3][ic];

         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];
         rhsl[4][iql]-= f[4];

         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];
         rhsr[4][iqr]+= f[4];

     }
  }

   void cMfRoeGasIsoth::iflxmuscl22( Int ics,Int ice, Int *icql, Int idl, Real *xql[], Real *ql0[], Real *dxdxl[], Real **dqdxl[], Real *auxl0[], Real *rhsl[],
                                                 Int *icqr, Int idr, Real *xqr[], Real *qr0[], Real *dxdxr[], Real **dqdxr[], Real *auxr0[], Real *rhsr[], 
                                                                     Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd ) 
  {
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Int             ix,ic,iql,iqr,ia;
      Real            xn[3],wn[3];
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs],ql[MxNVs],dhl,dal;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs],qr[MxNVs],dhr,dar;
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dq[MxNVs],dun,dp,dpa;
      Real            dw1l,dw3l,dw4l,dw2l[3],dul[3],dunl,dpl,drl,dtl,tl,dw5[MxNVs];
      Real            dw1l0,dw3l0,dw4l0,dw2l0[3],dul0[3],dunl0,dpl0,drl0,dtl0,tl0;
      Real            dw1r0,dw3r0,dw4r0,dw2r0[3],dur0[3],dunr0,dpr0,drr0,dtr0,tr0;
      Real            dw1r,dw3r,dw4r,dw2r[3],dur[3],dunr,dpr,drr,dtr,tr,dt;
      Real            dwl[MxNVs],dwr[MxNVs];
      Real            mr,ml,wl,wr,cp;
      Real            f[MxNVs];

      Real            dl,dr,da;
      Real            utr,utl,dut;

      cp= rg*gam/(gam-1);

      for( ic=ics;ic<ice;ic++ )
     {
         wn[0]= wc[0][ic];
         wn[1]= wc[1][ic];

         xn[0]= xc[0][ic];
         xn[1]= xc[1][ic];

         iql= ic;
         if( icql ){ iql= icql[ic]; }
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; }
         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );

         for( ia=0;ia<nv;ia++ )
        {
            ql[ia]= ql0[ia][iql]+ dql[ia];
            qr[ia]= qr0[ia][iqr]+ dqr[ia];
        }


// left state - auxiliary variables
         auxl[0]=  ql[2]/( rg*tref );
         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]*= 0.5;
         auxl[2]= gam*rg*tref; 
         auxl[3]= auxl[2]/(gam-1)+ auxl[1];
         auxl[2]= sqrt( auxl[2] );

// right state - auxiliary variables
         auxr[0]= qr[2]/( rg*tref );
         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]*= 0.5;
         auxr[2]= gam*rg*tref; 
         auxr[3]= auxr[2]/(gam-1)+ auxr[1];
         auxr[2]= sqrt( auxr[2] );

// fluxes from the left
         pl=   ql[2];
         rl=   auxl[0];
         al=   auxl[2];
         hl=   auxl[3];
         unl=  wc[0][ic]*ql[0];
         unl+= wc[1][ic]*ql[1];
         utl= -wc[1][ic]*ql[0];
         utl+= wc[0][ic]*ql[1];
         dl=rg*tref;
         dl=sqrt(dl);

         ll1= unl-wxdc[0][ic];
         ll3= unl+ dl;
         ll4= unl- dl;

         fl[0]= ll1*rl;
         fl[1]= fl[0]*ql[0]+   wc[0][ic]*pl;
         fl[2]= fl[0]*ql[1]+   wc[1][ic]*pl;
         for( ia=3;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia]; }

// fluxes from the right
         pr= qr[2];
         rr= auxr[0];
         ar= auxr[2];
         hr= auxr[3];
         unr=  wc[0][ic]*qr[0];
         unr+= wc[1][ic]*qr[1];
         utr= -wc[1][ic]*qr[0];
         utr+= wc[0][ic]*qr[1];
         dr=rg*tref;
         dr=sqrt(dr);

         lr1= unr-wxdc[0][ic];
         lr3= unr+ dr;
         lr4= unr- dr;

         fr[0]= lr1 *rr;
         fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         for( ia=3;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         for( ia=3;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];
         ha= wl*hl+ wr*hr;
         ka*= 0.5; 
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

         da=rg*tref;
         da=sqrt(da);
         raa=ra*da;

// eigenvalues with Harten's fix

         la1= una-wxdc[0][ic];
         la3= una+ da;
         la4= una- da;

         lmax= fabs(la1)+ da;
         lmax= fmax( lmax, fabs(ll1)+ dl );
         lmax= fmax( lmax, fabs(lr1)+ dr );

         le3= fmax( fmax( eps*lmax, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps*lmax, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         auxc[0][ic]= wl;
         auxc[1][ic]= wr;
         auxc[2][ic]= ra;
         auxc[3][ic]= la1;
         auxc[4][ic]= la3;
         auxc[5][ic]= la4;
         auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0]- ql[0];
         dq[1]= qr[1]- ql[1];
         dq[2]= qr[2]- ql[2];
         for( ia=3;ia<nv;ia++ )
        {
            dq[ia]= qr[ia]- ql[ia];
        }
         dun= unr- unl;
         dut= utr- utl;

         la3*=  0.5*ra/da;
         la4*= -0.5*ra/da;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*dut;
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         ana[0]= da*wc[0][ic];
         ana[1]= da*wc[1][ic];
         for( ia=3;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1               + dw3                 + dw4;
         fa[1]= dw1*-ra*wc[1][ic] + dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*ra*wc[0][ic]  + dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         for( ia=3;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[2][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[2][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[2][ic];

         la1=una;
         la3=una+da;
         la4=una-da;
         la3*=  0.5*ra/da;
         la4*= -0.5*ra/da;
         dw1= la1*dut;
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         //cout<<"FLUX FR-FL0 "<<wc[0][ic]<<" "<<wc[1][ic]<<" "<<fr[0]-fl[0]<<" "<<dw1*0             + dw3                 + dw4<<endl;
         //cout<<"FLUX FR-FL1 "<<wc[0][ic]<<" "<<wc[1][ic]<<" "<<fr[1]-fl[1]<<" "<<dw1*-ra*wc[1][ic] + dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] )<<endl;
         //cout<<"FLUX FR-FL2 "<<wc[0][ic]<<" "<<wc[1][ic]<<" "<<fr[2]-fl[2]<<" "<<dw1*ra*wc[0][ic]  + dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] )<<endl;
         //cout<<endl;

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];

         for( ia=3;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[2][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }

         auxc[nauxf-1][ic]*= wc[2][ic];
     }
  }

   void cMfRoeGasIsoth::iflxmuscl23( Int ics,Int ice, Int *icql, Int idl, Real *xql[], Real *ql0[], Real *dxdxl[], Real **dqdxl[], Real *auxl0[], Real *rhsl[],
                                                 Int *icqr, Int idr, Real *xqr[], Real *qr0[], Real *dxdxr[], Real **dqdxr[], Real *auxr0[], Real *rhsr[], 
                                                                     Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd ) 
  {
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Int             ix,ic,iql,iqr,ia;
      Real            xn[3],wn[3];
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs],ql[MxNVs],dhl,dal;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs],qr[MxNVs],dhr,dar;
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa;
      Real            dw1l,dw3l,dw4l,dw2l[3],dul[3],dunl,dpl,drl,dtl,tl,dw5[MxNVs];
      Real            dw1l0,dw3l0,dw4l0,dw2l0[3],dul0[3],dunl0,dpl0,drl0,dtl0,tl0;
      Real            dw1r0,dw3r0,dw4r0,dw2r0[3],dur0[3],dunr0,dpr0,drr0,dtr0,tr0;
      Real            dw1r,dw3r,dw4r,dw2r[3],dur[3],dunr,dpr,drr,dtr,tr,dt;
      Real            dwl[MxNVs],dwr[MxNVs];
      Real            mr,ml,wl,wr,cp;
      Real            f[MxNVs];

      cp= rg*gam/(gam-1);

      for( ic=ics;ic<ice;ic++ )
     {
         wn[0]= wc[0][ic];
         wn[1]= wc[1][ic];

         xn[0]= xc[0][ic];
         xn[1]= xc[1][ic];

         iql= ic;
         if( icql ){ iql= icql[ic]; }
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; }
         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );

         for( ia=0;ia<nv;ia++ )
        {
            ql[ia]= ql0[ia][iql]+ dql[ia];
            qr[ia]= qr0[ia][iqr]+ dqr[ia];
        }


// left state - auxiliary variables
         auxl[0]=  ql[4]/( rg*ql[3] );
         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;
         auxl[2]= gam*rg* ql[3]; 
         auxl[3]= auxl[2]/(gam-1)+ auxl[1];
         auxl[2]= sqrt( auxl[2] );

// right state - auxiliary variables
         auxr[0]= qr[4]/( rg*qr[3] );
         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;
         auxr[2]= gam*rg* qr[3]; 
         auxr[3]= auxr[2]/(gam-1)+ auxr[1];
         auxr[2]= sqrt( auxr[2] );

// fluxes from the left
         pl=   ql[4];
         rl=   auxl[0];
         al=   auxl[2];
         hl=   auxl[3];
         unl=  wc[0][ic]*ql[0];
         unl+= wc[1][ic]*ql[1];

         ll1= unl-wxdc[0][ic];
         ll3= unl+ al;
         ll4= unl- al;

         fl[0]= ll1*rl;
         fl[1]= fl[0]*ql[0]+   wc[0][ic]*pl;
         fl[2]= fl[0]*ql[1]+   wc[1][ic]*pl;
         fl[3]= fl[0]*ql[2];
         fl[4]= fl[0]*hl+    wxdc[0][ic]*pl;
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia]; }

// fluxes from the right
         pr= qr[4];
         rr= auxr[0];
         ar= auxr[2];
         hr= auxr[3];
         unr=  wc[0][ic]*qr[0];
         unr+= wc[1][ic]*qr[1];

         lr1= unr-wxdc[0][ic];
         lr3= unr+ ar;
         lr4= unr- ar;

         fr[0]= lr1 *rr;
         fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         fr[3]= fr[0]*qr[2];
         fr[4]= fr[0]*hr+    wxdc[0][ic]*pr;
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia]; }

// Roe averages

         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];

         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];
         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         la1= una-wxdc[0][ic];
         la3= una+ aa;
         la4= una- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps*lmax, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps*lmax, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         auxc[0][ic]= wl;
         auxc[1][ic]= wr;
         auxc[2][ic]= ra;
         auxc[3][ic]= la1;
         auxc[4][ic]= la3;
         auxc[5][ic]= la4;
         auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0]- ql[0];
         dq[1]= qr[1]- ql[1];
         dq[2]= qr[2]- ql[2];
         dq[3]= qr[3]- ql[3];
         dq[4]= qr[4]- ql[4];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= qr[ia]- ql[ia];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]               ; dw2[2]*= ra*la1;
         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];
         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];
         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=   dw1+               dw3+                  dw4;
         fa[1]=   dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]=   dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= fa[0]*qa[2]+ dw2[2]; 
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[2][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[2][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[2][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[2][ic];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[2][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];
         rhsl[4][iql]-= f[4];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];
         rhsr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[2][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }

         auxc[nauxf-1][ic]*= wc[2][ic];
     }
  }

   void cMfRoeGasIsoth::iflxmuscl33( Int ics,Int ice, Int *icql, Int idl, Real *xql[], Real *ql0[], Real *dxdxl[], Real **dqdxl[], Real *auxl0[], Real *rhsl[],
                                                 Int *icqr, Int idr, Real *xqr[], Real *qr0[], Real *dxdxr[], Real **dqdxr[], Real *auxr0[], Real *rhsr[], 
                                                                     Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd ) 
  {
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Int             ix,ic,iql,iqr,ia;
      Real            xn[3],wn[3];
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs],ql[MxNVs],dhl,dal;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs],qr[MxNVs],dhr,dar;
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa;
      Real            dw1l,dw3l,dw4l,dw2l[3],dul[3],dunl,dpl,drl,dtl,tl,dw5[MxNVs];
      Real            dw1l0,dw3l0,dw4l0,dw2l0[3],dul0[3],dunl0,dpl0,drl0,dtl0,tl0;
      Real            dw1r0,dw3r0,dw4r0,dw2r0[3],dur0[3],dunr0,dpr0,drr0,dtr0,tr0;
      Real            dw1r,dw3r,dw4r,dw2r[3],dur[3],dunr,dpr,drr,dtr,tr,dt;
      Real            dwl[MxNVs],dwr[MxNVs];
      Real            mr,ml,wl,wr,cp;
      Real            f[MxNVs];

      cp= rg*gam/(gam-1);

      for( ic=ics;ic<ice;ic++ )
     {
         wn[0]= wc[0][ic];
         wn[1]= wc[1][ic];
         wn[2]= wc[2][ic];

         xn[0]= xc[0][ic];
         xn[1]= xc[1][ic];
         xn[2]= xc[2][ic];

         iql= ic;
         if( icql ){ iql= icql[ic]; }
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; }
         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );

         for( ia=0;ia<nv;ia++ )
        {
            ql[ia]= ql0[ia][iql]+ dql[ia];
            qr[ia]= qr0[ia][iqr]+ dqr[ia];
        }


// left state - auxiliary variables
         auxl[0]=  ql[4]/( rg*ql[3] );
         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;
         auxl[2]= gam*rg* ql[3]; 
         auxl[3]= auxl[2]/(gam-1)+ auxl[1];
         auxl[2]= sqrt( auxl[2] );

// right state - auxiliary variables
         auxr[0]= qr[4]/( rg*qr[3] );
         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;
         auxr[2]= gam*rg* qr[3]; 
         auxr[3]= auxr[2]/(gam-1)+ auxr[1];
         auxr[2]= sqrt( auxr[2] );

// fluxes from the left
         pl=   ql[4];
         rl=   auxl[0];
         al=   auxl[2];
         hl=   auxl[3];
         unl=  wc[0][ic]*ql[0];
         unl+= wc[1][ic]*ql[1];
         unl+= wc[2][ic]*ql[2];

         ll1= unl-wxdc[0][ic];
         ll3= unl+ al;
         ll4= unl- al;

         fl[0]= ll1*rl;
         fl[1]= fl[0]*ql[0]+   wc[0][ic]*pl;
         fl[2]= fl[0]*ql[1]+   wc[1][ic]*pl;
         fl[3]= fl[0]*ql[2]+   wc[2][ic]*pl;
         fl[4]= fl[0]*hl+    wxdc[0][ic]*pl;
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia]; }

// fluxes from the right
         rr= auxr[0];
         pr= qr[4];
         rr= auxr[0];
         ar= auxr[2];
         hr= auxr[3];
         unr=  wc[0][ic]*qr[0];
         unr+= wc[1][ic]*qr[1];
         unr+= wc[2][ic]*qr[2];

         lr1= unr-wxdc[0][ic];
         lr3= unr+ ar;
         lr4= unr- ar;

         fr[0]= lr1 *rr;
         fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         fr[3]= fr[0]*qr[2]+ wc[2][ic]*pr;
         fr[4]= fr[0]*hr+    wxdc[0][ic]*pr;
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];
         una+= qa[2]* wc[2][ic];
         ha= wl*hl+ wr*hr;
         ka*= 0.5; 
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         la1= una-wxdc[0][ic];
         la3= una+ aa;
         la4= una- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps*lmax, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps*lmax, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         auxc[0][ic]= wl;
         auxc[1][ic]= wr;
         auxc[2][ic]= ra;
         auxc[3][ic]= la1;
         auxc[4][ic]= la3;
         auxc[5][ic]= la4;
         auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0]- ql[0];
         dq[1]= qr[1]- ql[1];
         dq[2]= qr[2]- ql[2];
         dq[3]= qr[3]- ql[3];
         dq[4]= qr[4]- ql[4];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= qr[ia]- ql[ia];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];
         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];
         ana[2]= aa*wc[2][ic];
         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];
         rhsl[4][iql]-= f[4];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];
         rhsr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }

         auxc[nauxf-1][ic]*= wc[3][ic];
     }
  }


   void cMfRoeGasIsoth::dwflx22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            dpr;

      Int             ia,ic,iql,iqr;
      Real            f[MxNVs];

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

         dpr=  dauxr[2][iqr];
         f[1]=   wc[0][ic]*dpr*wc[2][ic];
         f[2]=   wc[1][ic]*dpr*wc[2][ic];

         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];

         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];

     }
  }

   void cMfRoeGasIsoth::dwflx23( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            dpr;

      Int             ia,ic,iql,iqr;
      Real            f[MxNVs];

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

         dpr=  dauxr[4][iqr];
         f[1]=   wc[0][ic]*dpr*wc[2][ic];
         f[2]=   wc[1][ic]*dpr*wc[2][ic];
         f[4]= wxdc[0][ic]*dpr*wc[2][ic];

         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];
         resl[4][iql]-= f[4];

         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];
         resr[4][iqr]+= f[4];

     }
  }

   void cMfRoeGasIsoth::dwflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            dpr;

      Int             ia,ic,iql,iqr;
      Real            f[MxNVs];

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

         dpr=  dauxr[4][iqr];
         f[1]=   wc[0][ic]*dpr*wc[3][ic];
         f[2]=   wc[1][ic]*dpr*wc[3][ic];
         f[3]=   wc[2][ic]*dpr*wc[3][ic];
         f[4]= wxdc[0][ic]*dpr*wc[3][ic];

         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];
         resl[3][iql]-= f[3];
         resl[4][iql]-= f[4];

         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];
         resr[3][iqr]+= f[3];
         resr[4][iqr]+= f[4];

     }
  }

   void cMfRoeGasIsoth::diflx22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            wl,wr,aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];

      Real            da,dr,dl;
      Real            utr,utl,dut;
      Real            dutr,dutl;

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

// fluxes from the left

         pl= ql[2][iql];
         rl= auxl[0][iql];
         hl= auxl[3][iql];

         drl=    dql[0][iql];
         dpl=  dauxl[2][iql];

         unl=  wc[0][ic]*ql[0][iql]; 
         unl+= wc[1][ic]*ql[1][iql]; 
         unl-= wxdc[0][ic];

         utl= -wc[1][ic]*ql[0][iql];
         utl+= wc[0][ic]*ql[1][iql];

         dunl=  wc[0][ic]*dauxl[0][iql]; 
         dunl+= wc[1][ic]*dauxl[1][iql]; 

         dutl= -wc[1][ic]*dauxl[0][iql];
         dutl+= wc[0][ic]*dauxl[1][iql];

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
         fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
         for( ia=3;ia<nv;ia++ )
        {
            fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
        }

// fluxes from the right

         pr= qr[2][iqr];
         rr= auxr[0][iqr];
         hr= auxr[3][iqr];

         drr=    dqr[0][iqr];
         dpr=  dauxr[2][iqr];

         unr=  wc[0][ic]*qr[0][iqr]; 
         unr+= wc[1][ic]*qr[1][iqr]; 
         unr-= wxdc[0][ic];

         utr= -wc[1][ic]*qr[0][iqr];
         utr+= wc[0][ic]*qr[1][iqr];

         dunr=  wc[0][ic]*dauxr[0][iqr]; 
         dunr+= wc[1][ic]*dauxr[1][iqr]; 

         dutr= -wc[1][ic]*dauxr[0][iql];
         dutr+= wc[0][ic]*dauxr[1][iql];

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
         fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
         for( ia=3;ia<nv;ia++ )
        {
            fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
        }

// retrieve Roe averages

         wl=  auxc[0][ic];
         wr=  auxc[1][ic];
         ra=  auxc[2][ic];
         la1= auxc[3][ic];
         la3= auxc[4][ic];
         la4= auxc[5][ic];

         qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         for( ia=3;ia<nv;ia++ ){ qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr]; }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka*= 0.5; 

         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

         da=rg*tref;
         da=sqrt(da);
         raa=ra*da;

// Left eigenvectors

         dr= drr- drl;
         dp= dpr- dpl;
         dq[0]= dauxr[0][iqr]- dauxl[0][iql];
         dq[1]= dauxr[1][iqr]- dauxl[1][iql];
         dq[2]= dauxr[2][iqr]- dauxl[2][iql];
         for( ia=3;ia<nv;ia++ )
        {
            dq[ia]= dauxr[ia][iqr]- dauxl[ia][iql];
        }
         dun= dunr- dunl;
         dut= dutr- dutl;

         la3*=  0.5*ra/da;
         la4*= -0.5*ra/da;

         dpa=dp/raa;
         dw1= la1*dut;
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];

         ana[0]= da*wc[0][ic];
         ana[1]= da*wc[1][ic];

         for( ia=3;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1*0             + dw3                  + dw4;
         fa[1]= dw1*-ra*wc[1][ic] + dw3*( qa[0]+ ana[0]) + dw4*( qa[0]- ana[0] );
         fa[2]= dw1*ra*wc[0][ic]  + dw3*( qa[1]+ ana[1]) + dw4*( qa[1]- ana[1] );
         for( ia=3;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[2][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[2][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[2][ic];

         resl[0][iql]-= f[0];
         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];

         resr[0][iqr]+= f[0];
         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];

         for( ia=3;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[2][ic];
            resl[ia][iql]-= f[ia];
            resr[ia][iqr]+= f[ia];
        }

     }
  }

   void cMfRoeGasIsoth::diflx23( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            wl,wr,aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

// fluxes from the left

         pl= ql[4][iql];
         rl= auxl[0][iql];
         hl= auxl[3][iql];

         drl=    dql[0][iql];
         dpl=  dauxl[4][iql];
         drel=   dql[4][iql];

         unl=  wc[0][ic]*ql[0][iql]; 
         unl+= wc[1][ic]*ql[1][iql]; 
         unl-= wxdc[0][ic];

         dunl=  wc[0][ic]*dauxl[0][iql]; 
         dunl+= wc[1][ic]*dauxl[1][iql]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
         fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
         fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql];
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         for( ia=5;ia<nv;ia++ )
        {
            fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
        }

// fluxes from the right

         pr= qr[4][iqr];
         rr= auxr[0][iqr];
         hr= auxr[3][iqr];

         drr=    dqr[0][iqr];
         dpr=  dauxr[4][iqr];
         drer=   dqr[4][iqr];

         unr=  wc[0][ic]*qr[0][iqr]; 
         unr+= wc[1][ic]*qr[1][iqr]; 
         unr-= wxdc[0][ic];

         dunr=  wc[0][ic]*dauxr[0][iqr]; 
         dunr+= wc[1][ic]*dauxr[1][iqr]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
         fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
         fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr];
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         for( ia=5;ia<nv;ia++ )
        {
            fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
        }

// retrieve Roe averages

         wl=  auxc[0][ic];
         wr=  auxc[1][ic];
         ra=  auxc[2][ic];
         la1= auxc[3][ic];
         la3= auxc[4][ic];
         la4= auxc[5][ic];

         qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
         qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
         for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr]; }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// Left eigenvectors

         dr= drr- drl;
         dp= dpr- dpl;
         dq[0]= dauxr[0][iqr]- dauxl[0][iql];
         dq[1]= dauxr[1][iqr]- dauxl[1][iql];
         dq[2]= dauxr[2][iqr]- dauxl[2][iql];
         dq[3]= dauxr[3][iqr]- dauxl[3][iql];
         dq[4]= dauxr[4][iqr]- dauxl[4][iql];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= dauxr[ia][iqr]- dauxl[ia][iql];
        }
         dun= dunr- dunl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2[2]= dq[2];                dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=   dw1+               dw3+                  dw4;
         fa[1]=   dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]=   dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= fa[0]*qa[2]+ dw2[2]; 
         fa[4]=      dw1*ka+ dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[2][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[2][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[2][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[2][ic];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[2][ic];

         resl[0][iql]-= f[0];
         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];
         resl[3][iql]-= f[3];
         resl[4][iql]-= f[4];

         resr[0][iqr]+= f[0];
         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];
         resr[3][iqr]+= f[3];
         resr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[2][ic];
            resl[ia][iql]-= f[ia];
            resr[ia][iqr]+= f[ia];
        }
     }
  }

   void cMfRoeGasIsoth::diflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            wl,wr,aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

// fluxes from the left

         pl= ql[4][iql];
         rl= auxl[0][iql];
         hl= auxl[3][iql];

         drl=    dql[0][iql];
         dpl=  dauxl[4][iql];
         drel=   dql[4][iql];

         unl=  wc[0][ic]*ql[0][iql]; 
         unl+= wc[1][ic]*ql[1][iql]; 
         unl+= wc[2][ic]*ql[2][iql]; 
         unl-= wxdc[0][ic];

         dunl=  wc[0][ic]*dauxl[0][iql]; 
         dunl+= wc[1][ic]*dauxl[1][iql]; 
         dunl+= wc[2][ic]*dauxl[2][iql]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
         fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
         fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql]+  dpl*wc[2][ic]; 
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         for( ia=5;ia<nv;ia++ )
        {
            fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
        }

// fluxes from the right

         pr= qr[4][iqr];
         rr= auxr[0][iqr];
         hr= auxr[3][iqr];

         drr=    dqr[0][iqr];
         dpr=  dauxr[4][iqr];
         drer=   dqr[4][iqr];

         unr=  wc[0][ic]*qr[0][iqr]; 
         unr+= wc[1][ic]*qr[1][iqr]; 
         unr+= wc[2][ic]*qr[2][iqr]; 
         unr-= wxdc[0][ic];

         dunr=  wc[0][ic]*dauxr[0][iqr]; 
         dunr+= wc[1][ic]*dauxr[1][iqr]; 
         dunr+= wc[2][ic]*dauxr[2][iqr]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
         fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
         fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         for( ia=5;ia<nv;ia++ )
        {
            fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
        }

// retrieve Roe averages

         wl=  auxc[0][ic];
         wr=  auxc[1][ic];
         ra=  auxc[2][ic];
         la1= auxc[3][ic];
         la3= auxc[4][ic];
         la4= auxc[5][ic];

         qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
         qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
         for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr]; }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];
         una+= qa[2]* wc[2][ic];

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// Left eigenvectors

         dr= drr- drl;
         dp= dpr- dpl;
         dq[0]= dauxr[0][iqr]- dauxl[0][iql];
         dq[1]= dauxr[1][iqr]- dauxl[1][iql];
         dq[2]= dauxr[2][iqr]- dauxl[2][iql];
         dq[3]= dauxr[3][iqr]- dauxl[3][iql];
         dq[4]= dauxr[4][iqr]- dauxl[4][iql];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= dauxr[ia][iqr]- dauxl[ia][iql];
        }
         dun= dunr- dunl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];
         ana[2]= aa*wc[2][ic];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];

         resl[0][iql]-= f[0];
         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];
         resl[3][iql]-= f[3];
         resl[4][iql]-= f[4];

         resr[0][iqr]+= f[0];
         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];
         resr[3][iqr]+= f[3];
         resr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            resl[ia][iql]-= f[ia];
            resr[ia][iqr]+= f[ia];
        }

     }
  }

   void cMfRoeGasIsoth::diflxb22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };


// fluxes from the left

         pl= ql[2][iql];
         rl= auxl[0][iql];
         hl= auxl[3][iql];

         drl=    dql[0][iql];
         dpl=  dauxl[2][iql];

         unl=  wc[0][ic]*ql[0][iql]; 
         unl+= wc[1][ic]*ql[1][iql]; 
         unl-= wxdc[0][ic];

         dunl=  wc[0][ic]*dauxl[0][iql]; 
         dunl+= wc[1][ic]*dauxl[1][iql]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
         fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
         for( ia=3;ia<nv;ia++ )
        {
            fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
        }

// fluxes from the right

         pr= qr[2][iqr];
         rr= auxr[0][iqr];
         hr= auxr[3][iqr];

         drr=    dqr[0][iqr];
         dpr=  dauxr[2][iqr];

         unr=  wc[0][ic]*qr[0][iqr]; 
         unr+= wc[1][ic]*qr[1][iqr]; 
         unr-= wxdc[0][ic];

         dunr=  wc[0][ic]*dauxr[0][iqr]; 
         dunr+= wc[1][ic]*dauxr[1][iqr]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
         fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
         for( ia=3;ia<nv;ia++ )
        {
            fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
        }

// one wave

         Real lmax= auxc[nauxf-1][ic];
         fa[0]= lmax*( dqr[0][iqr]- dql[0][iql] );
         fa[1]= lmax*( dqr[1][iqr]- dql[1][iql] );
         fa[2]= lmax*( dqr[2][iqr]- dql[2][iql] );
         for( ia=3;ia<nv;ia++ )
        {
            fa[ia]= lmax*( dqr[ia][iqr]- dql[ia][iql] );
        }

// assemble 
         Real f[MxNVs];

         f[0]= 0.5*( fr[0]+ fl[0] )*wc[2][ic]- 0.5*fa[0];
         f[1]= 0.5*( fr[1]+ fl[1] )*wc[2][ic]- 0.5*fa[1];
         f[2]= 0.5*( fr[2]+ fl[2] )*wc[2][ic]- 0.5*fa[2];

         resl[0][iql]-= f[0];
         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];

         resr[0][iqr]+= f[0];
         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];

         for( ia=3;ia<nv;ia++ )
        { 
            f[ia]= 0.5*( fr[ia]+ fl[ia] )*wc[2][ic]- 0.5*fa[ia];
            resl[ia][iql]-= f[ia];
            resr[ia][iqr]+= f[ia];
        }
     }
  }

   void cMfRoeGasIsoth::diflxb23( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];


      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };


// fluxes from the left

         pl= ql[4][iql];
         rl= auxl[0][iql];
         hl= auxl[3][iql];

         drl=    dql[0][iql];
         dpl=  dauxl[4][iql];
         drel=   dql[4][iql];

         unl=  wc[0][ic]*ql[0][iql]; 
         unl+= wc[1][ic]*ql[1][iql]; 
         unl-= wxdc[0][ic];

         dunl=  wc[0][ic]*dauxl[0][iql]; 
         dunl+= wc[1][ic]*dauxl[1][iql]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
         fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
         fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql];
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         for( ia=5;ia<nv;ia++ )
        {
            fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
        }

// fluxes from the right

         pr= qr[4][iqr];
         rr= auxr[0][iqr];
         hr= auxr[3][iqr];

         drr=    dqr[0][iqr];
         dpr=  dauxr[4][iqr];
         drer=   dqr[4][iqr];

         unr=  wc[0][ic]*qr[0][iqr]; 
         unr+= wc[1][ic]*qr[1][iqr]; 
         unr-= wxdc[0][ic];

         dunr=  wc[0][ic]*dauxr[0][iqr]; 
         dunr+= wc[1][ic]*dauxr[1][iqr]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
         fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
         fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr];
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         for( ia=5;ia<nv;ia++ )
        {
            fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
        }

// one wave

         Real lmax= auxc[nauxf-1][ic];
         fa[0]= lmax*( dqr[0][iqr]- dql[0][iql] );
         fa[1]= lmax*( dqr[1][iqr]- dql[1][iql] );
         fa[2]= lmax*( dqr[2][iqr]- dql[2][iql] );
         fa[3]= lmax*( dqr[3][iqr]- dql[3][iql] );
         fa[4]= lmax*( dqr[4][iqr]- dql[4][iql] );
         for( ia=5;ia<nv;ia++ )
        {
            fa[ia]= lmax*( dqr[ia][iqr]- dql[ia][iql] );
        }

// assemble 

         Real f[MxNVs];

         f[0]= 0.5*( fr[0]+ fl[0] )*wc[2][ic]- 0.5*fa[0];
         f[1]= 0.5*( fr[1]+ fl[1] )*wc[2][ic]- 0.5*fa[1];
         f[2]= 0.5*( fr[2]+ fl[2] )*wc[2][ic]- 0.5*fa[2];
         f[3]= 0.5*( fr[3]+ fl[3] )*wc[2][ic]- 0.5*fa[3];
         f[4]= 0.5*( fr[4]+ fl[4] )*wc[2][ic]- 0.5*fa[4];

         resl[0][iql]-= f[0];
         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];
         resl[3][iql]-= f[3];
         resl[4][iql]-= f[4];

         resr[0][iqr]+= f[0];
         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];
         resr[3][iqr]+= f[3];
         resr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        { 
            f[ia]= 0.5*( fr[ia]+ fl[ia] )*wc[2][ic]- 0.5*fa[ia];
            resl[ia][iql]-= f[ia];
            resr[ia][iqr]+= f[ia];
        }
     }
  }

   void cMfRoeGasIsoth::diflxb33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];


      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };


// fluxes from the left

         pl= ql[4][iql];
         rl= auxl[0][iql];
         hl= auxl[3][iql];

         drl=    dql[0][iql];
         dpl=  dauxl[4][iql];
         drel=   dql[4][iql];

         unl=  wc[0][ic]*ql[0][iql]; 
         unl+= wc[1][ic]*ql[1][iql]; 
         unl+= wc[2][ic]*ql[2][iql]; 
         unl-= wxdc[0][ic];

         dunl=  wc[0][ic]*dauxl[0][iql]; 
         dunl+= wc[1][ic]*dauxl[1][iql]; 
         dunl+= wc[2][ic]*dauxl[2][iql]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
         fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
         fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql]+  dpl*wc[2][ic]; 
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         for( ia=5;ia<nv;ia++ )
        {
            fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
        }

// fluxes from the right

         pr= qr[4][iqr];
         rr= auxr[0][iqr];
         hr= auxr[3][iqr];

         drr=    dqr[0][iqr];
         dpr=  dauxr[4][iqr];
         drer=   dqr[4][iqr];

         unr=  wc[0][ic]*qr[0][iqr]; 
         unr+= wc[1][ic]*qr[1][iqr]; 
         unr+= wc[2][ic]*qr[2][iqr]; 
         unr-= wxdc[0][ic];

         dunr=  wc[0][ic]*dauxr[0][iqr]; 
         dunr+= wc[1][ic]*dauxr[1][iqr]; 
         dunr+= wc[2][ic]*dauxr[2][iqr]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
         fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
         fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         for( ia=5;ia<nv;ia++ )
        {
            fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
        }

// one wave

         Real lmax= auxc[nauxf-1][ic];
         fa[0]= lmax*( dqr[0][iqr]- dql[0][iql] );
         fa[1]= lmax*( dqr[1][iqr]- dql[1][iql] );
         fa[2]= lmax*( dqr[2][iqr]- dql[2][iql] );
         fa[3]= lmax*( dqr[3][iqr]- dql[3][iql] );
         fa[4]= lmax*( dqr[4][iqr]- dql[4][iql] );
         for( ia=5;ia<nv;ia++ )
        {
            fa[ia]= lmax*( dqr[ia][iqr]- dql[ia][iql] );
        }

// assemble 

         Real f[MxNVs];

         f[0]= 0.5*( fr[0]+ fl[0] )*wc[3][ic]- 0.5*fa[0];
         f[1]= 0.5*( fr[1]+ fl[1] )*wc[3][ic]- 0.5*fa[1];
         f[2]= 0.5*( fr[2]+ fl[2] )*wc[3][ic]- 0.5*fa[2];
         f[3]= 0.5*( fr[3]+ fl[3] )*wc[3][ic]- 0.5*fa[3];
         f[4]= 0.5*( fr[4]+ fl[4] )*wc[3][ic]- 0.5*fa[4];

         resl[0][iql]-= f[0];
         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];
         resl[3][iql]-= f[3];
         resl[4][iql]-= f[4];

         resr[0][iqr]+= f[0];
         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];
         resr[3][iqr]+= f[3];
         resr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        { 
            f[ia]= 0.5*( fr[ia]+ fl[ia] )*wc[3][ic]- 0.5*fa[ia];
            resl[ia][iql]-= f[ia];
            resr[ia][iqr]+= f[ia];
        }
     }
  }

   void cMfRoeGasIsoth::mwflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
                                      Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      vsc->mwflxisoth( ics,ice, icql, xl, ql, auxl, rhsl,  icqr, xr, qr, auxr, rhsr, wc, wxdc, auxc );
  }

   void cMfRoeGasIsoth::dmwflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                       Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                       Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      vsc->dmwflxisoth( ics,ice, icql, xl,ql,auxl,dql,dauxl,resl, icqr, xr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
  }

   void cMfRoeGasIsoth::accel( Int iqs, Int iqe, Real omega, Real *wq[], Real *xq[], Real *q[], Real *aux[], Real *xdq[], Real *rhs[] )
  {
      Int iq,ia;
      coo->accel( iqs,iqe, omega, xq,q,xdq ); 
      for( ia=0;ia<nvel;ia++ )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            rhs[ia+1][iq]+=  wq[0][iq]*aux[0][iq]*xdq[ia][iq];
        }
     }
      for( ia=0;ia<nx;ia++ )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            rhs[ia+1][iq]-=  q[nvel][iq]*wq[ia+1][iq];
        }
     }
  }

   void cMfRoeGasIsoth::daccel( Int iqs, Int iqe, Real omega, Real *wq[], Real *xq[], Real *q[], Real *dq[], Real *daux[], Real *aux[], 
                      Real *xdq[], Real *rhs[] )
  {
      Int iq,ia;
      coo->daccel( iqs,iqe, omega, xq,q,daux,xdq ); 
      for( ia=0;ia<nvel;ia++ )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            rhs[ia+1][iq]+=  wq[0][iq]*aux[0][iq]*xdq[ia][iq];
        }
     }
      coo->accel( iqs,iqe, omega, xq,q,xdq ); 
      for( ia=0;ia<nvel;ia++ )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            rhs[ia+1][iq]+=  wq[0][iq]*daux[0][iq]*xdq[ia][iq];
        }
     }
      for( ia=0;ia<nx;ia++ )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            rhs[ia+1][iq]-=  daux[nvel][iq]*wq[ia+1][iq];
        }
     }
  }

   void cMfRoeGasIsoth::nondim( Int iqs, Int iqe, Real *q[], Int *idone )
  {
      Int ia,iq;

      if( iqe > iqs )
     {
         for( ia=0;ia<nvel;ia++ )
        {
            if( idone[ia] != 1 )
           {
               for( iq=iqs;iq<iqe;iq++ )
              {
                  q[ia][iq]= deflt[0];
              }
           }
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[ia][iq]/= unit[0];
           }
        }

         tref/= unit[1];

         ia= nvel;
         if( idone[ia] != 1 )   
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[ia][iq]= deflt[2];
           }
        }
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[ia][iq]/= unit[2];
        }
         vsc->nondim( iqs,iqe, q, idone);
     }
  }

   void cMfRoeGasIsoth::redim( Int iqs, Int iqe, Real *q[] )
  {
      Int ia,iq;

      if( iqe > iqs )
     {
         for( ia=0;ia<nvel;ia++ )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[ia][iq]*= unit[0];
           }
        }
         tref*= unit[1];
         ia= nvel;
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[ia][iq]*= unit[2];
        }
         vsc->redim( iqs,iqe, q );
     }
  }

   void cMfRoeGasIsoth::mflx22( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                       Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            for( iv=0;iv<nv0;iv++ )
           {
               dqnl[iv]=  dqdxl[iv][0][iql]*wc[0][ic];
               dqnl[iv]+= dqdxl[iv][1][iql]*wc[1][ic];

               dqnr[iv]=  dqdxr[iv][0][iqr]*wc[0][ic];
               dqnr[iv]+= dqdxr[iv][1][iqr]*wc[1][ic];

               dqn[iv]= ( qr[iv][iqr]-ql[iv][iql] )/w;
               q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
           }

// tangential gradients

            for( iv=0;iv<nv0;iv++ )
           {
               dqtl= dqdxl[iv][0][iql]- wc[0][ic]*dqnl[iv];
               dqtr= dqdxr[iv][0][iqr]- wc[0][ic]*dqnr[iv];
               dqt[iv][0]= wl*dqtl+ wr*dqtr;

               dqtl= dqdxl[iv][1][iql]- wc[1][ic]*dqnl[iv];
               dqtr= dqdxr[iv][1][iqr]- wc[1][ic]*dqnr[iv];
               dqt[iv][1]= wl*dqtl+ wr*dqtr;

           }

// reconstruct gradient
            for( iv=0;iv<nv0;iv++ )
           {
               dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
               dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
           }

// stress tensor
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 

            tau[0][0]+= 2/3*mu*dqdx[0][0];
            tau[0][0]+= 2/3*mu*dqdx[1][1];

            tau[1][1]+= 2/3*mu*dqdx[0][0];
            tau[1][1]+= 2/3*mu*dqdx[1][1];


// viscous fluxes

            taun[1]=  tau[0][0]*wc[0][ic];
            taun[1]+= tau[0][1]*wc[1][ic];

            taun[2]=  tau[1][0]*wc[0][ic];
            taun[2]+= tau[1][1]*wc[1][ic];

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               rhsr[iv][iqr]+= taun[iv]*wc[2][ic];
               rhsl[iv][iql]-= taun[iv]*wc[2][ic];
           }
            auxc[nauxf-1][ic]+= wc[2][ic]*mu/(rho*w);
        }
     }
  }

   void cMfRoeGasIsoth::mflx23( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                       Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            for( iv=0;iv<nv0;iv++ )
           {
               dqnl[iv]=  dqdxl[iv][0][iql]*wc[0][ic];
               dqnl[iv]+= dqdxl[iv][1][iql]*wc[1][ic];

               dqnr[iv]=  dqdxr[iv][0][iqr]*wc[0][ic];
               dqnr[iv]+= dqdxr[iv][1][iqr]*wc[1][ic];

               dqn[iv]= ( qr[iv][iqr]-ql[iv][iql] )/w;
               q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
           }

// tangential gradients

            for( iv=0;iv<nv0;iv++ )
           {
               dqtl= dqdxl[iv][0][iql]- wc[0][ic]*dqnl[iv];
               dqtr= dqdxr[iv][0][iqr]- wc[0][ic]*dqnr[iv];
               dqt[iv][0]= wl*dqtl+ wr*dqtr;

               dqtl= dqdxl[iv][1][iql]- wc[1][ic]*dqnl[iv];
               dqtr= dqdxr[iv][1][iqr]- wc[1][ic]*dqnr[iv];
               dqt[iv][1]= wl*dqtl+ wr*dqtr;
           }

// reconstruct gradient
            for( iv=0;iv<nv0;iv++ )
           {
               dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
               dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
           }

// stress tensor
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];
            div=  dqdx[0][0];
            div=  dqdx[1][1];
            div*= 2/3*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*(             dqdx[2][0] ); 
            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*(             dqdx[2][1] ); 

            tau[0][0]+= div;
            tau[1][1]+= div;


// viscous fluxes

            taun[1]=  tau[0][0]*wc[0][ic];
            taun[1]+= tau[0][1]*wc[1][ic];

            taun[2]=  tau[1][0]*wc[0][ic];
            taun[2]+= tau[1][1]*wc[1][ic];

            taun[3]=  tau[2][0]*wc[0][ic];
            taun[3]+= tau[2][1]*wc[1][ic];


            taun[4]= -kappa*dqn[3];
            taun[4]+= taun[1]*q[0];
            taun[4]+= taun[2]*q[1];
            taun[4]+= taun[3]*q[2];

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               rhsr[iv][iqr]+= taun[iv]*wc[2][ic];
               rhsl[iv][iql]-= taun[iv]*wc[2][ic];
           }
            auxc[nauxf-1][ic]+= wc[2][ic]*mu/(rho*w);
            
        }
     }
  }

   void cMfRoeGasIsoth::mflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            for( iv=0;iv<nv0;iv++ )
           {
               dqnl[iv]=  dqdxl[iv][0][iql]*wc[0][ic];
               dqnl[iv]+= dqdxl[iv][1][iql]*wc[1][ic];
               dqnl[iv]+= dqdxl[iv][2][iql]*wc[2][ic];

               dqnr[iv]=  dqdxr[iv][0][iqr]*wc[0][ic];
               dqnr[iv]+= dqdxr[iv][1][iqr]*wc[1][ic];
               dqnr[iv]+= dqdxr[iv][2][iqr]*wc[2][ic];

               dqn[iv]= ( qr[iv][iqr]-ql[iv][iql] )/w;
               q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
           }

// tangential gradients

            for( iv=0;iv<nv0;iv++ )
           {
               dqtl= dqdxl[iv][0][iql]- wc[0][ic]*dqnl[iv];
               dqtr= dqdxr[iv][0][iqr]- wc[0][ic]*dqnr[iv];
               dqt[iv][0]= wl*dqtl+ wr*dqtr;

               dqtl= dqdxl[iv][1][iql]- wc[1][ic]*dqnl[iv];
               dqtr= dqdxr[iv][1][iqr]- wc[1][ic]*dqnr[iv];
               dqt[iv][1]= wl*dqtl+ wr*dqtr;

               dqtl= dqdxl[iv][2][iql]- wc[2][ic]*dqnl[iv];
               dqtr= dqdxr[iv][2][iqr]- wc[2][ic]*dqnr[iv];
               dqt[iv][2]= wl*dqtl+ wr*dqtr;
           }

// reconstruct gradient
            for( iv=0;iv<nv0;iv++ )
           {
               dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
               dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
               dqdx[iv][2]= dqn[iv]*wc[2][ic]+ dqt[iv][2];
           }

// stress tensor
            mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho=   wl*auxl[     0][iql]+ wr*auxr[     0][iqr];
            div=  dqdx[0][0];
            div+= dqdx[1][1];
            div+= dqdx[2][2];
            div*= 2/3*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 
            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 
            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            tau[0][0]+= div;
            tau[1][1]+= div;
            tau[2][2]+= div;

// viscous fluxes

            taun[1]=  tau[0][0]*wc[0][ic];
            taun[1]+= tau[0][1]*wc[1][ic];
            taun[1]+= tau[0][2]*wc[2][ic];

            taun[2]=  tau[1][0]*wc[0][ic];
            taun[2]+= tau[1][1]*wc[1][ic];
            taun[2]+= tau[1][2]*wc[2][ic];

            taun[3]=  tau[2][0]*wc[0][ic];
            taun[3]+= tau[2][1]*wc[1][ic];
            taun[3]+= tau[2][2]*wc[2][ic];


            taun[4]= -kappa*dqn[3];
            taun[4]+= taun[1]*q[0];
            taun[4]+= taun[2]*q[1];
            taun[4]+= taun[3]*q[2];

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               rhsr[iv][iqr]+= taun[iv]*wc[3][ic];
               rhsl[iv][iql]-= taun[iv]*wc[3][ic];
           }
            auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            
        }
     }
  }

   void cMfRoeGasIsoth::dmflx22( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                        Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv;

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv0;iv++ )
           {
               ddqn[iv]= ( dauxr[iv][iqr]-dauxl[iv][iql] )/w;
               dqn[iv]=  ( qr[iv][iqr]-ql[iv][iql] )/w;

               q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
               dq[iv]= wl*dauxl[iv][iql]+ wr*dauxr[iv][iqr];

           }

            for( iv=0;iv<nv0;iv++ )
           {
               dqdx[iv][0]= dqn[iv]*wc[0][ic];
               dqdx[iv][1]= dqn[iv]*wc[1][ic];

               ddqdx[iv][0]= ddqn[iv]*wc[0][ic];
               ddqdx[iv][1]= ddqn[iv]*wc[1][ic];
           }

// stress tensor

            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            div=  dqdx[0][0];
            div=  dqdx[1][1];
            div*= 2/3*mu;

            ddiv=  ddqdx[0][0];
            ddiv=  ddqdx[1][1];
            ddiv*= 2/3*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 

            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 

            dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
            dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 

            dtau[0][1]=  dtau[1][0];
            dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 

            tau[0][0]+= div;
            tau[1][1]+= div;

            dtau[0][0]+= ddiv;
            dtau[1][1]+= ddiv;

// viscous flux

            for( iv=0;iv<nv0;iv++ )
           {
               taun[iv]=0;
               dtaun[iv]=0;
           }

            taun[1]=    tau[0][0]*wc[0][ic];
            taun[1]+=   tau[0][1]*wc[1][ic];

            taun[2]=    tau[1][0]*wc[0][ic];
            taun[2]+=   tau[1][1]*wc[1][ic];

            dtaun[1]=   dtau[0][0]*wc[0][ic];
            dtaun[1]+=  dtau[0][1]*wc[1][ic];

            dtaun[2]=   dtau[1][0]*wc[0][ic];
            dtaun[2]+=  dtau[1][1]*wc[1][ic];
           
// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               resr[iv][iqr]+= dtaun[iv]*wc[2][ic];
               resl[iv][iql]-= dtaun[iv]*wc[2][ic];
           }
        }
     }
  }
   void cMfRoeGasIsoth::dmflx23( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                        Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv;

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv0;iv++ )
           {
               ddqn[iv]= ( dauxr[iv][iqr]-dauxl[iv][iql] )/w;
               dqn[iv]=  ( qr[iv][iqr]-ql[iv][iql] )/w;

               q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
               dq[iv]= wl*dauxl[iv][iql]+ wr*dauxr[iv][iqr];

           }

            for( iv=0;iv<nv0;iv++ )
           {
               dqdx[iv][0]= dqn[iv]*wc[0][ic];
               dqdx[iv][1]= dqn[iv]*wc[1][ic];

               ddqdx[iv][0]= ddqn[iv]*wc[0][ic];
               ddqdx[iv][1]= ddqn[iv]*wc[1][ic];
           }

// stress tensor

            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho= wl*auxl[0][iql]+ wr*auxr[0][iqr];

            div=  dqdx[0][0];
            div=  dqdx[1][1];
            div*= 2/3*mu;

            ddiv=  ddqdx[0][0];
            ddiv=  ddqdx[1][1];
            ddiv*= 2/3*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*(             dqdx[2][0] ); 

            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*(             dqdx[2][1] ); 

            dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
            dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 
            dtau[2][0]= -mu*(              ddqdx[2][0] ); 

            dtau[0][1]=  dtau[1][0];
            dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 
            dtau[2][1]= -mu*(              ddqdx[2][1] ); 

            tau[0][0]+= div;
            tau[1][1]+= div;

            dtau[0][0]+= ddiv;
            dtau[1][1]+= ddiv;

// viscous flux

            for( iv=0;iv<nv0;iv++ )
           {
               taun[iv]=0;
               dtaun[iv]=0;
           }

            taun[1]=    tau[0][0]*wc[0][ic];
            taun[1]+=   tau[0][1]*wc[1][ic];

            taun[2]=    tau[1][0]*wc[0][ic];
            taun[2]+=   tau[1][1]*wc[1][ic];

            taun[3]=    tau[2][0]*wc[0][ic];
            taun[3]+=   tau[2][1]*wc[1][ic];

            dtaun[1]=   dtau[0][0]*wc[0][ic];
            dtaun[1]+=  dtau[0][1]*wc[1][ic];

            dtaun[2]=   dtau[1][0]*wc[0][ic];
            dtaun[2]+=  dtau[1][1]*wc[1][ic];

            dtaun[3]=   dtau[2][0]*wc[0][ic];
            dtaun[3]+=  dtau[2][1]*wc[1][ic];

            dtaun[4]= -kappa*ddqn[3];
            dtaun[4]+= taun[1]*dq[0]+ dtaun[1]*q[0];
            dtaun[4]+= taun[2]*dq[1]+ dtaun[2]*q[1];
            dtaun[4]+= taun[3]*dq[2]+ dtaun[3]*q[2];
           
// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               resr[iv][iqr]+= dtaun[iv]*wc[2][ic];
               resl[iv][iql]-= dtaun[iv]*wc[2][ic];
           }
            

        }
     }
  }

   void cMfRoeGasIsoth::dmflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                        Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv;

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv0;iv++ )
           {
               ddqn[iv]= ( dauxr[iv][iqr]- dauxl[iv][iql] )/w;
               dqn[iv]=  ( qr[iv][iqr]-       ql[iv][iql] )/w;

               q[iv]=  wl*   ql[iv][iql]+ wr*   qr[iv][iqr];
               dq[iv]= wl*dauxl[iv][iql]+ wr*dauxr[iv][iqr];

           }

            for( iv=0;iv<nv0;iv++ )
           {
               dqdx[iv][0]= dqn[iv]*wc[0][ic];
               dqdx[iv][1]= dqn[iv]*wc[1][ic];
               dqdx[iv][2]= dqn[iv]*wc[2][ic];

               ddqdx[iv][0]= ddqn[iv]*wc[0][ic];
               ddqdx[iv][1]= ddqn[iv]*wc[1][ic];
               ddqdx[iv][2]= ddqn[iv]*wc[2][ic];
           }

// stress tensor

            mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            rho=   wl*auxl[0][iql]+      wr*auxr[0][iqr];

            div=  dqdx[0][0];
            div=  dqdx[1][1];
            div*= 2/3*mu;

            ddiv=  ddqdx[0][0];
            ddiv=  ddqdx[1][1];
            ddiv*= 2/3*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 

            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 

            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
            dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 
            dtau[2][0]= -mu*( ddqdx[0][2]+ ddqdx[2][0] ); 

            dtau[0][1]=  dtau[1][0];
            dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 
            dtau[2][1]= -mu*( ddqdx[1][2]+ ddqdx[2][1] ); 

            dtau[0][2]=  dtau[2][0];
            dtau[1][2]=  dtau[2][1];
            dtau[2][2]= -mu*( ddqdx[2][2]+ ddqdx[2][2] ); 

            tau[0][0]+=  div;
            tau[1][1]+=  div;
            tau[2][2]+=  div;

            dtau[0][0]+= ddiv;
            dtau[1][1]+= ddiv;
            dtau[2][2]+= ddiv;

// viscous flux

            taun[1]=    tau[0][0]*wc[0][ic];
            taun[1]+=   tau[0][1]*wc[1][ic];
            taun[1]+=   tau[0][2]*wc[2][ic];

            taun[2]=    tau[1][0]*wc[0][ic];
            taun[2]+=   tau[1][1]*wc[1][ic];
            taun[2]+=   tau[1][2]*wc[2][ic];

            taun[3]=    tau[2][0]*wc[0][ic];
            taun[3]+=   tau[2][1]*wc[1][ic];
            taun[3]+=   tau[2][2]*wc[2][ic];

            dtaun[1]=   dtau[0][0]*wc[0][ic];
            dtaun[1]+=  dtau[0][1]*wc[1][ic];
            dtaun[1]+=  dtau[0][2]*wc[2][ic];

            dtaun[2]=   dtau[1][0]*wc[0][ic];
            dtaun[2]+=  dtau[1][1]*wc[1][ic];
            dtaun[2]+=  dtau[1][2]*wc[2][ic];

            dtaun[3]=   dtau[2][0]*wc[0][ic];
            dtaun[3]+=  dtau[2][1]*wc[1][ic];
            dtaun[3]+=  dtau[2][2]*wc[2][ic];

            dtaun[4]= -kappa*ddqn[3];
            dtaun[4]+= taun[1]*dq[0]+ dtaun[1]*q[0];
            dtaun[4]+= taun[2]*dq[1]+ dtaun[2]*q[1];
            dtaun[4]+= taun[3]*dq[2]+ dtaun[3]*q[2];
           
// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               resr[iv][iqr]+= dtaun[iv]*wc[3][ic];
               resl[iv][iql]-= dtaun[iv]*wc[3][ic];
           }
        }
     }
  }
