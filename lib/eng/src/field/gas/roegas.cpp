using namespace std;

# include <field/gas.h>

   cRoeGas::cRoeGas( cCosystem *Coo, cVisc *visc )
  {

      coo= Coo;
      vsc= visc;
      nx=  coo->getnx();
      nvel=coo->getnvel(); 

      nvk=3;
      nv=2+nvel;
      naux=7;
      nauxf=7;
      nlhs= 2;

      nv0= nv;
      naux0= naux;
      nauxf0= nauxf;
      nlhs0= nlhs;

      vsc->setvrs( nx,nvel, &nv,&naux,&nauxf,&nlhs );
      ilv[0]=nvel;
      ilv[1]=ilv[0]+1;
      ilv[2]=ilv[1]+1;
      unit[0]= 100.;
      unit[1]=   1.;
      unit[2]= unit[0]*unit[0];
      deflt[0]=   0.;
      deflt[1]= 298.;
      deflt[2]= 100000./unit[2];
      rg= 287/unit[2];
      gam=1.4;
      eps= 0.05;

//      Int i=0;
//      for( Int ja=0;ja<nv;ja++ )
//     {
//         ija[ja]= new Int[nv];
//         for( Int ia=0;ia<nv;ia++ )
//        {
//            ija[ja][ia]= i++;
//        }
//     }
  }

   void cRoeGas::ilhs( Int ics, Int ice, Int *icql, Real *ql[], Real *auxl[], Real *lhsl[], 
                                         Int *icqr, Real *qr[], Real *auxr[], Real *lhsr[],  
                                         Real *wc[], Real *wxdc[], Real *auxc[], cJacBlk *jac_df[2] ) 
  {
      Real            kl,al,unl,rl,ll1,ll3,ll4,pl,hl,cvl,epl,qepl,tl,el,kl2;
      Real            kr,ar,unr,rr,lr1,lr3,lr4,pr,hr,cvr,epr,qepr,tr,er,kr2;
      Real            aa,a2a,ra,ha,ka,ana[3],una,unaa,raa, la1,la4,la3, qepa,epa,ka2;
      Real            dw1,dw3,dw4,dw2[3],dw2a;

      Real            ut[3],qt,gam1;
      Int             ia,ja,iql,iqr,ic;
      Real            dfa[MxNVs][MxNVs];
      Real            dfr[MxNVs][MxNVs];
      Real            dfl[MxNVs][MxNVs];
      Real            wl, wr, qa[MxNVs];

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        { 

            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };
       
// fluxes from the left
            tl= ql[nvel][iql];
            pl= ql[nvel+1][iql];
            rl= auxl[0][iql];
            kl= auxl[1][iql];
            al= auxl[2][iql];
            hl= auxl[3][iql];
            cvl=auxl[4][iql]-rg; //aux[4] is cp
            el=hl-rg*tl;
            epl=el-cvl*tl;
            gam1=gam-1;
            kl2= 2*kl;
            qepl= kl2-epl;

            unl= 0.;
            for( ia=0;ia<nvel;ia++ ){ unl+= wc[ia][ic]*ql[ia][iql]; }
      
            ll1= unl-wxdc[0][ic];
            ll3= ll1+ al;
            ll4= ll1- al;
      
            dfl[0][0]= -wxdc[0][ic];
            dfl[0][1]= wc[0][ic]*gam1*qepl-ql[0][iql]*unl;
            dfl[0][2]= wc[1][ic]*gam1*qepl-ql[1][iql]*unl;
            dfl[0][3]= wc[2][ic]*gam1*qepl-ql[2][iql]*unl;
            dfl[0][4]= unl*(gam1*kl2-gam*el);
      
            dfl[1][0]=     wc[0][ic];
            dfl[1][1]= ql[0][iql]*wc[0][ic]-gam1*ql[0][iql]*wc[0][ic]+ll1;
            dfl[1][2]= ql[1][iql]*wc[0][ic]-gam1*ql[0][iql]*wc[1][ic];
            dfl[1][3]= ql[2][iql]*wc[0][ic]-gam1*ql[0][iql]*wc[2][ic];
            dfl[1][4]=  hl*wc[0][ic]- gam1*ql[0][iql]*unl;
      
            dfl[2][0]=     wc[1][ic];
            dfl[2][1]= ql[0][iql]*wc[1][ic]-gam1*ql[1][iql]*wc[0][ic];
            dfl[2][2]= ql[1][iql]*wc[1][ic]-gam1*ql[1][iql]*wc[1][ic]+ll1;
            dfl[2][3]= ql[2][iql]*wc[1][ic]-gam1*ql[1][iql]*wc[2][ic];
            dfl[2][4]=  hl*wc[1][ic]-gam1*ql[1][iql]*unl;
      
            dfl[3][0]=     wc[2][ic];
            dfl[3][1]= ql[0][iql]*wc[2][ic]-gam1*ql[2][iql]*wc[0][ic];
            dfl[3][2]= ql[1][iql]*wc[2][ic]-gam1*ql[2][iql]*wc[1][ic];
            dfl[3][3]= ql[2][iql]*wc[2][ic]-gam1*ql[2][iql]*wc[2][ic]+ll1;
            dfl[3][4]=  hl*wc[2][ic]-gam1*ql[2][iql]*unl;
      
            dfl[4][0]= 0;
            dfl[4][1]= gam1*wc[0][ic];
            dfl[4][2]= gam1*wc[1][ic];
            dfl[4][3]= gam1*wc[2][ic];
            dfl[4][4]= gam*unl-wxdc[0][ic];
      
// fluxes from the right
            tr= qr[nvel][iqr];
            pr= qr[nvel+1][iqr];
            rr= auxr[0][iqr];
            kr= auxr[1][iqr];
            ar= auxr[2][iqr];
            hr= auxr[3][iqr];
            cvr=auxr[4][iqr] - rg; //au3x[4] is cp
            er=hr-rg*tr;
            epr=er-cvr*tr;
            gam1=gam-1;
            kr2= 2*kr;
            qepr= kr2-epr;

            unr= 0.;
            for( ia=0;ia<nvel;ia++ ){ unr+= wc[ia][ic]*qr[ia][iqr]; }
      
            lr1= unr-wxdc[0][ic];
            lr3= lr1+ ar;
            lr4= lr1- ar;
      
            dfr[0][0]= -wxdc[0][ic];
            dfr[0][1]= wc[0][ic]*gam1*qepr-qr[0][iqr]*unr;
            dfr[0][2]= wc[1][ic]*gam1*qepr-qr[1][iqr]*unr;
            dfr[0][3]= wc[2][ic]*gam1*qepr-qr[2][iqr]*unr;
            dfr[0][4]= unr*(gam1*kr2-gam*er);
      
            dfr[1][0]=     wc[0][ic];
            dfr[1][1]= qr[0][iqr]*wc[0][ic]-gam1*qr[0][iqr]*wc[0][ic]+lr1;
            dfr[1][2]= qr[1][iqr]*wc[0][ic]-gam1*qr[0][iqr]*wc[1][ic];
            dfr[1][3]= qr[2][iqr]*wc[0][ic]-gam1*qr[0][iqr]*wc[2][ic];
            dfr[1][4]=  hr*wc[0][ic]- gam1*qr[0][iqr]*unr;
      
            dfr[2][0]=     wc[1][ic];
            dfr[2][1]= qr[0][iqr]*wc[1][ic]-gam1*qr[1][iqr]*wc[0][ic];
            dfr[2][2]= qr[1][iqr]*wc[1][ic]-gam1*qr[1][iqr]*wc[1][ic]+lr1;
            dfr[2][3]= qr[2][iqr]*wc[1][ic]-gam1*qr[1][iqr]*wc[2][ic];
            dfr[2][4]=  hr*wc[1][ic]-gam1*qr[1][iqr]*unr;
      
            dfr[3][0]=     wc[2][ic];
            dfr[3][1]= qr[0][iqr]*wc[2][ic]-gam1*qr[2][iqr]*wc[0][ic];
            dfr[3][2]= qr[1][iqr]*wc[2][ic]-gam1*qr[2][iqr]*wc[1][ic];
            dfr[3][3]= qr[2][iqr]*wc[2][ic]-gam1*qr[2][iqr]*wc[2][ic]+lr1;
            dfr[3][4]=  hr*wc[2][ic]-gam1*qr[2][iqr]*unr;
      
            dfr[4][0]= 0;
            dfr[4][1]= gam1*wc[0][ic];
            dfr[4][2]= gam1*wc[1][ic];
            dfr[4][3]= gam1*wc[2][ic];
            dfr[4][4]= gam*unr-wxdc[0][ic];
      
// retrieve Roe averages
      
//            for( ia=0;ia<nvel;ia++ ){ ua[ia]= auxc[ia][ic]; }
//            ra= auxc[ira][ic];
//            ha= auxc[iha][ic];
//            ka= auxc[ika][ic];
//            aa= auxc[iaa][ic];
//            epa= auxc[iea][ic];
//            gam= auxc[iga][ic];
//            la1= auxc[il1][ic];
//            la3= auxc[il3][ic];
//            la4= auxc[il4][ic];
//            a2a= aa*aa;
//            raa= ra*aa;
//            ka2= 2*ka;
//            qepa=ka2-epa;
//            gam1= gam-1;
//      
//            una=0;
//            for( ia=0;ia<nvel;ia++ )
//           {
//               una+= ua[ia]*wc[ia][ic];
//           }
//            for( ia=0;ia<nvel;ia++ )
//           {
//               ana[ia]= aa*wc[ia][ic];
//           }
//            unaa=aa*una;
            wl=  auxc[0][ic];
            wr=  auxc[1][ic];
            ra=  auxc[2][ic];
            la1= auxc[3][ic];
            la3= auxc[4][ic];
            la4= auxc[5][ic];
   
            qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
            qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
            qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
            qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
            qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
            for( ia=5;ia<nv;ia++ )
           {
               qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
           }
 
            una=  qa[0]* wc[0][ic];
            una+= qa[1]* wc[1][ic];
            una+= qa[2]* wc[2][ic];
   
            ka=  qa[0]*qa[0];
            ka+= qa[1]*qa[1];
            ka+= qa[2]*qa[2];
            ka*= 0.5;
            ka2= 2*ka;
 
            ha= wl*hl+ wr*hr;
            a2a= (gam-1.)*( ha- ka );
            aa= sqrt( a2a );
            raa=ra*aa;
            qepa = ka; //for perfect gas, qepa and epa should be the same
            epa = ka;
            gam1= gam-1;

            for( ia=0;ia<nvel;ia++ )
           {
               ana[ia]= aa*wc[ia][ic];
           }
            unaa=aa*una;
      
            ut[0]= qa[0]-una*wc[0][ic];
            ut[1]= qa[1]-una*wc[1][ic];
            ut[2]= qa[2]-una*wc[2][ic];
            qt=  ut[0]*ut[0]+ ut[1]*ut[1]+ ut[2]*ut[2];
      
            dw1=    la1*(1-gam1*qepa/a2a);
            dw2[0]=  -la1*ut[0];
            dw2[1]=  -la1*ut[1];
            dw2[2]=  -la1*ut[2];
            dw2a=    -la1*qt;
            dw3=    0.5*la3*(gam1*qepa-unaa)/a2a;
            dw4=    0.5*la4*(gam1*qepa+unaa)/a2a;
      
                                      dfa[0][0]=      dw1+                 dw3+                    dw4;
            for( ia=0;ia<nvel;ia++ ){ dfa[0][ia+1]=   dw1*qa[ia]+ dw2[ia]+ dw3*( qa[ia]+ ana[ia])+ dw4*( qa[ia]- ana[ia] ); }
                                      dfa[0][nvel+1]= dw1*epa+    dw2a+    dw3*( ha+ unaa)+        dw4*( ha- unaa );
      
            dw1=      la1*gam1*qa[0]/a2a;
            dw2[0]=   la1*( 1 -wc[0][ic]*wc[0][ic]);
            dw2[1]=   la1*(   -wc[0][ic]*wc[1][ic]);
            dw2[2]=   la1*(   -wc[0][ic]*wc[2][ic]);
            dw2a=     la1*ut[0];
            dw3=      0.5*la3*(wc[0][ic]-gam1*qa[0]/aa)/aa;
            dw4=     -0.5*la4*(wc[0][ic]+gam1*qa[0]/aa)/aa;
      
                                      dfa[1][0]=      dw1+                 dw3+                    dw4;
            for( ia=0;ia<nvel;ia++ ){ dfa[1][ia+1]=   dw1*qa[ia]+ dw2[ia]+ dw3*( qa[ia]+ ana[ia])+ dw4*( qa[ia]- ana[ia] ); }
                                      dfa[1][nvel+1]= dw1*epa+    dw2a+    dw3*( ha+ unaa)+        dw4*( ha- unaa );
      
            dw1=    la1*gam1*qa[1]/a2a;
            dw2[0]= la1*(   -wc[1][ic]*wc[0][ic]);
            dw2[1]= la1*( 1 -wc[1][ic]*wc[1][ic]);
            dw2[2]= la1*(   -wc[1][ic]*wc[2][ic]);
            dw2a=   la1*ut[1];
            dw3=    0.5*la3*(wc[1][ic]-gam1*qa[1]/aa)/aa;
            dw4=   -0.5*la4*(wc[1][ic]+gam1*qa[1]/aa)/aa;
      
                                      dfa[2][0]=      dw1+                 dw3+                    dw4;
            for( ia=0;ia<nvel;ia++ ){ dfa[2][ia+1]=   dw1*qa[ia]+ dw2[ia]+ dw3*( qa[ia]+ ana[ia])+ dw4*( qa[ia]- ana[ia] ); }
                                      dfa[2][nvel+1]= dw1*epa+    dw2a+    dw3*( ha+ unaa)+        dw4*( ha- unaa );
      
            dw1=    la1*gam1*qa[2]/a2a;
            dw2[0]= la1*(   -wc[2][ic]*wc[0][ic]);
            dw2[1]= la1*(   -wc[2][ic]*wc[1][ic]);
            dw2[2]= la1*( 1 -wc[2][ic]*wc[2][ic]);
            dw2a=   la1*ut[2];
            dw3=    0.5*la3*(wc[2][ic]-gam1*qa[2]/aa)/aa;
            dw4=   -0.5*la4*(wc[2][ic]+gam1*qa[2]/aa)/aa;
      
                                      dfa[3][0]=      dw1+                 dw3+                    dw4;
            for( ia=0;ia<nvel;ia++ ){ dfa[3][ia+1]=   dw1*qa[ia]+ dw2[ia]+ dw3*( qa[ia]+ ana[ia])+ dw4*( qa[ia]- ana[ia] ); }
                                      dfa[3][nvel+1]= dw1*epa+    dw2a+    dw3*( ha+ unaa)+        dw4*( ha- unaa );
      
            dw1=   -la1*gam1/a2a;
            dw3=    0.5*la3*gam1/a2a;
            dw4=    0.5*la4*gam1/a2a;
      
                                      dfa[4][0]=      dw1+        dw3+                    dw4;
            for( ia=0;ia<nvel;ia++ ){ dfa[4][ia+1]=   dw1*qa[ia]+ dw3*( qa[ia]+ ana[ia])+ dw4*( qa[ia]- ana[ia] ); }
                                      dfa[4][nvel+1]= dw1*epa+    dw3*( ha+ unaa)+        dw4*( ha- unaa );
      
            for( ja=0;ja<nv;ja++ )
           {
               for( ia=0;ia<nv;ia++ )
              {
                  dfl[ja][ia]+= dfa[ja][ia]; dfl[ja][ia]*= 0.5* wc[nx][ic];
                  dfr[ja][ia]-= dfa[ja][ia]; dfr[ja][ia]*= 0.5* wc[nx][ic];

//                  if(lhsl)
//                 {
//                     lhsl[ija[ja][ia]][iql]-= dfl[ja][ia];
//                 }
//
//                  if(lhsr)
//                 {
//                     lhsr[ija[ja][ia]][iqr]+= dfr[ja][ia];
//                 }
//
//                  if(lhsl_jac)
//                 {
//                     lhsl_jac[iql][iql].jac[ia][ja]-= dfl[ja][ia];
//                     if(lhsr_jac)
//                    {
//                        lhsl_jac[iql][iqr].jac[ia][ja]-= dfr[ja][ia];
//                    }
//                 }
//
//                  if(lhsr_jac)
//                 {
//                     if(lhsl_jac)
//                    {
//                        lhsr_jac[iqr][iql].jac[ia][ja]+= dfl[ja][ia];
//                    }
//                     lhsr_jac[iqr][iqr].jac[ia][ja]+= dfr[ja][ia];
//                 }

                  jac_df[0][ic].jac[ia][ja] = dfl[ja][ia]; //transpose
                  jac_df[1][ic].jac[ia][ja] = dfr[ja][ia]; //transpose
              }
           }
            lhsl[0][iql]+= auxc[nauxf-1][ic];
            lhsr[0][iqr]+= auxc[nauxf-1][ic];
        }
     }
  }

   void cRoeGas::wlhs( Int ics, Int ice, Int *icql, Real *ql[], Real *auxl[], Real *lhsl[], 
                                         Int *icqr, Real *qr[], Real *auxr[], Real *lhsr[], 
                                         Real *wc[], Real *wxdc[], Real *auxc[], cJacBlk *jac_df[2] ) 
  {
      Real            gam1,kr,kr2,rr,tr,qepr,cvr,hr,pr,ar,er,epr;
      Real            dp1,dp2,dp3,dp4,dp5;
      Real            dfr[MxNVs][MxNVs];

      Int             ic,ia,iql,iqr,ja;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };
      
            tr= qr[nvel][iqr];
            pr= qr[nvel+1][iqr];
            rr= auxr[0][iqr];
            kr= auxr[1][iqr];
            ar= auxr[2][iqr];
            hr= auxr[3][iqr];
            cvr=auxr[4][iqr] - rg; //aux[4] is cp
            er=hr-rg*tr;
            epr=er-cvr*tr;
            gam1=gam-1;
            kr2= 2*kr;
            qepr= kr2-epr;
            gam1= gam-1;

            dp1= gam1*qepr;
            dp2=-gam1*qr[0][iqr];
            dp3=-gam1*qr[1][iqr];
            dp4=-gam1*qr[2][iqr];
            dp5= gam1;
      
            dp1*= wc[nx][ic];
            dp2*= wc[nx][ic];
            dp3*= wc[nx][ic];
            dp4*= wc[nx][ic];
            dp5*= wc[nx][ic];
      
            dfr[0][0]= 0;
            dfr[1][0]= 0;
            dfr[2][0]= 0;
            dfr[3][0]= 0;
            dfr[4][0]= 0;
      
            dfr[0][1]= wc[0][ic]*dp1;
            dfr[1][1]= wc[0][ic]*dp2;
            dfr[2][1]= wc[0][ic]*dp3;
            dfr[3][1]= wc[0][ic]*dp4;
            dfr[4][1]= wc[0][ic]*dp5;
      
            dfr[0][2]= wc[1][ic]*dp1;
            dfr[1][2]= wc[1][ic]*dp2;
            dfr[2][2]= wc[1][ic]*dp3;
            dfr[3][2]= wc[1][ic]*dp4;
            dfr[4][2]= wc[1][ic]*dp5;
      
            dfr[0][3]= wc[2][ic]*dp1;
            dfr[1][3]= wc[2][ic]*dp2;
            dfr[2][3]= wc[2][ic]*dp3;
            dfr[3][3]= wc[2][ic]*dp4;
            dfr[4][3]= wc[2][ic]*dp5;
      
            dfr[0][4]= wxdc[0][ic]*dp1; 
            dfr[1][4]= wxdc[0][ic]*dp2;
            dfr[2][4]= wxdc[0][ic]*dp3;
            dfr[3][4]= wxdc[0][ic]*dp4;
            dfr[4][4]= wxdc[0][ic]*dp5;

            for( ja=0;ja<nv;ja++ )
           {
               for( ia=0;ia<nv;ia++ )
              {
//                  if(lhsr)
//                 {
//                     lhsr[ija[ja][ia]][iqr]+= dfr[ja][ia];
//                 }
//
//                  if(lhsr_jac)
//                 {
//                     lhsr_jac[iqr][iqr].jac[ia][ja]+= dfr[ja][ia];
//                 }

                  jac_df[1][ic].jac[ia][ja] = dfr[ja][ia]; //transpose
              }
           }
            lhsr[0][iqr]+= auxc[nauxf-1][ic];

        }
     }

  }

