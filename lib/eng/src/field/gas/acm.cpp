using namespace std;

# include <field/gas.h>

   void crossproduct3( Real *dl0, Real *dl1, Real *dn )
  {
      dn[0] = dl0[1]*dl1[2] - dl0[2]*dl1[1];
      dn[1] = dl0[2]*dl1[0] - dl0[0]*dl1[2];
      dn[2] = dl0[0]*dl1[1] - dl0[1]*dl1[0];
  }

   void tangent_vectors(Real *n, Real *n0, Real *n1)
  {
      Int i, imin;
      Real dmin, d, tmp[3];

      //check which component is the smallest
      dmin = 99999;
      for(i=0; i<3; i++)
     {
         if(n[i]<dmin)
        {
            imin = i;
            dmin = n[i];
        }
     }
      //cout << "imin " << imin << " " << n[imin] << "\n";

      for(i=0; i<3; i++)
     {
         if(i==imin) tmp[i] = 0;
         else        tmp[i] = n[i];
     }

       if(imin==0)
      {
          d = tmp[1];
          tmp[1] =-tmp[2];
          tmp[2] = d;
      }
       else if(imin==1)
      {
          d = tmp[0];
          tmp[0] =-tmp[2];
          tmp[2] = d;
      }
       else if(imin==2)
      {
          d = tmp[0];
          tmp[0] =-tmp[1];
          tmp[1] = d;
      }


      d = tmp[0]*tmp[0];
      d+= tmp[1]*tmp[1];
      d+= tmp[2]*tmp[2];
      d = sqrt(d)+small;
      tmp[0] /= d;
      tmp[1] /= d;
      tmp[2] /= d;

      crossproduct3(tmp, n, n0);
      d = n0[0]*n0[0];
      d+= n0[1]*n0[1];
      d+= n0[2]*n0[2];
      d = sqrt(d)+small;
      n0[0] /= d;
      n0[1] /= d;
      n0[2] /= d;
      crossproduct3(n, n0, n1);
      d = n1[0]*n1[0];
      d+= n1[1]*n1[1];
      d+= n1[2]*n1[2];
      d = sqrt(d)+small;
      n1[0] /= d;
      n1[1] /= d;
      n1[2] /= d;

//      cout << n0[0] << " " << n0[1]  << " " << n0[2] << " n0\n";
//      cout << n1[0] << " " << n1[1]  << " " << n1[2] << " n1\n";
//      crossproduct(n0, n1, tmp);
//      cout << tmp[0] << " " << tmp[1]  << " " << tmp[2] << " check\n";
  }

   cACMGas::cACMGas( cCosystem *Coo, cVisc *visc )
  {
      coo= Coo;
      vsc= visc;
      nx=  coo->getnx();
      nvel=coo->getnvel(); 

      nvk=3;
      nv=2+nvel;
      naux=7;
      nauxf=7;
      nlhs= 2;

      nv0= nv;
      naux0= naux;
      nauxf0= nauxf;
      nlhs0= nlhs;

      vsc->setvrs( nx,nvel, &nv,&naux,&nauxf,&nlhs );
      ilv[0]=nvel;
      ilv[1]=ilv[0]+1;
      ilv[2]=ilv[1]+1;
      unit[0]=   100.;
      unit[1]=   1.;
      unit[2]= unit[0]*unit[0];
      deflt[0]=   0.;
      deflt[1]= 298.;
      deflt[2]= 100000./unit[2];

      beta = -1;
      density = 1;
      nu = -1;
  }


   void cACMGas::ilhs( Int ics, Int ice, Int *icql, Real *ql[], Real *auxl[], Real *lhsl[], 
                                           Int *icqr, Real *qr[], Real *auxr[], Real *lhsr[],  
                                           Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Int             iql,iqr,ic;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        { 

            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };
       
            lhsl[0][iql]+= auxc[nauxf-1][ic];
            lhsr[0][iqr]+= auxc[nauxf-1][ic];

        }
     }
      vsc->ilhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc ) ;
  }

   void cACMGas::wlhs( Int ics, Int ice, Int *icql, Real *ql[], Real *auxl[], Real *lhsl[], 
                                           Int *icqr, Real *qr[], Real *auxr[], Real *lhsr[], 
                                           Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Int             ic,iql,iqr;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };
      
            lhsr[0][iqr]+= auxc[nauxf-1][ic];

        }
     }
      vsc->wlhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc );
  }

   void cACMGas::slhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[] )
  {
      vsc->slhs( iqs,iqe, cfl, q,aux, dqdx,dst,wq,lhs );
  }
 
   void cACMGas::vlhs( Int iqs, Int iqe, Real dtm,Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[],
                        Real *wq[], Real *lhs[], Real rord )
  {
      Int             iq;
      Real            w;
      if( iqe > iqs )
     {
         w= 1./cfl;
         for( iq=iqs;iq<iqe;iq++ )
        {
            lhs[nlhs-1][iq]=  lhs[0][iq]*w;
            if(rord>0) lhs[nlhs-1][iq]+= rord*wq[0][iq]/dtm;
            lhs[0][iq]+=     lhs[nlhs-1][iq];

        }
     }
      vsc->vlhs( iqs,iqe, cfl, wq,lhs );
  }

   void cACMGas::invdg( Int iqs, Int iqe, Real *lhs[], Real *res[] )
  {
      Int iv,iq;
      if( iqe > iqs )
     {

         for( iv=0;iv<nv0;iv++ )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               res[iv][iq]/= lhs[0][iq]; 
           }
        }
     }
      vsc->invdg( iqs,iqe, lhs,res );
  }

   void cACMGas::iflx22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            al,unl,rl,ll1,pl,fl[MxNVs];
      Real            ar,unr,rr,lr1,pr,fr[MxNVs];
      Real            lmax,m;
      Real            f[MxNVs]; 
      Real            fa[MxNVs], leig[3][3], reig[3][3], reig_fac;
      Real            nx, ny, una, ua, va, qa[MxNVs], aa, la1, la2, la3, dw[3], dq[MxNVs], dp, du, dv, dw5[MxNVs];

      Int             ia,ic,iql,iqr;

      for( ic=ics;ic<ice;ic++ )
     {

         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };


// fluxes from the left

         pl= ql[2][iql];
         rl= auxl[0][iql];
         al= auxl[2][iql];

         unl=  wc[0][ic]*ql[0][iql];
         unl+= wc[1][ic]*ql[1][iql];

         ll1= unl-wxdc[0][ic];

//         cout << pl << " " << rl << " " << al << " var \n";

         m = ll1*rl;
         fl[0]= m*beta;
         fl[1]= m*ql[0][iql]+ wc[0][ic]*pl/density;
         fl[2]= m*ql[1][iql]+ wc[1][ic]*pl/density;
         for( ia=3;ia<nv;ia++ ){ fl[ia]= m*ql[ia][iql]; }

// fluxes from the right
         pr= qr[2][iqr];
         rr= auxr[0][iqr];
         ar= auxr[2][iqr];

         unr=  wc[0][ic]*qr[0][iqr];
         unr+= wc[1][ic]*qr[1][iqr];

         lr1= unr-wxdc[0][ic];

         m = lr1 *rr;
         fr[0]= m*beta;
         fr[1]= m*qr[0][iqr]+ wc[0][ic]*pr/density;
         fr[2]= m*qr[1][iqr]+ wc[1][ic]*pr/density;
         for( ia=3;ia<nv;ia++ ){ fr[ia]= m*qr[ia][iqr]; }


// Roe'e Riemann solver
         for(ia=0; ia<nv; ia++)
        {
            qa[ia] = 0.5*(ql[ia][iql]+qr[ia][iqr]);
        }
         nx = wc[0][ic];
         ny = wc[1][ic];
         ua = qa[0];
         va = qa[1];
         una = ua*nx + va*ny;
         aa = sqrt(una*una+beta);
         la1= una-wxdc[0][ic];
         la2= una-0.5*wxdc[0][ic]+ aa;
         la3= una-0.5*wxdc[0][ic]- aa;
         la1 = fabs(la1);
         la2 = fabs(la2);
         la3 = fabs(la3);

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );
         auxc[nauxf-1][ic]= lmax;

//         fa[0] = lmax*(pr - pl);
//         fa[1] = lmax*(qr[0][iqr] - ql[0][iql]);
//         fa[2] = lmax*(qr[1][iqr] - ql[1][iql]);
//         for( ia=3;ia<nv;ia++){ fa[ia]=  lmax*(qr[ia][iqr] - ql[ia][iql]); }

//         if(ic==0)
//        {
//            cout << nx << " " << ny << " " << ua << " " << va << " " << una << " " << aa << " " << la1 << " " 
//                 << la2 << " " << la3 << "\n";    
//        }

         leig[0][0] = ny*ua-nx*va;
         leig[1][0] = aa-una;
         leig[2][0] = -aa-una;
         leig[0][1] = -una*va-beta*ny;
         leig[1][1] = beta*nx;
         leig[2][1] = beta*nx;
         leig[0][2] = una*ua+beta*nx;
         leig[1][2] = beta*ny;
         leig[2][2] = beta*ny;

         reig_fac = 1./(2*beta*aa*aa);
         reig[0][0] = reig_fac*0;
         reig[1][0] = reig_fac*(-2.*beta*ny);
         reig[2][0] = reig_fac*(2.*beta*nx);
         reig[0][1] = reig_fac*(aa*beta);
         reig[1][1] = reig_fac*(ua*(aa+una) + beta*nx);
         reig[2][1] = reig_fac*(va*(aa+una) + beta*ny);
         reig[0][2] = reig_fac*(-aa*beta);
         reig[1][2] = reig_fac*(-ua*(aa-una) + beta*nx);
         reig[2][2] = reig_fac*(-va*(aa-una) + beta*ny);

         dq[0] = qr[0][iqr] - ql[0][iql];
         dq[1] = qr[1][iqr] - ql[1][iql];
         dq[2] = qr[2][iqr] - ql[2][iql];
         for(ia=3; ia<nv; ia++)
        {
            dq[ia] = qr[ia][iqr] - ql[ia][iql];
        }

         for( ia=3;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*la1;
        }

         dp = qr[2][iqr] - ql[2][iql]; //dp
         du = qr[0][iqr] - ql[0][iql]; //du
         dv = qr[1][iqr] - ql[1][iql]; //dv
         dw[0] = leig[0][0]*dp + leig[0][1]*du + leig[0][2]*dv;
         dw[1] = leig[1][0]*dp + leig[1][1]*du + leig[1][2]*dv;
         dw[2] = leig[2][0]*dp + leig[2][1]*du + leig[2][2]*dv;

         dw[0] *= la1;
         dw[1] *= la2;
         dw[2] *= la3;

         fa[0] = reig[0][0]*dw[0] + reig[0][1]*dw[1] + reig[0][2]*dw[2];
         fa[1] = reig[1][0]*dw[0] + reig[1][1]*dw[1] + reig[1][2]*dw[2];
         fa[2] = reig[2][0]*dw[0] + reig[2][1]*dw[1] + reig[2][2]*dw[2];
         //for( ia=3;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia] + dw5[ia]; }
         for( ia=3;ia<nv;ia++){ fa[ia]=   dw5[ia]; }

//         for(ia=0; ia<nv; ia++)  
//        {
//            cout << qr[ia][iqr] << " " << ql[ia][iql] << " " << qa[ia] << "\n";
//        }
//         if(ic==1)
//        {
//         for(ia=0; ia<nv; ia++)  
//        {
//            cout << fl[ia] << " " << fr[ia] << " " << fa[ia] << " " << ia << "\n"; 
//        }
//        } 
//         cout << rhsl[0][iql] << " " << rhsl[1][iql] << " " << rhsl[2][iql] << " " << iql << "\n";
//         cout << rhsr[0][iqr] << " " << rhsr[1][iqr] << " " << rhsr[2][iqr] << " " << iqr << "\n";

// assemble 
         f[0]= 0.5*( fr[0]+fl[0]-fa[0] )*wc[2][ic];
         f[1]= 0.5*( fr[1]+fl[1]-fa[1] )*wc[2][ic];
         f[2]= 0.5*( fr[2]+fl[2]-fa[2] )*wc[2][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];

         for( ia=3;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia] -fa[ia] )*wc[2][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }
         auxc[nauxf-1][ic]*= wc[2][ic];
//         cout << rhsl[0][iql] << " " << rhsl[1][iql] << " " << rhsl[2][iql] << " " << iql << " iflx\n";
//         cout << rhsr[0][iqr] << " " << rhsr[1][iqr] << " " << rhsr[2][iqr] << " " << iqr << " iflx\n";
//exit(0);
     }
  }

   void cACMGas::iflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            al,unl,rl,ll1,pl,fl[MxNVs];
      Real            ar,unr,rr,lr1,pr,fr[MxNVs];
      Real            lmax,fa[MxNVs];
      Real            f[MxNVs], m;
      Real            leig[4][4], reig[4][4], fac;
      Real            nx,ny,nz,una,ua,va,wa,qa[MxNVs],aa,la1,la2,la3,dw[4],dq[MxNVs],dp,dux,duy,duz,dw5[MxNVs];
      Real            n[3],n0[3],n1[3],x1,y1,z1,x2,y2,z2;

      Int             ia,ic,iql,iqr;

      for( ic=ics;ic<ice;ic++ )
     {

         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

 
// fluxes from the left

         pl= ql[3][iql];
         rl= auxl[0][iql];
         al= auxl[2][iql];

         unl=  wc[0][ic]*ql[0][iql];
         unl+= wc[1][ic]*ql[1][iql];
         unl+= wc[2][ic]*ql[2][iql];

         ll1= unl-wxdc[0][ic];

         m = ll1*rl;
         fl[0]= m*beta;
         fl[1]= m*ql[0][iql]+ wc[0][ic]*pl/density;
         fl[2]= m*ql[1][iql]+ wc[1][ic]*pl/density;
         fl[3]= m*ql[2][iql]+ wc[2][ic]*pl/density;
         for( ia=4;ia<nv;ia++ ){ fl[ia]= m*ql[ia][iql]; }

// fluxes from the right
         rr= auxr[0][iqr];
         pr= qr[3][iqr];
         rr= auxr[0][iqr];
         ar= auxl[2][iqr];

         unr=  wc[0][ic]*qr[0][iqr];
         unr+= wc[1][ic]*qr[1][iqr];
         unr+= wc[2][ic]*qr[2][iqr];

         lr1= unr-wxdc[0][ic];

         m = lr1 *rr;
         fr[0]= m*beta;
         fr[1]= m*qr[0][iqr]+ wc[0][ic]*pr/density;
         fr[2]= m*qr[1][iqr]+ wc[1][ic]*pr/density;
         fr[3]= m*qr[2][iqr]+ wc[2][ic]*pr/density;
         for( ia=4;ia<nv;ia++ ){ fr[ia]= m*qr[ia][iqr]; }

// Roe'e Riemann solver
         for(ia=0; ia<nv; ia++)
        {
            qa[ia] = 0.5*(ql[ia][iql]+qr[ia][iqr]);
        }
         nx = wc[0][ic];
         ny = wc[1][ic];
         nz = wc[2][ic];
         ua = qa[0];
         va = qa[1];
         wa = qa[2];
         una = ua*nx + va*ny + wa*nz;
         aa = sqrt(una*una+beta);
         la1= una-wxdc[0][ic];
         la2= una-0.5*wxdc[0][ic]+ aa;
         la3= una-0.5*wxdc[0][ic]- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );
         auxc[nauxf-1][ic]= lmax;

//dissipaition, Rusanov approximate Riemann solvers
//         fa[0] = lmax*(pr-pl);
//         fa[1] = lmax*(qr[0][iqr] - ql[0][iql]);
//         fa[2] = lmax*(qr[1][iqr] - ql[1][iql]);
//         fa[3] = lmax*(qr[2][iqr] - ql[2][iql]);
//         for(ia=4; ia<nv; ia++)
//        {
//            fa[ia] = lmax*(qr[ia][iqr] - qr[ia][iql]);
//        }

         n[0] = nx;
         n[1] = ny;
         n[2] = nz;
         tangent_vectors(n, n0, n1); //n0 x n1 = n

         x1 = n0[0];
         y1 = n0[1];
         z1 = n0[2];

         x2 = n1[0];
         y2 = n1[1];
         z2 = n1[2];

//         cout << wc[0][ic] << " " << wc[1][ic] << " " << wc[2][ic] << " wc\n";
//         cout << nx << " " << ny << " " << nz << " x\n";
//         cout << x1 << " " << y1 << " " << z1 << " x1\n";
//         cout << x2 << " " << y2 << " " << z2 << " x2\n";

         reig[0][0] = 0;
         reig[1][0] = x1;
         reig[2][0] = y1;
         reig[3][0] = z1;

         reig[0][1] = 0;
         reig[1][1] = x2;
         reig[2][1] = y2;
         reig[3][1] = z2;

         reig[0][2] = aa;
         reig[1][2] = nx+ua*la2/beta;
         reig[2][2] = ny+va*la2/beta;
         reig[3][2] = nz+wa*la2/beta;

         reig[0][3] = -aa;
         reig[1][3] = nx+ua*la3/beta;
         reig[2][3] = ny+va*la3/beta;
         reig[3][3] = nz+wa*la3/beta;

         fac = 1./(aa*aa+small);
         leig[0][0] =  nx*(wa*y2-va*z2)+ny*(ua*z2-wa*x2)+nz*(va*x2-ua*y2);
         leig[0][1] =  beta*(nz*y2-ny*z2)+la1*(wa*y2-va*z2);
         leig[0][2] =  beta*(nx*z2-nz*x2)+la1*(ua*z2-wa*x2);
         leig[0][3] =  beta*(ny*x2-nx*y2)+la1*(va*x2-ua*y2);
         leig[0][0]*=  fac;
         leig[0][1]*=  fac;
         leig[0][2]*=  fac;
         leig[0][3]*=  fac;
  
         leig[1][0] =  nx*(va*z1-wa*y1)+ny*(wa*x1-ua*z1)+nz*(ua*y1-va*x1);
         leig[1][1] =  beta*(ny*z1-nz*y1)+la1*(va*z1-wa*y1);
         leig[1][2] =  beta*(nz*x1-nx*z1)+la1*(wa*x1-ua*z1);
         leig[1][3] =  beta*(nx*y1-ny*x1)+la1*(ua*y1-va*x1);
         leig[1][0]*=  fac;
         leig[1][1]*=  fac;
         leig[1][2]*=  fac;
         leig[1][3]*=  fac;
  
         leig[2][0] = -0.5*la3;
         leig[2][1] =  0.5*beta*nx;
         leig[2][2] =  0.5*beta*ny;
         leig[2][3] =  0.5*beta*nz;
         leig[2][0]*=  fac;
         leig[2][1]*=  fac;
         leig[2][2]*=  fac;
         leig[2][3]*=  fac;
  
         leig[3][0] = -0.5*la2;
         leig[3][1] =  0.5*beta*nx;
         leig[3][2] =  0.5*beta*ny;
         leig[3][3] =  0.5*beta*nz;
         leig[3][0]*=  fac;
         leig[3][1]*=  fac;
         leig[3][2]*=  fac;
         leig[3][3]*=  fac;


         dq[0] = qr[0][iqr] - ql[0][iql];
         dq[1] = qr[1][iqr] - ql[1][iql];
         dq[2] = qr[2][iqr] - ql[2][iql];
         dq[3] = qr[3][iqr] - ql[3][iql];
         for(ia=4; ia<nv; ia++)
        {
            dq[ia] = qr[ia][iqr] - ql[ia][iql];
        }

         for( ia=4;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*fabs(la1);
        }

         dp = qr[3][iqr] - ql[3][iql]; //dp
         dux= qr[0][iqr] - ql[0][iql]; //dux
         duy= qr[1][iqr] - ql[1][iql]; //duy
         duz= qr[2][iqr] - ql[2][iql]; //duz
         dw[0] = leig[0][0]*dp + leig[0][1]*dux + leig[0][2]*duy + leig[0][3]*duz;
         dw[1] = leig[1][0]*dp + leig[1][1]*dux + leig[1][2]*duy + leig[1][3]*duz;
         dw[2] = leig[2][0]*dp + leig[2][1]*dux + leig[2][2]*duy + leig[2][3]*duz;
         dw[3] = leig[3][0]*dp + leig[3][1]*dux + leig[3][2]*duy + leig[3][3]*duz;

         dw[0] *= fabs(la1);
         dw[1] *= fabs(la1);
         dw[2] *= fabs(la2);
         dw[3] *= fabs(la3);

         fa[0] = reig[0][0]*dw[0] + reig[0][1]*dw[1] + reig[0][2]*dw[2] + reig[0][3]*dw[3];
         fa[1] = reig[1][0]*dw[0] + reig[1][1]*dw[1] + reig[1][2]*dw[2] + reig[1][3]*dw[3];
         fa[2] = reig[2][0]*dw[0] + reig[2][1]*dw[1] + reig[2][2]*dw[2] + reig[2][3]*dw[3];
         fa[3] = reig[3][0]*dw[0] + reig[3][1]*dw[1] + reig[3][2]*dw[2] + reig[3][3]*dw[3];
         //for( ia=4;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]/beta + dw5[ia]; }
         for( ia=4;ia<nv;ia++){ fa[ia]= dw5[ia]; }

// assemble 
         f[0]= 0.5*( fr[0]+ fl[0] -fa[0])*wc[3][ic];
         f[1]= 0.5*( fr[1]+ fl[1] -fa[1])*wc[3][ic];
         f[2]= 0.5*( fr[2]+ fl[2] -fa[2])*wc[3][ic];
         f[3]= 0.5*( fr[3]+ fl[3] -fa[3])*wc[3][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];

         for( ia=4;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }
         auxc[nauxf-1][ic]*= wc[3][ic];
//         cout << rhsl[0][iql] << " " << rhsl[1][iql] << " " << rhsl[2][iql] << " " << rhsl[3][iql] << " " << iql << " iflx\n";
//         cout << rhsr[0][iqr] << " " << rhsr[1][iqr] << " " << rhsr[2][iqr] << " " << rhsr[3][iqr] << " " << iqr << " iflx\n";
//         cout << "\n";
     }
  }

   void cACMGas::dvar2( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dU[], Real *dq[] )
  {
      Int iq;

      for( iq=iqs;iq<iqe;iq++ )
     {
         //dU: p, u, v
         //dq: u, v, p
         dq[0][iq]= dU[1][iq];
         dq[1][iq]= dU[2][iq];
         dq[2][iq]= dU[0][iq];
     }
  }

   void cACMGas::dvar3( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dU[], Real *dq[] )
  {
      Int iq;

      for( iq=iqs;iq<iqe;iq++ )
     {
         //dU: p, u, v, w
         //dq: u, v, w, p
         dq[0][iq]= dU[1][iq];
         dq[1][iq]= dU[2][iq];
         dq[2][iq]= dU[3][iq];
         dq[3][iq]= dU[0][iq];
     }
  }

   void cACMGas::auxv2( Int iqs, Int iqe, Real *q[], Real *aux[] )
  {
      Int iq; 
      Real            mu=1.57e-5,kappa=2.624e-2;//mu is used as kinematic viscosity

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);
  
      //by default use the nu of air at 25 degree,otherwise, use specified nu
      if(nu>0) mu = nu;   
      for( iq=iqs;iq<iqe;iq++ )
     {
// density
         aux[0][iq]= 1;
// kinetic energy
         aux[1][iq]= q[0][iq]*q[0][iq];
         aux[1][iq]+= q[1][iq]*q[1][iq];
         aux[1][iq]*= 0.5;
// speed of sound and total entalpy
         aux[2][iq]= sqrt(2*aux[1][iq]+beta);
         aux[3][iq]= -1;
         aux[4][iq]= -1;
         aux[5][iq]= mu/density;
         aux[6][iq]= kappa;
     } 
  }

   void cACMGas::auxv3( Int iqs, Int iqe, Real *q[], Real *aux[] )
  {
      Int iq; 
      Real            mu=1.57e-5,kappa=2.624e-2;//mu is used as kinematic viscosity

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);
      
      //by default use the nu of air at 25 degree,otherwise, use specified nu
      if(nu>0) mu = nu;   
      for( iq=iqs;iq<iqe;iq++ )
     {
// density
         aux[0][iq]= 1;
// kinetic energy
         aux[1][iq]=  q[0][iq]*q[0][iq];
         aux[1][iq]+= q[1][iq]*q[1][iq];
         aux[1][iq]+= q[2][iq]*q[2][iq];
         aux[1][iq]*= 0.5;
// speed of sound and total entalpy
         aux[2][iq]= sqrt(2*aux[1][iq]+beta);
         aux[3][iq]= -1;
         aux[4][iq]= -1;
         aux[5][iq]= mu/density;
         aux[6][iq]= kappa;
     } 
  }

//compute mu using sutherland's law
//   void cACMGas::auxv3( Int iqs, Int iqe, Real *q[], Real *aux[] )
//  {
//      Int iq; 
//      Int iv;
//      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;
//      //Real            l=1.512041288e-6;
//      //Real            c = 120.;
//      Real            l = 1.45e-6;
//      Real            c = 110.;
//
//      kappa/= (unit[0]*unit[0]*unit[0]);
//      Real cp=rg*gam/(gam-1);
//      
//      for( iq=iqs;iq<iqe;iq++ )
//     {
//         //suhterland's law
//         mu = l*pow(q[3][iq], 1.5)/(q[3][iq]+c);
//         mu/= unit[0];
//
//// density
//         aux[0][iq]= q[4][iq]/( rg*q[3][iq] );
//// kinetic energy
//         aux[1][iq]=  q[0][iq]*q[0][iq];
//         aux[1][iq]+= q[1][iq]*q[1][iq];
//         aux[1][iq]+= q[2][iq]*q[2][iq];
//         aux[1][iq]*= 0.5;
//// speed of sound and total entalpy
//         aux[2][iq]= gam*rg* q[3][iq]; 
//         aux[3][iq]= aux[2][iq]/(gam-1)+ aux[1][iq];
//         aux[2][iq]= sqrt( aux[2][iq] );
//         aux[4][iq]= cp;
//         aux[5][iq]= mu;
//         aux[6][iq]= kappa;
//     } 
//  }

   void cACMGas::wflx22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],  
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real f[MxNVs];
      Int  ic,iql,iqr;
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };
 
// fluxes from the left
         f[1]=    wc[0][ic]* qr[2][iqr]* wc[2][ic]/density;
         f[2]=    wc[1][ic]* qr[2][iqr]* wc[2][ic]/density;
         auxc[nauxf-1][ic]= ( auxr[2][iqr]+fabs(wxdc[0][ic]) )*wc[2][ic];

         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];

         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];

     }
  }

   void cACMGas::wflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],  
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real f[MxNVs];
      Int  ic,iql,iqr;
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

// fluxes from the left
         f[1]=    wc[0][ic]* qr[3][iqr]* wc[3][ic]/density;
         f[2]=    wc[1][ic]* qr[3][iqr]* wc[3][ic]/density;
         f[3]=    wc[2][ic]* qr[3][iqr]* wc[3][ic]/density;
         auxc[nauxf-1][ic]= ( auxr[2][iqr]+fabs(wxdc[0][ic]) )*wc[3][ic];

         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];

         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];

     }
  }

   void cACMGas::iflxmuscl22( Int ics,Int ice, Int *icql, Int idl, Real *xql[], Real *ql0[], Real *dxdxl[], Real **dqdxl[], Real *auxl0[], Real *rhsl[],
                                                 Int *icqr, Int idr, Real *xqr[], Real *qr0[], Real *dxdxr[], Real **dqdxr[], Real *auxr0[], Real *rhsr[], 
                                                                     Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd ) 
  {
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Int             ic,iql,iqr,ia;
      Real            xn[3],wn[3];
      Real            al,unl,rl,ll1,pl,fl[MxNVs],ql[MxNVs];
      Real            ar,unr,rr,lr1,pr,fr[MxNVs],qr[MxNVs];
      Real            lmax, m;
      Real            f[MxNVs];
      Real            fa[MxNVs], leig[3][3], reig[3][3], reig_fac;
      Real            nx, ny, una, ua, va, qa[MxNVs], aa, la1, la2, la3, dw[3], dq[MxNVs], dp, du, dv, dw5[MxNVs];

      for( ic=ics;ic<ice;ic++ )
     {
         wn[0]= wc[0][ic];
         wn[1]= wc[1][ic];

         xn[0]= xc[0][ic];
         xn[1]= xc[1][ic];

         iql= ic;
         if( icql ){ iql= icql[ic]; }
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; }
         if(grd->getlimtype()==1)
        {
            grd->deltq_venk( nx, nv, ic, iql, xql, dxdxl, dqdxl, dql, 
                                         iqr, xqr, dxdxr, dqdxr, dqr, 
                                         xc, wc );
//            for(ia=0; ia<nv; ia++)
//           {
//               cout << dql[ia] << " ";
//           }
//            for(ia=0; ia<nv; ia++)
//           {
//               cout << dqr[ia] << " ";
//           }
//            cout << "\n";
            if(icql!=NULL && icqr!=NULL)
           {
               //internal face, save the values on the face
               for( ia=0;ia<nv;ia++ )
              {
                  auxl0[ia][ic]= ql0[ia][iql]+ dql[ia];
                  auxr0[ia][ic]= qr0[ia][iqr]+ dqr[ia];
              }
           }
        }
         else 
        {
            grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );
        }

         for( ia=0;ia<nv;ia++ )
        {
            ql[ia]= ql0[ia][iql]+ dql[ia];
            qr[ia]= qr0[ia][iqr]+ dqr[ia];
        }


// fluxes from the left
         pl=   ql[2];
         rl=   1;
         al = ql[0]*ql[0];
         al+= ql[1]*ql[1];
         al= sqrt(al+beta);
         unl=  wc[0][ic]*ql[0];
         unl+= wc[1][ic]*ql[1];

         ll1= unl-wxdc[0][ic];

         m = ll1*rl;
         fl[0]= m*beta;
         fl[1]= m*ql[0]+   wc[0][ic]*pl/density;
         fl[2]= m*ql[1]+   wc[1][ic]*pl/density;
         for( ia=3;ia<nv;ia++ ){ fl[ia]= m*ql[ia]; }

// fluxes from the right
         pr= qr[2];
         rr= 1;
         ar = qr[0]*qr[0];
         ar+= qr[1]*qr[1];
         ar= sqrt(ar+beta);
         unr=  wc[0][ic]*qr[0];
         unr+= wc[1][ic]*qr[1];

         lr1= unr-wxdc[0][ic];

         m = lr1 *rr;
         fr[0]= m*beta;
         fr[1]= m*qr[0]+ wc[0][ic]*pr/density;
         fr[2]= m*qr[1]+ wc[1][ic]*pr/density;
         for( ia=3;ia<nv;ia++ ){ fr[ia]= m*qr[ia]; }


// Roe'e Riemann solver
         for(ia=0; ia<nv; ia++)
        {
            qa[ia] = 0.5*(ql[ia]+qr[ia]);
        }
         nx = wn[0];
         ny = wn[1];
         ua = qa[0];
         va = qa[1];
         una = ua*nx + va*ny;
         aa = sqrt(una*una+beta);
         la1= una-wxdc[0][ic];
         la2= una-0.5*wxdc[0][ic]+ aa;
         la3= una-0.5*wxdc[0][ic]- aa;
         la1 = fabs(la1);
         la2 = fabs(la2);
         la3 = fabs(la3);

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );
         auxc[nauxf-1][ic]= lmax;

//         fa[0] = lmax*(pr - pl);
//         fa[1] = lmax*(qr[0] - ql[0]);
//         fa[2] = lmax*(qr[1] - ql[1]);
//         for( ia=3;ia<nv;ia++){ fa[ia]=  lmax*(qr[ia] - ql[ia]); }

         leig[0][0] = ny*ua-nx*va;
         leig[1][0] = aa-una;
         leig[2][0] = -aa-una;
         leig[0][1] = -una*va-beta*ny;
         leig[1][1] = beta*nx;
         leig[2][1] = beta*nx;
         leig[0][2] = una*ua+beta*nx;
         leig[1][2] = beta*ny;
         leig[2][2] = beta*ny;

         reig_fac = 1./(2*beta*aa*aa);
         reig[0][0] = reig_fac*0;
         reig[1][0] = reig_fac*(-2.*beta*ny);
         reig[2][0] = reig_fac*(2.*beta*nx);
         reig[0][1] = reig_fac*(aa*beta);
         reig[1][1] = reig_fac*(ua*(aa+una) + beta*nx);
         reig[2][1] = reig_fac*(va*(aa+una) + beta*ny);
         reig[0][2] = reig_fac*(-aa*beta);
         reig[1][2] = reig_fac*(-ua*(aa-una) + beta*nx);
         reig[2][2] = reig_fac*(-va*(aa-una) + beta*ny);

         dq[0] = qr[0] - ql[0];
         dq[1] = qr[1] - ql[1];
         dq[2] = qr[2] - ql[2];
         for(ia=3; ia<nv; ia++)
        {
            dq[ia] = qr[ia] - ql[ia];
        }

         for( ia=3;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*la1;
        }

         dp = qr[2] - ql[2]; //dp
         du = qr[0] - ql[0]; //du
         dv = qr[1] - ql[1]; //dv
         dw[0] = leig[0][0]*dp + leig[0][1]*du + leig[0][2]*dv;
         dw[1] = leig[1][0]*dp + leig[1][1]*du + leig[1][2]*dv;
         dw[2] = leig[2][0]*dp + leig[2][1]*du + leig[2][2]*dv;

         dw[0] *= la1;
         dw[1] *= la2;
         dw[2] *= la3;

         fa[0] = reig[0][0]*dw[0] + reig[0][1]*dw[1] + reig[0][2]*dw[2];
         fa[1] = reig[1][0]*dw[0] + reig[1][1]*dw[1] + reig[1][2]*dw[2];
         fa[2] = reig[2][0]*dw[0] + reig[2][1]*dw[1] + reig[2][2]*dw[2];
         //for( ia=3;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]/beta + dw5[ia]; }
         for( ia=3;ia<nv;ia++){ fa[ia]=   dw5[ia]; }

//         for(ia=0; ia<nv; ia++)
//        {
//            cout << ia << " " << fa[ia] << "\n";
//        }

//         cout << rhsl[0][iql] << " " << rhsl[1][iql] << " " << rhsl[2][iql] << " " << iql << " before\n";
//         cout << rhsr[0][iqr] << " " << rhsr[1][iqr] << " " << rhsr[2][iqr] << " " << iqr << " before\n";
// assemble 

         f[0]= 0.5*( fr[0]+ fl[0] -fa[0])*wc[2][ic];
         f[1]= 0.5*( fr[1]+ fl[1] -fa[1])*wc[2][ic];
         f[2]= 0.5*( fr[2]+ fl[2] -fa[2])*wc[2][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];

         for( ia=3;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia] - fa[ia] )*wc[2][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }

//         cout << rhsl[0][iql] << " " << rhsl[1][iql] << " " << rhsl[2][iql] << " " << iql << " after\n";
//         cout << rhsr[0][iqr] << " " << rhsr[1][iqr] << " " << rhsr[2][iqr] << " " << iqr << " after\n";

         auxc[nauxf-1][ic]*= wc[2][ic];
     }
//exit(0);
  }

   void cACMGas::iflxmuscl33( Int ics,Int ice, Int *icql, Int idl, Real *xql[], Real *ql0[], Real *dxdxl[], Real **dqdxl[], Real *auxl0[], Real *rhsl[],
                                                 Int *icqr, Int idr, Real *xqr[], Real *qr0[], Real *dxdxr[], Real **dqdxr[], Real *auxr0[], Real *rhsr[], 
                                                                     Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd ) 
  {
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Int             ic,iql,iqr,ia;
      Real            xn[3],wn[3];
      Real            al,unl,rl,ll1,pl,fl[MxNVs],ql[MxNVs];
      Real            ar,unr,rr,lr1,pr,fr[MxNVs],qr[MxNVs];
      Real            lmax, m;
      Real            f[MxNVs], fa[MxNVs];
      Real            leig[4][4], reig[4][4], fac;
      Real            nx,ny,nz,una,ua,va,wa,qa[MxNVs],aa,la1,la2,la3,dw[4],dq[MxNVs],dp,dux,duy,duz,dw5[MxNVs];
      Real            n[3],n0[3],n1[3],x1,y1,z1,x2,y2,z2;

      for( ic=ics;ic<ice;ic++ )
     {
         wn[0]= wc[0][ic];
         wn[1]= wc[1][ic];
         wn[2]= wc[2][ic];

         xn[0]= xc[0][ic];
         xn[1]= xc[1][ic];
         xn[2]= xc[2][ic];

         iql= ic;
         if( icql ){ iql= icql[ic]; }
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; }
         if(grd->getlimtype()==1)
        {
            grd->deltq_venk( nx, nv, ic, iql, xql, dxdxl, dqdxl, dql, 
                                         iqr, xqr, dxdxr, dqdxr, dqr, 
                                         xc, wc );

            if(icql!=NULL && icqr!=NULL)
           {
               //internal face, save the values on the face
               for( ia=0;ia<nv;ia++ )
              {
                  auxl0[ia][ic]= ql0[ia][iql]+ dql[ia];
                  auxr0[ia][ic]= qr0[ia][iqr]+ dqr[ia];
              }
           }
        }
         else
        {
            grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );
        }

         for( ia=0;ia<nv;ia++ )
        {
            ql[ia]= ql0[ia][iql]+ dql[ia];
            qr[ia]= qr0[ia][iqr]+ dqr[ia];
        }


// fluxes from the left
         pl=   ql[3];
         rl=   1;
         al = ql[0]*ql[0];
         al+= ql[1]*ql[1];
         al+= ql[2]*ql[2];
         al= sqrt(al+beta);
         unl=  wc[0][ic]*ql[0];
         unl+= wc[1][ic]*ql[1];
         unl+= wc[2][ic]*ql[2];

         ll1= unl-wxdc[0][ic];

         m = ll1*rl;
         fl[0]= m*beta;
         fl[1]= m*ql[0]+   wc[0][ic]*pl/density;
         fl[2]= m*ql[1]+   wc[1][ic]*pl/density;
         fl[3]= m*ql[2]+   wc[2][ic]*pl/density;
         for( ia=4;ia<nv;ia++ ){ fl[ia]= m*ql[ia]; }

// fluxes from the right
         pr= qr[3];
         rr= 1;
         ar = qr[0]*qr[0];
         ar+= qr[1]*qr[1];
         ar+= qr[2]*qr[2];
         ar= sqrt(ar+beta);
         unr=  wc[0][ic]*qr[0];
         unr+= wc[1][ic]*qr[1];
         unr+= wc[2][ic]*qr[2];

         lr1= unr-wxdc[0][ic];

         m = lr1 *rr;
         fr[0]= m*beta;
         fr[1]= m*qr[0]+ wc[0][ic]*pr/density;
         fr[2]= m*qr[1]+ wc[1][ic]*pr/density;
         fr[3]= m*qr[2]+ wc[2][ic]*pr/density;
         for( ia=4;ia<nv;ia++ ){ fr[ia]= m*qr[ia]; }

//dissipaition, Rusanov approximate Riemann solvers
//         fa[0] = lmax*(pr-pl);
//         fa[1] = lmax*(qr[0] - ql[0]);
//         fa[2] = lmax*(qr[1] - ql[1]);
//         fa[3] = lmax*(qr[2] - ql[2]);
//         for(ia=4; ia<nv; ia++)
//        {
//            fa[ia] = lmax*(qr[ia] - ql[ia]);
//        }
////         for(ia=0; ia<nv; ia++)
////        {
////            cout << ia << " " << fl[ia] << " " << fr[ia] << " " << fa[ia] << "\n";
////        }
// Roe'e Riemann solver
         for(ia=0; ia<nv; ia++)
        {
            qa[ia] = 0.5*(ql[ia]+qr[ia]);
        }
         nx = wc[0][ic];
         ny = wc[1][ic];
         nz = wc[2][ic];
         ua = qa[0];
         va = qa[1];
         wa = qa[2];
         una = ua*nx + va*ny + wa*nz;
         aa = sqrt(una*una+beta);
         la1= una-wxdc[0][ic];
         la2= una-0.5*wxdc[0][ic]+ aa;
         la3= una-0.5*wxdc[0][ic]- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );
         auxc[nauxf-1][ic]= lmax;

         n[0] = nx;
         n[1] = ny;
         n[2] = nz;
         tangent_vectors(n, n0, n1); //n0 x n1 = n

         x1 = n0[0];
         y1 = n0[1];
         z1 = n0[2];

         x2 = n1[0];
         y2 = n1[1];
         z2 = n1[2];

         reig[0][0] = 0;
         reig[1][0] = x1;
         reig[2][0] = y1;
         reig[3][0] = z1;

         reig[0][1] = 0;
         reig[1][1] = x2;
         reig[2][1] = y2;
         reig[3][1] = z2;

         reig[0][2] = aa;
         reig[1][2] = nx+ua*la2/beta;
         reig[2][2] = ny+va*la2/beta;
         reig[3][2] = nz+wa*la2/beta;

         reig[0][3] = -aa;
         reig[1][3] = nx+ua*la3/beta;
         reig[2][3] = ny+va*la3/beta;
         reig[3][3] = nz+wa*la3/beta;

         fac = 1./(aa*aa+small);
         leig[0][0] =  nx*(wa*y2-va*z2)+ny*(ua*z2-wa*x2)+nz*(va*x2-ua*y2);
         leig[0][1] =  beta*(nz*y2-ny*z2)+la1*(wa*y2-va*z2);
         leig[0][2] =  beta*(nx*z2-nz*x2)+la1*(ua*z2-wa*x2);
         leig[0][3] =  beta*(ny*x2-nx*y2)+la1*(va*x2-ua*y2);
         leig[0][0]*=  fac;
         leig[0][1]*=  fac;
         leig[0][2]*=  fac;
         leig[0][3]*=  fac;
  
         leig[1][0] =  nx*(va*z1-wa*y1)+ny*(wa*x1-ua*z1)+nz*(ua*y1-va*x1);
         leig[1][1] =  beta*(ny*z1-nz*y1)+la1*(va*z1-wa*y1);
         leig[1][2] =  beta*(nz*x1-nx*z1)+la1*(wa*x1-ua*z1);
         leig[1][3] =  beta*(nx*y1-ny*x1)+la1*(ua*y1-va*x1);
         leig[1][0]*=  fac;
         leig[1][1]*=  fac;
         leig[1][2]*=  fac;
         leig[1][3]*=  fac;
  
         leig[2][0] = -0.5*la3;
         leig[2][1] =  0.5*beta*nx;
         leig[2][2] =  0.5*beta*ny;
         leig[2][3] =  0.5*beta*nz;
         leig[2][0]*=  fac;
         leig[2][1]*=  fac;
         leig[2][2]*=  fac;
         leig[2][3]*=  fac;
  
         leig[3][0] = -0.5*la2;
         leig[3][1] =  0.5*beta*nx;
         leig[3][2] =  0.5*beta*ny;
         leig[3][3] =  0.5*beta*nz;
         leig[3][0]*=  fac;
         leig[3][1]*=  fac;
         leig[3][2]*=  fac;
         leig[3][3]*=  fac;


         dq[0] = qr[0] - ql[0];
         dq[1] = qr[1] - ql[1];
         dq[2] = qr[2] - ql[2];
         dq[3] = qr[3] - ql[3];
         for(ia=4; ia<nv; ia++)
        {
            dq[ia] = qr[ia] - ql[ia];
        }

         for( ia=4;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*fabs(la1);
        }

         dp = qr[3] - ql[3]; //dp
         dux= qr[0] - ql[0]; //dux
         duy= qr[1] - ql[1]; //duy
         duz= qr[2] - ql[2]; //duz
         dw[0] = leig[0][0]*dp + leig[0][1]*dux + leig[0][2]*duy + leig[0][3]*duz;
         dw[1] = leig[1][0]*dp + leig[1][1]*dux + leig[1][2]*duy + leig[1][3]*duz;
         dw[2] = leig[2][0]*dp + leig[2][1]*dux + leig[2][2]*duy + leig[2][3]*duz;
         dw[3] = leig[3][0]*dp + leig[3][1]*dux + leig[3][2]*duy + leig[3][3]*duz;

         dw[0] *= fabs(la1);
         dw[1] *= fabs(la1);
         dw[2] *= fabs(la2);
         dw[3] *= fabs(la3);

         fa[0] = reig[0][0]*dw[0] + reig[0][1]*dw[1] + reig[0][2]*dw[2] + reig[0][3]*dw[3];
         fa[1] = reig[1][0]*dw[0] + reig[1][1]*dw[1] + reig[1][2]*dw[2] + reig[1][3]*dw[3];
         fa[2] = reig[2][0]*dw[0] + reig[2][1]*dw[1] + reig[2][2]*dw[2] + reig[2][3]*dw[3];
         fa[3] = reig[3][0]*dw[0] + reig[3][1]*dw[1] + reig[3][2]*dw[2] + reig[3][3]*dw[3];
         //for( ia=4;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]/beta + dw5[ia]; }
         for( ia=4;ia<nv;ia++){ fa[ia]= dw5[ia]; }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0] - fa[0])*wc[3][ic];
         f[1]= 0.5*( fr[1]+ fl[1] - fa[1])*wc[3][ic];
         f[2]= 0.5*( fr[2]+ fl[2] - fa[2])*wc[3][ic];
         f[3]= 0.5*( fr[3]+ fl[3] - fa[3])*wc[3][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];

         for( ia=4;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia] - fa[ia] )*wc[3][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }

         auxc[nauxf-1][ic]*= wc[3][ic];
//         cout << rhsl[0][iql] << " " << rhsl[1][iql] << " " << rhsl[2][iql] << " " << rhsl[3][iql] << " " << iql << " iflxmuscl\n";
//         cout << rhsr[0][iqr] << " " << rhsr[1][iqr] << " " << rhsr[2][iqr] << " " << rhsr[3][iqr] << " " << iqr << " iflxmuscl\n";
     }
//exit(0);
  }


   void cACMGas::dwflx22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            dpr;

      Int             ic,iql,iqr;
      Real            f[MxNVs];

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

         dpr=  dauxr[2][iqr]/density;
         f[1]=   wc[0][ic]*dpr*wc[2][ic];
         f[2]=   wc[1][ic]*dpr*wc[2][ic];

         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];

         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];

     }
  }

   void cACMGas::dwflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            dpr;

      Int             ic,iql,iqr;
      Real            f[MxNVs];

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

         dpr=  dauxr[3][iqr]/density;
         f[1]=   wc[0][ic]*dpr*wc[3][ic];
         f[2]=   wc[1][ic]*dpr*wc[3][ic];
         f[3]=   wc[2][ic]*dpr*wc[3][ic];

         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];
         resl[3][iql]-= f[3];

         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];
         resr[3][iqr]+= f[3];

     }
  }

   void cACMGas::diflx22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                    Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                    Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            unl,rl,fl[MxNVs];
      Real            unr,rr,fr[MxNVs];

      Real            ml, dpl, dunl, dml;
      Real            mr, dpr, dunr, dmr;

      Int             ia,ic,iql,iqr;

      Real            f[MxNVs];
      Real            fa[MxNVs], leig[3][3], reig[3][3], reig_fac;
      Real            nx, ny, una, ua, va, qa[MxNVs], aa, la1, la2, la3, dw[3], dq[MxNVs], dp, du, dv, dw5[MxNVs];

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

// fluxes from the left

         rl= auxl[0][iql];

         dpl=  dauxl[2][iql]/density;

         unl=  wc[0][ic]*ql[0][iql]; 
         unl+= wc[1][ic]*ql[1][iql]; 
         unl-= wxdc[0][ic];

         dunl=  wc[0][ic]*dauxl[0][iql]; 
         dunl+= wc[1][ic]*dauxl[1][iql]; 

         ml= unl*rl;
         dml= dunl*rl;
         fl[0]= dml*beta;
         fl[1]= dml*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
         fl[2]= dml*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
         for( ia=3;ia<nv;ia++ )
        {
            fl[ia]= dml*ql[ia][iql]+ ml*dauxl[ia][iql];
        }

// fluxes from the right

         rr= auxr[0][iqr];

         dpr=  dauxr[2][iqr]/density;

         unr=  wc[0][ic]*qr[0][iqr]; 
         unr+= wc[1][ic]*qr[1][iqr]; 
         unr-= wxdc[0][ic];

         dunr=  wc[0][ic]*dauxr[0][iqr]; 
         dunr+= wc[1][ic]*dauxr[1][iqr]; 

         mr= unr*rr;
         dmr= dunr*rr;
         fr[0]= dmr*beta;
         fr[1]= dmr*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
         fr[2]= dmr*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
         for( ia=3;ia<nv;ia++ )
        {
            fr[ia]= dmr*qr[ia][iqr]+ mr*dauxr[ia][iqr];
        }

////dissipaition, Rusanov approximate Riemann solvers
//         lmax = auxc[nauxf-1][ic];
//         for(ia=0; ia<nv; ia++)
//        {
//            fa[ia] = lmax*(dqr[ia][iqr] - dql[ia][iql]);
//        }
// Roe'e Riemann solver
         for(ia=0; ia<nv; ia++)
        {
            qa[ia] = 0.5*(ql[ia][iql]+qr[ia][iqr]);
        }
         nx = wc[0][ic];
         ny = wc[1][ic];
         ua = qa[0];
         va = qa[1];
         una = ua*nx + va*ny;
         aa = sqrt(una*una+beta);
         la1= una-wxdc[0][ic];
         la2= una-0.5*wxdc[0][ic]+ aa;
         la3= una-0.5*wxdc[0][ic]- aa;
         la1 = fabs(la1);
         la2 = fabs(la2);
         la3 = fabs(la3);

//         Real lmax = auxc[nauxf-1][ic]/wc[2][ic];
//         fa[0] = lmax*(dpr - dpl);
//         fa[1] = lmax*(dauxr[0][iqr] - dauxl[0][iql]);
//         fa[2] = lmax*(dauxr[1][iqr] - dauxl[1][iql]);
//         for( ia=3;ia<nv;ia++){ fa[ia]=  lmax*(dauxr[ia][iqr] - dauxl[ia][iql]); }

         leig[0][0] = ny*ua-nx*va;
         leig[1][0] = aa-una;
         leig[2][0] = -aa-una;
         leig[0][1] = -una*va-beta*ny;
         leig[1][1] = beta*nx;
         leig[2][1] = beta*nx;
         leig[0][2] = una*ua+beta*nx;
         leig[1][2] = beta*ny;
         leig[2][2] = beta*ny;

         reig_fac = 1./(2*beta*aa*aa);
         reig[0][0] = reig_fac*0;
         reig[1][0] = reig_fac*(-2.*beta*ny);
         reig[2][0] = reig_fac*(2.*beta*nx);
         reig[0][1] = reig_fac*(aa*beta);
         reig[1][1] = reig_fac*(ua*(aa+una) + beta*nx);
         reig[2][1] = reig_fac*(va*(aa+una) + beta*ny);
         reig[0][2] = reig_fac*(-aa*beta);
         reig[1][2] = reig_fac*(-ua*(aa-una) + beta*nx);
         reig[2][2] = reig_fac*(-va*(aa-una) + beta*ny);

         dq[0] = dauxr[0][iqr] - dauxl[0][iql];
         dq[1] = dauxr[1][iqr] - dauxl[1][iql];
         dq[2] = dauxr[2][iqr] - dauxl[2][iql];
         for(ia=3; ia<nv; ia++)
        {
            dq[ia] = dauxr[ia][iqr] - dauxl[ia][iql];
        }

         for( ia=3;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*la1;
        }

         dp = dauxr[2][iqr] - dauxl[2][iql]; //dp
         du = dauxr[0][iqr] - dauxl[0][iql]; //du
         dv = dauxr[1][iqr] - dauxl[1][iql]; //dv
         dw[0] = leig[0][0]*dp + leig[0][1]*du + leig[0][2]*dv;
         dw[1] = leig[1][0]*dp + leig[1][1]*du + leig[1][2]*dv;
         dw[2] = leig[2][0]*dp + leig[2][1]*du + leig[2][2]*dv;

         dw[0] *= la1;
         dw[1] *= la2;
         dw[2] *= la3;

         fa[0] = reig[0][0]*dw[0] + reig[0][1]*dw[1] + reig[0][2]*dw[2];
         fa[1] = reig[1][0]*dw[0] + reig[1][1]*dw[1] + reig[1][2]*dw[2];
         fa[2] = reig[2][0]*dw[0] + reig[2][1]*dw[1] + reig[2][2]*dw[2];
         //for( ia=3;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia] + dw5[ia]; }
         for( ia=3;ia<nv;ia++){ fa[ia]=   dw5[ia]; }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0] -fa[0] )*wc[2][ic];
         f[1]= 0.5*( fr[1]+ fl[1] -fa[1] )*wc[2][ic];
         f[2]= 0.5*( fr[2]+ fl[2] -fa[2] )*wc[2][ic];

         resl[0][iql]-= f[0];
         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];

         resr[0][iqr]+= f[0];
         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];

         for( ia=3;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia] - fa[ia] )*wc[2][ic];
            resl[ia][iql]-= f[ia];
            resr[ia][iqr]+= f[ia];
        }

     }
  }

   void cACMGas::diflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            unl,rl,fl[MxNVs];
      Real            unr,rr,fr[MxNVs];

      Real            ml, dpl, dunl, dml;
      Real            mr, dpr, dunr, dmr;

      Int             ia,ic,iql,iqr;

      Real            fa[MxNVs];
      Real            f[MxNVs], lmax;

      Real            leig[4][4], reig[4][4], fac;
      Real            nx,ny,nz,una,ua,va,wa,qa[MxNVs],aa,la1,la2,la3,dw[4],dq[MxNVs],dp,dux,duy,duz,dw5[MxNVs];
      Real            n[3],n0[3],n1[3],x1,y1,z1,x2,y2,z2;

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

// fluxes from the left

         rl= auxl[0][iql];

         dpl=  dauxl[3][iql]/density;

         unl=  wc[0][ic]*ql[0][iql]; 
         unl+= wc[1][ic]*ql[1][iql]; 
         unl+= wc[2][ic]*ql[2][iql]; 
         unl-= wxdc[0][ic];

         dunl=  wc[0][ic]*dauxl[0][iql]; 
         dunl+= wc[1][ic]*dauxl[1][iql]; 
         dunl+= wc[2][ic]*dauxl[2][iql]; 

         ml= unl*rl;
         dml= dunl*rl;
         fl[0]= dml*beta;
         fl[1]= dml*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
         fl[2]= dml*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
         fl[3]= dml*ql[2][iql]+ ml*dauxl[2][iql]+  dpl*wc[2][ic]; 
         for( ia=4;ia<nv;ia++ )
        {
            fl[ia]= dml*ql[ia][iql]+ ml*dauxl[ia][iql];
        }

// fluxes from the right

         rr= auxr[0][iqr];

         dpr=  dauxr[3][iqr]/density;

         unr=  wc[0][ic]*qr[0][iqr]; 
         unr+= wc[1][ic]*qr[1][iqr]; 
         unr+= wc[2][ic]*qr[2][iqr]; 
         unr-= wxdc[0][ic];

         dunr=  wc[0][ic]*dauxr[0][iqr]; 
         dunr+= wc[1][ic]*dauxr[1][iqr]; 
         dunr+= wc[2][ic]*dauxr[2][iqr]; 

         mr= unr*rr;
         dmr= dunr*rr;
         fr[0]= dmr*beta;
         fr[1]= dmr*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
         fr[2]= dmr*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
         fr[3]= dmr*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
         for( ia=4;ia<nv;ia++ )
        {
            fr[ia]= dmr*qr[ia][iqr]+ mr*dauxr[ia][iqr];
        }

//dissipaition, Rusanov approximate Riemann solvers
//         lmax = auxc[nauxf-1][ic];
//         for(ia=0; ia<nv; ia++)
//        {
//            fa[ia] = lmax*(dqr[ia][iqr] - dql[ia][iql]);
//        }
// Roe'e Riemann solver
         for(ia=0; ia<nv; ia++)
        {
            qa[ia] = 0.5*(ql[ia][iql]+qr[ia][iqr]);
        }
         nx = wc[0][ic];
         ny = wc[1][ic];
         nz = wc[2][ic];
         ua = qa[0];
         va = qa[1];
         wa = qa[2];
         una = ua*nx + va*ny + wa*nz;
         aa = sqrt(una*una+beta);
         la1= una-wxdc[0][ic];
         la2= una-0.5*wxdc[0][ic]+ aa;
         la3= una-0.5*wxdc[0][ic]- aa;

         n[0] = nx;
         n[1] = ny;
         n[2] = nz;
         tangent_vectors(n, n0, n1); //n0 x n1 = n

//         cout << wc[0][ic] << " " << wc[1][ic] << " " << wc[2][ic] << " wc diflx\n";
//         cout << nx << " " << ny << " " << nz << " x diflx\n";
//         cout << x1 << " " << y1 << " " << z1 << " x1 diflx\n";
//         cout << x2 << " " << y2 << " " << z2 << " x2 diflx\n";

         x1 = n0[0];
         y1 = n0[1];
         z1 = n0[2];

         x2 = n1[0];
         y2 = n1[1];
         z2 = n1[2];

         reig[0][0] = 0;
         reig[1][0] = x1;
         reig[2][0] = y1;
         reig[3][0] = z1;

         reig[0][1] = 0;
         reig[1][1] = x2;
         reig[2][1] = y2;
         reig[3][1] = z2;

         reig[0][2] = aa;
         reig[1][2] = nx+ua*la2/beta;
         reig[2][2] = ny+va*la2/beta;
         reig[3][2] = nz+wa*la2/beta;

         reig[0][3] = -aa;
         reig[1][3] = nx+ua*la3/beta;
         reig[2][3] = ny+va*la3/beta;
         reig[3][3] = nz+wa*la3/beta;

         fac = 1./(aa*aa+small);
         leig[0][0] =  nx*(wa*y2-va*z2)+ny*(ua*z2-wa*x2)+nz*(va*x2-ua*y2);
         leig[0][1] =  beta*(nz*y2-ny*z2)+la1*(wa*y2-va*z2);
         leig[0][2] =  beta*(nx*z2-nz*x2)+la1*(ua*z2-wa*x2);
         leig[0][3] =  beta*(ny*x2-nx*y2)+la1*(va*x2-ua*y2);
         leig[0][0]*=  fac;
         leig[0][1]*=  fac;
         leig[0][2]*=  fac;
         leig[0][3]*=  fac;
  
         leig[1][0] =  nx*(va*z1-wa*y1)+ny*(wa*x1-ua*z1)+nz*(ua*y1-va*x1);
         leig[1][1] =  beta*(ny*z1-nz*y1)+la1*(va*z1-wa*y1);
         leig[1][2] =  beta*(nz*x1-nx*z1)+la1*(wa*x1-ua*z1);
         leig[1][3] =  beta*(nx*y1-ny*x1)+la1*(ua*y1-va*x1);
         leig[1][0]*=  fac;
         leig[1][1]*=  fac;
         leig[1][2]*=  fac;
         leig[1][3]*=  fac;
  
         leig[2][0] = -0.5*la3;
         leig[2][1] =  0.5*beta*nx;
         leig[2][2] =  0.5*beta*ny;
         leig[2][3] =  0.5*beta*nz;
         leig[2][0]*=  fac;
         leig[2][1]*=  fac;
         leig[2][2]*=  fac;
         leig[2][3]*=  fac;
  
         leig[3][0] = -0.5*la2;
         leig[3][1] =  0.5*beta*nx;
         leig[3][2] =  0.5*beta*ny;
         leig[3][3] =  0.5*beta*nz;
         leig[3][0]*=  fac;
         leig[3][1]*=  fac;
         leig[3][2]*=  fac;
         leig[3][3]*=  fac;


         dq[0] = dauxr[0][iqr] - dauxl[0][iql];
         dq[1] = dauxr[1][iqr] - dauxl[1][iql];
         dq[2] = dauxr[2][iqr] - dauxl[2][iql];
         dq[3] = dauxr[3][iqr] - dauxl[3][iql];
         for(ia=4; ia<nv; ia++)
        {
            dq[ia] = dauxr[ia][iqr] - dauxl[ia][iql];
        }

         for( ia=4;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*fabs(la1);
        }

         dp = dauxr[3][iqr] - dauxl[3][iql]; //dp
         dux= dauxr[0][iqr] - dauxl[0][iql]; //dux
         duy= dauxr[1][iqr] - dauxl[1][iql]; //duy
         duz= dauxr[2][iqr] - dauxl[2][iql]; //duz
         dw[0] = leig[0][0]*dp + leig[0][1]*dux + leig[0][2]*duy + leig[0][3]*duz;
         dw[1] = leig[1][0]*dp + leig[1][1]*dux + leig[1][2]*duy + leig[1][3]*duz;
         dw[2] = leig[2][0]*dp + leig[2][1]*dux + leig[2][2]*duy + leig[2][3]*duz;
         dw[3] = leig[3][0]*dp + leig[3][1]*dux + leig[3][2]*duy + leig[3][3]*duz;

         dw[0] *= fabs(la1);
         dw[1] *= fabs(la1);
         dw[2] *= fabs(la2);
         dw[3] *= fabs(la3);

         fa[0] = reig[0][0]*dw[0] + reig[0][1]*dw[1] + reig[0][2]*dw[2] + reig[0][3]*dw[3];
         fa[1] = reig[1][0]*dw[0] + reig[1][1]*dw[1] + reig[1][2]*dw[2] + reig[1][3]*dw[3];
         fa[2] = reig[2][0]*dw[0] + reig[2][1]*dw[1] + reig[2][2]*dw[2] + reig[2][3]*dw[3];
         fa[3] = reig[3][0]*dw[0] + reig[3][1]*dw[1] + reig[3][2]*dw[2] + reig[3][3]*dw[3];
         //for( ia=4;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]/beta + dw5[ia]; }
         for( ia=4;ia<nv;ia++){ fa[ia]= dw5[ia]; }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0] - fa[0])*wc[3][ic];
         f[1]= 0.5*( fr[1]+ fl[1] - fa[1])*wc[3][ic];
         f[2]= 0.5*( fr[2]+ fl[2] - fa[2])*wc[3][ic];
         f[3]= 0.5*( fr[3]+ fl[3] - fa[3])*wc[3][ic];

         resl[0][iql]-= f[0];
         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];
         resl[3][iql]-= f[3];

         resr[0][iqr]+= f[0];
         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];
         resr[3][iqr]+= f[3];

         for( ia=4;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia] - fa[ia] )*wc[3][ic];
            resl[ia][iql]-= f[ia];
            resr[ia][iqr]+= f[ia];
        }

     }
  }

   void cACMGas::diflxmuscl22( Int ics,Int ice, Int idl, Int *icql, Real *xql[], Real *ql0[], Real *zl0[], Real *dxdxl[], Real **dqdxl[], Real **dzdxl[], Real *resl[],
                                                  Int idr, Int *icqr, Real *xqr[], Real *qr0[], Real *zr0[], Real *dxdxr[], Real **dqdxr[], Real **dzdxr[], Real *resr[],
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd )
  {
      Real            unl,rl,fl[MxNVs];
      Real            unr,rr,fr[MxNVs];

      Real            ml, dpl, dunl, dml;
      Real            mr, dpr, dunr, dmr;

      Int             ia,ic,iql,iqr;

      Real            f[MxNVs];

      Real            wn[2], xn[2];
      Real            ql[MxNVs],qr[MxNVs];
      Real            zl[MxNVs],zr[MxNVs];
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            dzl[MxNVs],dzr[MxNVs];
      Real            dzl0[MxNVs],dzr0[MxNVs];
      Real            fa[MxNVs], leig[3][3], reig[3][3], reig_fac;
      Real            nx, ny, una, ua, va, qa[MxNVs], aa, la1, la2, la3, dw[3], dq[MxNVs], dp, du, dv, dw5[MxNVs];


      for( ic=ics;ic<ice;ic++ )
     {
         wn[0]= wc[0][ic];
         wn[1]= wc[1][ic];

         xn[0]= xc[0][ic];
         xn[1]= xc[1][ic];

         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

         if(grd->getlimtype()==1)
        {
            grd->deltq( iql,idl,xql,zl0,dxdxl,dzdxl, iqr,idr,xqr,zr0,dxdxr,dzdxr, xn,wn, dzl0,dzr0, dzl,dzr );
            for( ia=0;ia<nv;ia++ )
           {
               ql[ia]= ql0[ia][ic];
               qr[ia]= qr0[ia][ic];
               zl[ia]= zl0[ia][iql]+ dzl[ia];
               zr[ia]= zr0[ia][iqr]+ dzr[ia];
           }
        }
         else
        {
            grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );
            //grd->deltq_exact( iql,idl,xql,zl0,dxdxl,dzdxl, iqr,idr,xqr,zr0,dxdxr,dzdxr, xn,wn, dzl0,dzr0, dzl,dzr );
            grd->deltq( iql,idl,xql,zl0,dxdxl,dzdxl, iqr,idr,xqr,zr0,dxdxr,dzdxr, xn,wn, dzl0,dzr0, dzl,dzr );

            for( ia=0;ia<nv;ia++ )
           {
               ql[ia]= ql0[ia][iql]+ dql[ia];
               qr[ia]= qr0[ia][iqr]+ dqr[ia];
               zl[ia]= zl0[ia][iql]+ dzl[ia];
               zr[ia]= zr0[ia][iqr]+ dzr[ia];
           }
        }

// fluxes from the left

         rl= 1.;

         dpl=    zl[2]/density;

         unl=  wc[0][ic]*ql[0];
         unl+= wc[1][ic]*ql[1];
         unl-= wxdc[0][ic];

         dunl=  wc[0][ic]*zl[0];
         dunl+= wc[1][ic]*zl[1];

         ml= unl*rl;
         dml= dunl*rl;
         fl[0]= dml*beta;
         fl[1]= dml*ql[0]+ ml*zl[0]+  dpl*wc[0][ic]; 
         fl[2]= dml*ql[1]+ ml*zl[1]+  dpl*wc[1][ic]; 
         for( ia=3;ia<nv;ia++ )
        {
            fl[ia]= dml*ql[ia]+ ml*zl[ia];
        }

// fluxes from the right

         rr= 1;

         dpr=    zr[2]/density;

         unr=  wc[0][ic]*qr[0];
         unr+= wc[1][ic]*qr[1];
         unr-= wxdc[0][ic];

         dunr=  wc[0][ic]*zr[0];
         dunr+= wc[1][ic]*zr[1];

         mr= unr*rr;
         dmr= dunr*rr;
         fr[0]= dmr*beta;
         fr[1]= dmr*qr[0] + mr*zr[0] + dpr*wc[0][ic]; 
         fr[2]= dmr*qr[1] + mr*zr[1] + dpr*wc[1][ic]; 
         for( ia=3;ia<nv;ia++ )
        {
            fr[ia]= dmr*qr[ia] + mr*zr[ia];
        }

////dissipaition, Rusanov approximate Riemann solvers
//         lmax = auxc[nauxf-1][ic];
//         fa[0] = lmax*(dpr - dpl);
//         fa[1] = lmax*(zr[0] - zl[0]);
//         fa[2] = lmax*(zr[1] - zl[1]);
//         for(ia=3; ia<nv; ia++)
//        {
//            fa[ia] = lmax*(zr[ia] - zl[ia]);
//        }
// Roe'e Riemann solver
         for(ia=0; ia<nv; ia++)
        {
            qa[ia] = 0.5*(ql[ia]+qr[ia]);
        }
         nx = wc[0][ic];
         ny = wc[1][ic];
         ua = qa[0];
         va = qa[1];
         una = ua*nx + va*ny;
         aa = sqrt(una*una+beta);
         la1= una-wxdc[0][ic];
         la2= una-0.5*wxdc[0][ic]+ aa;
         la3= una-0.5*wxdc[0][ic]- aa;
         la1 = fabs(la1);
         la2 = fabs(la2);
         la3 = fabs(la3);

         leig[0][0] = ny*ua-nx*va;
         leig[1][0] = aa-una;
         leig[2][0] = -aa-una;
         leig[0][1] = -una*va-beta*ny;
         leig[1][1] = beta*nx;
         leig[2][1] = beta*nx;
         leig[0][2] = una*ua+beta*nx;
         leig[1][2] = beta*ny;
         leig[2][2] = beta*ny;

         reig_fac = 1./(2*beta*aa*aa);
         reig[0][0] = reig_fac*0;
         reig[1][0] = reig_fac*(-2.*beta*ny);
         reig[2][0] = reig_fac*(2.*beta*nx);
         reig[0][1] = reig_fac*(aa*beta);
         reig[1][1] = reig_fac*(ua*(aa+una) + beta*nx);
         reig[2][1] = reig_fac*(va*(aa+una) + beta*ny);
         reig[0][2] = reig_fac*(-aa*beta);
         reig[1][2] = reig_fac*(-ua*(aa-una) + beta*nx);
         reig[2][2] = reig_fac*(-va*(aa-una) + beta*ny);

         dq[0] = zr[0] - zl[0];
         dq[1] = zr[1] - zl[1];
         dq[2] = zr[2] - zl[2];
         for(ia=3; ia<nv; ia++)
        {
            dq[ia] = zr[ia] - zl[ia];
        }

         for( ia=4;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*la1;
        }

         dp = zr[2] - zl[2]; //dp
         du = zr[0] - zl[0]; //du
         dv = zr[1] - zl[1]; //dv
         dw[0] = leig[0][0]*dp + leig[0][1]*du + leig[0][2]*dv;
         dw[1] = leig[1][0]*dp + leig[1][1]*du + leig[1][2]*dv;
         dw[2] = leig[2][0]*dp + leig[2][1]*du + leig[2][2]*dv;

         dw[0] *= la1;
         dw[1] *= la2;
         dw[2] *= la3;

         fa[0] = reig[0][0]*dw[0] + reig[0][1]*dw[1] + reig[0][2]*dw[2];
         fa[1] = reig[1][0]*dw[0] + reig[1][1]*dw[1] + reig[1][2]*dw[2];
         fa[2] = reig[2][0]*dw[0] + reig[2][1]*dw[1] + reig[2][2]*dw[2];
         //for( ia=3;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]/beta + dw5[ia]; }
         for( ia=3;ia<nv;ia++){ fa[ia]= dw5[ia]; }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0] -fa[0] )*wc[2][ic];
         f[1]= 0.5*( fr[1]+ fl[1] -fa[1] )*wc[2][ic];
         f[2]= 0.5*( fr[2]+ fl[2] -fa[2] )*wc[2][ic];

         resl[0][iql]-= f[0];
         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];

         resr[0][iqr]+= f[0];
         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];

         for( ia=3;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia] - fa[ia] )*wc[2][ic];
            resl[ia][iql]-= f[ia];
            resr[ia][iqr]+= f[ia];
        }
     }
  }

   void cACMGas::diflxmuscl33( Int ics,Int ice, Int idl, Int *icql, Real *xql[], Real *ql0[], Real *zl0[], Real *dxdxl[], Real **dqdxl[], Real **dzdxl[], Real *resl[],
                                                  Int idr, Int *icqr, Real *xqr[], Real *qr0[], Real *zr0[], Real *dxdxr[], Real **dqdxr[], Real **dzdxr[], Real *resr[],
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd )
  {
      Real            unl,rl,fl[MxNVs];
      Real            unr,rr,fr[MxNVs];

      Real            ml, dpl, dunl, dml;
      Real            mr, dpr, dunr, dmr;

      Int             ia,ic,iql,iqr;

      Real            f[MxNVs], fa[MxNVs], lmax;

      Real            xn[3], wn[3];

      Real            ql[MxNVs],qr[MxNVs];
      Real            zl[MxNVs],zr[MxNVs];
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dzl[MxNVs],dzr[MxNVs];

      Real            leig[4][4], reig[4][4], fac;
      Real            nx,ny,nz,una,ua,va,wa,qa[MxNVs],aa,la1,la2,la3,dw[4],dq[MxNVs],dp,dux,duy,duz,dw5[MxNVs];
      Real            n[3],n0[3],n1[3],x1,y1,z1,x2,y2,z2;

      for( ic=ics;ic<ice;ic++ )
     {
         wn[0]= wc[0][ic];
         wn[1]= wc[1][ic];
         wn[2]= wc[2][ic];

         xn[0]= xc[0][ic];
         xn[1]= xc[1][ic];
         xn[2]= xc[2][ic];

         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

         if(grd->getlimtype()==1)
        {
            grd->deltq( iql,idl,xql,zl0,dxdxl,dzdxl, iqr,idr,xqr,zr0,dxdxr,dzdxr, xn,wn, dzl,dzr, dzl,dzr );
   
            for( ia=0;ia<nv;ia++ )
           {
               ql[ia]= ql0[ia][ic];
               qr[ia]= qr0[ia][ic];
               zl[ia]= zl0[ia][iql]+ dzl[ia];
               zr[ia]= zr0[ia][iqr]+ dzr[ia];
           }
        }
         else
        {
            grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql,dqr, dql,dqr );
            grd->deltq( iql,idl,xql,zl0,dxdxl,dzdxl, iqr,idr,xqr,zr0,dxdxr,dzdxr, xn,wn, dzl,dzr, dzl,dzr );
   
            for( ia=0;ia<nv;ia++ )
           {
               ql[ia]= ql0[ia][iql]+ dql[ia];
               qr[ia]= qr0[ia][iqr]+ dqr[ia];
               zl[ia]= zl0[ia][iql]+ dzl[ia];
               zr[ia]= zr0[ia][iqr]+ dzr[ia];
           }
        }

// fluxes from the left

         rl= 1.;

         dpl=    zl[3]/density;

         unl=  wc[0][ic]*ql[0]; 
         unl+= wc[1][ic]*ql[1]; 
         unl+= wc[2][ic]*ql[2]; 
         unl-= wxdc[0][ic];

         dunl=  wc[0][ic]*zl[0]; 
         dunl+= wc[1][ic]*zl[1]; 
         dunl+= wc[2][ic]*zl[2]; 

         ml= unl*rl;
         dml= dunl*rl;
         fl[0]= dml*beta;
         fl[1]= dml*ql[0] + ml*zl[0] +  dpl*wc[0][ic]; 
         fl[2]= dml*ql[1] + ml*zl[1] +  dpl*wc[1][ic]; 
         fl[3]= dml*ql[2] + ml*zl[2] +  dpl*wc[2][ic]; 
         for( ia=4;ia<nv;ia++ )
        {
            fl[ia]= dml*ql[ia]+ ml*zl[ia];
        }

// fluxes from the right

         rr= 1.;
         dpr=    zr[3]/density;

         unr=  wc[0][ic]*qr[0]; 
         unr+= wc[1][ic]*qr[1]; 
         unr+= wc[2][ic]*qr[2]; 
         unr-= wxdc[0][ic];

         dunr=  wc[0][ic]*zr[0]; 
         dunr+= wc[1][ic]*zr[1]; 
         dunr+= wc[2][ic]*zr[2]; 

         mr= unr*rr;
         dmr= dunr*rr;
         fr[0]= dmr*beta;
         fr[1]= dmr*qr[0] + mr*zr[0] + dpr*wc[0][ic]; 
         fr[2]= dmr*qr[1] + mr*zr[1] + dpr*wc[1][ic]; 
         fr[3]= dmr*qr[2] + mr*zr[2] + dpr*wc[2][ic]; 
         for( ia=4;ia<nv;ia++ )
        {
            fr[ia]= dmr*qr[ia] + mr*zr[ia];
        }

//dissipaition, Rusanov approximate Riemann solvers
//         lmax = auxc[nauxf-1][ic];
//         fa[0] = lmax*(dpr - dpl);
//         fa[1] = lmax*(zr[0] - zl[0]);
//         fa[2] = lmax*(zr[1] - zl[1]);
//         fa[3] = lmax*(zr[2] - zl[2]);
//         for(ia=4; ia<nv; ia++)
//        {
//            fa[ia] = lmax*(zr[ia] - zl[ia]);
//        }
// Roe'e Riemann solver
         for(ia=0; ia<nv; ia++)
        {
            qa[ia] = 0.5*(ql[ia]+qr[ia]);
        }
         nx = wc[0][ic];
         ny = wc[1][ic];
         nz = wc[2][ic];
         ua = qa[0];
         va = qa[1];
         wa = qa[2];
         una = ua*nx + va*ny + wa*nz;
         aa = sqrt(una*una+beta);
         la1= una-wxdc[0][ic];
         la2= una-0.5*wxdc[0][ic]+ aa;
         la3= una-0.5*wxdc[0][ic]- aa;

         n[0] = nx;
         n[1] = ny;
         n[2] = nz;
         tangent_vectors(n, n0, n1); //n0 x n1 = n

//         cout << wc[0][ic] << " " << wc[1][ic] << " " << wc[2][ic] << " wc diflx\n";
//         cout << nx << " " << ny << " " << nz << " x diflx\n";
//         cout << x1 << " " << y1 << " " << z1 << " x1 diflx\n";
//         cout << x2 << " " << y2 << " " << z2 << " x2 diflx\n";

         x1 = n0[0];
         y1 = n0[1];
         z1 = n0[2];

         x2 = n1[0];
         y2 = n1[1];
         z2 = n1[2];

         reig[0][0] = 0;
         reig[1][0] = x1;
         reig[2][0] = y1;
         reig[3][0] = z1;

         reig[0][1] = 0;
         reig[1][1] = x2;
         reig[2][1] = y2;
         reig[3][1] = z2;

         reig[0][2] = aa;
         reig[1][2] = nx+ua*la2/beta;
         reig[2][2] = ny+va*la2/beta;
         reig[3][2] = nz+wa*la2/beta;

         reig[0][3] = -aa;
         reig[1][3] = nx+ua*la3/beta;
         reig[2][3] = ny+va*la3/beta;
         reig[3][3] = nz+wa*la3/beta;

         fac = 1./(aa*aa+small);
         leig[0][0] =  nx*(wa*y2-va*z2)+ny*(ua*z2-wa*x2)+nz*(va*x2-ua*y2);
         leig[0][1] =  beta*(nz*y2-ny*z2)+la1*(wa*y2-va*z2);
         leig[0][2] =  beta*(nx*z2-nz*x2)+la1*(ua*z2-wa*x2);
         leig[0][3] =  beta*(ny*x2-nx*y2)+la1*(va*x2-ua*y2);
         leig[0][0]*=  fac;
         leig[0][1]*=  fac;
         leig[0][2]*=  fac;
         leig[0][3]*=  fac;
  
         leig[1][0] =  nx*(va*z1-wa*y1)+ny*(wa*x1-ua*z1)+nz*(ua*y1-va*x1);
         leig[1][1] =  beta*(ny*z1-nz*y1)+la1*(va*z1-wa*y1);
         leig[1][2] =  beta*(nz*x1-nx*z1)+la1*(wa*x1-ua*z1);
         leig[1][3] =  beta*(nx*y1-ny*x1)+la1*(ua*y1-va*x1);
         leig[1][0]*=  fac;
         leig[1][1]*=  fac;
         leig[1][2]*=  fac;
         leig[1][3]*=  fac;
  
         leig[2][0] = -0.5*la3;
         leig[2][1] =  0.5*beta*nx;
         leig[2][2] =  0.5*beta*ny;
         leig[2][3] =  0.5*beta*nz;
         leig[2][0]*=  fac;
         leig[2][1]*=  fac;
         leig[2][2]*=  fac;
         leig[2][3]*=  fac;
  
         leig[3][0] = -0.5*la2;
         leig[3][1] =  0.5*beta*nx;
         leig[3][2] =  0.5*beta*ny;
         leig[3][3] =  0.5*beta*nz;
         leig[3][0]*=  fac;
         leig[3][1]*=  fac;
         leig[3][2]*=  fac;
         leig[3][3]*=  fac;


         dq[0] = zr[0] - zl[0];
         dq[1] = zr[1] - zl[1];
         dq[2] = zr[2] - zl[2];
         dq[3] = zr[3] - zl[3];
         for(ia=4; ia<nv; ia++)
        {
            dq[ia] = zr[ia] - zl[ia];
        }

         for( ia=4;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*fabs(la1);
        }

         dp = zr[3] - zl[3]; //dp
         dux= zr[0] - zl[0]; //dux
         duy= zr[1] - zl[1]; //duy
         duz= zr[2] - zl[2]; //duz
         dw[0] = leig[0][0]*dp + leig[0][1]*dux + leig[0][2]*duy + leig[0][3]*duz;
         dw[1] = leig[1][0]*dp + leig[1][1]*dux + leig[1][2]*duy + leig[1][3]*duz;
         dw[2] = leig[2][0]*dp + leig[2][1]*dux + leig[2][2]*duy + leig[2][3]*duz;
         dw[3] = leig[3][0]*dp + leig[3][1]*dux + leig[3][2]*duy + leig[3][3]*duz;

         dw[0] *= fabs(la1);
         dw[1] *= fabs(la1);
         dw[2] *= fabs(la2);
         dw[3] *= fabs(la3);

         fa[0] = reig[0][0]*dw[0] + reig[0][1]*dw[1] + reig[0][2]*dw[2] + reig[0][3]*dw[3];
         fa[1] = reig[1][0]*dw[0] + reig[1][1]*dw[1] + reig[1][2]*dw[2] + reig[1][3]*dw[3];
         fa[2] = reig[2][0]*dw[0] + reig[2][1]*dw[1] + reig[2][2]*dw[2] + reig[2][3]*dw[3];
         fa[3] = reig[3][0]*dw[0] + reig[3][1]*dw[1] + reig[3][2]*dw[2] + reig[3][3]*dw[3];
         //for( ia=4;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]/beta + dw5[ia]; }
         for( ia=4;ia<nv;ia++){ fa[ia]= dw5[ia]; }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0] -fa[0])*wc[3][ic];
         f[1]= 0.5*( fr[1]+ fl[1] -fa[1])*wc[3][ic];
         f[2]= 0.5*( fr[2]+ fl[2] -fa[2])*wc[3][ic];
         f[3]= 0.5*( fr[3]+ fl[3] -fa[3])*wc[3][ic];

         resl[0][iql]-= f[0];
         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];
         resl[3][iql]-= f[3];

         resr[0][iqr]+= f[0];
         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];
         resr[3][iqr]+= f[3];

         for( ia=4;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia] -fa[ia] )*wc[3][ic];
            resl[ia][iql]-= f[ia];
            resr[ia][iqr]+= f[ia];
        }

     }
  }

   void cACMGas::mflx22( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                       Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl;
      Real            dqnr[MxNVs],dqtr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            mu;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            for( iv=0;iv<nv0;iv++ )
           {
               dqnl[iv]=  dqdxl[iv][0][iql]*wc[0][ic];
               dqnl[iv]+= dqdxl[iv][1][iql]*wc[1][ic];

               dqnr[iv]=  dqdxr[iv][0][iqr]*wc[0][ic];
               dqnr[iv]+= dqdxr[iv][1][iqr]*wc[1][ic];

               dqn[iv]= ( qr[iv][iqr]-ql[iv][iql] )/w;
               q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
           }

// tangential gradients

            for( iv=0;iv<nv0;iv++ )
           {
               dqtl= dqdxl[iv][0][iql]- wc[0][ic]*dqnl[iv];
               dqtr= dqdxr[iv][0][iqr]- wc[0][ic]*dqnr[iv];
               dqt[iv][0]= wl*dqtl+ wr*dqtr;

               dqtl= dqdxl[iv][1][iql]- wc[1][ic]*dqnl[iv];
               dqtr= dqdxr[iv][1][iqr]- wc[1][ic]*dqnr[iv];
               dqt[iv][1]= wl*dqtl+ wr*dqtr;

           }

// reconstruct gradient
            for( iv=0;iv<nv0;iv++ )
           {
               dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
               dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
           }

// stress tensor
            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 

// viscous fluxes
            //taun[0]= -mu*dqn[2];
            taun[0]= 0;

            taun[1]=  tau[0][0]*wc[0][ic];
            taun[1]+= tau[0][1]*wc[1][ic];

            taun[2]=  tau[1][0]*wc[0][ic];
            taun[2]+= tau[1][1]*wc[1][ic];

// accumulate
            for( iv=0;iv<nv0;iv++ )
           {
               rhsr[iv][iqr]+= taun[iv]*wc[2][ic];
               rhsl[iv][iql]-= taun[iv]*wc[2][ic];
           }
            auxc[nauxf-1][ic]+= wc[2][ic]*mu/(w);
        }
     }
  }

   void cACMGas::mflx23( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                       Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      assert(0);
  }

   void cACMGas::mflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
                                            Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl;
      Real            dqnr[MxNVs],dqtr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            mu;

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients

            for( iv=0;iv<nv0;iv++ )
           {
               dqnl[iv]=  dqdxl[iv][0][iql]*wc[0][ic];
               dqnl[iv]+= dqdxl[iv][1][iql]*wc[1][ic];
               dqnl[iv]+= dqdxl[iv][2][iql]*wc[2][ic];

               dqnr[iv]=  dqdxr[iv][0][iqr]*wc[0][ic];
               dqnr[iv]+= dqdxr[iv][1][iqr]*wc[1][ic];
               dqnr[iv]+= dqdxr[iv][2][iqr]*wc[2][ic];

               dqn[iv]= ( qr[iv][iqr]-ql[iv][iql] )/w;
               q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
           }

// tangential gradients

            for( iv=0;iv<nv0;iv++ )
           {
               dqtl= dqdxl[iv][0][iql]- wc[0][ic]*dqnl[iv];
               dqtr= dqdxr[iv][0][iqr]- wc[0][ic]*dqnr[iv];
               dqt[iv][0]= wl*dqtl+ wr*dqtr;

               dqtl= dqdxl[iv][1][iql]- wc[1][ic]*dqnl[iv];
               dqtr= dqdxr[iv][1][iqr]- wc[1][ic]*dqnr[iv];
               dqt[iv][1]= wl*dqtl+ wr*dqtr;

               dqtl= dqdxl[iv][2][iql]- wc[2][ic]*dqnl[iv];
               dqtr= dqdxr[iv][2][iqr]- wc[2][ic]*dqnr[iv];
               dqt[iv][2]= wl*dqtl+ wr*dqtr;
           }

// reconstruct gradient
            for( iv=0;iv<nv0;iv++ )
           {
               dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
               dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
               dqdx[iv][2]= dqn[iv]*wc[2][ic]+ dqt[iv][2];
           }

// stress tensor
            mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 
            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 
            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

// viscous fluxes

            taun[1]=  tau[0][0]*wc[0][ic];
            taun[1]+= tau[0][1]*wc[1][ic];
            taun[1]+= tau[0][2]*wc[2][ic];

            taun[2]=  tau[1][0]*wc[0][ic];
            taun[2]+= tau[1][1]*wc[1][ic];
            taun[2]+= tau[1][2]*wc[2][ic];

            taun[3]=  tau[2][0]*wc[0][ic];
            taun[3]+= tau[2][1]*wc[1][ic];
            taun[3]+= tau[2][2]*wc[2][ic];

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               rhsr[iv][iqr]+= taun[iv]*wc[3][ic];
               rhsl[iv][iql]-= taun[iv]*wc[3][ic];
           }
            auxc[nauxf-1][ic]+= wc[3][ic]*mu/(w);
            
        }
     }
  }

   void cACMGas::dmflx22( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                        Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,iql,iqr;
      Real            wl,wr,w;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            mu;

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv0-1;iv++ )
           {
               ddqn[iv]= ( dauxr[iv][iqr]-dauxl[iv][iql] )/w;
               dqn[iv]=  ( qr[iv][iqr]-ql[iv][iql] )/w;

               q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
               dq[iv]= wl*dauxl[iv][iql]+ wr*dauxr[iv][iqr];

           }

            for( iv=0;iv<nv0-1;iv++ )
           {
               dqdx[iv][0]= dqn[iv]*wc[0][ic];
               dqdx[iv][1]= dqn[iv]*wc[1][ic];

               ddqdx[iv][0]= ddqn[iv]*wc[0][ic];
               ddqdx[iv][1]= ddqn[iv]*wc[1][ic];
           }

// stress tensor

            mu= wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 

            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 

            dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
            dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 

            dtau[0][1]=  dtau[1][0];
            dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 

// viscous flux

            for( iv=0;iv<nv0;iv++ )
           {
               taun[iv]=0;
               dtaun[iv]=0;
           }

            taun[1]=    tau[0][0]*wc[0][ic];
            taun[1]+=   tau[0][1]*wc[1][ic];

            taun[2]=    tau[1][0]*wc[0][ic];
            taun[2]+=   tau[1][1]*wc[1][ic];

            //dtaun[0]=   -mu*ddqn[2];
            dtaun[0]=   0;

            dtaun[1]=   dtau[0][0]*wc[0][ic];
            dtaun[1]+=  dtau[0][1]*wc[1][ic];

            dtaun[2]=   dtau[1][0]*wc[0][ic];
            dtaun[2]+=  dtau[1][1]*wc[1][ic];

            dtaun[3]=   0;
// accumulate
            for( iv=0;iv<nv0;iv++ )
           {
               resr[iv][iqr]+= dtaun[iv]*wc[2][ic];
               resl[iv][iql]-= dtaun[iv]*wc[2][ic];
           }
        }
     }
  }

   void cACMGas::dmflx23( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                        Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      assert(0);
  }

   void cACMGas::dmflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                             Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
                                                        Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      Int             ic,iv,iql,iqr;
      Real            wl,wr,w;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            mu;

      if( ice > ics )
     { 
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

// distance of DOF positions from face centre

            wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );

            wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv0;iv++ )
           {
               ddqn[iv]= ( dauxr[iv][iqr]- dauxl[iv][iql] )/w;
               dqn[iv]=  ( qr[iv][iqr]-       ql[iv][iql] )/w;

               q[iv]=  wl*   ql[iv][iql]+ wr*   qr[iv][iqr];
               dq[iv]= wl*dauxl[iv][iql]+ wr*dauxr[iv][iqr];

           }

            for( iv=0;iv<nv0;iv++ )
           {
               dqdx[iv][0]= dqn[iv]*wc[0][ic];
               dqdx[iv][1]= dqn[iv]*wc[1][ic];
               dqdx[iv][2]= dqn[iv]*wc[2][ic];

               ddqdx[iv][0]= ddqn[iv]*wc[0][ic];
               ddqdx[iv][1]= ddqn[iv]*wc[1][ic];
               ddqdx[iv][2]= ddqn[iv]*wc[2][ic];
           }

// stress tensor

            mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 

            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 

            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
            dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 
            dtau[2][0]= -mu*( ddqdx[0][2]+ ddqdx[2][0] ); 

            dtau[0][1]=  dtau[1][0];
            dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 
            dtau[2][1]= -mu*( ddqdx[1][2]+ ddqdx[2][1] ); 

            dtau[0][2]=  dtau[2][0];
            dtau[1][2]=  dtau[2][1];
            dtau[2][2]= -mu*( ddqdx[2][2]+ ddqdx[2][2] ); 

// viscous flux

            taun[1]=    tau[0][0]*wc[0][ic];
            taun[1]+=   tau[0][1]*wc[1][ic];
            taun[1]+=   tau[0][2]*wc[2][ic];

            taun[2]=    tau[1][0]*wc[0][ic];
            taun[2]+=   tau[1][1]*wc[1][ic];
            taun[2]+=   tau[1][2]*wc[2][ic];

            taun[3]=    tau[2][0]*wc[0][ic];
            taun[3]+=   tau[2][1]*wc[1][ic];
            taun[3]+=   tau[2][2]*wc[2][ic];

            dtaun[1]=   dtau[0][0]*wc[0][ic];
            dtaun[1]+=  dtau[0][1]*wc[1][ic];
            dtaun[1]+=  dtau[0][2]*wc[2][ic];

            dtaun[2]=   dtau[1][0]*wc[0][ic];
            dtaun[2]+=  dtau[1][1]*wc[1][ic];
            dtaun[2]+=  dtau[1][2]*wc[2][ic];

            dtaun[3]=   dtau[2][0]*wc[0][ic];
            dtaun[3]+=  dtau[2][1]*wc[1][ic];
            dtaun[3]+=  dtau[2][2]*wc[2][ic];

            dtaun[4]=   0;

// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               resr[iv][iqr]+= dtaun[iv]*wc[3][ic];
               resl[iv][iql]-= dtaun[iv]*wc[3][ic];
           }
        }
     }
  }

   void cACMGas::nondim( Int iqs, Int iqe, Real *q[], Int *idone )
  {
      Int ia,iq;

      if( iqe > iqs )
     {
         for( ia=0;ia<nvel;ia++ )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[ia][iq]/= unit[0];
           }
        }
         ia= nvel;
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[ia][iq]/= unit[2];
        }

         ia= nvel+1;
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[ia][iq] = 0;
        }
         vsc->nondim( iqs,iqe, q, idone);
     }     
  }

   void cACMGas::redim( Int iqs, Int iqe, Real *q[] )
  {
      Int ia,iq;

      if( iqe > iqs )
     {
         for( ia=0;ia<nvel;ia++ )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[ia][iq]*= unit[0];
           }
        }
         ia= nvel;
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[ia][iq]*= unit[2];
        }
         ia= nvel+1;
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[ia][iq]*= 0;
        }
         vsc->redim( iqs,iqe, q );
     }
  }

   void cACMGas::diflxb22( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      diflx22( ics, ice, icql, ql, auxl, dql, dauxl, resl,
                         icqr, qr, auxr, dqr, dauxr, resr,
                         wc, wxdc, auxc );
  }

   void cACMGas::diflxb33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      diflx33( ics, ice, icql, ql, auxl, dql, dauxl, resl,
                         icqr, qr, auxr, dqr, dauxr, resr,
                         wc, wxdc, auxc );
  }


   void cACMGas::cnsv2( Int iqs, Int iqe, Real *q[], Real *aux[], Real *qo[] )
  {

      Int iq,ia;

      for( iq=iqs;iq<iqe;iq++ )
     {
         qo[0][iq]= q[2][iq];
         qo[1][iq]= q[0][iq];
         qo[2][iq]= q[1][iq];
         qo[3][iq]= 0;
         for( ia=4;ia<nv;ia++ )
        {   
            qo[ia][iq]= q[ia][iq];
        }
     }
  }

   void cACMGas::cnsv3( Int iqs, Int iqe, Real *q[], Real *aux[], Real *qo[] )
  {
      Int iq,ia;

      for( iq=iqs;iq<iqe;iq++ )
     {
         qo[0][iq]= q[3][iq];
         qo[1][iq]= q[0][iq];
         qo[2][iq]= q[1][iq];
         qo[3][iq]= q[2][iq];
         qo[4][iq]= 0;
         for( ia=5;ia<nv;ia++ )
        {   
            qo[ia][iq]= q[ia][iq];
        }
     }
  }

