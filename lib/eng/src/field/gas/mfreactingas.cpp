using namespace std;

# include <field/gas.h>

   void cMfReactingGas::gtcomp( Real z, Real *ssp0 )
  {
      Real w;
      Real zp,z0,z1;
      Int  ir,ir0,ir1,isp;

      w=0;
      ir1= 0;
      ir0= 0;

      zp= z;
      zp= fmax( zp,zrc[0] );
      zp= fmin( zp,zrc[nrc-1] );

      z1= zrc[0];
      for( ir=1;ir<nrc;ir++ )
     {
         z0= z1;
         z1= zrc[ir];
         if( zp <= z1 )
        {
            ir0= ir-1;
            ir1= ir;
            w= ( zp-z0 )/( z1-z0 );
            break;
        }
     }
      for( isp=0;isp<nsp;isp++ )
     {
         ssp0[isp]= w*ssp[ir1][isp]+ (1.-w)*ssp[ir0][isp];
     }
  }

   cMfReactingGas::cMfReactingGas( cCosystem *Coo, cVisc *visc )
  {
      Int il,iw,ir,isp;
      Real w;
      cTxtfle *fle;
      string       seps=" ,;:|",comms="c#!";


      coo= Coo;
      vsc= visc;

      nvel=coo->getnvel(); 
      nx=coo->getnx(); 

      nvk=4;

      ilv[0]=nvel;
      ilv[1]=ilv[0]+1;
      ilv[2]=ilv[1]+1;
      ilv[3]=ilv[2]+1;
      unit[0]= 100.;
      unit[1]=   1.;
      unit[2]= unit[0]*unit[0];
      unit[4]=   1.;
      deflt[0]=   0.;
      deflt[1]= 298.;
      deflt[2]= 100000./unit[2];
      deflt[3]=   0.;
      rg= -1;
      gam= -1;
      eps= 0.05;

      string tmp= share;
      string jname=tmp+"/JANAF/";
      cJanaf::read( jname );

      nv=2+nvel+1;
      naux=8;
      nauxf=9;
      nlhs= 2;

      nv0= nv;
      naux0= naux;
      nauxf0= nauxf;
      nlhs0= nlhs;

      vsc->setvrs( nx,nvel, &nv,&naux,&nauxf,&nlhs );
      coo->setnv(nv);

// gas composition & molecular weights
      string fname= jname+"/compositions.dat";
      fle= new cTxtfle( fname,comms,seps );
//  *(fle->stream(0,0)) >> nrc;
      conv( (fle->text(0,0)),&nrc );
      il= 0;
      for( ir=0;ir<nrc;ir++ )
     {
        il++;
//     *(fle->stream(il,0)) >> zrc[ir];
        conv( (fle->text(il,0)) ,zrc+ir);
        iw=0;
        for( isp=0;isp<nsp;isp++ )
       {
           iw++; 
//       *(fle->stream(il,iw))>>xsp[ir][isp];
          conv( (fle->text(il,iw)),( xsp[ir])+isp);
       }
     }
      delete fle;

      for( ir=0;ir<nrc;ir++ )
     {
         w= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            w+= xsp[ir][isp]*wsp[isp];
        }
   
         for( isp=0;isp<nsp;isp++ )
        {
            ssp[ir][isp]= xsp[ir][isp]/w;
        }
     }
  }

   void cMfReactingGas::props( )
  {
  }

   void cMfReactingGas::auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch )
  {

      Int   isp,ir,iv,iq,ir0,ir1;
      Real  h0,cv,rg,rg1,gam;
      Real  a0,a1,a2,a3,a4,a5,t,p,z0,z1,w,z,zp;
      Real  ssp0[MxNsp];
      Real  cvsp[MxNsp];
      Real  h0sp[MxNsp];
      Real  mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);

      if( iqe > iqs )
     {
         Int nq;
         Real *q, *aux;

         nq = q_view.get_dim1();

         q   = q_view.get_data();
         aux = aux_view.get_data();

         rg1= runi/unit[2];
      
         for( iq=iqs;iq<iqe;iq++ )
        {

            //t= q[3][iq];
            //p= q[4][iq];
            //z= q[5][iq];
            t= q[ADDR(3,iq,nq)];
            p= q[ADDR(4,iq,nq)];
            z= q[ADDR(5,iq,nq)];
 
            gtcomp( z,ssp0 );

// molecular weight, constant volume specific heat and standard formation internal energy
            rg= 0.;
            h0= 0.;
            cv= 0.;
            rg=  0.;
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( t > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvsp[isp]= ( a0+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );
               cvsp[isp]-= 1.;
               cvsp[isp]*= rg1;
               cv+= cvsp[isp]*ssp0[isp];

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               h0sp[isp]= a5+ t*( a0+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );
               h0+= h0sp[isp]*ssp0[isp];

               h0sp[isp]-= t;
               h0sp[isp]*= rg1;

               rg+= ssp0[isp]; 
           }
            rg*= rg1;
            h0*= rg1;
            gam= (cv+rg)/cv;
// density
            //aux[0][iq]= p/( rg*t );
            aux[ADDR(0,iq,nq)]= p/( rg*t );

// kinetic energy
            //aux[1][iq]=  q[0][iq]*q[0][iq];
            //aux[1][iq]+= q[1][iq]*q[1][iq];
            //aux[1][iq]+= q[2][iq]*q[2][iq];
            //aux[1][iq]*= 0.5;
            aux[ADDR(1,iq,nq)]=  q[ADDR(0,iq,nq)]*q[ADDR(0,iq,nq)];
            aux[ADDR(1,iq,nq)]+= q[ADDR(1,iq,nq)]*q[ADDR(1,iq,nq)];
            aux[ADDR(1,iq,nq)]+= q[ADDR(2,iq,nq)]*q[ADDR(2,iq,nq)];
            aux[ADDR(1,iq,nq)]*= 0.5;

// speed of sound and total entalpy
            //aux[2][iq]= gam*rg* t;
            //aux[2][iq]= sqrt( aux[2][iq] );
            aux[ADDR(2,iq,nq)]= gam*rg* t;
            aux[ADDR(2,iq,nq)]= sqrt( aux[ADDR(2,iq,nq)] );

            //aux[3][iq]= h0+ aux[1][iq];
            aux[ADDR(3,iq,nq)]= h0+ aux[ADDR(1,iq,nq)];

            //aux[4][iq]= rg;
            //aux[5][iq]= cv+rg;
            //aux[6][iq]= mu;
            //aux[7][iq]= kappa;
            aux[ADDR(4,iq,nq)]= rg;
            aux[ADDR(5,iq,nq)]= cv+rg;
            aux[ADDR(6,iq,nq)]= mu;
            aux[ADDR(7,iq,nq)]= kappa;

        }
     } 
  }

   void cMfReactingGas::dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view )
  {
      Int iq,ia,isp,ir0,ir1,ir; 
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e,z,drz,dz,z1,z0,w,de0sp,zi,zp,rg1;
      Real ssp0[MxNsp],ssp1[MxNsp],cvsp[MxNsp],e0sp[MxNsp];
      Real  a0,a1,a2,a3,a4,a5;
      Int nq;
      Real *q, *aux, *dU, *dq;

      nq = q_view.get_dim1();

      q = q_view.get_data();
      aux = aux_view.get_data();
      dU = dU_view.get_data();
      dq = dq_view.get_data();

      if( iqe > iqs )
     {

         rg1= runi/unit[2];
         for( iq=iqs;iq<iqe;iq++ )
        {
            //t=  q[3][iq];
            //p=  q[4][iq];
            //z=  q[5][iq];
            t=  q[ADDR(3,iq,nq)];
            p=  q[ADDR(4,iq,nq)];
            z=  q[ADDR(5,iq,nq)];
            //ro= aux[0][iq];
            //h=  aux[3][iq];
            //rg= aux[4][iq];
            //cv= aux[5][iq];
            ro= aux[ADDR(0,iq,nq)];
            h=  aux[ADDR(3,iq,nq)];
            rg= aux[ADDR(4,iq,nq)];
            cv= aux[ADDR(5,iq,nq)];
            cv-=rg;
            re= ro*h- p;
            e= re/ro;

            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( t > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               e0sp[isp]= a5+ t*( a0+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );
               e0sp[isp]-= t;
               e0sp[isp]*= rg1;
           }

            //dro= dU[0][iq];
            //dre= dU[4][iq];
            //drz= dU[5][iq];
            dro= dU[ADDR(0,iq,nq)];
            dre= dU[ADDR(4,iq,nq)];
            drz= dU[ADDR(5,iq,nq)];

            dz= ( drz- z*dro )/ro;
            dz= fmax( -0.3*z,dz );
            dz= fmin( 0.3*(1-z),dz );
            gtcomp( z   ,ssp0 );
            gtcomp( z+dz,ssp1 );

            de0sp=0;
            Real dn= 0;
            Real n=0;
            for( isp=0;isp<nsp;isp++ )
           {
               de0sp+= ( ssp1[isp]- ssp0[isp] )*e0sp[isp];
               dn+= ssp1[isp]-ssp0[isp];
               n+= ssp0[isp];
           }
            de0sp*= ro;

            //dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
            //dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
            //dq[2][iq]= ( dU[3][iq]- q[2][iq]*dro )/ro;
            dq[ADDR(0,iq,nq)]= ( dU[ADDR(1,iq,nq)]- q[ADDR(0,iq,nq)]*dro )/ro;
            dq[ADDR(1,iq,nq)]= ( dU[ADDR(2,iq,nq)]- q[ADDR(1,iq,nq)]*dro )/ro;
            dq[ADDR(2,iq,nq)]= ( dU[ADDR(3,iq,nq)]- q[ADDR(2,iq,nq)]*dro )/ro;

            //dk=  q[0][iq]*dq[0][iq];
            //dk+= q[1][iq]*dq[1][iq];
            //dk+= q[2][iq]*dq[2][iq];
            dk=  q[ADDR(0,iq,nq)]*dq[ADDR(0,iq,nq)];
            dk+= q[ADDR(1,iq,nq)]*dq[ADDR(1,iq,nq)];
            dk+= q[ADDR(2,iq,nq)]*dq[ADDR(2,iq,nq)];

            dk*= ro;
            dt= ( dre- e*dro- dk- de0sp )/( ro*cv );
            dp= p*( dro/ro+ dt/t+ dn/n );

            //dq[3][iq]= dt;
            //dq[4][iq]= dp;
            //dq[5][iq]= dz;

            //set constraint on the perturbation of dt and dp
            //     if(dt>0) dt = fmin( 0.3*t, dt);
            //else if(dt<0) dt = fmax(-0.3*t, dt);
            //     if(dp>0) dp = fmin( 0.3*p, dp);
            //else if(dp<0) dp = fmax(-0.3*p, dp);
            dt = fmin(fmax(dt, -0.3 * t), 0.3 * t);
            dp = fmin(fmax(dp, -0.3 * p), 0.3 * p);


            dq[ADDR(3,iq,nq)]= dt;
            dq[ADDR(4,iq,nq)]= dp;
            dq[ADDR(5,iq,nq)]= dz;

        }
     } 
  }

   void cMfReactingGas::diflxb33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                   cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                   cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {

      Real            dzl, ml, drl, dpl, drel, dunl, dsspl[MxNsp];
      Real            dzr, mr, drr, dpr, drer, dunr, dsspr[MxNsp];

      Real            aa,a2a,ra,ha,epa,ka,ua[3],ana[3],una,unaa,raa,cva,za,ta, la1,la4,la3,lmax,fa[MxNVs],
                      gama,rga,sspa[MxNsp],cvspa[MxNsp],dssp[MxNsp],e0spa[MxNsp],etaa;

      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,zl,fl[MxNVs],sspl[MxNsp],cvspl[MxNsp],e0spl[MxNsp],rgl;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,zr,fr[MxNVs],sspr[MxNsp],cvspr[MxNsp],e0spr[MxNsp],rgr;
      Real            le1,le3,le4;
      Real            dw1,dw3,dw4,dw5,dw2[3],dw2a,dw5a,dr,du[3],dun,dp,dpa,dw6[MxNVs],qa[MxNVs];
      Real            f[MxNVs];
      Real            a0,a1,a2,a3,a4,a5;

      Int             ia,ic,iql,iqr,isp;
      Int             ir,ir0,ir1;
      Real            z,z0,z1,zp;
      Real            rg1= runi/unit[2];

      Int nfc, nq;
      Int nql,nqr;
 
      Int *icql;
      Real *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql = icql_view.get_data();
      sql = ql.get_data();
      sauxl = auxl.get_data();
      sdql = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl = resl.get_data();
      icqr = icqr_view.get_data();
      sqr = qr.get_data();
      sauxr = auxr.get_data();
      sdqr = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr = resr.get_data();
      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      nql = nfc;
      nqr = nq;
      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            //iql= ic;
            //if( icql ){ iql= icql[ic]; };
            //iqr= ic;
            //if( icqr ){ iqr= icqr[ic]; };
            iql= ic;
            iqr= icqr[ic]; 

// fluxes from the left

            //tl=  ql[3][iql];
            //pl=  ql[4][iql];
            //zl=  ql[5][iql];
            tl=  sql[ADDR(3,iql,nql)];
            pl=  sql[ADDR(4,iql,nql)];
            zl=  sql[ADDR(5,iql,nql)];
            //rl=  auxl[0][iql];
            //kl=  auxl[1][iql];
            //al=  auxl[2][iql];
            //hl=  auxl[3][iql];
            //rgl= auxl[4][iql];
            //cvl= auxl[5][iql]-rgl;
            rl=  sauxl[ADDR(0,iql,nql)];
            kl=  sauxl[ADDR(1,iql,nql)];
            al=  sauxl[ADDR(2,iql,nql)];
            hl=  sauxl[ADDR(3,iql,nql)];
            rgl= sauxl[ADDR(4,iql,nql)];
            cvl= sauxl[ADDR(5,iql,nql)]-rgl;

            gtcomp( zl,sspl );
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( tl > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvspl[isp]= ( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
               cvspl[isp]-= 1.;
               cvspl[isp]*= rg1;

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               e0spl[isp]= a5+ tl*( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
               e0spl[isp]-= tl;
               e0spl[isp]*= rg1;

           }
            e0l= hl-kl-rgl*tl;

            //drl=    dql[0][iql];
            //dpl=  dauxl[4][iql];
            //dzl=  dauxl[5][iql];
            //drel=   dql[4][iql];
            drl=    sdql[ADDR(0,iql,nql)];
            dpl=  sdauxl[ADDR(4,iql,nql)];
            dzl=  sdauxl[ADDR(5,iql,nql)];
            drel=   sdql[ADDR(4,iql,nql)];


            //unl= -wxdc[0][ic];
            //unl+= wc[0][ic]*ql[0][iql]; 
            //unl+= wc[1][ic]*ql[1][iql]; 
            //unl+= wc[2][ic]*ql[2][iql]; 
            unl= -swxdc[ADDR(0,ic,nfc)];
            unl+= swc[ADDR(0,ic,nfc)]*sql[ADDR(0,iql,nql)]; 
            unl+= swc[ADDR(1,ic,nfc)]*sql[ADDR(1,iql,nql)]; 
            unl+= swc[ADDR(2,ic,nfc)]*sql[ADDR(2,iql,nql)]; 
            ll1= unl;
            ll3= ll1+ al;
            ll4= ll1- al;
            //dunl=  wc[0][ic]*dauxl[0][iql]; 
            //dunl+= wc[1][ic]*dauxl[1][iql]; 
            //dunl+= wc[2][ic]*dauxl[2][iql]; 
            dunl=  swc[ADDR(0,ic,nfc)]*sdauxl[ADDR(0,iql,nql)]; 
            dunl+= swc[ADDR(1,ic,nfc)]*sdauxl[ADDR(1,iql,nql)]; 
            dunl+= swc[ADDR(2,ic,nfc)]*sdauxl[ADDR(2,iql,nql)]; 

            ml= unl*rl;
            fl[0]= drl*unl+ rl*dunl;
            //fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+ dpl*wc[0][ic]; 
            //fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+ dpl*wc[1][ic]; 
            //fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql]+ dpl*wc[2][ic]; 
            //fl[4]= dunl*rl*hl+ unl*( drel+ dpl )+ dpl*wxdc[0][ic];
            //fl[5]= fl[0]*ql[5][iql]+ ml*dauxl[5][iql];
            fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ ml*sdauxl[ADDR(0,iql,nql)]+ dpl*swc[ADDR(0,ic,nfc)]; 
            fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ ml*sdauxl[ADDR(1,iql,nql)]+ dpl*swc[ADDR(1,ic,nfc)]; 
            fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ ml*sdauxl[ADDR(2,iql,nql)]+ dpl*swc[ADDR(2,ic,nfc)]; 
            fl[4]= dunl*rl*hl+ unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
            fl[5]= fl[0]*sql[ADDR(5,iql,nql)]+ ml*sdauxl[ADDR(5,iql,nql)];
            for( ia=6;ia<nv;ia++ )
           {
               //fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
               fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)]+ ml*sdauxl[ADDR(ia,iql,nql)];
           }

// fluxes from the right

            //tr=  qr[3][iqr];
            //pr=  qr[4][iqr];
            //zr=  qr[5][iqr];
            tr=  sqr[ADDR(3,iqr,nqr)];
            pr=  sqr[ADDR(4,iqr,nqr)];
            zr=  sqr[ADDR(5,iqr,nqr)];
            //rr=  auxr[0][iqr];
            //kr=  auxr[1][iqr];
            //ar=  auxr[2][iqr];
            //hr=  auxr[3][iqr];
            //rgr= auxr[4][iqr];
            //cvr= auxr[5][iqr]-rgr;
            rr=  sauxr[ADDR(0,iqr,nqr)];
            kr=  sauxr[ADDR(1,iqr,nqr)];
            ar=  sauxr[ADDR(2,iqr,nqr)];
            hr=  sauxr[ADDR(3,iqr,nqr)];
            rgr= sauxr[ADDR(4,iqr,nqr)];
            cvr= sauxr[ADDR(5,iqr,nqr)]-rgr;
            gtcomp( zr,sspr );
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( tr > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvspr[isp]= ( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
               cvspr[isp]-= 1.;
               cvspr[isp]*= rg1;

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               e0spr[isp]= a5+ tr*( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
               e0spr[isp]-= tr;
               e0spr[isp]*= rg1;

           }
            e0r= hr-kr-rgr*tr;

            //drr=    dqr[0][iqr];
            //dpr=  dauxr[4][iqr];
            //dzr=  dauxr[5][iqr];
            //drer=   dqr[4][iqr];
            drr=    sdqr[ADDR(0,iqr,nqr)];
            dpr=  sdauxr[ADDR(4,iqr,nqr)];
            dzr=  sdauxr[ADDR(5,iqr,nqr)];
            drer=   sdqr[ADDR(4,iqr,nqr)];

            //unr= -wxdc[0][ic];
            //unr+= wc[0][ic]*qr[0][iqr]; 
            //unr+= wc[1][ic]*qr[1][iqr]; 
            //unr+= wc[2][ic]*qr[2][iqr]; 
            unr= -swxdc[ADDR(0,ic,nfc)];
            unr+= swc[ADDR(0,ic,nfc)]*sqr[ADDR(0,iqr,nqr)]; 
            unr+= swc[ADDR(1,ic,nfc)]*sqr[ADDR(1,iqr,nqr)]; 
            unr+= swc[ADDR(2,ic,nfc)]*sqr[ADDR(2,iqr,nqr)]; 
            lr1= unr;
            lr3= lr1+ ar;
            lr4= lr1- ar;
            //dunr=  wc[0][ic]*dauxr[0][iqr]; 
            //dunr+= wc[1][ic]*dauxr[1][iqr]; 
            //dunr+= wc[2][ic]*dauxr[2][iqr]; 
            dunr=  swc[ADDR(0,ic,nfc)]*sdauxr[ADDR(0,iqr,nqr)]; 
            dunr+= swc[ADDR(1,ic,nfc)]*sdauxr[ADDR(1,iqr,nqr)]; 
            dunr+= swc[ADDR(2,ic,nfc)]*sdauxr[ADDR(2,iqr,nqr)]; 

            mr= unr*rr;
            fr[0]= drr*unr+ rr*dunr;
            //fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
            //fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
            //fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
            //fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
            //fr[5]= fr[0]*qr[5][iqr]+ mr*dauxr[5][iqr];
            fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ mr*sdauxr[ADDR(0,iqr,nqr)]+ dpr*swc[ADDR(0,ic,nfc)]; 
            fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ mr*sdauxr[ADDR(1,iqr,nqr)]+ dpr*swc[ADDR(1,ic,nfc)]; 
            fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ mr*sdauxr[ADDR(2,iqr,nqr)]+ dpr*swc[ADDR(2,ic,nfc)]; 
            fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
            fr[5]= fr[0]*sqr[ADDR(5,iqr,nqr)]+ mr*sdauxr[ADDR(5,iqr,nqr)];
            for( ia=6;ia<nv;ia++ )
           {
               //fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
               fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)]+ mr*sdauxr[ADDR(ia,iqr,nqr)];
           }

// one wave

         lmax= sauxc[ADDR(nauxf-1,ic,nfc)];
         fa[0]= lmax*( sdqr[ADDR(0,iqr,nqr)]- sdql[ADDR(0,iql,nql)] );
         fa[1]= lmax*( sdqr[ADDR(1,iqr,nqr)]- sdql[ADDR(1,iql,nql)] );
         fa[2]= lmax*( sdqr[ADDR(2,iqr,nqr)]- sdql[ADDR(2,iql,nql)] );
         fa[3]= lmax*( sdqr[ADDR(3,iqr,nqr)]- sdql[ADDR(3,iql,nql)] );
         fa[4]= lmax*( sdqr[ADDR(4,iqr,nqr)]- sdql[ADDR(4,iql,nql)] );
         for( ia=5;ia<nv;ia++ )
        {
            fa[ia]= lmax*( sdqr[ADDR(ia,iqr,nqr)]- sdql[ADDR(ia,iql,nql)] );
        }

// assemble 
            //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
            //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
            //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
            //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
            //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
            //f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*wc[3][ic];
            f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*swc[ADDR(3,ic,nfc)];
            f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*swc[ADDR(3,ic,nfc)];
            f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*swc[ADDR(3,ic,nfc)];
            f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*swc[ADDR(3,ic,nfc)];
            f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*swc[ADDR(3,ic,nfc)];
            f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*swc[ADDR(3,ic,nfc)];

            //resl[0][iql]-= f[0];
            //resl[1][iql]-= f[1];
            //resl[2][iql]-= f[2];
            //resl[3][iql]-= f[3];
            //resl[4][iql]-= f[4];
            //resl[5][iql]-= f[5];
            sresl[ADDR(0,iql,nql)]-= f[0];
            sresl[ADDR(1,iql,nql)]-= f[1];
            sresl[ADDR(2,iql,nql)]-= f[2];
            sresl[ADDR(3,iql,nql)]-= f[3];
            sresl[ADDR(4,iql,nql)]-= f[4];
            sresl[ADDR(5,iql,nql)]-= f[5];

            //resr[0][iqr]+= f[0];
            //resr[1][iqr]+= f[1];
            //resr[2][iqr]+= f[2];
            //resr[3][iqr]+= f[3];
            //resr[4][iqr]+= f[4];
            //resr[5][iqr]+= f[5];
            sresr[ADDR(0,iqr,nqr)]+= f[0];
            sresr[ADDR(1,iqr,nqr)]+= f[1];
            sresr[ADDR(2,iqr,nqr)]+= f[2];
            sresr[ADDR(3,iqr,nqr)]+= f[3];
            sresr[ADDR(4,iqr,nqr)]+= f[4];
            sresr[ADDR(5,iqr,nqr)]+= f[5];

            for( ia=6;ia<nv;ia++ )
           { 
               //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
               f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*swc[ADDR(3,ic,nfc)];
               //resl[ia][iql]-= f[ia];
               //resr[ia][iqr]+= f[ia];
               sresl[ADDR(ia,iql,nql)]-= f[ia];
               sresr[ADDR(ia,iqr,nqr)]+= f[ia];
           }
        }
     }
  }

   void cMfReactingGas::diflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                  cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                  cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {

      Real            dzl, ml, drl, dpl, drel, dunl, dsspl[MxNsp];
      Real            dzr, mr, drr, dpr, drer, dunr, dsspr[MxNsp];

      Real            aa,a2a,ra,ha,epa,ka,ua[3],ana[3],una,unaa,raa,cva,za,ta, la1,la4,la3,lmax,fa[MxNVs],
                      gama,rga,sspa[MxNsp],cvspa[MxNsp],dssp[MxNsp],e0spa[MxNsp],etaa;

      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,zl,fl[MxNVs],sspl[MxNsp],cvspl[MxNsp],e0spl[MxNsp],rgl;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,zr,fr[MxNVs],sspr[MxNsp],cvspr[MxNsp],e0spr[MxNsp],rgr;
      Real            le1,le3,le4;
      Real            dw1,dw3,dw4,dw5,dw2[3],dw2a,dw5a,dr,du[3],dun,dp,dpa,dw6[MxNVs],qa[MxNVs];
      Real            f[MxNVs];
      Real            a0,a1,a2,a3,a4,a5;

      Int             ia,ic,iql,iqr,isp;
      Int             ir,ir0,ir1;
      Real            z,z0,z1,zp;
      Real            rg1= runi/unit[2];

      Int nfc, nq;
      Int nql,nqr;
 
      Int *icql;
      Real *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql = icql_view.get_data();
      sql = ql.get_data();
      sauxl = auxl.get_data();
      sdql = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl = resl.get_data();
      icqr = icqr_view.get_data();
      sqr = qr.get_data();
      sauxr = auxr.get_data();
      sdqr = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr = resr.get_data();
      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      nql = nfc;
      nqr = nq;
      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            //iql= ic;
            //if( icql ){ iql= icql[ic]; };
            //iqr= ic;
            //if( icqr ){ iqr= icqr[ic]; };
            iql= ic;
            iqr= icqr[ic]; 

// fluxes from the left

            //tl=  ql[3][iql];
            //pl=  ql[4][iql];
            //zl=  ql[5][iql];
            tl=  sql[ADDR(3,iql,nql)];
            pl=  sql[ADDR(4,iql,nql)];
            zl=  sql[ADDR(5,iql,nql)];
            //rl=  auxl[0][iql];
            //kl=  auxl[1][iql];
            //al=  auxl[2][iql];
            //hl=  auxl[3][iql];
            //rgl= auxl[4][iql];
            //cvl= auxl[5][iql]-rgl;
            rl=  sauxl[ADDR(0,iql,nql)];
            kl=  sauxl[ADDR(1,iql,nql)];
            al=  sauxl[ADDR(2,iql,nql)];
            hl=  sauxl[ADDR(3,iql,nql)];
            rgl= sauxl[ADDR(4,iql,nql)];
            cvl= sauxl[ADDR(5,iql,nql)]-rgl;

            gtcomp( zl,sspl );
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( tl > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvspl[isp]= ( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
               cvspl[isp]-= 1.;
               cvspl[isp]*= rg1;

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               e0spl[isp]= a5+ tl*( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
               e0spl[isp]-= tl;
               e0spl[isp]*= rg1;

           }
            e0l= hl-kl-rgl*tl;

            //drl=    dql[0][iql];
            //dpl=  dauxl[4][iql];
            //dzl=  dauxl[5][iql];
            //drel=   dql[4][iql];
            drl=    sdql[ADDR(0,iql,nql)];
            dpl=  sdauxl[ADDR(4,iql,nql)];
            dzl=  sdauxl[ADDR(5,iql,nql)];
            drel=   sdql[ADDR(4,iql,nql)];


            //unl= -wxdc[0][ic];
            //unl+= wc[0][ic]*ql[0][iql]; 
            //unl+= wc[1][ic]*ql[1][iql]; 
            //unl+= wc[2][ic]*ql[2][iql]; 
            unl= -swxdc[ADDR(0,ic,nfc)];
            unl+= swc[ADDR(0,ic,nfc)]*sql[ADDR(0,iql,nql)]; 
            unl+= swc[ADDR(1,ic,nfc)]*sql[ADDR(1,iql,nql)]; 
            unl+= swc[ADDR(2,ic,nfc)]*sql[ADDR(2,iql,nql)]; 
            ll1= unl;
            ll3= ll1+ al;
            ll4= ll1- al;
            //dunl=  wc[0][ic]*dauxl[0][iql]; 
            //dunl+= wc[1][ic]*dauxl[1][iql]; 
            //dunl+= wc[2][ic]*dauxl[2][iql]; 
            dunl=  swc[ADDR(0,ic,nfc)]*sdauxl[ADDR(0,iql,nql)]; 
            dunl+= swc[ADDR(1,ic,nfc)]*sdauxl[ADDR(1,iql,nql)]; 
            dunl+= swc[ADDR(2,ic,nfc)]*sdauxl[ADDR(2,iql,nql)]; 

            ml= unl*rl;
            fl[0]= drl*unl+ rl*dunl;
            //fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+ dpl*wc[0][ic]; 
            //fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+ dpl*wc[1][ic]; 
            //fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql]+ dpl*wc[2][ic]; 
            //fl[4]= dunl*rl*hl+ unl*( drel+ dpl )+ dpl*wxdc[0][ic];
            //fl[5]= fl[0]*ql[5][iql]+ ml*dauxl[5][iql];
            fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ ml*sdauxl[ADDR(0,iql,nql)]+ dpl*swc[ADDR(0,ic,nfc)]; 
            fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ ml*sdauxl[ADDR(1,iql,nql)]+ dpl*swc[ADDR(1,ic,nfc)]; 
            fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ ml*sdauxl[ADDR(2,iql,nql)]+ dpl*swc[ADDR(2,ic,nfc)]; 
            fl[4]= dunl*rl*hl+ unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
            fl[5]= fl[0]*sql[ADDR(5,iql,nql)]+ ml*sdauxl[ADDR(5,iql,nql)];
            for( ia=6;ia<nv;ia++ )
           {
               //fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
               fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)]+ ml*sdauxl[ADDR(ia,iql,nql)];
           }

// fluxes from the right

            //tr=  qr[3][iqr];
            //pr=  qr[4][iqr];
            //zr=  qr[5][iqr];
            tr=  sqr[ADDR(3,iqr,nqr)];
            pr=  sqr[ADDR(4,iqr,nqr)];
            zr=  sqr[ADDR(5,iqr,nqr)];
            //rr=  auxr[0][iqr];
            //kr=  auxr[1][iqr];
            //ar=  auxr[2][iqr];
            //hr=  auxr[3][iqr];
            //rgr= auxr[4][iqr];
            //cvr= auxr[5][iqr]-rgr;
            rr=  sauxr[ADDR(0,iqr,nqr)];
            kr=  sauxr[ADDR(1,iqr,nqr)];
            ar=  sauxr[ADDR(2,iqr,nqr)];
            hr=  sauxr[ADDR(3,iqr,nqr)];
            rgr= sauxr[ADDR(4,iqr,nqr)];
            cvr= sauxr[ADDR(5,iqr,nqr)]-rgr;
            gtcomp( zr,sspr );
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( tr > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvspr[isp]= ( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
               cvspr[isp]-= 1.;
               cvspr[isp]*= rg1;

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               e0spr[isp]= a5+ tr*( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
               e0spr[isp]-= tr;
               e0spr[isp]*= rg1;

           }
            e0r= hr-kr-rgr*tr;

            //drr=    dqr[0][iqr];
            //dpr=  dauxr[4][iqr];
            //dzr=  dauxr[5][iqr];
            //drer=   dqr[4][iqr];
            drr=    sdqr[ADDR(0,iqr,nqr)];
            dpr=  sdauxr[ADDR(4,iqr,nqr)];
            dzr=  sdauxr[ADDR(5,iqr,nqr)];
            drer=   sdqr[ADDR(4,iqr,nqr)];

            //unr= -wxdc[0][ic];
            //unr+= wc[0][ic]*qr[0][iqr]; 
            //unr+= wc[1][ic]*qr[1][iqr]; 
            //unr+= wc[2][ic]*qr[2][iqr]; 
            unr= -swxdc[ADDR(0,ic,nfc)];
            unr+= swc[ADDR(0,ic,nfc)]*sqr[ADDR(0,iqr,nqr)]; 
            unr+= swc[ADDR(1,ic,nfc)]*sqr[ADDR(1,iqr,nqr)]; 
            unr+= swc[ADDR(2,ic,nfc)]*sqr[ADDR(2,iqr,nqr)]; 
            lr1= unr;
            lr3= lr1+ ar;
            lr4= lr1- ar;
            //dunr=  wc[0][ic]*dauxr[0][iqr]; 
            //dunr+= wc[1][ic]*dauxr[1][iqr]; 
            //dunr+= wc[2][ic]*dauxr[2][iqr]; 
            dunr=  swc[ADDR(0,ic,nfc)]*sdauxr[ADDR(0,iqr,nqr)]; 
            dunr+= swc[ADDR(1,ic,nfc)]*sdauxr[ADDR(1,iqr,nqr)]; 
            dunr+= swc[ADDR(2,ic,nfc)]*sdauxr[ADDR(2,iqr,nqr)]; 

            mr= unr*rr;
            fr[0]= drr*unr+ rr*dunr;
            //fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
            //fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
            //fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
            //fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
            //fr[5]= fr[0]*qr[5][iqr]+ mr*dauxr[5][iqr];
            fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ mr*sdauxr[ADDR(0,iqr,nqr)]+ dpr*swc[ADDR(0,ic,nfc)]; 
            fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ mr*sdauxr[ADDR(1,iqr,nqr)]+ dpr*swc[ADDR(1,ic,nfc)]; 
            fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ mr*sdauxr[ADDR(2,iqr,nqr)]+ dpr*swc[ADDR(2,ic,nfc)]; 
            fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
            fr[5]= fr[0]*sqr[ADDR(5,iqr,nqr)]+ mr*sdauxr[ADDR(5,iqr,nqr)];
            for( ia=6;ia<nv;ia++ )
           {
               //fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
               fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)]+ mr*sdauxr[ADDR(ia,iqr,nqr)];
           }

// linearised fluxes

            ra= rr/rl;
            ra= sqrt(ra);
            wl= 1.+ra;
            wl= 1./wl;
            wr= 1.-wl;

            ra*= rl;
            ha= wl*hl+ wr*hr;
            ta= wl*tl+ wr*tr;
            za= wl*zl+ wr*zr;

            rga=0;
            for( isp=0;isp<nsp;isp++ )
           {
               sspa[isp]= wl*sspl[isp]+ wr*sspr[isp];
               e0spa[isp]= wl*e0spl[isp]+ wr*e0spr[isp];
               dssp[isp]= sspr[isp]-sspl[isp];
               rga+= sspa[isp];
           }
            rga*= rg1;
            if( fabs( tl-tr ) > 1.e-4*(tl+tr) )
           {
               for( isp=0;isp<nsp;isp++ )
              {
                  cvspa[isp]= (e0spr[isp]-e0spl[isp])/(tr-tl);
              }
           }
            else
           {
               for( isp=0;isp<nsp;isp++ )
              {
                  cvspa[isp]= 0.5*(cvspr[isp]+cvspl[isp]);
              }
           }
            cva= 0;
            for( isp=0;isp<nsp;isp++ )    
           {
               cva+= cvspa[isp]*sspa[isp];
           }
            gama= (cva+rga)/cva;

            etaa= 0;
            for( isp=0;isp<nsp;isp++ )
           {
               e0spa[isp]-= rg1*ta/(gama-1);
               etaa+= e0spa[isp]*dssp[isp];
           }
          
            //ua[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
            //ua[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
            //ua[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
            ua[0]= wl*sql[ADDR(0,iql,nql)]+ wr*sqr[ADDR(0,iqr,nqr)];
            ua[1]= wl*sql[ADDR(1,iql,nql)]+ wr*sqr[ADDR(1,iqr,nqr)];
            ua[2]= wl*sql[ADDR(2,iql,nql)]+ wr*sqr[ADDR(2,iqr,nqr)];

            ka=  ua[0]*ua[0];
            ka+= ua[1]*ua[1];
            ka+= ua[2]*ua[2];
            ka*= 0.5; 

            //una=  ua[0]* wc[0][ic];
            //una+= ua[1]* wc[1][ic];
            //una+= ua[2]* wc[2][ic];
            una=  ua[0]* swc[ADDR(0,ic,nfc)];
            una+= ua[1]* swc[ADDR(1,ic,nfc)];
            una+= ua[2]* swc[ADDR(2,ic,nfc)];

            epa=wl*e0l+wr*e0r;
            epa=epa-cva*ta;
            epa+=ka;
            a2a= (gama-1.)*( ha- epa );
            aa= sqrt( a2a );
            raa=ra*aa;

// eigenvalues with Harten's fix

            //la1= una-wxdc[0][ic];
            la1= una-swxdc[ADDR(0,ic,nfc)];
            la3= la1+ aa;
            la4= la1- aa;

            lmax= fabs(la1)+ aa;
            lmax= fmax( lmax, fabs(ll1)+ al );
            lmax= fmax( lmax, fabs(lr1)+ ar );

            Real eps0= eps*lmax;
            le3= fmax( fmax( eps0, la3-ll3), lr3-la3 );
            le4= fmax( fmax( eps0, la4-ll4), lr4-la4 );

            la1= fabs(la1);
            la3= fabs(la3);
            la4= fabs(la4);

            if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
            if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

// Left eigenvectors

            dr= drr- drl;
            //du[0]= dauxr[0][iqr]- dauxl[0][iql];
            //du[1]= dauxr[1][iqr]- dauxl[1][iql];
            //du[2]= dauxr[2][iqr]- dauxl[2][iql];
            du[0]= sdauxr[ADDR(0,iqr,nqr)]- sdauxl[ADDR(0,iql,nql)];
            du[1]= sdauxr[ADDR(1,iqr,nqr)]- sdauxl[ADDR(1,iql,nql)];
            du[2]= sdauxr[ADDR(2,iqr,nqr)]- sdauxl[ADDR(2,iql,nql)];
            dun= dunr- dunl;

            la3*=  0.5*ra/aa;
            la4*= -0.5*ra/aa;

            dp= dpr-dpl;
            dpa=dp/raa;
            dw1= la1*(dr-dp/a2a);
            dw3= la3*(dun+dpa);
            dw4= la4*(dun-dpa);

            //dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
            //dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
            //dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
            dw2[0]= du[0]- dun*swc[ADDR(0,ic,nfc)]; dw2[0]*= ra*la1;
            dw2[1]= du[1]- dun*swc[ADDR(1,ic,nfc)]; dw2[1]*= ra*la1;
            dw2[2]= du[2]- dun*swc[ADDR(2,ic,nfc)]; dw2[2]*= ra*la1;

            dw2a=  dw2[0]*ua[0];
            dw2a+= dw2[1]*ua[1];
            dw2a+= dw2[2]*ua[2];

            ana[0]= aa*swc[ADDR(0,ic,nfc)];
            ana[1]= aa*swc[ADDR(1,ic,nfc)];
            ana[2]= aa*swc[ADDR(2,ic,nfc)];

            dw5= la1*ra*(dzr-dzl);
            dw5a= etaa*la1*ra;

            for( ia=6;ia<nv;ia++ )
           {
               //qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
               qa[ia]= wl*sql[ADDR(ia,iql,nql)]+ wr*sqr[ADDR(ia,iqr,nqr)];
               //dw6[ia]= la1*ra*( dauxr[ia][iqr]- dauxl[ia][iql] );
               dw6[ia]= la1*ra*( sdauxr[ADDR(ia,iqr,nqr)]- sdauxl[ADDR(ia,iql,nql)] );
           }
 
            unaa=aa*una;

// Roe fluxes
            fa[0]=   dw1+               dw3+                  dw4;
            fa[1]=   dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
            fa[2]=   dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
            fa[3]=   dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] );
            fa[4]=   dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa )+          dw5a;
            fa[5]=   fa[0]*za+                                                           dw5;
            for( ia=6;ia<nv;ia++ )
           {
               fa[ia]=    fa[0]*qa[ia]+                                                               dw6[ia];
           }

// assemble 
            Real f[MxNVs];
            //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
            //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
            //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
            //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
            //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
            //f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*wc[3][ic];
            f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*swc[ADDR(3,ic,nfc)];
            f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*swc[ADDR(3,ic,nfc)];
            f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*swc[ADDR(3,ic,nfc)];
            f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*swc[ADDR(3,ic,nfc)];
            f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*swc[ADDR(3,ic,nfc)];
            f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*swc[ADDR(3,ic,nfc)];

            //resl[0][iql]-= f[0];
            //resl[1][iql]-= f[1];
            //resl[2][iql]-= f[2];
            //resl[3][iql]-= f[3];
            //resl[4][iql]-= f[4];
            //resl[5][iql]-= f[5];
            sresl[ADDR(0,iql,nql)]-= f[0];
            sresl[ADDR(1,iql,nql)]-= f[1];
            sresl[ADDR(2,iql,nql)]-= f[2];
            sresl[ADDR(3,iql,nql)]-= f[3];
            sresl[ADDR(4,iql,nql)]-= f[4];
            sresl[ADDR(5,iql,nql)]-= f[5];

            //resr[0][iqr]+= f[0];
            //resr[1][iqr]+= f[1];
            //resr[2][iqr]+= f[2];
            //resr[3][iqr]+= f[3];
            //resr[4][iqr]+= f[4];
            //resr[5][iqr]+= f[5];
            sresr[ADDR(0,iqr,nqr)]+= f[0];
            sresr[ADDR(1,iqr,nqr)]+= f[1];
            sresr[ADDR(2,iqr,nqr)]+= f[2];
            sresr[ADDR(3,iqr,nqr)]+= f[3];
            sresr[ADDR(4,iqr,nqr)]+= f[4];
            sresr[ADDR(5,iqr,nqr)]+= f[5];

            for( ia=6;ia<nv;ia++ )
           { 
               //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
               f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*swc[ADDR(3,ic,nfc)];
               //resl[ia][iql]-= f[ia];
               //resr[ia][iqr]+= f[ia];
               sresl[ADDR(ia,iql,nql)]-= f[ia];
               sresr[ADDR(ia,iqr,nqr)]+= f[ia];
           }
        }
     }
  }

   void cMfReactingGas::diflx33( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                                 cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {

      Real            dzl, ml, drl, dpl, drel, dunl, dsspl[MxNsp];
      Real            dzr, mr, drr, dpr, drer, dunr, dsspr[MxNsp];

      Real            aa,a2a,ra,ha,epa,ka,ua[3],ana[3],una,unaa,raa,cva,za,ta, la1,la4,la3,lmax,fa[MxNVs],
                      gama,rga,sspa[MxNsp],cvspa[MxNsp],dssp[MxNsp],e0spa[MxNsp],etaa;

      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,zl,fl[MxNVs],sspl[MxNsp],cvspl[MxNsp],e0spl[MxNsp],rgl;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,zr,fr[MxNVs],sspr[MxNsp],cvspr[MxNsp],e0spr[MxNsp],rgr;
      Real            le1,le3,le4;
      Real            dw1,dw3,dw4,dw5,dw2[3],dw2a,dw5a,dr,du[3],dun,dp,dpa,dw6[MxNVs],qa[MxNVs];
      Real            f[MxNVs];
      Real            a0,a1,a2,a3,a4,a5;

      Int             ia,ic,iql,iqr,isp;
      Int             ir,ir0,ir1;
      Real            z,z0,z1,zp;
      Real            rg1= runi/unit[2];

      Int nfc, nq;
      
      nfc = wc.get_dim1();
      nq  = q.get_dim1();
      
      Int *sicq;
      Real *sq, *saux, *sdq, *sdaux, *sres, *swc, *swxdc, *sauxc;

      sicq  = icq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      sdq   = dq0.get_data();
      sdaux = daux.get_data();
      sres  = res.get_data(); 
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        {
            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];

// fluxes from the left

            //tl=  ql[3][iql];
            //pl=  ql[4][iql];
            //zl=  ql[5][iql];
            tl=  sq[ADDR(3,iql,nq)];
            pl=  sq[ADDR(4,iql,nq)];
            zl=  sq[ADDR(5,iql,nq)];
            //rl=  auxl[0][iql];
            //kl=  auxl[1][iql];
            //al=  auxl[2][iql];
            //hl=  auxl[3][iql];
            //rgl= auxl[4][iql];
            //cvl= auxl[5][iql]-rgl;
            rl=  saux[ADDR(0,iql,nq)];
            kl=  saux[ADDR(1,iql,nq)];
            al=  saux[ADDR(2,iql,nq)];
            hl=  saux[ADDR(3,iql,nq)];
            rgl= saux[ADDR(4,iql,nq)];
            cvl= saux[ADDR(5,iql,nq)]-rgl;

            gtcomp( zl,sspl );
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( tl > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvspl[isp]= ( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
               cvspl[isp]-= 1.;
               cvspl[isp]*= rg1;

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               e0spl[isp]= a5+ tl*( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
               e0spl[isp]-= tl;
               e0spl[isp]*= rg1;

           }
            e0l= hl-kl-rgl*tl;

            //drl=    dql[0][iql];
            //dpl=  dauxl[4][iql];
            //dzl=  dauxl[5][iql];
            //drel=   dql[4][iql];
            drl=    sdq[ADDR(0,iql,nq)];
            dpl=  sdaux[ADDR(4,iql,nq)];
            dzl=  sdaux[ADDR(5,iql,nq)];
            drel=   sdq[ADDR(4,iql,nq)];


            //unl= -wxdc[0][ic];
            //unl+= wc[0][ic]*ql[0][iql]; 
            //unl+= wc[1][ic]*ql[1][iql]; 
            //unl+= wc[2][ic]*ql[2][iql]; 
            unl= -swxdc[ADDR(0,ic,nfc)];
            unl+= swc[ADDR(0,ic,nfc)]*sq[ADDR(0,iql,nq)]; 
            unl+= swc[ADDR(1,ic,nfc)]*sq[ADDR(1,iql,nq)]; 
            unl+= swc[ADDR(2,ic,nfc)]*sq[ADDR(2,iql,nq)]; 
            ll1= unl;
            ll3= ll1+ al;
            ll4= ll1- al;
            //dunl=  wc[0][ic]*dauxl[0][iql]; 
            //dunl+= wc[1][ic]*dauxl[1][iql]; 
            //dunl+= wc[2][ic]*dauxl[2][iql]; 
            dunl=  swc[ADDR(0,ic,nfc)]*sdaux[ADDR(0,iql,nq)]; 
            dunl+= swc[ADDR(1,ic,nfc)]*sdaux[ADDR(1,iql,nq)]; 
            dunl+= swc[ADDR(2,ic,nfc)]*sdaux[ADDR(2,iql,nq)]; 

            ml= unl*rl;
            fl[0]= drl*unl+ rl*dunl;
            //fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+ dpl*wc[0][ic]; 
            //fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+ dpl*wc[1][ic]; 
            //fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql]+ dpl*wc[2][ic]; 
            //fl[4]= dunl*rl*hl+ unl*( drel+ dpl )+ dpl*wxdc[0][ic];
            //fl[5]= fl[0]*ql[5][iql]+ ml*dauxl[5][iql];
            fl[1]= fl[0]*sq[ADDR(0,iql,nq)]+ ml*sdaux[ADDR(0,iql,nq)]+ dpl*swc[ADDR(0,ic,nfc)]; 
            fl[2]= fl[0]*sq[ADDR(1,iql,nq)]+ ml*sdaux[ADDR(1,iql,nq)]+ dpl*swc[ADDR(1,ic,nfc)]; 
            fl[3]= fl[0]*sq[ADDR(2,iql,nq)]+ ml*sdaux[ADDR(2,iql,nq)]+ dpl*swc[ADDR(2,ic,nfc)]; 
            fl[4]= dunl*rl*hl+ unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
            fl[5]= fl[0]*sq[ADDR(5,iql,nq)]+ ml*sdaux[ADDR(5,iql,nq)];
            for( ia=6;ia<nv;ia++ )
           {
               //fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
               fl[ia]= fl[0]*sq[ADDR(ia,iql,nq)]+ ml*sdaux[ADDR(ia,iql,nq)];
           }

// fluxes from the right

            //tr=  qr[3][iqr];
            //pr=  qr[4][iqr];
            //zr=  qr[5][iqr];
            tr=  sq[ADDR(3,iqr,nq)];
            pr=  sq[ADDR(4,iqr,nq)];
            zr=  sq[ADDR(5,iqr,nq)];
            //rr=  auxr[0][iqr];
            //kr=  auxr[1][iqr];
            //ar=  auxr[2][iqr];
            //hr=  auxr[3][iqr];
            //rgr= auxr[4][iqr];
            //cvr= auxr[5][iqr]-rgr;
            rr=  saux[ADDR(0,iqr,nq)];
            kr=  saux[ADDR(1,iqr,nq)];
            ar=  saux[ADDR(2,iqr,nq)];
            hr=  saux[ADDR(3,iqr,nq)];
            rgr= saux[ADDR(4,iqr,nq)];
            cvr= saux[ADDR(5,iqr,nq)]-rgr;
            gtcomp( zr,sspr );
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( tr > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvspr[isp]= ( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
               cvspr[isp]-= 1.;
               cvspr[isp]*= rg1;

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               e0spr[isp]= a5+ tr*( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
               e0spr[isp]-= tr;
               e0spr[isp]*= rg1;

           }
            e0r= hr-kr-rgr*tr;

            //drr=    dqr[0][iqr];
            //dpr=  dauxr[4][iqr];
            //dzr=  dauxr[5][iqr];
            //drer=   dqr[4][iqr];
            drr=    sdq[ADDR(0,iqr,nq)];
            dpr=  sdaux[ADDR(4,iqr,nq)];
            dzr=  sdaux[ADDR(5,iqr,nq)];
            drer=   sdq[ADDR(4,iqr,nq)];

            //unr= -wxdc[0][ic];
            //unr+= wc[0][ic]*qr[0][iqr]; 
            //unr+= wc[1][ic]*qr[1][iqr]; 
            //unr+= wc[2][ic]*qr[2][iqr]; 
            unr= -swxdc[ADDR(0,ic,nfc)];
            unr+= swc[ADDR(0,ic,nfc)]*sq[ADDR(0,iqr,nq)]; 
            unr+= swc[ADDR(1,ic,nfc)]*sq[ADDR(1,iqr,nq)]; 
            unr+= swc[ADDR(2,ic,nfc)]*sq[ADDR(2,iqr,nq)]; 
            lr1= unr;
            lr3= lr1+ ar;
            lr4= lr1- ar;
            //dunr=  wc[0][ic]*dauxr[0][iqr]; 
            //dunr+= wc[1][ic]*dauxr[1][iqr]; 
            //dunr+= wc[2][ic]*dauxr[2][iqr]; 
            dunr=  swc[ADDR(0,ic,nfc)]*sdaux[ADDR(0,iqr,nq)]; 
            dunr+= swc[ADDR(1,ic,nfc)]*sdaux[ADDR(1,iqr,nq)]; 
            dunr+= swc[ADDR(2,ic,nfc)]*sdaux[ADDR(2,iqr,nq)]; 

            mr= unr*rr;
            fr[0]= drr*unr+ rr*dunr;
            //fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
            //fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
            //fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
            //fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
            //fr[5]= fr[0]*qr[5][iqr]+ mr*dauxr[5][iqr];
            fr[1]= fr[0]*sq[ADDR(0,iqr,nq)]+ mr*sdaux[ADDR(0,iqr,nq)]+ dpr*swc[ADDR(0,ic,nfc)]; 
            fr[2]= fr[0]*sq[ADDR(1,iqr,nq)]+ mr*sdaux[ADDR(1,iqr,nq)]+ dpr*swc[ADDR(1,ic,nfc)]; 
            fr[3]= fr[0]*sq[ADDR(2,iqr,nq)]+ mr*sdaux[ADDR(2,iqr,nq)]+ dpr*swc[ADDR(2,ic,nfc)]; 
            fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
            fr[5]= fr[0]*sq[ADDR(5,iqr,nq)]+ mr*sdaux[ADDR(5,iqr,nq)];
            for( ia=6;ia<nv;ia++ )
           {
               //fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
               fr[ia]= fr[0]*sq[ADDR(ia,iqr,nq)]+ mr*sdaux[ADDR(ia,iqr,nq)];
           }

// one-wave linearised fluxes

            ra= rr/rl;
            ra= sqrt(ra);
            wl= 1.+ra;
            wl= 1./wl;
            wr= 1.-wl;

            ra*= rl;
            ha= wl*hl+ wr*hr;
            ta= wl*tl+ wr*tr;
            za= wl*zl+ wr*zr;

            rga=0;
            for( isp=0;isp<nsp;isp++ )
           {
               sspa[isp]= wl*sspl[isp]+ wr*sspr[isp];
               e0spa[isp]= wl*e0spl[isp]+ wr*e0spr[isp];
               dssp[isp]= sspr[isp]-sspl[isp];
               rga+= sspa[isp];
           }
            rga*= rg1;
            if( fabs( tl-tr ) > 1.e-4*(tl+tr) )
           {
               for( isp=0;isp<nsp;isp++ )
              {
                  cvspa[isp]= (e0spr[isp]-e0spl[isp])/(tr-tl);
              }
           }
            else
           {
               for( isp=0;isp<nsp;isp++ )
              {
                  cvspa[isp]= 0.5*(cvspr[isp]+cvspl[isp]);
              }
           }
            cva= 0;
            for( isp=0;isp<nsp;isp++ )    
           {
               cva+= cvspa[isp]*sspa[isp];
           }
            gama= (cva+rga)/cva;

            etaa= 0;
            for( isp=0;isp<nsp;isp++ )
           {
               e0spa[isp]-= rg1*ta/(gama-1);
               etaa+= e0spa[isp]*dssp[isp];
           }
          
            //ua[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
            //ua[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
            //ua[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
            ua[0]= wl*sq[ADDR(0,iql,nq)]+ wr*sq[ADDR(0,iqr,nq)];
            ua[1]= wl*sq[ADDR(1,iql,nq)]+ wr*sq[ADDR(1,iqr,nq)];
            ua[2]= wl*sq[ADDR(2,iql,nq)]+ wr*sq[ADDR(2,iqr,nq)];

            ka=  ua[0]*ua[0];
            ka+= ua[1]*ua[1];
            ka+= ua[2]*ua[2];
            ka*= 0.5; 

            //una=  ua[0]* wc[0][ic];
            //una+= ua[1]* wc[1][ic];
            //una+= ua[2]* wc[2][ic];
            una=  ua[0]* swc[ADDR(0,ic,nfc)];
            una+= ua[1]* swc[ADDR(1,ic,nfc)];
            una+= ua[2]* swc[ADDR(2,ic,nfc)];

            epa=wl*e0l+wr*e0r;
            epa=epa-cva*ta;
            epa+=ka;
            a2a= (gama-1.)*( ha- epa );
            aa= sqrt( a2a );
            raa=ra*aa;

// eigenvalues with Harten's fix

            //la1= una-wxdc[0][ic];
            la1= una-swxdc[ADDR(0,ic,nfc)];
            la3= la1+ aa;
            la4= la1- aa;

            lmax= fabs(la1)+ aa;
            lmax= fmax( lmax, fabs(ll1)+ al );
            lmax= fmax( lmax, fabs(lr1)+ ar );

            Real eps0= eps*lmax;
            le3= fmax( fmax( eps0, la3-ll3), lr3-la3 );
            le4= fmax( fmax( eps0, la4-ll4), lr4-la4 );

            la1= fabs(la1);
            la3= fabs(la3);
            la4= fabs(la4);

            if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
            if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

// Left eigenvectors

            dr= drr- drl;
            //du[0]= dauxr[0][iqr]- dauxl[0][iql];
            //du[1]= dauxr[1][iqr]- dauxl[1][iql];
            //du[2]= dauxr[2][iqr]- dauxl[2][iql];
            du[0]= sdaux[ADDR(0,iqr,nq)]- sdaux[ADDR(0,iql,nq)];
            du[1]= sdaux[ADDR(1,iqr,nq)]- sdaux[ADDR(1,iql,nq)];
            du[2]= sdaux[ADDR(2,iqr,nq)]- sdaux[ADDR(2,iql,nq)];
            dun= dunr- dunl;

            la3*=  0.5*ra/aa;
            la4*= -0.5*ra/aa;

            dp= dpr-dpl;
            dpa=dp/raa;
            dw1= la1*(dr-dp/a2a);
            dw3= la3*(dun+dpa);
            dw4= la4*(dun-dpa);

            //dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
            //dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
            //dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
            dw2[0]= du[0]- dun*swc[ADDR(0,ic,nfc)]; dw2[0]*= ra*la1;
            dw2[1]= du[1]- dun*swc[ADDR(1,ic,nfc)]; dw2[1]*= ra*la1;
            dw2[2]= du[2]- dun*swc[ADDR(2,ic,nfc)]; dw2[2]*= ra*la1;

            dw2a=  dw2[0]*ua[0];
            dw2a+= dw2[1]*ua[1];
            dw2a+= dw2[2]*ua[2];

            ana[0]= aa*swc[ADDR(0,ic,nfc)];
            ana[1]= aa*swc[ADDR(1,ic,nfc)];
            ana[2]= aa*swc[ADDR(2,ic,nfc)];

            dw5= la1*ra*(dzr-dzl);
            dw5a= etaa*la1*ra;

            for( ia=6;ia<nv;ia++ )
           {
               //qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
               qa[ia]= wl*sq[ADDR(ia,iql,nq)]+ wr*sq[ADDR(ia,iqr,nq)];
               //dw6[ia]= la1*ra*( dauxr[ia][iqr]- dauxl[ia][iql] );
               dw6[ia]= la1*ra*( sdaux[ADDR(ia,iqr,nq)]- sdaux[ADDR(ia,iql,nq)] );
           }
 
            unaa=aa*una;

// Roe fluxes
            fa[0]=   dw1+               dw3+                  dw4;
            fa[1]=   dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
            fa[2]=   dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
            fa[3]=   dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] );
            fa[4]=   dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa )+          dw5a;
            fa[5]=   fa[0]*za+                                                           dw5;
            for( ia=6;ia<nv;ia++ )
           {
               fa[ia]=    fa[0]*qa[ia]+                                                               dw6[ia];
           }

// assemble 
            Real f[MxNVs];
            //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
            //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
            //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
            //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
            //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
            //f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*wc[3][ic];
            f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*swc[ADDR(3,ic,nfc)];
            f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*swc[ADDR(3,ic,nfc)];
            f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*swc[ADDR(3,ic,nfc)];
            f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*swc[ADDR(3,ic,nfc)];
            f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*swc[ADDR(3,ic,nfc)];
            f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*swc[ADDR(3,ic,nfc)];

            //resl[0][iql]-= f[0];
            //resl[1][iql]-= f[1];
            //resl[2][iql]-= f[2];
            //resl[3][iql]-= f[3];
            //resl[4][iql]-= f[4];
            //resl[5][iql]-= f[5];
            sres[ADDR(0,iql,nq)]-= f[0];
            sres[ADDR(1,iql,nq)]-= f[1];
            sres[ADDR(2,iql,nq)]-= f[2];
            sres[ADDR(3,iql,nq)]-= f[3];
            sres[ADDR(4,iql,nq)]-= f[4];
            sres[ADDR(5,iql,nq)]-= f[5];

            //resr[0][iqr]+= f[0];
            //resr[1][iqr]+= f[1];
            //resr[2][iqr]+= f[2];
            //resr[3][iqr]+= f[3];
            //resr[4][iqr]+= f[4];
            //resr[5][iqr]+= f[5];
            sres[ADDR(0,iqr,nq)]+= f[0];
            sres[ADDR(1,iqr,nq)]+= f[1];
            sres[ADDR(2,iqr,nq)]+= f[2];
            sres[ADDR(3,iqr,nq)]+= f[3];
            sres[ADDR(4,iqr,nq)]+= f[4];
            sres[ADDR(5,iqr,nq)]+= f[5];

            for( ia=6;ia<nv;ia++ )
           { 
               //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
               f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*swc[ADDR(3,ic,nfc)];
               //resl[ia][iql]-= f[ia];
               //resr[ia][iqr]+= f[ia];
               sres[ADDR(ia,iql,nq)]-= f[ia];
               sres[ADDR(ia,iqr,nq)]+= f[ia];
           }
        }
     }
  }

   void cMfReactingGas::iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icql_view, Int idl, cAu3xView<Real>& xql, cAu3xView<Real>& ql0, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& auxl0, cAu3xView<Real>& rhsl,
                                                      cAu3xView<Int>& icqr_view, Int idr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr0, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& auxr0, cAu3xView<Real>& rhsr,
                                                      cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder )

  {
      Real            wl,wr;
      Real            zl,al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,gaml,rgl,cvl,fl[MxNVs],ql[MxNVs];
      Real            zr,ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,gamr,rgr,cvr,fr[MxNVs],qr[MxNVs];
      Real            le1,le3,le4;
      Real            za,aa,a2a,ra,ha,epa,ka,qa[MxNVs],ua[3],ana[3],una,unaa,raa,cva,ta, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,dw5,dw6[MxNVs],dw5a,rga,gama,etaa;
      Real            f[MxNVs];
      Int             ia,ic,iql,iqr,isp,ir;
      Real            rg1= runi/unit[2];
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Real            xn[3],wn[3];


      Real e0spl[MxNsp],cvspl[MxNsp];
      Real e0spr[MxNsp],cvspr[MxNsp];
      Real e0spa[MxNsp],cvspa[MxNsp];
      Real sspl[MxNsp];
      Real sspa[MxNsp];
      Real sspr[MxNsp];
      Real dssp[MxNsp];
      Real a0,a1,a2,a3,a4,a5;

      Int nfc, nq, nql, nqr;
      Int *icql;
      Real *sxql, *sql, *sdxdxl, *sdqdxl, *sauxl, *srhsl;
      Int *icqr;
      Real *sxqr, *sqr, *sdxdxr, *sdqdxr, *sauxr, *srhsr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr0.get_dim1();
      nql = nfc;
      nqr = nq;

      icql   = icql_view.get_data();
      sxql   = xql.get_data();
      sql    = ql0.get_data();
      sdxdxl = dxdxl.get_data();
      sdqdxl = dqdxl.get_data();
      sauxl  = auxl0.get_data();
      srhsl  = rhsl.get_data();
      icqr   = icqr_view.get_data();
      sxqr   = xqr.get_data();
      sqr    = qr0.get_data();
      sdxdxr = dxdxr.get_data();
      sdqdxr = dqdxr.get_data();
      sauxr  = auxr0.get_data();
      srhsr  = rhsr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      rg1= runi/unit[2];

      for( ic=ics;ic<ice;ic++ )
     {

         //wn[0]= wc[0][ic];
         //wn[1]= wc[1][ic];
         //wn[2]= wc[2][ic];
         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];

         //xn[0]= xc[0][ic];
         //xn[1]= xc[1][ic];
         //xn[2]= xc[2][ic];
         xn[0]= sxc[ADDR(0,ic,nfc)];
         xn[1]= sxc[ADDR(1,ic,nfc)];
         xn[2]= sxc[ADDR(2,ic,nfc)];

         iql= ic;
         iqr= icqr[ic]; 

//         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );
         deltq( nv, iql, sxql, sql, sdxdxl, sdqdxl,
                    iqr, sxqr, sqr, sdxdxr, sdqdxr,
                    xn, wn,  dql0, dqr0, dql, dqr, nfc, nq );

         for( ia=0;ia<nv;ia++ )
        {
//            ql[ia]= ql0[ia][iql]+ dql[ia];
//            qr[ia]= qr0[ia][iqr]+ dqr[ia];
            ql[ia]= sql[ADDR(ia,iql,nql)]+ (iorder-1)*dql[ia];
            qr[ia]= sqr[ADDR(ia,iqr,nqr)]+ (iorder-1)*dqr[ia];
        }

// fluxes from the left
         tl=  ql[3];
         pl=  ql[4];
         zl=  ql[5];
         gtcomp( zl,sspl );

         hl= 0;
         cvl= 0;
         rgl= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tl > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvspl[isp]= ( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            cvspl[isp]-= 1;
//wrong?
//          cvl-= 1;
            cvl+= cvspl[isp]*sspl[isp];
            cvspl[isp]*= rg1;

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            e0spl[isp]= a5+ tl*( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            hl+= e0spl[isp]*sspl[isp];
            e0spl[isp]-= tl; 
            e0spl[isp]*= rg1;

            rgl+= sspl[isp];
        }
         
         hl*=  rg1;
         rgl*= rg1;
         cvl*= rg1;
         gaml= (cvl+rgl)/cvl;

         auxl[0]= pl/( rgl*tl );

         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;

         auxl[2]= gaml*rgl*tl;
         auxl[3]= hl+ auxl[1];
         auxl[2]= sqrt( auxl[2] );
         auxl[4]= cvl+rgl;

         rl=  auxl[0];
         kl=  auxl[1];
         al=  auxl[2];
         hl=  auxl[3];
         cvl= auxl[4];
         cvl-= rgl;
         e0l= hl-kl-rgl*tl;
         //unl=  wc[0][ic]*ql[0];
         //unl+= wc[1][ic]*ql[1];
         //unl+= wc[2][ic]*ql[2];
         unl=  swc[ADDR(0,ic,nfc)]*ql[0];
         unl+= swc[ADDR(1,ic,nfc)]*ql[1];
         unl+= swc[ADDR(2,ic,nfc)]*ql[2];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0]+ wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1]+ wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2]+ wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
         fl[1]= fl[0]*ql[0]+ swc[ADDR(0,ic,nfc)]*pl;
         fl[2]= fl[0]*ql[1]+ swc[ADDR(1,ic,nfc)]*pl;
         fl[3]= fl[0]*ql[2]+ swc[ADDR(2,ic,nfc)]*pl;
         fl[4]= fl[0]*hl+ swxdc[ADDR(0,ic,nfc)]*pl;
         fl[5]= fl[0]*ql[5];
         for( ia=6;ia<nv;ia++ )
        {  
            fl[ia]= fl[0]*ql[ia];
        }

// fluxes from the right

         tr=  qr[3];
         pr=  qr[4];
         zr=  qr[5];
         gtcomp( zr,sspr );

         hr= 0;
         cvr= 0;
         rgr= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tr > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvspr[isp]= ( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            cvspr[isp]-= 1;
//Wrong?
//          cvr-= 1;
            cvr+= cvspr[isp]*sspr[isp];
            cvspr[isp]*= rg1;

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            e0spr[isp]= a5+ tr*( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            hr+= e0spr[isp]*sspr[isp];
            e0spr[isp]-= tr; 
            e0spr[isp]*= rg1;

            rgr+= sspr[isp];
        }
         
         rgr*= rg1;
         cvr*= rg1;
         hr*=  rg1;
         gamr= (cvr+rgr)/cvr;

         auxr[0]= pr/( rgr*tr );

         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;

         auxr[2]= gamr*rgr*tr;
         auxr[3]= hr+ auxr[1];
         auxr[2]= sqrt( auxr[2] );
         auxr[4]= cvr+rgr;

         rr=  auxr[0];
         kr=  auxr[1];
         ar=  auxr[2];
         hr=  auxr[3];
         cvr= auxr[4];
         cvr-= rgr;
         e0r= hr-kr-rgr*tr;
         //unr=  wc[0][ic]*qr[0];
         //unr+= wc[1][ic]*qr[1];
         //unr+= wc[2][ic]*qr[2];
         unr=  swc[ADDR(0,ic,nfc)]*qr[0];
         unr+= swc[ADDR(1,ic,nfc)]*qr[1];
         unr+= swc[ADDR(2,ic,nfc)]*qr[2];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1*rr;
         //fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
         fr[1]= fr[0]*qr[0]+ swc[ADDR(0,ic,nfc)]*pr;
         fr[2]= fr[0]*qr[1]+ swc[ADDR(1,ic,nfc)]*pr;
         fr[3]= fr[0]*qr[2]+ swc[ADDR(2,ic,nfc)]*pr;
         fr[4]= fr[0]*hr+ swxdc[ADDR(0,ic,nfc)]*pr;
         fr[5]= fr[0]*qr[5];
         for( ia=6;ia<nv;ia++ )
        {  
            fr[ia]= fr[0]*qr[ia];
        }


// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;
         ha= wl*hl+ wr*hr;
         ta= wl*tl+ wr*tr;
         za= wl*zl+ wr*zr;

         for( ia=6;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }

         rga=0;
         for( isp=0;isp<nsp;isp++ )
        {
            sspa[isp]= wl*sspl[isp]+ wr*sspr[isp];
            e0spa[isp]= wl*e0spl[isp]+ wr*e0spr[isp];
            dssp[isp]= sspr[isp]-sspl[isp];
            rga+= sspa[isp];
        }
         rga*= rg1;
         if( fabs( tl-tr ) > 1.e-4*(tr+tl) )
        {
            for( isp=0;isp<nsp;isp++ )
           {
               cvspa[isp]= (e0spr[isp]-e0spl[isp])/(tr-tl);
           }
        }
         else
        {
            for( isp=0;isp<nsp;isp++ )
           {
               cvspa[isp]= 0.5*(cvspr[isp]+cvspl[isp]);
           }
        }
         cva= 0;
         for( isp=0;isp<nsp;isp++ )    
        {
            cva+= cvspa[isp]*sspa[isp];
        }
         gama= (cva+rga)/cva;

         etaa= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            e0spa[isp]-= rg1*ta/(gama-1);
            etaa+= e0spa[isp]*dssp[isp];
        }
       
         ua[0]= wl*ql[0]+ wr*qr[0];
         ua[1]= wl*ql[1]+ wr*qr[1];
         ua[2]= wl*ql[2]+ wr*qr[2];

         ka=  ua[0]*ua[0];
         ka+= ua[1]*ua[1];
         ka+= ua[2]*ua[2];
         ka*= 0.5; 

         //una=  ua[0]* wc[0][ic];
         //una+= ua[1]* wc[1][ic];
         //una+= ua[2]* wc[2][ic];
         una=  ua[0]* swc[ADDR(0,ic,nfc)];
         una+= ua[1]* swc[ADDR(1,ic,nfc)];
         una+= ua[2]* swc[ADDR(2,ic,nfc)];

         epa=wl*e0l+wr*e0r;
         epa=epa-cva*ta;
         epa+=ka;
         a2a= (gama-1.)*( ha- epa );
//         if( a2a < 0 ){ cout << "a2a "<<a2a<<"\n"; stophere();};
//       assert( a2a > 0 );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         Real eps0= eps*lmax;
         le3= fmax( fmax( eps0, la3-ll3), lr3-la3 );
         le4= fmax( fmax( eps0, la4-ll4), lr4-la4 );

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);

         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages
         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= epa;
         //auxc[4][ic]= gama;
         //auxc[5][ic]= la1;
         //auxc[6][ic]= la3;
         //auxc[7][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= epa;
         sauxc[ADDR(4,ic,nfc)]= gama;
         sauxc[ADDR(5,ic,nfc)]= la1;
         sauxc[ADDR(6,ic,nfc)]= la3;
         sauxc[ADDR(7,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         du[0]= qr[0]- ql[0];
         du[1]= qr[1]- ql[1];
         du[2]= qr[2]- ql[2];
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         //dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= du[0]- dun*swc[ADDR(0,ic,nfc)]; dw2[0]*= ra*la1;
         dw2[1]= du[1]- dun*swc[ADDR(1,ic,nfc)]; dw2[1]*= ra*la1;
         dw2[2]= du[2]- dun*swc[ADDR(2,ic,nfc)]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*ua[0];
         dw2a+= dw2[1]*ua[1];
         dw2a+= dw2[2]*ua[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*swc[ADDR(0,ic,nfc)];
         ana[1]= aa*swc[ADDR(1,ic,nfc)];
         ana[2]= aa*swc[ADDR(2,ic,nfc)];

         dw5= la1*ra*(zr-zl);
         dw5a= etaa*la1*ra;

         for( ia=6;ia<nv;ia++ )
        {
            dw6[ia]= la1*ra*( qr[ia]-ql[ia] );
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=  dw1+               dw3+                  dw4;
         fa[1]=  dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] ); 
         fa[2]=  dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] ); 
         fa[3]=  dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] ); 
         fa[4]=  dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa )+        dw5a;
         fa[5]=  fa[0]*za+                                                         dw5;

         for( ia=6;ia<nv;ia++ )
        {
            fa[ia]=  fa[0]*qa[ia]+ dw6[ia];
        }

// assemble 
         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         //f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*swc[ADDR(3,ic,nfc)];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*swc[ADDR(3,ic,nfc)];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*swc[ADDR(3,ic,nfc)];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*swc[ADDR(3,ic,nfc)];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*swc[ADDR(3,ic,nfc)];
         f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*swc[ADDR(3,ic,nfc)];

         //rhsl[0][iql]-= f[0];
         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         //rhsl[5][iql]-= f[5];
         srhsl[ADDR(0,iql,nql)]-= f[0];
         srhsl[ADDR(1,iql,nql)]-= f[1];
         srhsl[ADDR(2,iql,nql)]-= f[2];
         srhsl[ADDR(3,iql,nql)]-= f[3];
         srhsl[ADDR(4,iql,nql)]-= f[4];
         srhsl[ADDR(5,iql,nql)]-= f[5];

         //rhsr[0][iqr]+= f[0];
         //rhsr[1][iqr]+= f[1];
         //rhsr[2][iqr]+= f[2];
         //rhsr[3][iqr]+= f[3];
         //rhsr[4][iqr]+= f[4];
         //rhsr[5][iqr]+= f[5];
         srhsr[ADDR(0,iqr,nqr)]+= f[0];
         srhsr[ADDR(1,iqr,nqr)]+= f[1];
         srhsr[ADDR(2,iqr,nqr)]+= f[2];
         srhsr[ADDR(3,iqr,nqr)]+= f[3];
         srhsr[ADDR(4,iqr,nqr)]+= f[4];
         srhsr[ADDR(5,iqr,nqr)]+= f[5];

         for( ia=6;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*swc[ADDR(3,ic,nfc)];
            srhsl[ADDR(ia,iql,nql)]-= f[ia];
            srhsr[ADDR(ia,iqr,nqr)]+= f[ia];
        }
      
         //auxc[nauxf-1][ic]*= wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]*= swc[ADDR(3,ic,nfc)];

     }
  }

   void cMfReactingGas::iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                                     cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder )
  {
      Real            wl,wr;
      Real            zl,al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,gaml,rgl,cvl,fl[MxNVs],ql[MxNVs];
      Real            zr,ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,gamr,rgr,cvr,fr[MxNVs],qr[MxNVs];
      Real            le1,le3,le4;
      Real            za,aa,a2a,ra,ha,epa,ka,qa[MxNVs],ua[3],ana[3],una,unaa,raa,cva,ta, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,du[3],dun,dp,dpa,dw5,dw6[MxNVs],dw5a,rga,gama,etaa;
      Real            f[MxNVs];
      Int             ia,ic,iql,iqr,isp,ir;
      Real            rg1= runi/unit[2];
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Real            xn[3],wn[3];


      Real e0spl[MxNsp],cvspl[MxNsp];
      Real e0spr[MxNsp],cvspr[MxNsp];
      Real e0spa[MxNsp],cvspa[MxNsp];
      Real sspl[MxNsp];
      Real sspa[MxNsp];
      Real sspr[MxNsp];
      Real dssp[MxNsp];
      Real a0,a1,a2,a3,a4,a5;

      Int nfc, nq;
      Int *sicq;
      Real *sxq, *sq, *sdxdx, *sdqdx, *saux, *srhs;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = q.get_dim1();

      sicq  = icq.get_data();
      sxq   = xq.get_data();
      sq    = q.get_data();
      sdxdx = dxdx.get_data();
      sdqdx = dqdx.get_data();
      saux  = aux.get_data();
      srhs  = rhs.get_data();
      sxc   = xc.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      rg1= runi/unit[2];

      for( ic=ics;ic<ice;ic++ )
     {

         //wn[0]= wc[0][ic];
         //wn[1]= wc[1][ic];
         //wn[2]= wc[2][ic];
         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];

         //xn[0]= xc[0][ic];
         //xn[1]= xc[1][ic];
         //xn[2]= xc[2][ic];
         xn[0]= sxc[ADDR(0,ic,nfc)];
         xn[1]= sxc[ADDR(1,ic,nfc)];
         xn[2]= sxc[ADDR(2,ic,nfc)];

         iql = sicq[ADDR(0,ic,nfc)];
         iqr = sicq[ADDR(1,ic,nfc)];
//         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );
         deltq( nv,iql,iqr,sxq,sq,sdxdx,sdqdx,xn,wn,dql0,dqr0,dql,dqr,nq );

         for( ia=0;ia<nv;ia++ )
        {
            ql[ia]= sq[ADDR(ia,iql,nq)]+ (iorder-1)*dql[ia];
            qr[ia]= sq[ADDR(ia,iqr,nq)]+ (iorder-1)*dqr[ia];
        }

// fluxes from the left
         tl=  ql[3];
         pl=  ql[4];
         zl=  ql[5];
         gtcomp( zl,sspl );

         hl= 0;
         cvl= 0;
         rgl= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tl > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvspl[isp]= ( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            cvspl[isp]-= 1;
//wrong?
//          cvl-= 1;
            cvl+= cvspl[isp]*sspl[isp];
            cvspl[isp]*= rg1;

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            e0spl[isp]= a5+ tl*( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
            hl+= e0spl[isp]*sspl[isp];
            e0spl[isp]-= tl; 
            e0spl[isp]*= rg1;

            rgl+= sspl[isp];
        }
         
         hl*=  rg1;
         rgl*= rg1;
         cvl*= rg1;
         gaml= (cvl+rgl)/cvl;

         auxl[0]= pl/( rgl*tl );

         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;

         auxl[2]= gaml*rgl*tl;
         auxl[3]= hl+ auxl[1];
         auxl[2]= sqrt( auxl[2] );
         auxl[4]= cvl+rgl;

         rl=  auxl[0];
         kl=  auxl[1];
         al=  auxl[2];
         hl=  auxl[3];
         cvl= auxl[4];
         cvl-= rgl;
         e0l= hl-kl-rgl*tl;
         //unl=  wc[0][ic]*ql[0];
         //unl+= wc[1][ic]*ql[1];
         //unl+= wc[2][ic]*ql[2];
         unl=  swc[ADDR(0,ic,nfc)]*ql[0];
         unl+= swc[ADDR(1,ic,nfc)]*ql[1];
         unl+= swc[ADDR(2,ic,nfc)]*ql[2];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0]+ wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1]+ wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2]+ wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
         fl[1]= fl[0]*ql[0]+ swc[ADDR(0,ic,nfc)]*pl;
         fl[2]= fl[0]*ql[1]+ swc[ADDR(1,ic,nfc)]*pl;
         fl[3]= fl[0]*ql[2]+ swc[ADDR(2,ic,nfc)]*pl;
         fl[4]= fl[0]*hl+ swxdc[ADDR(0,ic,nfc)]*pl;
         fl[5]= fl[0]*ql[5];
         for( ia=6;ia<nv;ia++ )
        {  
            fl[ia]= fl[0]*ql[ia];
        }

// fluxes from the right

         tr=  qr[3];
         pr=  qr[4];
         zr=  qr[5];
         gtcomp( zr,sspr );

         hr= 0;
         cvr= 0;
         rgr= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            ir= 1;
            if( tr > rsp[1][isp] ){ ir= 0; };

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp];
            a2= asp[ir][2][isp];
            a3= asp[ir][3][isp];
            a4= asp[ir][4][isp];
            cvspr[isp]= ( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            cvspr[isp]-= 1;
//Wrong?
//          cvr-= 1;
            cvr+= cvspr[isp]*sspr[isp];
            cvspr[isp]*= rg1;

            a0= asp[ir][0][isp];
            a1= asp[ir][1][isp]/2;
            a2= asp[ir][2][isp]/3;
            a3= asp[ir][3][isp]/4;
            a4= asp[ir][4][isp]/5;
            a5= asp[ir][5][isp];
            e0spr[isp]= a5+ tr*( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
            hr+= e0spr[isp]*sspr[isp];
            e0spr[isp]-= tr; 
            e0spr[isp]*= rg1;

            rgr+= sspr[isp];
        }
         
         rgr*= rg1;
         cvr*= rg1;
         hr*=  rg1;
         gamr= (cvr+rgr)/cvr;

         auxr[0]= pr/( rgr*tr );

         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;

         auxr[2]= gamr*rgr*tr;
         auxr[3]= hr+ auxr[1];
         auxr[2]= sqrt( auxr[2] );
         auxr[4]= cvr+rgr;

         rr=  auxr[0];
         kr=  auxr[1];
         ar=  auxr[2];
         hr=  auxr[3];
         cvr= auxr[4];
         cvr-= rgr;
         e0r= hr-kr-rgr*tr;
         //unr=  wc[0][ic]*qr[0];
         //unr+= wc[1][ic]*qr[1];
         //unr+= wc[2][ic]*qr[2];
         unr=  swc[ADDR(0,ic,nfc)]*qr[0];
         unr+= swc[ADDR(1,ic,nfc)]*qr[1];
         unr+= swc[ADDR(2,ic,nfc)]*qr[2];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1*rr;
         //fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
         fr[1]= fr[0]*qr[0]+ swc[ADDR(0,ic,nfc)]*pr;
         fr[2]= fr[0]*qr[1]+ swc[ADDR(1,ic,nfc)]*pr;
         fr[3]= fr[0]*qr[2]+ swc[ADDR(2,ic,nfc)]*pr;
         fr[4]= fr[0]*hr+ swxdc[ADDR(0,ic,nfc)]*pr;
         fr[5]= fr[0]*qr[5];
         for( ia=6;ia<nv;ia++ )
        {  
            fr[ia]= fr[0]*qr[ia];
        }


// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;
         ha= wl*hl+ wr*hr;
         ta= wl*tl+ wr*tr;
         za= wl*zl+ wr*zr;

         for( ia=6;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }

         rga=0;
         for( isp=0;isp<nsp;isp++ )
        {
            sspa[isp]= wl*sspl[isp]+ wr*sspr[isp];
            e0spa[isp]= wl*e0spl[isp]+ wr*e0spr[isp];
            dssp[isp]= sspr[isp]-sspl[isp];
            rga+= sspa[isp];
        }
         rga*= rg1;
         if( fabs( tl-tr ) > 1.e-4*(tr+tl) )
        {
            for( isp=0;isp<nsp;isp++ )
           {
               cvspa[isp]= (e0spr[isp]-e0spl[isp])/(tr-tl);
           }
        }
         else
        {
            for( isp=0;isp<nsp;isp++ )
           {
               cvspa[isp]= 0.5*(cvspr[isp]+cvspl[isp]);
           }
        }
         cva= 0;
         for( isp=0;isp<nsp;isp++ )    
        {
            cva+= cvspa[isp]*sspa[isp];
        }
         gama= (cva+rga)/cva;

         etaa= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            e0spa[isp]-= rg1*ta/(gama-1);
            etaa+= e0spa[isp]*dssp[isp];
        }
       
         ua[0]= wl*ql[0]+ wr*qr[0];
         ua[1]= wl*ql[1]+ wr*qr[1];
         ua[2]= wl*ql[2]+ wr*qr[2];

         ka=  ua[0]*ua[0];
         ka+= ua[1]*ua[1];
         ka+= ua[2]*ua[2];
         ka*= 0.5; 

         //una=  ua[0]* wc[0][ic];
         //una+= ua[1]* wc[1][ic];
         //una+= ua[2]* wc[2][ic];
         una=  ua[0]* swc[ADDR(0,ic,nfc)];
         una+= ua[1]* swc[ADDR(1,ic,nfc)];
         una+= ua[2]* swc[ADDR(2,ic,nfc)];

         epa=wl*e0l+wr*e0r;
         epa=epa-cva*ta;
         epa+=ka;
         a2a= (gama-1.)*( ha- epa );
//         if( a2a < 0 ){ cout << "a2a "<<a2a<<"\n"; stophere();};
//       assert( a2a > 0 );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         Real eps0= eps*lmax;
         le3= fmax( fmax( eps0, la3-ll3), lr3-la3 );
         le4= fmax( fmax( eps0, la4-ll4), lr4-la4 );

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);

         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages
         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= epa;
         //auxc[4][ic]= gama;
         //auxc[5][ic]= la1;
         //auxc[6][ic]= la3;
         //auxc[7][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= epa;
         sauxc[ADDR(4,ic,nfc)]= gama;
         sauxc[ADDR(5,ic,nfc)]= la1;
         sauxc[ADDR(6,ic,nfc)]= la3;
         sauxc[ADDR(7,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         du[0]= qr[0]- ql[0];
         du[1]= qr[1]- ql[1];
         du[2]= qr[2]- ql[2];
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         //dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= du[0]- dun*swc[ADDR(0,ic,nfc)]; dw2[0]*= ra*la1;
         dw2[1]= du[1]- dun*swc[ADDR(1,ic,nfc)]; dw2[1]*= ra*la1;
         dw2[2]= du[2]- dun*swc[ADDR(2,ic,nfc)]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*ua[0];
         dw2a+= dw2[1]*ua[1];
         dw2a+= dw2[2]*ua[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*swc[ADDR(0,ic,nfc)];
         ana[1]= aa*swc[ADDR(1,ic,nfc)];
         ana[2]= aa*swc[ADDR(2,ic,nfc)];

         dw5= la1*ra*(zr-zl);
         dw5a= etaa*la1*ra;

         for( ia=6;ia<nv;ia++ )
        {
            dw6[ia]= la1*ra*( qr[ia]-ql[ia] );
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=  dw1+               dw3+                  dw4;
         fa[1]=  dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] ); 
         fa[2]=  dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] ); 
         fa[3]=  dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] ); 
         fa[4]=  dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa )+        dw5a;
         fa[5]=  fa[0]*za+                                                         dw5;

         for( ia=6;ia<nv;ia++ )
        {
            fa[ia]=  fa[0]*qa[ia]+ dw6[ia];
        }

// assemble 
         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         //f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*swc[ADDR(3,ic,nfc)];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*swc[ADDR(3,ic,nfc)];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*swc[ADDR(3,ic,nfc)];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*swc[ADDR(3,ic,nfc)];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*swc[ADDR(3,ic,nfc)];
         f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*swc[ADDR(3,ic,nfc)];

         //rhsl[0][iql]-= f[0];
         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         //rhsl[5][iql]-= f[5];
         srhs[ADDR(0,iql,nq)]-= f[0];
         srhs[ADDR(1,iql,nq)]-= f[1];
         srhs[ADDR(2,iql,nq)]-= f[2];
         srhs[ADDR(3,iql,nq)]-= f[3];
         srhs[ADDR(4,iql,nq)]-= f[4];
         srhs[ADDR(5,iql,nq)]-= f[5];

         //rhsr[0][iqr]+= f[0];
         //rhsr[1][iqr]+= f[1];
         //rhsr[2][iqr]+= f[2];
         //rhsr[3][iqr]+= f[3];
         //rhsr[4][iqr]+= f[4];
         //rhsr[5][iqr]+= f[5];
         srhs[ADDR(0,iqr,nq)]+= f[0];
         srhs[ADDR(1,iqr,nq)]+= f[1];
         srhs[ADDR(2,iqr,nq)]+= f[2];
         srhs[ADDR(3,iqr,nq)]+= f[3];
         srhs[ADDR(4,iqr,nq)]+= f[4];
         srhs[ADDR(5,iqr,nq)]+= f[5];

         for( ia=6;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*swc[ADDR(3,ic,nfc)];
            srhs[ADDR(ia,iql,nq)]-= f[ia];
            srhs[ADDR(ia,iqr,nq)]+= f[ia];
        }
      
         //auxc[nauxf-1][ic]*= wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]*= swc[ADDR(3,ic,nfc)];

     }
  }

   void cMfReactingGas::iflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                 cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                 cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )

  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,kl,tl,e0l,cvl,zl,fl[MxNVs],sspl[MxNsp],cvspl[MxNsp],e0spl[MxNsp],rgl;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,kr,tr,e0r,cvr,zr,fr[MxNVs],sspr[MxNsp],cvspr[MxNsp],e0spr[MxNsp],rgr;
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,epa,ka,ua[3],ana[3],una,unaa,raa,cva,za,ta, la1,la4,la3,lmax,fa[MxNVs],
                      gama,rga,sspa[MxNsp],cvspa[MxNsp],dssp[MxNsp],e0spa[MxNsp],etaa;
      Real            dw1,dw3,dw4,dw5,dw2[3],dw2a,dw5a,dr,du[3],dun,dp,dpa,dw6[MxNVs],qa[MxNVs];
      Real            f[MxNVs];
      Real            a0,a1,a2,a3,a4,a5;

      Int             ir,ia,ic,iql,iqr,isp;
      Real            rg1= runi/unit[2];

      Int             nql,nqr;

      Int nfc, nq; 
      Int *icql;
      Real *sql, *sauxl, *srhsl;
      Int *icqr; 
      Real *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      srhsr = rhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();


      nql = nfc;
      nqr = nq;

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic]; 

 
// fluxes from the left
            //tl=  ql[3][iql];
            //pl=  ql[4][iql];
            //zl=  ql[5][iql];
            //rl=  auxl[0][iql];
            //kl=  auxl[1][iql];
            //al=  auxl[2][iql];
            //hl=  auxl[3][iql];
            //rgl= auxl[4][iql];
            //cvl= auxl[5][iql]-rgl;
            tl=  sql[ADDR(3,iql,nql)];
            pl=  sql[ADDR(4,iql,nql)];
            zl=  sql[ADDR(5,iql,nql)];
            rl=  sauxl[ADDR(0,iql,nql)];
            kl=  sauxl[ADDR(1,iql,nql)];
            al=  sauxl[ADDR(2,iql,nql)];
            hl=  sauxl[ADDR(3,iql,nql)];
            rgl= sauxl[ADDR(4,iql,nql)];
            cvl= sauxl[ADDR(5,iql,nql)]-rgl;

            gtcomp( zl,sspl );
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( tl > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvspl[isp]= ( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
               cvspl[isp]-= 1.;
               cvspl[isp]*= rg1;

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               e0spl[isp]= a5+ tl*( a0+ tl*( a1+ tl*( a2+ tl*( a3+ tl*a4 ) ) ) );
               e0spl[isp]-= tl;
               e0spl[isp]*= rg1;

           }

            e0l= hl-kl-rgl*tl;
            unl= 0.;
            //unl=  wc[0][ic]*ql[0][iql];
            //unl+= wc[1][ic]*ql[1][iql];
            //unl+= wc[2][ic]*ql[2][iql];
            unl=  swc[ADDR(0,ic,nfc)]*sql[ADDR(0,iql,nql)];
            unl+= swc[ADDR(1,ic,nfc)]*sql[ADDR(1,iql,nql)];
            unl+= swc[ADDR(2,ic,nfc)]*sql[ADDR(2,iql,nql)];

            //ll1= unl-wxdc[0][ic];
            ll1= unl-swxdc[ADDR(0,ic,nfc)];
            ll3= ll1+ al;
            ll4= ll1- al;

            fl[0]= ll1*rl;
            //fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
            //fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
            //fl[3]= fl[0]*ql[2][iql]+ wc[2][ic]*pl;
            //fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
            fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ swc[ADDR(0,ic,nfc)]*pl;
            fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ swc[ADDR(1,ic,nfc)]*pl;
            fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ swc[ADDR(2,ic,nfc)]*pl;
            fl[4]= fl[0]*hl+ swxdc[ADDR(0,ic,nfc)]*pl;
            fl[5]= fl[0]*zl;
            for( ia=6;ia<nv;ia++ )
           {
               //fl[ia]= fl[0]*ql[ia][iql];
               fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)];
           }

// fluxes from the right
            //tr=  qr[3][iqr];
            //pr=  qr[4][iqr];
            //zr=  qr[5][iqr];
            //rr=  auxr[0][iqr];
            //kr=  auxr[1][iqr];
            //ar=  auxr[2][iqr];
            //hr=  auxr[3][iqr];
            //rgr= auxr[4][iqr];
            //cvr= auxr[5][iqr]-rgr;
            tr=  sqr[ADDR(3,iqr,nqr)];
            pr=  sqr[ADDR(4,iqr,nqr)];
            zr=  sqr[ADDR(5,iqr,nqr)];
            rr=  sauxr[ADDR(0,iqr,nqr)];
            kr=  sauxr[ADDR(1,iqr,nqr)];
            ar=  sauxr[ADDR(2,iqr,nqr)];
            hr=  sauxr[ADDR(3,iqr,nqr)];
            rgr= sauxr[ADDR(4,iqr,nqr)];
            cvr= sauxr[ADDR(5,iqr,nqr)]-rgr;
            gtcomp( zr,sspr );
            for( isp=0;isp<nsp;isp++ )
           {
               ir= 1;
               if( tr > rsp[1][isp] ){ ir= 0; };

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp];
               a2= asp[ir][2][isp];
               a3= asp[ir][3][isp];
               a4= asp[ir][4][isp];
               cvspr[isp]= ( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
               cvspr[isp]-= 1.;
               cvspr[isp]*= rg1;

               a0= asp[ir][0][isp];
               a1= asp[ir][1][isp]/2;
               a2= asp[ir][2][isp]/3;
               a3= asp[ir][3][isp]/4;
               a4= asp[ir][4][isp]/5;
               a5= asp[ir][5][isp];
               e0spr[isp]= a5+ tr*( a0+ tr*( a1+ tr*( a2+ tr*( a3+ tr*a4 ) ) ) );
               e0spr[isp]-= tr;
               e0spr[isp]*= rg1;

           }
            e0r= hr-kr-rgr*tr;
            unr= 0.;
            //unr=  wc[0][ic]*qr[0][iqr]; 
            //unr+= wc[1][ic]*qr[1][iqr];
            //unr+= wc[2][ic]*qr[2][iqr];
            unr=  swc[ADDR(0,ic,nfc)]*sqr[ADDR(0,iqr,nqr)]; 
            unr+= swc[ADDR(1,ic,nfc)]*sqr[ADDR(1,iqr,nqr)];
            unr+= swc[ADDR(2,ic,nfc)]*sqr[ADDR(2,iqr,nqr)];

            //lr1= unr-wxdc[0][ic];
            lr1= unr-swxdc[ADDR(0,ic,nfc)];
            lr3= lr1+ ar;
            lr4= lr1- ar;

            fr[0]= lr1 *rr;
            //fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
            //fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
            //fr[3]= fr[0]*qr[2][iqr]+ wc[2][ic]*pr;
            //fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
            fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ swc[ADDR(0,ic,nfc)]*pr;
            fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ swc[ADDR(1,ic,nfc)]*pr;
            fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ swc[ADDR(2,ic,nfc)]*pr;
            fr[4]= fr[0]*hr+ swxdc[ADDR(0,ic,nfc)]*pr;
            fr[5]= fr[0]*zr;
            for( ia=6;ia<nv;ia++ )
           {
               //fr[ia]= fr[0]*qr[ia][iqr];
               fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)];
           }

// Roe averages
            ra= rr/rl;
            ra= sqrt(ra);
            wl= 1.+ra;
            wl= 1./wl;
            wr= 1.-wl;

            ra*= rl;
            ha= wl*hl+ wr*hr;
            ta= wl*tl+ wr*tr;
            za= wl*zl+ wr*zr;

            rga=0;
            for( isp=0;isp<nsp;isp++ )
           {
               sspa[isp]= wl*sspl[isp]+ wr*sspr[isp];
               e0spa[isp]= wl*e0spl[isp]+ wr*e0spr[isp];
               dssp[isp]= sspr[isp]-sspl[isp];
               rga+= sspa[isp];
           }
            rga*= rg1;
            if( fabs( tl-tr ) > 1.e-4*(tr+tl) )
           {
               for( isp=0;isp<nsp;isp++ )
              {
                  cvspa[isp]= (e0spr[isp]-e0spl[isp])/(tr-tl);
              }
           }
            else
           {
               for( isp=0;isp<nsp;isp++ )
              {
                  cvspa[isp]= 0.5*(cvspr[isp]+cvspl[isp]);
              }
           }
            cva= 0;
            for( isp=0;isp<nsp;isp++ )    
           {
               cva+= cvspa[isp]*sspa[isp];
           }
            gama= (cva+rga)/cva;

            etaa= 0;
            for( isp=0;isp<nsp;isp++ )
           {
               e0spa[isp]-= rg1*ta/(gama-1);
               etaa+= e0spa[isp]*dssp[isp];
           }
          
            //ua[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
            //ua[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
            //ua[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
            ua[0]= wl*sql[ADDR(0,iql,nql)]+ wr*sqr[ADDR(0,iqr,nqr)];
            ua[1]= wl*sql[ADDR(1,iql,nql)]+ wr*sqr[ADDR(1,iqr,nqr)];
            ua[2]= wl*sql[ADDR(2,iql,nql)]+ wr*sqr[ADDR(2,iqr,nqr)];

            ka=  ua[0]*ua[0];
            ka+= ua[1]*ua[1];
            ka+= ua[2]*ua[2];
            ka*= 0.5; 

            //una=  ua[0]* wc[0][ic];
            //una+= ua[1]* wc[1][ic];
            //una+= ua[2]* wc[2][ic];
            una=  ua[0]* swc[ADDR(0,ic,nfc)];
            una+= ua[1]* swc[ADDR(1,ic,nfc)];
            una+= ua[2]* swc[ADDR(2,ic,nfc)];

            epa=wl*e0l+wr*e0r;
            epa=epa-cva*ta;
            epa+=ka;
            a2a= (gama-1.)*( ha- epa );
            aa= sqrt( a2a );
            raa=ra*aa;

// eigenvalues with Harten's fix

            //la1= una-wxdc[0][ic];
            la1= una-swxdc[ADDR(0,ic,nfc)];
            la3= la1+ aa;
            la4= la1- aa;

            lmax= fabs(la1)+ aa;
            lmax= fmax( lmax, fabs(ll1)+ al );
            lmax= fmax( lmax, fabs(lr1)+ ar );

            Real eps0= eps*lmax;
            le3= fmax( fmax( eps0, la3-ll3), lr3-la3 );
            le4= fmax( fmax( eps0, la4-ll4), lr4-la4 );

            la1= fabs(la1);
            la3= fabs(la3);
            la4= fabs(la4);

            if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
            if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages
            //auxc[0][ic]= wl;
            //auxc[1][ic]= wr;
            //auxc[2][ic]= ra;
            //auxc[3][ic]= epa;
            //auxc[4][ic]= gama;
            //auxc[5][ic]= la1;
            //auxc[6][ic]= la3;
            //auxc[7][ic]= la4;
            //auxc[nauxf-1][ic]= lmax;
            sauxc[ADDR(0,ic,nfc)]= wl;
            sauxc[ADDR(1,ic,nfc)]= wr;
            sauxc[ADDR(2,ic,nfc)]= ra;
            sauxc[ADDR(3,ic,nfc)]= epa;
            sauxc[ADDR(4,ic,nfc)]= gama;
            sauxc[ADDR(5,ic,nfc)]= la1;
            sauxc[ADDR(6,ic,nfc)]= la3;
            sauxc[ADDR(7,ic,nfc)]= la4;
            sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

            dr= rr- rl;
            //du[0]= qr[0][iqr]- ql[0][iql];
            //du[1]= qr[1][iqr]- ql[1][iql];
            //du[2]= qr[2][iqr]- ql[2][iql];
            du[0]= sqr[ADDR(0,iqr,nqr)]- sql[ADDR(0,iql,nql)];
            du[1]= sqr[ADDR(1,iqr,nqr)]- sql[ADDR(1,iql,nql)];
            du[2]= sqr[ADDR(2,iqr,nqr)]- sql[ADDR(2,iql,nql)];
            dun= unr- unl;

            la3*=  0.5*ra/aa;
            la4*= -0.5*ra/aa;

            dp= pr-pl;
            dpa=dp/raa;
            dw1= la1*(dr-dp/a2a);
            dw3= la3*(dun+dpa);
            dw4= la4*(dun-dpa);

            //dw2[0]= du[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
            //dw2[1]= du[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
            //dw2[2]= du[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
            dw2[0]= du[0]- dun*swc[ADDR(0,ic,nfc)]; dw2[0]*= ra*la1;
            dw2[1]= du[1]- dun*swc[ADDR(1,ic,nfc)]; dw2[1]*= ra*la1;
            dw2[2]= du[2]- dun*swc[ADDR(2,ic,nfc)]; dw2[2]*= ra*la1;

            dw2a=  dw2[0]*ua[0];
            dw2a+= dw2[1]*ua[1];
            dw2a+= dw2[2]*ua[2];

            //ana[0]= aa*wc[0][ic];
            //ana[1]= aa*wc[1][ic];
            //ana[2]= aa*wc[2][ic];
            ana[0]= aa*swc[ADDR(0,ic,nfc)];
            ana[1]= aa*swc[ADDR(1,ic,nfc)];
            ana[2]= aa*swc[ADDR(2,ic,nfc)];

            dw5= la1*ra*(zr-zl);
            dw5a= etaa*la1*ra;

            for( ia=6;ia<nv;ia++ )
           {
               //qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
               //dw6[ia]= la1*ra*( qr[ia][iqr]- ql[ia][iql] );
               qa[ia]= wl*sql[ADDR(ia,iql,nql)]+ wr*sqr[ADDR(ia,iqr,nqr)];
               dw6[ia]= la1*ra*( sqr[ADDR(ia,iqr,nqr)]- sql[ADDR(ia,iql,nql)] );
           }
 
            unaa=aa*una;

// Roe fluxes
            fa[0]=   dw1+               dw3+                  dw4;
            fa[1]=   dw1*ua[0]+ dw2[0]+ dw3*( ua[0]+ ana[0])+ dw4*( ua[0]- ana[0] );
            fa[2]=   dw1*ua[1]+ dw2[1]+ dw3*( ua[1]+ ana[1])+ dw4*( ua[1]- ana[1] );
            fa[3]=   dw1*ua[2]+ dw2[2]+ dw3*( ua[2]+ ana[2])+ dw4*( ua[2]- ana[2] );
            fa[4]=   dw1*epa+   dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa )+          dw5a;
            fa[5]=   fa[0]*za+                                                           dw5;
            for( ia=6;ia<nv;ia++ )
           {
               fa[ia]=    fa[0]*qa[ia]+                                                               dw6[ia];
           }

// assemble 

            //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
            //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
            //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
            //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
            //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
            //f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*wc[3][ic];
            f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*swc[ADDR(3,ic,nfc)];
            f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*swc[ADDR(3,ic,nfc)];
            f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*swc[ADDR(3,ic,nfc)];
            f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*swc[ADDR(3,ic,nfc)];
            f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*swc[ADDR(3,ic,nfc)];
            f[5]= 0.5*( fr[5]+ fl[5]- fa[5] )*swc[ADDR(3,ic,nfc)];

            //rhsl[0][iql]-= f[0];
            //rhsl[1][iql]-= f[1];
            //rhsl[2][iql]-= f[2];
            //rhsl[3][iql]-= f[3];
            //rhsl[4][iql]-= f[4];
            //rhsl[5][iql]-= f[5];
            srhsl[ADDR(0,iql,nql)]-= f[0];
            srhsl[ADDR(1,iql,nql)]-= f[1];
            srhsl[ADDR(2,iql,nql)]-= f[2];
            srhsl[ADDR(3,iql,nql)]-= f[3];
            srhsl[ADDR(4,iql,nql)]-= f[4];
            srhsl[ADDR(5,iql,nql)]-= f[5];

            //rhsr[0][iqr]+= f[0];
            //rhsr[1][iqr]+= f[1];
            //rhsr[2][iqr]+= f[2];
            //rhsr[3][iqr]+= f[3];
            //rhsr[4][iqr]+= f[4];
            //rhsr[5][iqr]+= f[5];
            srhsr[ADDR(0,iqr,nqr)]+= f[0];
            srhsr[ADDR(1,iqr,nqr)]+= f[1];
            srhsr[ADDR(2,iqr,nqr)]+= f[2];
            srhsr[ADDR(3,iqr,nqr)]+= f[3];
            srhsr[ADDR(4,iqr,nqr)]+= f[4];
            srhsr[ADDR(5,iqr,nqr)]+= f[5];

            for( ia=6;ia<nv;ia++ )
           {
               //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
               //rhsl[ia][iql]-= f[ia];
               //rhsr[ia][iqr]+= f[ia];
               f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*swc[ADDR(3,ic,nfc)];
               srhsl[ADDR(ia,iql,nql)]-= f[ia];
               srhsr[ADDR(ia,iqr,nqr)]+= f[ia];
           }
      
            //auxc[nauxf-1][ic]*= wc[3][ic];
            sauxc[ADDR(nauxf-1,ic,nfc)]*= swc[ADDR(3,ic,nfc)];

        }
     }
  }

   void cMfReactingGas::mflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl0, cAu3xView<Real>& rhsl,
                                                  cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr0, cAu3xView<Real>& rhsr,
                                                  cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div, cp, D;
      Int             nql, nqr;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];
      Real            hl, hr;

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdqdxl, *srhsl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqdxr, *srhsr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sxl    = xl.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdqdxl = dqdxl0.get_data();
      srhsl  = rhsl.get_data();
      icqr   = icqr_view.get_data();
      sxr    = xr.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqdxr = dqdxr0.get_data();
      srhsr  = rhsr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr) \
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxl,dqdxr)\
         present(sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],sdqdxl[0:nv*nx*nfc],srhsl[0:nv*nfc],\
                 icqr[0:nfc],sxr[0:nx*nq],sqr[0:nv*nq],sauxr[0:naux*nq],sdqdxr[0:nv*nx*nq],srhsr[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
            wr=  wn[0]*( xn[0]- sxl[ADDR(0,iql,nql)] );
            wr+= wn[1]*( xn[1]- sxl[ADDR(1,iql,nql)] );
            wr+= wn[2]*( xn[2]- sxl[ADDR(2,iql,nql)] );

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients
            for( iv=0;iv<nv0;iv++ )
           {
               dqdxl[iv][0] = sdqdxl[ADDR(iv,0,iql,nql)];
               dqdxl[iv][1] = sdqdxl[ADDR(iv,1,iql,nql)];
               dqdxl[iv][2] = sdqdxl[ADDR(iv,2,iql,nql)];

               dqdxr[iv][0] = sdqdxr[ADDR(iv,0,iqr,nqr)];
               dqdxr[iv][1] = sdqdxr[ADDR(iv,1,iqr,nqr)];
               dqdxr[iv][2] = sdqdxr[ADDR(iv,2,iqr,nqr)];
           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqnl[iv]=  dqdxl[iv][0][iql]*wc[0][ic];
               //dqnl[iv]+= dqdxl[iv][1][iql]*wc[1][ic];
               //dqnl[iv]+= dqdxl[iv][2][iql]*wc[2][ic];
               dqnl[iv]=  dqdxl[iv][0]*wn[0];
               dqnl[iv]+= dqdxl[iv][1]*wn[1];
               dqnl[iv]+= dqdxl[iv][2]*wn[2];

               //dqnr[iv]=  dqdxr[iv][0][iqr]*wc[0][ic];
               //dqnr[iv]+= dqdxr[iv][1][iqr]*wc[1][ic];
               //dqnr[iv]+= dqdxr[iv][2][iqr]*wc[2][ic];
               dqnr[iv]=  dqdxr[iv][0]*wn[0];
               dqnr[iv]+= dqdxr[iv][1]*wn[1];
               dqnr[iv]+= dqdxr[iv][2]*wn[2];

               //dqn[iv]= ( qr[iv][iqr]-ql[iv][iql] )/w;
               //q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
               dqn[iv]= ( sqr[ADDR(iv,iqr,nqr)]-sql[ADDR(iv,iql,nql)] )/w;
               q[iv]= wl*sql[ADDR(iv,iql,nql)]+ wr*sqr[ADDR(iv,iqr,nqr)];
           }

// tangential gradients

            for( iv=0;iv<nv0;iv++ )
           {
               //dqtl= dqdxl[iv][0][iql]- wc[0][ic]*dqnl[iv];
               //dqtr= dqdxr[iv][0][iqr]- wc[0][ic]*dqnr[iv];
               dqtl= dqdxl[iv][0]- wn[0]*dqnl[iv];
               dqtr= dqdxr[iv][0]- wn[0]*dqnr[iv];
               dqt[iv][0]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdxl[iv][1][iql]- wc[1][ic]*dqnl[iv];
               //dqtr= dqdxr[iv][1][iqr]- wc[1][ic]*dqnr[iv];
               dqtl= dqdxl[iv][1]- wn[1]*dqnl[iv];
               dqtr= dqdxr[iv][1]- wn[1]*dqnr[iv];
               dqt[iv][1]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdxl[iv][2][iql]- wc[2][ic]*dqnl[iv];
               //dqtr= dqdxr[iv][2][iqr]- wc[2][ic]*dqnr[iv];
               dqtl= dqdxl[iv][2]- wn[2]*dqnl[iv];
               dqtr= dqdxr[iv][2]- wn[2]*dqnr[iv];
               dqt[iv][2]= wl*dqtl+ wr*dqtr;
           }

// reconstruct gradient
            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic]+ dqt[iv][2];
               dqdx[iv][0]= dqn[iv]*wn[0]+ dqt[iv][0];
               dqdx[iv][1]= dqn[iv]*wn[1]+ dqt[iv][1];
               dqdx[iv][2]= dqn[iv]*wn[2]+ dqt[iv][2];
           }

// stress tensor
            //mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho=   wl*auxl[     0][iql]+ wr*auxr[     0][iqr];
            mu=    wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
            rho=   wl*sauxl[ADDR(     0,iql,nql)]+ wr*sauxr[ADDR(     0,iqr,nqr)];
            cp=    wl*sauxl[ADDR(     5,iql,nql)]+ wr*sauxr[ADDR(     5,iqr,nqr)];
            div=  dqdx[0][0];
            div+= dqdx[1][1];
            div+= dqdx[2][2];
            div*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 
            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 
            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            tau[0][0]+= div;
            tau[1][1]+= div;
            tau[2][2]+= div;

// viscous fluxes

            //taun[1]=  tau[0][0]*wc[0][ic];
            //taun[1]+= tau[0][1]*wc[1][ic];
            //taun[1]+= tau[0][2]*wc[2][ic];
            taun[1]=  tau[0][0]*wn[0];
            taun[1]+= tau[0][1]*wn[1];
            taun[1]+= tau[0][2]*wn[2];

            //taun[2]=  tau[1][0]*wc[0][ic];
            //taun[2]+= tau[1][1]*wc[1][ic];
            //taun[2]+= tau[1][2]*wc[2][ic];
            taun[2]=  tau[1][0]*wn[0];
            taun[2]+= tau[1][1]*wn[1];
            taun[2]+= tau[1][2]*wn[2];

            //taun[3]=  tau[2][0]*wc[0][ic];
            //taun[3]+= tau[2][1]*wc[1][ic];
            //taun[3]+= tau[2][2]*wc[2][ic];
            taun[3]=  tau[2][0]*wn[0];
            taun[3]+= tau[2][1]*wn[1];
            taun[3]+= tau[2][2]*wn[2];


            taun[4]= -kappa*dqn[3];
            taun[4]+= taun[1]*q[0];
            taun[4]+= taun[2]*q[1];
            taun[4]+= taun[3]*q[2];

            //I did not consider the diffusion of the specices in the energy equation, need to add it here
//            hl= sauxl[ADDR(3,iql,nql)]- sauxl[ADDR(1,iql,nql)]- sql[ADDR(4,iql,nql)]/sauxl[ADDR(0,iql,nql)];
//            hr= sauxr[ADDR(3,iqr,nqr)]- sauxr[ADDR(1,iqr,nqr)]- sqr[ADDR(4,iqr,nqr)]/sauxr[ADDR(0,iqr,nqr)];
//            taun[4]= -mu*( hr-hl )/w;
//            taun[4]+= taun[1]*q[0];
//            taun[4]+= taun[2]*q[1];
//            taun[4]+= taun[3]*q[2];

            //diffusion of specifices,  assume unity lewis number
            D = kappa/(cp+small);
            for( iv=5;iv<nv0;iv++ )
           {
               //taun[iv]= -mu*( qr[iv][iqr]-ql[iv][iql] )/w;
               taun[iv]= -D*dqn[iv];
           }

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               //rhsr[iv][iqr]+= taun[iv]*wc[3][ic];
               //rhsl[iv][iql]-= taun[iv]*wc[3][ic];
               #pragma acc atomic
               srhsr[ADDR_(iv,iqr,nqr)]+= taun[iv]*wn[3];
               #pragma acc atomic
               srhsl[ADDR_(iv,iql,nql)]-= taun[iv]*wn[3];
           }
            //auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*mu/(rho*w);
            
        }
        #pragma acc exit data delete(this)
     }
  }

   void cMfReactingGas::mflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx0, cAu3xView<Real>& rhs,
                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div, cp, D;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];

      Int nq, nfc;    
      
      nfc = xc.get_dim1();
      nq  = q0.get_dim1();

      Int *sicq;
      Real *sx, *sq, *saux, *sdqdx, *srhs, *sxc, *swc, *swxdc, *sauxc;

      sicq = icq.get_data();
      sx = x.get_data();
      sq = q0.get_data();
      saux = aux.get_data();
      sdqdx = dqdx0.get_data();
      srhs = rhs.get_data();
      sxc = xc.get_data();
      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxr,dqdxl)\
         present(sicq[0:nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nv*nx*nq], srhs[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)

         for( ic=ics;ic<ice;ic++ )
        {

            //iql= icq[0][ic]; 
            //iqr= icq[1][ic]; 
            iql= sicq[ADDR(0,ic,nfc)]; 
            iqr= sicq[ADDR(1,ic,nfc)]; 

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients
            for( iv=0;iv<nv0;iv++ )
           {
               dqdxl[iv][0] =  sdqdx[ADDR(iv,0,iql,nq)];
               dqdxl[iv][1] =  sdqdx[ADDR(iv,1,iql,nq)];
               dqdxl[iv][2] =  sdqdx[ADDR(iv,2,iql,nq)];

               dqdxr[iv][0] =  sdqdx[ADDR(iv,0,iqr,nq)];
               dqdxr[iv][1] =  sdqdx[ADDR(iv,1,iqr,nq)];
               dqdxr[iv][2] =  sdqdx[ADDR(iv,2,iqr,nq)];
           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqnl[iv]=  dqdx0[iv][0][iql]*wc[0][ic];
               //dqnl[iv]+= dqdx0[iv][1][iql]*wc[1][ic];
               //dqnl[iv]+= dqdx0[iv][2][iql]*wc[2][ic];
               dqnl[iv]=  dqdxl[iv][0]*wn[0];
               dqnl[iv]+= dqdxl[iv][1]*wn[1];
               dqnl[iv]+= dqdxl[iv][2]*wn[2];

               //dqnr[iv]=  dqdx0[iv][0][iqr]*wc[0][ic];
               //dqnr[iv]+= dqdx0[iv][1][iqr]*wc[1][ic];
               //dqnr[iv]+= dqdx0[iv][2][iqr]*wc[2][ic];
               dqnr[iv]=  dqdxr[iv][0]*wn[0];
               dqnr[iv]+= dqdxr[iv][1]*wn[1];
               dqnr[iv]+= dqdxr[iv][2]*wn[2];

               //dqn[iv]= ( q0[iv][iqr]-q0[iv][iql] )/w;
               //q[iv]= wl*q0[iv][iql]+ wr*q0[iv][iqr];
               dqn[iv]= ( sq[ADDR(iv,iqr,nq)]-sq[ADDR(iv,iql,nq)] )/w;
               q[iv]= wl*sq[ADDR(iv,iql,nq)]+ wr*sq[ADDR(iv,iqr,nq)];
           }

// tangential gradients

            for( iv=0;iv<nv0;iv++ )
           {
               //dqtl= dqdx0[iv][0][iql]- wc[0][ic]*dqnl[iv];
               //dqtr= dqdx0[iv][0][iqr]- wc[0][ic]*dqnr[iv];
               dqtl= dqdxl[iv][0]- wn[0]*dqnl[iv];
               dqtr= dqdxr[iv][0]- wn[0]*dqnr[iv];
               dqt[iv][0]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdx0[iv][1][iql]- wc[1][ic]*dqnl[iv];
               //dqtr= dqdx0[iv][1][iqr]- wc[1][ic]*dqnr[iv];
               dqtl= dqdxl[iv][1]- wn[1]*dqnl[iv];
               dqtr= dqdxr[iv][1]- wn[1]*dqnr[iv];
               dqt[iv][1]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdx0[iv][2][iql]- wc[2][ic]*dqnl[iv];
               //dqtr= dqdx0[iv][2][iqr]- wc[2][ic]*dqnr[iv];
               dqtl= dqdxl[iv][2]- wn[2]*dqnl[iv];
               dqtr= dqdxr[iv][2]- wn[2]*dqnr[iv];
               dqt[iv][2]= wl*dqtl+ wr*dqtr;
           }

// reconstruct gradient
            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic]+ dqt[iv][2];
               dqdx[iv][0]= dqn[iv]*wn[0]+ dqt[iv][0];
               dqdx[iv][1]= dqn[iv]*wn[1]+ dqt[iv][1];
               dqdx[iv][2]= dqn[iv]*wn[2]+ dqt[iv][2];
           }

// stress tensor
            //mu=    wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
            //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
            //rho=   wl*aux[     0][iql]+ wr*aux[     0][iqr];
            mu=    wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho=   wl*saux[ADDR(     0,iql,nq)]+ wr*saux[ADDR(     0,iqr,nq)];
            cp =   wl*saux[ADDR(     5,iql,nq)]+ wr*saux[ADDR(     5,iqr,nq)];
            div=  dqdx[0][0];
            div+= dqdx[1][1];
            div+= dqdx[2][2];
            div*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 
            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 
            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            tau[0][0]+= div;
            tau[1][1]+= div;
            tau[2][2]+= div;

// viscous fluxes

            //taun[1]=  tau[0][0]*wc[0][ic];
            //taun[1]+= tau[0][1]*wc[1][ic];
            //taun[1]+= tau[0][2]*wc[2][ic];
            taun[1]=  tau[0][0]*wn[0];
            taun[1]+= tau[0][1]*wn[1];
            taun[1]+= tau[0][2]*wn[2];

            //taun[2]=  tau[1][0]*wc[0][ic];
            //taun[2]+= tau[1][1]*wc[1][ic];
            //taun[2]+= tau[1][2]*wc[2][ic];
            taun[2]=  tau[1][0]*wn[0];
            taun[2]+= tau[1][1]*wn[1];
            taun[2]+= tau[1][2]*wn[2];

            //taun[3]=  tau[2][0]*wc[0][ic];
            //taun[3]+= tau[2][1]*wc[1][ic];
            //taun[3]+= tau[2][2]*wc[2][ic];
            taun[3]=  tau[2][0]*wn[0];
            taun[3]+= tau[2][1]*wn[1];
            taun[3]+= tau[2][2]*wn[2];


            taun[4]= -kappa*dqn[3];
            taun[4]+= taun[1]*q[0];
            taun[4]+= taun[2]*q[1];
            taun[4]+= taun[3]*q[2];

            //diffusion of specifices, assume unity lewis number
            D = kappa/(cp+small);
            for( iv=5;iv<nv0;iv++ )
           {
               //taun[iv]= -mu*( qr[iv][iqr]-ql[iv][iql] )/w;
               taun[iv]= -D*dqn[iv];
           }

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               //rhs[iv][iqr]+= taun[iv]*wc[3][ic];
               //rhs[iv][iql]-= taun[iv]*wc[3][ic];
               #pragma acc atomic
               srhs[ADDR_(iv,iqr,nq)]+= taun[iv]*wn[3];
               #pragma acc atomic
               srhs[ADDR_(iv,iql,nq)]-= taun[iv]*wn[3];
           }
            //auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*mu/(rho*w);
            
        }
        #pragma acc exit data delete(this)
     }
  }

   void cMfReactingGas::dmflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                   cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                   cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv,cp,D;
      Int             nql, nqr;

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sxl    = xl.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdql   = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl  = resl.get_data();
      icqr   = icqr_view.get_data();
      sxr    = xr.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqr   = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr  = resr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      if( ice > ics )
     { 
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr)\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun,wn,xn)\
         present (sxl[0:nx*nql],sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
                  icqr[0:nfc],sxr[0:nx*nqr],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
                  sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
            wr=  wn[0]*( xn[0]- sxl[ADDR(0,iql,nql)] );
            wr+= wn[1]*( xn[1]- sxl[ADDR(1,iql,nql)] );
            wr+= wn[2]*( xn[2]- sxl[ADDR(2,iql,nql)] );

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv0;iv++ )
           {
               //ddqn[iv]= ( dauxr[iv][iqr]- dauxl[iv][iql] )/w;
               //dqn[iv]=  ( qr[iv][iqr]-       ql[iv][iql] )/w;
               ddqn[iv]= ( sdauxr[ADDR(iv,iqr,nqr)]- sdauxl[ADDR(iv,iql,nql)] )/w;
               dqn[iv]=  (    sqr[ADDR(iv,iqr,nqr)]-    sql[ADDR(iv,iql,nql)] )/w;

               //q[iv]=  wl*   ql[iv][iql]+ wr*   qr[iv][iqr];
               //dq[iv]= wl*dauxl[iv][iql]+ wr*dauxr[iv][iqr];
               q[iv]=  wl*   sql[ADDR(iv,iql,nql)]+ wr*   sqr[ADDR(iv,iqr,nqr)];
               dq[iv]= wl*sdauxl[ADDR(iv,iql,nql)]+ wr*sdauxr[ADDR(iv,iqr,nqr)];

           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic];
               dqdx[iv][0]= dqn[iv]*wn[0];
               dqdx[iv][1]= dqn[iv]*wn[1];
               dqdx[iv][2]= dqn[iv]*wn[2];

               //ddqdx[iv][0]= ddqn[iv]*wc[0][ic];
               //ddqdx[iv][1]= ddqn[iv]*wc[1][ic];
               //ddqdx[iv][2]= ddqn[iv]*wc[2][ic];
               ddqdx[iv][0]= ddqn[iv]*wn[0];
               ddqdx[iv][1]= ddqn[iv]*wn[1];
               ddqdx[iv][2]= ddqn[iv]*wn[2];
           }

// stress tensor

            //mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho=   wl*auxl[0][iql]+      wr*auxr[0][iqr];
            mu=    wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
            rho=   wl*sauxl[ADDR(0,iql,nql)]+      wr*sauxr[ADDR(0,iqr,nqr)];
            cp=    wl*sauxl[ADDR(5,iql,nql)]+      wr*sauxr[ADDR(5,iqr,nqr)];

            div =  dqdx[0][0];
            div+=  dqdx[1][1];
            div+=  dqdx[2][2];
            div*= 2./3.*mu;

            ddiv =  ddqdx[0][0];
            ddiv+=  ddqdx[1][1];
            ddiv+=  ddqdx[2][2];
            ddiv*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 

            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 

            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
            dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 
            dtau[2][0]= -mu*( ddqdx[0][2]+ ddqdx[2][0] ); 

            dtau[0][1]=  dtau[1][0];
            dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 
            dtau[2][1]= -mu*( ddqdx[1][2]+ ddqdx[2][1] ); 

            dtau[0][2]=  dtau[2][0];
            dtau[1][2]=  dtau[2][1];
            dtau[2][2]= -mu*( ddqdx[2][2]+ ddqdx[2][2] ); 

            tau[0][0]+=  div;
            tau[1][1]+=  div;
            tau[2][2]+=  div;

            dtau[0][0]+= ddiv;
            dtau[1][1]+= ddiv;
            dtau[2][2]+= ddiv;

// viscous flux

            //taun[1]=    tau[0][0]*wc[0][ic];
            //taun[1]+=   tau[0][1]*wc[1][ic];
            //taun[1]+=   tau[0][2]*wc[2][ic];
            taun[1]=    tau[0][0]*wn[0];
            taun[1]+=   tau[0][1]*wn[1];
            taun[1]+=   tau[0][2]*wn[2];

            //taun[2]=    tau[1][0]*wc[0][ic];
            //taun[2]+=   tau[1][1]*wc[1][ic];
            //taun[2]+=   tau[1][2]*wc[2][ic];
            taun[2]=    tau[1][0]*wn[0];
            taun[2]+=   tau[1][1]*wn[1];
            taun[2]+=   tau[1][2]*wn[2];

            //taun[3]=    tau[2][0]*wc[0][ic];
            //taun[3]+=   tau[2][1]*wc[1][ic];
            //taun[3]+=   tau[2][2]*wc[2][ic];
            taun[3]=    tau[2][0]*wn[0];
            taun[3]+=   tau[2][1]*wn[1];
            taun[3]+=   tau[2][2]*wn[2];

            //dtaun[1]=   dtau[0][0]*wc[0][ic];
            //dtaun[1]+=  dtau[0][1]*wc[1][ic];
            //dtaun[1]+=  dtau[0][2]*wc[2][ic];
            dtaun[1]=   dtau[0][0]*wn[0];
            dtaun[1]+=  dtau[0][1]*wn[1];
            dtaun[1]+=  dtau[0][2]*wn[2];

            //dtaun[2]=   dtau[1][0]*wc[0][ic];
            //dtaun[2]+=  dtau[1][1]*wc[1][ic];
            //dtaun[2]+=  dtau[1][2]*wc[2][ic];
            dtaun[2]=   dtau[1][0]*wn[0];
            dtaun[2]+=  dtau[1][1]*wn[1];
            dtaun[2]+=  dtau[1][2]*wn[2];

            //dtaun[3]=   dtau[2][0]*wc[0][ic];
            //dtaun[3]+=  dtau[2][1]*wc[1][ic];
            //dtaun[3]+=  dtau[2][2]*wc[2][ic];
            dtaun[3]=   dtau[2][0]*wn[0];
            dtaun[3]+=  dtau[2][1]*wn[1];
            dtaun[3]+=  dtau[2][2]*wn[2];

            dtaun[4]= -kappa*ddqn[3];
            dtaun[4]+= taun[1]*dq[0]+ dtaun[1]*q[0];
            dtaun[4]+= taun[2]*dq[1]+ dtaun[2]*q[1];
            dtaun[4]+= taun[3]*dq[2]+ dtaun[3]*q[2];

            //diffusion of specifices, assume lewis number
            D = kappa/(cp+small);
            for( iv=5;iv<nv0;iv++ )
           {
               //taun[iv]= -mu*( qr[iv][iqr]-ql[iv][iql] )/w;
               dtaun[iv]= -D*ddqn[iv];
           }
           
// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               //resr[iv][iqr]+= dtaun[iv]*wc[3][ic];
               //resl[iv][iql]-= dtaun[iv]*wc[3][ic];
               #pragma acc atomic
               sresr[ADDR_(iv,iqr,nqr)]+= dtaun[iv]*wn[3];
               #pragma acc atomic
               sresl[ADDR_(iv,iql,nql)]-= dtaun[iv]*wn[3];
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cMfReactingGas::dmflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                                 cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv, cp, D;
      Int             nql, nqr;

      Int nfc, nq;    
      Int *sicq;
      Real *sx, *sq, *saux, *sdq, *sdaux, *sres, *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = q0.get_dim1();
        
      sicq  = icq.get_data();
      sx    = x.get_data();
      sq    = q0.get_data();
      saux  = aux.get_data();
      sdq   = dq0.get_data();
      sdaux = daux.get_data();
      sres  = res.get_data();
      sxc   = xc.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     { 
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr)\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun,wn,xn)\
         present (sxl[0:nx*nql],sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
                  icqr[0:nfc],sxr[0:nx*nqr],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
                  sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv0;iv++ )
           {
               //ddqn[iv]= ( dauxr[iv][iqr]- dauxl[iv][iql] )/w;
               //dqn[iv]=  ( qr[iv][iqr]-       ql[iv][iql] )/w;
               ddqn[iv]= ( sdaux[ADDR(iv,iqr,nq)]- sdaux[ADDR(iv,iql,nq)] )/w;
               dqn[iv]=  (    sq[ADDR(iv,iqr,nq)]-    sq[ADDR(iv,iql,nq)] )/w;

               //q[iv]=  wl*   ql[iv][iql]+ wr*   qr[iv][iqr];
               //dq[iv]= wl*dauxl[iv][iql]+ wr*dauxr[iv][iqr];
               q[iv]=  wl*   sq[ADDR(iv,iql,nq)]+ wr*   sq[ADDR(iv,iqr,nq)];
               dq[iv]= wl*sdaux[ADDR(iv,iql,nq)]+ wr*sdaux[ADDR(iv,iqr,nq)];

           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic];
               dqdx[iv][0]= dqn[iv]*wn[0];
               dqdx[iv][1]= dqn[iv]*wn[1];
               dqdx[iv][2]= dqn[iv]*wn[2];

               //ddqdx[iv][0]= ddqn[iv]*wc[0][ic];
               //ddqdx[iv][1]= ddqn[iv]*wc[1][ic];
               //ddqdx[iv][2]= ddqn[iv]*wc[2][ic];
               ddqdx[iv][0]= ddqn[iv]*wn[0];
               ddqdx[iv][1]= ddqn[iv]*wn[1];
               ddqdx[iv][2]= ddqn[iv]*wn[2];
           }

// stress tensor

            //mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho=   wl*auxl[0][iql]+      wr*auxr[0][iqr];
            mu=    wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho=   wl*saux[ADDR(0,iql,nq)]+      wr*saux[ADDR(0,iqr,nq)];
            cp=    wl*saux[ADDR(5,iql,nq)]+      wr*saux[ADDR(5,iqr,nq)];

            div =  dqdx[0][0];
            div+=  dqdx[1][1];
            div+=  dqdx[2][2];
            div*= 2./3.*mu;

            ddiv =  ddqdx[0][0];
            ddiv+=  ddqdx[1][1];
            ddiv+=  ddqdx[2][2];
            ddiv*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 

            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 

            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
            dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 
            dtau[2][0]= -mu*( ddqdx[0][2]+ ddqdx[2][0] ); 

            dtau[0][1]=  dtau[1][0];
            dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 
            dtau[2][1]= -mu*( ddqdx[1][2]+ ddqdx[2][1] ); 

            dtau[0][2]=  dtau[2][0];
            dtau[1][2]=  dtau[2][1];
            dtau[2][2]= -mu*( ddqdx[2][2]+ ddqdx[2][2] ); 

            tau[0][0]+=  div;
            tau[1][1]+=  div;
            tau[2][2]+=  div;

            dtau[0][0]+= ddiv;
            dtau[1][1]+= ddiv;
            dtau[2][2]+= ddiv;

// viscous flux

            //taun[1]=    tau[0][0]*wc[0][ic];
            //taun[1]+=   tau[0][1]*wc[1][ic];
            //taun[1]+=   tau[0][2]*wc[2][ic];
            taun[1]=    tau[0][0]*wn[0];
            taun[1]+=   tau[0][1]*wn[1];
            taun[1]+=   tau[0][2]*wn[2];

            //taun[2]=    tau[1][0]*wc[0][ic];
            //taun[2]+=   tau[1][1]*wc[1][ic];
            //taun[2]+=   tau[1][2]*wc[2][ic];
            taun[2]=    tau[1][0]*wn[0];
            taun[2]+=   tau[1][1]*wn[1];
            taun[2]+=   tau[1][2]*wn[2];

            //taun[3]=    tau[2][0]*wc[0][ic];
            //taun[3]+=   tau[2][1]*wc[1][ic];
            //taun[3]+=   tau[2][2]*wc[2][ic];
            taun[3]=    tau[2][0]*wn[0];
            taun[3]+=   tau[2][1]*wn[1];
            taun[3]+=   tau[2][2]*wn[2];

            //dtaun[1]=   dtau[0][0]*wc[0][ic];
            //dtaun[1]+=  dtau[0][1]*wc[1][ic];
            //dtaun[1]+=  dtau[0][2]*wc[2][ic];
            dtaun[1]=   dtau[0][0]*wn[0];
            dtaun[1]+=  dtau[0][1]*wn[1];
            dtaun[1]+=  dtau[0][2]*wn[2];

            //dtaun[2]=   dtau[1][0]*wc[0][ic];
            //dtaun[2]+=  dtau[1][1]*wc[1][ic];
            //dtaun[2]+=  dtau[1][2]*wc[2][ic];
            dtaun[2]=   dtau[1][0]*wn[0];
            dtaun[2]+=  dtau[1][1]*wn[1];
            dtaun[2]+=  dtau[1][2]*wn[2];

            //dtaun[3]=   dtau[2][0]*wc[0][ic];
            //dtaun[3]+=  dtau[2][1]*wc[1][ic];
            //dtaun[3]+=  dtau[2][2]*wc[2][ic];
            dtaun[3]=   dtau[2][0]*wn[0];
            dtaun[3]+=  dtau[2][1]*wn[1];
            dtaun[3]+=  dtau[2][2]*wn[2];

            dtaun[4]= -kappa*ddqn[3];
            dtaun[4]+= taun[1]*dq[0]+ dtaun[1]*q[0];
            dtaun[4]+= taun[2]*dq[1]+ dtaun[2]*q[1];
            dtaun[4]+= taun[3]*dq[2]+ dtaun[3]*q[2];

            //diffusion of specifices, assume unity lewis number
            D = kappa/(cp+small);
            for( iv=5;iv<nv0;iv++ )
           {
               //taun[iv]= -mu*( qr[iv][iqr]-ql[iv][iql] )/w;
               dtaun[iv]= -D*ddqn[iv];
           }
           
// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               //resr[iv][iqr]+= dtaun[iv]*wc[3][ic];
               //resl[iv][iql]-= dtaun[iv]*wc[3][ic];
               #pragma acc atomic
               sres[ADDR_(iv,iqr,nq)]+= dtaun[iv]*wn[3];
               #pragma acc atomic
               sres[ADDR_(iv,iql,nq)]-= dtaun[iv]*wn[3];
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cMfReactingGas::qupd( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux )
  {
      Int iv,iq;

      Int nq;
      Real *sq, *saux, *sdq, *sdaux;

      nq    = q.get_dim1();

      sq    = q.get_data();
      saux  = aux.get_data();
      sdq   = dq.get_data();
      sdaux = daux.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present (sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv;iv++ )
           {
               sq[ADDR(iv,iq,nq)]+= sdaux[ADDR(iv,iq,nq)];
           }

            //cap on temperature, it should not go beyond JANAF range
            iv=3;
            sq[ADDR(iv,iq,nq)] = fmin( sq[ADDR(iv,iq,nq)], 5000);

            //constrain on z, [0:1]
            iv = 5;
            sq[ADDR(iv,iq,nq)] = fmin( sq[ADDR(iv,iq,nq)], 1.0);
            sq[ADDR(iv,iq,nq)] = fmax( sq[ADDR(iv,iq,nq)], 0.0);
        }
        #pragma acc exit data copyout(this)
     }
  }
