using namespace std;

# include <field/gas.h>

   cMfAusmGas::cMfAusmGas( cCosystem *Coo, cVisc *visc )
  {
      alpha0= 3./16.;
      beta0= 1./8.;

      coo= Coo;
      vsc= visc;
      nx=  coo->getnx();
      nvel=coo->getnvel(); 

      nvk=3;
      nv=2+nvel;
      naux=7;
      nauxf=2;
      nlhs= 2;

      nv0= nv;
      naux0= naux;
      nauxf0= nauxf;
      nlhs0= nlhs;

      vsc->setvrs( nx,nvel, &nv,&naux,&nauxf,&nlhs );
      ilv[0]=nvel;
      ilv[1]=ilv[0]+1;
      ilv[2]=ilv[1]+1;
      unit[0]= 100.;
      unit[1]=   1.;
      unit[2]= unit[0]*unit[0];
      deflt[0]=   0.;
      deflt[1]= 298.;
      deflt[2]= 100000./unit[2];
      rg= 287/unit[2];
      rg= 287;
      gam=1.4;

  }


   void cMfAusmGas::ilhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                            cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                            cAu3xView<Real>& wc,  cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             nql,nqr,iql,iqr,ic;

      Int nfc, nq;
      Int *sicql;
      Real *sql, *sauxl, *slhsl;
      Int *sicqr;
      Real *sqr, *sauxr, *slhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      sicql = icql.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      slhsl = lhsl.get_data();
      sicqr = icqr.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      slhsr = lhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(             sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
                 sicqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            iqr= sicqr[ADDR(0,ic,nfc)];

            //lhsl[0][iql]+= auxc[nauxf-1][ic];
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhsl[ADDR_(0,iql,nql)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhsr[ADDR_(0,iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
      vsc->ilhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc ) ;
  }

   void cMfAusmGas::ilhs( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& lhs,
                                           cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             iql,iqr,ic;

      Int nfc, nq;
      Int *sicq;
      Real *sq, *saux, *slhs, *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = q.get_dim1();

      sicq  = icq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      slhs  = lhs.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {

        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],slhs[0:nlhs*nq], \
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this )\
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {

            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];

            //lhsl[0][iql]+= auxc[nauxf-1][ic];
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhs[ADDR_(0,iql,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhs[ADDR_(0,iqr,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
      //vsc->ilhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc ) ;
      vsc->ilhs( ics, ice, icq, q, aux, lhs, wc, wxdc, auxc ) ;
  }

   void cMfAusmGas::wlhs( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                            cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            gam1,kr;
      Real            dp1,dp2,dp3,dp4,dp5;
      Real            dfr[MxNVs][MxNVs];

      Int             ic,ia,iql,iqr,ja;
      Int             nql,nqr;

      Int nfc, nq;
      Int *icql;
      Real *sql, *sauxl, *slhsl;
      Int *icqr;
      Real *sqr, *sauxr, *slhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      slhsl = lhsl.get_data();
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      slhsr = lhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(            sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
                 icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic];

            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhsr[ADDR_(0,iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
      //vsc->wlhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc );
      vsc->wlhs( ics, ice, icql_view, ql, auxl, lhsl, icqr_view, qr, auxr, lhsr, wc, wxdc, auxc );
  }

   void cMfAusmGas::slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa )
  {
      //vsc->slhs( iqs,iqe, cfl, q,aux, dqdx,dst,wq,lhs );
      vsc->slhs( iqs,iqe, cfl, q,aux, dqdx,dst,wq,lhsa );
  }

   void cMfAusmGas::vlhs( Int iqs, Int iqe, Real dtm,Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                          cAu3xView<Real>& wq, cAu3xView<Real>& lhs, Real rord )
  {
      Int             ia,ja,iq;
      Real            w,tau;

      Int nq;
      Real *sq, *saux, *sdqdx, *sdst, *swq, *slhs;

      nq    = q.get_dim1();

      sq    = q.get_data();
      saux  = aux.get_data();
      sdqdx = dqdx.get_data();
      sdst  = dst.get_data();
      swq   = wq.get_data();
      slhs  = lhs.get_data();

      if( iqe > iqs )
     {
         w= 1./cfl;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //lhs[nlhs-1][iq]=  lhs[0][iq]*w;
            //if(rord>0) lhs[nlhs-1][iq]+= rord*wq[0][iq]/dtm;
            //lhs[0][iq]+=     lhs[nlhs-1][iq];
            slhs[ADDR(nlhs-1,iq,nq)]=  slhs[ADDR(0,iq,nq)]*w;
            if(rord>0) slhs[ADDR(nlhs-1,iq,nq)]+= rord*swq[ADDR(0,iq,nq)]/dtm;
            slhs[ADDR(0,iq,nq)]+=     slhs[ADDR(nlhs-1,iq,nq)];

        }
        #pragma acc exit data delete(this)
     }
      vsc->vlhs( iqs,iqe, cfl, wq,lhs );
  }

   void cMfAusmGas::invdg( Int iqs, Int iqe, cAu3xView<Real>& lhs, cAu3xView<Real>& res )
  {
      Int iv,iq;

      Int nq;
      Real *slhs, *sres;

      nq = lhs.get_dim1();
      slhs = lhs.get_data();
      sres = res.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present(sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv0;iv++ )
           {
               sres[ADDR(iv,iq,nq)]/= slhs[ADDR(0,iq,nq)];
           }
        }
        #pragma acc exit data copyout(this)
     }
      //vsc->invdg( iqs,iqe, lhs,res );
      vsc->invdg( iqs,iqe, lhs,res );
  }


   void cMfAusmGas::iflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      assert(0);
  }

   void cMfAusmGas::dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view )
  {
      Int iq,ia;
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;
      Int nq;
      Real *q, *aux, *dU, *dq;

      nq = q_view.get_dim1();

      q = q_view.get_data();
      aux = aux_view.get_data();
      dU = dU_view.get_data();
      dq = dq_view.get_data();

      cv= rg/(gam-1.);

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop \
      present(q[0:nv*nq],aux[0:naux*nq],dU[0:nv*nq],dq[0:nv*nq],this) \
      default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         //t=  q[3][iq];
         //p=  q[4][iq];
         //ro= aux[0][iq];
         //h=  aux[3][iq];
         t=  q[ADDR(3,iq,nq)];
         p=  q[ADDR(4,iq,nq)];
         ro= aux[ADDR(0,iq,nq)];
         h=  aux[ADDR(3,iq,nq)];
         re= ro*h- p;
         e= re/ro;
         //dro= dU[0][iq];
         //dre= dU[4][iq];
         dro= dU[ADDR(0,iq,nq)];
         dre= dU[ADDR(4,iq,nq)];

         //dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
         //dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
         //dq[2][iq]= ( dU[3][iq]- q[2][iq]*dro )/ro;
         dq[ADDR(0,iq,nq)]= ( dU[ADDR(1,iq,nq)]- q[ADDR(0,iq,nq)]*dro )/ro;
         dq[ADDR(1,iq,nq)]= ( dU[ADDR(2,iq,nq)]- q[ADDR(1,iq,nq)]*dro )/ro;
         dq[ADDR(2,iq,nq)]= ( dU[ADDR(3,iq,nq)]- q[ADDR(2,iq,nq)]*dro )/ro;

         //dk=  q[0][iq]*dq[0][iq];
         //dk+= q[1][iq]*dq[1][iq];
         //dk+= q[2][iq]*dq[2][iq];
         dk=  q[ADDR(0,iq,nq)]*dq[ADDR(0,iq,nq)];
         dk+= q[ADDR(1,iq,nq)]*dq[ADDR(1,iq,nq)];
         dk+= q[ADDR(2,iq,nq)]*dq[ADDR(2,iq,nq)];

         dk*= ro;
         dt= ( dre- e*dro- dk )/( ro*cv );
         dp= p*( dro/ro+ dt/t );

         //dq[3][iq]= dt;
         //dq[4][iq]= dp;
         dq[ADDR(3,iq,nq)]= dt;
         dq[ADDR(4,iq,nq)]= dp;
     }
     #pragma acc exit data delete(this)
  }

   void cMfAusmGas::auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch )
  {
      Int iq;
      Int iv;
      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);
      Real cp=rg*gam/(gam-1);

      if(arch=="d")
     {
         Int nq;
         Real *q, *aux;

         nq = q_view.get_dim1();

         q   = q_view.get_data();
         aux = aux_view.get_data();

         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          present(q[0:nv*nq],aux[0:naux*nq],this) \
          default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
//    density
            aux[ADDR(0,iq,nq)]= q[ADDR(4,iq,nq)]/( rg*q[ADDR(3,iq,nq)] );
//    kinetic energy
            aux[ADDR(1,iq,nq)]=  q[ADDR(0,iq,nq)]*q[ADDR(0,iq,nq)];
            aux[ADDR(1,iq,nq)]+= q[ADDR(1,iq,nq)]*q[ADDR(1,iq,nq)];
            aux[ADDR(1,iq,nq)]+= q[ADDR(2,iq,nq)]*q[ADDR(2,iq,nq)];
            aux[ADDR(1,iq,nq)]*= 0.5;
//    speed of sound and total entalpy
            aux[ADDR(2,iq,nq)]= gam*rg* q[ADDR(3,iq,nq)];
            aux[ADDR(3,iq,nq)]= aux[ADDR(2,iq,nq)]/(gam-1)+ aux[ADDR(1,iq,nq)];
            aux[ADDR(2,iq,nq)]= sqrt( aux[ADDR(2,iq,nq)] );
            aux[ADDR(4,iq,nq)]= cp;
            aux[ADDR(5,iq,nq)]= mu;
            aux[ADDR(6,iq,nq)]= kappa;
        }
         #pragma acc exit data delete(this)
     }
      else if(arch=="h")
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
//    density
            aux_view(0,iq)= q_view(4,iq)/( rg*q_view(3,iq) );
//    kinetic energy
            aux_view(1,iq)=  q_view(0,iq)*q_view(0,iq);
            aux_view(1,iq)+= q_view(1,iq)*q_view(1,iq);
            aux_view(1,iq)+= q_view(2,iq)*q_view(2,iq);
            aux_view(1,iq)*= 0.5;
//    speed of sound and total entalpy
            aux_view(2,iq)= gam*rg* q_view(3,iq);
            aux_view(3,iq)= aux_view(2,iq)/(gam-1)+ aux_view(1,iq);
            aux_view(2,iq)= sqrt( aux_view(2,iq) );
            aux_view(4,iq)= cp;
            aux_view(5,iq)= mu;
            aux_view(6,iq)= kappa;
        }
     }
      else
     {
         cout << "unkown arch\n";
         assert(0);
     }
  }

   void cMfAusmGas::cnsv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch )
  {
      Int iq,ia;
      Int nq;
      Real *sq, *saux, *sqo;

      if(arch == "d")
     {
         nq = q.get_dim1();

         sq   = q.get_data();
         saux = aux.get_data();
         sqo  = qo.get_data();

         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          present(sq[0:nv*nq],saux[0:naux*nq],sqo[0:nv*nq],this) \
          default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            // density
            sqo[ADDR(0,iq,nq)]= saux[ADDR(0,iq,nq)];
            sqo[ADDR(1,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(0,iq,nq)];
            sqo[ADDR(2,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(1,iq,nq)];
            sqo[ADDR(3,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(2,iq,nq)];
            sqo[ADDR(4,iq,nq)]= sqo[ADDR(0,iq,nq)]*saux[ADDR(3,iq,nq)]- sq[ADDR(4,iq,nq)];
            for( ia=5;ia<nv;ia++ )
           {
               //qo[ia][iq]= qo[0][iq]*q[ia][iq];
               sqo[ADDR(ia,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(ia,iq,nq)];
           }
        }
         #pragma acc exit data delete(this)
     }
      else if(arch == "h")
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            // density
            qo(0,iq)= aux(0,iq);
            qo(1,iq)= qo(0,iq)*q(0,iq);
            qo(2,iq)= qo(0,iq)*q(1,iq);
            qo(3,iq)= qo(0,iq)*q(2,iq);
            qo(4,iq)= qo(0,iq)*aux(3,iq)- q(4,iq);
            for( ia=5;ia<nv;ia++ )
           {
               //qo[ia][iq]= qo[0][iq]*q[ia][iq];
               qo(ia,iq)= qo(0,iq)*q(ia,iq);
           }
        }
     }
      else
     {
         assert(0);
     }
  }

   void cMfAusmGas::wflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,  
                                            cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr, 
                                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real f[MxNVs],wn[4];
      Int  ic,iql,iqr,nql,nqr;

      Int nfc, nq;    
      Int *icql;      
      Real *sql, *sauxl, *srhsl;
      Int *icqr;
      Real *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq = qr.get_dim1();
  
      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data(); 
      sauxr = auxr.get_data(); 
      srhsr = rhsr.get_data();
      swc   = wc.get_data(); 
      swxdc = wxdc.get_data(); 
      sauxc = auxc.get_data();

      nql = nfc;
      nqr = nq;
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop \
       firstprivate(nql,nqr) \
       private(f,wn)\
       present(            sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left
         //f[1]=    wc[0][ic]* qr[4][iqr]* wc[3][ic];
         //f[2]=    wc[1][ic]* qr[4][iqr]* wc[3][ic];
         //f[3]=    wc[2][ic]* qr[4][iqr]* wc[3][ic];
         //f[4]=  wxdc[0][ic]* qr[4][iqr]* wc[3][ic];
         f[1]=    wn[0]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[2]=    wn[1]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[3]=    wn[2]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[4]=  swxdc[ADDR(0,ic,nfc)]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         //auxc[nauxf-1][ic]= ( auxr[2][iqr]+fabs(wxdc[0][ic]) )*wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]= ( sauxr[ADDR(2,iqr,nqr)]+fabs(swxdc[ADDR(0,ic,nfc)]) )*wn[3];

         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nql)]-= f[4];

         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nqr)]+= f[4];
     }
  }

   void cMfAusmGas::dwflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                              cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            dpr;

      Int             ia,ic,iql,iqr;
      Real            f[MxNVs],wn[4];
      Int             nfc,nqr;

      Int nbb, nq;
      Int *icql; 
      Real *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr; 
      Real *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *swc, *swxdc, *sauxc;

      nbb = wc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdql   = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl  = resl.get_data();
      icqr   = icqr_view.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqr   = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr  = resr.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      nfc = nbb;
      nqr = nq;
     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      private(f,wn)\
      present(sql[0:nv*nfc],sauxl[0:naux*nfc],sdql[0:nv*nfc],sdauxl[0:nv*nfc],sresl[0:nv*nfc],\
              icqr[0:nfc],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
              swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic];

         wn[0] = swc[ADDR(0,ic,nfc)];
         wn[1] = swc[ADDR(1,ic,nfc)];
         wn[2] = swc[ADDR(2,ic,nfc)];
         wn[3] = swc[ADDR(3,ic,nfc)];
         //dpr=  dauxr[4][iqr];
         //f[1]=   wc[0][ic]*dpr*wc[3][ic];
         //f[2]=   wc[1][ic]*dpr*wc[3][ic];
         //f[3]=   wc[2][ic]*dpr*wc[3][ic];
         //f[4]= wxdc[0][ic]*dpr*wc[3][ic];
         dpr=  sdauxr[ADDR(4,iqr,nqr)];
         f[1]=   wn[0]*dpr*wn[3];
         f[2]=   wn[1]*dpr*wn[3];
         f[3]=   wn[2]*dpr*wn[3];
         f[4]= swxdc[ADDR(0,ic,nfc)]*dpr*wn[3];

         //resl[1][iql]-= f[1];
         //resl[2][iql]-= f[2];
         //resl[3][iql]-= f[3];
         //resl[4][iql]-= f[4];
         #pragma acc atomic
         sresl[ADDR_(1,iql,nfc)]-= f[1];
         #pragma acc atomic
         sresl[ADDR_(2,iql,nfc)]-= f[2];
         #pragma acc atomic
         sresl[ADDR_(3,iql,nfc)]-= f[3];
         #pragma acc atomic
         sresl[ADDR_(4,iql,nfc)]-= f[4];

         //resr[1][iqr]+= f[1];
         //resr[2][iqr]+= f[2];
         //resr[3][iqr]+= f[3];
         //resr[4][iqr]+= f[4];
         #pragma acc atomic
         sresr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         sresr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         sresr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         sresr[ADDR_(4,iqr,nqr)]+= f[4];

     }
     #pragma acc exit data copyout(this)
  }

   void cMfAusmGas::diflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Int             ia,ic,iql,iqr;
      Real            al,unl,rl,pl,hl,dfl[MxNVs],als,ml,mlp,plp, drl,dpl,drel,drhl,dpnl,dml,dmlp,dplp,dunl;
      Real            ar,unr,rr,pr,hr,dfr[MxNVs],ars,mr,mrm,prm, drr,dpr,drer,drhr,dpnr,dmr,dmrm,dprm,dunr;
      Real            as,ps,m,mp,mm,m2p,m2m;
      Real            dm,dmp,dmm,dm2p,dm2m;
      Real            df[MxNVs];

      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };


// fluxes from the left

         pl= ql[4][iql];
         rl= auxl[0][iql];
         hl= auxl[3][iql];
         al= auxl[2][iql];

         drl=    dql[0][iql];
         dpl=  dauxl[4][iql];
         drel=   dql[4][iql];
         drhl=   drel+ dpl;

         unl=  wc[0][ic]*ql[0][iql];
         unl+= wc[1][ic]*ql[1][iql];
         unl+= wc[2][ic]*ql[2][iql];
         unl-= wxdc[0][ic];

         dunl=  wc[0][ic]*dauxl[0][iql]; 
         dunl+= wc[1][ic]*dauxl[1][iql]; 
         dunl+= wc[2][ic]*dauxl[2][iql]; 

         als = 2.*( gam-1. )*hl/( gam+1. );
         als = sqrt(als);
         als = als*als/max(als,unl);

// fluxes from the right

         pr= qr[4][iqr];
         rr= auxr[0][iqr];
         hr= auxr[3][iqr];
         ar= auxr[2][iqr];

         drr=    dqr[0][iqr];
         dpr=  dauxr[4][iqr];
         drer=   dqr[4][iqr];
         drhr=   drer+ dpr;

         unr=  wc[0][ic]*qr[0][iqr];
         unr+= wc[1][ic]*qr[1][iqr];
         unr+= wc[2][ic]*qr[2][iqr];
         unr-= wxdc[0][ic];

         dunr=  wc[0][ic]*dauxr[0][iqr]; 
         dunr+= wc[1][ic]*dauxr[1][iqr]; 
         dunr+= wc[2][ic]*dauxr[2][iqr]; 

         ars = 2.*( gam-1. )*hr/( gam+1. );
         ars = sqrt(ars);
         ars = ars*ars/max(ars,-unr);

         as= min( ars,als );

// linearised AUSM+ flux - 2 coordinates, 2 velocity components


         ml  = unl/as;
         mr  = unr/as;
         dml = dunl/as;
         dmr = dunr/as;

         if( ml < -1. )
        {
            mlp= 0.;
            plp= 0.;
            dmlp= 0.;
            dplp= 0.;
        }
         else 
        {
            if( ml < 1. )
           {
               m2p= ml+1;
               dm2p= 0.5*m2p*dml;
               m2p= 0.25*m2p*m2p;
               m2m= ml-1;
               dm2m=-0.5*m2m*dml;
               m2m=-0.25*m2m*m2m;
               mlp= m2p*( 1- 16*beta0*m2m );
               plp= m2p*( (2-ml)-16*alpha0*ml*m2m );
               dmlp= dm2p*( 1.-16.*beta0*m2m )- 16.*beta0*m2p*dm2m;
               dplp= dm2p*( 2.-ml-16.*alpha0*ml*m2m )+ m2p*(  -dml-16.*alpha0*( dml*m2m+ ml*dm2m ) );
           }
            else
           {
               mlp= ml;
               plp= 1.;
               dmlp= dml;
               dplp= 0.;
           }
        }

         if( mr < -1. ) 
        {
            mrm= mr;
            prm= 1.;
            dmrm= dmr;
            dprm= 0.;

        }
         else 
        {
            if( mr < 1. )
           {
               m2p= mr+1;
               dm2p= 0.5*m2p*dmr;
               m2p= 0.25*m2p*m2p;
               m2m= mr-1;
               dm2m=-0.5*m2m*dmr;
               m2m=-0.25*m2m*m2m;
               mrm= m2m*( 1+ 16*beta0*m2p );
               prm=-m2m*( 2+mr-16*alpha0*mr*m2p );
               dmrm= dm2m*( 1.+ 16.*beta0*m2p )+ 16.*beta0*m2m*dm2p;
               dprm=-dm2m*( 2.+mr-16.*alpha0*mr*m2p )- m2m*(   dmr-16.*alpha0*( dmr*m2p+ mr*dm2p ) );
           }
            else
           {
               mrm= 0.;
               prm= 0.;
               dmrm= 0.;
               dprm= 0.;
           } 
        }

         m= as*( mlp+ mrm );
         dm= as*( dmlp+ dmrm );
         if( m > 0 )
        {
            mp= m;
            dmp= dm;
            mm= 0;
            dmm= 0;
        }
         else
        {
            mp= 0;
            dmp= 0;
            mm= m;
            dmm= dm;
        }

         dpnl= plp*dpl+ dplp*pl;
         dpnr= prm*dpr+ dprm*pr;

// assemble 
         dfl[0]= dmp*rl+            mp*drl;
         dfl[1]= dmp*rl*ql[0][iql]+ mp*dql[1][iql]+ dpnl*wc[0][ic];
         dfl[2]= dmp*rl*ql[1][iql]+ mp*dql[2][iql]+ dpnl*wc[1][ic];
         dfl[3]= dmp*rl*ql[2][iql]+ mp*dql[3][iql]+ dpnl*wc[2][ic];
         dfl[4]= dmp*rl*hl+         mp*drhl;
         for( ia=5;ia<nv;ia++ )
        {
             dfl[ia]= dmp*rl*ql[ia][iql]+ mp*dql[ia][iql]; 
        }

         dfr[0]= dmm*rr+            mm*drr;
         dfr[1]= dmm*rr*qr[0][iqr]+ mm*dqr[1][iqr]+ dpnr*wc[0][ic];
         dfr[2]= dmm*rr*qr[1][iqr]+ mm*dqr[2][iqr]+ dpnr*wc[1][ic];
         dfr[3]= dmm*rr*qr[2][iqr]+ mm*dqr[3][iqr]+ dpnr*wc[2][ic];
         dfr[4]= dmm*rr*hr+         mm*drhr;
         for( ia=5;ia<nv;ia++ )
        {
             dfr[ia]= dmm*rr*qr[ia][iqr]+ mm*dqr[ia][iqr]; 
        }

         df[0]= ( dfl[0]+ dfr[0] )*wc[3][ic];
         df[1]= ( dfl[1]+ dfr[1] )*wc[3][ic];
         df[2]= ( dfl[2]+ dfr[2] )*wc[3][ic];
         df[3]= ( dfl[3]+ dfr[3] )*wc[3][ic];
         df[4]= ( dfl[4]+ dfr[4] )*wc[3][ic];
         for( ia=5;ia<nv;ia++ )
        {
             df[ia]= ( dfl[ia]+ dfr[ia] )*wc[3][ic];
        }

         resl[0][iql]-= df[0];
         resl[1][iql]-= df[1];
         resl[2][iql]-= df[2];
         resl[3][iql]-= df[3];
         resl[4][iql]-= df[4];

         resr[0][iqr]+= df[0];
         resr[1][iqr]+= df[1];
         resr[2][iqr]+= df[2];
         resr[3][iqr]+= df[3];
         resr[4][iqr]+= df[4];

         for( ia=5;ia<nv;ia++ )
        {
            resl[ia][iql]-= df[ia];
            resr[ia][iqr]+= df[ia];
        }
     }

  }

   void cMfAusmGas::diflxb33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
                                      Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
                                      Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      diflx33(  ics,ice,  icql,ql,auxl,dql,dauxl,resl, icqr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
  }

