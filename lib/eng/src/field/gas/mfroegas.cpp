using namespace std;

# include <field/gas.h>
# include <mem/proto.h>
# include <field/grad.h>

   cMfRoeGas::cMfRoeGas( cCosystem *Coo, cVisc *visc )
  {

      coo= Coo;
      vsc= visc;
      nx=  coo->getnx();
      nvel=coo->getnvel(); 

      nvk=3;
      nv=2+nvel;
      naux=7;
      nauxf=7;
      nlhs= 2;

      nv0= nv;
      naux0= naux;
      nauxf0= nauxf;
      nlhs0= nlhs;

      vsc->setvrs( nx,nvel, &nv,&naux,&nauxf,&nlhs );
      ilv[0]=nvel;
      ilv[1]=ilv[0]+1;
      ilv[2]=ilv[1]+1;
      unit[0]= 100.;
      unit[1]=   1.;
      unit[2]= unit[0]*unit[0];
      deflt[0]=   0.;
      deflt[1]= 298.;
      deflt[2]= 100000./unit[2];
      rg= 287/unit[2];
      gam=1.4;
      eps= 0.05;
      coo->setnv(nv);
  }


   void cMfRoeGas::ilhs( Int ics, Int ice, Int *icql, Real *ql[], Real *auxl[], Real *lhsl[], 
                                           Int *icqr, Real *qr[], Real *auxr[], Real *lhsr[],  
                                           Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Int             ia,ja,iql,iqr,ic;

      if( ice > ics )
     {

         for( ic=ics;ic<ice;ic++ )
        { 

            iql= ic;
            if( icql ){ iql= icql[ic]; };
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; };
       
            lhsl[0][iql]+= auxc[nauxf-1][ic];
            lhsr[0][iqr]+= auxc[nauxf-1][ic];

        }
     }
      vsc->ilhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc ) ;
  }

   void cMfRoeGas::ilhs( Int ics, Int ice, Int *sicql, Real *sql, Real *sauxl, Real *slhsl, 
                                           Int *sicqr, Real *sqr, Real *sauxr, Real *slhsr,  
                                           Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Int             nql,nqr,iql,iqr,ic;

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(             sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
                 sicqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        { 

            iql= ic;
            iqr= sicqr[ADDR(0,ic,nfc)]; 
       
            //lhsl[0][iql]+= auxc[nauxf-1][ic];
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhsl[ADDR_(0,iql,nql)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhsr[ADDR_(0,iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
      vsc->ilhs( ics, ice, sicql, sql, sauxl, slhsl, sicqr, sqr, sauxr, slhsr, swc, swxdc, sauxc,nfc,nq ) ;
  }


   void cMfRoeGas::ilhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                           cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                           cAu3xView<Real>& wc,  cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             nql,nqr,iql,iqr,ic;

      Int nfc, nq;
      Int *sicql;
      Real *sql, *sauxl, *slhsl;
      Int *sicqr;
      Real *sqr, *sauxr, *slhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      sicql = icql.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      slhsl = lhsl.get_data();
      sicqr = icqr.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      slhsr = lhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(             sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
                 sicqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        { 

            iql= ic;
            iqr= sicqr[ADDR(0,ic,nfc)]; 
       
            //lhsl[0][iql]+= auxc[nauxf-1][ic];
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhsl[ADDR_(0,iql,nql)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhsr[ADDR_(0,iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
      vsc->ilhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc ) ;
  }

   void cMfRoeGas::ilhs( Int ics, Int ice, Int *sicq, Real *sq, Real *saux, Real *slhs, 
                                           Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Int             iql,iqr,ic;

      if( ice > ics )
     {

        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],slhs[0:nlhs*nq], \
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this )\
         default(none)
         for( ic=ics;ic<ice;ic++ )
        { 

            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];
       
            //lhsl[0][iql]+= auxc[nauxf-1][ic];
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhs[ADDR_(0,iql,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhs[ADDR_(0,iqr,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
      //vsc->ilhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc ) ;
      vsc->ilhs( ics, ice, sicq, sq, saux, slhs, swc, swxdc, sauxc, nfc, nq ) ;
  }

   void cMfRoeGas::ilhs( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& lhs,
                                           cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             iql,iqr,ic;

      Int nfc, nq;
      Int *sicq;  
      Real *sq, *saux, *slhs, *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = q.get_dim1();

      sicq  = icq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      slhs  = lhs.get_data();
      swc   = wc.get_data(); 
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {

        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],slhs[0:nlhs*nq], \
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this )\
         default(none)
         for( ic=ics;ic<ice;ic++ )
        { 

            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];
       
            //lhsl[0][iql]+= auxc[nauxf-1][ic];
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhs[ADDR_(0,iql,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhs[ADDR_(0,iqr,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
      //vsc->ilhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc ) ;
      vsc->ilhs( ics, ice, icq, q, aux, lhs, wc, wxdc, auxc ) ;
  }

   //void cMfRoeGas::wlhs( Int ics, Int ice, Int *icql, Real *ql[], Real *auxl[], Real *lhsl[], 
   //                                        Int *icqr, Real *qr[], Real *auxr[], Real *lhsr[], 
   //                                        Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cMfRoeGas::wlhs( Int ics, Int ice, Int *icql, Real *sql, Real *sauxl, Real *slhsl, 
                                           Int *icqr, Real *sqr, Real *sauxr, Real *slhsr, 
                                           Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
//      Real            gam1,kr;
//      Real            dp1,dp2,dp3,dp4,dp5;
//      Real            dfr[MxNVs][MxNVs];
//
//      Int             ic,ia,iql,iqr,ja;
//      Int             nql,nqr;
//      if( ice > ics )
//     {
//         nql = nfc;
//         nqr = nq;
//        #pragma acc enter data copyin(this)
//        #pragma acc parallel loop \
//         present(            sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
//                 icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
//                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
//         default(none)
//         for( ic=ics;ic<ice;ic++ )
//        {
//            iql= ic;
//            iqr= icqr[ic]; 
//      
//            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
//            slhsr[ADDR(0,iqr,nqr)]+= sauxc[ADDR(nauxf-1,ic,nfc)];
//
//        }
//        #pragma acc exit data delete(this)
//     }
//      //vsc->wlhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc );
//      vsc->wlhs( ics, ice, icql, sql, sauxl, slhsl, icqr, sqr, sauxr, slhsr, swc, swxdc, sauxc, nql, nqr );
  }

   void cMfRoeGas::wlhs( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                           cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                           cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            gam1,kr;
      Real            dp1,dp2,dp3,dp4,dp5;
      Real            dfr[MxNVs][MxNVs];

      Int             ic,ia,iql,iqr,ja;
      Int             nql,nqr;

      Int nfc, nq;
      Int *icql;
      Real *sql, *sauxl, *slhsl;
      Int *icqr;
      Real *sqr, *sauxr, *slhsr;  
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      slhsl = lhsl.get_data(); 
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      slhsr = lhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(            sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
                 icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic]; 
      
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhsr[ADDR_(0,iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
      //vsc->wlhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc );
      vsc->wlhs( ics, ice, icql_view, ql, auxl, lhsl, icqr_view, qr, auxr, lhsr, wc, wxdc, auxc );
  }

   //void cMfRoeGas::slhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[] )
   void cMfRoeGas::slhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *slhsa, Int nq )
  {
      //vsc->slhs( iqs,iqe, cfl, q,aux, dqdx,dst,wq,lhs );
      vsc->slhs( iqs,iqe, cfl, sq,saux, sdqdx,sdst,swq,slhsa,nq );
  }

   void cMfRoeGas::slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa )
  {
      //vsc->slhs( iqs,iqe, cfl, q,aux, dqdx,dst,wq,lhs );
      vsc->slhs( iqs,iqe, cfl, q,aux, dqdx,dst,wq,lhsa );
  }
 
   //void cMfRoeGas::vlhs( Int iqs, Int iqe, Real dtm,Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[],
   //                     Real *wq[], Real *lhs[], Real rord )
   void cMfRoeGas::vlhs( Int iqs, Int iqe, Real dtm,Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst,
                         Real *swq, Real *slhs, Real rord, Int nq )
  {
      Int             ia,ja,iq;
      Real            w,tau;
      if( iqe > iqs )
     {
         w= 1./cfl;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //lhs[nlhs-1][iq]=  lhs[0][iq]*w;
            //if(rord>0) lhs[nlhs-1][iq]+= rord*wq[0][iq]/dtm;
            //lhs[0][iq]+=     lhs[nlhs-1][iq];
            slhs[ADDR(nlhs-1,iq,nq)]=  slhs[ADDR(0,iq,nq)]*w;
            if(rord>0) slhs[ADDR(nlhs-1,iq,nq)]+= rord*swq[ADDR(0,iq,nq)]/dtm;
            slhs[ADDR(0,iq,nq)]+=     slhs[ADDR(nlhs-1,iq,nq)];

        }
        #pragma acc exit data delete(this)
     }
      vsc->vlhs( iqs,iqe, cfl, swq,slhs, nq );
  }

   void cMfRoeGas::vlhs( Int iqs, Int iqe, Real dtm,Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                         cAu3xView<Real>& wq, cAu3xView<Real>& lhs, Real rord )
  {
      Int             ia,ja,iq;
      Real            w,tau;

      Int nq;
      Real *sq, *saux, *sdqdx, *sdst, *swq, *slhs;

      nq    = q.get_dim1();

      sq    = q.get_data();
      saux  = aux.get_data();
      sdqdx = dqdx.get_data();
      sdst  = dst.get_data();
      swq   = wq.get_data(); 
      slhs  = lhs.get_data();

      if( iqe > iqs )
     {
         w= 1./cfl;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //lhs[nlhs-1][iq]=  lhs[0][iq]*w;
            //if(rord>0) lhs[nlhs-1][iq]+= rord*wq[0][iq]/dtm;
            //lhs[0][iq]+=     lhs[nlhs-1][iq];
            slhs[ADDR(nlhs-1,iq,nq)]=  slhs[ADDR(0,iq,nq)]*w;
            if(rord>0) slhs[ADDR(nlhs-1,iq,nq)]+= rord*swq[ADDR(0,iq,nq)]/dtm;
            slhs[ADDR(0,iq,nq)]+=     slhs[ADDR(nlhs-1,iq,nq)];

        }
        #pragma acc exit data delete(this)
     }
      vsc->vlhs( iqs,iqe, cfl, wq,lhs );
  }

   void cMfRoeGas::invdg( Int iqs, Int iqe, Real *slhs, Real *sres, Int nq )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present(sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv0;iv++ )
           {
               sres[ADDR(iv,iq,nq)]/= slhs[ADDR(0,iq,nq)]; 
           }
        }
        #pragma acc exit data copyout(this)
     }
      //vsc->invdg( iqs,iqe, lhs,res );
      vsc->invdg( iqs,iqe, slhs,sres,nq );
  }

   void cMfRoeGas::invdg( Int iqs, Int iqe, cAu3xView<Real>& lhs, cAu3xView<Real>& res )
  {
      Int iv,iq;

      Int nq;
      Real *slhs, *sres;

      nq = lhs.get_dim1();
      slhs = lhs.get_data();
      sres = res.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present(sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv0;iv++ )
           {
               sres[ADDR(iv,iq,nq)]/= slhs[ADDR(0,iq,nq)]; 
           }
        }
        #pragma acc exit data copyout(this)
     }
      //vsc->invdg( iqs,iqe, lhs,res );
      vsc->invdg( iqs,iqe, lhs,res );
  }

   void cMfRoeGas::iflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                            Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];

      Int             ia,ic,iql,iqr;

      for( ic=ics;ic<ice;ic++ )
     {

         iql= ic;
         iqr= icqr[ic];

 
// fluxes from the left

         pl= ql[4][iql];
         rl= auxl[0][iql];
         al= auxl[2][iql];
         hl= auxl[3][iql];

         unl=  wc[0][ic]*ql[0][iql];
         unl+= wc[1][ic]*ql[1][iql];
         unl+= wc[2][ic]*ql[2][iql];

         ll1= unl-wxdc[0][ic];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
         fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
         fl[3]= fl[0]*ql[2][iql]+ wc[2][ic]*pl;
         fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia][iql]; }

// fluxes from the right
         rr= auxr[0][iqr];
         pr= qr[4][iqr];
         rr= auxr[0][iqr];
         ar= auxr[2][iqr];
         hr= auxr[3][iqr];

         unr=  wc[0][ic]*qr[0][iqr];
         unr+= wc[1][ic]*qr[1][iqr];
         unr+= wc[2][ic]*qr[2][iqr];

         lr1= unr-wxdc[0][ic];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
         fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
         fr[3]= fr[0]*qr[2][iqr]+ wc[2][ic]*pr;
         fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia][iqr]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
         qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
        }

         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];
         una+= qa[2]* wc[2][ic];

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         la1= una-wxdc[0][ic];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         auxc[0][ic]= wl;
         auxc[1][ic]= wr;
         auxc[2][ic]= ra;
         auxc[3][ic]= la1;
         auxc[4][ic]= la3;
         auxc[5][ic]= la4;
         auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0][iqr]- ql[0][iql];
         dq[1]= qr[1][iqr]- ql[1][iql];
         dq[2]= qr[2][iqr]- ql[2][iql];
         dq[3]= qr[3][iqr]- ql[3][iql];
         dq[4]= qr[4][iqr]- ql[4][iql];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= qr[ia][iqr]- ql[ia][iql];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];
         ana[2]= aa*wc[2][ic];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=   dw1+               dw3+                  dw4;
         fa[1]=   dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]=   dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]=   dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]=   dw1*ka+    dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; }

// assemble 
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];

         rhsl[0][iql]-= f[0];
         rhsl[1][iql]-= f[1];
         rhsl[2][iql]-= f[2];
         rhsl[3][iql]-= f[3];
         rhsl[4][iql]-= f[4];

         rhsr[0][iqr]+= f[0];
         rhsr[1][iqr]+= f[1];
         rhsr[2][iqr]+= f[2];
         rhsr[3][iqr]+= f[3];
         rhsr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            rhsl[ia][iql]-= f[ia];
            rhsr[ia][iqr]+= f[ia];
        }
         auxc[nauxf-1][ic]*= wc[3][ic];
     }
  }

   void cMfRoeGas::iflx33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *srhsl,
                                            Int *icqr, Real *sqr, Real *sauxr, Real *srhsr, 
                                            Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs],wn[4];

      Int             ia,ic,iql,iqr;
      Int             nql,nqr;

      nql = nfc;
      nqr = nq;
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       firstprivate(nql,nqr) \
       private(fl,fr,qa,ana,fa,dw2,dq,dw5,f,wn)\
       present(            sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)
      for( ic=ics;ic<ice;ic++ )
     {

         iql= ic;
         iqr= icqr[ic];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];
 
// fluxes from the left

         //pl= ql[4][iql];
         //rl= auxl[0][iql];
         //al= auxl[2][iql];
         //hl= auxl[3][iql];
         pl= sql[ADDR(4,iql,nql)];
         rl= sauxl[ADDR(0,iql,nql)];
         al= sauxl[ADDR(2,iql,nql)];
         hl= sauxl[ADDR(3,iql,nql)];

         //unl=  wc[0][ic]*ql[0][iql];
         //unl+= wc[1][ic]*ql[1][iql];
         //unl+= wc[2][ic]*ql[2][iql];
         unl=  wn[0]*sql[ADDR(0,iql,nql)];
         unl+= wn[1]*sql[ADDR(1,iql,nql)];
         unl+= wn[2]*sql[ADDR(2,iql,nql)];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2][iql]+ wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
         fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ wn[0]*pl;
         fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ wn[1]*pl;
         fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ wn[2]*pl;
         fl[4]= fl[0]*hl+ swxdc[ADDR(0,ic,nfc)]*pl;
         //for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia][iql]; }
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)]; }

// fluxes from the right
         //pr= qr[4][iqr];
         //rr= auxr[0][iqr];
         //ar= auxr[2][iqr];
         //hr= auxr[3][iqr];
         pr= sqr[ADDR(4,iqr,nqr)];
         rr= sauxr[ADDR(0,iqr,nqr)];
         ar= sauxr[ADDR(2,iqr,nqr)];
         hr= sauxr[ADDR(3,iqr,nqr)];

         //unr=  wc[0][ic]*qr[0][iqr];
         //unr+= wc[1][ic]*qr[1][iqr];
         //unr+= wc[2][ic]*qr[2][iqr];
         unr=  wn[0]*sqr[ADDR(0,iqr,nqr)];
         unr+= wn[1]*sqr[ADDR(1,iqr,nqr)];
         unr+= wn[2]*sqr[ADDR(2,iqr,nqr)];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         //fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2][iqr]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
         fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ wn[0]*pr;
         fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ wn[1]*pr;
         fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ wn[2]*pr;
         fr[4]= fr[0]*hr+ swxdc[ADDR(0,ic,nfc)]*pr;
         //for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia][iqr]; }
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         //qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         //qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         //qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         //qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
         //qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
         qa[0]= wl*sql[ADDR(0,iql,nql)]+ wr*sqr[ADDR(0,iqr,nqr)];
         qa[1]= wl*sql[ADDR(1,iql,nql)]+ wr*sqr[ADDR(1,iqr,nqr)];
         qa[2]= wl*sql[ADDR(2,iql,nql)]+ wr*sqr[ADDR(2,iqr,nqr)];
         qa[3]= wl*sql[ADDR(3,iql,nql)]+ wr*sqr[ADDR(3,iqr,nqr)];
         qa[4]= wl*sql[ADDR(4,iql,nql)]+ wr*sqr[ADDR(4,iqr,nqr)];
         for( ia=5;ia<nv;ia++ )
        {
            //qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
            qa[ia]= wl*sql[ADDR(ia,iql,nql)]+ wr*sqr[ADDR(ia,iqr,nqr)];
        }

         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= la1;
         //auxc[4][ic]= la3;
         //auxc[5][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= la1;
         sauxc[ADDR(4,ic,nfc)]= la3;
         sauxc[ADDR(5,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         //dq[0]= qr[0][iqr]- ql[0][iql];
         //dq[1]= qr[1][iqr]- ql[1][iql];
         //dq[2]= qr[2][iqr]- ql[2][iql];
         //dq[3]= qr[3][iqr]- ql[3][iql];
         //dq[4]= qr[4][iqr]- ql[4][iql];
         dq[0]= sqr[ADDR(0,iqr,nqr)]- sql[ADDR(0,iql,nql)];
         dq[1]= sqr[ADDR(1,iqr,nqr)]- sql[ADDR(1,iql,nql)];
         dq[2]= sqr[ADDR(2,iqr,nqr)]- sql[ADDR(2,iql,nql)];
         dq[3]= sqr[ADDR(3,iqr,nqr)]- sql[ADDR(3,iql,nql)];
         dq[4]= sqr[ADDR(4,iqr,nqr)]- sql[ADDR(4,iql,nql)];
         for( ia=5;ia<nv;ia++ )
        {
            //dq[ia]= qr[ia][iqr]- ql[ia][iql];
            dq[ia]= sqr[ADDR(ia,iqr,nqr)]- sql[ADDR(ia,iql,nql)];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=   dw1+               dw3+                  dw4;
         fa[1]=   dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]=   dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]=   dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]=   dw1*ka+    dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; }

// assemble 
         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //rhsl[0][iql]-= f[0];
         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhsl[ADDR_(0,iql,nql)]-= f[0];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nql)]-= f[4];

         //rhsr[0][iqr]+= f[0];
         //rhsr[1][iqr]+= f[1];
         //rhsr[2][iqr]+= f[2];
         //rhsr[3][iqr]+= f[3];
         //rhsr[4][iqr]+= f[4];
         #pragma acc atomic
         srhsr[ADDR_(0,iqr,nqr)]+= f[0];
         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nqr)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic
            srhsl[ADDR_(ia,iql,nql)]-= f[ia];
            #pragma acc atomic
            srhsr[ADDR_(ia,iqr,nqr)]+= f[ia];
        }
         sauxc[ADDR(nauxf-1,ic,nfc)]*= wn[3];
     }
  }

   void cMfRoeGas::iflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                            cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs],wn[4];

      Int             ia,ic,iql,iqr;
      Int             nql,nqr;

      Int nfc, nq;
      Int *icql;
      Real *sql, *sauxl, *srhsl;
      Int *icqr; 
      Real *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      srhsr = rhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();


      nql = nfc;
      nqr = nq;
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       firstprivate(nql,nqr) \
       private(fl,fr,qa,ana,fa,dw2,dq,dw5,f,wn)\
       present(            sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)
      for( ic=ics;ic<ice;ic++ )
     {

         iql= ic;
         iqr= icqr[ic];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];
 
// fluxes from the left

         //pl= ql[4][iql];
         //rl= auxl[0][iql];
         //al= auxl[2][iql];
         //hl= auxl[3][iql];
         pl= sql[ADDR(4,iql,nql)];
         rl= sauxl[ADDR(0,iql,nql)];
         al= sauxl[ADDR(2,iql,nql)];
         hl= sauxl[ADDR(3,iql,nql)];

         //unl=  wc[0][ic]*ql[0][iql];
         //unl+= wc[1][ic]*ql[1][iql];
         //unl+= wc[2][ic]*ql[2][iql];
         unl=  wn[0]*sql[ADDR(0,iql,nql)];
         unl+= wn[1]*sql[ADDR(1,iql,nql)];
         unl+= wn[2]*sql[ADDR(2,iql,nql)];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2][iql]+ wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
         fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ wn[0]*pl;
         fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ wn[1]*pl;
         fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ wn[2]*pl;
         fl[4]= fl[0]*hl+ swxdc[ADDR(0,ic,nfc)]*pl;
         //for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia][iql]; }
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)]; }

// fluxes from the right
         //pr= qr[4][iqr];
         //rr= auxr[0][iqr];
         //ar= auxr[2][iqr];
         //hr= auxr[3][iqr];
         pr= sqr[ADDR(4,iqr,nqr)];
         rr= sauxr[ADDR(0,iqr,nqr)];
         ar= sauxr[ADDR(2,iqr,nqr)];
         hr= sauxr[ADDR(3,iqr,nqr)];

         //unr=  wc[0][ic]*qr[0][iqr];
         //unr+= wc[1][ic]*qr[1][iqr];
         //unr+= wc[2][ic]*qr[2][iqr];
         unr=  wn[0]*sqr[ADDR(0,iqr,nqr)];
         unr+= wn[1]*sqr[ADDR(1,iqr,nqr)];
         unr+= wn[2]*sqr[ADDR(2,iqr,nqr)];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         //fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2][iqr]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
         fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ wn[0]*pr;
         fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ wn[1]*pr;
         fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ wn[2]*pr;
         fr[4]= fr[0]*hr+ swxdc[ADDR(0,ic,nfc)]*pr;
         //for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia][iqr]; }
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         //qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         //qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         //qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         //qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
         //qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
         qa[0]= wl*sql[ADDR(0,iql,nql)]+ wr*sqr[ADDR(0,iqr,nqr)];
         qa[1]= wl*sql[ADDR(1,iql,nql)]+ wr*sqr[ADDR(1,iqr,nqr)];
         qa[2]= wl*sql[ADDR(2,iql,nql)]+ wr*sqr[ADDR(2,iqr,nqr)];
         qa[3]= wl*sql[ADDR(3,iql,nql)]+ wr*sqr[ADDR(3,iqr,nqr)];
         qa[4]= wl*sql[ADDR(4,iql,nql)]+ wr*sqr[ADDR(4,iqr,nqr)];
         for( ia=5;ia<nv;ia++ )
        {
            //qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
            qa[ia]= wl*sql[ADDR(ia,iql,nql)]+ wr*sqr[ADDR(ia,iqr,nqr)];
        }

         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= la1;
         //auxc[4][ic]= la3;
         //auxc[5][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= la1;
         sauxc[ADDR(4,ic,nfc)]= la3;
         sauxc[ADDR(5,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         //dq[0]= qr[0][iqr]- ql[0][iql];
         //dq[1]= qr[1][iqr]- ql[1][iql];
         //dq[2]= qr[2][iqr]- ql[2][iql];
         //dq[3]= qr[3][iqr]- ql[3][iql];
         //dq[4]= qr[4][iqr]- ql[4][iql];
         dq[0]= sqr[ADDR(0,iqr,nqr)]- sql[ADDR(0,iql,nql)];
         dq[1]= sqr[ADDR(1,iqr,nqr)]- sql[ADDR(1,iql,nql)];
         dq[2]= sqr[ADDR(2,iqr,nqr)]- sql[ADDR(2,iql,nql)];
         dq[3]= sqr[ADDR(3,iqr,nqr)]- sql[ADDR(3,iql,nql)];
         dq[4]= sqr[ADDR(4,iqr,nqr)]- sql[ADDR(4,iql,nql)];
         for( ia=5;ia<nv;ia++ )
        {
            //dq[ia]= qr[ia][iqr]- ql[ia][iql];
            dq[ia]= sqr[ADDR(ia,iqr,nqr)]- sql[ADDR(ia,iql,nql)];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=   dw1+               dw3+                  dw4;
         fa[1]=   dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]=   dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]=   dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]=   dw1*ka+    dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; }

// assemble 
         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //rhsl[0][iql]-= f[0];
         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhsl[ADDR_(0,iql,nql)]-= f[0];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nql)]-= f[4];

         //rhsr[0][iqr]+= f[0];
         //rhsr[1][iqr]+= f[1];
         //rhsr[2][iqr]+= f[2];
         //rhsr[3][iqr]+= f[3];
         //rhsr[4][iqr]+= f[4];
         #pragma acc atomic
         srhsr[ADDR_(0,iqr,nqr)]+= f[0];
         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nqr)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic
            srhsl[ADDR_(ia,iql,nql)]-= f[ia];
            #pragma acc atomic
            srhsr[ADDR_(ia,iqr,nqr)]+= f[ia];
        }
         sauxc[ADDR(nauxf-1,ic,nfc)]*= wn[3];
     }
  }

   void cMfRoeGas::iflx33( Int ics,Int ice, Int *icq[2], Real *q[], Real *aux[], Real *rhs[],
                           Real *wc[], Real *wxdc[], Real *auxc[] ) 
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];

      Int             ia,ic,iql,iqr;

      for( ic=ics;ic<ice;ic++ )
     {

         iql= icq[0][ic]; 
         iqr= icq[1][ic];

 
// fluxes from the left

         pl= q[4][iql];
         rl= aux[0][iql];
         al= aux[2][iql];
         hl= aux[3][iql];

         unl=  wc[0][ic]*q[0][iql];
         unl+= wc[1][ic]*q[1][iql];
         unl+= wc[2][ic]*q[2][iql];

         ll1= unl-wxdc[0][ic];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         fl[1]= fl[0]*q[0][iql]+ wc[0][ic]*pl;
         fl[2]= fl[0]*q[1][iql]+ wc[1][ic]*pl;
         fl[3]= fl[0]*q[2][iql]+ wc[2][ic]*pl;
         fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*q[ia][iql]; }

// fluxes from the right
         rr= aux[0][iqr];
         pr= q[4][iqr];
         rr= aux[0][iqr];
         ar= aux[2][iqr];
         hr= aux[3][iqr];

         unr=  wc[0][ic]*q[0][iqr];
         unr+= wc[1][ic]*q[1][iqr];
         unr+= wc[2][ic]*q[2][iqr];

         lr1= unr-wxdc[0][ic];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         fr[1]= fr[0]*q[0][iqr]+ wc[0][ic]*pr;
         fr[2]= fr[0]*q[1][iqr]+ wc[1][ic]*pr;
         fr[3]= fr[0]*q[2][iqr]+ wc[2][ic]*pr;
         fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*q[ia][iqr]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*q[0][iql]+ wr*q[0][iqr];
         qa[1]= wl*q[1][iql]+ wr*q[1][iqr];
         qa[2]= wl*q[2][iql]+ wr*q[2][iqr];
         qa[3]= wl*q[3][iql]+ wr*q[3][iqr];
         qa[4]= wl*q[4][iql]+ wr*q[4][iqr];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*q[ia][iql]+ wr*q[ia][iqr];
        }

         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];
         una+= qa[2]* wc[2][ic];

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         la1= una-wxdc[0][ic];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         auxc[0][ic]= wl;
         auxc[1][ic]= wr;
         auxc[2][ic]= ra;
         auxc[3][ic]= la1;
         auxc[4][ic]= la3;
         auxc[5][ic]= la4;
         auxc[nauxf-1][ic]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= q[0][iqr]- q[0][iql];
         dq[1]= q[1][iqr]- q[1][iql];
         dq[2]= q[2][iqr]- q[2][iql];
         dq[3]= q[3][iqr]- q[3][iql];
         dq[4]= q[4][iqr]- q[4][iql];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= q[ia][iqr]- q[ia][iql];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];
         ana[2]= aa*wc[2][ic];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=   dw1+               dw3+                  dw4;
         fa[1]=   dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]=   dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]=   dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]=   dw1*ka+    dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; }

// assemble 
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];

         rhs[0][iql]-= f[0];
         rhs[1][iql]-= f[1];
         rhs[2][iql]-= f[2];
         rhs[3][iql]-= f[3];
         rhs[4][iql]-= f[4];

         rhs[0][iqr]+= f[0];
         rhs[1][iqr]+= f[1];
         rhs[2][iqr]+= f[2];
         rhs[3][iqr]+= f[3];
         rhs[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            rhs[ia][iql]-= f[ia];
            rhs[ia][iqr]+= f[ia];
        }
         auxc[nauxf-1][ic]*= wc[3][ic];
     }
  }

   void cMfRoeGas::dvar3( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dU[], Real *dq[] )
  {
      Int iq,ia; 
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;
      cv= rg/(gam-1.);

      for( iq=iqs;iq<iqe;iq++ )
     {
         t=  q[3][iq];
         p=  q[4][iq];
         ro= aux[0][iq];
         h=  aux[3][iq];
         re= ro*h- p;
         e= re/ro;
   
         dro= dU[0][iq];
         dre= dU[4][iq];

         dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
         dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
         dq[2][iq]= ( dU[3][iq]- q[2][iq]*dro )/ro;

         dk=  q[0][iq]*dq[0][iq];
         dk+= q[1][iq]*dq[1][iq];
         dk+= q[2][iq]*dq[2][iq];

         dk*= ro;
         dt= ( dre- e*dro- dk )/( ro*cv );
         dp= p*( dro/ro+ dt/t );

         dq[3][iq]= dt;
         dq[4][iq]= dp;
     }
  }

   void cMfRoeGas::dvar3( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq )
  {
      Int iq,ia; 
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;
      cv= rg/(gam-1.);

      for( iq=iqs;iq<iqe;iq++ )
     {
         //t=  q[3][iq];
         //p=  q[4][iq];
         //ro= aux[0][iq];
         //h=  aux[3][iq];
         t=  q[ADDR(3,iq,nq)];
         p=  q[ADDR(4,iq,nq)];
         ro= aux[ADDR(0,iq,nq)];
         h=  aux[ADDR(3,iq,nq)];
         re= ro*h- p;
         e= re/ro;
   
         //dro= dU[0][iq];
         //dre= dU[4][iq];
         dro= dU[ADDR(0,iq,nq)];
         dre= dU[ADDR(4,iq,nq)];

         //dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
         //dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
         //dq[2][iq]= ( dU[3][iq]- q[2][iq]*dro )/ro;
         dq[ADDR(0,iq,nq)]= ( dU[ADDR(1,iq,nq)]- q[ADDR(0,iq,nq)]*dro )/ro;
         dq[ADDR(1,iq,nq)]= ( dU[ADDR(2,iq,nq)]- q[ADDR(1,iq,nq)]*dro )/ro;
         dq[ADDR(2,iq,nq)]= ( dU[ADDR(3,iq,nq)]- q[ADDR(2,iq,nq)]*dro )/ro;

         //dk=  q[0][iq]*dq[0][iq];
         //dk+= q[1][iq]*dq[1][iq];
         //dk+= q[2][iq]*dq[2][iq];
         dk=  q[ADDR(0,iq,nq)]*dq[ADDR(0,iq,nq)];
         dk+= q[ADDR(1,iq,nq)]*dq[ADDR(1,iq,nq)];
         dk+= q[ADDR(2,iq,nq)]*dq[ADDR(2,iq,nq)];

         dk*= ro;
         dt= ( dre- e*dro- dk )/( ro*cv );
         dp= p*( dro/ro+ dt/t );

         //dq[3][iq]= dt;
         //dq[4][iq]= dp;
         dq[ADDR(3,iq,nq)]= dt;
         dq[ADDR(4,iq,nq)]= dp;
     }
  }

   void cMfRoeGas::dvar3gpu( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq )
  {
      Int iq,ia; 
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;
      cv= rg/(gam-1.);

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop \
      present(q[0:nv*nq],aux[0:naux*nq],dU[0:nv*nq],dq[0:nv*nq],this) \
      default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         //t=  q[3][iq];
         //p=  q[4][iq];
         //ro= aux[0][iq];
         //h=  aux[3][iq];
         t=  q[ADDR(3,iq,nq)];
         p=  q[ADDR(4,iq,nq)];
         ro= aux[ADDR(0,iq,nq)];
         h=  aux[ADDR(3,iq,nq)];
         re= ro*h- p;
         e= re/ro;
   
         //dro= dU[0][iq];
         //dre= dU[4][iq];
         dro= dU[ADDR(0,iq,nq)];
         dre= dU[ADDR(4,iq,nq)];

         //dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
         //dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
         //dq[2][iq]= ( dU[3][iq]- q[2][iq]*dro )/ro;
         dq[ADDR(0,iq,nq)]= ( dU[ADDR(1,iq,nq)]- q[ADDR(0,iq,nq)]*dro )/ro;
         dq[ADDR(1,iq,nq)]= ( dU[ADDR(2,iq,nq)]- q[ADDR(1,iq,nq)]*dro )/ro;
         dq[ADDR(2,iq,nq)]= ( dU[ADDR(3,iq,nq)]- q[ADDR(2,iq,nq)]*dro )/ro;

         //dk=  q[0][iq]*dq[0][iq];
         //dk+= q[1][iq]*dq[1][iq];
         //dk+= q[2][iq]*dq[2][iq];
         dk=  q[ADDR(0,iq,nq)]*dq[ADDR(0,iq,nq)];
         dk+= q[ADDR(1,iq,nq)]*dq[ADDR(1,iq,nq)];
         dk+= q[ADDR(2,iq,nq)]*dq[ADDR(2,iq,nq)];

         dk*= ro;
         dt= ( dre- e*dro- dk )/( ro*cv );
         dp= p*( dro/ro+ dt/t );

         //dq[3][iq]= dt;
         //dq[4][iq]= dp;
         dq[ADDR(3,iq,nq)]= dt;
         dq[ADDR(4,iq,nq)]= dp;
     }
     #pragma acc exit data delete(this)
  }

   void cMfRoeGas::dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view )
  {
      Int iq,ia;
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;
      Int nq;
      Real *q, *aux, *dU, *dq;

      nq = q_view.get_dim1();

      q = q_view.get_data();
      aux = aux_view.get_data();
      dU = dU_view.get_data();
      dq = dq_view.get_data();

      cv= rg/(gam-1.);

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop \
      present(q[0:nv*nq],aux[0:naux*nq],dU[0:nv*nq],dq[0:nv*nq],this) \
      default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         //t=  q[3][iq];
         //p=  q[4][iq];
         //ro= aux[0][iq];
         //h=  aux[3][iq];
         t=  q[ADDR(3,iq,nq)];
         p=  q[ADDR(4,iq,nq)];
         ro= aux[ADDR(0,iq,nq)];
         h=  aux[ADDR(3,iq,nq)];
         re= ro*h- p;
         e= re/ro;
         //dro= dU[0][iq];
         //dre= dU[4][iq];
         dro= dU[ADDR(0,iq,nq)];
         dre= dU[ADDR(4,iq,nq)];

         //dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
         //dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
         //dq[2][iq]= ( dU[3][iq]- q[2][iq]*dro )/ro;
         dq[ADDR(0,iq,nq)]= ( dU[ADDR(1,iq,nq)]- q[ADDR(0,iq,nq)]*dro )/ro;
         dq[ADDR(1,iq,nq)]= ( dU[ADDR(2,iq,nq)]- q[ADDR(1,iq,nq)]*dro )/ro;
         dq[ADDR(2,iq,nq)]= ( dU[ADDR(3,iq,nq)]- q[ADDR(2,iq,nq)]*dro )/ro;

         //dk=  q[0][iq]*dq[0][iq];
         //dk+= q[1][iq]*dq[1][iq];
         //dk+= q[2][iq]*dq[2][iq];
         dk=  q[ADDR(0,iq,nq)]*dq[ADDR(0,iq,nq)];
         dk+= q[ADDR(1,iq,nq)]*dq[ADDR(1,iq,nq)];
         dk+= q[ADDR(2,iq,nq)]*dq[ADDR(2,iq,nq)];

         dk*= ro;
         dt= ( dre- e*dro- dk )/( ro*cv );
         dp= p*( dro/ro+ dt/t );

         //dq[3][iq]= dt;
         //dq[4][iq]= dp;
         dq[ADDR(3,iq,nq)]= dt;
         dq[ADDR(4,iq,nq)]= dp;
     }
     #pragma acc exit data delete(this)
  }

   void cMfRoeGas::auxv3( Int iqs, Int iqe, Real *q[], Real *aux[] )
  {
      Int iq; 
      Int iv;
      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);
      Real cp=rg*gam/(gam-1);
      
      for( iq=iqs;iq<iqe;iq++ )
     {
// density
         aux[0][iq]= q[4][iq]/( rg*q[3][iq] );
// kinetic energy
         aux[1][iq]=  q[0][iq]*q[0][iq];
         aux[1][iq]+= q[1][iq]*q[1][iq];
         aux[1][iq]+= q[2][iq]*q[2][iq];
         aux[1][iq]*= 0.5;
// speed of sound and total entalpy
         aux[2][iq]= gam*rg* q[3][iq]; 
         aux[3][iq]= aux[2][iq]/(gam-1)+ aux[1][iq];
         aux[2][iq]= sqrt( aux[2][iq] );
         aux[4][iq]= cp;
         aux[5][iq]= mu;
         aux[6][iq]= kappa;
     } 
  }

   void cMfRoeGas::auxv3( Int iqs, Int iqe, Real *q, Real *aux, Int nq )
  {
      Int iq; 
      Int iv;
      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);
      Real cp=rg*gam/(gam-1);
      
      for( iq=iqs;iq<iqe;iq++ )
     {
// density
         aux[ADDR(0,iq,nq)]= q[ADDR(4,iq,nq)]/( rg*q[ADDR(3,iq,nq)] );
// kinetic energy
         aux[ADDR(1,iq,nq)]=  q[ADDR(0,iq,nq)]*q[ADDR(0,iq,nq)];
         aux[ADDR(1,iq,nq)]+= q[ADDR(1,iq,nq)]*q[ADDR(1,iq,nq)];
         aux[ADDR(1,iq,nq)]+= q[ADDR(2,iq,nq)]*q[ADDR(2,iq,nq)];
         aux[ADDR(1,iq,nq)]*= 0.5;
// speed of sound and total entalpy
         aux[ADDR(2,iq,nq)]= gam*rg* q[ADDR(3,iq,nq)]; 
         aux[ADDR(3,iq,nq)]= aux[ADDR(2,iq,nq)]/(gam-1)+ aux[ADDR(1,iq,nq)];
         aux[ADDR(2,iq,nq)]= sqrt( aux[ADDR(2,iq,nq)] );
         aux[ADDR(4,iq,nq)]= cp;
         aux[ADDR(5,iq,nq)]= mu;
         aux[ADDR(6,iq,nq)]= kappa;
     } 
  }

   void cMfRoeGas::auxv3gpu( Int iqs, Int iqe, Real *q, Real *aux, Int nq )
  {
      Int iq; 
      Int iv;
      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);
      Real cp=rg*gam/(gam-1);
      
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop \
       present(q[0:nv*nq],aux[0:naux*nq],this) \
       default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
// density
         aux[ADDR(0,iq,nq)]= q[ADDR(4,iq,nq)]/( rg*q[ADDR(3,iq,nq)] );
// kinetic energy
         aux[ADDR(1,iq,nq)]=  q[ADDR(0,iq,nq)]*q[ADDR(0,iq,nq)];
         aux[ADDR(1,iq,nq)]+= q[ADDR(1,iq,nq)]*q[ADDR(1,iq,nq)];
         aux[ADDR(1,iq,nq)]+= q[ADDR(2,iq,nq)]*q[ADDR(2,iq,nq)];
         aux[ADDR(1,iq,nq)]*= 0.5;
// speed of sound and total entalpy
         aux[ADDR(2,iq,nq)]= gam*rg* q[ADDR(3,iq,nq)]; 
         aux[ADDR(3,iq,nq)]= aux[ADDR(2,iq,nq)]/(gam-1)+ aux[ADDR(1,iq,nq)];
         aux[ADDR(2,iq,nq)]= sqrt( aux[ADDR(2,iq,nq)] );
         aux[ADDR(4,iq,nq)]= cp;
         aux[ADDR(5,iq,nq)]= mu;
         aux[ADDR(6,iq,nq)]= kappa;
     } 
      #pragma acc exit data delete(this)
  }

   void cMfRoeGas::auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch )
  {
      Int iq;
      Int iv;
      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);
      Real cp=rg*gam/(gam-1);

      if(arch=="d")
     {
         Int nq;
         Real *q, *aux;

         nq = q_view.get_dim1();

         q   = q_view.get_data();
         aux = aux_view.get_data();

         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          present(q[0:nv*nq],aux[0:naux*nq],this) \
          default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
//    density
            aux[ADDR(0,iq,nq)]= q[ADDR(4,iq,nq)]/( rg*q[ADDR(3,iq,nq)] );
//    kinetic energy
            aux[ADDR(1,iq,nq)]=  q[ADDR(0,iq,nq)]*q[ADDR(0,iq,nq)];
            aux[ADDR(1,iq,nq)]+= q[ADDR(1,iq,nq)]*q[ADDR(1,iq,nq)];
            aux[ADDR(1,iq,nq)]+= q[ADDR(2,iq,nq)]*q[ADDR(2,iq,nq)];
            aux[ADDR(1,iq,nq)]*= 0.5;
//    speed of sound and total entalpy
            aux[ADDR(2,iq,nq)]= gam*rg* q[ADDR(3,iq,nq)];
            aux[ADDR(3,iq,nq)]= aux[ADDR(2,iq,nq)]/(gam-1)+ aux[ADDR(1,iq,nq)];
            aux[ADDR(2,iq,nq)]= sqrt( aux[ADDR(2,iq,nq)] );
            aux[ADDR(4,iq,nq)]= cp;
            aux[ADDR(5,iq,nq)]= mu;
            aux[ADDR(6,iq,nq)]= kappa;
        }
         #pragma acc exit data delete(this)
     }
      else if(arch=="h")
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
//    density
            aux_view(0,iq)= q_view(4,iq)/( rg*q_view(3,iq) );
//    kinetic energy
            aux_view(1,iq)=  q_view(0,iq)*q_view(0,iq);
            aux_view(1,iq)+= q_view(1,iq)*q_view(1,iq);
            aux_view(1,iq)+= q_view(2,iq)*q_view(2,iq);
            aux_view(1,iq)*= 0.5;
//    speed of sound and total entalpy
            aux_view(2,iq)= gam*rg* q_view(3,iq); 
            aux_view(3,iq)= aux_view(2,iq)/(gam-1)+ aux_view(1,iq);
            aux_view(2,iq)= sqrt( aux_view(2,iq) );
            aux_view(4,iq)= cp;
            aux_view(5,iq)= mu;
            aux_view(6,iq)= kappa;
        } 
     }
      else
     {
         cout << "unkown arch\n"; 
         assert(0);
     }
  }

//compute mu using sutherland's law
//   void cMfRoeGas::auxv3( Int iqs, Int iqe, Real *q[], Real *aux[] )
//  {
//      Int iq; 
//      Int iv;
//      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;
//      //Real            l=1.512041288e-6;
//      //Real            c = 120.;
//      Real            l = 1.45e-6;
//      Real            c = 110.;
//
//      kappa/= (unit[0]*unit[0]*unit[0]);
//      Real cp=rg*gam/(gam-1);
//      
//      for( iq=iqs;iq<iqe;iq++ )
//     {
//         //suhterland's law
//         mu = l*pow(q[3][iq], 1.5)/(q[3][iq]+c);
//         mu/= unit[0];
//
//// density
//         aux[0][iq]= q[4][iq]/( rg*q[3][iq] );
//// kinetic energy
//         aux[1][iq]=  q[0][iq]*q[0][iq];
//         aux[1][iq]+= q[1][iq]*q[1][iq];
//         aux[1][iq]+= q[2][iq]*q[2][iq];
//         aux[1][iq]*= 0.5;
//// speed of sound and total entalpy
//         aux[2][iq]= gam*rg* q[3][iq]; 
//         aux[3][iq]= aux[2][iq]/(gam-1)+ aux[1][iq];
//         aux[2][iq]= sqrt( aux[2][iq] );
//         aux[4][iq]= cp;
//         aux[5][iq]= mu;
//         aux[6][iq]= kappa;
//     } 
//  }

   void cMfRoeGas::cnsv3( Int iqs, Int iqe, Real *q[], Real *aux[], Real *qo[] )
  {

      Int iq,ia; 

      for( iq=iqs;iq<iqe;iq++ )
     {
// density
         qo[0][iq]= aux[0][iq];
         qo[1][iq]= qo[0][iq]*q[0][iq];
         qo[2][iq]= qo[0][iq]*q[1][iq];
         qo[3][iq]= qo[0][iq]*q[2][iq];
         qo[4][iq]= qo[0][iq]*aux[3][iq]- q[4][iq];
         for( ia=5;ia<nv;ia++ )
        {
            qo[ia][iq]= qo[0][iq]*q[ia][iq];
        }
     } 
  }

   void cMfRoeGas::cnsv3( Int iqs, Int iqe, Real *sq, Real *saux, Real *sqo, Int nq )
  {

      Int iq,ia; 

      #pragma acc enter data copyin(this)
      #pragma acc parallel loop \
       present(sq[0:nv*nq],saux[0:naux*nq],sqo[0:nv*nq],this) \
       default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
// density
         sqo[ADDR(0,iq,nq)]= saux[ADDR(0,iq,nq)];
         sqo[ADDR(1,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(0,iq,nq)];
         sqo[ADDR(2,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(1,iq,nq)];
         sqo[ADDR(3,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(2,iq,nq)];
         sqo[ADDR(4,iq,nq)]= sqo[ADDR(0,iq,nq)]*saux[ADDR(3,iq,nq)]- sq[ADDR(4,iq,nq)];
         for( ia=5;ia<nv;ia++ )
        {
            //qo[ia][iq]= qo[0][iq]*q[ia][iq];
            sqo[ADDR(ia,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(ia,iq,nq)];
        }
     } 
      #pragma acc exit data delete(this)
  }

   void cMfRoeGas::cnsv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch )
  {      
      Int iq,ia;
      Int nq;
      Real *sq, *saux, *sqo;
        
      if(arch == "d")
     { 
         nq = q.get_dim1();
   
         sq   = q.get_data();
         saux = aux.get_data();
         sqo  = qo.get_data();
   
         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          present(sq[0:nv*nq],saux[0:naux*nq],sqo[0:nv*nq],this) \
          default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            // density
            sqo[ADDR(0,iq,nq)]= saux[ADDR(0,iq,nq)];
            sqo[ADDR(1,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(0,iq,nq)];
            sqo[ADDR(2,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(1,iq,nq)];
            sqo[ADDR(3,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(2,iq,nq)];
            sqo[ADDR(4,iq,nq)]= sqo[ADDR(0,iq,nq)]*saux[ADDR(3,iq,nq)]- sq[ADDR(4,iq,nq)];
            for( ia=5;ia<nv;ia++ )
           {
               //qo[ia][iq]= qo[0][iq]*q[ia][iq];
               sqo[ADDR(ia,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(ia,iq,nq)];
           }
        }
         #pragma acc exit data delete(this)
     }
      else if(arch == "h")
     { 
         for( iq=iqs;iq<iqe;iq++ )
        {
            // density
            qo(0,iq)= aux(0,iq);
            qo(1,iq)= qo(0,iq)*q(0,iq);
            qo(2,iq)= qo(0,iq)*q(1,iq);
            qo(3,iq)= qo(0,iq)*q(2,iq);
            qo(4,iq)= qo(0,iq)*aux(3,iq)- q(4,iq);
            for( ia=5;ia<nv;ia++ )
           {
               //qo[ia][iq]= qo[0][iq]*q[ia][iq];
               qo(ia,iq)= qo(0,iq)*q(ia,iq);
           }
        }
     }
      else
     {
         assert(0);
     }
  }

   void cMfRoeGas::cnsv3_z( Int iqs, Int iqe, Real *q[], Real *dq[], Real *qo[] )
  {
      Int iq,ia; 
      Real u, v, w, t, p, rho, k;
      Real du, dv, dw, dt, dp, drho, dk;
      Real gam1 = 1/(gam-1);


      for( iq=iqs;iq<iqe;iq++ )
     {
         u = q[0][iq];
         v = q[1][iq];
         w = q[2][iq];
         t = q[3][iq];
         p = q[4][iq];
         rho = p/(rg*t);

         du = dq[0][iq];
         dv = dq[1][iq];
         dw = dq[2][iq];
         dt = dq[3][iq];
         dp = dq[4][iq];
         drho = (1/rg)*( -dt*p/(t*t) + dp/t );

         k = 0.5*(u*u + v*v + w*w);
         dk = du*u + dv*v + dw*w;

         qo[0][iq]= drho;
         qo[1][iq]= drho*u + du*rho;
         qo[2][iq]= drho*v + dv*rho;
         qo[3][iq]= drho*w + dw*rho;
         qo[4][iq] = gam1*dp + rho*dk + k*drho;
         for( ia=5;ia<nv;ia++ )
        {
            qo[ia][iq]= rho*dq[ia][iq] + drho*q[ia][iq];
        }
     } 
  }

   void cMfRoeGas::csv_to_pri3( Int iqs, Int iqe, Real *qo[], Real *q[] )
  {

      Int iq,ia; 
      Real rho, k;

      for( iq=iqs;iq<iqe;iq++ )
     {
         rho = qo[0][iq];

         //ux
         q[0][iq] = qo[1][iq]/rho;
         //uy
         q[1][iq] = qo[2][iq]/rho;
         //uy
         q[2][iq] = qo[3][iq]/rho;

         k = 0.5*(q[0][iq]*q[0][iq] + q[1][iq]*q[1][iq] + q[2][iq]*q[2][iq]);

         //p (gam-1)*(rhoE - rho*k)
         q[4][iq] = (gam-1.)*(qo[4][iq] - rho*k);

         //T
         q[3][iq] = q[4][iq]/(rg*rho); 

         for( ia=5;ia<nv;ia++ )
        {
            q[ia][iq]= qo[ia][iq]/rho;
        }
     } 
  }

   //void cMfRoeGas::wflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],  
   //                                         Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
   //                                         Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cMfRoeGas::wflx33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *srhsl,  
                                            Int *icqr, Real *sqr, Real *sauxr, Real *srhsr, 
                                            Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Real f[MxNVs],wn[4];
      Int  ic,iql,iqr,nql,nqr;
      nql = nfc;
      nqr = nq;
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop \
       firstprivate(nql,nqr) \
       private(f,wn)\
       present(            sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left
         //f[1]=    wc[0][ic]* qr[4][iqr]* wc[3][ic];
         //f[2]=    wc[1][ic]* qr[4][iqr]* wc[3][ic];
         //f[3]=    wc[2][ic]* qr[4][iqr]* wc[3][ic];
         //f[4]=  wxdc[0][ic]* qr[4][iqr]* wc[3][ic];
         f[1]=    wn[0]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[2]=    wn[1]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[3]=    wn[2]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[4]=  swxdc[ADDR(0,ic,nfc)]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         //auxc[nauxf-1][ic]= ( auxr[2][iqr]+fabs(wxdc[0][ic]) )*wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]= ( sauxr[ADDR(2,iqr,nqr)]+fabs(swxdc[ADDR(0,ic,nfc)]) )*wn[3];

         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nql)]-= f[4];

         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nqr)]+= f[4];

     }
  }

   void cMfRoeGas::wflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,  
                                            cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr, 
                                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real f[MxNVs],wn[4];
      Int  ic,iql,iqr,nql,nqr;

      Int nfc, nq;    
      Int *icql;      
      Real *sql, *sauxl, *srhsl;
      Int *icqr;
      Real *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq = qr.get_dim1();
  
      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data(); 
      sauxr = auxr.get_data(); 
      srhsr = rhsr.get_data();
      swc   = wc.get_data(); 
      swxdc = wxdc.get_data(); 
      sauxc = auxc.get_data();

      nql = nfc;
      nqr = nq;
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop \
       firstprivate(nql,nqr) \
       private(f,wn)\
       present(            sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left
         //f[1]=    wc[0][ic]* qr[4][iqr]* wc[3][ic];
         //f[2]=    wc[1][ic]* qr[4][iqr]* wc[3][ic];
         //f[3]=    wc[2][ic]* qr[4][iqr]* wc[3][ic];
         //f[4]=  wxdc[0][ic]* qr[4][iqr]* wc[3][ic];
         f[1]=    wn[0]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[2]=    wn[1]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[3]=    wn[2]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[4]=  swxdc[ADDR(0,ic,nfc)]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         //auxc[nauxf-1][ic]= ( auxr[2][iqr]+fabs(wxdc[0][ic]) )*wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]= ( sauxr[ADDR(2,iqr,nqr)]+fabs(swxdc[ADDR(0,ic,nfc)]) )*wn[3];

         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nql)]-= f[4];

         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nqr)]+= f[4];
     }
  }

   //void cMfRoeGas::iflxmuscl33( Int ics,Int ice, Int *icql, Int idl, Real *xql[], Real *ql0[], Real *dxdxl[], Real **dqdxl[], Real *auxl0[], Real *rhsl[],
   //                                              Int *icqr, Int idr, Real *xqr[], Real *qr0[], Real *dxdxr[], Real **dqdxr[], Real *auxr0[], Real *rhsr[], 
   //                                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd ) 
   void cMfRoeGas::iflxmuscl33( Int ics,Int ice, Int *icql, Int idl, Real *sxql, Real *sql, Real *sdxdxl, Real *sdqdxl, Real *sauxl, Real *srhsl,
                                                 Int *icqr, Int idr, Real *sxqr, Real *sqr, Real *sdxdxr, Real *sdqdxr, Real *sauxr, Real *srhsr, 
                                                 Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq, Int iorder ) 
  {
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Int             ix,iql,iqr,ia;
      Real            xn[3],wn[4];
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs],ql[MxNVs],dhl,dal;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs],qr[MxNVs],dhr,dar;
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa;
      Real            dur[3],dunr,dpr,drr,dtr,tr,dt;
      Real            dwl[MxNVs],dwr[MxNVs];
      Real            mr,ml,wl,wr,cp;
      Real            f[MxNVs], dw5[MxNVs];

      cp= rg*gam/(gam-1);
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       firstprivate(cp) \
       private(dql0,dqr0,\
               dql,dqr,\
               auxl,auxr,\
               ix,iql,iqr,ia,\
               xn,wn,\
               al,unl,rl,ll1,ll3,ll4,pl,hl,fl,ql,dhl,dal, \
               ar,unr,rr,lr1,lr3,lr4,pr,hr,fr,qr,dhr,dar, \
               le1,le3,le4, \
               aa,a2a,ra,ha,ka,qa,ana,una,unaa,raa, la1,la4,la3,lmax,fa, \
               dw1,dw3,dw4,dw2,dw2a,dr,dq,dun,dp,dpa, \
               dur,dunr,dpr,drr,dtr,tr,dt, \
               dwl,dwr, \
               mr,ml,wl,wr,\
               f, dw5 ) \ 
       present(              sxql[0:nx*nfc],sql[0:nv*nfc],sdxdxl[0:nx*nx*nfc],sdqdxl[0:nv*nx*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sxqr[0:nx*nq] ,sqr[0:nv*nq], sdxdxr[0:nx*nx*nq], sdqdxr[0:nv*nx*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)

      for( Int ic=ics;ic<ice;ic++ )
     {
         //wn[0]= wc[0][ic];
         //wn[1]= wc[1][ic];
         //wn[2]= wc[2][ic];
         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

         //xn[0]= xc[0][ic];
         //xn[1]= xc[1][ic];
         //xn[2]= xc[2][ic];
         xn[0]= sxc[ADDR(0,ic,nfc)];
         xn[1]= sxc[ADDR(1,ic,nfc)];
         xn[2]= sxc[ADDR(2,ic,nfc)];

         iql= ic;
         //iqr= icqr[ic];
         iqr= icqr[ADDR(0,ic,nfc)];
//         deltq( iql,idl,sxql,sql,sdxdxl,sdqdxl, iqr,idr,sxqr,sqr,sdxdxr,sdqdxr, xn,wn, dql0,dqr0, dql,dqr,nfc,nq );
         deltq( nv, iql, sxql, sql, sdxdxl, sdqdxl,
                    iqr, sxqr, sqr, sdxdxr, sdqdxr,
                    xn, wn,  dql0, dqr0, dql, dqr, nfc, nq );

         for( ia=0;ia<nv;ia++ )
        {
            //ql[ia]= ql0[ia][iql]+ dql[ia];
            //qr[ia]= qr0[ia][iqr]+ dqr[ia];
            ql[ia]= sql[ADDR(ia,iql,nfc)]+ (iorder-1)*dql[ia];
            qr[ia]= sqr[ADDR(ia,iqr,nq)] + (iorder-1)*dqr[ia];
        }


// left state - auxiliary variables
         auxl[0]=  ql[4]/( rg*ql[3] );
         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;
         auxl[2]= gam*rg* ql[3]; 
         auxl[3]= auxl[2]/(gam-1)+ auxl[1];
         auxl[2]= sqrt( auxl[2] );

// right state - auxiliary variables
         auxr[0]= qr[4]/( rg*qr[3] );
         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;
         auxr[2]= gam*rg* qr[3]; 
         auxr[3]= auxr[2]/(gam-1)+ auxr[1];
         auxr[2]= sqrt( auxr[2] );

// fluxes from the left
         pl=   ql[4];
         rl=   auxl[0];
         al=   auxl[2];
         hl=   auxl[3];
         //unl=  wc[0][ic]*ql[0];
         //unl+= wc[1][ic]*ql[1];
         //unl+= wc[2][ic]*ql[2];
         unl=  wn[0]*ql[0];
         unl+= wn[1]*ql[1];
         unl+= wn[2]*ql[2];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0]+   wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1]+   wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2]+   wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+    wxdc[0][ic]*pl;
         fl[1]= fl[0]*ql[0]+   wn[0]*pl;
         fl[2]= fl[0]*ql[1]+   wn[1]*pl;
         fl[3]= fl[0]*ql[2]+   wn[2]*pl;
         fl[4]= fl[0]*hl+    swxdc[ADDR(0,ic,nfc)]*pl;
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia]; }

// fluxes from the right
         rr= auxr[0];
         pr= qr[4];
         rr= auxr[0];
         ar= auxr[2];
         hr= auxr[3];
         //unr=  wc[0][ic]*qr[0];
         //unr+= wc[1][ic]*qr[1];
         //unr+= wc[2][ic]*qr[2];
         unr=  wn[0]*qr[0];
         unr+= wn[1]*qr[1];
         unr+= wn[2]*qr[2];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         //fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+    wxdc[0][ic]*pr;
         fr[1]= fr[0]*qr[0]+ wn[0]*pr;
         fr[2]= fr[0]*qr[1]+ wn[1]*pr;
         fr[3]= fr[0]*qr[2]+ wn[2]*pr;
         fr[4]= fr[0]*hr+  swxdc[ADDR(0,ic,nfc)]*pr;
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];
         ha= wl*hl+ wr*hr;
         ka*= 0.5; 
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps*lmax, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps*lmax, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= la1;
         //auxc[4][ic]= la3;
         //auxc[5][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= la1;
         sauxc[ADDR(4,ic,nfc)]= la3;
         sauxc[ADDR(5,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0]- ql[0];
         dq[1]= qr[1]- ql[1];
         dq[2]= qr[2]- ql[2];
         dq[3]= qr[3]- ql[3];
         dq[4]= qr[4]- ql[4];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= qr[ia]- ql[ia];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;
         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];
         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];
         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //rhsl[0][iql]-= f[0];
         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhsl[ADDR_(0,iql,nfc)]-= f[0];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nfc)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nfc)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nfc)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nfc)]-= f[4];

         #pragma acc atomic
         srhsr[ADDR_(0,iqr,nq)]+= f[0];
         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nq)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nq)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nq)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nq)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic
            srhsl[ADDR_(ia,iql,nfc)]-= f[ia];
            #pragma acc atomic
            srhsr[ADDR_(ia,iqr,nq)] += f[ia];
        }

         //auxc[nauxf-1][ic]*= wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]*= wn[3];
     }
      #pragma acc exit data delete(this)
  }

   void cMfRoeGas::iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icql_view, Int idl, cAu3xView<Real>& xql, cAu3xView<Real>& ql0, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& auxl0, cAu3xView<Real>& rhsl,
                                                 cAu3xView<Int>& icqr_view, Int idr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr0, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& auxr0, cAu3xView<Real>& rhsr,
                                                 cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder )
  {
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Int             ix,iql,iqr,ia;
      Real            xn[3],wn[4];
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs],ql[MxNVs],dhl,dal;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs],qr[MxNVs],dhr,dar;
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa;
      Real            dur[3],dunr,dpr,drr,dtr,tr,dt;
      Real            dwl[MxNVs],dwr[MxNVs];
      Real            mr,ml,wl,wr,cp;
      Real            f[MxNVs], dw5[MxNVs];

      Int nfc, nq;
      Int *icql;
      Real *sxql, *sql, *sdxdxl, *sdqdxl, *sauxl, *srhsl;
      Int *icqr; 
      Real *sxqr, *sqr, *sdxdxr, *sdqdxr, *sauxr, *srhsr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr0.get_dim1();

      icql   = icql_view.get_data();
      sxql   = xql.get_data();
      sql    = ql0.get_data();
      sdxdxl = dxdxl.get_data();
      sdqdxl = dqdxl.get_data();
      sauxl  = auxl0.get_data();
      srhsl  = rhsl.get_data();
      icqr   = icqr_view.get_data();
      sxqr   = xqr.get_data();
      sqr    = qr0.get_data();
      sdxdxr = dxdxr.get_data();
      sdqdxr = dqdxr.get_data();
      sauxr  = auxr0.get_data();
      srhsr  = rhsr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      cp= rg*gam/(gam-1);
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       firstprivate(cp) \
       private(dql0,dqr0,\
               dql,dqr,\
               auxl,auxr,\
               ix,iql,iqr,ia,\
               xn,wn,\
               al,unl,rl,ll1,ll3,ll4,pl,hl,fl,ql,dhl,dal, \
               ar,unr,rr,lr1,lr3,lr4,pr,hr,fr,qr,dhr,dar, \
               le1,le3,le4, \
               aa,a2a,ra,ha,ka,qa,ana,una,unaa,raa, la1,la4,la3,lmax,fa, \
               dw1,dw3,dw4,dw2,dw2a,dr,dq,dun,dp,dpa, \
               dur,dunr,dpr,drr,dtr,tr,dt, \
               dwl,dwr, \
               mr,ml,wl,wr,\
               f, dw5 ) \ 
       present(              sxql[0:nx*nfc],sql[0:nv*nfc],sdxdxl[0:nx*nx*nfc],sdqdxl[0:nv*nx*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sxqr[0:nx*nq] ,sqr[0:nv*nq], sdxdxr[0:nx*nx*nq], sdqdxr[0:nv*nx*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)
      for( Int ic=ics;ic<ice;ic++ )
     {
         //wn[0]= wc[0][ic];
         //wn[1]= wc[1][ic];
         //wn[2]= wc[2][ic];
         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

         //xn[0]= xc[0][ic];
         //xn[1]= xc[1][ic];
         //xn[2]= xc[2][ic];
         xn[0]= sxc[ADDR(0,ic,nfc)];
         xn[1]= sxc[ADDR(1,ic,nfc)];
         xn[2]= sxc[ADDR(2,ic,nfc)];

         iql= ic;
         //iqr= icqr[ic];
         iqr= icqr[ADDR(0,ic,nfc)];
//         deltq( iql,idl,sxql,sql,sdxdxl,sdqdxl, iqr,idr,sxqr,sqr,sdxdxr,sdqdxr, xn,wn, dql0,dqr0, dql,dqr,nfc,nq );
         deltq( nv, iql, sxql, sql, sdxdxl, sdqdxl,
                    iqr, sxqr, sqr, sdxdxr, sdqdxr,
                    xn, wn,  dql0, dqr0, dql, dqr, nfc, nq );

         for( ia=0;ia<nv;ia++ )
        {
            //ql[ia]= ql0[ia][iql]+ dql[ia];
            //qr[ia]= qr0[ia][iqr]+ dqr[ia];
            ql[ia]= sql[ADDR(ia,iql,nfc)]+ (iorder-1)*dql[ia];
            qr[ia]= sqr[ADDR(ia,iqr,nq)] + (iorder-1)*dqr[ia];
        }


// left state - auxiliary variables
         auxl[0]=  ql[4]/( rg*ql[3] );
         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;
         auxl[2]= gam*rg* ql[3]; 
         auxl[3]= auxl[2]/(gam-1)+ auxl[1];
         auxl[2]= sqrt( auxl[2] );

// right state - auxiliary variables
         auxr[0]= qr[4]/( rg*qr[3] );
         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;
         auxr[2]= gam*rg* qr[3]; 
         auxr[3]= auxr[2]/(gam-1)+ auxr[1];
         auxr[2]= sqrt( auxr[2] );

// fluxes from the left
         pl=   ql[4];
         rl=   auxl[0];
         al=   auxl[2];
         hl=   auxl[3];
         //unl=  wc[0][ic]*ql[0];
         //unl+= wc[1][ic]*ql[1];
         //unl+= wc[2][ic]*ql[2];
         unl=  wn[0]*ql[0];
         unl+= wn[1]*ql[1];
         unl+= wn[2]*ql[2];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0]+   wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1]+   wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2]+   wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+    wxdc[0][ic]*pl;
         fl[1]= fl[0]*ql[0]+   wn[0]*pl;
         fl[2]= fl[0]*ql[1]+   wn[1]*pl;
         fl[3]= fl[0]*ql[2]+   wn[2]*pl;
         fl[4]= fl[0]*hl+    swxdc[ADDR(0,ic,nfc)]*pl;
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia]; }

// fluxes from the right
         rr= auxr[0];
         pr= qr[4];
         rr= auxr[0];
         ar= auxr[2];
         hr= auxr[3];
         //unr=  wc[0][ic]*qr[0];
         //unr+= wc[1][ic]*qr[1];
         //unr+= wc[2][ic]*qr[2];
         unr=  wn[0]*qr[0];
         unr+= wn[1]*qr[1];
         unr+= wn[2]*qr[2];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         //fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+    wxdc[0][ic]*pr;
         fr[1]= fr[0]*qr[0]+ wn[0]*pr;
         fr[2]= fr[0]*qr[1]+ wn[1]*pr;
         fr[3]= fr[0]*qr[2]+ wn[2]*pr;
         fr[4]= fr[0]*hr+  swxdc[ADDR(0,ic,nfc)]*pr;
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];
         ha= wl*hl+ wr*hr;
         ka*= 0.5; 
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps*lmax, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps*lmax, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= la1;
         //auxc[4][ic]= la3;
         //auxc[5][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= la1;
         sauxc[ADDR(4,ic,nfc)]= la3;
         sauxc[ADDR(5,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0]- ql[0];
         dq[1]= qr[1]- ql[1];
         dq[2]= qr[2]- ql[2];
         dq[3]= qr[3]- ql[3];
         dq[4]= qr[4]- ql[4];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= qr[ia]- ql[ia];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;
         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];
         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];
         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //rhsl[0][iql]-= f[0];
         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhsl[ADDR_(0,iql,nfc)]-= f[0];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nfc)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nfc)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nfc)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nfc)]-= f[4];

         #pragma acc atomic
         srhsr[ADDR_(0,iqr,nq)]+= f[0];
         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nq)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nq)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nq)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nq)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic
            srhsl[ADDR_(ia,iql,nfc)]-= f[ia];
            #pragma acc atomic
            srhsr[ADDR_(ia,iqr,nq)] += f[ia];
        }

         //auxc[nauxf-1][ic]*= wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]*= wn[3];
     }
      #pragma acc exit data delete(this)
  }

   void cMfRoeGas::iflxmuscl33( Int ics,Int ice, Int *sicq,  Real *sxq, Real *sq, Real *sdxdx, Real *sdqdx, Real *saux, Real *srhs,
                                Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq, Int iorder ) 
  {
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Int             ix,iql,iqr,ia;
      Real            xn[3],wn[4];
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs],ql[MxNVs],dhl,dal;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs],qr[MxNVs],dhr,dar;
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa;
      Real            dur[3],dunr,dpr,drr,dtr,tr,dt;
      Real            dwl[MxNVs],dwr[MxNVs];
      Real            mr,ml,wl,wr,cp;
      Real            f[MxNVs], dw5[MxNVs];

      cp= rg*gam/(gam-1);
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       firstprivate(cp), \
       private(dql0,dqr0,\
               dql,dqr,\
               auxl,auxr,\
               ix,iql,iqr,ia,\
               xn,wn,\
               al,unl,rl,ll1,ll3,ll4,pl,hl,fl,ql,dhl,dal, \
               ar,unr,rr,lr1,lr3,lr4,pr,hr,fr,qr,dhr,dar, \
               le1,le3,le4, \
               aa,a2a,ra,ha,ka,qa,ana,una,unaa,raa, la1,la4,la3,lmax,fa, \
               dw1,dw3,dw4,dw2,dw2a,dr,dq,dun,dp,dpa, \
               dur,dunr,dpr,drr,dtr,tr,dt, \
               dwl,dwr, \
               mr,ml,wl,wr,\
               f, dw5 ) \ 
       present(sq[0:nv*nq],sauxc[0:nauxf*nfc], saux[0:naux*nq], \
                       sxq[0:nx*nq],srhs[0:nv*nq],sdqdx[0:nv*nx*nq],sicq[0:2*nfc], \
                       sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sdxdx[0:nx*nx*nq],this) \
       default(none)

      for( Int ic=ics;ic<ice;ic++ )
     {
         //wn[0]= wc[0][ic];
         //wn[1]= wc[1][ic];
         //wn[2]= wc[2][ic];
         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];
         //xn[0]= xc[0][ic];
         //xn[1]= xc[1][ic];
         //xn[2]= xc[2][ic];
         xn[0]= sxc[ADDR(0,ic,nfc)];
         xn[1]= sxc[ADDR(1,ic,nfc)];
         xn[2]= sxc[ADDR(2,ic,nfc)];

         //iql = icq[0][ic];
         //iqr = icq[1][ic];
         iql = sicq[ADDR(0,ic,nfc)];
         iqr = sicq[ADDR(1,ic,nfc)];

         //dq0 is not used here
         //grd->deltq( iql,iqr,sxq,sq,sdxdx,sdqdx,xn,wn,dql0,dqr0,dql,dqr );
         deltq( nv,iql,iqr,sxq,sq,sdxdx,sdqdx,xn,wn,dql0,dqr0,dql,dqr,nq );


         for( ia=0;ia<nv;ia++ )
        {
            //ql[ia]= q0[ia][iql]+ dql[ia];
            //qr[ia]= q0[ia][iqr]+ dqr[ia];
            ql[ia]= sq[ADDR(ia,iql,nq)]+ (iorder-1)*dql[ia];
            qr[ia]= sq[ADDR(ia,iqr,nq)]+ (iorder-1)*dqr[ia];
        }


// left state - auxiliary variables
         auxl[0]=  ql[4]/( rg*ql[3] );
         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;
         auxl[2]= gam*rg* ql[3]; 
         auxl[3]= auxl[2]/(gam-1)+ auxl[1];
         auxl[2]= sqrt( auxl[2] );

// right state - auxiliary variables
         auxr[0]= qr[4]/( rg*qr[3] );
         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;
         auxr[2]= gam*rg* qr[3]; 
         auxr[3]= auxr[2]/(gam-1)+ auxr[1];
         auxr[2]= sqrt( auxr[2] );

// fluxes from the left
         pl=   ql[4];
         rl=   auxl[0];
         al=   auxl[2];
         hl=   auxl[3];
         //unl=  wc[0][ic]*ql[0];
         //unl+= wc[1][ic]*ql[1];
         //unl+= wc[2][ic]*ql[2];
         unl=  wn[0]*ql[0];
         unl+= wn[1]*ql[1];
         unl+= wn[2]*ql[2];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0]+   wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1]+   wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2]+   wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+    wxdc[0][ic]*pl;
         fl[1]= fl[0]*ql[0]+   wn[0]*pl;
         fl[2]= fl[0]*ql[1]+   wn[1]*pl;
         fl[3]= fl[0]*ql[2]+   wn[2]*pl;
         fl[4]= fl[0]*hl+    swxdc[ADDR(0,ic,nfc)]*pl;
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia]; }

// fluxes from the right
         rr= auxr[0];
         pr= qr[4];
         rr= auxr[0];
         ar= auxr[2];
         hr= auxr[3];
         //unr=  wc[0][ic]*qr[0];
         //unr+= wc[1][ic]*qr[1];
         //unr+= wc[2][ic]*qr[2];
         unr=  wn[0]*qr[0];
         unr+= wn[1]*qr[1];
         unr+= wn[2]*qr[2];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         //fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+    wxdc[0][ic]*pr;
         fr[1]= fr[0]*qr[0]+ wn[0]*pr;
         fr[2]= fr[0]*qr[1]+ wn[1]*pr;
         fr[3]= fr[0]*qr[2]+ wn[2]*pr;
         fr[4]= fr[0]*hr+  swxdc[ADDR(0,ic,nfc)]*pr;
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];
         ha= wl*hl+ wr*hr;
         ka*= 0.5; 
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps*lmax, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps*lmax, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= la1;
         //auxc[4][ic]= la3;
         //auxc[5][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= la1;
         sauxc[ADDR(4,ic,nfc)]= la3;
         sauxc[ADDR(5,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0]- ql[0];
         dq[1]= qr[1]- ql[1];
         dq[2]= qr[2]- ql[2];
         dq[3]= qr[3]- ql[3];
         dq[4]= qr[4]- ql[4];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= qr[ia]- ql[ia];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;
         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];
         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];
         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //rhs[0][iql]-= f[0];
         //rhs[1][iql]-= f[1];
         //rhs[2][iql]-= f[2];
         //rhs[3][iql]-= f[3];
         //rhs[4][iql]-= f[4];
         #pragma acc atomic 
         srhs[ADDR_(0,iql,nq)]-= f[0];
         #pragma acc atomic 
         srhs[ADDR_(1,iql,nq)]-= f[1];
         #pragma acc atomic 
         srhs[ADDR_(2,iql,nq)]-= f[2];
         #pragma acc atomic 
         srhs[ADDR_(3,iql,nq)]-= f[3];
         #pragma acc atomic 
         srhs[ADDR_(4,iql,nq)]-= f[4];

         //rhs[0][iqr]+= f[0];
         //rhs[1][iqr]+= f[1];
         //rhs[2][iqr]+= f[2];
         //rhs[3][iqr]+= f[3];
         //rhs[4][iqr]+= f[4];
         #pragma acc atomic 
         srhs[ADDR_(0,iqr,nq)]+= f[0];
         #pragma acc atomic 
         srhs[ADDR_(1,iqr,nq)]+= f[1];
         #pragma acc atomic 
         srhs[ADDR_(2,iqr,nq)]+= f[2];
         #pragma acc atomic 
         srhs[ADDR_(3,iqr,nq)]+= f[3];
         #pragma acc atomic 
         srhs[ADDR_(4,iqr,nq)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //rhs[ia][iql]-= f[ia];
            //rhs[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic 
            srhs[ADDR_(ia,iql,nq)]-= f[ia];
            #pragma acc atomic 
            srhs[ADDR_(ia,iqr,nq)]+= f[ia];
        }

         //auxc[nauxf-1][ic]*= wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]*= wn[3];
     }
      #pragma acc exit data delete(this)
  }

   void cMfRoeGas::iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder )
  {
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Int             ix,iql,iqr,ia;
      Real            xn[3],wn[4];
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs],ql[MxNVs],dhl,dal;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs],qr[MxNVs],dhr,dar;
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa;
      Real            dur[3],dunr,dpr,drr,dtr,tr,dt;
      Real            dwl[MxNVs],dwr[MxNVs];
      Real            mr,ml,wl,wr,cp;
      Real            f[MxNVs], dw5[MxNVs];

      Int nfc, nq;
      Int *sicq;
      Real *sxq, *sq, *sdxdx, *sdqdx, *saux, *srhs;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = q.get_dim1();

      sicq  = icq.get_data();
      sxq   = xq.get_data();
      sq    = q.get_data();
      sdxdx = dxdx.get_data();
      sdqdx = dqdx.get_data();
      saux  = aux.get_data();
      srhs  = rhs.get_data();
      sxc   = xc.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      cp= rg*gam/(gam-1);
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       firstprivate(cp), \
       private(dql0,dqr0,\
               dql,dqr,\
               auxl,auxr,\
               ix,iql,iqr,ia,\
               xn,wn,\
               al,unl,rl,ll1,ll3,ll4,pl,hl,fl,ql,dhl,dal, \
               ar,unr,rr,lr1,lr3,lr4,pr,hr,fr,qr,dhr,dar, \
               le1,le3,le4, \
               aa,a2a,ra,ha,ka,qa,ana,una,unaa,raa, la1,la4,la3,lmax,fa, \
               dw1,dw3,dw4,dw2,dw2a,dr,dq,dun,dp,dpa, \
               dur,dunr,dpr,drr,dtr,tr,dt, \
               dwl,dwr, \
               mr,ml,wl,wr,\
               f, dw5 ) \ 
       present(sq[0:nv*nq],sauxc[0:nauxf*nfc], saux[0:naux*nq], \
                       sxq[0:nx*nq],srhs[0:nv*nq],sdqdx[0:nv*nx*nq],sicq[0:2*nfc], \
                       sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sdxdx[0:nx*nx*nq],this) \
       default(none)

      for( Int ic=ics;ic<ice;ic++ )
     {
         //wn[0]= wc[0][ic];
         //wn[1]= wc[1][ic];
         //wn[2]= wc[2][ic];
         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];
         //xn[0]= xc[0][ic];
         //xn[1]= xc[1][ic];
         //xn[2]= xc[2][ic];
         xn[0]= sxc[ADDR(0,ic,nfc)];
         xn[1]= sxc[ADDR(1,ic,nfc)];
         xn[2]= sxc[ADDR(2,ic,nfc)];

         //iql = icq[0][ic];
         //iqr = icq[1][ic];
         iql = sicq[ADDR(0,ic,nfc)];
         iqr = sicq[ADDR(1,ic,nfc)];

         //dq0 is not used here
         //grd->deltq( iql,iqr,sxq,sq,sdxdx,sdqdx,xn,wn,dql0,dqr0,dql,dqr );
         deltq( nv,iql,iqr,sxq,sq,sdxdx,sdqdx,xn,wn,dql0,dqr0,dql,dqr,nq );


         for( ia=0;ia<nv;ia++ )
        {
            //ql[ia]= q0[ia][iql]+ dql[ia];
            //qr[ia]= q0[ia][iqr]+ dqr[ia];
            ql[ia]= sq[ADDR(ia,iql,nq)]+ (iorder-1)*dql[ia];
            qr[ia]= sq[ADDR(ia,iqr,nq)]+ (iorder-1)*dqr[ia];
        }


// left state - auxiliary variables
         auxl[0]=  ql[4]/( rg*ql[3] );
         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;
         auxl[2]= gam*rg* ql[3]; 
         auxl[3]= auxl[2]/(gam-1)+ auxl[1];
         auxl[2]= sqrt( auxl[2] );

// right state - auxiliary variables
         auxr[0]= qr[4]/( rg*qr[3] );
         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;
         auxr[2]= gam*rg* qr[3]; 
         auxr[3]= auxr[2]/(gam-1)+ auxr[1];
         auxr[2]= sqrt( auxr[2] );

// fluxes from the left
         pl=   ql[4];
         rl=   auxl[0];
         al=   auxl[2];
         hl=   auxl[3];
         //unl=  wc[0][ic]*ql[0];
         //unl+= wc[1][ic]*ql[1];
         //unl+= wc[2][ic]*ql[2];
         unl=  wn[0]*ql[0];
         unl+= wn[1]*ql[1];
         unl+= wn[2]*ql[2];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0]+   wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1]+   wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2]+   wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+    wxdc[0][ic]*pl;
         fl[1]= fl[0]*ql[0]+   wn[0]*pl;
         fl[2]= fl[0]*ql[1]+   wn[1]*pl;
         fl[3]= fl[0]*ql[2]+   wn[2]*pl;
         fl[4]= fl[0]*hl+    swxdc[ADDR(0,ic,nfc)]*pl;
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia]; }

// fluxes from the right
         rr= auxr[0];
         pr= qr[4];
         rr= auxr[0];
         ar= auxr[2];
         hr= auxr[3];
         //unr=  wc[0][ic]*qr[0];
         //unr+= wc[1][ic]*qr[1];
         //unr+= wc[2][ic]*qr[2];
         unr=  wn[0]*qr[0];
         unr+= wn[1]*qr[1];
         unr+= wn[2]*qr[2];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         //fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+    wxdc[0][ic]*pr;
         fr[1]= fr[0]*qr[0]+ wn[0]*pr;
         fr[2]= fr[0]*qr[1]+ wn[1]*pr;
         fr[3]= fr[0]*qr[2]+ wn[2]*pr;
         fr[4]= fr[0]*hr+  swxdc[ADDR(0,ic,nfc)]*pr;
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];
         ha= wl*hl+ wr*hr;
         ka*= 0.5; 
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps*lmax, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps*lmax, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= la1;
         //auxc[4][ic]= la3;
         //auxc[5][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= la1;
         sauxc[ADDR(4,ic,nfc)]= la3;
         sauxc[ADDR(5,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0]- ql[0];
         dq[1]= qr[1]- ql[1];
         dq[2]= qr[2]- ql[2];
         dq[3]= qr[3]- ql[3];
         dq[4]= qr[4]- ql[4];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= qr[ia]- ql[ia];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;
         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];
         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];
         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //rhs[0][iql]-= f[0];
         //rhs[1][iql]-= f[1];
         //rhs[2][iql]-= f[2];
         //rhs[3][iql]-= f[3];
         //rhs[4][iql]-= f[4];
         #pragma acc atomic 
         srhs[ADDR_(0,iql,nq)]-= f[0];
         #pragma acc atomic 
         srhs[ADDR_(1,iql,nq)]-= f[1];
         #pragma acc atomic 
         srhs[ADDR_(2,iql,nq)]-= f[2];
         #pragma acc atomic 
         srhs[ADDR_(3,iql,nq)]-= f[3];
         #pragma acc atomic 
         srhs[ADDR_(4,iql,nq)]-= f[4];

         //rhs[0][iqr]+= f[0];
         //rhs[1][iqr]+= f[1];
         //rhs[2][iqr]+= f[2];
         //rhs[3][iqr]+= f[3];
         //rhs[4][iqr]+= f[4];
         #pragma acc atomic 
         srhs[ADDR_(0,iqr,nq)]+= f[0];
         #pragma acc atomic 
         srhs[ADDR_(1,iqr,nq)]+= f[1];
         #pragma acc atomic 
         srhs[ADDR_(2,iqr,nq)]+= f[2];
         #pragma acc atomic 
         srhs[ADDR_(3,iqr,nq)]+= f[3];
         #pragma acc atomic 
         srhs[ADDR_(4,iqr,nq)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //rhs[ia][iql]-= f[ia];
            //rhs[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic 
            srhs[ADDR_(ia,iql,nq)]-= f[ia];
            #pragma acc atomic 
            srhs[ADDR_(ia,iqr,nq)]+= f[ia];
        }

         //auxc[nauxf-1][ic]*= wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]*= wn[3];
     }
      #pragma acc exit data delete(this)
  }


   //void cMfRoeGas::dwflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
   //                                   Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
   //                                   Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cMfRoeGas::dwflx33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                             Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                             Real *swc, Real *swxdc, Real *sauxc, Int nbb, Int nq ) 
  {
      Real            dpr;

      Int             ia,ic,iql,iqr;
      Real            f[MxNVs],wn[4];
      Int             nfc,nqr;

      nfc = nbb;
      nqr = nq;
     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      private(f,wn)\
      present(sql[0:nv*nfc],sauxl[0:naux*nfc],sdql[0:nv*nfc],sdauxl[0:nv*nfc],sresl[0:nv*nfc],\
              icqr[0:nfc],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
              swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic];

         wn[0] = swc[ADDR(0,ic,nfc)];
         wn[1] = swc[ADDR(1,ic,nfc)];
         wn[2] = swc[ADDR(2,ic,nfc)];
         wn[3] = swc[ADDR(3,ic,nfc)];
         //dpr=  dauxr[4][iqr];
         //f[1]=   wc[0][ic]*dpr*wc[3][ic];
         //f[2]=   wc[1][ic]*dpr*wc[3][ic];
         //f[3]=   wc[2][ic]*dpr*wc[3][ic];
         //f[4]= wxdc[0][ic]*dpr*wc[3][ic];
         dpr=  sdauxr[ADDR(4,iqr,nqr)];
         f[1]=   wn[0]*dpr*wn[3];
         f[2]=   wn[1]*dpr*wn[3];
         f[3]=   wn[2]*dpr*wn[3];
         f[4]= swxdc[ADDR(0,ic,nfc)]*dpr*wn[3];

         //resl[1][iql]-= f[1];
         //resl[2][iql]-= f[2];
         //resl[3][iql]-= f[3];
         //resl[4][iql]-= f[4];
         #pragma acc atomic
         sresl[ADDR_(1,iql,nfc)]-= f[1];
         #pragma acc atomic
         sresl[ADDR_(2,iql,nfc)]-= f[2];
         #pragma acc atomic
         sresl[ADDR_(3,iql,nfc)]-= f[3];
         #pragma acc atomic
         sresl[ADDR_(4,iql,nfc)]-= f[4];

         //resr[1][iqr]+= f[1];
         //resr[2][iqr]+= f[2];
         //resr[3][iqr]+= f[3];
         //resr[4][iqr]+= f[4];
         #pragma acc atomic
         sresr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         sresr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         sresr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         sresr[ADDR_(4,iqr,nqr)]+= f[4];

     }
     #pragma acc exit data copyout(this)
  }

   void cMfRoeGas::dwflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                             cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                             cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            dpr;

      Int             ia,ic,iql,iqr;
      Real            f[MxNVs],wn[4];
      Int             nfc,nqr;

      Int nbb, nq;
      Int *icql; 
      Real *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr; 
      Real *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *swc, *swxdc, *sauxc;

      nbb = wc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdql   = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl  = resl.get_data();
      icqr   = icqr_view.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqr   = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr  = resr.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      nfc = nbb;
      nqr = nq;
     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      private(f,wn)\
      present(sql[0:nv*nfc],sauxl[0:naux*nfc],sdql[0:nv*nfc],sdauxl[0:nv*nfc],sresl[0:nv*nfc],\
              icqr[0:nfc],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
              swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic];

         wn[0] = swc[ADDR(0,ic,nfc)];
         wn[1] = swc[ADDR(1,ic,nfc)];
         wn[2] = swc[ADDR(2,ic,nfc)];
         wn[3] = swc[ADDR(3,ic,nfc)];
         //dpr=  dauxr[4][iqr];
         //f[1]=   wc[0][ic]*dpr*wc[3][ic];
         //f[2]=   wc[1][ic]*dpr*wc[3][ic];
         //f[3]=   wc[2][ic]*dpr*wc[3][ic];
         //f[4]= wxdc[0][ic]*dpr*wc[3][ic];
         dpr=  sdauxr[ADDR(4,iqr,nqr)];
         f[1]=   wn[0]*dpr*wn[3];
         f[2]=   wn[1]*dpr*wn[3];
         f[3]=   wn[2]*dpr*wn[3];
         f[4]= swxdc[ADDR(0,ic,nfc)]*dpr*wn[3];

         //resl[1][iql]-= f[1];
         //resl[2][iql]-= f[2];
         //resl[3][iql]-= f[3];
         //resl[4][iql]-= f[4];
         #pragma acc atomic
         sresl[ADDR_(1,iql,nfc)]-= f[1];
         #pragma acc atomic
         sresl[ADDR_(2,iql,nfc)]-= f[2];
         #pragma acc atomic
         sresl[ADDR_(3,iql,nfc)]-= f[3];
         #pragma acc atomic
         sresl[ADDR_(4,iql,nfc)]-= f[4];

         //resr[1][iqr]+= f[1];
         //resr[2][iqr]+= f[2];
         //resr[3][iqr]+= f[3];
         //resr[4][iqr]+= f[4];
         #pragma acc atomic
         sresr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         sresr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         sresr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         sresr[ADDR_(4,iqr,nqr)]+= f[4];

     }
     #pragma acc exit data copyout(this)
  }

   //void cMfRoeGas::diflx33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
   //                                   Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
   //                                   Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cMfRoeGas::diflx33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                             Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                             Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            wl,wr,aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs],wn[4];

      Int             nql, nqr;

      nql = nfc;
      nqr = nq;
     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      firstprivate(nql,nqr), \
      private(fl,fr,qa,ana,fa,dw2,dq,dw5,f,wn)\
      present(sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
              icqr[0:nfc],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
              swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         //iqr= icqr[ic];
         iqr= icqr[ADDR(0,ic,nfc)];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left

         //pl= ql[4][iql];
         //rl= auxl[0][iql];
         //hl= auxl[3][iql];
         pl= sql[ADDR(4,iql,nql)];
         rl= sauxl[ADDR(0,iql,nql)];
         hl= sauxl[ADDR(3,iql,nql)];

         //drl=    dql[0][iql];
         //dpl=  dauxl[4][iql];
         //drel=   dql[4][iql];
         drl=    sdql[ADDR(0,iql,nql)];
         dpl=  sdauxl[ADDR(4,iql,nql)];
         drel=   sdql[ADDR(4,iql,nql)];

         //unl=  wc[0][ic]*ql[0][iql]; 
         //unl+= wc[1][ic]*ql[1][iql]; 
         //unl+= wc[2][ic]*ql[2][iql]; 
         //unl-= wxdc[0][ic];
         unl=  wn[0]*sql[ADDR(0,iql,nql)]; 
         unl+= wn[1]*sql[ADDR(1,iql,nql)]; 
         unl+= wn[2]*sql[ADDR(2,iql,nql)]; 
         unl-= swxdc[ADDR(0,ic,nfc)];

         //dunl=  wc[0][ic]*dauxl[0][iql]; 
         //dunl+= wc[1][ic]*dauxl[1][iql]; 
         //dunl+= wc[2][ic]*dauxl[2][iql]; 
         dunl=  wn[0]*sdauxl[ADDR(0,iql,nql)]; 
         dunl+= wn[1]*sdauxl[ADDR(1,iql,nql)]; 
         dunl+= wn[2]*sdauxl[ADDR(2,iql,nql)]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
        // fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
        // fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
        // fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql]+  dpl*wc[2][ic]; 
        // fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ ml*sdauxl[ADDR(0,iql,nql)]+  dpl*wn[0]; 
         fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ ml*sdauxl[ADDR(1,iql,nql)]+  dpl*wn[1]; 
         fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ ml*sdauxl[ADDR(2,iql,nql)]+  dpl*wn[2]; 
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
        //    fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
            fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)]+ ml*sdauxl[ADDR(ia,iql,nql)];
        }

// fluxes from the right

         //pr= qr[4][iqr];
         //rr= auxr[0][iqr];
         //hr= auxr[3][iqr];
         pr= sqr[ADDR(4,iqr,nqr)];
         rr= sauxr[ADDR(0,iqr,nqr)];
         hr= sauxr[ADDR(3,iqr,nqr)];

         //drr=    dqr[0][iqr];
         //dpr=  dauxr[4][iqr];
         //drer=   dqr[4][iqr];
         drr=    sdqr[ADDR(0,iqr,nqr)];
         dpr=  sdauxr[ADDR(4,iqr,nqr)];
         drer=   sdqr[ADDR(4,iqr,nqr)];

         //unr=  wc[0][ic]*qr[0][iqr]; 
         //unr+= wc[1][ic]*qr[1][iqr]; 
         //unr+= wc[2][ic]*qr[2][iqr]; 
         //unr-= wxdc[0][ic];
         unr=  wn[0]*sqr[ADDR(0,iqr,nqr)]; 
         unr+= wn[1]*sqr[ADDR(1,iqr,nqr)]; 
         unr+= wn[2]*sqr[ADDR(2,iqr,nqr)]; 
         unr-= swxdc[ADDR(0,ic,nfc)];

         //dunr=  wc[0][ic]*dauxr[0][iqr]; 
         //dunr+= wc[1][ic]*dauxr[1][iqr]; 
         //dunr+= wc[2][ic]*dauxr[2][iqr]; 
         dunr=  wn[0]*sdauxr[ADDR(0,iqr,nqr)]; 
         dunr+= wn[1]*sdauxr[ADDR(1,iqr,nqr)]; 
         dunr+= wn[2]*sdauxr[ADDR(2,iqr,nqr)]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
        // fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
        // fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
        // fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
        // fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ mr*sdauxr[ADDR(0,iqr,nqr)]+ dpr*wn[0]; 
         fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ mr*sdauxr[ADDR(1,iqr,nqr)]+ dpr*wn[1]; 
         fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ mr*sdauxr[ADDR(2,iqr,nqr)]+ dpr*wn[2]; 
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
            fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)]+ mr*sdauxr[ADDR(ia,iqr,nqr)];
        }

// retrieve Roe averages

         //wl=  auxc[0][ic];
         //wr=  auxc[1][ic];
         //ra=  auxc[2][ic];
         //la1= auxc[3][ic];
         //la3= auxc[4][ic];
         //la4= auxc[5][ic];
         wl=  sauxc[ADDR(0,ic,nfc)];
         wr=  sauxc[ADDR(1,ic,nfc)];
         ra=  sauxc[ADDR(2,ic,nfc)];
         la1= sauxc[ADDR(3,ic,nfc)];
         la3= sauxc[ADDR(4,ic,nfc)];
         la4= sauxc[ADDR(5,ic,nfc)];

         //qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         //qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         //qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         //qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
         //qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
         //for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr]; }
         qa[0]= wl*sql[ADDR(0,iql,nql)]+ wr*sqr[ADDR(0,iqr,nqr)];
         qa[1]= wl*sql[ADDR(1,iql,nql)]+ wr*sqr[ADDR(1,iqr,nqr)];
         qa[2]= wl*sql[ADDR(2,iql,nql)]+ wr*sqr[ADDR(2,iqr,nqr)];
         qa[3]= wl*sql[ADDR(3,iql,nql)]+ wr*sqr[ADDR(3,iqr,nqr)];
         qa[4]= wl*sql[ADDR(4,iql,nql)]+ wr*sqr[ADDR(4,iqr,nqr)];
         for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*sql[ADDR(ia,iql,nql)]+ wr*sqr[ADDR(ia,iqr,nqr)]; }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// Left eigenvectors

         dr= drr- drl;
         dp= dpr- dpl;
         //dq[0]= dauxr[0][iqr]- dauxl[0][iql];
         //dq[1]= dauxr[1][iqr]- dauxl[1][iql];
         //dq[2]= dauxr[2][iqr]- dauxl[2][iql];
         //dq[3]= dauxr[3][iqr]- dauxl[3][iql];
         //dq[4]= dauxr[4][iqr]- dauxl[4][iql];
         dq[0]= sdauxr[ADDR(0,iqr,nqr)]- sdauxl[ADDR(0,iql,nql)];
         dq[1]= sdauxr[ADDR(1,iqr,nqr)]- sdauxl[ADDR(1,iql,nql)];
         dq[2]= sdauxr[ADDR(2,iqr,nqr)]- sdauxl[ADDR(2,iql,nql)];
         dq[3]= sdauxr[ADDR(3,iqr,nqr)]- sdauxl[ADDR(3,iql,nql)];
         dq[4]= sdauxr[ADDR(4,iqr,nqr)]- sdauxl[ADDR(4,iql,nql)];
         for( ia=5;ia<nv;ia++ )
        {
            //dq[ia]= dauxr[ia][iqr]- dauxl[ia][iql];
            dq[ia]= sdauxr[ADDR(ia,iqr,nqr)]- sdauxl[ADDR(ia,iql,nql)];
        }
         dun= dunr- dunl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //resl[0][iql]-= f[0];
         //resl[1][iql]-= f[1];
         //resl[2][iql]-= f[2];
         //resl[3][iql]-= f[3];
         //resl[4][iql]-= f[4];
         #pragma acc atomic
         sresl[ADDR_(0,iql,nql)]-= f[0];
         #pragma acc atomic
         sresl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         sresl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         sresl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         sresl[ADDR_(4,iql,nql)]-= f[4];

         //resr[0][iqr]+= f[0];
         //resr[1][iqr]+= f[1];
         //resr[2][iqr]+= f[2];
         //resr[3][iqr]+= f[3];
         //resr[4][iqr]+= f[4];
         #pragma acc atomic
         sresr[ADDR_(0,iqr,nqr)]+= f[0];
         #pragma acc atomic
         sresr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         sresr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         sresr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         sresr[ADDR_(4,iqr,nqr)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //resl[ia][iql]-= f[ia];
            //resr[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic
            sresl[ADDR_(ia,iql,nql)]-= f[ia];
            #pragma acc atomic
            sresr[ADDR_(ia,iqr,nqr)]+= f[ia];
        }

     }
     #pragma acc exit data delete(this)
  }

   void cMfRoeGas::diflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                             cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                             cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            wl,wr,aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs],wn[4];

      Int             nql, nqr;

      Int nfc, nq;
      Int *icql;
      Real *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdql   = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl  = resl.get_data();
      icqr   = icqr_view.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqr   = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr  = resr.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      nql = nfc;
      nqr = nq;
     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      firstprivate(nql,nqr), \
      private(fl,fr,qa,ana,fa,dw2,dq,dw5,f,wn)\
      present(sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
              icqr[0:nfc],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
              swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         //iqr= icqr[ic];
         iqr= icqr[ADDR(0,ic,nfc)];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left

         //pl= ql[4][iql];
         //rl= auxl[0][iql];
         //hl= auxl[3][iql];
         pl= sql[ADDR(4,iql,nql)];
         rl= sauxl[ADDR(0,iql,nql)];
         hl= sauxl[ADDR(3,iql,nql)];

         //drl=    dql[0][iql];
         //dpl=  dauxl[4][iql];
         //drel=   dql[4][iql];
         drl=    sdql[ADDR(0,iql,nql)];
         dpl=  sdauxl[ADDR(4,iql,nql)];
         drel=   sdql[ADDR(4,iql,nql)];

         //unl=  wc[0][ic]*ql[0][iql]; 
         //unl+= wc[1][ic]*ql[1][iql]; 
         //unl+= wc[2][ic]*ql[2][iql]; 
         //unl-= wxdc[0][ic];
         unl=  wn[0]*sql[ADDR(0,iql,nql)]; 
         unl+= wn[1]*sql[ADDR(1,iql,nql)]; 
         unl+= wn[2]*sql[ADDR(2,iql,nql)]; 
         unl-= swxdc[ADDR(0,ic,nfc)];

         //dunl=  wc[0][ic]*dauxl[0][iql]; 
         //dunl+= wc[1][ic]*dauxl[1][iql]; 
         //dunl+= wc[2][ic]*dauxl[2][iql]; 
         dunl=  wn[0]*sdauxl[ADDR(0,iql,nql)]; 
         dunl+= wn[1]*sdauxl[ADDR(1,iql,nql)]; 
         dunl+= wn[2]*sdauxl[ADDR(2,iql,nql)]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
        // fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
        // fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
        // fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql]+  dpl*wc[2][ic]; 
        // fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ ml*sdauxl[ADDR(0,iql,nql)]+  dpl*wn[0]; 
         fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ ml*sdauxl[ADDR(1,iql,nql)]+  dpl*wn[1]; 
         fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ ml*sdauxl[ADDR(2,iql,nql)]+  dpl*wn[2]; 
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
        //    fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
            fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)]+ ml*sdauxl[ADDR(ia,iql,nql)];
        }

// fluxes from the right

         //pr= qr[4][iqr];
         //rr= auxr[0][iqr];
         //hr= auxr[3][iqr];
         pr= sqr[ADDR(4,iqr,nqr)];
         rr= sauxr[ADDR(0,iqr,nqr)];
         hr= sauxr[ADDR(3,iqr,nqr)];

         //drr=    dqr[0][iqr];
         //dpr=  dauxr[4][iqr];
         //drer=   dqr[4][iqr];
         drr=    sdqr[ADDR(0,iqr,nqr)];
         dpr=  sdauxr[ADDR(4,iqr,nqr)];
         drer=   sdqr[ADDR(4,iqr,nqr)];

         //unr=  wc[0][ic]*qr[0][iqr]; 
         //unr+= wc[1][ic]*qr[1][iqr]; 
         //unr+= wc[2][ic]*qr[2][iqr]; 
         //unr-= wxdc[0][ic];
         unr=  wn[0]*sqr[ADDR(0,iqr,nqr)]; 
         unr+= wn[1]*sqr[ADDR(1,iqr,nqr)]; 
         unr+= wn[2]*sqr[ADDR(2,iqr,nqr)]; 
         unr-= swxdc[ADDR(0,ic,nfc)];

         //dunr=  wc[0][ic]*dauxr[0][iqr]; 
         //dunr+= wc[1][ic]*dauxr[1][iqr]; 
         //dunr+= wc[2][ic]*dauxr[2][iqr]; 
         dunr=  wn[0]*sdauxr[ADDR(0,iqr,nqr)]; 
         dunr+= wn[1]*sdauxr[ADDR(1,iqr,nqr)]; 
         dunr+= wn[2]*sdauxr[ADDR(2,iqr,nqr)]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
        // fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
        // fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
        // fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
        // fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ mr*sdauxr[ADDR(0,iqr,nqr)]+ dpr*wn[0]; 
         fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ mr*sdauxr[ADDR(1,iqr,nqr)]+ dpr*wn[1]; 
         fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ mr*sdauxr[ADDR(2,iqr,nqr)]+ dpr*wn[2]; 
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
            fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)]+ mr*sdauxr[ADDR(ia,iqr,nqr)];
        }

// retrieve Roe averages

         //wl=  auxc[0][ic];
         //wr=  auxc[1][ic];
         //ra=  auxc[2][ic];
         //la1= auxc[3][ic];
         //la3= auxc[4][ic];
         //la4= auxc[5][ic];
         wl=  sauxc[ADDR(0,ic,nfc)];
         wr=  sauxc[ADDR(1,ic,nfc)];
         ra=  sauxc[ADDR(2,ic,nfc)];
         la1= sauxc[ADDR(3,ic,nfc)];
         la3= sauxc[ADDR(4,ic,nfc)];
         la4= sauxc[ADDR(5,ic,nfc)];

         //qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         //qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         //qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         //qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
         //qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
         //for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr]; }
         qa[0]= wl*sql[ADDR(0,iql,nql)]+ wr*sqr[ADDR(0,iqr,nqr)];
         qa[1]= wl*sql[ADDR(1,iql,nql)]+ wr*sqr[ADDR(1,iqr,nqr)];
         qa[2]= wl*sql[ADDR(2,iql,nql)]+ wr*sqr[ADDR(2,iqr,nqr)];
         qa[3]= wl*sql[ADDR(3,iql,nql)]+ wr*sqr[ADDR(3,iqr,nqr)];
         qa[4]= wl*sql[ADDR(4,iql,nql)]+ wr*sqr[ADDR(4,iqr,nqr)];
         for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*sql[ADDR(ia,iql,nql)]+ wr*sqr[ADDR(ia,iqr,nqr)]; }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// Left eigenvectors

         dr= drr- drl;
         dp= dpr- dpl;
         //dq[0]= dauxr[0][iqr]- dauxl[0][iql];
         //dq[1]= dauxr[1][iqr]- dauxl[1][iql];
         //dq[2]= dauxr[2][iqr]- dauxl[2][iql];
         //dq[3]= dauxr[3][iqr]- dauxl[3][iql];
         //dq[4]= dauxr[4][iqr]- dauxl[4][iql];
         dq[0]= sdauxr[ADDR(0,iqr,nqr)]- sdauxl[ADDR(0,iql,nql)];
         dq[1]= sdauxr[ADDR(1,iqr,nqr)]- sdauxl[ADDR(1,iql,nql)];
         dq[2]= sdauxr[ADDR(2,iqr,nqr)]- sdauxl[ADDR(2,iql,nql)];
         dq[3]= sdauxr[ADDR(3,iqr,nqr)]- sdauxl[ADDR(3,iql,nql)];
         dq[4]= sdauxr[ADDR(4,iqr,nqr)]- sdauxl[ADDR(4,iql,nql)];
         for( ia=5;ia<nv;ia++ )
        {
            //dq[ia]= dauxr[ia][iqr]- dauxl[ia][iql];
            dq[ia]= sdauxr[ADDR(ia,iqr,nqr)]- sdauxl[ADDR(ia,iql,nql)];
        }
         dun= dunr- dunl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //resl[0][iql]-= f[0];
         //resl[1][iql]-= f[1];
         //resl[2][iql]-= f[2];
         //resl[3][iql]-= f[3];
         //resl[4][iql]-= f[4];
         #pragma acc atomic
         sresl[ADDR_(0,iql,nql)]-= f[0];
         #pragma acc atomic
         sresl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         sresl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         sresl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         sresl[ADDR_(4,iql,nql)]-= f[4];

         //resr[0][iqr]+= f[0];
         //resr[1][iqr]+= f[1];
         //resr[2][iqr]+= f[2];
         //resr[3][iqr]+= f[3];
         //resr[4][iqr]+= f[4];
         #pragma acc atomic
         sresr[ADDR_(0,iqr,nqr)]+= f[0];
         #pragma acc atomic
         sresr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         sresr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         sresr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         sresr[ADDR_(4,iqr,nqr)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //resl[ia][iql]-= f[ia];
            //resr[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic
            sresl[ADDR_(ia,iql,nql)]-= f[ia];
            #pragma acc atomic
            sresr[ADDR_(ia,iqr,nqr)]+= f[ia];
        }

     }
     #pragma acc exit data delete(this)
  }

   //void cMfRoeGas::diflx33( Int ics,Int ice, Int *icq[2], Real *q[], Real *aux[], Real *dq0[], Real *daux[], Real *res[],
   //                         Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cMfRoeGas::diflx33( Int ics,Int ice, Int *sicq, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sres,
                            Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,iql,iqr;

      Real            wl,wr,aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs],wn[4];

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      firstprivate(cp), \
      private(fl,fr,qa,ana,fa,dw2,dq,dw5,f,wn)\
      present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],\
               sres[0:nv*nq],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( Int ic=ics;ic<ice;ic++ )
     {
         //iql= icq[0][ic];
         //iqr= icq[1][ic];
         iql= sicq[ADDR(0,ic,nfc)];
         iqr= sicq[ADDR(1,ic,nfc)];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left

         //pl= q[4][iql];
         //rl= aux[0][iql];
         //hl= aux[3][iql];
         pl= sq[ADDR(4,iql,nq)];
         rl= saux[ADDR(0,iql,nq)];
         hl= saux[ADDR(3,iql,nq)];

         //drl=    dq0[0][iql];
         //dpl=  daux[4][iql];
         //drel=   dq0[4][iql];
         drl=    sdq[ADDR(0,iql,nq)];
         dpl=  sdaux[ADDR(4,iql,nq)];
         drel=   sdq[ADDR(4,iql,nq)];

         //unl=  wc[0][ic]*q[0][iql]; 
         //unl+= wc[1][ic]*q[1][iql]; 
         //unl+= wc[2][ic]*q[2][iql]; 
         //unl-= wxdc[0][ic];
         unl=  wn[0]*sq[ADDR(0,iql,nq)]; 
         unl+= wn[1]*sq[ADDR(1,iql,nq)]; 
         unl+= wn[2]*sq[ADDR(2,iql,nq)]; 
         unl-= swxdc[ADDR(0,ic,nfc)];

         //dunl=  wc[0][ic]*daux[0][iql]; 
         //dunl+= wc[1][ic]*daux[1][iql]; 
         //dunl+= wc[2][ic]*daux[2][iql]; 
         dunl=  wn[0]*sdaux[ADDR(0,iql,nq)]; 
         dunl+= wn[1]*sdaux[ADDR(1,iql,nq)]; 
         dunl+= wn[2]*sdaux[ADDR(2,iql,nq)]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         //fl[1]= fl[0]*q[0][iql]+ ml*daux[0][iql]+  dpl*wc[0][ic]; 
         //fl[2]= fl[0]*q[1][iql]+ ml*daux[1][iql]+  dpl*wc[1][ic]; 
         //fl[3]= fl[0]*q[2][iql]+ ml*daux[2][iql]+  dpl*wc[2][ic]; 
         //fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         fl[1]= fl[0]*sq[ADDR(0,iql,nq)]+ ml*sdaux[ADDR(0,iql,nq)]+  dpl*wn[0]; 
         fl[2]= fl[0]*sq[ADDR(1,iql,nq)]+ ml*sdaux[ADDR(1,iql,nq)]+  dpl*wn[1]; 
         fl[3]= fl[0]*sq[ADDR(2,iql,nq)]+ ml*sdaux[ADDR(2,iql,nq)]+  dpl*wn[2]; 
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fl[ia]= fl[0]*q[ia][iql]+ ml*daux[ia][iql];
            fl[ia]= fl[0]*sq[ADDR(ia,iql,nq)]+ ml*sdaux[ADDR(ia,iql,nq)];
        }

// fluxes from the right

         //pr= q[4][iqr];
         //rr= aux[0][iqr];
         //hr= aux[3][iqr];
         pr= sq[ADDR(4,iqr,nq)];
         rr= saux[ADDR(0,iqr,nq)];
         hr= saux[ADDR(3,iqr,nq)];

         //drr=    dq0[0][iqr];
         //dpr=  daux[4][iqr];
         //drer=   dq0[4][iqr];
         drr=    sdq[ADDR(0,iqr,nq)];
         dpr=  sdaux[ADDR(4,iqr,nq)];
         drer=   sdq[ADDR(4,iqr,nq)];

         //unr=  wc[0][ic]*q[0][iqr]; 
         //unr+= wc[1][ic]*q[1][iqr]; 
         //unr+= wc[2][ic]*q[2][iqr]; 
         //unr-= wxdc[0][ic];
         unr=  wn[0]*sq[ADDR(0,iqr,nq)]; 
         unr+= wn[1]*sq[ADDR(1,iqr,nq)]; 
         unr+= wn[2]*sq[ADDR(2,iqr,nq)]; 
         unr-= swxdc[ADDR(0,ic,nfc)];

         //dunr=  wc[0][ic]*daux[0][iqr]; 
         //dunr+= wc[1][ic]*daux[1][iqr]; 
         //dunr+= wc[2][ic]*daux[2][iqr]; 
         dunr=  wn[0]*sdaux[ADDR(0,iqr,nq)]; 
         dunr+= wn[1]*sdaux[ADDR(1,iqr,nq)]; 
         dunr+= wn[2]*sdaux[ADDR(2,iqr,nq)]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         //fr[1]= fr[0]*q[0][iqr]+ mr*daux[0][iqr]+ dpr*wc[0][ic]; 
         //fr[2]= fr[0]*q[1][iqr]+ mr*daux[1][iqr]+ dpr*wc[1][ic]; 
         //fr[3]= fr[0]*q[2][iqr]+ mr*daux[2][iqr]+ dpr*wc[2][ic]; 
         //fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         fr[1]= fr[0]*sq[ADDR(0,iqr,nq)]+ mr*sdaux[ADDR(0,iqr,nq)]+ dpr*wn[0]; 
         fr[2]= fr[0]*sq[ADDR(1,iqr,nq)]+ mr*sdaux[ADDR(1,iqr,nq)]+ dpr*wn[1]; 
         fr[3]= fr[0]*sq[ADDR(2,iqr,nq)]+ mr*sdaux[ADDR(2,iqr,nq)]+ dpr*wn[2]; 
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fr[ia]= fr[0]*q[ia][iqr]+ mr*daux[ia][iqr];
            fr[ia]= fr[0]*sq[ADDR(ia,iqr,nq)]+ mr*sdaux[ADDR(ia,iqr,nq)];
        }

// retrieve Roe averages

         //wl=  auxc[0][ic];
         //wr=  auxc[1][ic];
         //ra=  auxc[2][ic];
         //la1= auxc[3][ic];
         //la3= auxc[4][ic];
         //la4= auxc[5][ic];
         wl=  sauxc[ADDR(0,ic,nfc)];
         wr=  sauxc[ADDR(1,ic,nfc)];
         ra=  sauxc[ADDR(2,ic,nfc)];
         la1= sauxc[ADDR(3,ic,nfc)];
         la3= sauxc[ADDR(4,ic,nfc)];
         la4= sauxc[ADDR(5,ic,nfc)];

         //qa[0]= wl*q[0][iql]+ wr*q[0][iqr];
         //qa[1]= wl*q[1][iql]+ wr*q[1][iqr];
         //qa[2]= wl*q[2][iql]+ wr*q[2][iqr];
         //qa[3]= wl*q[3][iql]+ wr*q[3][iqr];
         //qa[4]= wl*q[4][iql]+ wr*q[4][iqr];
         //for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*q[ia][iql]+ wr*q[ia][iqr]; }
         qa[0]= wl*sq[ADDR(0,iql,nq)]+ wr*sq[ADDR(0,iqr,nq)];
         qa[1]= wl*sq[ADDR(1,iql,nq)]+ wr*sq[ADDR(1,iqr,nq)];
         qa[2]= wl*sq[ADDR(2,iql,nq)]+ wr*sq[ADDR(2,iqr,nq)];
         qa[3]= wl*sq[ADDR(3,iql,nq)]+ wr*sq[ADDR(3,iqr,nq)];
         qa[4]= wl*sq[ADDR(4,iql,nq)]+ wr*sq[ADDR(4,iqr,nq)];
         for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*sq[ADDR(ia,iql,nq)]+ wr*sq[ADDR(ia,iqr,nq)]; }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// Left eigenvectors

         dr= drr- drl;
         dp= dpr- dpl;
        // dq[0]= daux[0][iqr]- daux[0][iql];
        // dq[1]= daux[1][iqr]- daux[1][iql];
        // dq[2]= daux[2][iqr]- daux[2][iql];
        // dq[3]= daux[3][iqr]- daux[3][iql];
        // dq[4]= daux[4][iqr]- daux[4][iql];
        // for( ia=5;ia<nv;ia++ )
        //{
        //    dq[ia]= daux[ia][iqr]- daux[ia][iql];
        //}
         dq[0]= sdaux[ADDR(0,iqr,nq)]- sdaux[ADDR(0,iql,nq)];
         dq[1]= sdaux[ADDR(1,iqr,nq)]- sdaux[ADDR(1,iql,nq)];
         dq[2]= sdaux[ADDR(2,iqr,nq)]- sdaux[ADDR(2,iql,nq)];
         dq[3]= sdaux[ADDR(3,iqr,nq)]- sdaux[ADDR(3,iql,nq)];
         dq[4]= sdaux[ADDR(4,iqr,nq)]- sdaux[ADDR(4,iql,nq)];
         for( ia=5;ia<nv;ia++ )
        {
            //dq[ia]= daux[ia][iqr]- daux[ia][iql];
            dq[ia]= sdaux[ADDR(ia,iqr,nq)]- sdaux[ADDR(ia,iql,nq)];
        }
         dun= dunr- dunl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //res[0][iql]-= f[0];
         //res[1][iql]-= f[1];
         //res[2][iql]-= f[2];
         //res[3][iql]-= f[3];
         //res[4][iql]-= f[4];
         #pragma acc atomic
         sres[ADDR_(0,iql,nq)]-= f[0];
         #pragma acc atomic
         sres[ADDR_(1,iql,nq)]-= f[1];
         #pragma acc atomic
         sres[ADDR_(2,iql,nq)]-= f[2];
         #pragma acc atomic
         sres[ADDR_(3,iql,nq)]-= f[3];
         #pragma acc atomic
         sres[ADDR_(4,iql,nq)]-= f[4];

         //res[0][iqr]+= f[0];
         //res[1][iqr]+= f[1];
         //res[2][iqr]+= f[2];
         //res[3][iqr]+= f[3];
         //res[4][iqr]+= f[4];
         #pragma acc atomic
         sres[ADDR_(0,iqr,nq)]+= f[0];
         #pragma acc atomic
         sres[ADDR_(1,iqr,nq)]+= f[1];
         #pragma acc atomic
         sres[ADDR_(2,iqr,nq)]+= f[2];
         #pragma acc atomic
         sres[ADDR_(3,iqr,nq)]+= f[3];
         #pragma acc atomic
         sres[ADDR_(4,iqr,nq)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //res[ia][iql]-= f[ia];
            //res[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic
            sres[ADDR_(ia,iql,nq)]-= f[ia];
            #pragma acc atomic
            sres[ADDR_(ia,iqr,nq)]+= f[ia];
        }

     }
     #pragma acc exit data delete (this)
  }

   void cMfRoeGas::diflx33( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,iql,iqr;

      Real            wl,wr,aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs],wn[4];

      Int nfc, nq;    

      nfc = wc.get_dim1();
      nq  = q.get_dim1();

      Int *sicq;
      Real *sq, *saux, *sdq, *sdaux, *sres, *swc, *swxdc, *sauxc;

      sicq  = icq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      sdq   = dq0.get_data();
      sdaux = daux.get_data();
      sres  = res.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      firstprivate(cp), \
      private(fl,fr,qa,ana,fa,dw2,dq,dw5,f,wn)\
      present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],\
               sres[0:nv*nq],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( Int ic=ics;ic<ice;ic++ )
     {
         //iql= icq[0][ic];
         //iqr= icq[1][ic];
         iql= sicq[ADDR(0,ic,nfc)];
         iqr= sicq[ADDR(1,ic,nfc)];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left

         //pl= q[4][iql];
         //rl= aux[0][iql];
         //hl= aux[3][iql];
         pl= sq[ADDR(4,iql,nq)];
         rl= saux[ADDR(0,iql,nq)];
         hl= saux[ADDR(3,iql,nq)];

         //drl=    dq0[0][iql];
         //dpl=  daux[4][iql];
         //drel=   dq0[4][iql];
         drl=    sdq[ADDR(0,iql,nq)];
         dpl=  sdaux[ADDR(4,iql,nq)];
         drel=   sdq[ADDR(4,iql,nq)];

         //unl=  wc[0][ic]*q[0][iql]; 
         //unl+= wc[1][ic]*q[1][iql]; 
         //unl+= wc[2][ic]*q[2][iql]; 
         //unl-= wxdc[0][ic];
         unl=  wn[0]*sq[ADDR(0,iql,nq)]; 
         unl+= wn[1]*sq[ADDR(1,iql,nq)]; 
         unl+= wn[2]*sq[ADDR(2,iql,nq)]; 
         unl-= swxdc[ADDR(0,ic,nfc)];

         //dunl=  wc[0][ic]*daux[0][iql]; 
         //dunl+= wc[1][ic]*daux[1][iql]; 
         //dunl+= wc[2][ic]*daux[2][iql]; 
         dunl=  wn[0]*sdaux[ADDR(0,iql,nq)]; 
         dunl+= wn[1]*sdaux[ADDR(1,iql,nq)]; 
         dunl+= wn[2]*sdaux[ADDR(2,iql,nq)]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         //fl[1]= fl[0]*q[0][iql]+ ml*daux[0][iql]+  dpl*wc[0][ic]; 
         //fl[2]= fl[0]*q[1][iql]+ ml*daux[1][iql]+  dpl*wc[1][ic]; 
         //fl[3]= fl[0]*q[2][iql]+ ml*daux[2][iql]+  dpl*wc[2][ic]; 
         //fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         fl[1]= fl[0]*sq[ADDR(0,iql,nq)]+ ml*sdaux[ADDR(0,iql,nq)]+  dpl*wn[0]; 
         fl[2]= fl[0]*sq[ADDR(1,iql,nq)]+ ml*sdaux[ADDR(1,iql,nq)]+  dpl*wn[1]; 
         fl[3]= fl[0]*sq[ADDR(2,iql,nq)]+ ml*sdaux[ADDR(2,iql,nq)]+  dpl*wn[2]; 
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fl[ia]= fl[0]*q[ia][iql]+ ml*daux[ia][iql];
            fl[ia]= fl[0]*sq[ADDR(ia,iql,nq)]+ ml*sdaux[ADDR(ia,iql,nq)];
        }

// fluxes from the right

         //pr= q[4][iqr];
         //rr= aux[0][iqr];
         //hr= aux[3][iqr];
         pr= sq[ADDR(4,iqr,nq)];
         rr= saux[ADDR(0,iqr,nq)];
         hr= saux[ADDR(3,iqr,nq)];

         //drr=    dq0[0][iqr];
         //dpr=  daux[4][iqr];
         //drer=   dq0[4][iqr];
         drr=    sdq[ADDR(0,iqr,nq)];
         dpr=  sdaux[ADDR(4,iqr,nq)];
         drer=   sdq[ADDR(4,iqr,nq)];

         //unr=  wc[0][ic]*q[0][iqr]; 
         //unr+= wc[1][ic]*q[1][iqr]; 
         //unr+= wc[2][ic]*q[2][iqr]; 
         //unr-= wxdc[0][ic];
         unr=  wn[0]*sq[ADDR(0,iqr,nq)]; 
         unr+= wn[1]*sq[ADDR(1,iqr,nq)]; 
         unr+= wn[2]*sq[ADDR(2,iqr,nq)]; 
         unr-= swxdc[ADDR(0,ic,nfc)];

         //dunr=  wc[0][ic]*daux[0][iqr]; 
         //dunr+= wc[1][ic]*daux[1][iqr]; 
         //dunr+= wc[2][ic]*daux[2][iqr]; 
         dunr=  wn[0]*sdaux[ADDR(0,iqr,nq)]; 
         dunr+= wn[1]*sdaux[ADDR(1,iqr,nq)]; 
         dunr+= wn[2]*sdaux[ADDR(2,iqr,nq)]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         //fr[1]= fr[0]*q[0][iqr]+ mr*daux[0][iqr]+ dpr*wc[0][ic]; 
         //fr[2]= fr[0]*q[1][iqr]+ mr*daux[1][iqr]+ dpr*wc[1][ic]; 
         //fr[3]= fr[0]*q[2][iqr]+ mr*daux[2][iqr]+ dpr*wc[2][ic]; 
         //fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         fr[1]= fr[0]*sq[ADDR(0,iqr,nq)]+ mr*sdaux[ADDR(0,iqr,nq)]+ dpr*wn[0]; 
         fr[2]= fr[0]*sq[ADDR(1,iqr,nq)]+ mr*sdaux[ADDR(1,iqr,nq)]+ dpr*wn[1]; 
         fr[3]= fr[0]*sq[ADDR(2,iqr,nq)]+ mr*sdaux[ADDR(2,iqr,nq)]+ dpr*wn[2]; 
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fr[ia]= fr[0]*q[ia][iqr]+ mr*daux[ia][iqr];
            fr[ia]= fr[0]*sq[ADDR(ia,iqr,nq)]+ mr*sdaux[ADDR(ia,iqr,nq)];
        }

// retrieve Roe averages

         //wl=  auxc[0][ic];
         //wr=  auxc[1][ic];
         //ra=  auxc[2][ic];
         //la1= auxc[3][ic];
         //la3= auxc[4][ic];
         //la4= auxc[5][ic];
         wl=  sauxc[ADDR(0,ic,nfc)];
         wr=  sauxc[ADDR(1,ic,nfc)];
         ra=  sauxc[ADDR(2,ic,nfc)];
         la1= sauxc[ADDR(3,ic,nfc)];
         la3= sauxc[ADDR(4,ic,nfc)];
         la4= sauxc[ADDR(5,ic,nfc)];

         //qa[0]= wl*q[0][iql]+ wr*q[0][iqr];
         //qa[1]= wl*q[1][iql]+ wr*q[1][iqr];
         //qa[2]= wl*q[2][iql]+ wr*q[2][iqr];
         //qa[3]= wl*q[3][iql]+ wr*q[3][iqr];
         //qa[4]= wl*q[4][iql]+ wr*q[4][iqr];
         //for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*q[ia][iql]+ wr*q[ia][iqr]; }
         qa[0]= wl*sq[ADDR(0,iql,nq)]+ wr*sq[ADDR(0,iqr,nq)];
         qa[1]= wl*sq[ADDR(1,iql,nq)]+ wr*sq[ADDR(1,iqr,nq)];
         qa[2]= wl*sq[ADDR(2,iql,nq)]+ wr*sq[ADDR(2,iqr,nq)];
         qa[3]= wl*sq[ADDR(3,iql,nq)]+ wr*sq[ADDR(3,iqr,nq)];
         qa[4]= wl*sq[ADDR(4,iql,nq)]+ wr*sq[ADDR(4,iqr,nq)];
         for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*sq[ADDR(ia,iql,nq)]+ wr*sq[ADDR(ia,iqr,nq)]; }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// Left eigenvectors

         dr= drr- drl;
         dp= dpr- dpl;
        // dq[0]= daux[0][iqr]- daux[0][iql];
        // dq[1]= daux[1][iqr]- daux[1][iql];
        // dq[2]= daux[2][iqr]- daux[2][iql];
        // dq[3]= daux[3][iqr]- daux[3][iql];
        // dq[4]= daux[4][iqr]- daux[4][iql];
        // for( ia=5;ia<nv;ia++ )
        //{
        //    dq[ia]= daux[ia][iqr]- daux[ia][iql];
        //}
         dq[0]= sdaux[ADDR(0,iqr,nq)]- sdaux[ADDR(0,iql,nq)];
         dq[1]= sdaux[ADDR(1,iqr,nq)]- sdaux[ADDR(1,iql,nq)];
         dq[2]= sdaux[ADDR(2,iqr,nq)]- sdaux[ADDR(2,iql,nq)];
         dq[3]= sdaux[ADDR(3,iqr,nq)]- sdaux[ADDR(3,iql,nq)];
         dq[4]= sdaux[ADDR(4,iqr,nq)]- sdaux[ADDR(4,iql,nq)];
         for( ia=5;ia<nv;ia++ )
        {
            //dq[ia]= daux[ia][iqr]- daux[ia][iql];
            dq[ia]= sdaux[ADDR(ia,iqr,nq)]- sdaux[ADDR(ia,iql,nq)];
        }
         dun= dunr- dunl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //res[0][iql]-= f[0];
         //res[1][iql]-= f[1];
         //res[2][iql]-= f[2];
         //res[3][iql]-= f[3];
         //res[4][iql]-= f[4];
         #pragma acc atomic
         sres[ADDR_(0,iql,nq)]-= f[0];
         #pragma acc atomic
         sres[ADDR_(1,iql,nq)]-= f[1];
         #pragma acc atomic
         sres[ADDR_(2,iql,nq)]-= f[2];
         #pragma acc atomic
         sres[ADDR_(3,iql,nq)]-= f[3];
         #pragma acc atomic
         sres[ADDR_(4,iql,nq)]-= f[4];

         //res[0][iqr]+= f[0];
         //res[1][iqr]+= f[1];
         //res[2][iqr]+= f[2];
         //res[3][iqr]+= f[3];
         //res[4][iqr]+= f[4];
         #pragma acc atomic
         sres[ADDR_(0,iqr,nq)]+= f[0];
         #pragma acc atomic
         sres[ADDR_(1,iqr,nq)]+= f[1];
         #pragma acc atomic
         sres[ADDR_(2,iqr,nq)]+= f[2];
         #pragma acc atomic
         sres[ADDR_(3,iqr,nq)]+= f[3];
         #pragma acc atomic
         sres[ADDR_(4,iqr,nq)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //res[ia][iql]-= f[ia];
            //res[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic
            sres[ADDR_(ia,iql,nq)]-= f[ia];
            #pragma acc atomic
            sres[ADDR_(ia,iqr,nq)]+= f[ia];
        }

     }
     #pragma acc exit data delete (this)
  }

   //void cMfRoeGas::diflxb33( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
   //                                   Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
   //                                   Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cMfRoeGas::diflxb33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                              Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                              Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Int             nql,nqr;
      Real            f[MxNVs],wn[4];

      nql = nfc;
      nqr = nq;
     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      private(fl,fr,qa,ana,fa,dw2,dq,dw5,f,wn)\
      present(sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
              icqr[0:nfc],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
              swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];
// fluxes from the left

         //pl= ql[4][iql];
         //rl= auxl[0][iql];
         //hl= auxl[3][iql];
         pl= sql[ADDR(4,iql,nql)];
         rl= sauxl[ADDR(0,iql,nql)];
         hl= sauxl[ADDR(3,iql,nql)];


         //drl=    dql[0][iql];
         //dpl=  dauxl[4][iql];
         //drel=   dql[4][iql];
         drl=    sdql[ADDR(0,iql,nql)];
         dpl=  sdauxl[ADDR(4,iql,nql)];
         drel=   sdql[ADDR(4,iql,nql)];

         //unl=  wc[0][ic]*ql[0][iql]; 
         //unl+= wc[1][ic]*ql[1][iql]; 
         //unl+= wc[2][ic]*ql[2][iql]; 
         //unl-= wxdc[0][ic];
         unl=  wn[0]*sql[ADDR(0,iql,nql)]; 
         unl+= wn[1]*sql[ADDR(1,iql,nql)]; 
         unl+= wn[2]*sql[ADDR(2,iql,nql)]; 
         unl-= swxdc[ADDR(0,ic,nfc)];

         //dunl=  wc[0][ic]*dauxl[0][iql]; 
         //dunl+= wc[1][ic]*dauxl[1][iql]; 
         //dunl+= wc[2][ic]*dauxl[2][iql]; 
         dunl=  wn[0]*sdauxl[ADDR(0,iql,nql)]; 
         dunl+= wn[1]*sdauxl[ADDR(1,iql,nql)]; 
         dunl+= wn[2]*sdauxl[ADDR(2,iql,nql)]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         //fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
         //fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
         //fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql]+  dpl*wc[2][ic]; 
         //fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ ml*sdauxl[ADDR(0,iql,nql)]+  dpl*wn[0]; 
         fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ ml*sdauxl[ADDR(1,iql,nql)]+  dpl*wn[1]; 
         fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ ml*sdauxl[ADDR(2,iql,nql)]+  dpl*wn[2]; 
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
            fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)]+ ml*sdauxl[ADDR(ia,iql,nql)];
        }

// fluxes from the right

         //pr= qr[4][iqr];
         //rr= auxr[0][iqr];
         //hr= auxr[3][iqr];
         pr= sqr[ADDR(4,iqr,nqr)];
         rr= sauxr[ADDR(0,iqr,nqr)];
         hr= sauxr[ADDR(3,iqr,nqr)];

         //drr=    dqr[0][iqr];
         //dpr=  dauxr[4][iqr];
         //drer=   dqr[4][iqr];
         drr=    sdqr[ADDR(0,iqr,nqr)];
         dpr=  sdauxr[ADDR(4,iqr,nqr)];
         drer=   sdqr[ADDR(4,iqr,nqr)];

         //unr=  wc[0][ic]*qr[0][iqr]; 
         //unr+= wc[1][ic]*qr[1][iqr]; 
         //unr+= wc[2][ic]*qr[2][iqr]; 
         //unr-= wxdc[0][ic];
         unr=  wn[0]*sqr[ADDR(0,iqr,nqr)]; 
         unr+= wn[1]*sqr[ADDR(1,iqr,nqr)]; 
         unr+= wn[2]*sqr[ADDR(2,iqr,nqr)]; 
         unr-= swxdc[ADDR(0,ic,nfc)];

         //dunr=  wc[0][ic]*dauxr[0][iqr]; 
         //dunr+= wc[1][ic]*dauxr[1][iqr]; 
         //dunr+= wc[2][ic]*dauxr[2][iqr]; 
         dunr=  wn[0]*sdauxr[ADDR(0,iqr,nqr)]; 
         dunr+= wn[1]*sdauxr[ADDR(1,iqr,nqr)]; 
         dunr+= wn[2]*sdauxr[ADDR(2,iqr,nqr)]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         //fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
         //fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
         //fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
         //fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ mr*sdauxr[ADDR(0,iqr,nqr)]+ dpr*wn[0]; 
         fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ mr*sdauxr[ADDR(1,iqr,nqr)]+ dpr*wn[1]; 
         fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ mr*sdauxr[ADDR(2,iqr,nqr)]+ dpr*wn[2]; 
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
            fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)]+ mr*sdauxr[ADDR(ia,iqr,nqr)];
        }

// one wave

         //Real lmax= auxc[nauxf-1][ic];
         Real lmax= sauxc[ADDR(nauxf-1,ic,nfc)];
         //fa[0]= lmax*( dqr[0][iqr]- dql[0][iql] );
         //fa[1]= lmax*( dqr[1][iqr]- dql[1][iql] );
         //fa[2]= lmax*( dqr[2][iqr]- dql[2][iql] );
         //fa[3]= lmax*( dqr[3][iqr]- dql[3][iql] );
         //fa[4]= lmax*( dqr[4][iqr]- dql[4][iql] );
         fa[0]= lmax*( sdqr[ADDR(0,iqr,nqr)]- sdql[ADDR(0,iql,nql)] );
         fa[1]= lmax*( sdqr[ADDR(1,iqr,nqr)]- sdql[ADDR(1,iql,nql)] );
         fa[2]= lmax*( sdqr[ADDR(2,iqr,nqr)]- sdql[ADDR(2,iql,nql)] );
         fa[3]= lmax*( sdqr[ADDR(3,iqr,nqr)]- sdql[ADDR(3,iql,nql)] );
         fa[4]= lmax*( sdqr[ADDR(4,iqr,nqr)]- sdql[ADDR(4,iql,nql)] );
         for( ia=5;ia<nv;ia++ )
        {
            //fa[ia]= lmax*( dqr[ia][iqr]- dql[ia][iql] );
            fa[ia]= lmax*( sdqr[ADDR(ia,iqr,nqr)]- sdql[ADDR(ia,iql,nql)] );
        }

// assemble 


         //f[0]= 0.5*( fr[0]+ fl[0] )*wc[3][ic]- 0.5*fa[0];
         //f[1]= 0.5*( fr[1]+ fl[1] )*wc[3][ic]- 0.5*fa[1];
         //f[2]= 0.5*( fr[2]+ fl[2] )*wc[3][ic]- 0.5*fa[2];
         //f[3]= 0.5*( fr[3]+ fl[3] )*wc[3][ic]- 0.5*fa[3];
         //f[4]= 0.5*( fr[4]+ fl[4] )*wc[3][ic]- 0.5*fa[4];
         f[0]= 0.5*( fr[0]+ fl[0] )*wn[3]- 0.5*fa[0];
         f[1]= 0.5*( fr[1]+ fl[1] )*wn[3]- 0.5*fa[1];
         f[2]= 0.5*( fr[2]+ fl[2] )*wn[3]- 0.5*fa[2];
         f[3]= 0.5*( fr[3]+ fl[3] )*wn[3]- 0.5*fa[3];
         f[4]= 0.5*( fr[4]+ fl[4] )*wn[3]- 0.5*fa[4];

         //resl[0][iql]-= f[0];
         //resl[1][iql]-= f[1];
         //resl[2][iql]-= f[2];
         //resl[3][iql]-= f[3];
         //resl[4][iql]-= f[4];
         #pragma acc atomic
         sresl[ADDR_(0,iql,nql)]-= f[0];
         #pragma acc atomic
         sresl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         sresl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         sresl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         sresl[ADDR_(4,iql,nql)]-= f[4];

         //sresr[0][iqr]+= f[0];
         //sresr[1][iqr]+= f[1];
         //sresr[2][iqr]+= f[2];
         //sresr[3][iqr]+= f[3];
         //sresr[4][iqr]+= f[4];
         #pragma acc atomic
         sresr[ADDR_(0,iqr,nqr)]+= f[0];
         #pragma acc atomic
         sresr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         sresr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         sresr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         sresr[ADDR_(4,iqr,nqr)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        { 
            //f[ia]= 0.5*( fr[ia]+ fl[ia] )*wc[3][ic]- 0.5*fa[ia];
            //resl[ia][iql]-= f[ia];
            //resr[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia] )*wn[3]- 0.5*fa[ia];
            #pragma acc atomic
            sresl[ADDR_(ia,iql,nql)]-= f[ia];
            #pragma acc atomic
            sresr[ADDR_(ia,iqr,nqr)]+= f[ia];
        }
     }
     #pragma acc exit data copyout(this)
  }

   void cMfRoeGas::diflxb33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                              cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Int             nql,nqr;
      Real            f[MxNVs],wn[4];

      Int nfc, nq;

      Int *icql;
      Real *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql = icql_view.get_data();
      sql = ql.get_data();
      sauxl = auxl.get_data();
      sdql = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl = resl.get_data();
      icqr = icqr_view.get_data();
      sqr = qr.get_data();
      sauxr = auxr.get_data();
      sdqr = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr = resr.get_data();
      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      nql = nfc;
      nqr = nq;
     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      private(fl,fr,qa,ana,fa,dw2,dq,dw5,f,wn)\
      present(sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
              icqr[0:nfc],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
              swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];
// fluxes from the left

         //pl= ql[4][iql];
         //rl= auxl[0][iql];
         //hl= auxl[3][iql];
         pl= sql[ADDR(4,iql,nql)];
         rl= sauxl[ADDR(0,iql,nql)];
         hl= sauxl[ADDR(3,iql,nql)];


         //drl=    dql[0][iql];
         //dpl=  dauxl[4][iql];
         //drel=   dql[4][iql];
         drl=    sdql[ADDR(0,iql,nql)];
         dpl=  sdauxl[ADDR(4,iql,nql)];
         drel=   sdql[ADDR(4,iql,nql)];

         //unl=  wc[0][ic]*ql[0][iql]; 
         //unl+= wc[1][ic]*ql[1][iql]; 
         //unl+= wc[2][ic]*ql[2][iql]; 
         //unl-= wxdc[0][ic];
         unl=  wn[0]*sql[ADDR(0,iql,nql)]; 
         unl+= wn[1]*sql[ADDR(1,iql,nql)]; 
         unl+= wn[2]*sql[ADDR(2,iql,nql)]; 
         unl-= swxdc[ADDR(0,ic,nfc)];

         //dunl=  wc[0][ic]*dauxl[0][iql]; 
         //dunl+= wc[1][ic]*dauxl[1][iql]; 
         //dunl+= wc[2][ic]*dauxl[2][iql]; 
         dunl=  wn[0]*sdauxl[ADDR(0,iql,nql)]; 
         dunl+= wn[1]*sdauxl[ADDR(1,iql,nql)]; 
         dunl+= wn[2]*sdauxl[ADDR(2,iql,nql)]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         //fl[1]= fl[0]*ql[0][iql]+ ml*dauxl[0][iql]+  dpl*wc[0][ic]; 
         //fl[2]= fl[0]*ql[1][iql]+ ml*dauxl[1][iql]+  dpl*wc[1][ic]; 
         //fl[3]= fl[0]*ql[2][iql]+ ml*dauxl[2][iql]+  dpl*wc[2][ic]; 
         //fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ ml*sdauxl[ADDR(0,iql,nql)]+  dpl*wn[0]; 
         fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ ml*sdauxl[ADDR(1,iql,nql)]+  dpl*wn[1]; 
         fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ ml*sdauxl[ADDR(2,iql,nql)]+  dpl*wn[2]; 
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fl[ia]= fl[0]*ql[ia][iql]+ ml*dauxl[ia][iql];
            fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)]+ ml*sdauxl[ADDR(ia,iql,nql)];
        }

// fluxes from the right

         //pr= qr[4][iqr];
         //rr= auxr[0][iqr];
         //hr= auxr[3][iqr];
         pr= sqr[ADDR(4,iqr,nqr)];
         rr= sauxr[ADDR(0,iqr,nqr)];
         hr= sauxr[ADDR(3,iqr,nqr)];

         //drr=    dqr[0][iqr];
         //dpr=  dauxr[4][iqr];
         //drer=   dqr[4][iqr];
         drr=    sdqr[ADDR(0,iqr,nqr)];
         dpr=  sdauxr[ADDR(4,iqr,nqr)];
         drer=   sdqr[ADDR(4,iqr,nqr)];

         //unr=  wc[0][ic]*qr[0][iqr]; 
         //unr+= wc[1][ic]*qr[1][iqr]; 
         //unr+= wc[2][ic]*qr[2][iqr]; 
         //unr-= wxdc[0][ic];
         unr=  wn[0]*sqr[ADDR(0,iqr,nqr)]; 
         unr+= wn[1]*sqr[ADDR(1,iqr,nqr)]; 
         unr+= wn[2]*sqr[ADDR(2,iqr,nqr)]; 
         unr-= swxdc[ADDR(0,ic,nfc)];

         //dunr=  wc[0][ic]*dauxr[0][iqr]; 
         //dunr+= wc[1][ic]*dauxr[1][iqr]; 
         //dunr+= wc[2][ic]*dauxr[2][iqr]; 
         dunr=  wn[0]*sdauxr[ADDR(0,iqr,nqr)]; 
         dunr+= wn[1]*sdauxr[ADDR(1,iqr,nqr)]; 
         dunr+= wn[2]*sdauxr[ADDR(2,iqr,nqr)]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         //fr[1]= fr[0]*qr[0][iqr]+ mr*dauxr[0][iqr]+ dpr*wc[0][ic]; 
         //fr[2]= fr[0]*qr[1][iqr]+ mr*dauxr[1][iqr]+ dpr*wc[1][ic]; 
         //fr[3]= fr[0]*qr[2][iqr]+ mr*dauxr[2][iqr]+ dpr*wc[2][ic]; 
         //fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ mr*sdauxr[ADDR(0,iqr,nqr)]+ dpr*wn[0]; 
         fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ mr*sdauxr[ADDR(1,iqr,nqr)]+ dpr*wn[1]; 
         fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ mr*sdauxr[ADDR(2,iqr,nqr)]+ dpr*wn[2]; 
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fr[ia]= fr[0]*qr[ia][iqr]+ mr*dauxr[ia][iqr];
            fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)]+ mr*sdauxr[ADDR(ia,iqr,nqr)];
        }

// one wave

         //Real lmax= auxc[nauxf-1][ic];
         Real lmax= sauxc[ADDR(nauxf-1,ic,nfc)];
         //fa[0]= lmax*( dqr[0][iqr]- dql[0][iql] );
         //fa[1]= lmax*( dqr[1][iqr]- dql[1][iql] );
         //fa[2]= lmax*( dqr[2][iqr]- dql[2][iql] );
         //fa[3]= lmax*( dqr[3][iqr]- dql[3][iql] );
         //fa[4]= lmax*( dqr[4][iqr]- dql[4][iql] );
         fa[0]= lmax*( sdqr[ADDR(0,iqr,nqr)]- sdql[ADDR(0,iql,nql)] );
         fa[1]= lmax*( sdqr[ADDR(1,iqr,nqr)]- sdql[ADDR(1,iql,nql)] );
         fa[2]= lmax*( sdqr[ADDR(2,iqr,nqr)]- sdql[ADDR(2,iql,nql)] );
         fa[3]= lmax*( sdqr[ADDR(3,iqr,nqr)]- sdql[ADDR(3,iql,nql)] );
         fa[4]= lmax*( sdqr[ADDR(4,iqr,nqr)]- sdql[ADDR(4,iql,nql)] );
         for( ia=5;ia<nv;ia++ )
        {
            //fa[ia]= lmax*( dqr[ia][iqr]- dql[ia][iql] );
            fa[ia]= lmax*( sdqr[ADDR(ia,iqr,nqr)]- sdql[ADDR(ia,iql,nql)] );
        }

// assemble 


         //f[0]= 0.5*( fr[0]+ fl[0] )*wc[3][ic]- 0.5*fa[0];
         //f[1]= 0.5*( fr[1]+ fl[1] )*wc[3][ic]- 0.5*fa[1];
         //f[2]= 0.5*( fr[2]+ fl[2] )*wc[3][ic]- 0.5*fa[2];
         //f[3]= 0.5*( fr[3]+ fl[3] )*wc[3][ic]- 0.5*fa[3];
         //f[4]= 0.5*( fr[4]+ fl[4] )*wc[3][ic]- 0.5*fa[4];
         f[0]= 0.5*( fr[0]+ fl[0] )*wn[3]- 0.5*fa[0];
         f[1]= 0.5*( fr[1]+ fl[1] )*wn[3]- 0.5*fa[1];
         f[2]= 0.5*( fr[2]+ fl[2] )*wn[3]- 0.5*fa[2];
         f[3]= 0.5*( fr[3]+ fl[3] )*wn[3]- 0.5*fa[3];
         f[4]= 0.5*( fr[4]+ fl[4] )*wn[3]- 0.5*fa[4];

         //resl[0][iql]-= f[0];
         //resl[1][iql]-= f[1];
         //resl[2][iql]-= f[2];
         //resl[3][iql]-= f[3];
         //resl[4][iql]-= f[4];
         #pragma acc atomic
         sresl[ADDR_(0,iql,nql)]-= f[0];
         #pragma acc atomic
         sresl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         sresl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         sresl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         sresl[ADDR_(4,iql,nql)]-= f[4];

         //sresr[0][iqr]+= f[0];
         //sresr[1][iqr]+= f[1];
         //sresr[2][iqr]+= f[2];
         //sresr[3][iqr]+= f[3];
         //sresr[4][iqr]+= f[4];
         #pragma acc atomic
         sresr[ADDR_(0,iqr,nqr)]+= f[0];
         #pragma acc atomic
         sresr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         sresr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         sresr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         sresr[ADDR_(4,iqr,nqr)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        { 
            //f[ia]= 0.5*( fr[ia]+ fl[ia] )*wc[3][ic]- 0.5*fa[ia];
            //resl[ia][iql]-= f[ia];
            //resr[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia] )*wn[3]- 0.5*fa[ia];
            #pragma acc atomic
            sresl[ADDR_(ia,iql,nql)]-= f[ia];
            #pragma acc atomic
            sresr[ADDR_(ia,iqr,nqr)]+= f[ia];
        }
     }
     #pragma acc exit data copyout(this)
  }

   void cMfRoeGas::diflxmuscl33( Int ics,Int ice, Int idl, Int *icql, Real *xql[], Real *ql0[], Real *zl0[], Real *dxdxl[], Real **dqdxl[], Real **dzdxl[], Real *resl[],
                                                  Int idr, Int *icqr, Real *xqr[], Real *qr0[], Real *zr0[], Real *dxdxr[], Real **dqdxr[], Real **dzdxr[], Real *resr[],
                                                  Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd )
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,ic,iql,iqr;

      Real            wl,wr,aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs];

      Real            xn[3], wn[3];

      Real            gam1 = 1./(gam-1.);
      Real            rho, u, v, w, t, p, k;
      Real            drho, du, dv, dw, dt, dk;
      Real            auxl[MxNVs],auxr[MxNVs];
      Real            ql[MxNVs],qr[MxNVs];
      Real            zl[MxNVs],zr[MxNVs];
      Real            zcl[MxNVs],zcr[MxNVs];
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dzl[MxNVs],dzr[MxNVs];


      for( ic=ics;ic<ice;ic++ )
     {
         wn[0]= wc[0][ic];
         wn[1]= wc[1][ic];
         wn[2]= wc[2][ic];

         xn[0]= xc[0][ic];
         xn[1]= xc[1][ic];
         xn[2]= xc[2][ic];

         iql= ic;
         if( icql ){ iql= icql[ic]; };
         iqr= ic;
         if( icqr ){ iqr= icqr[ic]; };

         if(grd->getlimtype()==1)
        {
            grd->deltq( iql,idl,xql,zl0,dxdxl,dzdxl, iqr,idr,xqr,zr0,dxdxr,dzdxr, xn,wn, dzl,dzr, dzl,dzr );
   
            for( ia=0;ia<nv;ia++ )
           {
               ql[ia]= ql0[ia][ic];
               qr[ia]= qr0[ia][ic];
               zl[ia]= zl0[ia][iql]+ dzl[ia];
               zr[ia]= zr0[ia][iqr]+ dzr[ia];
           }
        }
         else
        {
            grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql,dqr, dql,dqr );
            grd->deltq( iql,idl,xql,zl0,dxdxl,dzdxl, iqr,idr,xqr,zr0,dxdxr,dzdxr, xn,wn, dzl,dzr, dzl,dzr );
   
            for( ia=0;ia<nv;ia++ )
           {
               ql[ia]= ql0[ia][iql]+ dql[ia];
               qr[ia]= qr0[ia][iqr]+ dqr[ia];
               zl[ia]= zl0[ia][iql]+ dzl[ia];
               zr[ia]= zr0[ia][iqr]+ dzr[ia];
           }
        }

// left state - auxiliary variables
         auxl[0]=  ql[4]/( rg*ql[3] );
         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;
         auxl[2]= gam*rg* ql[3];
         auxl[3]= auxl[2]/(gam-1)+ auxl[1];
         auxl[2]= sqrt( auxl[2] );

// right state - auxiliary variables
         auxr[0]= qr[4]/( rg*qr[3] );
         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;
         auxr[2]= gam*rg* qr[3];
         auxr[3]= auxr[2]/(gam-1)+ auxr[1];
         auxr[2]= sqrt( auxr[2] );


// left state - cnsv z
         u = ql[0];
         v = ql[1];
         w = ql[2];
         t = ql[3];
         p = ql[4];
         rho = p/(rg*t);

         du = zl[0];
         dv = zl[1];
         dw = zl[2];
         dt = zl[3];
         dp = zl[4];
         drho = (1/rg)*( -dt*p/(t*t) + dp/t );

         k = 0.5*(u*u + v*v + w*w);
         dk = du*u + dv*v + dw*w;

         zcl[0]= drho;
         zcl[1]= drho*u + du*rho;
         zcl[2]= drho*v + dv*rho;
         zcl[3]= drho*w + dw*rho;
         zcl[4] = gam1*dp + rho*dk + k*drho;
         for( ia=5;ia<nv;ia++ )
        {
            zcl[ia]= rho*zl[ia] + drho*ql[ia];
        }


// right state - cnsv z
         u = qr[0];
         v = qr[1];
         w = qr[2];
         t = qr[3];
         p = qr[4];
         rho = p/(rg*t);

         du = zr[0];
         dv = zr[1];
         dw = zr[2];
         dt = zr[3];
         dp = zr[4];
         drho = (1/rg)*( -dt*p/(t*t) + dp/t );

         k = 0.5*(u*u + v*v + w*w);
         dk = du*u + dv*v + dw*w;

         zcr[0]= drho;
         zcr[1]= drho*u + du*rho;
         zcr[2]= drho*v + dv*rho;
         zcr[3]= drho*w + dw*rho;
         zcr[4]= gam1*dp + rho*dk + k*drho;
         for( ia=5;ia<nv;ia++ )
        {
            zcr[ia]= rho*zr[ia] + drho*qr[ia];
        }

// fluxes from the left

         pl= ql[4];
         rl= auxl[0];
         hl= auxl[3];

         drl=    zcl[0];
         dpl=    zl[4];
         drel=   zcl[4];

         unl=  wc[0][ic]*ql[0]; 
         unl+= wc[1][ic]*ql[1]; 
         unl+= wc[2][ic]*ql[2]; 
         unl-= wxdc[0][ic];

         dunl=  wc[0][ic]*zl[0]; 
         dunl+= wc[1][ic]*zl[1]; 
         dunl+= wc[2][ic]*zl[2]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         fl[1]= fl[0]*ql[0] + ml*zl[0] +  dpl*wc[0][ic]; 
         fl[2]= fl[0]*ql[1] + ml*zl[1] +  dpl*wc[1][ic]; 
         fl[3]= fl[0]*ql[2] + ml*zl[2] +  dpl*wc[2][ic]; 
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         for( ia=5;ia<nv;ia++ )
        {
            fl[ia]= fl[0]*ql[ia]+ ml*zl[ia];
        }

// fluxes from the right

         pr= qr[4];
         rr= auxr[0];
         hr= auxr[3];

         drr=    zcr[0];
         dpr=    zr[4];
         drer=   zcr[4];

         unr=  wc[0][ic]*qr[0]; 
         unr+= wc[1][ic]*qr[1]; 
         unr+= wc[2][ic]*qr[2]; 
         unr-= wxdc[0][ic];

         dunr=  wc[0][ic]*zr[0]; 
         dunr+= wc[1][ic]*zr[1]; 
         dunr+= wc[2][ic]*zr[2]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         fr[1]= fr[0]*qr[0] + mr*zr[0] + dpr*wc[0][ic]; 
         fr[2]= fr[0]*qr[1] + mr*zr[1] + dpr*wc[1][ic]; 
         fr[3]= fr[0]*qr[2] + mr*zr[2] + dpr*wc[2][ic]; 
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         for( ia=5;ia<nv;ia++ )
        {
            fr[ia]= fr[0]*qr[ia] + mr*zr[ia];
        }

// retrieve Roe averages

         wl=  auxc[0][ic];
         wr=  auxc[1][ic];
         ra=  auxc[2][ic];
         la1= auxc[3][ic];
         la3= auxc[4][ic];
         la4= auxc[5][ic];

         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*ql[ia] + wr*qr[ia]; }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         una=  qa[0]* wc[0][ic];
         una+= qa[1]* wc[1][ic];
         una+= qa[2]* wc[2][ic];

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// Left eigenvectors

         dr= drr- drl;
         dp= dpr- dpl;
         dq[0]= zr[0]- zl[0];
         dq[1]= zr[1]- zl[1];
         dq[2]= zr[2]- zl[2];
         dq[3]= zr[3]- zl[3];
         dq[4]= zr[4]- zl[4];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= zr[ia]- zl[ia];
        }
         dun= dunr- dunl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         ana[0]= aa*wc[0][ic];
         ana[1]= aa*wc[1][ic];
         ana[2]= aa*wc[2][ic];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];

         resl[0][iql]-= f[0];
         resl[1][iql]-= f[1];
         resl[2][iql]-= f[2];
         resl[3][iql]-= f[3];
         resl[4][iql]-= f[4];

         resr[0][iqr]+= f[0];
         resr[1][iqr]+= f[1];
         resr[2][iqr]+= f[2];
         resr[3][iqr]+= f[3];
         resr[4][iqr]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            resl[ia][iql]-= f[ia];
            resr[ia][iqr]+= f[ia];
        }

     }
  }
