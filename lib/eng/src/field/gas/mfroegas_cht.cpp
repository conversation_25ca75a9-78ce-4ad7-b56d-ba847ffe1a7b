using namespace std;

# include <field/gas.h>
# include <mem/proto.h>
# include <field/grad.h>

   cMfRoeGasCHT::cMfRoeGasCHT( cCosystem *Coo, cVisc *visc )
  {

      coo= Coo;
      vsc= visc;
      nx=  coo->getnx();
      nvel=coo->getnvel(); 

      nvk=3;
      nv=2+nvel+1; //u, v, w, t, p, solid-or-fluid 0:solid, 1:fluid
      naux=7;
      nauxf=7;
      nlhs= 2;

      nv0= nv;
      naux0= naux;
      nauxf0= nauxf;
      nlhs0= nlhs;

      vsc->setvrs( nx,nvel, &nv,&naux,&nauxf,&nlhs );
      ilv[0]=nvel;
      ilv[1]=ilv[0]+1;
      ilv[2]=ilv[1]+1;
      unit[0]= 100.;
      unit[1]=   1.;
      unit[2]= unit[0]*unit[0];
      deflt[0]=   0.;
      deflt[1]= 298.;
      deflt[2]= 100000./unit[2];
      rg= 287/unit[2];
      gam=1.4;
      eps= 0.05;
      coo->setnv(nv);

      solid_kappa=236;
      solid_rho=2710;
      solid_cp = 902;
  }

   void cMfRoeGasCHT::dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view )
  {
      Int iq,ia;
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;
      Int nq;
      Real *q, *aux, *dU, *dq;
      Real cp;

      nq = q_view.get_dim1();

      q = q_view.get_data();
      aux = aux_view.get_data();
      dU = dU_view.get_data();
      dq = dq_view.get_data();

      cv= rg/(gam-1.);

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop \
      present(q[0:nv*nq],aux[0:naux*nq],dU[0:nv*nq],dq[0:nv*nq],this) \
      default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         if(q[ADDR(5, iq, nq)]==1)
        {
            //t=  q[3][iq];
            //p=  q[4][iq];
            //ro= aux[0][iq];
            //h=  aux[3][iq];
            t=  q[ADDR(3,iq,nq)];
            p=  q[ADDR(4,iq,nq)];
            ro= aux[ADDR(0,iq,nq)];
            h=  aux[ADDR(3,iq,nq)];
            re= ro*h- p;
            e= re/ro;
            //dro= dU[0][iq];
            //dre= dU[4][iq];
            dro= dU[ADDR(0,iq,nq)];
            dre= dU[ADDR(4,iq,nq)];

            //dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
            //dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
            //dq[2][iq]= ( dU[3][iq]- q[2][iq]*dro )/ro;
            dq[ADDR(0,iq,nq)]= ( dU[ADDR(1,iq,nq)]- q[ADDR(0,iq,nq)]*dro )/ro;
            dq[ADDR(1,iq,nq)]= ( dU[ADDR(2,iq,nq)]- q[ADDR(1,iq,nq)]*dro )/ro;
            dq[ADDR(2,iq,nq)]= ( dU[ADDR(3,iq,nq)]- q[ADDR(2,iq,nq)]*dro )/ro;

            //dk=  q[0][iq]*dq[0][iq];
            //dk+= q[1][iq]*dq[1][iq];
            //dk+= q[2][iq]*dq[2][iq];
            dk=  q[ADDR(0,iq,nq)]*dq[ADDR(0,iq,nq)];
            dk+= q[ADDR(1,iq,nq)]*dq[ADDR(1,iq,nq)];
            dk+= q[ADDR(2,iq,nq)]*dq[ADDR(2,iq,nq)];

            dk*= ro;
            dt= ( dre- e*dro- dk )/( ro*cv );
            dp= p*( dro/ro+ dt/t );

            //dq[3][iq]= dt;
            //dq[4][iq]= dp;
            dq[ADDR(3,iq,nq)]= dt;
            dq[ADDR(4,iq,nq)]= dp;
        }
         else
        {
            ro= aux[ADDR(0,iq,nq)];
            cp= aux[ADDR(4,iq,nq)];
            dre= dU[ADDR(4,iq,nq)];
            dt= dre/( ro*cp );

            dq[ADDR(3,iq,nq)]= dt;
        }
     }
     #pragma acc exit data delete(this)
  }

   void cMfRoeGasCHT::auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch )
  {
      Int iq;
      Int iv;
      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      Real tmpkappa=solid_kappa;
      Real tmprho=solid_rho;
      Real tmpcp = solid_cp;
         
      tmpkappa/= (unit[0]*unit[0]*unit[0]);
      tmpcp/=unit[0]*unit[0];

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);
      Real cp=rg*gam/(gam-1);

      if(arch=="d")
     {
         Int nq;
         Real *q, *aux;

         nq = q_view.get_dim1();

         q   = q_view.get_data();
         aux = aux_view.get_data();

         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          present(q[0:nv*nq],aux[0:naux*nq],this) \
          default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            if(q[ADDR(5,iq,nq)]==1)
           {
//    density
               aux[ADDR(0,iq,nq)]= q[ADDR(4,iq,nq)]/( rg*q[ADDR(3,iq,nq)] );
//    kinetic energy
               aux[ADDR(1,iq,nq)]=  q[ADDR(0,iq,nq)]*q[ADDR(0,iq,nq)];
               aux[ADDR(1,iq,nq)]+= q[ADDR(1,iq,nq)]*q[ADDR(1,iq,nq)];
               aux[ADDR(1,iq,nq)]+= q[ADDR(2,iq,nq)]*q[ADDR(2,iq,nq)];
               aux[ADDR(1,iq,nq)]*= 0.5;
//    speed of sound and total entalpy
               aux[ADDR(2,iq,nq)]= gam*rg* q[ADDR(3,iq,nq)];
               aux[ADDR(3,iq,nq)]= aux[ADDR(2,iq,nq)]/(gam-1)+ aux[ADDR(1,iq,nq)];
               aux[ADDR(2,iq,nq)]= sqrt( aux[ADDR(2,iq,nq)] );
               aux[ADDR(4,iq,nq)]= cp;
               aux[ADDR(5,iq,nq)]= mu;
               aux[ADDR(6,iq,nq)]= kappa;
           }
            else
           {
//    density
               aux[ADDR(0,iq,nq)]= tmprho;
//    kinetic energy
               aux[ADDR(1,iq,nq)]= 0.;
//    speed of sound and total entalpy
               aux[ADDR(2,iq,nq)]= 0.;
               aux[ADDR(3,iq,nq)]= tmpcp*q[ADDR(3,iq,nq)];
               aux[ADDR(4,iq,nq)]= tmpcp;
               aux[ADDR(5,iq,nq)]= 0.;
               aux[ADDR(6,iq,nq)]= tmpkappa;
           }
        }
         #pragma acc exit data delete(this)
     }
      else if(arch=="h")
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            if(q_view(5,iq)==1)
           {
//    density
               aux_view(0,iq)= q_view(4,iq)/( rg*q_view(3,iq) );
//    kinetic energy
               aux_view(1,iq)=  q_view(0,iq)*q_view(0,iq);
               aux_view(1,iq)+= q_view(1,iq)*q_view(1,iq);
               aux_view(1,iq)+= q_view(2,iq)*q_view(2,iq);
               aux_view(1,iq)*= 0.5;
//    speed of sound and total entalpy
               aux_view(2,iq)= gam*rg* q_view(3,iq); 
               aux_view(3,iq)= aux_view(2,iq)/(gam-1)+ aux_view(1,iq);
               aux_view(2,iq)= sqrt( aux_view(2,iq) );
               aux_view(4,iq)= cp;
               aux_view(5,iq)= mu;
               aux_view(6,iq)= kappa;
           }
            else
           {
//    density
               aux_view(0,iq)= tmprho;
//    kinetic energy
               aux_view(1,iq)= 0.;
//    speed of sound and total entalpy
               aux_view(2,iq)= 0.;
               aux_view(3,iq)= tmpcp*q_view(3,iq);
               aux_view(4,iq)= tmpcp;
               aux_view(5,iq)= 0.;
               aux_view(6,iq)= tmpkappa;
           }
        } 
     }
      else
     {
         cout << "unkown arch\n"; 
         assert(0);
     }
  }

   void cMfRoeGasCHT::cnsv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch )
  {      
      Int iq,ia;
      Int nq;
      Real *sq, *saux, *sqo;
        
      if(arch == "d")
     { 
         nq = q.get_dim1();
   
         sq   = q.get_data();
         saux = aux.get_data();
         sqo  = qo.get_data();
   
         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          present(sq[0:nv*nq],saux[0:naux*nq],sqo[0:nv*nq],this) \
          default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            if(sq[ADDR(5,iq,nq)]==1)
           {
               // density
               sqo[ADDR(0,iq,nq)]= saux[ADDR(0,iq,nq)];
               sqo[ADDR(1,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(0,iq,nq)];
               sqo[ADDR(2,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(1,iq,nq)];
               sqo[ADDR(3,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(2,iq,nq)];
               sqo[ADDR(4,iq,nq)]= sqo[ADDR(0,iq,nq)]*saux[ADDR(3,iq,nq)]- sq[ADDR(4,iq,nq)];
               sqo[ADDR(5,iq,nq)]= 1;
               for( ia=nv0;ia<nv;ia++ )
              {
                  //qo[ia][iq]= qo[0][iq]*q[ia][iq];
                  sqo[ADDR(ia,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(ia,iq,nq)];
              }
           }
            else
           {
               // density
               sqo[ADDR(0,iq,nq)]= saux[ADDR(0,iq,nq)];
               sqo[ADDR(1,iq,nq)]= 0;
               sqo[ADDR(2,iq,nq)]= 0;
               sqo[ADDR(3,iq,nq)]= 0;
               sqo[ADDR(4,iq,nq)]= sqo[ADDR(0,iq,nq)]*saux[ADDR(3,iq,nq)];
               sqo[ADDR(5,iq,nq)]= 0;
           }
        }
         #pragma acc exit data delete(this)
     }
      else if(arch == "h")
     { 
         for( iq=iqs;iq<iqe;iq++ )
        {
            if(sq[ADDR(5,iq,nq)]==1)
           {
               // density
               qo(0,iq)= aux(0,iq);
               qo(1,iq)= qo(0,iq)*q(0,iq);
               qo(2,iq)= qo(0,iq)*q(1,iq);
               qo(3,iq)= qo(0,iq)*q(2,iq);
               qo(4,iq)= qo(0,iq)*aux(3,iq)- q(4,iq);
               qo(5,iq)= 1;
               for( ia=nv0;ia<nv;ia++ )
              {
                  //qo[ia][iq]= qo[0][iq]*q[ia][iq];
                  qo(ia,iq)= qo(0,iq)*q(ia,iq);
              }
           }
            else
           {
               // density
               qo(0,iq)= aux(0,iq);
               qo(1,iq)= 0;
               qo(2,iq)= 0;
               qo(3,iq)= 0;
               qo(4,iq)= qo(0,iq)*aux(3,iq);
               qo(5,iq)= 0;
           }
        }
     }
      else
     {
         assert(0);
     }
  }

   void cMfRoeGasCHT::iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                                   cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder )
  {
      Real            dql[MxNVs],dqr[MxNVs];
      Real            dql0[MxNVs],dqr0[MxNVs];
      Real            auxl[MxNVs],auxr[MxNVs];
      Int             ix,iql,iqr,ia;
      Real            xn[3],wn[4];
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs],ql[MxNVs],dhl,dal;
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs],qr[MxNVs],dhr,dar;
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa;
      Real            dur[3],dunr,dpr,drr,dtr,tr,dt;
      Real            dwl[MxNVs],dwr[MxNVs];
      Real            mr,ml,wl,wr,cp;
      Real            f[MxNVs], dw5[MxNVs];
      bool            bfluid;

      Int nfc, nq;
      Int *sicq;
      Real *sxq, *sq, *sdxdx, *sdqdx, *saux, *srhs;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = q.get_dim1();

      sicq  = icq.get_data();
      sxq   = xq.get_data();
      sq    = q.get_data();
      sdxdx = dxdx.get_data();
      sdqdx = dqdx.get_data();
      saux  = aux.get_data();
      srhs  = rhs.get_data();
      sxc   = xc.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      cp= rg*gam/(gam-1);
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       firstprivate(cp), \
       private(dql0,dqr0,\
               dql,dqr,\
               auxl,auxr,\
               ix,iql,iqr,ia,\
               xn,wn,\
               al,unl,rl,ll1,ll3,ll4,pl,hl,fl,ql,dhl,dal, \
               ar,unr,rr,lr1,lr3,lr4,pr,hr,fr,qr,dhr,dar, \
               le1,le3,le4, \
               aa,a2a,ra,ha,ka,qa,ana,una,unaa,raa, la1,la4,la3,lmax,fa, \
               dw1,dw3,dw4,dw2,dw2a,dr,dq,dun,dp,dpa, \
               dur,dunr,dpr,drr,dtr,tr,dt, \
               dwl,dwr, \
               mr,ml,wl,wr,\
               f, dw5 ) \ 
       present(sq[0:nv*nq],sauxc[0:nauxf*nfc], saux[0:naux*nq], \
                       sxq[0:nx*nq],srhs[0:nv*nq],sdqdx[0:nv*nx*nq],sicq[0:2*nfc], \
                       sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sdxdx[0:nx*nx*nq],this) \
       default(none)

      for( Int ic=ics;ic<ice;ic++ )
     {
         //wn[0]= wc[0][ic];
         //wn[1]= wc[1][ic];
         //wn[2]= wc[2][ic];
         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];
         //xn[0]= xc[0][ic];
         //xn[1]= xc[1][ic];
         //xn[2]= xc[2][ic];
         xn[0]= sxc[ADDR(0,ic,nfc)];
         xn[1]= sxc[ADDR(1,ic,nfc)];
         xn[2]= sxc[ADDR(2,ic,nfc)];

         //iql = icq[0][ic];
         //iqr = icq[1][ic];
         iql = sicq[ADDR(0,ic,nfc)];
         iqr = sicq[ADDR(1,ic,nfc)];

         bfluid = (sq[ADDR(nv0-1,iql,nq)]==1) && (sq[ADDR(nv0-1,iqr,nq)]==1);

         if(!bfluid) continue;

         //dq0 is not used here
         //grd->deltq( iql,iqr,sxq,sq,sdxdx,sdqdx,xn,wn,dql0,dqr0,dql,dqr );
         deltq( nv,iql,iqr,sxq,sq,sdxdx,sdqdx,xn,wn,dql0,dqr0,dql,dqr,nq );


         for( ia=0;ia<nv;ia++ )
        {
            //ql[ia]= q0[ia][iql]+ dql[ia];
            //qr[ia]= q0[ia][iqr]+ dqr[ia];
            ql[ia]= sq[ADDR(ia,iql,nq)]+ (iorder-1)*dql[ia];
            qr[ia]= sq[ADDR(ia,iqr,nq)]+ (iorder-1)*dqr[ia];
        }


// left state - auxiliary variables
         auxl[0]=  ql[4]/( rg*ql[3] );
         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;
         auxl[2]= gam*rg* ql[3]; 
         auxl[3]= auxl[2]/(gam-1)+ auxl[1];
         auxl[2]= sqrt( auxl[2] );

// right state - auxiliary variables
         auxr[0]= qr[4]/( rg*qr[3] );
         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;
         auxr[2]= gam*rg* qr[3]; 
         auxr[3]= auxr[2]/(gam-1)+ auxr[1];
         auxr[2]= sqrt( auxr[2] );

// fluxes from the left
         pl=   ql[4];
         rl=   auxl[0];
         al=   auxl[2];
         hl=   auxl[3];
         //unl=  wc[0][ic]*ql[0];
         //unl+= wc[1][ic]*ql[1];
         //unl+= wc[2][ic]*ql[2];
         unl=  wn[0]*ql[0];
         unl+= wn[1]*ql[1];
         unl+= wn[2]*ql[2];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0]+   wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1]+   wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2]+   wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+    wxdc[0][ic]*pl;
         fl[1]= fl[0]*ql[0]+   wn[0]*pl;
         fl[2]= fl[0]*ql[1]+   wn[1]*pl;
         fl[3]= fl[0]*ql[2]+   wn[2]*pl;
         fl[4]= fl[0]*hl+    swxdc[ADDR(0,ic,nfc)]*pl;
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia]; }

// fluxes from the right
         rr= auxr[0];
         pr= qr[4];
         rr= auxr[0];
         ar= auxr[2];
         hr= auxr[3];
         //unr=  wc[0][ic]*qr[0];
         //unr+= wc[1][ic]*qr[1];
         //unr+= wc[2][ic]*qr[2];
         unr=  wn[0]*qr[0];
         unr+= wn[1]*qr[1];
         unr+= wn[2]*qr[2];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         //fr[1]= fr[0]*qr[0]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+    wxdc[0][ic]*pr;
         fr[1]= fr[0]*qr[0]+ wn[0]*pr;
         fr[2]= fr[0]*qr[1]+ wn[1]*pr;
         fr[3]= fr[0]*qr[2]+ wn[2]*pr;
         fr[4]= fr[0]*hr+  swxdc[ADDR(0,ic,nfc)]*pr;
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         qa[0]= wl*ql[0]+ wr*qr[0];
         qa[1]= wl*ql[1]+ wr*qr[1];
         qa[2]= wl*ql[2]+ wr*qr[2];
         qa[3]= wl*ql[3]+ wr*qr[3];
         qa[4]= wl*ql[4]+ wr*qr[4];
         for( ia=5;ia<nv;ia++ )
        {
            qa[ia]= wl*ql[ia]+ wr*qr[ia];
        }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];
         ha= wl*hl+ wr*hr;
         ka*= 0.5; 
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps*lmax, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps*lmax, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= la1;
         //auxc[4][ic]= la3;
         //auxc[5][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= la1;
         sauxc[ADDR(4,ic,nfc)]= la3;
         sauxc[ADDR(5,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         dq[0]= qr[0]- ql[0];
         dq[1]= qr[1]- ql[1];
         dq[2]= qr[2]- ql[2];
         dq[3]= qr[3]- ql[3];
         dq[4]= qr[4]- ql[4];
         for( ia=5;ia<nv;ia++ )
        {
            dq[ia]= qr[ia]- ql[ia];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);
         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;
         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];
         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];
         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //rhs[0][iql]-= f[0];
         //rhs[1][iql]-= f[1];
         //rhs[2][iql]-= f[2];
         //rhs[3][iql]-= f[3];
         //rhs[4][iql]-= f[4];
         #pragma acc atomic 
         srhs[ADDR_(0,iql,nq)]-= f[0];
         #pragma acc atomic 
         srhs[ADDR_(1,iql,nq)]-= f[1];
         #pragma acc atomic 
         srhs[ADDR_(2,iql,nq)]-= f[2];
         #pragma acc atomic 
         srhs[ADDR_(3,iql,nq)]-= f[3];
         #pragma acc atomic 
         srhs[ADDR_(4,iql,nq)]-= f[4];

         //rhs[0][iqr]+= f[0];
         //rhs[1][iqr]+= f[1];
         //rhs[2][iqr]+= f[2];
         //rhs[3][iqr]+= f[3];
         //rhs[4][iqr]+= f[4];
         #pragma acc atomic 
         srhs[ADDR_(0,iqr,nq)]+= f[0];
         #pragma acc atomic 
         srhs[ADDR_(1,iqr,nq)]+= f[1];
         #pragma acc atomic 
         srhs[ADDR_(2,iqr,nq)]+= f[2];
         #pragma acc atomic 
         srhs[ADDR_(3,iqr,nq)]+= f[3];
         #pragma acc atomic 
         srhs[ADDR_(4,iqr,nq)]+= f[4];

         for( ia=nv0;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //rhs[ia][iql]-= f[ia];
            //rhs[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic 
            srhs[ADDR_(ia,iql,nq)]-= f[ia];
            #pragma acc atomic 
            srhs[ADDR_(ia,iqr,nq)]+= f[ia];
        }

         //auxc[nauxf-1][ic]*= wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]*= wn[3];
     }
      #pragma acc exit data delete(this)
  }

   void cMfRoeGasCHT::diflx33( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            unl,rl,pl,hl,fl[MxNVs];
      Real            unr,rr,pr,hr,fr[MxNVs];

      Real            ml, drl, dpl, drel, dunl;
      Real            mr, drr, dpr, drer, dunr;

      Int             ia,iql,iqr;

      Real            wl,wr,aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs],wn[4];
      bool            bfluid;

      Int nfc, nq;    

      nfc = wc.get_dim1();
      nq  = q.get_dim1();

      Int *sicq;
      Real *sq, *saux, *sdq, *sdaux, *sres, *swc, *swxdc, *sauxc;

      sicq  = icq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      sdq   = dq0.get_data();
      sdaux = daux.get_data();
      sres  = res.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      firstprivate(cp), \
      private(fl,fr,qa,ana,fa,dw2,dq,dw5,f,wn)\
      present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],\
               sres[0:nv*nq],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( Int ic=ics;ic<ice;ic++ )
     {
         //iql= icq[0][ic];
         //iqr= icq[1][ic];
         iql= sicq[ADDR(0,ic,nfc)];
         iqr= sicq[ADDR(1,ic,nfc)];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

         bfluid = (sq[ADDR(nv0-1,iql,nq)]==1) && (sq[ADDR(nv0-1,iqr,nq)]==1);

         if(!bfluid) continue;

// fluxes from the left

         //pl= q[4][iql];
         //rl= aux[0][iql];
         //hl= aux[3][iql];
         pl= sq[ADDR(4,iql,nq)];
         rl= saux[ADDR(0,iql,nq)];
         hl= saux[ADDR(3,iql,nq)];

         //drl=    dq0[0][iql];
         //dpl=  daux[4][iql];
         //drel=   dq0[4][iql];
         drl=    sdq[ADDR(0,iql,nq)];
         dpl=  sdaux[ADDR(4,iql,nq)];
         drel=   sdq[ADDR(4,iql,nq)];

         //unl=  wc[0][ic]*q[0][iql]; 
         //unl+= wc[1][ic]*q[1][iql]; 
         //unl+= wc[2][ic]*q[2][iql]; 
         //unl-= wxdc[0][ic];
         unl=  wn[0]*sq[ADDR(0,iql,nq)]; 
         unl+= wn[1]*sq[ADDR(1,iql,nq)]; 
         unl+= wn[2]*sq[ADDR(2,iql,nq)]; 
         unl-= swxdc[ADDR(0,ic,nfc)];

         //dunl=  wc[0][ic]*daux[0][iql]; 
         //dunl+= wc[1][ic]*daux[1][iql]; 
         //dunl+= wc[2][ic]*daux[2][iql]; 
         dunl=  wn[0]*sdaux[ADDR(0,iql,nq)]; 
         dunl+= wn[1]*sdaux[ADDR(1,iql,nq)]; 
         dunl+= wn[2]*sdaux[ADDR(2,iql,nq)]; 

         ml= unl*rl;
         fl[0]= drl*unl+ rl*dunl;
         //fl[1]= fl[0]*q[0][iql]+ ml*daux[0][iql]+  dpl*wc[0][ic]; 
         //fl[2]= fl[0]*q[1][iql]+ ml*daux[1][iql]+  dpl*wc[1][ic]; 
         //fl[3]= fl[0]*q[2][iql]+ ml*daux[2][iql]+  dpl*wc[2][ic]; 
         //fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*wxdc[0][ic];
         fl[1]= fl[0]*sq[ADDR(0,iql,nq)]+ ml*sdaux[ADDR(0,iql,nq)]+  dpl*wn[0]; 
         fl[2]= fl[0]*sq[ADDR(1,iql,nq)]+ ml*sdaux[ADDR(1,iql,nq)]+  dpl*wn[1]; 
         fl[3]= fl[0]*sq[ADDR(2,iql,nq)]+ ml*sdaux[ADDR(2,iql,nq)]+  dpl*wn[2]; 
         fl[4]= dunl*rl*hl+       unl*( drel+ dpl )+ dpl*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fl[ia]= fl[0]*q[ia][iql]+ ml*daux[ia][iql];
            fl[ia]= fl[0]*sq[ADDR(ia,iql,nq)]+ ml*sdaux[ADDR(ia,iql,nq)];
        }

// fluxes from the right

         //pr= q[4][iqr];
         //rr= aux[0][iqr];
         //hr= aux[3][iqr];
         pr= sq[ADDR(4,iqr,nq)];
         rr= saux[ADDR(0,iqr,nq)];
         hr= saux[ADDR(3,iqr,nq)];

         //drr=    dq0[0][iqr];
         //dpr=  daux[4][iqr];
         //drer=   dq0[4][iqr];
         drr=    sdq[ADDR(0,iqr,nq)];
         dpr=  sdaux[ADDR(4,iqr,nq)];
         drer=   sdq[ADDR(4,iqr,nq)];

         //unr=  wc[0][ic]*q[0][iqr]; 
         //unr+= wc[1][ic]*q[1][iqr]; 
         //unr+= wc[2][ic]*q[2][iqr]; 
         //unr-= wxdc[0][ic];
         unr=  wn[0]*sq[ADDR(0,iqr,nq)]; 
         unr+= wn[1]*sq[ADDR(1,iqr,nq)]; 
         unr+= wn[2]*sq[ADDR(2,iqr,nq)]; 
         unr-= swxdc[ADDR(0,ic,nfc)];

         //dunr=  wc[0][ic]*daux[0][iqr]; 
         //dunr+= wc[1][ic]*daux[1][iqr]; 
         //dunr+= wc[2][ic]*daux[2][iqr]; 
         dunr=  wn[0]*sdaux[ADDR(0,iqr,nq)]; 
         dunr+= wn[1]*sdaux[ADDR(1,iqr,nq)]; 
         dunr+= wn[2]*sdaux[ADDR(2,iqr,nq)]; 

         mr= unr*rr;
         fr[0]= drr*unr+ rr*dunr;
         //fr[1]= fr[0]*q[0][iqr]+ mr*daux[0][iqr]+ dpr*wc[0][ic]; 
         //fr[2]= fr[0]*q[1][iqr]+ mr*daux[1][iqr]+ dpr*wc[1][ic]; 
         //fr[3]= fr[0]*q[2][iqr]+ mr*daux[2][iqr]+ dpr*wc[2][ic]; 
         //fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*wxdc[0][ic];
         fr[1]= fr[0]*sq[ADDR(0,iqr,nq)]+ mr*sdaux[ADDR(0,iqr,nq)]+ dpr*wn[0]; 
         fr[2]= fr[0]*sq[ADDR(1,iqr,nq)]+ mr*sdaux[ADDR(1,iqr,nq)]+ dpr*wn[1]; 
         fr[3]= fr[0]*sq[ADDR(2,iqr,nq)]+ mr*sdaux[ADDR(2,iqr,nq)]+ dpr*wn[2]; 
         fr[4]= dunr*rr*hr+ unr*( drer+ dpr )+ dpr*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
            //fr[ia]= fr[0]*q[ia][iqr]+ mr*daux[ia][iqr];
            fr[ia]= fr[0]*sq[ADDR(ia,iqr,nq)]+ mr*sdaux[ADDR(ia,iqr,nq)];
        }

// retrieve Roe averages

         //wl=  auxc[0][ic];
         //wr=  auxc[1][ic];
         //ra=  auxc[2][ic];
         //la1= auxc[3][ic];
         //la3= auxc[4][ic];
         //la4= auxc[5][ic];
         wl=  sauxc[ADDR(0,ic,nfc)];
         wr=  sauxc[ADDR(1,ic,nfc)];
         ra=  sauxc[ADDR(2,ic,nfc)];
         la1= sauxc[ADDR(3,ic,nfc)];
         la3= sauxc[ADDR(4,ic,nfc)];
         la4= sauxc[ADDR(5,ic,nfc)];

         //qa[0]= wl*q[0][iql]+ wr*q[0][iqr];
         //qa[1]= wl*q[1][iql]+ wr*q[1][iqr];
         //qa[2]= wl*q[2][iql]+ wr*q[2][iqr];
         //qa[3]= wl*q[3][iql]+ wr*q[3][iqr];
         //qa[4]= wl*q[4][iql]+ wr*q[4][iqr];
         //for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*q[ia][iql]+ wr*q[ia][iqr]; }
         qa[0]= wl*sq[ADDR(0,iql,nq)]+ wr*sq[ADDR(0,iqr,nq)];
         qa[1]= wl*sq[ADDR(1,iql,nq)]+ wr*sq[ADDR(1,iqr,nq)];
         qa[2]= wl*sq[ADDR(2,iql,nq)]+ wr*sq[ADDR(2,iqr,nq)];
         qa[3]= wl*sq[ADDR(3,iql,nq)]+ wr*sq[ADDR(3,iqr,nq)];
         qa[4]= wl*sq[ADDR(4,iql,nq)]+ wr*sq[ADDR(4,iqr,nq)];
         for( ia=5;ia<nv;ia++ ){ qa[ia]= wl*sq[ADDR(ia,iql,nq)]+ wr*sq[ADDR(ia,iqr,nq)]; }

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// Left eigenvectors

         dr= drr- drl;
         dp= dpr- dpl;
        // dq[0]= daux[0][iqr]- daux[0][iql];
        // dq[1]= daux[1][iqr]- daux[1][iql];
        // dq[2]= daux[2][iqr]- daux[2][iql];
        // dq[3]= daux[3][iqr]- daux[3][iql];
        // dq[4]= daux[4][iqr]- daux[4][iql];
        // for( ia=5;ia<nv;ia++ )
        //{
        //    dq[ia]= daux[ia][iqr]- daux[ia][iql];
        //}
         dq[0]= sdaux[ADDR(0,iqr,nq)]- sdaux[ADDR(0,iql,nq)];
         dq[1]= sdaux[ADDR(1,iqr,nq)]- sdaux[ADDR(1,iql,nq)];
         dq[2]= sdaux[ADDR(2,iqr,nq)]- sdaux[ADDR(2,iql,nq)];
         dq[3]= sdaux[ADDR(3,iqr,nq)]- sdaux[ADDR(3,iql,nq)];
         dq[4]= sdaux[ADDR(4,iqr,nq)]- sdaux[ADDR(4,iql,nq)];
         for( ia=5;ia<nv;ia++ )
        {
            //dq[ia]= daux[ia][iqr]- daux[ia][iql];
            dq[ia]= sdaux[ADDR(ia,iqr,nq)]- sdaux[ADDR(ia,iql,nq)];
        }
         dun= dunr- dunl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]= dw1+               dw3+                  dw4;
         fa[1]= dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]= dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]= dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]= dw1*ka+    dw2a+   dw3*( ha+ unaa )+     dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++)
        { 
            fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; 
        }

// assemble 

         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //res[0][iql]-= f[0];
         //res[1][iql]-= f[1];
         //res[2][iql]-= f[2];
         //res[3][iql]-= f[3];
         //res[4][iql]-= f[4];
         #pragma acc atomic
         sres[ADDR_(0,iql,nq)]-= f[0];
         #pragma acc atomic
         sres[ADDR_(1,iql,nq)]-= f[1];
         #pragma acc atomic
         sres[ADDR_(2,iql,nq)]-= f[2];
         #pragma acc atomic
         sres[ADDR_(3,iql,nq)]-= f[3];
         #pragma acc atomic
         sres[ADDR_(4,iql,nq)]-= f[4];

         //res[0][iqr]+= f[0];
         //res[1][iqr]+= f[1];
         //res[2][iqr]+= f[2];
         //res[3][iqr]+= f[3];
         //res[4][iqr]+= f[4];
         #pragma acc atomic
         sres[ADDR_(0,iqr,nq)]+= f[0];
         #pragma acc atomic
         sres[ADDR_(1,iqr,nq)]+= f[1];
         #pragma acc atomic
         sres[ADDR_(2,iqr,nq)]+= f[2];
         #pragma acc atomic
         sres[ADDR_(3,iqr,nq)]+= f[3];
         #pragma acc atomic
         sres[ADDR_(4,iqr,nq)]+= f[4];

         for( ia=nv0;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //res[ia][iql]-= f[ia];
            //res[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic
            sres[ADDR_(ia,iql,nq)]-= f[ia];
            #pragma acc atomic
            sres[ADDR_(ia,iqr,nq)]+= f[ia];
        }

     }
     #pragma acc exit data delete (this)
  }

   void cMfRoeGasCHT::mflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx0, cAu3xView<Real>& rhs,
                              cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ) 
  {
//cout << "inside mflx33 ====================================\n";
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];
      Int             icase; //0:solid, 1: fluid, 2:fluid-solid interface
      Real            Tl, Tr, Tw, kl, kr, hl, hr, cp;

      Int nq, nfc;    
      
      nfc = xc.get_dim1();
      nq  = q0.get_dim1();

      Int *sicq;
      Real *sx, *sq, *saux, *sdqdx, *srhs, *sxc, *swc, *swxdc, *sauxc;

      sicq = icq.get_data();
      sx = x.get_data();
      sq = q0.get_data();
      saux = aux.get_data();
      sdqdx = dqdx0.get_data();
      srhs = rhs.get_data();
      sxc = xc.get_data();
      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxr,dqdxl)\
         present(sicq[0:nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nv*nx*nq], srhs[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)

         for( ic=ics;ic<ice;ic++ )
        {

            //iql= icq[0][ic]; 
            //iqr= icq[1][ic]; 
            iql= sicq[ADDR(0,ic,nfc)]; 
            iqr= sicq[ADDR(1,ic,nfc)]; 

                 if(sq[ADDR(nv0-1,iql,nq)]==0 && sq[ADDR(nv0-1,iqr,nq)]==0) icase = 0;
            else if(sq[ADDR(nv0-1,iql,nq)]==1 && sq[ADDR(nv0-1,iqr,nq)]==1) icase = 1;
            else                                                            icase = 2;

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            if(icase==2)
           {
               Tl = sq[ADDR(3,iql,nq)];
               Tr = sq[ADDR(3,iqr,nq)];
               kl = saux[ADDR(naux-1,iql,nq)];
               kr = saux[ADDR(naux-1,iqr,nq)];
               hl = kl/(wr+small); //wr is the distance between the left cell center to the face
               hr = kr/(wl+small); //wl is the distance between the right cell center to the face
               Tw = (Tl*hl + Tr*hr)/(hl+hr+small);

               dqn[3] = (sq[ADDR(3,iqr,nq)] - Tw)/wl; //wl is the distance between the right cell center to the face

               mu=    0;
               kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
               rho=   wl*saux[ADDR(     0,iql,nq)]+ wr*saux[ADDR(     0,iqr,nq)];
               cp=    wl*saux[ADDR(     4,iql,nq)]+ wr*saux[ADDR(     4,iqr,nq)];

               for(iv=0; iv<nv; iv++) taun[iv] = 0;
               taun[4]= -kr*dqn[3];

               //cout << kappa << " " << rho << " " << cp << " " << Tw << " " << sq[ADDR(3,iqr,nq)] << " " << wl << " " << iqr << " " << -kappa*dqn[3] << "\n";

               sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*kappa/(rho*cp*w);
           }
            else if(icase==1)
           {
               //fluid or solid
// normal gradients
               for( iv=0;iv<nv0;iv++ )
              {
                  dqdxl[iv][0] =  sdqdx[ADDR(iv,0,iql,nq)];
                  dqdxl[iv][1] =  sdqdx[ADDR(iv,1,iql,nq)];
                  dqdxl[iv][2] =  sdqdx[ADDR(iv,2,iql,nq)];

                  dqdxr[iv][0] =  sdqdx[ADDR(iv,0,iqr,nq)];
                  dqdxr[iv][1] =  sdqdx[ADDR(iv,1,iqr,nq)];
                  dqdxr[iv][2] =  sdqdx[ADDR(iv,2,iqr,nq)];
              }

               for( iv=0;iv<nv0;iv++ )
              {
                  //dqnl[iv]=  dqdx0[iv][0][iql]*wc[0][ic];
                  //dqnl[iv]+= dqdx0[iv][1][iql]*wc[1][ic];
                  //dqnl[iv]+= dqdx0[iv][2][iql]*wc[2][ic];
                  dqnl[iv]=  dqdxl[iv][0]*wn[0];
                  dqnl[iv]+= dqdxl[iv][1]*wn[1];
                  dqnl[iv]+= dqdxl[iv][2]*wn[2];

                  //dqnr[iv]=  dqdx0[iv][0][iqr]*wc[0][ic];
                  //dqnr[iv]+= dqdx0[iv][1][iqr]*wc[1][ic];
                  //dqnr[iv]+= dqdx0[iv][2][iqr]*wc[2][ic];
                  dqnr[iv]=  dqdxr[iv][0]*wn[0];
                  dqnr[iv]+= dqdxr[iv][1]*wn[1];
                  dqnr[iv]+= dqdxr[iv][2]*wn[2];

                  //dqn[iv]= ( q0[iv][iqr]-q0[iv][iql] )/w;
                  //q[iv]= wl*q0[iv][iql]+ wr*q0[iv][iqr];
                  dqn[iv]= ( sq[ADDR(iv,iqr,nq)]-sq[ADDR(iv,iql,nq)] )/w;
                  q[iv]= wl*sq[ADDR(iv,iql,nq)]+ wr*sq[ADDR(iv,iqr,nq)];
              }

// tangential gradients

               for( iv=0;iv<nv0;iv++ )
              {
                  //dqtl= dqdx0[iv][0][iql]- wc[0][ic]*dqnl[iv];
                  //dqtr= dqdx0[iv][0][iqr]- wc[0][ic]*dqnr[iv];
                  dqtl= dqdxl[iv][0]- wn[0]*dqnl[iv];
                  dqtr= dqdxr[iv][0]- wn[0]*dqnr[iv];
                  dqt[iv][0]= wl*dqtl+ wr*dqtr;

                  //dqtl= dqdx0[iv][1][iql]- wc[1][ic]*dqnl[iv];
                  //dqtr= dqdx0[iv][1][iqr]- wc[1][ic]*dqnr[iv];
                  dqtl= dqdxl[iv][1]- wn[1]*dqnl[iv];
                  dqtr= dqdxr[iv][1]- wn[1]*dqnr[iv];
                  dqt[iv][1]= wl*dqtl+ wr*dqtr;

                  //dqtl= dqdx0[iv][2][iql]- wc[2][ic]*dqnl[iv];
                  //dqtr= dqdx0[iv][2][iqr]- wc[2][ic]*dqnr[iv];
                  dqtl= dqdxl[iv][2]- wn[2]*dqnl[iv];
                  dqtr= dqdxr[iv][2]- wn[2]*dqnr[iv];
                  dqt[iv][2]= wl*dqtl+ wr*dqtr;
              }

// reconstruct gradient
               for( iv=0;iv<nv0;iv++ )
              {
                  //dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
                  //dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
                  //dqdx[iv][2]= dqn[iv]*wc[2][ic]+ dqt[iv][2];
                  dqdx[iv][0]= dqn[iv]*wn[0]+ dqt[iv][0];
                  dqdx[iv][1]= dqn[iv]*wn[1]+ dqt[iv][1];
                  dqdx[iv][2]= dqn[iv]*wn[2]+ dqt[iv][2];
              }

// stress tensor
               //mu=    wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
               //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
               //rho=   wl*aux[     0][iql]+ wr*aux[     0][iqr];
               mu=    wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
               kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
               rho=   wl*saux[ADDR(     0,iql,nq)]+ wr*saux[ADDR(     0,iqr,nq)];
               div=  dqdx[0][0];
               div+= dqdx[1][1];
               div+= dqdx[2][2];
               div*= 2./3.*mu;

               tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
               tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
               tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 
               tau[0][1]=  tau[1][0];
               tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
               tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 
               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

               tau[0][0]+= div;
               tau[1][1]+= div;
               tau[2][2]+= div;

// viscous fluxes

               //taun[1]=  tau[0][0]*wc[0][ic];
               //taun[1]+= tau[0][1]*wc[1][ic];
               //taun[1]+= tau[0][2]*wc[2][ic];
               taun[1]=  tau[0][0]*wn[0];
               taun[1]+= tau[0][1]*wn[1];
               taun[1]+= tau[0][2]*wn[2];

               //taun[2]=  tau[1][0]*wc[0][ic];
               //taun[2]+= tau[1][1]*wc[1][ic];
               //taun[2]+= tau[1][2]*wc[2][ic];
               taun[2]=  tau[1][0]*wn[0];
               taun[2]+= tau[1][1]*wn[1];
               taun[2]+= tau[1][2]*wn[2];

               //taun[3]=  tau[2][0]*wc[0][ic];
               //taun[3]+= tau[2][1]*wc[1][ic];
               //taun[3]+= tau[2][2]*wc[2][ic];
               taun[3]=  tau[2][0]*wn[0];
               taun[3]+= tau[2][1]*wn[1];
               taun[3]+= tau[2][2]*wn[2];


               taun[4]= -kappa*dqn[3];
               taun[4]+= taun[1]*q[0];
               taun[4]+= taun[2]*q[1];
               taun[4]+= taun[3]*q[2];
               sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*mu/(rho*w);
           }
            else if(icase==0)
           {

// normal gradients
               iv=3;
               dqn[iv]= ( sq[ADDR(iv,iqr,nq)]-sq[ADDR(iv,iql,nq)] )/w;

               for(iv=0; iv<nv; iv++) taun[iv] = 0;

// heat flux
               mu=    0;
               kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
               rho=   wl*saux[ADDR(     0,iql,nq)]+ wr*saux[ADDR(     0,iqr,nq)];
               cp=    wl*saux[ADDR(     4,iql,nq)]+ wr*saux[ADDR(     4,iqr,nq)];

               for(iv=0; iv<nv; iv++) taun[iv] = 0;
               taun[4]= -kappa*dqn[3];
               sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*kappa/(rho*cp*w);
           }

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               //rhs[iv][iqr]+= taun[iv]*wc[3][ic];
               //rhs[iv][iql]-= taun[iv]*wc[3][ic];
               #pragma acc atomic
               srhs[ADDR_(iv,iqr,nq)]+= taun[iv]*wn[3];
               #pragma acc atomic
               srhs[ADDR_(iv,iql,nq)]-= taun[iv]*wn[3];
           }
            //auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            
        }
        #pragma acc exit data delete(this)
     }
  }

/*   void cMfRoeGasCHT::dmflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                 cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                 cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv;
      Int             nql, nqr;
      Int             icase;

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sxl    = xl.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdql   = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl  = resl.get_data();
      icqr   = icqr_view.get_data();
      sxr    = xr.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqr   = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr  = resr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      if( ice > ics )
     { 
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr)\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun,wn,xn)\
         present (sxl[0:nx*nql],sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
                  icqr[0:nfc],sxr[0:nx*nqr],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
                  sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];

                 if(sqr[ADDR(nv0-1,iqr,nqr)]==0) icase = 0;
            else if(sqr[ADDR(nv0-1,iqr,nqr)]==1) icase = 1;
            else                                 icase = 2;

            if(icase==0)
           {
// distance of DOF positions from face centre

               wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
               wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
               wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );
   
               w= wl;
               wl = 0.5;
               wr = 0.5;

//linearized gradients
               iv = 3;
               ddqn[iv]= ( sdauxr[ADDR(iv,iqr,nqr)]- 0. )/w; //boundary value is fixed

               kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];

//linearized heat flux
               for(iv=0; iv<nv; iv++) dtaun[iv] = 0;
               dtaun[4]= -kappa*ddqn[3];
           }
            else if(icase==1)
           {
// distance of DOF positions from face centre

               //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
               //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
               //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
               wr=  wn[0]*( xn[0]- sxl[ADDR(0,iql,nql)] );
               wr+= wn[1]*( xn[1]- sxl[ADDR(1,iql,nql)] );
               wr+= wn[2]*( xn[2]- sxl[ADDR(2,iql,nql)] );
   
               //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
               //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
               //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
               wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
               wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
               wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );
   
               w= wl+wr;
               wl/= w; 
               wr/= w; 

               for( iv=0;iv<nv0;iv++ )
              {
                  //ddqn[iv]= ( dauxr[iv][iqr]- dauxl[iv][iql] )/w;
                  //dqn[iv]=  ( qr[iv][iqr]-       ql[iv][iql] )/w;
                  ddqn[iv]= ( sdauxr[ADDR(iv,iqr,nqr)]- sdauxl[ADDR(iv,iql,nql)] )/w;
                  dqn[iv]=  (    sqr[ADDR(iv,iqr,nqr)]-    sql[ADDR(iv,iql,nql)] )/w;

                  //q[iv]=  wl*   ql[iv][iql]+ wr*   qr[iv][iqr];
                  //dq[iv]= wl*dauxl[iv][iql]+ wr*dauxr[iv][iqr];
                  q[iv]=  wl*   sql[ADDR(iv,iql,nql)]+ wr*   sqr[ADDR(iv,iqr,nqr)];
                  dq[iv]= wl*sdauxl[ADDR(iv,iql,nql)]+ wr*sdauxr[ADDR(iv,iqr,nqr)];

              }

               for( iv=0;iv<nv0;iv++ )
              {
                  //dqdx[iv][0]= dqn[iv]*wc[0][ic];
                  //dqdx[iv][1]= dqn[iv]*wc[1][ic];
                  //dqdx[iv][2]= dqn[iv]*wc[2][ic];
                  dqdx[iv][0]= dqn[iv]*wn[0];
                  dqdx[iv][1]= dqn[iv]*wn[1];
                  dqdx[iv][2]= dqn[iv]*wn[2];

                  //ddqdx[iv][0]= ddqn[iv]*wc[0][ic];
                  //ddqdx[iv][1]= ddqn[iv]*wc[1][ic];
                  //ddqdx[iv][2]= ddqn[iv]*wc[2][ic];
                  ddqdx[iv][0]= ddqn[iv]*wn[0];
                  ddqdx[iv][1]= ddqn[iv]*wn[1];
                  ddqdx[iv][2]= ddqn[iv]*wn[2];
              }

// stress tensor

               //mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
               //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
               //rho=   wl*auxl[0][iql]+      wr*auxr[0][iqr];
               mu=    wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
               kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
               rho=   wl*sauxl[ADDR(0,iql,nql)]+      wr*sauxr[ADDR(0,iqr,nqr)];

               div =  dqdx[0][0];
               div+=  dqdx[1][1];
               div+=  dqdx[2][2];
               div*= 2./3.*mu;

               ddiv =  ddqdx[0][0];
               ddiv+=  ddqdx[1][1];
               ddiv+=  ddqdx[2][2];
               ddiv*= 2./3.*mu;

               tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
               tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
               tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 

               tau[0][1]=  tau[1][0];
               tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
               tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 

               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

               dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
               dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 
               dtau[2][0]= -mu*( ddqdx[0][2]+ ddqdx[2][0] ); 

               dtau[0][1]=  dtau[1][0];
               dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 
               dtau[2][1]= -mu*( ddqdx[1][2]+ ddqdx[2][1] ); 

               dtau[0][2]=  dtau[2][0];
               dtau[1][2]=  dtau[2][1];
               dtau[2][2]= -mu*( ddqdx[2][2]+ ddqdx[2][2] ); 

               tau[0][0]+=  div;
               tau[1][1]+=  div;
               tau[2][2]+=  div;

               dtau[0][0]+= ddiv;
               dtau[1][1]+= ddiv;
               dtau[2][2]+= ddiv;

// viscous flux

               //taun[1]=    tau[0][0]*wc[0][ic];
               //taun[1]+=   tau[0][1]*wc[1][ic];
               //taun[1]+=   tau[0][2]*wc[2][ic];
               taun[1]=    tau[0][0]*wn[0];
               taun[1]+=   tau[0][1]*wn[1];
               taun[1]+=   tau[0][2]*wn[2];

               //taun[2]=    tau[1][0]*wc[0][ic];
               //taun[2]+=   tau[1][1]*wc[1][ic];
               //taun[2]+=   tau[1][2]*wc[2][ic];
               taun[2]=    tau[1][0]*wn[0];
               taun[2]+=   tau[1][1]*wn[1];
               taun[2]+=   tau[1][2]*wn[2];

               //taun[3]=    tau[2][0]*wc[0][ic];
               //taun[3]+=   tau[2][1]*wc[1][ic];
               //taun[3]+=   tau[2][2]*wc[2][ic];
               taun[3]=    tau[2][0]*wn[0];
               taun[3]+=   tau[2][1]*wn[1];
               taun[3]+=   tau[2][2]*wn[2];

               //dtaun[1]=   dtau[0][0]*wc[0][ic];
               //dtaun[1]+=  dtau[0][1]*wc[1][ic];
               //dtaun[1]+=  dtau[0][2]*wc[2][ic];
               dtaun[1]=   dtau[0][0]*wn[0];
               dtaun[1]+=  dtau[0][1]*wn[1];
               dtaun[1]+=  dtau[0][2]*wn[2];

               //dtaun[2]=   dtau[1][0]*wc[0][ic];
               //dtaun[2]+=  dtau[1][1]*wc[1][ic];
               //dtaun[2]+=  dtau[1][2]*wc[2][ic];
               dtaun[2]=   dtau[1][0]*wn[0];
               dtaun[2]+=  dtau[1][1]*wn[1];
               dtaun[2]+=  dtau[1][2]*wn[2];

               //dtaun[3]=   dtau[2][0]*wc[0][ic];
               //dtaun[3]+=  dtau[2][1]*wc[1][ic];
               //dtaun[3]+=  dtau[2][2]*wc[2][ic];
               dtaun[3]=   dtau[2][0]*wn[0];
               dtaun[3]+=  dtau[2][1]*wn[1];
               dtaun[3]+=  dtau[2][2]*wn[2];

               dtaun[4]= -kappa*ddqn[3];
               dtaun[4]+= taun[1]*dq[0]+ dtaun[1]*q[0];
               dtaun[4]+= taun[2]*dq[1]+ dtaun[2]*q[1];
               dtaun[4]+= taun[3]*dq[2]+ dtaun[3]*q[2];

               dtaun[nv0-1] = 0;
           } 
            else 
           {
               cout << "linearized flux on the boundary for the fluid-solid interface?\n";
               assert(0);
           }

// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               //resr[iv][iqr]+= dtaun[iv]*wc[3][ic];
               //resl[iv][iql]-= dtaun[iv]*wc[3][ic];
               #pragma acc atomic
               sresr[ADDR_(iv,iqr,nqr)]+= dtaun[iv]*wn[3];
               #pragma acc atomic
               sresl[ADDR_(iv,iql,nql)]-= dtaun[iv]*wn[3];
           }
        }
        #pragma acc exit data delete(this)
     }
  }*/

   void cMfRoeGasCHT::dmflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                               cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv;
      Real            dTl, dTr, dTw, kl, kr, hl, hr;
      Int             icase; 

      Int nfc, nq;
      Int *sicq;      
      Real *sx, *sq, *saux, *sdq, *sdaux, *sres, *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = q0.get_dim1();

      sicq  = icq.get_data();
      sx    = x.get_data();
      sq    = q0.get_data();
      saux  = aux.get_data();
      sdq   = dq0.get_data();
      sdaux = daux.get_data();
      sres  = res.get_data();
      sxc   = xc.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun,wn,xn)\
         present (sicq[0:2*nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],\
                  sres[0:nv*nq],sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
 
         for( ic=ics;ic<ice;ic++ )
        {
            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

                 if(sq[ADDR(nv0-1,iql,nq)]==0 && sq[ADDR(nv0-1,iqr,nq)]==0) icase = 0;
            else if(sq[ADDR(nv0-1,iql,nq)]==1 && sq[ADDR(nv0-1,iqr,nq)]==1) icase = 1;
            else                                                            icase = 2;

            if(icase==0)
           {
               //for( iv=0;iv<nv0;iv++ )
               iv = 3;
               ddqn[iv]= ( sdaux[ADDR(iv,iqr,nq)]- sdaux[ADDR(iv,iql,nq)] )/w;

// linearized heat flux

               //mu=    wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
               //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
               //rho=   wl*aux[0][iql]+      wr*aux[0][iqr];
               mu=    0;
               kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
               rho=   wl*saux[ADDR(0,iql,nq)]+      wr*saux[ADDR(0,iqr,nq)];
  
               for(iv=0; iv<nv; iv++) dtaun[iv] = 0;
               dtaun[4]= -kappa*ddqn[3];
           }
            else if(icase==1)
           {
               for( iv=0;iv<nv0;iv++ )
              {
                  //ddqn[iv]= ( daux[iv][iqr]- daux[iv][iql] )/w;
                  //dqn[iv]=  ( q0[iv][iqr]-       q0[iv][iql] )/w;
                  ddqn[iv]= ( sdaux[ADDR(iv,iqr,nq)]- sdaux[ADDR(iv,iql,nq)] )/w;
                  dqn[iv]=  (    sq[ADDR(iv,iqr,nq)]-    sq[ADDR(iv,iql,nq)] )/w;

                  //q[iv]=  wl*  q0[iv][iql]+ wr*  q0[iv][iqr];
                  //dq[iv]= wl*daux[iv][iql]+ wr*daux[iv][iqr];
                  q[iv]=  wl*   sq[ADDR(iv,iql,nq)]+ wr*   sq[ADDR(iv,iqr,nq)];
                  dq[iv]= wl*sdaux[ADDR(iv,iql,nq)]+ wr*sdaux[ADDR(iv,iqr,nq)];

              }

               for( iv=0;iv<nv0;iv++ )
              {
                  //dqdx[iv][0]= dqn[iv]*wc[0][ic];
                  //dqdx[iv][1]= dqn[iv]*wc[1][ic];
                  //dqdx[iv][2]= dqn[iv]*wc[2][ic];
                  dqdx[iv][0]= dqn[iv]*wn[0];
                  dqdx[iv][1]= dqn[iv]*wn[1];
                  dqdx[iv][2]= dqn[iv]*wn[2];

                  ddqdx[iv][0]= ddqn[iv]*wn[0];
                  ddqdx[iv][1]= ddqn[iv]*wn[1];
                  ddqdx[iv][2]= ddqn[iv]*wn[2];
              }

// stress tensor

               //mu=    wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
               //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
               //rho=   wl*aux[0][iql]+      wr*aux[0][iqr];
               mu=    wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
               kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
               rho=   wl*saux[ADDR(0,iql,nq)]+      wr*saux[ADDR(0,iqr,nq)];

               div =  dqdx[0][0];
               div+=  dqdx[1][1];
               div+=  dqdx[2][2];
               div*= 2./3.*mu;

               ddiv =  ddqdx[0][0];
               ddiv+=  ddqdx[1][1];
               ddiv+=  ddqdx[2][2];
               ddiv*= 2./3.*mu;

               tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
               tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
               tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 

               tau[0][1]=  tau[1][0];
               tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
               tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 

               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

               dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
               dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 
               dtau[2][0]= -mu*( ddqdx[0][2]+ ddqdx[2][0] ); 

               dtau[0][1]=  dtau[1][0];
               dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 
               dtau[2][1]= -mu*( ddqdx[1][2]+ ddqdx[2][1] ); 

               dtau[0][2]=  dtau[2][0];
               dtau[1][2]=  dtau[2][1];
               dtau[2][2]= -mu*( ddqdx[2][2]+ ddqdx[2][2] ); 

               tau[0][0]+=  div;
               tau[1][1]+=  div;
               tau[2][2]+=  div;

               dtau[0][0]+= ddiv;
               dtau[1][1]+= ddiv;
               dtau[2][2]+= ddiv;

// viscous flux

               //taun[1]=    tau[0][0]*wc[0][ic];
               //taun[1]+=   tau[0][1]*wc[1][ic];
               //taun[1]+=   tau[0][2]*wc[2][ic];
               taun[1]=    tau[0][0]*wn[0];
               taun[1]+=   tau[0][1]*wn[1];
               taun[1]+=   tau[0][2]*wn[2];

               //taun[2]=    tau[1][0]*wc[0][ic];
               //taun[2]+=   tau[1][1]*wc[1][ic];
               //taun[2]+=   tau[1][2]*wc[2][ic];
               taun[2]=    tau[1][0]*wn[0];
               taun[2]+=   tau[1][1]*wn[1];
               taun[2]+=   tau[1][2]*wn[2];

               //taun[3]=    tau[2][0]*wc[0][ic];
               //taun[3]+=   tau[2][1]*wc[1][ic];
               //taun[3]+=   tau[2][2]*wc[2][ic];
               taun[3]=    tau[2][0]*wn[0];
               taun[3]+=   tau[2][1]*wn[1];
               taun[3]+=   tau[2][2]*wn[2];

               //dtaun[1]=   dtau[0][0]*wc[0][ic];
               //dtaun[1]+=  dtau[0][1]*wc[1][ic];
               //dtaun[1]+=  dtau[0][2]*wc[2][ic];
               dtaun[1]=   dtau[0][0]*wn[0];
               dtaun[1]+=  dtau[0][1]*wn[1];
               dtaun[1]+=  dtau[0][2]*wn[2];

               //dtaun[2]=   dtau[1][0]*wc[0][ic];
               //dtaun[2]+=  dtau[1][1]*wc[1][ic];
               //dtaun[2]+=  dtau[1][2]*wc[2][ic];
               dtaun[2]=   dtau[1][0]*wn[0];
               dtaun[2]+=  dtau[1][1]*wn[1];
               dtaun[2]+=  dtau[1][2]*wn[2];

               //dtaun[3]=   dtau[2][0]*wc[0][ic];
               //dtaun[3]+=  dtau[2][1]*wc[1][ic];
               //dtaun[3]+=  dtau[2][2]*wc[2][ic];
               dtaun[3]=   dtau[2][0]*wn[0];
               dtaun[3]+=  dtau[2][1]*wn[1];
               dtaun[3]+=  dtau[2][2]*wn[2];

               dtaun[4]= -kappa*ddqn[3];
               dtaun[4]+= taun[1]*dq[0]+ dtaun[1]*q[0];
               dtaun[4]+= taun[2]*dq[1]+ dtaun[2]*q[1];
               dtaun[4]+= taun[3]*dq[2]+ dtaun[3]*q[2];
           }
            else if(icase==2)
           {
               kl = saux[ADDR(naux-1,iql,nq)];
               kr = saux[ADDR(naux-1,iqr,nq)];

               iv = 3;
               dTr = sdaux[ADDR(iv,iqr,nq)]; 
               dTl = sdaux[ADDR(iv,iql,nq)];

               hl = kl/(wr+small); //wr is the distance between the left cell center to the face
               hr = kr/(wl+small); //wl is the distance between the right cell center to the face

               dTw = dTl*hl/(hl+hr) + dTr*hr/(hl+hr);

               ddqn[iv]= ( sdaux[ADDR(iv,iqr,nq)]- dTw )/w;

               //kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];

               for(iv=0; iv<nv; iv++) dtaun[iv] = 0;
               dtaun[4]= -kr*ddqn[3];
           } 
// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               //res[iv][iqr]+= dtaun[iv]*wc[3][ic];
               //res[iv][iql]-= dtaun[iv]*wc[3][ic];
               #pragma acc atomic
               sres[ADDR_(iv,iqr,nq)]+= dtaun[iv]*wn[3];
               #pragma acc atomic
               sres[ADDR_(iv,iql,nq)]-= dtaun[iv]*wn[3];
           }
        }
        #pragma acc exit data delete(this)
     }
  }

/*   void cMfRoeGasCHT::mflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl0, cAu3xView<Real>& rhsl,
                                                cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr0, cAu3xView<Real>& rhsr,
                                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div, cp;
      Int             nql, nqr;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];
      Int             icase;

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdqdxl, *srhsl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqdxr, *srhsr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sxl    = xl.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdqdxl = dqdxl0.get_data();
      srhsl  = rhsl.get_data();
      icqr   = icqr_view.get_data();
      sxr    = xr.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqdxr = dqdxr0.get_data();
      srhsr  = rhsr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr) \
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxl,dqdxr)\
         present(sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],sdqdxl[0:nv*nx*nfc],srhsl[0:nv*nfc],\
                 icqr[0:nfc],sxr[0:nx*nq],sqr[0:nv*nq],sauxr[0:naux*nq],sdqdxr[0:nv*nx*nq],srhsr[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];

                 if(sqr[ADDR(nv0-1,iqr,nqr)]==0) icase = 0;
            else if(sqr[ADDR(nv0-1,iqr,nqr)]==1) icase = 1;
            else                                 icase = 2;

            if(icase==0)
           {
// distance of DOF positions from face centre

               wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
               wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
               wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

               w= wl;
               wl = 0.5;
               wr = 0.5;

// normal gradients
               iv=3;
               dqn[iv]= ( sqr[ADDR(iv,iqr,nqr)]-sql[ADDR(iv,iql,nql)] )/w;


               kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
               rho=   wl*sauxl[ADDR(     0,iql,nql)]+ wr*sauxr[ADDR(     0,iqr,nqr)];
               cp=    wl*sauxl[ADDR(     4,iql,nql)]+ wr*sauxr[ADDR(     4,iqr,nqr)];
 
               for(iv=0; iv<nv; iv++) taun[iv] = 0;
               taun[4]= -kappa*dqn[3];

               sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*kappa/(rho*cp*w);
           }
            else if(icase==1)
           {
// distance of DOF positions from face centre

               //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
               //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
               //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
               wr=  wn[0]*( xn[0]- sxl[ADDR(0,iql,nql)] );
               wr+= wn[1]*( xn[1]- sxl[ADDR(1,iql,nql)] );
               wr+= wn[2]*( xn[2]- sxl[ADDR(2,iql,nql)] );
   
               //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
               //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
               //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
               wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
               wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
               wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

               w= wl+wr;
               wl/= w; 
               wr/= w; 

// normal gradients
               for( iv=0;iv<nv0;iv++ )
              {
                  dqdxl[iv][0] = sdqdxl[ADDR(iv,0,iql,nql)];
                  dqdxl[iv][1] = sdqdxl[ADDR(iv,1,iql,nql)];
                  dqdxl[iv][2] = sdqdxl[ADDR(iv,2,iql,nql)];

                  dqdxr[iv][0] = sdqdxr[ADDR(iv,0,iqr,nqr)];
                  dqdxr[iv][1] = sdqdxr[ADDR(iv,1,iqr,nqr)];
                  dqdxr[iv][2] = sdqdxr[ADDR(iv,2,iqr,nqr)];
              }

               for( iv=0;iv<nv0;iv++ )
              {
                  //dqnl[iv]=  dqdxl[iv][0][iql]*wc[0][ic];
                  //dqnl[iv]+= dqdxl[iv][1][iql]*wc[1][ic];
                  //dqnl[iv]+= dqdxl[iv][2][iql]*wc[2][ic];
                  dqnl[iv]=  dqdxl[iv][0]*wn[0];
                  dqnl[iv]+= dqdxl[iv][1]*wn[1];
                  dqnl[iv]+= dqdxl[iv][2]*wn[2];

                  //dqnr[iv]=  dqdxr[iv][0][iqr]*wc[0][ic];
                  //dqnr[iv]+= dqdxr[iv][1][iqr]*wc[1][ic];
                  //dqnr[iv]+= dqdxr[iv][2][iqr]*wc[2][ic];
                  dqnr[iv]=  dqdxr[iv][0]*wn[0];
                  dqnr[iv]+= dqdxr[iv][1]*wn[1];
                  dqnr[iv]+= dqdxr[iv][2]*wn[2];

                  //dqn[iv]= ( qr[iv][iqr]-ql[iv][iql] )/w;
                  //q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
                  dqn[iv]= ( sqr[ADDR(iv,iqr,nqr)]-sql[ADDR(iv,iql,nql)] )/w;
                  q[iv]= wl*sql[ADDR(iv,iql,nql)]+ wr*sqr[ADDR(iv,iqr,nqr)];
              }

// tangential gradients

               for( iv=0;iv<nv0;iv++ )
              {
                  //dqtl= dqdxl[iv][0][iql]- wc[0][ic]*dqnl[iv];
                  //dqtr= dqdxr[iv][0][iqr]- wc[0][ic]*dqnr[iv];
                  dqtl= dqdxl[iv][0]- wn[0]*dqnl[iv];
                  dqtr= dqdxr[iv][0]- wn[0]*dqnr[iv];
                  dqt[iv][0]= wl*dqtl+ wr*dqtr;

                  //dqtl= dqdxl[iv][1][iql]- wc[1][ic]*dqnl[iv];
                  //dqtr= dqdxr[iv][1][iqr]- wc[1][ic]*dqnr[iv];
                  dqtl= dqdxl[iv][1]- wn[1]*dqnl[iv];
                  dqtr= dqdxr[iv][1]- wn[1]*dqnr[iv];
                  dqt[iv][1]= wl*dqtl+ wr*dqtr;

                  //dqtl= dqdxl[iv][2][iql]- wc[2][ic]*dqnl[iv];
                  //dqtr= dqdxr[iv][2][iqr]- wc[2][ic]*dqnr[iv];
                  dqtl= dqdxl[iv][2]- wn[2]*dqnl[iv];
                  dqtr= dqdxr[iv][2]- wn[2]*dqnr[iv];
                  dqt[iv][2]= wl*dqtl+ wr*dqtr;
              }

// reconstruct gradient
               for( iv=0;iv<nv0;iv++ )
              {
                  //dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
                  //dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
                  //dqdx[iv][2]= dqn[iv]*wc[2][ic]+ dqt[iv][2];
                  dqdx[iv][0]= dqn[iv]*wn[0]+ dqt[iv][0];
                  dqdx[iv][1]= dqn[iv]*wn[1]+ dqt[iv][1];
                  dqdx[iv][2]= dqn[iv]*wn[2]+ dqt[iv][2];
              }

// stress tensor
            //mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho=   wl*auxl[     0][iql]+ wr*auxr[     0][iqr];
               mu=    wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
               kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
               rho=   wl*sauxl[ADDR(     0,iql,nql)]+ wr*sauxr[ADDR(     0,iqr,nqr)];
               div=  dqdx[0][0];
               div+= dqdx[1][1];
               div+= dqdx[2][2];
               div*= 2./3.*mu;

               tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
               tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
               tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 
               tau[0][1]=  tau[1][0];
               tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
               tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 
               tau[0][2]=  tau[2][0];
               tau[1][2]=  tau[2][1];
               tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

               tau[0][0]+= div;
               tau[1][1]+= div;
               tau[2][2]+= div;

// viscous fluxes

               //taun[1]=  tau[0][0]*wc[0][ic];
               //taun[1]+= tau[0][1]*wc[1][ic];
               //taun[1]+= tau[0][2]*wc[2][ic];
               taun[1]=  tau[0][0]*wn[0];
               taun[1]+= tau[0][1]*wn[1];
               taun[1]+= tau[0][2]*wn[2];

               //taun[2]=  tau[1][0]*wc[0][ic];
               //taun[2]+= tau[1][1]*wc[1][ic];
               //taun[2]+= tau[1][2]*wc[2][ic];
               taun[2]=  tau[1][0]*wn[0];
               taun[2]+= tau[1][1]*wn[1];
               taun[2]+= tau[1][2]*wn[2];

               //taun[3]=  tau[2][0]*wc[0][ic];
               //taun[3]+= tau[2][1]*wc[1][ic];
               //taun[3]+= tau[2][2]*wc[2][ic];
               taun[3]=  tau[2][0]*wn[0];
               taun[3]+= tau[2][1]*wn[1];
               taun[3]+= tau[2][2]*wn[2];


               taun[4]= -kappa*dqn[3];
               taun[4]+= taun[1]*q[0];
               taun[4]+= taun[2]*q[1];
               taun[4]+= taun[3]*q[2];

               sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*mu/(rho*w);
           }
            else
           {
               cout << "boundary mflx for the solid and fluid interface??\n";
               assert(0);
           }

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               //rhsr[iv][iqr]+= taun[iv]*wc[3][ic];
               //rhsl[iv][iql]-= taun[iv]*wc[3][ic];
               #pragma acc atomic
               srhsr[ADDR_(iv,iqr,nqr)]+= taun[iv]*wn[3];
               #pragma acc atomic
               srhsl[ADDR_(iv,iql,nql)]-= taun[iv]*wn[3];
           }
            //auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            
        }
        #pragma acc exit data delete(this)
     }
  }*/

   void cMfRoeGasCHT::qupd( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux )
  {
      Int iv,iq;
  
      Int nq;
      Real *sq, *saux, *sdq, *sdaux;

      nq    = q.get_dim1();

      sq    = q.get_data();
      saux  = aux.get_data();
      sdq   = dq.get_data();
      sdaux = daux.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present (sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv0-1;iv++ )
           {
               sq[ADDR(iv,iq,nq)]+= sdaux[ADDR(iv,iq,nq)];
           }

            //nv0-1 is the marker for solid and fluid, this should not change

            for( iv=nv0;iv<nv;iv++ )
           {
               sq[ADDR(iv,iq,nq)]+= sdaux[ADDR(iv,iq,nq)];
           }
        }
        #pragma acc exit data copyout(this)
     }
  }

//   void cMfRoeGasCHT::mflx( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& rhsl,
//                                              cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& rhsr,
//                                              cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
//  {
//      if( vsc->viscous() )
//     {
//         if( ice > ics )
//        {
//            if(vsc->gettype() != sbslearsm_visc)
//           {
//               mflx33( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr,xc,wc,wxdc,auxc );
//           }
//            vsc->mflx( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr,xc,wc,wxdc,auxc );
//        }
//     }
//  }

   void cMfRoeGasCHT::wflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,  
                                               cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr, 
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real f[MxNVs],wn[4];
      Int  ic,iql,iqr,nql,nqr;

      Int nfc, nq, id;    
      Int *icql;      
      Real *sql, *sauxl, *srhsl;
      Int *icqr;
      Real *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq = qr.get_dim1();
  
      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data(); 
      sauxr = auxr.get_data(); 
      srhsr = rhsr.get_data();
      swc   = wc.get_data(); 
      swxdc = wxdc.get_data(); 
      sauxc = auxc.get_data();

      nql = nfc;
      nqr = nq;

      //update a specific element on the host and check if this is an inviscid wall for the fluid
      bool bfluid;
      iqr= icqr[ics];
      id = ADDR(nv0-1,iqr,nqr);
     #pragma acc update host (sqr[id:1])
      bfluid = (sqr[ADDR(nv0-1,iqr,nqr)]==1);
      if(!bfluid) return;

      #pragma acc enter data copyin(this)
      #pragma acc parallel loop \
       firstprivate(nql,nqr) \
       private(f,wn)\
       present(            sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left
         //f[1]=    wc[0][ic]* qr[4][iqr]* wc[3][ic];
         //f[2]=    wc[1][ic]* qr[4][iqr]* wc[3][ic];
         //f[3]=    wc[2][ic]* qr[4][iqr]* wc[3][ic];
         //f[4]=  wxdc[0][ic]* qr[4][iqr]* wc[3][ic];
         f[1]=    wn[0]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[2]=    wn[1]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[3]=    wn[2]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[4]=  swxdc[ADDR(0,ic,nfc)]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         //auxc[nauxf-1][ic]= ( auxr[2][iqr]+fabs(wxdc[0][ic]) )*wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]= ( sauxr[ADDR(2,iqr,nqr)]+fabs(swxdc[ADDR(0,ic,nfc)]) )*wn[3];

         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nql)]-= f[4];

         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nqr)]+= f[4];
     }
      #pragma acc exit data delete(this)
  }

//I have to copy this function from cMfRoeGas, otherwise I will get a segfault with OpenACC, don't know why
   void cMfRoeGasCHT::iflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                               cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            wl,wr;
      Real            al,unl,rl,ll1,ll3,ll4,pl,hl,fl[MxNVs];
      Real            ar,unr,rr,lr1,lr3,lr4,pr,hr,fr[MxNVs];
      Real            le1,le3,le4;
      Real            aa,a2a,ra,ha,ka,qa[MxNVs],ana[3],una,unaa,raa, la1,la4,la3,lmax,fa[MxNVs];
      Real            dw1,dw3,dw4,dw2[3],dw2a,dr,dq[MxNVs],dun,dp,dpa,dw5[MxNVs];
      Real            f[MxNVs],wn[4];

      Int             ia,ic,iql,iqr;
      Int             nql,nqr;

      Int nfc, nq;
      Int *icql;
      Real *sql, *sauxl, *srhsl;
      Int *icqr; 
      Real *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      srhsr = rhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();


      nql = nfc;
      nqr = nq;
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       firstprivate(nql,nqr) \
       private(fl,fr,qa,ana,fa,dw2,dq,dw5,f,wn)\
       present(            sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)
      for( ic=ics;ic<ice;ic++ )
     {

         iql= ic;
         iqr= icqr[ic];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];
 
// fluxes from the left

         //pl= ql[4][iql];
         //rl= auxl[0][iql];
         //al= auxl[2][iql];
         //hl= auxl[3][iql];
         pl= sql[ADDR(4,iql,nql)];
         rl= sauxl[ADDR(0,iql,nql)];
         al= sauxl[ADDR(2,iql,nql)];
         hl= sauxl[ADDR(3,iql,nql)];

         //unl=  wc[0][ic]*ql[0][iql];
         //unl+= wc[1][ic]*ql[1][iql];
         //unl+= wc[2][ic]*ql[2][iql];
         unl=  wn[0]*sql[ADDR(0,iql,nql)];
         unl+= wn[1]*sql[ADDR(1,iql,nql)];
         unl+= wn[2]*sql[ADDR(2,iql,nql)];

         //ll1= unl-wxdc[0][ic];
         ll1= unl-swxdc[ADDR(0,ic,nfc)];
         ll3= ll1+ al;
         ll4= ll1- al;

         fl[0]= ll1*rl;
         //fl[1]= fl[0]*ql[0][iql]+ wc[0][ic]*pl;
         //fl[2]= fl[0]*ql[1][iql]+ wc[1][ic]*pl;
         //fl[3]= fl[0]*ql[2][iql]+ wc[2][ic]*pl;
         //fl[4]= fl[0]*hl+ wxdc[0][ic]*pl;
         fl[1]= fl[0]*sql[ADDR(0,iql,nql)]+ wn[0]*pl;
         fl[2]= fl[0]*sql[ADDR(1,iql,nql)]+ wn[1]*pl;
         fl[3]= fl[0]*sql[ADDR(2,iql,nql)]+ wn[2]*pl;
         fl[4]= fl[0]*hl+ swxdc[ADDR(0,ic,nfc)]*pl;
         //for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*ql[ia][iql]; }
         for( ia=5;ia<nv;ia++ ){ fl[ia]= fl[0]*sql[ADDR(ia,iql,nql)]; }

// fluxes from the right
         //pr= qr[4][iqr];
         //rr= auxr[0][iqr];
         //ar= auxr[2][iqr];
         //hr= auxr[3][iqr];
         pr= sqr[ADDR(4,iqr,nqr)];
         rr= sauxr[ADDR(0,iqr,nqr)];
         ar= sauxr[ADDR(2,iqr,nqr)];
         hr= sauxr[ADDR(3,iqr,nqr)];

         //unr=  wc[0][ic]*qr[0][iqr];
         //unr+= wc[1][ic]*qr[1][iqr];
         //unr+= wc[2][ic]*qr[2][iqr];
         unr=  wn[0]*sqr[ADDR(0,iqr,nqr)];
         unr+= wn[1]*sqr[ADDR(1,iqr,nqr)];
         unr+= wn[2]*sqr[ADDR(2,iqr,nqr)];

         //lr1= unr-wxdc[0][ic];
         lr1= unr-swxdc[ADDR(0,ic,nfc)];
         lr3= lr1+ ar;
         lr4= lr1- ar;

         fr[0]= lr1 *rr;
         //fr[1]= fr[0]*qr[0][iqr]+ wc[0][ic]*pr;
         //fr[2]= fr[0]*qr[1][iqr]+ wc[1][ic]*pr;
         //fr[3]= fr[0]*qr[2][iqr]+ wc[2][ic]*pr;
         //fr[4]= fr[0]*hr+ wxdc[0][ic]*pr;
         fr[1]= fr[0]*sqr[ADDR(0,iqr,nqr)]+ wn[0]*pr;
         fr[2]= fr[0]*sqr[ADDR(1,iqr,nqr)]+ wn[1]*pr;
         fr[3]= fr[0]*sqr[ADDR(2,iqr,nqr)]+ wn[2]*pr;
         fr[4]= fr[0]*hr+ swxdc[ADDR(0,ic,nfc)]*pr;
         //for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*qr[ia][iqr]; }
         for( ia=5;ia<nv;ia++ ){ fr[ia]= fr[0]*sqr[ADDR(ia,iqr,nqr)]; }

// Roe averages
         ra= rr/rl;
         ra= sqrt(ra);
         wl= 1.+ra;
         wl= 1./wl;
         wr= 1.-wl;

         ra*= rl;

         //qa[0]= wl*ql[0][iql]+ wr*qr[0][iqr];
         //qa[1]= wl*ql[1][iql]+ wr*qr[1][iqr];
         //qa[2]= wl*ql[2][iql]+ wr*qr[2][iqr];
         //qa[3]= wl*ql[3][iql]+ wr*qr[3][iqr];
         //qa[4]= wl*ql[4][iql]+ wr*qr[4][iqr];
         qa[0]= wl*sql[ADDR(0,iql,nql)]+ wr*sqr[ADDR(0,iqr,nqr)];
         qa[1]= wl*sql[ADDR(1,iql,nql)]+ wr*sqr[ADDR(1,iqr,nqr)];
         qa[2]= wl*sql[ADDR(2,iql,nql)]+ wr*sqr[ADDR(2,iqr,nqr)];
         qa[3]= wl*sql[ADDR(3,iql,nql)]+ wr*sqr[ADDR(3,iqr,nqr)];
         qa[4]= wl*sql[ADDR(4,iql,nql)]+ wr*sqr[ADDR(4,iqr,nqr)];
         for( ia=5;ia<nv;ia++ )
        {
            //qa[ia]= wl*ql[ia][iql]+ wr*qr[ia][iqr];
            qa[ia]= wl*sql[ADDR(ia,iql,nql)]+ wr*sqr[ADDR(ia,iqr,nqr)];
        }

         //una=  qa[0]* wc[0][ic];
         //una+= qa[1]* wc[1][ic];
         //una+= qa[2]* wc[2][ic];
         una=  qa[0]* wn[0];
         una+= qa[1]* wn[1];
         una+= qa[2]* wn[2];

         ka=  qa[0]*qa[0];
         ka+= qa[1]*qa[1];
         ka+= qa[2]*qa[2];
         ka*= 0.5; 

         ha= wl*hl+ wr*hr;
         a2a= (gam-1.)*( ha- ka );
         aa= sqrt( a2a );
         raa=ra*aa;

// eigenvalues with Harten's fix

         //la1= una-wxdc[0][ic];
         la1= una-swxdc[ADDR(0,ic,nfc)];
         la3= la1+ aa;
         la4= la1- aa;

         lmax= fabs(la1)+ aa;
         lmax= fmax( lmax, fabs(ll1)+ al );
         lmax= fmax( lmax, fabs(lr1)+ ar );

         le3= fmax( fmax( eps, la3-ll3), lr3-la3 ); 
         le4= fmax( fmax( eps, la4-ll4), lr4-la4 ); 

         la1= fabs(la1);
         la3= fabs(la3);
         la4= fabs(la4);
         if(la3 < le3 ){ la3= 0.5*( la3*la3/le3+ le3 ); }
         if(la4 < le4 ){ la4= 0.5*( la4*la4/le4+ le4 ); }

// store Roe averages

         //auxc[0][ic]= wl;
         //auxc[1][ic]= wr;
         //auxc[2][ic]= ra;
         //auxc[3][ic]= la1;
         //auxc[4][ic]= la3;
         //auxc[5][ic]= la4;
         //auxc[nauxf-1][ic]= lmax;
         sauxc[ADDR(0,ic,nfc)]= wl;
         sauxc[ADDR(1,ic,nfc)]= wr;
         sauxc[ADDR(2,ic,nfc)]= ra;
         sauxc[ADDR(3,ic,nfc)]= la1;
         sauxc[ADDR(4,ic,nfc)]= la3;
         sauxc[ADDR(5,ic,nfc)]= la4;
         sauxc[ADDR(nauxf-1,ic,nfc)]= lmax;

// Left eigenvectors

         dr= rr- rl;
         //dq[0]= qr[0][iqr]- ql[0][iql];
         //dq[1]= qr[1][iqr]- ql[1][iql];
         //dq[2]= qr[2][iqr]- ql[2][iql];
         //dq[3]= qr[3][iqr]- ql[3][iql];
         //dq[4]= qr[4][iqr]- ql[4][iql];
         dq[0]= sqr[ADDR(0,iqr,nqr)]- sql[ADDR(0,iql,nql)];
         dq[1]= sqr[ADDR(1,iqr,nqr)]- sql[ADDR(1,iql,nql)];
         dq[2]= sqr[ADDR(2,iqr,nqr)]- sql[ADDR(2,iql,nql)];
         dq[3]= sqr[ADDR(3,iqr,nqr)]- sql[ADDR(3,iql,nql)];
         dq[4]= sqr[ADDR(4,iqr,nqr)]- sql[ADDR(4,iql,nql)];
         for( ia=5;ia<nv;ia++ )
        {
            //dq[ia]= qr[ia][iqr]- ql[ia][iql];
            dq[ia]= sqr[ADDR(ia,iqr,nqr)]- sql[ADDR(ia,iql,nql)];
        }
         dun= unr- unl;

         la3*=  0.5*ra/aa;
         la4*= -0.5*ra/aa;

         dp= pr-pl;
         dpa=dp/raa;
         dw1= la1*(dr-dp/a2a);
         dw3= la3*(dun+dpa);
         dw4= la4*(dun-dpa);

         //dw2[0]= dq[0]- dun*wc[0][ic]; dw2[0]*= ra*la1;
         //dw2[1]= dq[1]- dun*wc[1][ic]; dw2[1]*= ra*la1;
         //dw2[2]= dq[2]- dun*wc[2][ic]; dw2[2]*= ra*la1;
         dw2[0]= dq[0]- dun*wn[0]; dw2[0]*= ra*la1;
         dw2[1]= dq[1]- dun*wn[1]; dw2[1]*= ra*la1;
         dw2[2]= dq[2]- dun*wn[2]; dw2[2]*= ra*la1;

         dw2a=  dw2[0]*qa[0];
         dw2a+= dw2[1]*qa[1];
         dw2a+= dw2[2]*qa[2];

         //ana[0]= aa*wc[0][ic];
         //ana[1]= aa*wc[1][ic];
         //ana[2]= aa*wc[2][ic];
         ana[0]= aa*wn[0];
         ana[1]= aa*wn[1];
         ana[2]= aa*wn[2];

         for( ia=5;ia<nv;ia++ )
        {
            dw5[ia]= dq[ia]*ra*la1;
        }
 
         unaa=aa*una;

// Roe fluxes
         fa[0]=   dw1+               dw3+                  dw4;
         fa[1]=   dw1*qa[0]+ dw2[0]+ dw3*( qa[0]+ ana[0])+ dw4*( qa[0]- ana[0] );
         fa[2]=   dw1*qa[1]+ dw2[1]+ dw3*( qa[1]+ ana[1])+ dw4*( qa[1]- ana[1] );
         fa[3]=   dw1*qa[2]+ dw2[2]+ dw3*( qa[2]+ ana[2])+ dw4*( qa[2]- ana[2] );
         fa[4]=   dw1*ka+    dw2a+   dw3*( ha+ unaa)+      dw4*( ha- unaa );
         for( ia=5;ia<nv;ia++){ fa[ia]=   fa[0]*qa[ia]+                                  dw5[ia]; }

// assemble 
         //f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wc[3][ic];
         //f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wc[3][ic];
         //f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wc[3][ic];
         //f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wc[3][ic];
         //f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wc[3][ic];
         f[0]= 0.5*( fr[0]+ fl[0]- fa[0] )*wn[3];
         f[1]= 0.5*( fr[1]+ fl[1]- fa[1] )*wn[3];
         f[2]= 0.5*( fr[2]+ fl[2]- fa[2] )*wn[3];
         f[3]= 0.5*( fr[3]+ fl[3]- fa[3] )*wn[3];
         f[4]= 0.5*( fr[4]+ fl[4]- fa[4] )*wn[3];

         //rhsl[0][iql]-= f[0];
         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhsl[ADDR_(0,iql,nql)]-= f[0];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nql)]-= f[4];

         //rhsr[0][iqr]+= f[0];
         //rhsr[1][iqr]+= f[1];
         //rhsr[2][iqr]+= f[2];
         //rhsr[3][iqr]+= f[3];
         //rhsr[4][iqr]+= f[4];
         #pragma acc atomic
         srhsr[ADDR_(0,iqr,nqr)]+= f[0];
         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nqr)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wc[3][ic];
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            f[ia]= 0.5*( fr[ia]+ fl[ia]- fa[ia] )*wn[3];
            #pragma acc atomic
            srhsl[ADDR_(ia,iql,nql)]-= f[ia];
            #pragma acc atomic
            srhsr[ADDR_(ia,iqr,nqr)]+= f[ia];
        }
         sauxc[ADDR(nauxf-1,ic,nfc)]*= wn[3];
     }
  }
