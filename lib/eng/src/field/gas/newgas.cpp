   using namespace std;

#  include <field/gas.h>

   cGas *newgas( Int ityp, cCosystem *coo, cVisc *visc )
  {
      cGas *val=NULL;
      
      switch( ityp )
     {
         case( mfroe_gas ):
        {
             val= new cMfRoeGas( coo,visc );
             break;
        }
         case( mfjanaf_gas ):
        {
             val= new cMfJanafGas( coo,visc );
             break;
        }
         case( mfreacting_gas ):
        {
             val= new cMfReactingGas( coo,visc );
             break;
        }
         case( mfausmup_gas ):
        {
             val= new cMfAusmUpGas( coo,visc );
             break;
        }
         case( heat_conduction_gas ):
        {
             val= new cHeatConduction( coo,visc );
             break;
        }
         case( mfroe_gas_cht ):
        {
             val= new cMfRoeGasCHT( coo,visc );
             break;
        }
//         case( mfausm_gas ):
//        {
//             val= new cMfAusmGas( coo,visc );
//             break;
//        }
//         case( mfausmup_gas ):
//        {
//             val= new cMfAusmUPGas( coo,visc );
//             break;
//        }
//         case( mfroeisoth_gas ):
//        {
//             val= new cMfRoeGasIsoth( coo,visc );
//             break;
//        }
//         case( roe_gas ):
//        {
//             val= new cRoeGas( coo,visc );
//             break;
//        }
//         case( acm_gas ):
//        {
//             val= new cACMGas( coo,visc );
//             break;
//        }
         
     }
      return val;
  }
