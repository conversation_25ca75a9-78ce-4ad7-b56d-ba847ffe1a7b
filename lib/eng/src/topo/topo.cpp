   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Mon Oct 18 13:55:16 BST 2010
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         abstract symplex representation

#  include <topo/topo.h>

   void tpgraph( Int iss, Int ise, Int nsp, Int *isp[], Int *lgp[], Int **igp, Int *isg[] )
  {
      Int            is,jp,ip,kp,hp;
      Int            ip0,jp0,ip1,jp1;
      Int            dsize=5*nsp;
 
      for( is=iss;is<ise;is++ )
     {
         for( jp0=0;jp0<nsp;jp0++ )
        {
            ip= isp[jp0][is];
            for( jp1=0;jp1<nsp;jp1++ )
           {
               jp= isp[jp1][is];
               kp= -1;
               for( hp=0;hp<lgp[0][ip];hp++ )
              {
                  if( igp[ip][hp]== jp )
                 { 
                     kp= hp; 
                     break; 
                 }
              }
               if( kp == -1 ) 
              {
                  if( lgp[0][ip] == lgp[2][ip] )
                 {
                     realloc( lgp[2]+ip, dsize, igp+ip );
                 }
                  kp= lgp[0][ip];
                  igp[ip][kp]= jp;
                  lgp[0][ip]++; 
                  lgp[1][ip]++; 
              }
               isg[jp0+nsp*jp1][is]=kp;             
           }
        } 
     }
  }

   void tpmark( Int iss, Int ise, Int nsp, Int *isp[], Int *imrkp, Int ikey, Int *imrks )
  {
      Int            is,jp,ip;

      for( jp=0;jp<nsp;jp++ )
     {
         for( is=iss;is<ise;is++ )
        {
            ip= isp[jp][is];    
            if( imrkp[ip] == ikey )
           {
               imrks[is]= 1;
           } 
        }
     }
  }

   void marktp( Int iss, Int ise, Int nsp, Int *isp[], Int *imrks, Int ikey, Int *imrkp )
  {
      Int            is,jp,ip;

      for( jp=0;jp<nsp;jp++ )
     {
         for( is=iss;is<ise;is++ )
        {
            ip= isp[jp][is];    
            if( imrks[is] == ikey )
           {
               imrkp[ip]= 1;
           } 
        }
     }
  }

   void tpperm( Int n, Int *data )
  {
      Int m;
      Int imin,jmin;
      Int i;
//    if( n > 2 )
     {
         imin=(Int)big;
         jmin=-1;
         for( i=0;i<n;i++ )
        {
            if( data[i] < imin ) 
           {
               jmin= i;
               imin= data[i];
           }
        }
// need right circular shift
         if( jmin != 0 )
        {
            m= n-jmin;
            shftc( n,data, m );
        }
     }
  }
