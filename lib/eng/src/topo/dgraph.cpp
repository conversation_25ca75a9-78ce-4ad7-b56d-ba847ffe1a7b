   using namespace std;

//3456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 123456789 
//       1         2         3         4         5         6         7         8         9         0         1         2

// Author          <PERSON> <<EMAIL>>
// Created         Thu Jun  2 12:35:02 BST 2011
// Changes History -
// Next Change(s)  ( work in progress )
// Purpose         directed graph

#  include <topo/dgraph.h>

   void      triangles( Int *npc, Int *nce, Int ***ice )
  {
     *npc= 2;
     *nce= 3;
      ice[0]= new Int*[2];
      ice[0][0]= new Int[3];
      ice[0][1]= new Int[3];
      ice[0][0][0]= 0; ice[0][1][0]= 1;
      ice[0][0][1]= 1; ice[0][1][1]= 2;
      ice[0][0][2]= 2; ice[0][1][2]= 0;
  }

   void quadrilaterals( Int *npc, Int *nce, Int ***ice )
  {
     *npc= 2;
     *nce=4;
      ice[0]= new Int*[2];
      ice[0][0]= new Int[4];
      ice[0][1]= new Int[4];
      ice[0][0][0]= 0; ice[0][1][0]= 1;
      ice[0][0][1]= 1; ice[0][1][1]= 2;
      ice[0][0][2]= 2; ice[0][1][2]= 3;
      ice[0][0][3]= 3; ice[0][1][3]= 0;
  }




   cDgraph::cDgraph()
  {
      np= 0;
      npc= 0;

      lpc= NULL;
      mpc= NULL;
      ipc= NULL;

      nk= 0;
      ne= NULL;
      nce= NULL;
      ice= NULL;

      iec= NULL;
      hec= NULL;
  }

   cDgraph::cDgraph( Int n, Int m, Int nek, Int *nne, Int *nnce, Int ***nice ) 
  {
      Int   ik,jc,jp;
      np=  n;
      npc= m;
      lpc= new Int[np];
      mpc= new Int[np];
      ipc= new Int**[np];
      setv( (Int)0,np, (Int)0, lpc );
      setv( (Int)0,np, (Int)0, mpc );
      setv( (Int)0,np, (Int**)NULL, ipc );

      nk= nek;
      ne= new Int[nk];
      nce= new Int[nk];
      ice= new Int**[nk];
      iec= new Int**[nk];
      hec= new Int**[nk];
      for( ik=0;ik<nk;ik++ )
     {
         ne[ik]= nne[ik];
         nce[ik]= nnce[ik];
         ice[ik]= new Int*[npc];
         for( jp=0;jp<npc;jp++ )
        {
            ice[ik][jp]= new Int[nce[ik]];
            for( jc=0;jc<nce[ik];jc++ )
           {
               ice[ik][jp][jc]= nice[ik][jp][jc];
           } 
        }
         iec[ik]= new Int*[nce[ik]];     
         hec[ik]= new Int*[nce[ik]];     
         for( jc=0;jc<nce[ik];jc++ )
        {
            iec[ik][jc]= new Int[ne[ik]]; setv( (Int)0,ne[ik], (Int)-1,iec[ik][jc] );
            hec[ik][jc]= new Int[ne[ik]]; setv( (Int)0,ne[ik], (Int)-1,hec[ik][jc] );   
        }
     }
      

  }

   cDgraph::~cDgraph()
  {
      Int ip,jc,jp,ik;
      for( ip=0;ip<np;ip++ )
     {
         for( jc=0;jc<lpc[ip];jc++ )
        {
            delete[] ipc[ip][jc]; ipc[ip][jc]= NULL;
        }
         delete[] ipc[ip];
     }
      delete[] lpc; lpc= NULL;
      delete[] mpc; mpc= NULL;
      delete[] ipc; ipc= NULL;
      for( ik=0;ik<nk;ik++ )
     {
         for( jp=0;jp<npc;jp++ )
        {
            delete[] ice[ik][jp]; ice[ik][jp]= NULL;
        }
         for( jc=0;jc<nce[ik];jc++ )
        {
            delete[] iec[ik][jc]; iec[ik][jc]= NULL;     
            delete[] hec[ik][jc]; hec[ik][jc]= NULL;     
        }
         delete[] ice[ik]; ice[ik]= NULL;
         delete[] iec[ik]; iec[ik]= NULL;     
         delete[] hec[ik]; hec[ik]= NULL;     
     }
      delete[] ice;
      delete[] iec;
      delete[] hec;
      delete[] ne;
      delete[] nce;
      np=  0;
      npc= 0;
  }

   void cDgraph::build( Int **iep[] )
  {

      Int          ik,ip,ie,jc,jp,kp,ic,id,kc;

      Int         *icp;
      Int          iprm;
      bool         ifnd;

      Int          dsize=10;
      Int          tmp;

// executable statments

      icp= new Int[npc];
   
// loop around all element kinds and build graph

      for( ik=0;ik<nk;ik++ )
     {
         for( ie=0;ie<ne[ik];ie++ )
        {
            for( jc=0;jc<nce[ik];jc++ )
           {
               for( kp=0;kp<npc;kp++ )
              {
                  jp= ice[ik][kp][jc];
                  icp[kp]= iep[ik][jp][ie];
              }
   
               iprm= 1;
               psort( npc, icp, &iprm );
               ip=icp[0];
   
               ic= -1;
               for( kc=0;kc<lpc[ip];kc++ )
              {
                  ifnd=true;
                  for( jp=0;jp<npc;jp++ )
                 {   
                     ifnd = ifnd && ( ipc[ip][kc][jp] == icp[jp] ); 
                     if( !ifnd )
                    {
                        break;
                    }
                 }
                  if( ifnd )
                 { 
                     ic= kc;
                     break;
                 }
              }
               if( ic == -1 )
              {
                  ic= lpc[ip];
                  if( lpc[ip] == mpc[ip] )
                 {
                     tmp= mpc[ip]; realloc( &tmp,dsize, ipc+ip ); ipc[ip][ic]= NULL;
                     mpc[ip]= tmp;
                 }
                  ipc[ip][ic]= new Int[npc];
                  for( jp=0;jp<npc;jp++ )
                 {
                     ipc[ip][ic][jp]= icp[jp];
                 }
                  lpc[ip]++;
              }
               iec[ik][jc][ie]= ip;
               hec[ik][jc][ie]= ic;
           }
        }
     }
      delete[] icp; icp=NULL;
   
  }

   void cDgraph::pack( Int *nc, Int **jec[] )
  {
      Int          ik,ip,ie,jc,jp,kp,ic,id,kc,hc;
      Int          iprm;
      Int        **idx;

      idx= new Int*[np];
      for( ip=0;ip<np;ip++ )
     {
         idx[ip]= new Int[lpc[ip]];
         setv( (Int)0,lpc[ip], (Int)-1, idx[ip] );
     }
      ic=0;
      for( ip=0;ip<np;ip++ )
     {
         for( jc=0;jc<lpc[ip];jc++ )
        {
            idx[ip][jc]= ic++;
        }
     }
     *nc= ic;
      for( ik=0;ik<nk;ik++ )
     {
         for( jc=0;jc<nce[ik];jc++ )
        {
            for( ie=0;ie<ne[ik];ie++ )
           {
               ip=   iec[ik][jc][ie];
               ic=   hec[ik][jc][ie];
               jec[ik][jc][ie]= idx[ip][ic];
           }
        }
     }

      for( ip=0;ip<np;ip++ )
     {
         delete[] idx[ip]; idx[ip]= NULL;
     }
      delete[] idx; idx=NULL;
  }

   void cDgraph::destroy( Int ***nice )
  {
      Int ik,jc;
    
      for( ik=0;ik<nk;ik++ )
     {
         for( jc=0;jc<npc;jc++ )
        {
            delete[] nice[ik][jc]; nice[ik][jc]=NULL;
        }
         delete[] nice[ik]; nice[ik]= NULL;
     }
  }
