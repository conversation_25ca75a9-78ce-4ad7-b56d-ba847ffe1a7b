   using namespace std;

#  include <topo/topo.h>

   void tpcpack( Int np, Int ncp, Int *lpc, Int ***ipc, Int ***jpc, Int ***hpc, Int ***kpc, 
                 Int *n, Int **sicp, Int **sjcp, Int **shcp, Int **skcp )
  {
      Int ip,jc,ic,jp,ie,ik,kc;
      Int    **icp;
      Int    *hcp[2];
      Int    *jcp[2];
      Int    *kcp[2];

      ic=0;
      for( ip=0;ip<np;ip++ )
     {
         for( jc=0;jc<lpc[ip];jc++ )
        {
            if( jpc[ip][jc][0] != -1 && jpc[ip][jc][1] != -1 )
           {
              ic++;
           }
        }
     }
    *n= ic;
    (*sicp)= new Int[ncp*(*n)]; 
    (*sjcp)= new Int[2*(*n)]; setv( (Int)0,(Int)((*n)*2), (Int)-1, (*sjcp) );
    (*skcp)= new Int[2*(*n)]; setv( (Int)0,(Int)((*n)*2), (Int)-1, (*skcp) );
    (*shcp)= new Int[2*(*n)]; setv( (Int)0,(Int)((*n)*2), (Int)-1, (*shcp) );

      icp= new Int*[ncp];

      subv( ncp,*n,*sicp,icp );
      subv( 2,*n,*sjcp,jcp );
      subv( 2,*n,*skcp,kcp );
      subv( 2,*n,*shcp,hcp );

      ic=0;
      for( ip=0;ip<np;ip++ )
     {
         for( jc=0;jc<lpc[ip];jc++ )
        {
            if( jpc[ip][jc][0] != -1 && jpc[ip][jc][1] != -1 )
           {
               for( jp=0;jp<ncp;jp++ )
              {
                  icp[jp][ic]= ipc[ip][jc][jp];
              }

               ie= jpc[ip][jc][0];
               jcp[0][ic]= ie;
               hcp[0][ic]= hpc[ip][jc][0];
               kcp[0][ic]= kpc[ip][jc][0];

               ie= jpc[ip][jc][1];
               jcp[1][ic]= ie;
               hcp[1][ic]= hpc[ip][jc][1];
               kcp[1][ic]= kpc[ip][jc][1];

               ic++;
           }
        }
     }
      delete[] icp;
  }
