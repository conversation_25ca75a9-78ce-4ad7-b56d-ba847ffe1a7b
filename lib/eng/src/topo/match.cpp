
   using namespace std;

#  include <topo/topo.h>

   void tpmatch( Int  nb, Int nbp,  Int  *ibp[], Int *ibe, Int *jbe, Int *kbe, 
                         Int *nep,  Int **iep[], Int *neb, Int **ieb[], Int *lpe, Int **ipe, Int **jpe, Int **kpe )
  {

      Int ib,jp,ip,ie,ik,je,jb,kp;
      Int *jbp;
      Int *kbp;

      bool flag,flag0,flag1;

      jbp= new Int[nbp];
      kbp= new Int[nbp];
      for( ib=0;ib<nb;ib++ )
     {

         line( ib,nbp,ibp, jbp );
         ip= jbp[0];

         flag= false;
         for( je=0;je<lpe[ip];je++ )
        {
            ik= kpe[ip][je];
            ie= ipe[ip][je];
            for( jb=0;jb<neb[ik];jb++ )
           {
               for( kp=0;kp<nbp;kp++ )
              {
                  jp= ieb[ik][kp][jb];
                  jp= iep[ik][jp][ie];
                  kbp[kp]= jp;
              }
               kp=0;

               do
              {

                  flag0=true;
                  flag1=true;
                  for( jp=0;jp<nbp;jp++ )
                 {
                     flag0= flag0 && ( kbp[jp] == jbp[jp] );
                     flag1= flag1 && ( kbp[jp] == jbp[nbp-jp-1] );
                 } 
                  flag= flag0 || flag1; 
                  if( flag )
                 {
                     break;
                 }
                  shftc( nbp,kbp );
                  kp++;
              }while( kp < nbp );

               if( flag )
              {

/*                cout << "face "<<ib<<": points ";
                  for( jp=0;jp<nbp;jp++ )
                 {
                     cout << jbp[jp]<<" ";
                 }
                  cout << " matches element "<<ie<<" "<<ik<<" "<<jb<<": points ";
                  for( kp=0;kp<nbp;kp++ )
                 {
                     jp= ieb[ik][kp][jb];
                     jp= iep[ik][jp][ie];
                     cout << jp <<" ";
                 }
                  if( flag1 )
                 {
                     cout << "warning: orientation appears reversed, element has "<<nep[ik]<<" points ";
                 }
                  cout << "\n";*/

                  for( kp=0;kp<nbp;kp++ ) 
                 {
                     jp= ieb[ik][kp][jb];
                     jp= iep[ik][jp][ie];
                     ibp[kp][ib]= jp;
                 }
                  ibe[ib]= ie;
                  jbe[ib]= jb;
                  kbe[ib]= ik;
                  break;
              } 
           }
            if( flag )
           {
               break;
           }
        }
         if( !flag )
        {
            cout << "error: could not find a match for face "<<ib<<"\n";
            for( jp=0;jp<nbp;jp++ )
           {
               cout << ibp[jp][ib]<<" ";
           }
            cout << "\n";
            exit(0);
        }
     }
      delete[] jbp;
      delete[] kbp;
  }

