   using namespace std;

#  include <topo/topo.h>

   void tpgrcmk( Int n, Int *lg[], Int **ig, Int *iprm, Int *indx, Int *iwrk )
  {
      Int ist,ien;
      Int j,i,k,h;

      hsort( n,lg[1],indx );

      setv( (Int)0,n,(Int)-99999,iprm );
      setv( (Int)0,n,(Int)-1,iwrk );

      ien=0;
 
      do 
     {

         ist= ien;
         for( j=0;j<n;j++ )
        {
            k= indx[j];
            if( iwrk[k] == -1 )
           {
               i=k;
               break;
           }
        }

         iprm[ist]= i;
         iwrk[i]= 0;
         ien= ist+1;

         while( ien > ist )
        {

            for( j=ist;j<ien;j++ )
           {
               i=iprm[j]; 
               for( k=0;k<lg[0][i];k++ )
              {
                  h= ig[i][k];
                  if( iwrk[h] == -1 )
                 {
                     iwrk[h]=-2;
                 }
              }
           }

            ist= ien;
            for( j=0;j<n;j++ )
           {
               i=indx[j];
               if( iwrk[i] == -2 )
              {
                  iprm[ien]= i;
                  iwrk[i]=0;
                  ien++;
              }
           }
        }
     }while( ien < n );

      reverse( n,iprm );
      for( j=0;j<n;j++ )
     {
         i= iprm[j];
         indx[i]= j;
     }
  }

   void tpermute( Int n, Int *lg, Int **ig, Int *iprm, Int *indx )
  {
      Int j,i,k;

      for( i=0;i<n;i++ )
     {
         for( j=0;j<lg[i];j++ )
        {
            k= ig[i][j];
            ig[i][j]= indx[k];
        }
     }
     
      permute( n,lg,iprm );
      permute( n,ig,iprm );
  }

   void tprint( string fnme, Int n, Int *lg, Int **ig )
  {
      Int i,j,k;
      ofstream fle;
      char* line;
      line= new char[n];
      fle.open( fnme.c_str() );
      for( i=0;i<n;i++ )
     {
         setv( 0,n, ' ',line );
         for( j=0;j<lg[i];j++ )
        {
            k= ig[i][j];
            line[k]='x';
        } 
         for( j=0;j<n;j++ )
        {
            fle << line[j]<<",";
        }
         fle << "\n";
     }
      fle.close();
      delete[] line;
  }


   Int tpbwidth( Int n, Int *lg, Int **ig )
  {
      Int i,j,k,d;
      Int val=-1;
      for( i=0;i<n;i++ )
     {
         for( j=0;j<lg[i];j++ )
        {
            k=ig[i][j];
            d=k-i;
            d=labs(d);
            val= max( d,val );
        }
     }
      return val;
  }
