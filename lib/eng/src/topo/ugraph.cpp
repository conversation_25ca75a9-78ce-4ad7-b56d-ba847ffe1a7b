   using namespace std;

#  include <topo/ugraph.h>

   cUgraph::cUgraph()
  {
      n=0;
      lpe=NULL;
      mpe=NULL;
      ipe=NULL;
      jpe=NULL;
      kpe=NULL;
  }

   cUgraph::cUgraph( Int m )
  {
      n=m;
      lpe=new Int[n];
      mpe=new Int[n];
      ipe=new Int*[n];
      jpe=new Int*[n];
      kpe=new Int*[n];
      setv( (Int)0,n, (Int)0, lpe );
      setv( (Int)0,n, (Int)0, mpe );
      setv( (Int)0,n, (Int*)NULL, ipe );
      setv( (Int)0,n, (Int*)NULL, jpe );
      setv( (Int)0,n, (Int*)NULL, kpe );
  }

   cUgraph::~cUgraph()
  {
      Int i;
      for( i=0;i<n;i++ )
     {
         delete[] ipe[i]; ipe[i]= NULL;
         delete[] jpe[i]; jpe[i]= NULL;
         delete[] kpe[i]; kpe[i]= NULL;
     }
      delete[] lpe; lpe=NULL;
      delete[] mpe; mpe=NULL;
      delete[] ipe; ipe=NULL;
      delete[] jpe; jpe=NULL;
      delete[] kpe; kpe=NULL;
      n=0;
  }

   void cUgraph::build( Int ne, Int nep, Int *iep[], Int iflag )
  {
      Int iek,ie,ip,jp,n;
      
      Int    dsize=10;
      Int    tmp;

      for( jp=0;jp<nep;jp++ )
     {
         for( ie=0;ie<ne;ie++ )
        {
            ip= iep[jp][ie];    
            if( lpe[ip] == mpe[ip] )
           {
               tmp= mpe[ip];  realloc( &tmp,dsize, ipe+ip );
               tmp= mpe[ip];  realloc( &tmp,dsize, jpe+ip );
               tmp= mpe[ip];  realloc( &tmp,dsize, kpe+ip );
               mpe[ip]= tmp;
           }
            n= lpe[ip];
            ipe[ip][n]= ie;
            jpe[ip][n]= jp;
            kpe[ip][n]= iflag;
            lpe[ip]++;
        }
     }
  }

   void cUgraph::match( Int  nb, Int nbp,  Int  *ibp[], Int *ibe[], Int *nep,  Int **iep[], Int *neb, Int **ieb[] )
  {

      Int ib,jp,ip,ie,ik,je,jb,kp;
      Int *jbp;
      Int *kbp;

      bool flag,flag0,flag1;

      jbp= new Int[nbp];
      kbp= new Int[nbp];
      for( ib=0;ib<nb;ib++ )
     {

         line( ib,nbp,ibp, jbp );
         ip= jbp[0];

         flag= false;
         for( je=0;je<lpe[ip];je++ )
        {
            ik= kpe[ip][je];
            ie= ipe[ip][je];
            for( jb=0;jb<neb[ik];jb++ )
           {
               for( kp=0;kp<nbp;kp++ )
              {
                  jp= ieb[ik][kp][jb];
                  jp= iep[ik][jp][ie];
                  kbp[kp]= jp;
              }
               kp=0;

               do
              {

                  flag0=true;
                  flag1=true;
                  for( jp=0;jp<nbp;jp++ )
                 {
                     flag0= flag0 && ( kbp[jp] == jbp[jp] );
                     flag1= flag1 && ( kbp[jp] == jbp[nbp-jp-1] );
                 } 
                  flag= flag0 || flag1; 
                  if( flag )
                 {
                     break;
                 }
                  shftc( nbp,kbp );
                  kp++;
              }while( kp < nbp );

               if( flag )
              {
                  for( kp=0;kp<nbp;kp++ ) 
                 {
                     jp= ieb[ik][kp][jb];
                     jp= iep[ik][jp][ie];
                     ibp[nbp-kp-1][ib]= jp;
                 }
                  ibe[0][ib]= ie;
                  ibe[1][ib]= jb;
                  ibe[2][ib]= ik;
                  break;
              } 
           }
            if( flag )
           {
               break;
           }
        }
         if( !flag )
        {
            cout << "error: could not find a match for face "<<ib<<"\n";
            for( jp=0;jp<nbp;jp++ )
           {
               cout << ibp[jp][ib]<<" ";
           }
            cout << "\n";
            exit(0);
        }
     }
      delete[] jbp;
      delete[] kbp;
  }

   void cUgraph::match( Int  nb, Int nbp,  Int  *ibp[], Int *ibe[], Int *nep,  Int **iep[], Int *neb, Int **ieb[], Int *medium_marker[] )
  {

      Int ib,jp,ip,ie,ik,je,jb,kp;
      Int *jbp;
      Int *kbp;

      bool flag,flag0,flag1;

      jbp= new Int[nbp];
      kbp= new Int[nbp];
      for( ib=0;ib<nb;ib++ )
     {

         line( ib,nbp,ibp, jbp );
         ip= jbp[0];

         flag= false;
         for( je=0;je<lpe[ip];je++ )
        {
            ik= kpe[ip][je];
            ie= ipe[ip][je];

            if(medium_marker[ik][ie]!=1) continue; //force to match the fluid domain

            for( jb=0;jb<neb[ik];jb++ )
           {
               for( kp=0;kp<nbp;kp++ )
              {
                  jp= ieb[ik][kp][jb];
                  jp= iep[ik][jp][ie];
                  kbp[kp]= jp;
              }
               kp=0;

               do
              {

                  flag0=true;
                  flag1=true;
                  for( jp=0;jp<nbp;jp++ )
                 {
                     flag0= flag0 && ( kbp[jp] == jbp[jp] );
                     flag1= flag1 && ( kbp[jp] == jbp[nbp-jp-1] );
                 } 
                  flag= flag0 || flag1; 
                  if( flag ) //make sure the bondary is 
                 {
                     break;
                 }
                  shftc( nbp,kbp );
                  kp++;
              }while( kp < nbp );

               if( flag )
              {
                  for( kp=0;kp<nbp;kp++ ) 
                 {
                     jp= ieb[ik][kp][jb];
                     jp= iep[ik][jp][ie];
                     ibp[nbp-kp-1][ib]= jp;
                 }
                  ibe[0][ib]= ie;
                  ibe[1][ib]= jb;
                  ibe[2][ib]= ik;
                  break;
              } 
           }
            if( flag )
           {
               break;
           }
        }
         if( !flag )
        {
            cout << "error: could not find a match for face "<<ib<<"\n";
            for( jp=0;jp<nbp;jp++ )
           {
               cout << ibp[jp][ib]<<" ";
           }
            cout << "\n";
            exit(0);
        }
     }
      delete[] jbp;
      delete[] kbp;
  }

