   using namespace std;

#  include <topo/topo.h>

   void tpconn( Int ncp, Int nek, Int *ne, Int *nep,  Int **iep[], Int *nec, Int **iec[], Int *lpc, Int *mpc, Int ***ipc, Int ***jpc, Int ***hpc, Int ***kpc )
  {

      Int ik,ie,jc,jp,ip,kp,kc,ic;
      Int *icp;
      Int *jcp;
      Int  tmp,dsize=10;
      bool flag;

      icp= new Int[ncp];
      jcp= new Int[ncp];

      for( ik=0;ik<nek;ik++ )
     {
         for( ie=0;ie<ne[ik];ie++ )
        {
            for( jc=0;jc<nec[ik];jc++ )
           {
               for( kp=0;kp<ncp;kp++ )
              {
                  jp= iec[ik][kp][jc];
                  icp[kp]= iep[ik][jp][ie];
              }
               reverse( ncp,icp );
               tpperm( ncp,icp );
               ip= icp[ncp-1];
               kc= -1;
               for( ic=0;ic<lpc[ip];ic++ )
              {
                  for( kp=0;kp<ncp;kp++ )
                 {
                     jcp[kp]= ipc[ip][ic][kp];    
                 }     
                  tpperm( ncp,jcp );
                  flag= true;
                  for( kp=0;kp<ncp;kp++ )
                 {
                     flag= flag && ( jcp[kp] == icp[kp] );
                     if( !flag )
                    {
                        break;
                    }
                 }
                  if( flag )
                 {
                     kc= ic;
                     break;
                 }
              }
               if( kc == -1 )
              {
                  if( lpc[ip] == mpc[ip] )
                 {
                     
                     tmp= mpc[ip]; realloc( &tmp,dsize, ipc+ip  ); setv( mpc[ip],(Int)tmp, (Int*)NULL, ipc[ip] );
                     tmp= mpc[ip]; realloc( &tmp,dsize, jpc+ip  ); setv( mpc[ip],(Int)tmp, (Int*)NULL, jpc[ip] );
                     tmp= mpc[ip]; realloc( &tmp,dsize, kpc+ip  ); setv( mpc[ip],(Int)tmp, (Int*)NULL, kpc[ip] );
                     tmp= mpc[ip]; realloc( &tmp,dsize, hpc+ip  ); setv( mpc[ip],(Int)tmp, (Int*)NULL, hpc[ip] );
                     mpc[ip]= tmp;
                 }
                  kc= lpc[ip];
                  ipc[ip][kc]= new Int[ncp]; setv( (Int)0,   ncp,(Int)-1,ipc[ip][kc] );
                  jpc[ip][kc]= new Int[2];   setv( (Int)0,(Int)2,(Int)-1,jpc[ip][kc] );
                  kpc[ip][kc]= new Int[2];   setv( (Int)0,(Int)2,(Int)-1,kpc[ip][kc] );
                  hpc[ip][kc]= new Int[2];   setv( (Int)0,(Int)2,(Int)-1,hpc[ip][kc] );
                  reverse( ncp,icp );
                  for( kp=0;kp<ncp;kp++ )
                 {
                     ipc[ip][kc][kp]= icp[kp];
                 }
                  jpc[ip][kc][0]= ie;
                  kpc[ip][kc][0]= ik;
                  hpc[ip][kc][0]= jc;
                  lpc[ip]++;
              }
               else
              {
                  if( jpc[ip][kc][1] != -1 || kpc[ip][kc][1] != -1 || hpc[ip][kc][1] != -1 )
                 {
                     cout << "something really wrong\n";
                     exit(1);
                 } 
                  jpc[ip][kc][1]= ie; 
                  kpc[ip][kc][1]= ik; 
                  hpc[ip][kc][1]= jc; 
              }
           }
        }
     }
      delete[] icp;
      delete[] jcp;
  }
