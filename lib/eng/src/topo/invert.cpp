   using namespace std;

#  include <topo/topo.h>

   void tpinvert( Int ne, Int nep, Int *iep[], Int *lpe, Int *mpe, Int **ipe, Int **jpe, Int **kpe, Int iflag )
  {
      Int iek,ie,ip,jp,n;
      
      Int    dsize=10;
      Int    tmp;

      for( jp=0;jp<nep;jp++ )
     {
         for( ie=0;ie<ne;ie++ )
        {
            ip= iep[jp][ie];    
            if( lpe[ip] == mpe[ip] )
           {
               tmp= mpe[ip];  realloc( &tmp,dsize, ipe+ip );
               tmp= mpe[ip];  realloc( &tmp,dsize, jpe+ip );
               tmp= mpe[ip];  realloc( &tmp,dsize, kpe+ip );
               mpe[ip]= tmp;
           }
            n= lpe[ip];
            ipe[ip][n]= ie;
            jpe[ip][n]= jp;
            kpe[ip][n]= iflag;
            lpe[ip]++;
        }
     }
  }
