
   using namespace std;

#  include <lookup/lookup.h>
#  include <iostream>

   void isop( Real *l, Real *y, Real *y1, Real *y2, Real *y3, Real *y4, lookup_t *stat )
  {
      Int it;
      Real l1,l2;
      Real x, t;
      Real x0, t0, b1, b2;
      Real x1, x2, x3, x4;
      Real t1, t2, t3, t4;
      Real g1, g2, g3, g4;
      Real g11, g21, g31, g41;
      Real g12, g22, g32, g42;
      Real a11, a12, a21, a22;
      Real det, dl1, dl2;
      Real rlx, lim;
      bool good=true;
      
      x= y[0];
      t= y[1];

      x1=y1[0]; x2=y2[0]; x3=y3[0]; x4=y4[0];
      t1=y1[1]; t2=y2[1]; t3=y3[1]; t4=y4[1];
       
      l1= 0.5;
      l2= 0.5;

      rlx= 0.5;
      for(it=0; it<20; it++)
     {
        g1= (1-l1)*(1-l2);
        g2=    l1 *(1-l2);
        g3=    l1 *   l2;
        g4= (1-l1)*   l2;
        g11=       -(1-l2);
        g21=        (1-l2);
        g31=           l2;
        g41=          -l2;
        g12=-(1-l1);
        g22=   -l1;
        g32=    l1;
        g42= (1-l1);
        x0= g1*x1+ g2*x2+ g3*x3+ g4*x4;
        t0= g1*t1+ g2*t2+ g3*t3+ g4*t4;
        b1= x-x0;
        b2= t-t0;
        a11= g11*x1+ g21*x2+ g31*x3+ g41*x4;
        a12= g12*x1+ g22*x2+ g32*x3+ g42*x4;
        a21= g11*t1+ g21*t2+ g31*t3+ g41*t4;
        a22= g12*t1+ g22*t2+ g32*t3+ g42*t4;
        det= a11*a22- a12*a21;
        lim= fabs(a11)+fabs(a22)+fabs(a21)+fabs(a12);
        if( fabs(det) > small*lim )
       {
           dl1= ( a22*b1- a12*b2 )/det;
           dl2= (-a21*b1+ a11*b2 )/det;
           l1= l1+ rlx*dl1;
           l2= l2+ rlx*dl2;
           rlx= min( 0.999,1.2*rlx );
       }
        else
       {
           good=false;
           break;
       }
     }

      if( good )
     {
         good= good && ( abs(dl1) < 1.e-9 ) && ( abs(dl2) < 1.e-9 );
         if( good )
        { 
            l[0]= l1;
            l[1]= l2;
        }
     }

      stat->good=good;
      stat->s0=l1;
      stat->s1=l2;
      stat->inside= ( l1 <= 1. && l1 >= 0. ) && ( l2 <= 1. && l2 >= 0. );
  }

   cLookup::cLookup()
  {
      x= NULL;
      v= NULL;
      n1= 0;
      n2= 0;
      nv= 0;
  }

   cLookup::~cLookup()
  {
      del3(  2,n1,n2, &x );
      del3( nv,n1,n2, &v );

      n1= 0;
      n2= 0;
      nv= 0;
  }

   void cLookup::interp( Real *s, Real *q, Real *dq0, Real *dq1 )
  {
      Int   i,j,k;
      Int   i0,j0;
      Int   imin,jmin;
      Real  d,dmin,d0,d1,d2,d3;
      Real  s0[2],smin[2];
      Real  l1,l2;
      Real  g1, g2, g3, g4;
      Real  g11,g21,g31,g41;
      Real  g12,g22,g32,g42;
     
      lookup_t stat;
      dmin= big;

      for( j=0;j<n2-1;j++ )
     {
         for( i=0;i<n1-1;i++ )
        {
            isop( s0, s, x[j][i],x[j][i+1],x[j+1][i+1],x[j+1][i], &stat );
            if( stat.good )
           {
               if( stat.inside )
              {
                  imin= i;
                  jmin= j;
                  smin[0]= s0[0];
                  smin[1]= s0[1];
                  dmin= 0;
                  break;
              }
               d= abs(s0[0])+abs(s0[1]);
               if( d < dmin )
              {
                  imin= i;
                  jmin= j;
                  smin[0]= s0[0];
                  smin[1]= s0[1];
                  dmin= d;
              }
           }
        }
     }

      l1= smin[0];
      l2= smin[1];

      l1= max( ZERO,min((Real)1.,l1) );
      l2= max( ZERO,min((Real)1.,l2) );

      g1= (1-l1)*(1-l2);
      g2=    l1 *(1-l2);
      g3=    l1 *   l2;
      g4= (1-l1)*   l2;
      g11=       -(1-l2);
      g21=        (1-l2);
      g31=           l2;
      g41=          -l2;
      g12=-(1-l1);
      g22=   -l1;
      g32=    l1;
      g42= (1-l1);

      i=imin;
      j=jmin;

      for( k=0;k<nv;k++ )
     {
         q[k]= g1*v[j][i][k]+ g2*v[j][i+1][k]+ g3*v[j+1][i+1][k]+ g4*v[j+1][i][k];
     }

  }

   void cLookup::build( Int l, Int m, Int n, Real ***y, Real ***q )
  {
      Int i,j,k;      

      nv=l;
      n1=m;
      n2=n;

      allc3(  2,n1,n2, &x );
      allc3( nv,n1,n2, &v );

      for( j=0;j<n2;j++ )
     {
         for( i=0;i<n1;i++ )
        {
            x[j][i][0]= y[j][i][0];
            x[j][i][1]= y[j][i][1];
            for( k=0;k<nv;k++ )
           {
               v[j][i][k]= q[j][i][k];
           }
        }
     }
  }
