   using namespace std;

#  include <gas/idgas.h>
#  include <iostream>

   void cIdGas::mfbrhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {
      Real u1,u2,h,a,s,rho,p,t;
      Real n1,n2,t1,t2;
      Real un,ut,u0, ux;
      Real A;

      n1= w[0];
      n2= w[1];
      A=  w[2];
      u0= w[3];
      ux= w[4];
      t1=-n2;
      t2= n1;

      u1=    q[0]; 
      u2=    q[1]; 
      t=     q[2]; 
      p=     q[3]; 
      rho= aux[0];
      h=   aux[1];
      s=   aux[2];
      a=   aux[3];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;

      rhs[0]+= fct*A*rho*(un-ux);
      rhs[1]+= fct*atan2(ut-u0,un-ux);
      rhs[2]+= fct*s;
      rhs[3]+= fct*(h -ut*u0 -un*ux);

  }

   void cIdGas::mfbdrhsw( Real  *q, Real  *aux, Real  *w, Real *dw, Real fct, Real *drhs )
  {
      Real u1,u2,h,a,s,rho,p,t;

      Real n1,n2,t1,t2;
      Real un,ut;
      Real dn1,dn2,dt1,dt2;
      Real u,dun,dut,du0,vt,v,dvt,u0, vn, dvn, ux, dux;
      Real A,dA;
      Real dalpha;

      n1= w[0];
      n2= w[1];
      A = w[2];
      u0= w[3];
      ux= w[4];

      t1=-n2;
      t2= n1;

      dn1= dw[0];
      dn2= dw[1];
      dA = dw[2];
      du0= dw[3];
      dux= dw[4];

      dt1=-dn2;
      dt2= dn1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;
      vt=ut-u0;
      vn=un-ux;

      u=  un*un+ ut*ut;
      v=  vn*vn+ vt*vt;

      dun= u1*dn1+ u2*dn2; 
      dut= u1*dt1+ u2*dt2; 
      dvt=dut-du0;
		dvn=dun-dux;

      dalpha= -vt*dvn+ vn*dvt;
      dalpha/= v;

      drhs[0]+= fct*( A*rho*dvn+ vn*rho*dA );
      drhs[1]+= fct*dalpha;
      drhs[3]-= fct*( ut*du0+ dut*u0 + un*dux+ dun*ux );

  }

   void cIdGas::mfbdrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs )
  {
      Real u1,u2,h,a,s,rho,p,t;
      Real du1,du2,dh,da,ds,drho,dp,dt;

      Real n1,n2,t1,t2;
      Real un,ut;
      Real u,dun,dut,v,vt,u0, vn, ux;
      Real A,dA;
      Real dalpha;

      n1= w[0];
      n2= w[1];
      A = w[2];
      u0= w[3];
      ux= w[4];

      t1=-n2;
      t2= n1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      du1=    dq[0]; 
      du2=    dq[1]; 
      dt=     dq[2]; 
      dp=     dq[3]; 

      drho= daux[0];
      dh=   daux[1];
      ds=   daux[2];
      da=   daux[3];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;
      vn=un-ux;
      vt=ut-u0;
      u= un*un+ ut*ut;
      v= vn*vn+ vt*vt;

      dun= n1*du1+ n2*du2;
      dut= t1*du1+ t2*du2;

      dalpha= -vt*dun+ un*dut;
      dalpha/= v;

      drhs[0]+= fct*( A*rho*dun+ A*vn*drho );
      drhs[1]+= fct*dalpha;
      drhs[2]+= fct*ds;
      drhs[3]+= fct*(dh -dut*u0 -dun*ux);

  }

   void cIdGas::dmfb( Real  *q, Real  *aux, Real  *w, Real *drhs , Real *dq, Real *daux )
  {
      Real u1,u2,h,a,s,rho,p,t,m;
      Real du1,du2,dh,da,ds,drho,dp,dt,dm;

      Real n1,n2,t1,t2;
      Real u,un,ut,vt,v,u0, vn, ux;
      Real cb,sb,talp;
      Real dun,dut;

      Real A;

      n1= w[0];
      n2= w[1];
      A = w[2];
      u0= w[3];
      ux= w[4];

      t1=-n2;
      t2= n1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      dun=-drhs[0];
      dut=-drhs[1];
      ds= -drhs[2];
      dh= -drhs[3];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;
      vt= ut-u0;
      vn= un-ux;
      u= un*un+ ut*ut;
      v= vn*vn+ vt*vt;
      v= sqrt(v);
      cb= un/v;
      sb= vt/v;
      talp= sb/cb;

      dun/= ( rho*A ); 
      dut*= v;
      dut+= sb*dun;
      dut/= cb;

      dt=  dh;
      drho=ds;

      dt-= ( vn*dun+ vt*dut );
      dt/= (cp*t);
      drho-= cv*dt;
      drho/=( v*v/(gam*t)-rg );
      dt+= v*v*drho/(cp*t);
      dp= dt+drho;

      dun-= vn*drho;
      dut-= vt*drho;
      da= 0.5*a*dt;

      drho*= rho;
      dp*= p;
      dt*= t;

      dq[0]= dun*n1+ dut*t1;
      dq[1]= dun*n2+ dut*t2;
      dq[2]= dt;
      dq[3]= dp;
      
      daux[0]= drho;
      daux[1]=   dh;
      daux[2]=   ds;
      daux[3]=   da;

  }

