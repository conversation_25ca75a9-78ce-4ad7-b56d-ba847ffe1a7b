   using namespace std;

#  include <iostream>
#  include <utils/proto.h>
#  include <gas/idgas.h>

   void cIdGas::sta2to( Real *qi, Real *auxi, Real *q0, Real *aux0 )
  {

		Real rhsi[MXVARS], rhs0[MXVARS];
      Real         wi[3]={1.,0.,1.};
		Real dq0[MXVARS], daux0[MXVARS];

		Int  iv, it;
		Int nv= 4;

		for( iv=0;iv<nv;iv++ )
	  { 
			q0[iv]= qi[iv]; 
	  }

      setv( 0,nv, 0., rhsi );
      setv( 0,nv, 0., dq0 );

      verhs( qi,auxi, wi, -1.,rhsi );

      for( Int it=0;it<10;it++ )
     {
         setv( 0,nv, 0.,rhs0 );
         for( iv=2;iv<nv;iv++ )
		  {				rhs0[iv]= rhsi[iv]; 		  }

         auxv( q0,aux0 );
         verhs( q0,aux0, wi, 1.,rhs0 );
         dve( q0,aux0, wi,  rhs0, dq0,daux0 );
         for( iv=0;iv<nv;iv++ )
		  {				q0[iv]+= 0.99*dq0[iv]; 		  }

     }


  }




   void cIdGas::achnge( Real *qi, Real *auxi, Real *qo, Real *auxo, Real A1, Real A2 )
  {

		Real rhsi[MXVARS], rhso[MXVARS];
      Real         wi[3]={1., 0., A1 };
      Real         wo[3]={1., 0., A2 };
		Real dqo[MXVARS], dauxo[MXVARS];

		Int  iv, it;
		Int nv= 4;

		for( iv=0;iv<nv;iv++ )
	  { 
			qo[iv]= qi[iv]; 
	  }

      setv( 0,nv, 0., rhsi );
      setv( 0,nv, 0., dqo );

      mfrhs( qi,auxi, wi,  -1.,rhsi );

      for( Int it=0;it<10;it++ )
     {
         setv( 0,nv, 0.,rhso );
         for( iv=0;iv<nv;iv++ )
		  {				rhso[iv]= rhsi[iv]; 		  }

         auxv( qo,auxo );
         mfrhs( qo,auxo, wo, 1.,rhso );
         dmf( qo, auxo, wo, rhso, dqo, dauxo );
         for( iv=0;iv<nv;iv++ )
		  {				qo[iv]+= 0.99*dqo[iv]; 		  }
     }
  }


   void cIdGas::mbeta( Real *qi, Real *auxi, Real *qo, Real *auxo, Real beta, Real *wi )
  {

		Real rhsi[MXVARS], rhso[MXVARS];
  //Real         wi[3]={1.,0.,1.};
		Real dqo[MXVARS], dauxo[MXVARS];
		Real Vin, Uin, u0in;
		Real um, ut, vt;

		Int  iv, it;
		Int nv= 4;

		u0in= wi[3];

		um= qi[0];
		ut= qi[1];
		vt= ut - u0in;

		Uin= sqrt( um*um + ut*ut );
		Vin= sqrt( um*um + vt*vt );

		for( iv=0;iv<nv;iv++ )
	  { 
			qo[iv]= qi[iv]; 
	  }

      setv( 0,nv, 0., rhsi );
      setv( 0,nv, 0., dqo );

      verhs( qi,auxi, wi, -1.,rhsi );

      for( Int it=0;it<10;it++ )
     {
         setv( 0,nv, 0.,rhso );
			rhso[0]= -Uin*cos(beta);
			rhso[1]= -Uin*sin(beta);
         for( iv=2;iv<nv;iv++ )
		  {				rhso[iv]= rhsi[iv]; 		  }

         auxv( qo,auxo );
         verhs( qo,auxo, wi, 1.,rhso );
         dve( qo,auxo, wi,  rhso, dqo,dauxo );
         for( iv=0;iv<nv;iv++ )
		  {				qo[iv]+= 0.99*dqo[iv]; 		  }

     }

		qo[1]+= u0in;


		um= qi[0];
		ut= qi[1];
		vt= ut - u0in;

		Uin= sqrt( um*um + ut*ut );
		Vin= sqrt( um*um + vt*vt );

		cout << "Min relative:     " << Vin/auxo[3] << "\n";
		cout << "Min absolute:     " << Uin/auxo[3] << "\n";

  }




   void cIdGas::irpm( Real *qi, Real *auxi, Real *qo, Real *auxo, Real u0, Real beta, Real V1 )
  {

		Real rhsi[MXVARS], rhso[MXVARS];
      Real         wi[3]={1.,0.,1.};
		Real dqo[MXVARS], dauxo[MXVARS];

		Int  iv, it;
		Int nv= 4;
		

		for( iv=0;iv<nv;iv++ )
	  { 
			qo[iv]= qi[iv]; 
	  }

      setv( 0,nv, 0., rhsi );
      setv( 0,nv, 0., dqo );

      verhs( qi,auxi, wi, -1.,rhsi );

      for( Int it=0;it<10;it++ )
     {
         setv( 0,nv, 0.,rhso );
			rhso[0]= -(u0-V1)/tan(beta);
			rhso[1]= -(u0-V1);
         for( iv=2;iv<nv;iv++ )
		  {				rhso[iv]= rhsi[iv]; 		  }

         auxv( qo,auxo );
         verhs( qo,auxo, wi, 1.,rhso );
         dve( qo,auxo, wi,  rhso, dqo,dauxo );
         for( iv=0;iv<nv;iv++ )
		  {				qo[iv]+= 0.99*dqo[iv]; 		  }

     }


  }

	void cIdGas::chdir0b( Real *qi, Real *auxi, Real *qo, Real *auxo, Real *wi, Real *wo, Real beta )
  {
		
		Real rhsi[MXVARS], rhso[MXVARS];
		Real dqo[MXVARS], dauxo[MXVARS];
		Real qirel[MXVARS], auxirel[MXVARS];
		Real rlx;
		Int  iv, it;
		Int nv= 4;

		for( iv=0;iv<nv;iv++ )
	  { 
			qirel[iv]= qi[iv]; 
			qo[iv]= qi[iv]; 
	  }
		qirel[1]-= wi[3];
		qo[1]-= wi[3];

		auxv( qirel, auxirel );
		auxv( qo, auxo );



      setv( 0,nv, 0., rhsi );
      setv( 0,nv, 0., dqo );

		mfbrhs( qirel,auxirel, wo,  -1.,rhsi );

		rlx= 0.5;
      for( Int it=0;it<20;it++ )
     {
         setv( 0,nv, 0.,rhso );
         for( iv=0;iv<nv;iv++ )
		  {				rhso[iv]= rhsi[iv]; 		  }
	
			rhso[1]= -beta;

         auxv( qo,auxo );

         mfbrhs( qo,auxo, wo, 1.,rhso );
			dmfb( qo, auxo, wo, rhso, dqo, dauxo );

         for( iv=0;iv<nv;iv++ )
		  {				qo[iv]+= rlx*dqo[iv]; 		  }

         rlx*= 1.2;
         rlx= fmin( 0.999,rlx );

     }
  }
