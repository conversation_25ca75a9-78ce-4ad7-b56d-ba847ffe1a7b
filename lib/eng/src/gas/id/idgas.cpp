   using namespace std;

#  include <gas/idgas.h>
#  include <iostream>

   cIdGas::cIdGas()
  {
      gam= 1.4;
      rg=  287.;
      cv= gam-1;
      cv= 1./cv;
      cv*= rg;
      cp= cv*gam;

      cout << cp << " "<<cv<<" "<<rg<<" "<<gam<<"\n";

      tref=288.;
      pref=100000.;
  }
   cIdGas::~cIdGas()
  {
  }

   void cIdGas::auxv( Real *q, Real *aux )
  {
      Real u,v,t,p;
      Real rho,h,s,a,k;

      u= q[0];
      v= q[1];
      t= q[2];
      p= q[3];

      k= u*u;
      k+= v*v;
      k*= 0.5;

      h= cp*t+ k;
      rho= p/( rg*t );
      s= cp*log(t/tref)-rg*log(p/pref);
      a= gam*rg*t;
      a= sqrt(a);

      aux[0]= rho;
      aux[1]= h;
      aux[2]= s;
      aux[3]= a;
  }

   void cIdGas::dauxv( Real *q, Real *aux, Real *dq, Real *daux )
  {
      Real u,v,t,p;
      Real rho,h,s,a;
      Real dt,dp;
      Real drho,dh,ds,da,dk,du,dv;

      u= q[0];
      v= q[1];
      t= q[2];
      p= q[3];
      rho= aux[0];
      h= aux[1];
      s= aux[2];
      a= aux[3];

      du= dq[0];
      dv= dq[1];
      dt= dq[2];
      dp= dq[3];

      dk= u*du;
      dk+= v*dv;


      dh= dk+cp*dt;
      dt/= t;
      dp/= p;
      drho= dp-dt;
      drho*= rho;
      ds= cp*dt-rg*dp;
      da= 0.5*dt;
      da*= a;

      daux[0]= drho;
      daux[1]= dh;
      daux[2]= ds;
      daux[3]= da;
  }

   void cIdGas::prrhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {
      Real eta,pr,r0,r1,s;
      Real p;
      pr=  w[1];

      eta= w[0];
      p= q[3]; 
      s= aux[2];

      r0= pr*p;

      r1= log(pr);
      r1*= rg;
      if( pr > 1. )
     {
         r1*= (1/eta-1);
     }
      else
     {
         r1*= (eta-1);
     }
      r1+= s;

      rhs[2]+= fct*r0;
      rhs[3]+= fct*r1;

  }

   void cIdGas::prdrhsq( Real *q, Real *aux, Real *dq, Real *daux, Real *w, Real fct, Real *drhs )
  {
      Real eta,pr,ds;
      Real dp;

      pr=  w[1];
      dp= dq[3]; 
      ds= daux[2];
      drhs[2]+= fct*pr*dp; 
      drhs[3]+= fct*ds;
  }

   void cIdGas::prdrhsw( Real *q, Real *aux, Real *w, Real *dw, Real fct, Real *drhs )
  {
      Real eta,pr,ds;
      Real deta,dpr,p;
      Real r0,r1,r10,r11;;

      pr=  w[1];

      p= q[3]; 

      dpr=  dw[1];
      r0= dpr*p; 

      eta= w[0];
      deta= dw[0];

      r10= log(pr);
      r11= dpr/pr;
      if( pr > 1. )
     {
         r11*= (1./eta-1);
         r10*= -deta;
         eta*= eta;
         r10/= eta;
     }
      else
     {
         r11*= (eta-1);
         r10*= deta;
     }
      r1= r10+r11;
      r1*= rg;

      drhs[2]+= fct*r0;
      drhs[3]+= fct*r1;

  }

   void cIdGas::dpr( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux )
  {
      Real u,v,t,p;
      Real dk,dh,drho,dt,ds,dp,rho,du,dv,a,da;

      u= q[0]; 
      v= q[1]; 
      t= q[2]; 
      p= q[3]; 

      du=-rhs[0]; 
      dv=-rhs[1]; 
      dk= u*du+ v*dv;

      rho=aux[0];
      a=  aux[3];

      dp= -rhs[2];
      dp/= p;

      ds= -rhs[3];

      dt=  ds;
      dt+= rg*dp;
      dt/= cp;
    
      drho= dp-dt;
      drho*= rho;
      da= 0.5*dt;
      da*= a;

      dp*= p;
      dt*= t;
      dh= cp*dt;
      dh+= dk;

      dq[0]= du;
      dq[1]= dv;
      dq[2]= dt;
      dq[3]= dp;

      daux[0]= drho;
      daux[1]= dh;
      daux[2]= ds;
  }

   void cIdGas::dhcrhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {
      Real eta,wrk,ds,s;
      Real p,h;
      Real r0,r1;

      eta= w[0];
      wrk= w[1];

      p= q[3]; 

      h= aux[1]; 
      s= aux[2]; 
      r0= h;
      r0+= wrk;

      r1=-log(p/pref);
      r1*= rg;
      r1*= (1/eta-1);
      r1+= s;

      rhs[2]+= fct*r0;
      rhs[3]+= fct*r1;

  }

   void cIdGas::dhcdrhsq( Real *q, Real *aux, Real *dq, Real *daux, Real *w, Real fct, Real *drhs )
  {
      Real eta,pr,ds;
      Real dp,wrk,dh;
      Real p,h,s;
      Real r1,r0;

      eta= w[0];
      wrk=  w[1];

      p= q[3]; 
      h= aux[1]; 
      s= aux[2]; 
      dp= dq[1]; 
      dh= daux[1]; 
      ds= daux[2]; 

      r0= dh;
      r1=-dp;
      r1/= p;
      r1*= rg;
      r1*= (1/eta-1);
      r1+= ds;
   
      drhs[2]+= fct*r0;
      drhs[3]+= fct*r1;

  }

   void cIdGas::dhcdrhsw( Real *q, Real *aux, Real *w, Real *dw, Real fct, Real *drhs )
  {
      Real eta,pr,ds;
      Real deta,dpr;
      Real dp,wrk,dwrk,h,p;
      Real r1,r0,r10,r11;

      wrk=  w[1];
      eta=  w[0];

      dwrk= dw[1];
      deta= dw[0];

      p= q[3]; 
      h= aux[1]; 

      r0= dwrk;
      r1=-log(p/pref);
      r1*= -deta;
      eta*= eta;
      r1/= eta;
      r1*= rg;

      drhs[2]+= fct*r0;
      drhs[3]+= fct*r1;

  }

   void cIdGas::ddhc( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux )
  {
      Real u,v,t,p;
      Real dh,drho,dt,ds,dp,rho,da,a,du,dv,dk;
      Real eta,wrk;

      eta=  w[0];
      wrk=  w[1];

      u= q[0];
      v= q[1];
      t= q[2]; 
      p= q[3]; 
 
      du= -rhs[0];
      dv= -rhs[1];

      rho=aux[0];
      a=  aux[3];

      dk= u*du;
      dk+=v*dv;
      dh= -rhs[2];
      dh-= dk;
      dt=  dh;
      dt/= t;

      dp=  rhs[1];
      dp+= dt;
      dp*= eta;
      ds= dt- dp;
      dp/= rg;
      dt/= cp;
      da= 0.5*dt;
      da*= a;
    
      drho= dp-dt;
      drho*= rho;
      dp*= p;
      dt*= t;

      dq[0]= du;
      dq[1]= dv;
      dq[2]= dt;
      dq[3]= dp;

      daux[0]= drho;
      daux[1]= dh;
      daux[2]= ds;
      daux[3]= da;
  }

   void cIdGas::dherhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {
      Real eta,wrk,ds,s;
      Real p,h;
      Real r0,r1;

      eta= w[0];
      wrk= w[1];

      p= q[3]; 

      h= aux[1]; 
      s= aux[2]; 
      r0= h;
      r0+= wrk;

      r1=-log(p/pref);
      r1*= rg;
      r1*= (eta-1);
      r1+= s;

      rhs[2]+= fct*r0;
      rhs[3]+= fct*r1;

  }

   void cIdGas::dhedrhsq( Real *q, Real *aux, Real *dq, Real *daux, Real *w, Real fct, Real *drhs )
  {
      Real eta,pr,ds;
      Real dp,wrk,dh;
      Real p,h,s;
      Real r1,r0;

      eta= w[0];
      wrk=  w[1];

      p= q[3]; 
      h= aux[1]; 
      s= aux[2]; 
      dp= dq[1]; 
      dh= daux[1]; 
      ds= daux[2]; 

      r0= dh;
      r1=-dp;
      r1/= p;
      r1*= rg;
      r1*= (eta-1);
      r1+= ds;
   
      drhs[2]+= fct*r0;
      drhs[3]+= fct*r1;

  }

   void cIdGas::dhedrhsw( Real *q, Real *aux, Real *w, Real *dw, Real fct, Real *drhs )
  {
      Real eta,pr,ds;
      Real deta,dpr;
      Real dp,wrk,dwrk,h,p;
      Real r1,r0,r10,r11;

      wrk=  w[1];
      eta=  w[0];

      dwrk= dw[1];
      deta= dw[0];

      p= q[3]; 
      h= aux[1]; 

      r0= dwrk;
      r1=-log(p/pref);
      r1*= eta;
      r1*= rg;

      drhs[2]+= fct*r0;
      drhs[3]+= fct*r1;

  }

   void cIdGas::ddhe( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux )
  {
      Real t,p,u,v,a;
      Real dh,drho,dt,ds,dp,rho,da,du,dv,dk;
      Real eta,wrk;

      eta=  w[0];
      wrk=  w[1];

      u= q[0];
      v= q[1];
      t= q[2]; 
      p= q[3]; 
 
      du= -rhs[0];
      dv= -rhs[1];

      rho=aux[0];
      a=  aux[3];

      dk= u*du;
      dk+=v*dv;
      dh= -rhs[2];
      dh-= dk;
      dt=  dh;
      dt/= t;

      dp=  rhs[1];
      dp+= dt;
      dp/= eta;
      ds= dt- dp;
      dp/= rg;
      dt/= cp;
      da= 0.5*dt;
      da*= a;
    
      drho= dp-dt;
      drho*= rho;
      dp*= p;
      dt*= t;

      dq[0]= du;
      dq[1]= dv;
      dq[2]= dt;
      dq[3]= dp;

      daux[0]= drho;
      daux[1]= dh;
      daux[2]= ds;
      daux[3]= da;
  }

