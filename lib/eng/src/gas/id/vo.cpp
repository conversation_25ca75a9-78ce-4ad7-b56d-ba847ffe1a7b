
   using namespace std;

#  include <gas/idgas.h>
#  include <iostream>

   void cIdGas::vorhs( Int ist, Int ien, Real *q[], Real *aux[], Real *w[], Real fct, Real *rhs[] )
  {
      Int  i;
      Real rho,um,ut,e,p;
      Real v,r;

      for( i=ist;i<ien;i++ )
     {
         v= w[0][i];
         r= w[1][i];

         um=    q[0][i];
         ut=    q[1][i];
         p=     q[3][i];
         rho= aux[0][i];
         e=   aux[1][i];

         um*=  rho;
         ut*=  rho;
         e*=   rho;
         e-=     p;
         
         rhs[0][i]+= fct*rho*v;
         rhs[1][i]+= fct*um*v;
         rhs[2][i]+= fct*ut*v*r;
         rhs[3][i]+= fct*e*v;
     }
  }

   void cIdGas::vodrhsw( Int ist, Int ien, Real *q[], Real *aux[], Real *w[], Real *dw[], Real fct, Real *drhs[] )
  {
      Int  i;
      Real rho,um,ut,e,p;
      Real dut;
      Real v,r;
      Real dv,dr;

      for( i=ist;i<ien;i++ )
     {
         v=     w[0][i];
         r=     w[1][i];
         dv=   dw[0][i];
         dr=   dw[1][i];

         um=    q[0][i];
         ut=    q[1][i];
         p=     q[3][i];
         rho= aux[0][i];
         e=   aux[1][i];

         um*=  rho;
         ut*=  rho;
         e*=   rho;
         e-=     p;

         drhs[0][i]+= fct*rho*dv;
         drhs[1][i]+= fct*um*dv;
         drhs[2][i]+= fct*ut*( dv+ dr );
         drhs[3][i]+= fct*e*dv;
     }
  }

   void cIdGas::vodrhsq( Int ist, Int ien, Real  *q[], Real  *aux[], Real  *w[], Real *dq[], Real *daux[], Real fct, Real *drhs[] )
  {
      Int  i;
      Real rho,um,ut,h,e,p;
      Real drho,dum,dut,dh,de,dp;
      Real v,r;

      for( i=ist;i<ien;i++ )
     {
         v= w[0][i];
         r= w[1][i];

         um=      q[0][i];
         ut=      q[1][i];
         p=       q[3][i];
         rho=   aux[0][i];
         h=     aux[1][i];
         e=       h-p/rho;

         dum=    dq[0][i];
         dut=    dq[1][i];
         dp=     dq[3][i];
         drho= daux[0][i];
         dh=   daux[1][i];
         de=   rho*dh+ p*drho/rho- dp;
         de+=      e*drho;

         dum*=        rho;
         dum+=    um*drho;

         dut*=        rho;
         dut+=    ut*drho;

         drhs[0][i]+= fct*drho* v;
         drhs[1][i]+= fct*dum*  v;
         drhs[2][i]+= fct*dut*  v*r;
         drhs[3][i]+= fct*de*   v;
     }
  }

   void cIdGas::dvo( Int ist, Int ien, Real  *q[], Real  *aux[], Real  *w[], Real *drhs[], Real *dq[], Real *daux[] )
  {
      Int i;
      Real dt,t;
      Real rho,um,ut,e,h,a,p,s;
      Real drho,dum,dut,de,dh,da,dp,ds;
      Real v,r;
      for( i=ist;i<ien;i++ )
     {
         v= w[0][i];
         r= w[1][i];

         um=    q[0][i];
         ut=    q[1][i];
         t=     q[2][i];
         p=     q[3][i];
         rho= aux[0][i];
         e=   aux[1][i];

         um*=  rho;
         ut*=  rho;
         e*=   rho;
         e-=     p;

         drho= -drhs[0][i];
         dum = -drhs[1][i];
         dut = -drhs[2][i];
         de  = -drhs[3][i];

         drho/= v;
         dum/=  v;
         dut/=  v;
         dut/=  r;
         de/=   v;

         dum-= um*drho;
         dum/= rho;

         dut-= ut*drho;
         dut/= rho;

         de-= e*drho;
         de/= rho;

         de-= um*dum;
         de-= ut*dut;

         dt=  de/cv;
         dt/= t;
         dp=  drho/rho;
         dp+= dt;

         ds= cp*dt; 
         ds- rg*dp; 

         da= 0.5*dt;
         da*= a;

         dt*= t;
         dp*= p;

         dh=  cp*dt;
         dh+= um*dum; 
         dh+= ut*dut;

         daux[0][i]= drho;
         daux[1][i]=   dh;
         daux[2][i]=   ds;
         daux[3][i]=   da;
       
         dq[0][i]= dum;
         dq[1][i]= dut;
         dq[2][i]= dt;
         dq[3][i]= dp;
         
     }
  }
