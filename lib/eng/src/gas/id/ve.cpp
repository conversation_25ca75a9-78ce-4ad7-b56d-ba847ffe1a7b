   using namespace std;

#  include <gas/idgas.h>
#  include <iostream>

   void cIdGas::verhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {
      Real u1,u2,h,a,s,rho,p,t;
      Real n1,n2,t1,t2;
      Real un,ut;

      n1= w[0];
      n2= w[1];
      t1=-n2;
      t2= n1;

      u1=    q[0]; 
      u2=    q[1]; 
      t=     q[2]; 
      p=     q[3]; 

      rho= aux[0];
      h=   aux[1];
      s=   aux[2];
      a=   aux[3];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;

      rhs[0]+= fct*un;
      rhs[1]+= fct*ut;
      rhs[2]+= fct*s;
      rhs[3]+= fct*h;
  }

   void cIdGas::vedrhsw( Real  *q, Real  *aux, Real  *w, Real *dw, Real fct, Real *drhs )
  {
      Real u1,u2,h,a,s,rho,p,t;
      Real n1,n2,t1,t2;
      Real un,ut;
      Real dn1,dn2,dt1,dt2;
      Real dun,dut;

      n1= w[0];
      n2= w[1];
      t1=-n2;
      t2= n1;

      dn1= dw[0];
      dn2= dw[1];
      dt1=-dn2;
      dt2= dn1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      dun= u1*dn1+ u2*dn2; 
      dut= u1*dt1+ u2*dt2; 

      drhs[0]+= fct*dun;
      drhs[1]+= fct*dut;

  }

   void cIdGas::vedrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs )
  {
      Real u1,u2,h,a,s,rho,p,t;
      Real du1,du2,dh,da,ds,drho,dp,dt;
      Real n1,n2,t1,t2;
      Real un,ut;
      Real dun,dut;

      n1= w[0];
      n2= w[1];
      t1=-n2;
      t2= n1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      du1=    dq[0]; 
      du2=    dq[1]; 
      dt=     dq[2]; 
      dp=     dq[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      drho= daux[0];
      dh=   daux[1];
      ds=   daux[2];
      da=   daux[3];

      dun= n1*du1+ n2*du2;
      dut= t1*du1+ t2*du2;

      drhs[0]+= fct*dun;
      drhs[1]+= fct*dut;
      drhs[2]+= fct*ds;
      drhs[3]+= fct*dh;

  }

   void cIdGas::dve( Real  *q, Real  *aux, Real  *w, Real *drhs, Real *dq, Real *daux )
  {
      Real u1,u2,h,a,s,rho,p,t;
      Real du1,du2,dh,da,ds,drho,dp,dt,dk;

      Real n1,n2,t1,t2;
      Real un,ut;
      Real dun,dut;

      n1= w[0];
      n2= w[1];
      t1=-n2;
      t2= n1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      dun= -drhs[0];
      dut= -drhs[1];
      ds=  -drhs[2];
      dh=  -drhs[3];

      du1= n1*dun+ t1*dut;
      du2= n2*dun+ t2*dut;

      dk= u1*du1+ u2*du2;
      dt= dh- dk;
      dt/= t;
      dp= -ds+ dt;
      dp/= rg;

      dt/= cp;
      drho= dp- dt;

      dp*= p;
      drho*= rho;

      da= 0.5*a*dt;
      dt*= t;

      dq[0]= du1;
      dq[1]= du2;
      dq[2]= dt;
      dq[3]= dp;
      
      daux[0]= drho;
      daux[1]=   dh;
      daux[2]=   ds;
      daux[3]=   da;

  }
