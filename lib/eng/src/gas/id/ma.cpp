   using namespace std;

#  include <gas/idgas.h>
#  include <iostream>

   void cIdGas::marhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {

      Real u1,u2,h,a,s,rho,p,t;
      Real n1,n2,t1,t2,u0;
      Real un,ut;
      Real vn,vt,v;
      Real m,b;

      n1= w[0];
      n2= w[1];
      u0= w[3];

      t1=-n2;
      t2= n1;

      u1=    q[0]; 
      u2=    q[1]; 
      t=     q[2]; 
      p=     q[3]; 

      rho= aux[0];
      h=   aux[1];
      s=   aux[2];
      a=   aux[3];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;
      vt=ut-u0;
      v= un*un+vt*vt;
      v= sqrt(v);

      m=v/a;
      b=atan2(vt,un);

      rhs[0]+= fct*m;
      rhs[1]+= fct*atan2(vt,un);
      rhs[2]+= fct*s;
      rhs[3]+= fct*(h-u0*ut);
  }

   void cIdGas::madrhsw( Real  *q, Real  *aux, Real  *w,  Real *dw, Real fct, Real *drhs )
  {

      Real u1,u2,h,a,s,rho,p,t;

      Real n1,n2,t1,t2;
      Real un,ut;
      Real vn,vt;
      Real v,v2,b;
      Real dn1,dn2,dt1,dt2;
      Real dun,dut;
      Real dvn,dvt;
      Real u0,du0;
      Real cb,sb;
      Real dv,db;
      Real dm,m;

      n1= w[0];
      n2= w[1];
      u0= w[3];

      t1=-n2;
      t2= n1;

      dn1= dw[0];
      dn2= dw[1];
      du0= dw[3];

      dt1=-dn2;
      dt2= dn1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;
      vn=un;
      vt=ut-u0;

      v2= vn*vn+ vt*vt;
      v= sqrt(v2);
 
      cb= vn/v;
      sb= vt/v;

      dun= u1*dn1+ u2*dn2; 
      dut= u1*dt1+ u2*dt2; 
      dvn= dun;
      dvt= dut-du0;

      dv= cb*dvn+ sb*dvt;
      db=-sb*dvn+ cb*dvt; 
      db/= v;

      dm= dv/a;

      drhs[0]+= fct*dm;
      drhs[1]+= fct*db;
      drhs[3]-= fct*(ut*du0+u0*dut);

  }

   void cIdGas::madrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs )
  {

      Real u1,u2,h,a,s,rho,p,t;
      Real du1,du2,dh,da,ds,drho,dp,dt;

      Real n1,n2,t1,t2;
      Real un,ut;
      Real dun,dut;
      Real v,v2;
      Real vn,vt;
      Real dvn,dvt;
      Real cb,sb,b,dv,db,u0;
      Real m,dm;

      n1= w[0];
      n2= w[1];
      u0= w[3];
      t1=-n2;
      t2= n1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      du1=    dq[0]; 
      du2=    dq[1]; 
      dt=     dq[2]; 
      dp=     dq[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      drho= daux[0];
      dh=   daux[1];
      ds=   daux[2];
      da=   daux[3];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;
      vn= un;
      vt= ut-u0;

      dun= n1*du1+ n2*du2;
      dut= t1*du1+ t2*du2;

      dvn= dun;
      dvt= dut;

      v2= vn*vn+ vt*vt;
      v= sqrt(v2);
      m= v/a;

      cb= un/v;
      sb= vt/v;

      dv=  cb*dvn+ sb*dvt;
      db= -sb*dvn+ cb*dvt;
      db/= v;

      dm= dv/v- da/a;
      dm*=m;

      drhs[0]+= fct*dm;
      drhs[1]+= fct*db;
      drhs[2]+= fct*ds;
      drhs[3]+= fct*(dh-u0*dut);

  }

   void cIdGas::dma( Real  *q, Real  *aux, Real  *w, Real *drhs, Real *dq, Real *daux )
  {
      Real u1,u2,h,a,s,rho,p,t,k;
      Real du1,du2,dh,da,ds,drho,dp,dt,u0;

      Real n1,n2,t1,t2;
      Real un,ut;
      Real vn,vt;
      Real v,v2;
      Real dun,dut;
      Real dvn,dvt;
      Real dv,db;
      Real sb,cb;
      Real m;

      n1= w[0];
      n2= w[1];
      u0= w[3];
      t1=-n2;
      t2= n1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;

      vn= un;
      vt= ut-u0;

      v2= vn*vn+ vt*vt;
      v= sqrt(v2);
      m= v/a;

      cb= vn/v;
      sb= vt/v;

      dv= -drhs[0];
      db= -drhs[1];
      ds= -drhs[2];
      dh= -drhs[3];

      dt= dh;
      dp= ds;

      dv/= m;
      dt-= v2*dv;
      dt/= ( cp*t+0.5*v2 );
      dv+= 0.5*dt;
      
      dv*= v;
      dvn= dv*cb- vt*db;
      dvt= dv*sb+ vn*db;

      dun= dvn;
      dut= dvt;
      dh+= u0*dvt;
      
      dp-= cp*dt;
      dp/= -rg;
    
      drho= dp-dt;
      da= 0.5*dt;

      du1= n1*dun+ t1*dut;
      du2= n2*dun+ t2*dut;
      dp*= p;
      dt*= t;
      da*= a;
      drho*= rho;

      dq[0]= du1;
      dq[1]= du2;
      dq[2]= dt;
      dq[3]= dp;
      
      daux[0]= drho;
      daux[1]=   dh;
      daux[2]=   ds;
      daux[3]=   da;

  }
