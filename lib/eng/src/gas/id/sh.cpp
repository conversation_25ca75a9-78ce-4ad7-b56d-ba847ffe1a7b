   using namespace std;

#  include <gas/idgas.h>

   void cIdGas::shrhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {
      Real u1,u2,h,a,s,rho,p,t,m;
      Real un,ut;
      Real n1,n2,t1,t2;

      n1= w[0];
      n2= w[1];

      t1=-n2;
      t2= n1;

      u1=    q[0]; 
      u2=    q[1]; 
      t=     q[2]; 
      p=     q[3]; 

      rho= aux[0];
      h=   aux[1];
      s=   aux[2];
      a=   aux[3];

      un= n1*u1+ n2*u2;
      ut= t1*u1+ t2*u2;

      m= rho*un;

      rhs[0]+= fct*m;
      rhs[1]+= fct*( m*un+ p );
      rhs[2]+= fct*ut;
      rhs[3]+= fct*h;
  }

   void cIdGas::shdrhsw( Real  *q, Real  *aux, Real  *w, Real *dw, Real fct, Real *drhs )
  {
      Real u1,u2,h,a,s,rho,p,t,m,dm;

      Real un,ut;
      Real n1,n2,t1,t2;

      Real dun,dut;
      Real dn1,dn2,dt1,dt2;

      n1= w[0];
      n2= w[1];

      t1=-n2;
      t2= n1;

      dn1= dw[0];
      dn2= dw[1];

      dt1=-dn2;
      dt2= dn1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      un= n1*u1+ n2*u2;
      ut= t1*u1+ t2*u2;

      dun= u1*dn1+ u2*dn2;
      dut= u1*dt1+ u2*dt2;

      m= rho*un;
      dm= rho*dun;

      drhs[0]+=   fct*dm;
      drhs[1]+=   fct*( m*dun+ dm*un );
      drhs[2]+=   fct*dut;
  }

   void cIdGas::shdrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs )
  {

      Real u1,u2,h,a,s,rho,p,t,m,dm;
      Real du1,du2,dh,da,ds,drho,dp,dt;

      Real un,ut;
      Real n1,n2,t1,t2;

      Real dun,dut;

      n1= w[0];
      n2= w[1];

      t1=-n2;
      t2= n1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      du1=    dq[0]; 
      du2=    dq[1]; 
      dt=     dq[2]; 
      dp=     dq[3]; 

      drho= daux[0];
      dh=   daux[1];
      ds=   daux[2];
      da=   daux[3];

      un= n1*u1+ n2*u2;
      ut= t1*u1+ t2*u2;

      dun= n1*du1+ n2*du2;
      dut= t1*du1+ t2*du2;

      m= rho*un;
      dm= drho*un+ rho*dun;

      drhs[0]+=   fct*dm;
      drhs[1]+=   fct*( m*dun+ dm*un+ dp );
      drhs[2]+=   fct*dut;
      drhs[3]+=   fct*dh;
  }

   void cIdGas::dsh( Real  *q, Real  *aux, Real  *w, Real *drhs, Real *dq,  Real *daux )
  {
      Real u1,u2,h,a,s,rho,p,t,m;
      Real du1,du2,dh,da,ds,drho,dp,dt,dm;

      Real un,ut;
      Real n1,n2,t1,t2;

      Real dun,dut;

      n1= w[0];
      n2= w[1];

      t1=-n2;
      t2= n1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      a=     aux[3];

      un= n1*u1+ n2*u2;
      ut= t1*u1+ t2*u2;

      dun= -drhs[0];
      dp=  -drhs[1];
      dut= -drhs[2];
      dh=  -drhs[3];

      dp-= 2*un*dun;
      dun/= rho;

      drho= dh- un*dun- ut*dut- gam/(gam-1)*dp/rho;
      drho*= ( gam-1 );
      drho/= ( un*un- a*a );
      dun-= un*drho;
      dp+= rho*un*un*drho;
      dt= dp/p- drho;
      ds= cv*dt- rg*drho;
      da= 0.5*a*dt;

      dt*= t;
      drho*= rho;

      dq[0]= dun*n1+ dut*t1;
      dq[1]= dun*n2+ dut*t2;
      dq[2]= dt; 
      dq[3]= dp;

      daux[0]= drho;
      daux[1]= dh;
      daux[2]= ds;
      daux[3]= da;
  }
