#  include <fprec.h>

      subroutine janafrd( ncl,cpath, ncs,csp, nel,lel,cel,mel, 
     1                    rsp,asp0,asp1 )

      implicit none

      Int                 ncl
      character(len=ncl)  cpath

      Int                 ncs
      character(len=ncs)  csp

      Int                 nel
      Int                 mel
      dimension           mel(10)
      Int                 lel
      dimension           lel(10)
      Pntr                cel
      dimension           cel(10)

      character*80        line
      character*18        csp0

      character*4         c
      Int                 m

      Real                rsp,asp0,asp1
      dimension           rsp(*),asp0(*),asp1(*)

      Int                 ldum
      integer             iu,nl
      Int                 ie,i1,i2,ie0
      iu= 12
      nl=  5

      ldum=5

      open( unit=iu,file=trim(cpath),form='formatted',
     1      status= 'old' )
      call skipll( iu,nl )

      do 1000 while( .true. )

        read( iu,9999 ) line
        read( line,9998 ) csp0

        if( trim(csp0) .eq. trim(csp) )then
          nel= 0
          i2= 24
          ie0=1
          do 10 ie=1,4
            i1= i2+1   
            i2= i2+5 
            read( line(i1:i2),*,err=19,end=19) c,m
            nel= nel+1
            mel(nel)= m
            call jcassign( ldum,c,cel(nel))
            ie0= ie0+5
            lel(nel)= len_trim(c)
   10     continue
   19     continue

          read( line(46:75),* ) rsp(1),rsp(3),rsp(2)
          read( iu,9997 ) asp0(1), asp0(2), asp0(3), asp0(4), asp0(5)
          read( iu,9997 ) asp0(6), asp0(7), asp1(1), asp1(2), asp1(3)
          read( iu,9997 ) asp1(4), asp1(5), asp1(6), asp1(7)

          goto 1001

        endif

 1000 continue
 1001 continue

      close( iu )
 9999 format( a80 )
 9998 format( a18 )
 9997 format( 5(e15.8) )

      return

      end 

      subroutine skipll( iu,n )
      implicit none
      integer           iu,n
      integer           i
      do 5 i=1,n
    5   read( iu,* )
      return
      end

      subroutine janafrdtest( ncl,cpath, ncs,csp, cel )

      implicit none

      Int                 ncl
      character(len=ncl)  cpath

      Int                 ncs
      character(len=ncs)  csp

      character*4         cel
      dimension           cel(10)

      write( *,* ) ncl,cpath
      write( *,* ) ncs,csp
      write( *,* ) cel(1)
      return

      end
