   using namespace std;

#  include <gas/jgas.h>
#  include <iostream>

   void cJGas::mfarhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {
      Real u1,u2,h,a,s,rho,p,t,z;
      Real n1,n2,t1,t2;
      Real un,ut,u0;
      Real A;

      n1= w[0];
      n2= w[1];
      A=  w[2];
      u0= w[3];
      t1=-n2;
      t2= n1;

      u1=    q[0]; 
      u2=    q[1]; 
      t=     q[2]; 
      p=     q[3]; 
      z=     q[4]; 

      rho= aux[0];
      h=   aux[1];
      s=   aux[2];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;

      rhs[0]+= fct*A*rho*un;
      rhs[1]+= fct*atan2(ut-u0,un);
      rhs[2]+= fct*s;
      rhs[3]+= fct*(h-ut*u0);
      rhs[4]+= fct*z;

  }

   void cJGas::mfadrhsw( Real  *q, Real  *aux, Real  *w, Real *dw, Real fct, Real *drhs )
  {
      Real u1,u2,h,a,s,rho,p,t,z;

      Real n1,n2,t1,t2;
      Real un,ut;
      Real dn1,dn2,dt1,dt2;
      Real u,dun,dut,du0,vt,v,dvt,u0;
      Real A,dA;
      Real db;

      n1= w[0];
      n2= w[1];
      A = w[2];
      u0= w[3];

      t1=-n2;
      t2= n1;

      dn1= dw[0];
      dn2= dw[1];
      dA = dw[2];
      du0= dw[3];

      dt1=-dn2;
      dt2= dn1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 
      z=       q[4]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;
      vt=ut-u0;

      u=  un*un+ ut*ut;
      v= un*un+ vt*vt;

      dun= u1*dn1+ u2*dn2; 
      dut= u1*dt1+ u2*dt2; 
      dvt=dut-du0;

      db= -vt*dun+ un*dvt;
      db/= v;

      drhs[0]+= fct*( A*rho*dun+ un*rho*dA );
      drhs[1]+= fct*db;
      drhs[3]-= fct*( ut*du0+ dut*u0 );

  }

   void cJGas::mfadrhsq( Real  *q, Real  *aux, Real  *w, Real *dq, Real *daux, Real fct, Real *drhs )
  {
      Real u1,u2,h,a,s,rho,p,t;
      Real du1,du2,dh,da,ds,drho,dp,dt,dz;

      Real n1,n2,t1,t2;
      Real un,ut;
      Real u,dun,dut,v,vt,u0;
      Real A,dA;
      Real db;

      n1= w[0];
      n2= w[1];
      A = w[2];
      u0= w[3];

      t1=-n2;
      t2= n1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];

      du1=    dq[0]; 
      du2=    dq[1]; 
      dt=     dq[2]; 
      dp=     dq[3]; 
      dz=     dq[4]; 

      drho= daux[0];
      dh=   daux[1];
      ds=   daux[2];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;
      vt=ut-u0;
      u= un*un+ ut*ut;
      v= un*un+ vt*vt;

      dun= n1*du1+ n2*du2;
      dut= t1*du1+ t2*du2;

      db= -vt*dun+ un*dut;
      db/= v;

      drhs[0]+= fct*( A*rho*dun+ A*un*drho );
      drhs[1]+= fct*db;
      drhs[2]+= fct*ds;
      drhs[3]+= fct*(dh-dut*u0);
      drhs[4]+= fct*dz;

  }

   void cJGas::dmfa( Real  *q, Real  *aux, Real  *w, Real *drhs , Real *dq, Real *daux )
  {
      Real u1,u2,h,s,rho,p,t,m;
      Real du1,du2,dh,ds,drho,dp,dt,dm,dz,cp,rg,cv,gam;

      Real n1,n2,t1,t2;
      Real u,un,ut,vt,v,u0;
      Real cb,sb,talp;
      Real dun,dut;
      Real dauxz[6+5*MxNsp];

      Real A;

      n1= w[0];
      n2= w[1];
      A = w[2];
      u0= w[3];

      t1=-n2;
      t2= n1;

      u1=      q[0]; 
      u2=      q[1]; 
      t=       q[2]; 
      p=       q[3]; 

      rho=   aux[0];
      h=     aux[1];
      s=     aux[2];
      cp=    aux[4];
      rg=    aux[5];
      rg*=runi;
      cv= cp-rg;
      gam=cp/cv;

      dun=-drhs[0];
      dut=-drhs[1];
      ds= -drhs[2];
      dh= -drhs[3];
      dz= -drhs[4];

      un= u1*n1+ u2*n2;
      ut= u1*t1+ u2*t2;
      vt= ut-u0;
      u= un*un+ ut*ut;
      v= un*un+ vt*vt;
      v= sqrt(v);
      cb= un/v;
      sb= vt/v;
      talp= sb/cb;

      dauxvz( q,aux,dz,dauxz );
      dh-= dauxz[1];
      ds-= dauxz[2];
      dun-=un*A*dauxz[0];

      dun/= ( rho*A ); 
      dut*= v;
      dut+= sb*dun;
      dut/= cb;

      dt=  dh;
      drho=ds;

      dt-= ( un*dun+ vt*dut );
      dt/= (cp*t);
      drho-= cv*dt;
      drho/=( v*v/(gam*t)-rg );
      dt+= v*v*drho/(cp*t);
      dp= dt+drho;

      dun-= un*drho;
      dut-= vt*drho;

      dp*= p;
      dt*= t;

      dq[0]= dun*n1+ dut*t1;
      dq[1]= dun*n2+ dut*t2;
      dq[2]= dt;
      dq[3]= dp;
      dq[4]= dz;
      
      dauxv( q,aux, dq,daux );

  }

