   using namespace std;

#  include <gas/jgas.h>

   cJGas::cJGas(){};
   cJGas::~cJGas(){};

   void cJGas::props( string fname )
  {
      cTxtfle *fle;
      Int ir,isp,il,iw;
      string       seps=" +",comms="c#!";
      stringstream    *strm;
      string           jname;

      jname= share+"/JANAF/";
      cJanaf::read( jname );

      string cname= jname+"/compositions.dat";
      fle= new cTxtfle( cname,comms,seps );
/*    strm= fle->stream(0,0);
    *(strm) >> nrc;
      delete strm ;*/
      conv( fle->text(0,0),&nrc );

      il= 0;
      for( ir=0;ir<nrc;ir++ )
     {
        il++;
/*       strm= fle->stream(il,0);
       *(strm) >> zrc[ir];
         delete strm;*/
         conv( fle->text(il,0),&(zrc[ir]) );
        iw=0;
        for( isp=0;isp<nsp;isp++ )
       {
           iw++;
/*         strm= fle->stream(il,iw);
         *(strm)>>xsp[ir][isp];
           delete strm;*/
           conv( fle->text(il,iw),&(xsp[ir][isp]) );
       }
     }
      delete fle;

      Real w;
      for( ir=0;ir<nrc;ir++ )
     {
         w= 0;
         for( isp=0;isp<nsp;isp++ )
        {
            w+= xsp[ir][isp]*wsp[isp];
        }

         for( isp=0;isp<nsp;isp++ )
        {
            ssp[ir][isp]= xsp[ir][isp]/w;
        }
     }

  }

   void cJGas::z2sx( Real z, Real *wg, Real *ssp0, Real *xsp0 )
  {
      Real z0,z1;
      Int  isp,ir,ir0,ir1;
      Real w;
      ir0= bsearch( z, nrc-1,zrc+1 );
      ir1= ir0+1;
      z0= zrc[ir0];
      z1= zrc[ir1];
      w= ( z-z0 )/( z1-z0 );
    (*wg)=0;
      for( isp=0;isp<nsp;isp++ )
     {
         ssp0[isp]= w*ssp[ir1][isp]+ (1.-w)*ssp[ir0][isp];
       (*wg)+= ssp0[isp];
     }
      for( isp=0;isp<nsp;isp++ )
     {
         xsp0[isp]= ssp0[isp]/(*wg);
     }

  }

   void cJGas::dz2sx( Real z, Real dz, Real wg, Real *ssp0, Real *xsp0, Real *dwg, Real *dssp0, Real *dxsp0 )
  {
      Real z0,z1;
      Int  isp,ir,ir0,ir1;
      Real dw;
      ir0= bsearch( z, nrc-1,zrc+1 );
      ir1= ir0+1;
      z0= zrc[ir0];
      z1= zrc[ir1];
      dw= dz/( z1-z0 );

    (*dwg)=0;
      for( isp=0;isp<nsp;isp++ )
     {
         dssp0[isp]= dw*( ssp[ir1][isp]-ssp[ir0][isp] );
       (*dwg)+= dssp0[isp];
     }
      for( isp=0;isp<nsp;isp++ )
     {
         dxsp0[isp]= dssp0[isp]- xsp0[isp]*(*dwg);
         dxsp0[isp]/= wg;
     }

  }

   void cJGas::auxv( Real *q, Real *aux )
  {
      Real u,v,t,p,z;
      Real k,h,s,rho,a,cp,rg,wg,gam;
      Real *h0sp,*cpsp,*s0sp,*ssp0,*xsp0;
      Int  isp,ir,ir0,ir1;


      u= q[0];
      v= q[1];
      t= q[2];
      p= q[3];
      z= q[4];

      ssp0= aux+6;
      xsp0= ssp0+nsp;
      cpsp= xsp0+nsp;
      h0sp= cpsp+nsp;
      s0sp= h0sp+nsp;

      z2sx( z,&wg,ssp0,xsp0 );
      rg= wg*runi;

      k= u*u+ v*v;
      k*= 0.5;
      h= 0.;
      s= 0.;
      cp= 0.;

      eval( t,cpsp,h0sp,s0sp );

      for( isp=0;isp<nsp;isp++ )
     {
         h+=  h0sp[isp]*ssp0[isp];      
         cp+= cpsp[isp]*ssp0[isp];      
         s+=  s0sp[isp]*ssp0[isp];      
         if( xsp0[isp] > small )
        {
            s-= wg*log( xsp0[isp] );
        }
     }
      s-= wg*log(p/pref);


      cp*= runi;
      h*=  runi;
      s*=  runi;
      h+= k;

      gam= cp/(cp-rg);
      rho= p/( rg*t );
      a= gam*rg*t;
      a= sqrt(a);

      aux[0]= rho;
      aux[1]= h;
      aux[2]= s;
      aux[3]= a;
      aux[4]= cp;
      aux[5]= wg;

   }

   void cJGas::dauxv( Real *q, Real *aux, Real *dq, Real *daux )
  {

      Real u,v,t,p,z;
      Real du,dv,dt,dp,dz;
      Real h,s,rho,a,cp,cv,rg,wg,gam;
      Real dk,dh,ds,drho,da,dcp,dcv,drg,dwg,dgam;
      Int  isp,ir,ir0,ir1;

      Real *h0sp,*cpsp,*s0sp,*ssp0,*xsp0;
      Real *dh0sp,*dcpsp,*ds0sp,*dssp0,*dxsp0;


      u= q[0];
      v= q[1];
      t= q[2];
      p= q[3];
      z= q[4];

      du= dq[0];
      dv= dq[1];
      dt= dq[2];
      dp= dq[3];
      dz= dq[4];

      ssp0= aux+6;
      xsp0= ssp0+nsp;
      cpsp= xsp0+nsp;
      h0sp= cpsp+nsp;
      s0sp= h0sp+nsp;

      dssp0= daux+6;
      dxsp0= dssp0+nsp;
      dcpsp= dxsp0+nsp;
      dh0sp= dcpsp+nsp;
      ds0sp= dh0sp+nsp;

      rho= aux[0];
      h=   aux[1];
      s=   aux[2];
      a=   aux[3];
      cp=  aux[4];
      wg=  aux[5];

      dz2sx( z,dz,wg,ssp0,xsp0,&dwg,dssp0,dxsp0 );

      rg= wg*runi;
      drg= dwg*runi;
      dk= u*du+ v*dv;

      deval( t,dt, dcpsp, dh0sp, ds0sp );

      dh= 0.;
      ds= 0.;
      dcp= 0.;
      for( isp=0;isp<nsp;isp++ )
     {
         dh+=  ( dh0sp[isp]*ssp0[isp]+ h0sp[isp]*dssp0[isp] );      
         dcp+= ( dcpsp[isp]*ssp0[isp]+ cpsp[isp]*dssp0[isp] );      
         ds+=  ( ds0sp[isp]*ssp0[isp]+ s0sp[isp]*dssp0[isp] );      
         if( xsp0[isp] > small )
        {
            ds-= ( wg*dxsp0[isp]/xsp0[isp]+ dwg*log( xsp0[isp] ) );
        }
     }
      ds-=  wg*dp/p;
      ds-=  dwg*log(p/pref);

      dcp*= runi;
      dh*=  runi;
      dh+= dk;
      ds*=  runi;

      cv= cp-rg;
      dcv= dcp-drg;

      drg/= rg;
      daux[4]= dcp;
      dcp/= cp;
      dcv/= cv;
      dt/= t;
      dp/= p;

      drho= dp- dt- drg;
      drho*= rho;
      gam= cp/cv;
      dgam= dcp- dcv;
      da= dt+ dgam+ drg;
      da*= 0.5;
      da*= a;
      
      daux[0]= drho;
      daux[1]= dh;
      daux[2]= ds;
      daux[3]= da;
      daux[5]= dwg;

   }

   void cJGas::dauxvz( Real *q, Real *aux, Real dz, Real *daux )
  {

      Real u,v,t,p,z;
      Real h,s,rho,a,cp,cv,rg,wg,gam;
      Real dk,dh,ds,drho,da,dcp,dcv,drg,dwg,dgam;
      Int  isp,ir,ir0,ir1;

      Real *h0sp,*cpsp,*s0sp,*ssp0,*xsp0;
      Real *dssp0,*dxsp0;


      t= q[2];
      p= q[3];
      z= q[4];

      ssp0= aux+6;
      xsp0= ssp0+nsp;
      cpsp= xsp0+nsp;
      h0sp= cpsp+nsp;
      s0sp= h0sp+nsp;

      dssp0= daux+6;
      dxsp0= dssp0+nsp;

      rho= aux[0];
      h=   aux[1];
      s=   aux[2];
      a=   aux[3];
      cp=  aux[4];
      wg=  aux[5];

      dz2sx( z,dz,wg,ssp0,xsp0,&dwg,dssp0,dxsp0 );

      rg= wg*runi;
      drg= dwg*runi;

      dh= 0.;
      ds= 0.;
      dcp= 0.;
      for( isp=0;isp<nsp;isp++ )
     {
         dh+=  h0sp[isp]*dssp0[isp];      
         dcp+= cpsp[isp]*dssp0[isp];      
         ds+=  s0sp[isp]*dssp0[isp];      
         if( xsp0[isp] > small )
        {
            ds-= ( wg*dxsp0[isp]/xsp0[isp]+ dwg*log( xsp0[isp] ) );
        }
     }
      ds-=  dwg*log(p/pref);

      dcp*= runi;
      dh*=  runi;
      ds*=  runi;

      cv= cp-rg;
      dcv= dcp-drg;

      drg/= rg;
      daux[4]= dcp;
      dcp/= cp;
      dcv/= cv;

      drho= -drg;
      drho*= rho;
      gam= cp/cv;
      dgam= dcp- dcv;
      da= dgam+ drg;
      da*= 0.5;
      da*= a;
      
      daux[0]= drho;
      daux[1]= dh;
      daux[2]= ds;
      daux[3]= da;
      daux[5]= dwg;

   }

   void cJGas::prrhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {
      Real eta,pr,r0,r1,s,rg;
      Real p,z;
      pr=  w[1];
      eta= w[0];

      p= q[3]; 
      z= q[4]; 

      s= aux[2];
      rg= aux[5];
      rg*= runi;

      r0= pr*p;

      r1= log(pr);
      r1*= rg;
      if( pr > 1. )
     {
         r1*= (1/eta-1);
     }
      else
     {
         r1*= (eta-1);
     }
      r1+= s;

      rhs[2]+= fct*r0;
      rhs[3]+= fct*r1;
      rhs[4]+= fct*z;

  }

   void cJGas::prdrhsq( Real *q, Real *aux, Real *dq, Real *daux, Real *w, Real fct, Real *drhs )
  {
      Real eta,pr,ds,dz;
      Real dp;
      Real drg;
      Real r1,r0;

      pr=  w[1];
      eta= w[0];

      pr=  w[1];

      dp= dq[3]; 
      dz= dq[4]; 

      ds= daux[2];
      drg= daux[5];
      drg*=runi;

      r0= pr*dp;
      r1= log(pr);
      r1*= drg;
      if( pr > 1. )
     {
         r1*= (1/eta-1);
     }
      else
     {
         r1*= (eta-1);
     }
      r1+= ds;
      
      drhs[2]+= fct*r0;
      drhs[3]+= fct*r1;
      drhs[4]+= fct*dz;
  }

   void cJGas::prdrhsw( Real *q, Real *aux, Real *w, Real *dw, Real fct, Real *drhs )
  {
      Real eta,pr,ds;
      Real deta,dpr,p;
      Real rg,r0,r1,r10,r11;;

      pr=  w[1];

      p= q[3]; 

      rg= aux[5];
      rg*= runi;

      dpr=  dw[1];
      r0= dpr*p; 

      eta= w[0];
      deta= dw[0];

      r10= log(pr);
      r11= dpr/pr;
      if( pr > 1. )
     {
         r11*= (1./eta-1);
         r10*= -deta;
         eta*= eta;
         r10/= eta;
     }
      else
     {
         r11*= (eta-1);
         r10*= deta;
     }
      r1= r10+r11;
      r1*= rg;

      drhs[2]+= fct*r0;
      drhs[3]+= fct*r1;

  }

   void cJGas::dpr( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux )
  {
      Real u,v,t,p,z,eta;
      Real dk,cp,dz,dh,drho,dt,ds,dp,rho,rg,du,dv,pr,drg,r1;
      Real dauxz[6+5*MxNsp];

      u= q[0]; 
      v= q[1]; 
      t= q[2]; 
      p= q[3]; 
      z= q[4]; 
      eta=  w[0];
      pr=  w[1];

      rho=aux[0];

      cp=aux[4];
      rg=aux[5];
      rg*= runi;

      du= -rhs[0];
      dv= -rhs[1];
      dz= -rhs[4];

      dauxvz( q,aux,dz,dauxz );

      dp= -rhs[2];
      dp/= pr;
      dp/= p;

      ds= -rhs[3];
      ds-= dauxz[2];
      if( pr > 1. )
     {
         r1= (1/eta-1);
     }
      else
     {
         r1= (eta-1);
     }
      drg= dauxz[5];
      drg*= runi;
      ds-= drg*r1*log(pr);

      dt=  ds;
      dt+= rg*dp;
      dt/= cp;
      dp*= p;
      dt*= t;
      dq[0]= du;
      dq[1]= dv;
      dq[2]= dt;
      dq[3]= dp;
      dq[4]= dz;
      dauxv( q,aux,dq,daux );
    
  }

   void cJGas::dhcrhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {
      Real eta,wrk,ds,s,rg;
      Real p,h,z;
      Real r0,r1;

      eta= w[0];
      wrk= w[1];

      p= q[3]; 
      z= q[4]; 

      h= aux[1]; 
      s= aux[2]; 
      rg= aux[5]; 
      rg*=runi;
      r0= h;
      r0+= wrk;

      r1=-log(p/pref);
      r1*= rg;
      r1*= (1/eta-1);
      r1+= s;

      rhs[2]+= fct*r0;
      rhs[3]+= fct*r1;
      rhs[4]+= fct*z;

  }

   void cJGas::dhcdrhsq( Real *q, Real *aux, Real *dq, Real *daux, Real *w, Real fct, Real *drhs )
  {
      Real eta,pr,ds;
      Real dp,wrk,dh,dz;
      Real p,h,s,drg,rg;
      Real r1,r0,r10,r11;

      eta= w[0];
      wrk=  w[1];

      p= q[3]; 

      h= aux[1]; 
      s= aux[2]; 
      rg= aux[5]; 
      rg*= runi;

      dp= dq[3]; 
      dz= dq[4]; 

      dh= daux[1]; 
      ds= daux[2]; 
      drg= daux[5]; 
      drg*= runi;

      r0= dh;

      r10=-rg*dp/p;
      r11=-drg*log(p/pref);

      r1= r10+r11;
      r1*=( 1/eta-1 );
      r1+= ds;
   
      drhs[2]+= fct*r0;
      drhs[3]+= fct*r1;
      drhs[4]+= fct*dz;

  }

   void cJGas::dhcdrhsw( Real *q, Real *aux, Real *w, Real *dw, Real fct, Real *drhs )
  {
      Real eta,pr,ds;
      Real deta,dpr;
      Real dp,wrk,dwrk,h,p,rg;
      Real r1,r0,r10,r11;

      wrk=  w[1];
      eta=  w[0];

      dwrk= dw[1];
      deta= dw[0];

      p= q[3]; 

      h= aux[1]; 
      rg= aux[5]; 

      r0= dwrk;
      r1=-log(p/pref);
      r1*= -deta;
      eta*= eta;
      r1/= eta;
      r1*= rg;

      drhs[2]+= fct*r0;
      drhs[3]+= fct*r1;

  }

   void cJGas::ddhc( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux )
  {
      Real u,v,z,t,p;
      Real dk,du,dv,dh,drho,dt,ds,dp,rho,cp,rg,dz,drgz,dhz,dsz;
      Real dauxz[6+5*MxNsp];

      Real eta,wrk;

      eta=  w[0];
      wrk=  w[1];

      u= q[0]; 
      v= q[1]; 
      t= q[2]; 
      p= q[3]; 
      z= q[4]; 

      cp= aux[4]; 
      rg= aux[5]; 
      rg*= runi;

      du= -rhs[0];
      dv= -rhs[1];
      dz= -rhs[4];

      dauxvz( q,aux,dz,dauxz );

      dhz= dauxz[1];
      dsz= dauxz[2];
      drgz= dauxz[5];
      drgz*= runi;

      drgz*= (1/eta-1);
      drgz*= log(p/pref);

      dh= -rhs[2];
      dh-= dhz;

      dt=  dh/t;

      ds= -rhs[3];
      ds-= dsz;
      ds-= drgz;
      dp= ds- dt;
      dp*= -eta/rg;
      dt/= cp;
    
      dp*= p;
      dt*= t;

      dq[0]= du;
      dq[1]= dv;
      dq[2]= dt;
      dq[3]= dp;
      dq[4]= dz;

      dauxv( q,aux,dq,daux );
  }

   void cJGas::dherhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {
      Real eta,wrk,ds,s,rg;
      Real p,h,z;
      Real r0,r1;

      eta= w[0];
      wrk= w[1];

      p= q[3]; 
      z= q[4]; 

      h= aux[1]; 
      s= aux[2]; 
      rg= aux[5]; 
      rg*=runi;
      r0= h;
      r0+= wrk;

      r1=-log(p/pref);
      r1*= rg;
      r1*= (eta-1);
      r1+= s;

      rhs[2]+= fct*r0;
      rhs[3]+= fct*r1;
      rhs[4]+= fct*z;

  }

   void cJGas::dhedrhsq( Real *q, Real *aux, Real *dq, Real *daux, Real *w, Real fct, Real *drhs )
  {
      Real eta,pr,ds;
      Real dp,wrk,dh,dz;
      Real p,h,s,drg,rg;
      Real r1,r0,r10,r11;

      eta= w[0];
      wrk=  w[1];

      p= q[3]; 

      h= aux[1]; 
      s= aux[2]; 
      rg= aux[5]; 
      rg*= runi;

      dp= dq[3]; 
      dz= dq[4]; 

      dh= daux[1]; 
      ds= daux[2]; 
      drg= daux[5]; 
      drg*= runi;

      r0= dh;

      r10=-rg*dp/p;
      r11=-drg*log(p/pref);

      r1= r10+r11;
      r1*=( eta-1 );
      r1+= ds;
   
      drhs[2]+= fct*r0;
      drhs[3]+= fct*r1;
      drhs[4]+= fct*dz;

  }

   void cJGas::dhedrhsw( Real *q, Real *aux, Real *w, Real *dw, Real fct, Real *drhs )
  {
      Real eta,pr,ds;
      Real deta,dpr;
      Real dp,wrk,dwrk,h,p,rg;
      Real r1,r0,r10,r11;

      wrk=  w[1];
      eta=  w[0];

      dwrk= dw[1];
      deta= dw[0];

      p= q[3]; 

      h= aux[1]; 
      rg= aux[5]; 

      r0= dwrk;
      r1=-log(p/pref);
      r1*= deta;
      r1*= rg;

      drhs[2]+= fct*r0;
      drhs[3]+= fct*r1;

  }

   void cJGas::ddhe( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux )
  {
      Real u,v,z,t,p;
      Real dk,du,dv,dh,drho,dt,ds,dp,rho,cp,rg,dz,drgz,dhz,dsz;
      Real dauxz[6+5*MxNsp];

      Real eta,wrk;

      eta=  w[0];
      wrk=  w[1];

      u= q[0]; 
      v= q[1]; 
      t= q[2]; 
      p= q[3]; 
      z= q[4]; 

      cp= aux[4]; 
      rg= aux[5]; 
      rg*= runi;

      du= -rhs[0];
      dv= -rhs[1];
      dz= -rhs[4];

      dauxvz( q,aux,dz,dauxz );

      dhz= dauxz[1];
      dsz= dauxz[2];
      drgz= dauxz[5];
      drgz*= runi;

      drgz*= (eta-1);
      drgz*= log(p/pref);

      dh= -rhs[2];
      dh-= dhz;

      dt=  dh/t;

      ds= -rhs[3];
      ds-= dsz;
      ds-= drgz;
      dp= ds- dt;
      dp/= -(eta*rg);
      dt/= cp;
    
      dp*= p;
      dt*= t;

      dq[0]= du;
      dq[1]= dv;
      dq[2]= dt;
      dq[3]= dp;
      dq[4]= dz;

      dauxv( q,aux,dq,daux );
  }


   void cJGas::mixrhs( Real *q, Real *aux, Real *w, Real fct, Real *rhs )
  {
      Real p,h,z;
      Real r;

      r=   w[1];
      p=   q[3];
      z=   q[4];
      h= aux[1];

      rhs[2]+= fct*r*h;
      rhs[3]+= fct*r*z;
      rhs[4]+=   fct*p;

  }

   void cJGas::mixdrhsq( Real *q, Real *aux, Real *dq, Real *daux, Real *w, Real fct, Real *drhs )
  {
      Real p,h,z;
      Real dp,dh,dz;
      Real r;

      r=   w[1];
      dp=   dq[3];
      dz=   dq[4];
      dh= daux[1];

      drhs[2]+= fct*r*dh;
      drhs[3]+= fct*r*dz;
      drhs[4]+= fct*r*dp;

  }

   void cJGas::mixdrhsw( Real *q, Real *aux, Real *w, Real *dw, Real fct, Real *drhs )
  {
      Real p,h,z;
      Real dr;

      dr=   dw[1];
      p=   q[3];
      z=   q[4];
      h= aux[1];

      drhs[2]+= fct*dr*h;
      drhs[3]+= fct*dr*z;
      drhs[4]+= fct*dr*p;

  }

   void cJGas::dmix( Real *q, Real *aux, Real *w, Real *rhs, Real *dq, Real *daux )
  {
      Real u,v,p,h,z;
      Real du,dv,dp,dh,dz,dt,dk,cp;
      Real r;

      r=    w[1];
      u=    q[0];
      v=    q[1];
      du=-rhs[0];
      dv=-rhs[1];
      dz=-rhs[3];
      dp=-rhs[4];
      cp= aux[4];

      dh=-rhs[2];
      dk= u*du+ v*dv;
      dh-= dk;
      dt= dh/cp;

      dq[0]= du;    
      dq[1]= dv;    
      dq[2]= dt;    
      dq[3]= dp;    
      dq[4]= dz;    
      dauxv( q,aux,dq,daux );
   
  }
