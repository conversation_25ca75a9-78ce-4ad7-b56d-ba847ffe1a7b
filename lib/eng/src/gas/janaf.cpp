using namespace std;

#  include <gas/janaf.h>

   cJanaf::cJanaf()
  {
      nel= 0;
      nsp= 0;
  }

   cJanaf::~cJanaf()
  {
      nel= 0;
      nsp= 0;
  }

   void cJanaf::read( string cpath )
  {
      Int          nw,m;
      Int          il,iw,iel,jel,isp;
      string       tmp;
      bool         more;

      string       seps=" +",comms="c#!";
      string       nme= cpath+"/chemkin.dat";

      string       cel0[5]={ "O", "H", "C", "N", "AR" };
      Real         wel0[5]={ 0.01600, 0.00101, 0.01201, 0.01401, 0.03995 };

      cTxtfle *fle;
      fle= new cTxtfle( nme, comms, seps);

      iel= 0;
      more= true;
      il= fle->whereis( "ELEMENTS",0 );
      do
     {
         nw= fle->nwords(++il);
         for( iw=0;iw<nw;iw++ )
        {
            tmp= fle->text(il,iw);
            if( tmp == "END" ){ more=false;break; }
            for( jel=0;jel<5;jel++ )
           {
               if( tmp == cel0[jel] )
              {
                  wel[iel]= wel0[jel];
                  break;
              }
           }
            cel[iel++]= tmp;
        }
     }while( more );
      nel= iel;

      isp= 0;
      more= true;
      il= fle->whereis( "SPECIES",0 );
      do
     {
         nw= fle->nwords(++il);
         for( iw=0;iw<nw;iw++ )
        {
            tmp= fle->text(il,iw);
            if( tmp == "END" ){ more=false;break; }
            csp[isp++]= tmp;
        }
     }while( more );
      nsp= isp;

      cout <<"ELEMENTS\n";
      for( iel=0;iel<nel;iel++ )
     {
         cout<<cel[iel]<<" ";
     }
      cout <<"\n";

      cout <<"SPECIES\n";
      for( isp=0;isp<nsp;isp++ )
     {
         cout<<csp[isp]<<" ";
     }
      cout <<"\n";

      delete fle;

      readth( cpath );

  }

   void cJanaf::readth( string cpath )
  {
      Int          nw,m;
      Int          il,iw,iel,jel,isp;

      string stmp0,stmp1;
      //stmp0= cpath+"/thermo30.dat";
      stmp0= cpath+"/jetsurf2.dat";
      char  *ctmp0 = &(stmp0[0]);
      Int    ntmp0= stmp0.length();

      Int    ntmp1;
      char  *ctmp1;

      string ctmp2;
      Int    ntmp2;
      Int    ntmp3[MxNel];
      Int    ntmp4[MxNel];
      char  sctmp3[MxNel*5];
      char  *ctmp3[MxNel];
      Real   atmp0[3],atmp1[7],atmp2[7];

      subv( MxNel,5, sctmp3,ctmp3 );
    
      for( iel=0;iel<MxNel;iel++ )
     {
         ctmp3[iel][0]= '\0';
     }

      for( isp=0;isp<nsp;isp++ )
     {

         stmp1= csp[isp];
         ctmp1= &(stmp1[0]);
         ntmp1= stmp1.length();

         // Call the C++ version of janafrd
         if( janafrd(stmp0, stmp1, &ntmp2, ntmp3, ctmp3, ntmp4, atmp0, atmp1, atmp2) )
        {
            wsp[isp] = 0.;
            nelsp[isp] = ntmp2;
            for( iel=0; iel<ntmp2; iel++ )
           {
               ctmp2 = string(ctmp3[iel], ntmp3[iel]);
               for( jel=0; jel<nel; jel++ )
              {
                   if( cel[jel] == ctmp2 )
                  {
                      m = ntmp4[iel];
                      wsp[isp] += (Real)m*wel[jel];
                      ielsp[iel][isp] = jel;
                      melsp[iel][isp] = m;
                  }
               }
           }
            
            rsp[0][isp] = atmp0[0];
            rsp[1][isp] = atmp0[1];
            rsp[2][isp] = atmp0[2];

            asp[0][0][isp] = atmp1[0]; asp[1][0][isp] = atmp2[0];
            asp[0][1][isp] = atmp1[1]; asp[1][1][isp] = atmp2[1];
            asp[0][2][isp] = atmp1[2]; asp[1][2][isp] = atmp2[2];
            asp[0][3][isp] = atmp1[3]; asp[1][3][isp] = atmp2[3];
            asp[0][4][isp] = atmp1[4]; asp[1][4][isp] = atmp2[4];
            asp[0][5][isp] = atmp1[5]; asp[1][5][isp] = atmp2[5];
            asp[0][6][isp] = atmp1[6]; asp[1][6][isp] = atmp2[6];
        }
         else
        {
            cout << "Error: cannot find the speciece " << stmp1 << " from the JANAF table\n";
            assert(0);
        }

         for( jel=0;jel<nelsp[isp];jel++ )
        {
            iel= ielsp[jel][isp];
        }
     }
      show_janaf_gas();
  }

   void cJanaf::getWps( Int *n, Real *w )
  {
      Int isp;
     *n= nsp;
      for( isp=0;isp<nsp;isp++ )
     {
         w[isp]= wsp[isp];
     }
  }

   void cJanaf::getCps( Real t, Real *cp )
  {
      Int isp,ir;
      Real a0,a1,a2,a3,a4;
      for( isp=0;isp<nsp;isp++ )
     {
         Int ir= 1;
         if( t > rsp[1][isp] ){ ir= 0; };
         a0= asp[ir][0][isp];
         a1= asp[ir][1][isp];
         a2= asp[ir][2][isp];
         a3= asp[ir][3][isp];
         a4= asp[ir][4][isp];
         cp[isp]= a0+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) );
     }
  }

   void cJanaf::getHps( Real t, Real *hp )
  {
      Int isp,ir;
      Real a0,a1,a2,a3,a4,a5;
      for( isp=0;isp<nsp;isp++ )
     {
         Int ir= 1;
         if( t > rsp[1][isp] ){ ir= 0; };
         a0= asp[ir][0][isp];
         a1= asp[ir][1][isp]/2;
         a2= asp[ir][2][isp]/3;
         a3= asp[ir][3][isp]/4;
         a4= asp[ir][4][isp]/5;
         a5= asp[ir][5][isp];
         hp[isp]= a5+ t*( a0+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );
     }
  }

   void cJanaf::getSps( Real t, Real *sp )
  {
      Int isp,ir;
      Real a0,a1,a2,a3,a4,a5;
      for( isp=0;isp<nsp;isp++ )
     {
         Int ir= 1;
         if( t > rsp[1][isp] ){ ir= 0; };
         a0= asp[ir][0][isp];
         a1= asp[ir][1][isp];
         a2= asp[ir][2][isp]/2;
         a3= asp[ir][3][isp]/3;
         a4= asp[ir][4][isp]/4;
         a5= asp[ir][6][isp];
         sp[isp]= a5+ a0*log(t)+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) );
     }
  }

   void cJanaf::eval( Real t, Real *cp, Real *h, Real *s )
  {
      Int ir,isp;
      Real a0,a1,a2,a3,a4,a5;
      for( isp=0;isp<nsp;isp++ )
     {

         ir= 1;
         if( t > rsp[1][isp] ){ ir= 0; };

         a0= asp[ir][0][isp];
         a1= asp[ir][1][isp];
         a2= asp[ir][2][isp];
         a3= asp[ir][3][isp];
         a4= asp[ir][4][isp];
         cp[isp]= ( a0+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );

         a0= asp[ir][0][isp];
         a1= asp[ir][1][isp]/2;
         a2= asp[ir][2][isp]/3;
         a3= asp[ir][3][isp]/4;
         a4= asp[ir][4][isp]/5;
         a5= asp[ir][5][isp];
         h[isp]= a5+ t*( a0+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );

         a0= asp[ir][0][isp];
         a1= asp[ir][1][isp];
         a2= asp[ir][2][isp]/2;
         a3= asp[ir][3][isp]/3;
         a4= asp[ir][4][isp]/4;
         a5= asp[ir][6][isp];
         s[isp]= a5+ a0*log(t)+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) );

     }
  }

   void cJanaf::deval( Real t, Real dt, Real *dcp, Real *dh, Real *ds )
  {
      Int ir,isp;
      Real a0,a1,a2,a3,a4,a5;
      Real cp;
      for( isp=0;isp<nsp;isp++ )
     {

         ir= 1;
         if( t > rsp[1][isp] ){ ir= 0; };

         a0= asp[ir][0][isp];
         a1= asp[ir][1][isp];
         a2= asp[ir][2][isp];
         a3= asp[ir][3][isp];
         a4= asp[ir][4][isp];

         cp= ( a0+ t*( a1+ t*( a2+ t*( a3+ t*a4 ) ) ) );
         dcp[isp]= dt*( a1+ t*( 2*a2+ t*( 3*a3+ 4*t*a4 ) ) );

         dh[isp]= dt*cp;
         ds[isp]= dt*cp/t;

     }
  }
   extern "C" void jcassign( Int *len, char *src, char **dst )
  {
      Int i;
      for( i=0;i<*len;i++ )
     {
       (*dst)[i]= src[i];
     }
  }

  /**
   * C++ implementation of the janafrd Fortran function
   * Reads thermodynamic data for a specific species from JANAF thermochemical tables
   *
   * @param filepath      Path to the thermodynamic data file (thermo30.dat)
   * @param species       Name of the chemical species to search for
   * @param numElements   Output: number of elements in the species
   * @param elementLengths Output: length of each element's name
   * @param elementSymbols Output: array of element symbols
   * @param elementMultiplicities Output: number of each element in the species
   * @param tempRanges    Output: temperature ranges [0]=low, [1]=high, [2]=mid
   * @param highTCoeffs   Output: 7 coefficients for high-temperature range
   * @param lowTCoeffs    Output: 7 coefficients for low-temperature range
   * @return              true if species found, false if not
   */
  bool cJanaf::janafrd(const string& filepath, const string& species, 
                      Int* numElements, Int* elementLengths, char** elementSymbols, 
                      Int* elementMultiplicities, Real* tempRanges, 
                      Real* highTCoeffs, Real* lowTCoeffs)
  {
      std::ifstream file(filepath.c_str());
      if( !file.is_open() )
     {
         std::cerr << "Error: Cannot open file " << filepath << std::endl;
         return false;
     }
      
      // Skip header lines
      for( Int i = 0; i < 5; i++ )
     {
         file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
     }
      
      bool found = false;
      string line;
      string name;
      
      // Loop through the file searching for the species
      while( std::getline(file, line) && !found )
     {
         if( line.length() < 18 )
        {
            continue;  // Line too short, skip
        }
          
         // Extract species name (first 18 characters)
         name = line.substr(0, 18);
         name = name.substr(0, name.find_last_not_of(" \t") + 1);
          
         if( name == species )
        {
            found = true;
              
            // Parse element data
            *numElements = 0;
            Int pos = 24;
              
            // Read up to 4 element/count pairs
            for( Int i = 0; i < 4; i++ )
           {
                if( pos + 5 > line.length() )
               {
                   break;
               }
                  
                string element = line.substr(pos, 2);
                element = element.substr(0, element.find_first_of(" \t"));
                  
                if( element.empty() )
               {
                   break;  // No more elements
               }
                  
                string countStr = line.substr(pos + 2, 3);
                countStr = countStr.substr(0, countStr.find_last_not_of(" \t") + 1);
                  
                if( countStr.empty() )
               {
                   break;  // Malformed data
               }
                  
                Int count = std::stoi(countStr);
                  
                // Store element data
                (*numElements)++;
                elementLengths[*numElements - 1] = element.length();
                elementMultiplicities[*numElements - 1] = count;
                  
                // Copy element symbol to the char array
                for( size_t j = 0; j < element.length(); j++ )
               {
                   elementSymbols[*numElements - 1][j] = element[j];
               }
                elementSymbols[*numElements - 1][element.length()] = '\0';
                  
                pos += 5;  // Move to next element/count pair
           }
              
            // Extract temperature ranges
            size_t tempPos = line.find_first_not_of(" \t", 45);
            if( tempPos != string::npos && tempPos + 30 <= line.length() )
           {
                string tempStr = line.substr(tempPos, 30);
                std::istringstream tempStream(tempStr);
                tempStream >> tempRanges[0] >> tempRanges[2] >> tempRanges[1];
           }
            else
           {
                return false;  // Invalid temperature data
           }
              
            // Read next 3 lines for coefficients
            string coeffLine1, coeffLine2, coeffLine3;
            if( !std::getline(file, coeffLine1) || 
                !std::getline(file, coeffLine2) || 
                !std::getline(file, coeffLine3) )
           {
                return false;  // Unexpected end of file
           }
              
            // Parse coefficient lines
            std::istringstream stream1(coeffLine1);
            std::istringstream stream2(coeffLine2);
            std::istringstream stream3(coeffLine3);
              
            // High temperature coefficients (a1-a5) from line 1
            stream1 >> highTCoeffs[0] >> highTCoeffs[1] >> highTCoeffs[2] 
                    >> highTCoeffs[3] >> highTCoeffs[4];
              
            // High temp coefficients (a6-a7) and low temp coefficients (a1-a3) from line 2
            stream2 >> highTCoeffs[5] >> highTCoeffs[6] 
                    >> lowTCoeffs[0] >> lowTCoeffs[1] >> lowTCoeffs[2];
              
            // Low temperature coefficients (a4-a7) from line 3
            stream3 >> lowTCoeffs[3] >> lowTCoeffs[4] >> lowTCoeffs[5] >> lowTCoeffs[6];
              
            break;  // Species found, stop searching
        }
     }
      
      file.close();
      return found;
  }

   void cJanaf::show_janaf_gas()
  {
      Int isp;

      for( isp=0;isp<nsp;isp++ )
     {
         cout << "specices " << csp[isp] << "\n";
         cout << "  Temperature range   " << rsp[0][isp] << " " << rsp[1][isp] << " " << rsp[2][isp] << "\n";

         cout << "  Polynomial coff.   " << asp[0][0][isp] << " " << asp[1][0][isp] << " ";
         cout <<                            asp[0][1][isp] << " " << asp[1][1][isp] << " ";
         cout <<                            asp[0][2][isp] << " " << asp[1][2][isp] << " ";
         cout <<                            asp[0][3][isp] << " " << asp[1][3][isp] << " ";
         cout <<                            asp[0][4][isp] << " " << asp[1][4][isp] << " ";
         cout <<                            asp[0][5][isp] << " " << asp[1][5][isp] << " ";
         cout <<                            asp[0][6][isp] << " " << asp[1][6][isp] << "\n";
     }
  }
