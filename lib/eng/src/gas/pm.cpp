   using namespace std;

#  include <gas/idgas.h> 
#  include <const.h>
#  include <cstdlib>
#  include <iostream>
#  include <cmath>

   Real dudmu( Real a, Real u )
  {
      Real M2;
      M2= u*u/(a*a);
      return u / sqrt( M2 - 1 );
  }
   
   Real d_dudmu( Real a, Real u, Real da, Real du )
  {
      Real M2, dM2, sqM2, dsqM2;
      M2= u*u/(a*a);
   
      dM2= 2*u*du/(u*u) - 2*a*da/(a*a);
      dM2*= M2;   
   
      sqM2= sqrt( M2 - 1 );
   
      dsqM2= 0.5*dM2/sqM2;
   
      return du/sqM2 - u*dsqM2/(sqM2*sqM2);
  }
    
   
   void RK4( Real *q0, Real *aux0, Real step, Real *u1 )
  {
      Real y0,  y1, t1, h, a;
      Real k1, k2, k3, k4;
   
      y0= sqrt( q0[0]*q0[0] + q0[1]*q0[1] );
   
      a=  aux0[3];
   
      k1= dudmu( a, y0 );
      k2= dudmu( a, y0 + 0.5*step*k1);
      k3= dudmu( a, y0 + 0.5*step*k2);
      k4= dudmu( a, y0 + step*k3);
   
      y1= y0 + step/6 *( k1 + 2*k2 + 2*k3 + k4 );
   
     *u1= y1;
   
  }
   
   void dRK4q( Real *q0, Real *aux0, Real *dq0, Real *daux0, Real step, Real *u1, Real *du1)
  {
      Real y0, y1;
      Real dy0, dy1;
      Real k1, k2, k3, k4;
      Real dk1, dk2, dk3, dk4;
      Real  um0,  ut0,  t0,  p0,  rho0,  h0,  s0,  a0;
      Real dum0, dut0, dt0, dp0, drho0, dh0, ds0, da0;
   
      Real dstep= 0.;
      Real y0k1, y0k2, y0k3;
      Real dy0k1, dy0k2, dy0k3;
   
      um0=      q0[0]; 
      ut0=      q0[1]; 
      t0=       q0[2]; 
      p0=       q0[3]; 
      rho0=   aux0[0];
      h0=     aux0[1];
      s0=     aux0[2];
      a0=     aux0[3];
   
      dum0=      dq0[0]; 
      dut0=      dq0[1]; 
      dt0=       dq0[2]; 
      dp0=       dq0[3]; 
      drho0=   daux0[0];
      dh0=     daux0[1];
      ds0=     daux0[2];
      da0=     daux0[3];
   
      y0=  sqrt( um0*um0 + ut0*ut0 );
      dy0= um0*dum0/y0 + ut0*dut0/y0;
      a0=   aux0[3];
      da0= daux0[3];
   
   
      k1= dudmu( a0, y0 );
      dk1= d_dudmu( a0, y0, da0, dy0 );
   
      y0k1= y0 + 0.5*step*k1;
      dy0k1= dy0 + 0.5*step*dk1;
      k2= dudmu( a0, y0k1);
      dk2= d_dudmu( a0, y0k1, da0, dy0k1 );
   
   
      y0k2= y0 + 0.5*step*k2;
      dy0k2= dy0 + 0.5*step*dk2;
      k3= dudmu( a0, y0k2 );
      dk3= d_dudmu( a0, y0k2, da0, dy0k2 );
   
      y0k3= y0 + step*k3;
      dy0k3= dy0 + step*dk3;
      k4= dudmu( a0, y0k3);
      dk4= d_dudmu( a0, y0k3, da0, dy0k3 );
   
   
      y1= y0 + step/6 *( k1 + 2*k2 + 2*k3 + k4 );
      dy1= dy0 +  step/6 *( dk1 + 2*dk2 + 2*dk3 + dk4 );
   
     *u1 = y1;
     *du1= dy1;
   }
   

   void pm( cIdGas *gas, Real *q, Real *aux, Int n, Real da )
  {
  
      Real    w[3];
      Real   dw[3];
      Real    rhs[4],rhs0[4];
      Real    dq[4]= {0,0,0,0};
      Real    daux[4];
      
      Real  phi, phi0, u1m, u1t, u1;
      Int i, j, k;
      Real step;
    
      rhs0[0]=0;
      rhs0[1]=0;
      rhs0[2]=0;
      rhs0[3]=0;
    
      w[0]=1.;
      w[1]=0.;
      w[2]=0;
      
      gas->verhs( q,aux, w,(Real)-1.,rhs0 );

      step= da/(Real)n;
      for(i=0; i<n; i++)
     {

         phi0= atan2( q[1], q[0] );
         RK4( q, aux, step, &u1 );
         phi= phi0-step;
   
         u1m= u1*cos(phi);
         u1t= u1*sin(phi);

         rhs[0]= q[0]-u1m;
         rhs[1]= q[1]-u1t;
         rhs[2]= 0;
         rhs[3]= 0;
         gas->dve( q,aux,w,rhs,dq,daux );

         q[0]+= dq[0];
         q[1]+= dq[1];
         q[2]+= dq[2];
         q[3]+= dq[3];
    
         rhs0[0]= -u1m;
         rhs0[1]= -u1t;

         for( Int it=0;it<10;it++ )
        {
            rhs[0]= rhs0[0];
            rhs[1]= rhs0[1];
            rhs[2]= rhs0[2];
            rhs[3]= rhs0[3];
            gas->auxv( q,aux );
            gas->verhs( q,aux, w,(Real)1.,rhs );
            gas->dve( q,aux,w,rhs,dq,daux );
            q[0]+= 0.999*dq[0];
            q[1]+= 0.999*dq[1];
            q[2]+= 0.999*dq[2];
            q[3]+= 0.999*dq[3];
        }
     }
  }
 
