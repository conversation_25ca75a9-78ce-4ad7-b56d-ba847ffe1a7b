.SUFFIXES:
.SUFFIXES: .cpp .F .h .o

VERSION=29
LPATH= $(HOME)/xcode/lib
BUILD_TYPE?=cpu

#include $(LPATH)/Makefile.in.gnu.petsc
#include $(LPATH)/mopts/Makefile.in.nvsdk
#include $(LPATH)/mopts/Makefile.in.nvsdk.cc61
#include $(LPATH)/mopts/Makefile.in.nvsdk.cpu
#include $(LPATH)/mopts/Makefile.in.gnu
include $(LPATH)/mopts/Makefile.in.gnu.cgns
#include $(LPATH)/mopts/Makefile.in.gnu.fp32
#include $(LPATH)/mopts/Makefile.in.nvsdk.cc86
#include $(LPATH)/mopts/Makefile.in.nvsdk.cc86.cgns
#include $(LPATH)/mopts/Makefile.in.nvsdk.cc86.fp32
#include $(LPATH)/mopts/Makefile.in.nvsdk.cc89

SYSI=-I$(LPATH)/sys/inc 
SYSL=-L$(LPATH)/sys -lsys -lpthread

ENGI=-I$(LPATH)/eng/inc
ENGL=-L$(LPATH)/eng -leng

ORGI=-I$(LPATH)/org/inc
ORGL=-L$(LPATH)/org -lorg

#PETSCI = -I$(HOME)/cfd/petsc.complex/arch-opt/include -I$(HOME)/cfd/petsc.complex/include 
#PETSCL = -L$(HOME)/cfd/petsc.complex/arch-opt/lib -lpetsc -lm -ldl

