#  ifndef _DEV_
#  define _DEV_

#  include <frontend/dutil.h>

   void read_input( string fnm, string marg, cDevice *dev, cCase *cse );

   void dload( cEnv *env, cDevice *dsrv, cDevice **dev );
   void dsave( cEnv *env, cDevice *dsrv, cDevice  *dev );

   void partition( cEnv *, cCase *, cDevice * );
   void preprocess( cEnv *, cCase *, cDevice * );
   void postprocess( cEnv *, cCase *, cDevice * );
   void upostprocess( cEnv *, cCase *, cDevice * );
   void ppostprocess( cEnv *, cCase *, cDevice * );
   void outputjl09( cEnv *, cCase *, cDevice * );
   void compute( cEnv *, cCase *, cDevice * );

#  endif

