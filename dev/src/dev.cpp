
   using namespace std;

#  include <dev.h>

   void dload( cEnv *env, cDevice *dsrv, cDevice **dev )
  {
      assert(0);
//      string marg;
//      marg= env->getarg( "-m" );
//    (*dev)= dsrv->checkout( marg );
//      if( !(*dev) )
//     {
//         cout << "file not available ...\n";
//       (*dev)= new cEmptyDevice();
//       (*dev)->setname( marg ); 
//       (*dev)->touch( "created" );
//         dsrv->checkin( *dev );
//     }
//    (*dev)->check( "   " );
  }

   void dsave( cEnv *env, cDevice *dsrv, cDevice *dev )
  {
      assert(0);
//      dev->checkin( dev );
  }
