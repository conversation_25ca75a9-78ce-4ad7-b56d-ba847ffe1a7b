include $(HOME)/xcode/lib/Makefile.in

CSRC=   main.cpp \
	cse.cpp \
	dev.cpp \
	partition.cpp \
	preprocess.cpp \
	compute.cpp \
	input.cpp \
	postprocess.cpp

FSRC=
COBJ=$(CSRC:.cpp=.o)
FOBJ=$(FSRC:.F=.o)
OBJS=$(COBJ) $(FOBJ)

BINS=xcode

ifeq ($(BUILD_TYPE), gpu)
$(BINS): $(OBJS)
	$(PCCMP) $(COPT) -cuda $(OBJS) $(ORGL) $(ENGL) $(SYSL) $(LAPACK) $(LIBF) $(PETSCL) $(CGNSL) $(HDF5L) -o $@
else ifeq ($(BUILD_TYPE), cpu)
$(BINS): $(OBJS)
	$(PCCMP) $(COPT) $(OBJS) $(ORGL) $(ENGL) $(SYSL) $(LAPACK) $(LIBF) $(PETSCL) $(CGNSL) $(HDF5L) -o $@
endif

clean:
	rm -f $(OBJS) $(BINS)

.cpp.o:
	$(PCCMP) $(COPT) -I../inc $(ORGI) $(ENGI) $(SYSI) $(PETSCI) $(CGNSI) $(HDF5I) -o $@ -c $<

.F.o:
	$(PFCMP) $(FOPT) -I../inc $(ORGI) $(ENGI) $(SYSI) -o $@ -c $<
