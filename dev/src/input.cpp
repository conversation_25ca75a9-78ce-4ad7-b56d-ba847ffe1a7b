
   using namespace std;

#  include <dev.h>
#  include <iniParser.h>
#  include <vector>

   void read_input( string fnm, string marg, cDevice *dev, cCase *cse )
  {
      IniParser parser;
      string devnms;
      vector<string> dev_str;
      string sdum,substr, tmp_type, tmp_nm;
      stringstream ss;
      Int id, ndev, is, ityp;
      cTabData *tab;
      string item, val, str_sec;
      vector<cDevice*> leaf_dev;
      cDevice *data;

      cout << "read input file " << fnm << "\n";
      parser.parseFromFile("input.au3x");

      //case setup
      str_sec = "case";
      item = "requested-nodes";  val = parser.getValue(str_sec, item); 
      tab = new cTabData(); cse->get( tab );  tab->force( item, val );  cse->set( tab ); delete tab;
//      cout << val << "\n";
      item = "cores";  val = parser.getValue(str_sec, item); 
      //cse->get( &tab );  tab.force( item, val );  cse->set( &tab );
      tab = new cTabData(); cse->get( tab );  tab->force( item, val );  cse->set( tab ); delete tab;//have to do this dynamically, otherwise I get a memory leak somehow
//      cout << val << "\n";
      item = "run-time";  val = parser.getValue(str_sec, item); 
      tab = new cTabData(); cse->get( tab );  tab->force( item, val );  cse->set( tab ); delete tab;
//      cout << val << "\n";


      //root device
      sdum = "empty:"+marg;
      dev->set_fullname(sdum);
      item = "active";
      val  = "1";
      tab = new cTabData(); dev->get( tab );  tab->force( item, val );  dev->set( tab ); delete tab;

      //read list of devices and build the device tree;
      devnms = parser.getValue("devicelist", "device");
      ss.clear(); 
      ss.str(devnms);
      dev->build_dev_tree(devnms);
      dev->print_all_devices();

      //read in parameters for the leaf-node devices
      leaf_dev = dev->get_leaf_devices();
      for(size_t i=0; i<leaf_dev.size(); i++)
     {
         data = leaf_dev[i];

         item = "active";
         val  = "1";
         tab = new cTabData(); data->get( tab );  tab->force( item, val );  data->set( tab ); delete tab;
        
         str_sec = data->getname();
         item = "domain-loader-library";  val = parser.getValue(str_sec, item); 
         tab = new cTabData(); data->get( tab );  tab->force( item, val );  data->set( tab ); delete tab;
//         cout << val << "\n";

         item = "domain-loader-object";   val = parser.getValue(str_sec, item);
         tab = new cTabData(); data->get( tab );  tab->force( item, val );  data->set( tab ); delete tab;
//         cout << val << "\n";

         item = "domain-runtime-library"; val = parser.getValue(str_sec, item);
         tab = new cTabData(); data->get( tab );  tab->force( item, val );  data->set( tab ); delete tab;
//         cout << val << "\n";

         item = "domain-runtime-object";  val = parser.getValue(str_sec, item);
         tab = new cTabData(); data->get( tab );  tab->force( item, val );  data->set( tab ); delete tab;
//         cout << val << "\n";

         item = "domain-partitioner-library";  val = parser.getValue(str_sec, item);
         tab = new cTabData(); data->get( tab );  tab->force( item, val );  data->set( tab ); delete tab;
//         cout << val << "\n";

         item = "domain-partitioner-object";   val = parser.getValue(str_sec, item);
         tab = new cTabData(); data->get( tab );  tab->force( item, val );  data->set( tab ); delete tab;
//         cout << val << "\n";

         item = "multigrid-levels";   val = parser.getValue(str_sec, item);
         tab = new cTabData(); data->get( tab );  tab->force( item, val );  data->set( tab ); delete tab;
//         cout << val << "\n";

         data->set_rcse(cse);
     }
  }

