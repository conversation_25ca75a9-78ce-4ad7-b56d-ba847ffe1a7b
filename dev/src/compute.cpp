   using namespace std;

#  include <cse.h>
#  include <dev.h>
#  include <interf.h>

   void interf( Int k, Int n, box_t *bx, Int *isrf, cDevice **dev, string  *str, bool bposix )
  {
      cInterf  *inf;
      Int i,j;
      Int m=0;
      for( i=0;i<n-1;i++ )
     {
         for( j=i+1;j<n;j++ )
        {
            if( overlap( bx+i,bx+j ) && ( dev[i] != dev[j] ) )
           {
               cout << k<<": interface between surfaces "<<i<<" "<<j<<" devices "<<dev[i]->getname()<<" "<<dev[j]->getname()<<"\n";
//             if( dev[i]->resides() || dev[j]->resides() )
              {
                  inf= new cInterf();
                  inf->build( isrf[i],dev[i],str[i], isrf[j],dev[j],str[j], m, bposix );
                  if(bposix)
                 {
                    inf->boot();
                 }
              }
               m++;
           }
        }
     }
  }
  

   void compute( cEnv *env, cCase *cse, cDevice *dev )
  {
      cTabData tab;
      box_t   *bx=NULL;
      Int      n=0,m=0;
      Int     *isrf=NULL;
      cDevice **d=NULL;
      string   *s=NULL;
      Int      mcpu,ncpu;
      Real     rtime;
      bool     bposix;


      bposix = false;

      cse->get( &tab );
      tab.get( "ranks",&mcpu );
      tab.get( "run-time",&rtime );

      cout << "requested running time is "<<rtime<<"\n";
      if( mcpu == env->getncpu() )
     {
         Int rank;
         MPI_Comm_rank(  MPI_COMM_WORLD, &rank );

         if(rank==0)
        {
            vector<cDevice*> leaf_devices = dev->get_leaf_devices();
            cout << "PartName " << leaf_devices.size() << " ";
            for (size_t i = 0; i < leaf_devices.size(); i++)
           {
               cout << leaf_devices[i]->getname() << " ";
           }
            cout << "\n";
        }

         dev->occupy();
         dev->makeboxes( &n,&m,&bx,&isrf,&d,&s );
         interf( env->getrank(), n, bx,isrf,d,s, bposix );
         delete[] bx;
         delete[] isrf;
         delete[] d;
         delete[] s;
         dev->starthist();
         do
        {
            dev->request2();
            dev->poll2();
            dev->compute();
//            cout << rtime << " " << env->runtime() << "\n";   
        }while( env->runtime() < rtime );
         cout << "==========computation ends after "<<env->runtime()<<" seconds==================== \n";
         dev->shutdown2();
         dev->endhist();

         cout << "coming out\n";
     }
  }

