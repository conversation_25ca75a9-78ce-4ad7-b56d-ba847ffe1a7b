
   using namespace std;

#  include <cstdio>
#  include <cse.h>

   void cload( cEnv *env, cCase **cse )
  {
      FILE     *f=NULL;
      size_t    len=0;
      pickle_t  buf=NULL;
      string    marg,carg;
      string    fnme;
      string    tmp;
      cTabData *tab;

     *cse= NULL;
      marg= env->getarg( "-m" );
      carg= env->getarg( "-c" );

      if( marg != "" && carg != "" )
     {

         fnme= marg+"."+carg+".cse";
         f= fopen( fnme.c_str(),"r" );
         if( f )
        {
           *cse= new cCase();
            fread( (void*)&len, (size_t)1,sizeof(len),f ); buf= new pickle_v[ len ];
            fread( (void*) buf, (size_t)1,       len ,f );
            fclose( f );
            len=0;
          (*cse)->unpickle( &len,buf );
            delete[]  buf; buf=NULL; len=0; 
        } 
         else
        {
          (*cse)= new cCase();
        }
         tab= new cTabData;
       (*cse)->get( tab );
         tab->get( "name",(string*)&tmp );
         if( tmp == unassigned )
        {
            tab->set( "name",carg );
          (*cse)->set( tab );
          (*cse)->touch( "created" );
        }
        delete tab;   
      (*cse)->check( "   " );
     }
  }

   void csave( cEnv *env, cCase *cse )
  {
      FILE     *f=NULL;
      size_t    len=0;
      pickle_t  buf=NULL;
      string    marg,carg;
      string    fnme;
      cTabData  tab;

      if( env->getncpu() > 1 ){ return; };

      marg= env->getarg( "-m" );
      cse->get( &tab );
      tab.get( "name",&carg );

//    cse->touch( "edited" );
//    cse->check("   ");

      cse->pickle( &len,&buf );
     
      fnme= marg+"."+carg+".cse";
      f= fopen( fnme.c_str(),"w" );
      fwrite( (void*)&len,(size_t)1,sizeof(len),f );
      fwrite( (void*) buf,(size_t)1,       len, f );
      fclose(f);
      delete[] buf; buf= NULL; len=0;

  }

