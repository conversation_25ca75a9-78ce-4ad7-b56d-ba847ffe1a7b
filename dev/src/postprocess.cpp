   using namespace std;

#  include <cse.h>
#  include <dev.h>

   void postprocess( cEnv *env, cCase *cse, cDevice *dev )
  {
      Int                                 icpu,ncpu;


      ncpu= env->getncpu();

      icpu=0;
      dev->occupy( &icpu,ncpu );
      dev->postprocess();
      cout << "postprocess completed\n";
  }

   void outputjl09( cEnv *env, cCase *cse, cDevice *dev )
  {
      Int                                 icpu,ncpu;


      ncpu= env->getncpu();

      icpu=0;
      dev->occupy( &icpu,ncpu );
      dev->outputjl09();
      cout << "output jl09 completed\n";
  }

   void upostprocess( cEnv *env, cCase *cse, cDevice *dev )
  {
      Int                                 icpu,ncpu;
      ifstream                            fle;
      string                              fnm;
      Int                                 it, its, ite, idt;

      fnm = "time.upost";
      fle.open(fnm.c_str());
      if(!fle.good())
     {
         cout << "Error: can not open file " << fnm << "\n";
         return;
     }

      fle >> its;
      fle >> ite;
      fle >> idt;
      cout << "starting time level: " << its << "\n";
      cout << "ending   time level: " << ite << "\n";
      cout << "step               : " << idt << "\n";

      ncpu= env->getncpu();

      icpu=0;
      dev->occupy( &icpu,ncpu );
      for(it=its; it<ite; it+=idt)
     {
         cout << "postprocess at time level " << it << "\n";
         dev->postprocess(it);
     }
      cout << "postprocess completed\n";
  }

