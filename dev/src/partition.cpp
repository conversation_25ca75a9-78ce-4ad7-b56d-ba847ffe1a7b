

   using namespace std;

#  include <cse.h>
#  include <dev.h>

   void partition( cEnv *env, cCase *cse, cDevice *dev )
  {
      Real                                cost;
      cTabData                            tab; 

      cse->get( &tab ); 

      cost= 0;
      dev->getcost( &cost );
      cout << "the overall cost is "<<cost<<"\n";
      tab.set( "overall-load",cost );
      cse->set( &tab );
      cse->reset();

//      dev->assigncpu();
      
      dev->partition();
      cse->finalize();

  }
